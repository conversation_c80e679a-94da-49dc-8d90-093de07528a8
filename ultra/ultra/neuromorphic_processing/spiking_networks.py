#!/usr/bin/env python3
"""
ULTRA: Neuromorphic Processing Layer - Spiking Neural Networks

This module implements biologically-inspired spiking neural networks that use temporal 
spikes for information processing rather than continuous activations. These networks 
more closely resemble the operation of biological neural systems by incorporating
temporal dynamics and discrete action potentials.

The module includes:
1. Various spiking neuron models (<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>)
2. Synapse models with plasticity (STDP, homeostatic)
3. Network infrastructure for creating and simulating SNNs
4. Encoding/decoding mechanisms for interfacing with other ULTRA components
5. Tools for analysis and visualization of spiking dynamics
"""

import numpy as np
import torch
import scipy.sparse as sparse
from typing import Dict, List, Tuple, Set, Any, Optional, Union, Callable, Type
from dataclasses import dataclass, field
from enum import Enum
import logging
import time
import matplotlib.pyplot as plt
from collections import defaultdict, deque

logger = logging.getLogger(__name__)

# Constants
DEFAULT_SIMULATION_DT = 0.1  # ms - Default simulation time step
DEFAULT_REFRAC_PERIOD = 2.0  # ms - Default refractory period

class NeuronType(Enum):
    """Types of supported spiking neurons."""
    LIF = "leaky_integrate_and_fire"
    ADEX = "adaptive_exponential"
    IZHIKEVICH = "izhikevich"
    QIF = "quadratic_integrate_and_fire"
    HH = "hodgkin_huxley"
    FN = "fitzhugh_nagumo"
    CUSTOM = "custom"

class SynapseType(Enum):
    """Types of supported synapses."""
    BASIC = "basic"
    STDP = "spike_timing_dependent_plasticity"
    HOMEOSTATIC = "homeostatic"
    STP = "short_term_plasticity"
    HEBBIAN = "hebbian"
    CUSTOM = "custom"

@dataclass
class SpikingNeuron:
    """Base class for spiking neuron models."""
    
    neuron_id: int
    type: NeuronType = NeuronType.LIF
    v_rest: float = 0.0         # mV - Resting membrane potential
    v_reset: float = 0.0        # mV - Reset potential after spike
    v_threshold: float = 1.0    # mV - Spike threshold
    membrane_resistance: float = 1.0  # MΩ - Membrane resistance
    membrane_time_constant: float = 10.0  # ms - Membrane time constant
    refractory_period: float = DEFAULT_REFRAC_PERIOD  # ms - Refractory period duration
    
    # State variables
    v: float = field(init=False)  # Current membrane potential
    last_spike_time: float = field(init=False)  # Time of last spike
    refractory_until: float = field(init=False)  # Time when refractory period ends
    
    def __post_init__(self):
        self.v = self.v_rest
        self.last_spike_time = -float('inf')
        self.refractory_until = -float('inf')
        
    def reset(self):
        """Reset neuron state to initial conditions."""
        self.v = self.v_rest
        self.last_spike_time = -float('inf')
        self.refractory_until = -float('inf')
        
    def update(self, current_input: float, t: float, dt: float) -> bool:
        """
        Update neuron state and check for spike generation.
        
        Args:
            current_input: Input current in nA
            t: Current simulation time in ms
            dt: Simulation time step in ms
            
        Returns:
            True if neuron spiked, False otherwise
        """
        raise NotImplementedError("Subclasses must implement the update method")


class LIFNeuron(SpikingNeuron):
    """
    Leaky Integrate-and-Fire neuron model.
    
    The LIF neuron is described by the differential equation:
    τ_m * dv/dt = -(v - v_rest) + R_m * I(t)
    
    Where:
    - τ_m is the membrane time constant
    - v is the membrane potential
    - v_rest is the resting potential
    - R_m is the membrane resistance
    - I(t) is the input current
    """
    
    def __init__(
        self,
        neuron_id: int,
        v_rest: float = 0.0,
        v_reset: float = 0.0,
        v_threshold: float = 1.0,
        membrane_resistance: float = 1.0,
        membrane_time_constant: float = 10.0,
        refractory_period: float = DEFAULT_REFRAC_PERIOD
    ):
        """
        Initialize LIF neuron.
        
        Args:
            neuron_id: Unique identifier for this neuron
            v_rest: Resting membrane potential (mV)
            v_reset: Reset potential after spike (mV)
            v_threshold: Spike threshold (mV)
            membrane_resistance: Membrane resistance (MΩ)
            membrane_time_constant: Membrane time constant (ms)
            refractory_period: Refractory period duration (ms)
        """
        super().__init__(
            neuron_id=neuron_id,
            type=NeuronType.LIF,
            v_rest=v_rest,
            v_reset=v_reset,
            v_threshold=v_threshold,
            membrane_resistance=membrane_resistance,
            membrane_time_constant=membrane_time_constant,
            refractory_period=refractory_period
        )
    
    def update(self, current_input: float, t: float, dt: float) -> bool:
        """
        Update LIF neuron state and check for spike generation.
        
        The update uses the exponential Euler method:
        v(t+dt) = v_rest + (v(t) - v_rest) * exp(-dt/τ_m) + R_m * I * (1 - exp(-dt/τ_m))
        
        Args:
            current_input: Input current in nA
            t: Current simulation time in ms
            dt: Simulation time step in ms
            
        Returns:
            True if neuron spiked, False otherwise
        """
        # Check if in refractory period
        if t < self.refractory_until:
            return False
            
        # Update membrane potential using exponential Euler method
        # This is more accurate than simple Euler, especially for larger dt
        tau_m = self.membrane_time_constant
        R_m = self.membrane_resistance
        exp_term = np.exp(-dt / tau_m)
        
        self.v = self.v_rest + (self.v - self.v_rest) * exp_term + \
                R_m * current_input * (1.0 - exp_term)
        
        # Check for spike
        if self.v >= self.v_threshold:
            self.last_spike_time = t
            self.refractory_until = t + self.refractory_period
            self.v = self.v_reset
            return True
            
        return False


class AdExNeuron(SpikingNeuron):
    """
    Adaptive Exponential Integrate-and-Fire (AdEx) neuron model.
    
    The AdEx neuron is described by the differential equations:
    τ_m * dv/dt = -(v - E_L) + Δ_T * exp((v - v_T)/Δ_T) - w + R_m * I(t)
    τ_w * dw/dt = a(v - E_L) - w
    
    Where:
    - τ_m is the membrane time constant
    - v is the membrane potential
    - E_L is the leak reversal potential
    - Δ_T is the slope factor
    - v_T is the threshold potential
    - w is an adaptation variable
    - R_m is the membrane resistance
    - I(t) is the input current
    - τ_w is the adaptation time constant
    - a is the subthreshold adaptation parameter
    """
    
    def __init__(
        self,
        neuron_id: int,
        v_rest: float = -70.0,
        v_reset: float = -70.0,
        v_threshold: float = -50.0,
        membrane_resistance: float = 100.0,
        membrane_time_constant: float = 20.0,
        refractory_period: float = DEFAULT_REFRAC_PERIOD,
        delta_T: float = 2.0,
        v_T: float = -55.0,
        adaptation_time_constant: float = 100.0,
        a: float = 4.0,  # Subthreshold adaptation parameter
        b: float = 80.0,  # Spike-triggered adaptation parameter
    ):
        """
        Initialize AdEx neuron.
        
        Args:
            neuron_id: Unique identifier for this neuron
            v_rest: Resting/leak membrane potential E_L (mV)
            v_reset: Reset potential after spike (mV)
            v_threshold: Spike threshold, detection threshold (mV)
            membrane_resistance: Membrane resistance (MΩ)
            membrane_time_constant: Membrane time constant (ms)
            refractory_period: Refractory period duration (ms)
            delta_T: Slope factor (mV)
            v_T: Threshold potential (mV)
            adaptation_time_constant: Adaptation time constant τ_w (ms)
            a: Subthreshold adaptation parameter (nS)
            b: Spike-triggered adaptation parameter (pA)
        """
        super().__init__(
            neuron_id=neuron_id,
            type=NeuronType.ADEX,
            v_rest=v_rest,
            v_reset=v_reset,
            v_threshold=v_threshold,
            membrane_resistance=membrane_resistance,
            membrane_time_constant=membrane_time_constant,
            refractory_period=refractory_period
        )
        
        self.delta_T = delta_T
        self.v_T = v_T
        self.adaptation_time_constant = adaptation_time_constant
        self.a = a
        self.b = b
        
        # Additional state variables
        self.w = 0.0  # Adaptation variable
    
    def reset(self):
        """Reset neuron state to initial conditions."""
        super().reset()
        self.w = 0.0
        
    def update(self, current_input: float, t: float, dt: float) -> bool:
        """
        Update AdEx neuron state and check for spike generation.
        
        Uses Euler method for numerical integration of the differential equations.
        
        Args:
            current_input: Input current in nA
            t: Current simulation time in ms
            dt: Simulation time step in ms
            
        Returns:
            True if neuron spiked, False otherwise
        """
        # Check if in refractory period
        if t < self.refractory_until:
            return False
            
        # Convert to base units for computation
        I = current_input * 1000.0  # Convert nA to pA
        
        # Compute membrane potential derivative
        # τ_m * dv/dt = -(v - E_L) + Δ_T * exp((v - v_T)/Δ_T) - w + R_m * I(t)
        if (self.v - self.v_T) / self.delta_T > 20:
            # Prevent exponential explosion for numerical stability
            exp_term = np.exp(20)
        else:
            exp_term = np.exp((self.v - self.v_T) / self.delta_T)
            
        dv_dt = (-(self.v - self.v_rest) + self.delta_T * exp_term - self.w + 
                self.membrane_resistance * I) / self.membrane_time_constant
        
        # Compute adaptation variable derivative
        # τ_w * dw/dt = a(v - E_L) - w
        dw_dt = (self.a * (self.v - self.v_rest) - self.w) / self.adaptation_time_constant
        
        # Update state variables using Euler method
        self.v += dv_dt * dt
        self.w += dw_dt * dt
        
        # Check for spike
        if self.v >= self.v_threshold:
            self.last_spike_time = t
            self.refractory_until = t + self.refractory_period
            self.v = self.v_reset
            self.w += self.b  # Spike-triggered adaptation
            return True
            
        return False


class IzhikevichNeuron(SpikingNeuron):
    """
    Izhikevich neuron model.
    
    The Izhikevich neuron is described by the differential equations:
    dv/dt = 0.04v² + 5v + 140 - u + I
    du/dt = a(bv - u)
    
    With after-spike resetting:
    if v ≥ v_threshold then v ← c, u ← u + d
    
    Where:
    - v is the membrane potential
    - u is a recovery variable
    - a is the time scale of the recovery variable
    - b is the sensitivity of recovery variable to subthreshold fluctuations
    - c is the post-spike reset value of the membrane potential
    - d is the post-spike increment of the recovery variable
    
    The model can exhibit various firing patterns based on parameter values:
    - Regular spiking: a=0.02, b=0.2, c=-65, d=8
    - Intrinsically bursting: a=0.02, b=0.2, c=-55, d=4
    - Chattering: a=0.02, b=0.2, c=-50, d=2
    - Fast spiking: a=0.1, b=0.2, c=-65, d=2
    - Low-threshold spiking: a=0.02, b=0.25, c=-65, d=2
    - Resonator: a=0.1, b=0.26, c=-65, d=2
    """
    
    def __init__(
        self,
        neuron_id: int,
        a: float = 0.02,  # Recovery time constant
        b: float = 0.2,   # Sensitivity to subthreshold fluctuations
        c: float = -65.0, # Post-spike reset value of membrane potential
        d: float = 8.0,   # Post-spike recovery increment
        v_threshold: float = 30.0,  # Threshold for spike detection
        v_initial: float = -70.0,   # Initial membrane potential
        neuron_behavior: str = "regular_spiking"  # Pre-configured behavior
    ):
        """
        Initialize Izhikevich neuron.
        
        Args:
            neuron_id: Unique identifier for this neuron
            a: Recovery time constant
            b: Coupling parameter
            c: Voltage reset value after spike
            d: Recovery variable increment after spike
            v_threshold: Spike threshold
            v_initial: Initial membrane potential
            neuron_behavior: Pre-configured behavior type (optional)
        """
        # If a pre-configured behavior type is specified, override parameters
        if neuron_behavior == "regular_spiking":
            a, b, c, d = 0.02, 0.2, -65.0, 8.0
        elif neuron_behavior == "intrinsically_bursting":
            a, b, c, d = 0.02, 0.2, -55.0, 4.0
        elif neuron_behavior == "chattering":
            a, b, c, d = 0.02, 0.2, -50.0, 2.0
        elif neuron_behavior == "fast_spiking":
            a, b, c, d = 0.1, 0.2, -65.0, 2.0
        elif neuron_behavior == "low_threshold_spiking":
            a, b, c, d = 0.02, 0.25, -65.0, 2.0
        elif neuron_behavior == "resonator":
            a, b, c, d = 0.1, 0.26, -65.0, 2.0
        
        super().__init__(
            neuron_id=neuron_id,
            type=NeuronType.IZHIKEVICH,
            v_threshold=v_threshold,
            # Izhikevich doesn't directly use these parameters, but we need them for base class
            v_rest=c,
            v_reset=c,
            membrane_resistance=1.0,
            membrane_time_constant=1.0,
            refractory_period=0.0  # Izhikevich handles refractory period differently
        )
        
        self.a = a
        self.b = b
        self.c = c
        self.d = d
        
        # State variables
        self.v = v_initial
        self.u = b * v_initial  # Recovery variable
    
    def reset(self):
        """Reset neuron state to initial conditions."""
        self.v = self.c
        self.u = self.b * self.c
        self.last_spike_time = -float('inf')
        
    def update(self, current_input: float, t: float, dt: float) -> bool:
        """
        Update Izhikevich neuron state and check for spike generation.
        
        Args:
            current_input: Input current in nA
            t: Current simulation time in ms
            dt: Simulation time step in ms
            
        Returns:
            True if neuron spiked, False otherwise
        """
        # Update membrane potential and recovery variable using Euler method
        dv = (0.04 * self.v**2 + 5 * self.v + 140 - self.u + current_input) * dt
        du = (self.a * (self.b * self.v - self.u)) * dt
        
        self.v += dv
        self.u += du
        
        # Check for spike
        if self.v >= self.v_threshold:
            self.v = self.c
            self.u += self.d
            self.last_spike_time = t
            return True
            
        return False


@dataclass
class STDPSynapse:
    """
    Spike-Timing-Dependent Plasticity (STDP) synapse.
    
    STDP modifies synaptic weights based on the relative timing of pre- and post-synaptic spikes:
    Δw_ij = {
        A_+ * exp(-Δt/τ_+)  if Δt > 0
        A_- * exp(Δt/τ_-)   if Δt < 0
    }
    
    Where:
    - Δt = t_post - t_pre is the time difference between post- and pre-synaptic spikes
    - A_+, A_-, τ_+, τ_- are parameters controlling the learning rate and temporal window
    """
    
    synapse_id: int
    pre_id: int      # ID of presynaptic neuron
    post_id: int     # ID of postsynaptic neuron
    weight: float    # Synaptic weight
    delay: float = 1.0  # Synaptic delay (ms)
    
    # STDP parameters
    stdp_enabled: bool = True
    a_plus: float = 0.01    # Learning rate for potentiation
    a_minus: float = 0.0105  # Learning rate for depression (slightly stronger for stability)
    tau_plus: float = 20.0   # Time constant for potentiation (ms)
    tau_minus: float = 20.0  # Time constant for depression (ms)
    w_min: float = 0.0      # Minimum weight
    w_max: float = 1.0      # Maximum weight
    
    # State variables
    pre_trace: float = field(default=0.0)   # Presynaptic trace
    post_trace: float = field(default=0.0)  # Postsynaptic trace
    
    def reset(self):
        """Reset synapse state to initial conditions."""
        self.pre_trace = 0.0
        self.post_trace = 0.0
    
    def update_traces(self, dt: float):
        """
        Update pre- and post-synaptic traces.
        
        Args:
            dt: Simulation time step in ms
        """
        # Exponential decay of traces
        self.pre_trace *= np.exp(-dt / self.tau_plus)
        self.post_trace *= np.exp(-dt / self.tau_minus)
    
    def pre_spike(self, t: float):
        """
        Process presynaptic spike.
        
        Args:
            t: Current time in ms
        """
        # Update weight based on post trace (post-before-pre: depression)
        if self.stdp_enabled:
            dw = -self.a_minus * self.post_trace
            self.weight = max(self.w_min, min(self.w_max, self.weight + dw))
        
        # Increment pre trace
        self.pre_trace += 1.0
    
    def post_spike(self, t: float):
        """
        Process postsynaptic spike.
        
        Args:
            t: Current time in ms
        """
        # Update weight based on pre trace (pre-before-post: potentiation)
        if self.stdp_enabled:
            dw = self.a_plus * self.pre_trace
            self.weight = max(self.w_min, min(self.w_max, self.weight + dw))
        
        # Increment post trace
        self.post_trace += 1.0


@dataclass
class HomeostaticPlasticity:
    """
    Homeostatic plasticity mechanism to maintain target firing rates.
    
    The mechanism adjusts neuronal excitability to keep average firing rates
    within a target range:
    
    dθ_i/dt = α_h(r_i - r_target)
    
    Where:
    - θ_i is the firing threshold of neuron i
    - r_i is its actual firing rate
    - r_target is the target firing rate
    - α_h is a learning rate
    """
    
    neuron_id: int
    target_rate: float       # Target firing rate (Hz)
    learning_rate: float     # Rate of threshold adjustment
    threshold_min: float     # Minimum threshold value
    threshold_max: float     # Maximum threshold value
    time_window: float       # Time window for rate estimation (ms)
    
    # State variables
    spike_times: List[float] = field(default_factory=list)  # Recent spike times
    current_threshold: Optional[float] = None  # Current threshold adjustment
    
    def __post_init__(self):
        self.spike_times = []
        # Threshold adjustment starts at zero
        self.current_threshold = 0.0
    
    def add_spike(self, t: float):
        """
        Record a spike from the neuron.
        
        Args:
            t: Spike time in ms
        """
        self.spike_times.append(t)
        # Remove spikes outside the time window
        self.spike_times = [st for st in self.spike_times if t - st <= self.time_window]
    
    def update_threshold(self, t: float) -> float:
        """
        Update the threshold based on recent firing rate.
        
        Args:
            t: Current time in ms
            
        Returns:
            Current threshold adjustment
        """
        # Calculate current firing rate in Hz
        rate = len(self.spike_times) / (self.time_window / 1000.0)
        
        # Update threshold adjustment
        self.current_threshold += self.learning_rate * (rate - self.target_rate)
        
        # Ensure threshold adjustment stays within bounds
        self.current_threshold = max(
            self.threshold_min, 
            min(self.threshold_max, self.current_threshold)
        )
        
        return self.current_threshold


class SpikingNetwork:
    """
    Network of spiking neurons with plastic synapses.
    """
    
    def __init__(self, dt: float = DEFAULT_SIMULATION_DT):
        """
        Initialize spiking neural network.
        
        Args:
            dt: Simulation time step in ms
        """
        self.dt = dt
        
        # Network components
        self.neurons: Dict[int, SpikingNeuron] = {}
        self.synapses: Dict[int, STDPSynapse] = {}
        self.homeostatic_mechanisms: Dict[int, HomeostaticPlasticity] = {}
        
        # Connectivity information
        self.pre_synapses: Dict[int, List[int]] = defaultdict(list)  # neuron_id -> [synapse_ids]
        self.post_synapses: Dict[int, List[int]] = defaultdict(list)  # neuron_id -> [synapse_ids]
        
        # Spike propagation
        self.spike_queue: List[Tuple[float, int, int]] = []  # (delivery_time, synapse_id, pre_id)
        
        # Monitoring
        self.spike_record: Dict[int, List[float]] = defaultdict(list)  # neuron_id -> [spike_times]
        self.voltage_record: Dict[int, List[float]] = {}  # neuron_id -> [voltage_values]
        self.voltage_times: List[float] = []  # Times of voltage recordings
        
        # Simulation state
        self.current_time = 0.0
        self.next_id = 0
    
    def add_neuron(self, neuron_type: str, **params) -> int:
        """
        Add a neuron to the network.
        
        Args:
            neuron_type: Type of neuron (e.g., "lif", "adex", "izhikevich")
            **params: Parameters for the neuron
            
        Returns:
            ID of the added neuron
        """
        neuron_id = self.next_id
        self.next_id += 1
        
        # Create neuron based on type
        neuron_type = neuron_type.lower()
        
        if neuron_type == "lif":
            neuron = LIFNeuron(neuron_id=neuron_id, **params)
        elif neuron_type == "adex":
            neuron = AdExNeuron(neuron_id=neuron_id, **params)
        elif neuron_type == "izhikevich":
            neuron = IzhikevichNeuron(neuron_id=neuron_id, **params)
        else:
            raise ValueError(f"Unsupported neuron type: {neuron_type}")
        
        self.neurons[neuron_id] = neuron
        
        # Initialize spike and voltage recording for this neuron if requested
        if params.get("record_voltage", False):
            self.voltage_record[neuron_id] = []
        
        return neuron_id
    
    def add_synapse(
        self,
        pre_id: int,
        post_id: int,
        weight: float,
        delay: float = 1.0,
        stdp_enabled: bool = False,
        **stdp_params
    ) -> int:
        """
        Add a synapse to the network.
        
        Args:
            pre_id: ID of presynaptic neuron
            post_id: ID of postsynaptic neuron
            weight: Initial synaptic weight
            delay: Synaptic delay in ms
            stdp_enabled: Whether to enable STDP for this synapse
            **stdp_params: STDP parameters
            
        Returns:
            ID of the added synapse
        """
        # Verify neurons exist
        if pre_id not in self.neurons:
            raise ValueError(f"Presynaptic neuron with ID {pre_id} not found")
        if post_id not in self.neurons:
            raise ValueError(f"Postsynaptic neuron with ID {post_id} not found")
        
        synapse_id = self.next_id
        self.next_id += 1
        
        # Create synapse
        synapse = STDPSynapse(
            synapse_id=synapse_id,
            pre_id=pre_id,
            post_id=post_id,
            weight=weight,
            delay=delay,
            stdp_enabled=stdp_enabled,
            **stdp_params
        )
        
        self.synapses[synapse_id] = synapse
        
        # Update connectivity information
        self.pre_synapses[pre_id].append(synapse_id)
        self.post_synapses[post_id].append(synapse_id)
        
        return synapse_id
    
    def add_homeostatic_plasticity(
        self,
        neuron_id: int,
        target_rate: float = 10.0,  # Hz
        learning_rate: float = 0.001,
        threshold_min: float = -1.0,
        threshold_max: float = 1.0,
        time_window: float = 1000.0  # ms
    ) -> None:
        """
        Add homeostatic plasticity to a neuron.
        
        Args:
            neuron_id: ID of the neuron
            target_rate: Target firing rate in Hz
            learning_rate: Rate of threshold adjustment
            threshold_min: Minimum threshold adjustment
            threshold_max: Maximum threshold adjustment
            time_window: Time window for rate estimation in ms
        """
        # Verify neuron exists
        if neuron_id not in self.neurons:
            raise ValueError(f"Neuron with ID {neuron_id} not found")
        
        # Create homeostatic plasticity mechanism
        mechanism = HomeostaticPlasticity(
            neuron_id=neuron_id,
            target_rate=target_rate,
            learning_rate=learning_rate,
            threshold_min=threshold_min,
            threshold_max=threshold_max,
            time_window=time_window
        )
        
        self.homeostatic_mechanisms[neuron_id] = mechanism
    
    def create_population(
        self,
        size: int,
        neuron_type: str,
        **params
    ) -> List[int]:
        """
        Create a population of neurons with similar parameters.
        
        Args:
            size: Number of neurons to create
            neuron_type: Type of neuron (e.g., "lif", "adex", "izhikevich")
            **params: Parameters for the neurons
            
        Returns:
            List of neuron IDs
        """
        neuron_ids = []
        
        for _ in range(size):
            neuron_id = self.add_neuron(neuron_type=neuron_type, **params)
            neuron_ids.append(neuron_id)
        
        return neuron_ids
    
    def connect_all_to_all(
        self,
        pre_ids: List[int],
        post_ids: List[int],
        weight: float,
        weight_distribution: Optional[Tuple[str, Dict[str, Any]]] = None,
        delay: float = 1.0,
        stdp_enabled: bool = False,
        **stdp_params
    ) -> List[int]:
        """
        Create all-to-all connections between two populations.
        
        Args:
            pre_ids: IDs of presynaptic neurons
            post_ids: IDs of postsynaptic neurons
            weight: Base synaptic weight
            weight_distribution: Optional distribution for weights (type, params)
            delay: Synaptic delay in ms
            stdp_enabled: Whether to enable STDP
            **stdp_params: STDP parameters
            
        Returns:
            List of synapse IDs
        """
        synapse_ids = []
        
        for pre_id in pre_ids:
            for post_id in post_ids:
                # Skip self-connections
                if pre_id == post_id:
                    continue
                
                # Apply weight distribution if specified
                conn_weight = weight
                if weight_distribution is not None:
                    dist_type, dist_params = weight_distribution
                    if dist_type == "uniform":
                        min_val, max_val = dist_params["min"], dist_params["max"]
                        conn_weight = np.random.uniform(min_val, max_val)
                    elif dist_type == "normal":
                        mean, std = dist_params["mean"], dist_params["std"]
                        conn_weight = np.random.normal(mean, std)
                    elif dist_type == "log_normal":
                        mean, std = dist_params["mean"], dist_params["std"]
                        conn_weight = np.random.lognormal(mean, std)
                
                synapse_id = self.add_synapse(
                    pre_id=pre_id,
                    post_id=post_id,
                    weight=conn_weight,
                    delay=delay,
                    stdp_enabled=stdp_enabled,
                    **stdp_params
                )
                synapse_ids.append(synapse_id)
        
        return synapse_ids
    
    def connect_random(
        self,
        pre_ids: List[int],
        post_ids: List[int],
        connection_prob: float,
        weight: float,
        weight_distribution: Optional[Tuple[str, Dict[str, Any]]] = None,
        delay: float = 1.0,
        stdp_enabled: bool = False,
        **stdp_params
    ) -> List[int]:
        """
        Create random connections between two populations.
        
        Args:
            pre_ids: IDs of presynaptic neurons
            post_ids: IDs of postsynaptic neurons
            connection_prob: Probability of creating each connection
            weight: Base synaptic weight
            weight_distribution: Optional distribution for weights (type, params)
            delay: Synaptic delay in ms
            stdp_enabled: Whether to enable STDP
            **stdp_params: STDP parameters
            
        Returns:
            List of synapse IDs
        """
        synapse_ids = []
        
        for pre_id in pre_ids:
            for post_id in post_ids:
                # Skip self-connections
                if pre_id == post_id:
                    continue
                
                # Decide whether to create connection
                if np.random.random() > connection_prob:
                    continue
                
                # Apply weight distribution if specified
                conn_weight = weight
                if weight_distribution is not None:
                    dist_type, dist_params = weight_distribution
                    if dist_type == "uniform":
                        min_val, max_val = dist_params["min"], dist_params["max"]
                        conn_weight = np.random.uniform(min_val, max_val)
                    elif dist_type == "normal":
                        mean, std = dist_params["mean"], dist_params["std"]
                        conn_weight = np.random.normal(mean, std)
                    elif dist_type == "log_normal":
                        mean, std = dist_params["mean"], dist_params["std"]
                        conn_weight = np.random.lognormal(mean, std)
                
                synapse_id = self.add_synapse(
                    pre_id=pre_id,
                    post_id=post_id,
                    weight=conn_weight,
                    delay=delay,
                    stdp_enabled=stdp_enabled,
                    **stdp_params
                )
                synapse_ids.append(synapse_id)
        
        return synapse_ids
    
    def connect_distance_dependent(
        self,
        pre_ids: List[int],
        post_ids: List[int],
        positions: Dict[int, np.ndarray],
        distance_function: Callable[[np.ndarray, np.ndarray], float],
        connection_function: Callable[[float], float],
        weight_function: Callable[[float], float],
        delay_function: Optional[Callable[[float], float]] = None,
        stdp_enabled: bool = False,
        **stdp_params
    ) -> List[int]:
        """
        Create distance-dependent connections between two populations.
        
        Args:
            pre_ids: IDs of presynaptic neurons
            post_ids: IDs of postsynaptic neurons
            positions: Dictionary mapping neuron IDs to position vectors
            distance_function: Function that computes distance between two positions
            connection_function: Function that maps distance to connection probability
            weight_function: Function that maps distance to synaptic weight
            delay_function: Optional function that maps distance to synaptic delay
            stdp_enabled: Whether to enable STDP
            **stdp_params: STDP parameters
            
        Returns:
            List of synapse IDs
        """
        synapse_ids = []
        
        for pre_id in pre_ids:
            pre_pos = positions.get(pre_id)
            if pre_pos is None:
                continue
                
            for post_id in post_ids:
                # Skip self-connections
                if pre_id == post_id:
                    continue
                
                post_pos = positions.get(post_id)
                if post_pos is None:
                    continue
                
                # Compute distance
                distance = distance_function(pre_pos, post_pos)
                
                # Determine connection probability
                conn_prob = connection_function(distance)
                
                # Decide whether to create connection
                if np.random.random() > conn_prob:
                    continue
                
                # Determine weight and delay
                weight = weight_function(distance)
                delay = 1.0  # Default delay
                if delay_function is not None:
                    delay = delay_function(distance)
                
                synapse_id = self.add_synapse(
                    pre_id=pre_id,
                    post_id=post_id,
                    weight=weight,
                    delay=delay,
                    stdp_enabled=stdp_enabled,
                    **stdp_params
                )
                synapse_ids.append(synapse_id)
        
        return synapse_ids
    
    def add_input_spikes(
        self,
        neuron_ids: List[int],
        spike_times: Dict[int, List[float]]
    ) -> None:
        """
        Add input spikes to specific neurons.
        
        Args:
            neuron_ids: IDs of neurons to receive spikes
            spike_times: Dictionary mapping neuron IDs to lists of spike times
        """
        for neuron_id in neuron_ids:
            if neuron_id in spike_times:
                times = spike_times[neuron_id]
                for t in times:
                    # Add spikes directly to the neuron's record
                    self.spike_record[neuron_id].append(t)
                    
                    # Process outgoing spikes immediately
                    for synapse_id in self.pre_synapses.get(neuron_id, []):
                        synapse = self.synapses[synapse_id]
                        delivery_time = t + synapse.delay
                        self.spike_queue.append((delivery_time, synapse_id, neuron_id))
                    
                    # Sort spike queue by delivery time
                    self.spike_queue.sort()
    
    def add_current_input(
        self,
        neuron_id: int,
        amplitude: float,
        start_time: float,
        end_time: float
    ) -> None:
        """
        Add constant current input to a neuron for a specified time period.
        
        Args:
            neuron_id: ID of the neuron
            amplitude: Input current amplitude (nA)
            start_time: Start time of the input (ms)
            end_time: End time of the input (ms)
        """
        if neuron_id not in self.neurons:
            raise ValueError(f"Neuron with ID {neuron_id} not found")
        
        if not hasattr(self, 'current_inputs'):
            self.current_inputs = defaultdict(list)
        
        self.current_inputs[neuron_id].append((start_time, end_time, amplitude))
    
    def add_time_varying_input(
        self,
        neuron_id: int,
        input_function: Callable[[float], float],
        start_time: float,
        end_time: float
    ) -> None:
        """
        Add time-varying input to a neuron.
        
        Args:
            neuron_id: ID of the neuron
            input_function: Function mapping time to input current (nA)
            start_time: Start time of the input (ms)
            end_time: End time of the input (ms)
        """
        if neuron_id not in self.neurons:
            raise ValueError(f"Neuron with ID {neuron_id} not found")
        
        if not hasattr(self, 'time_varying_inputs'):
            self.time_varying_inputs = defaultdict(list)
        
        self.time_varying_inputs[neuron_id].append((start_time, end_time, input_function))
    
    def get_neuron_input(self, neuron_id: int, t: float) -> float:
        """
        Calculate total input current to a neuron at given time.
        
        Args:
            neuron_id: ID of the neuron
            t: Current time (ms)
            
        Returns:
            Total input current (nA)
        """
        total_input = 0.0
        
        # Add constant current inputs
        if hasattr(self, 'current_inputs'):
            for start_time, end_time, amplitude in self.current_inputs.get(neuron_id, []):
                if start_time <= t < end_time:
                    total_input += amplitude
        
        # Add time-varying inputs
        if hasattr(self, 'time_varying_inputs'):
            for start_time, end_time, input_function in self.time_varying_inputs.get(neuron_id, []):
                if start_time <= t < end_time:
                    total_input += input_function(t)
        
        return total_input
    
    def reset(self) -> None:
        """Reset all neurons and synapses to initial conditions."""
        # Reset neurons
        for neuron in self.neurons.values():
            neuron.reset()
        
        # Reset synapses
        for synapse in self.synapses.values():
            synapse.reset()
        
        # Clear spike queue
        self.spike_queue = []
        
        # Clear spike records
        self.spike_record = defaultdict(list)
        
        # Clear voltage records
        self.voltage_record = {k: [] for k in self.voltage_record}
        self.voltage_times = []
        
        # Reset time
        self.current_time = 0.0
    
    def run(
        self,
        duration: float,
        record_voltages: bool = False,
        voltage_record_dt: Optional[float] = None
    ) -> Dict[int, List[float]]:
        """
        Run network simulation for specified duration.
        
        Args:
            duration: Simulation duration (ms)
            record_voltages: Whether to record membrane potentials
            voltage_record_dt: Time step for voltage recording (ms, default: simulation dt)
            
        Returns:
            Dictionary mapping neuron IDs to lists of spike times
        """
        start_time = self.current_time
        end_time = start_time + duration
        
        # Set voltage recording time step
        if voltage_record_dt is None:
            voltage_record_dt = self.dt
        
        # Next time to record voltages
        next_voltage_record = start_time
        
        # Main simulation loop
        while self.current_time < end_time:
            # Record voltages if needed
            if record_voltages and self.current_time >= next_voltage_record:
                self.voltage_times.append(self.current_time)
                for neuron_id, v_record in self.voltage_record.items():
                    v_record.append(self.neurons[neuron_id].v)
                next_voltage_record += voltage_record_dt
            
            # Process spikes in queue that should be delivered at current time
            while self.spike_queue and self.spike_queue[0][0] <= self.current_time:
                delivery_time, synapse_id, pre_id = self.spike_queue.pop(0)
                
                # Get synapse and postsynaptic neuron
                synapse = self.synapses[synapse_id]
                post_id = synapse.post_id
                post_neuron = self.neurons[post_id]
                
                # Process presynaptic spike at synapse (for STDP)
                synapse.pre_spike(delivery_time)
                
                # Deliver spike to postsynaptic neuron (as input current)
                # The effect is instantaneous in simple SNN models
                if post_neuron.update(synapse.weight, self.current_time, self.dt):
                    # Postsynaptic neuron fired
                    self.spike_record[post_id].append(self.current_time)
                    
                    # Process postsynaptic spike at all synapses where this neuron is postsynaptic
                    for post_synapse_id in self.post_synapses.get(post_id, []):
                        self.synapses[post_synapse_id].post_spike(self.current_time)
                    
                    # Update homeostatic plasticity if enabled
                    if post_id in self.homeostatic_mechanisms:
                        self.homeostatic_mechanisms[post_id].add_spike(self.current_time)
                    
                    # Propagate spike to connected neurons
                    for out_synapse_id in self.pre_synapses.get(post_id, []):
                        out_synapse = self.synapses[out_synapse_id]
                        delivery_time = self.current_time + out_synapse.delay
                        self.spike_queue.append((delivery_time, out_synapse_id, post_id))
                    
                    # Keep spike queue sorted by delivery time
                    self.spike_queue.sort()
            
            # Update neuron states based on external inputs
            for neuron_id, neuron in self.neurons.items():
                # Skip neurons that have recently spiked and are in refractory period
                if self.current_time < neuron.refractory_until:
                    continue
                
                # Get input current for this neuron
                input_current = self.get_neuron_input(neuron_id, self.current_time)
                
                # Apply homeostatic threshold adjustment if enabled
                threshold_adjustment = 0.0
                if neuron_id in self.homeostatic_mechanisms:
                    threshold_adjustment = self.homeostatic_mechanisms[neuron_id].update_threshold(self.current_time)
                    neuron.v_threshold = neuron.v_threshold + threshold_adjustment
                
                # Update neuron state
                if input_current > 0 and neuron.update(input_current, self.current_time, self.dt):
                    # Neuron fired
                    self.spike_record[neuron_id].append(self.current_time)
                    
                    # Process postsynaptic spike at all synapses where this neuron is postsynaptic
                    for synapse_id in self.post_synapses.get(neuron_id, []):
                        self.synapses[synapse_id].post_spike(self.current_time)
                    
                    # Update homeostatic plasticity if enabled
                    if neuron_id in self.homeostatic_mechanisms:
                        self.homeostatic_mechanisms[neuron_id].add_spike(self.current_time)
                    
                    # Propagate spike to connected neurons
                    for synapse_id in self.pre_synapses.get(neuron_id, []):
                        synapse = self.synapses[synapse_id]
                        delivery_time = self.current_time + synapse.delay
                        self.spike_queue.append((delivery_time, synapse_id, neuron_id))
                    
                    # Keep spike queue sorted by delivery time
                    self.spike_queue.sort()
            
            # Update all synapse traces
            for synapse in self.synapses.values():
                synapse.update_traces(self.dt)
            
            # Advance simulation time
            self.current_time += self.dt
        
        return dict(self.spike_record)
    
    def get_firing_rates(
        self,
        neuron_ids: Optional[List[int]] = None,
        start_time: Optional[float] = None,
        end_time: Optional[float] = None
    ) -> Dict[int, float]:
        """
        Calculate firing rates over the specified time period.
        
        Args:
            neuron_ids: List of neuron IDs (if None, calculate for all neurons)
            start_time: Start time for rate calculation (ms)
            end_time: End time for rate calculation (ms)
            
        Returns:
            Dictionary mapping neuron IDs to firing rates (Hz)
        """
        if neuron_ids is None:
            neuron_ids = list(self.neurons.keys())
        
        if start_time is None:
            start_time = 0.0
        
        if end_time is None:
            end_time = self.current_time
        
        time_period = (end_time - start_time) / 1000.0  # Convert to seconds
        
        firing_rates = {}
        for neuron_id in neuron_ids:
            # Count spikes in the specified time period
            spike_times = self.spike_record.get(neuron_id, [])
            spike_count = sum(1 for t in spike_times if start_time <= t < end_time)
            
            # Calculate firing rate in Hz
            if time_period > 0:
                firing_rates[neuron_id] = spike_count / time_period
            else:
                firing_rates[neuron_id] = 0.0
        
        return firing_rates
    
    def get_weight_matrix(self) -> np.ndarray:
        """
        Get the weight matrix representation of the network connectivity.
        
        Returns:
            Array where element (i, j) is the weight from neuron i to neuron j
        """
        # Get neuron IDs and create mapping to indices
        neuron_ids = sorted(self.neurons.keys())
        id_to_index = {neuron_id: i for i, neuron_id in enumerate(neuron_ids)}
        
        # Create weight matrix
        n_neurons = len(neuron_ids)
        W = np.zeros((n_neurons, n_neurons))
        
        # Fill weight matrix
        for synapse in self.synapses.values():
            pre_idx = id_to_index.get(synapse.pre_id)
            post_idx = id_to_index.get(synapse.post_id)
            if pre_idx is not None and post_idx is not None:
                W[pre_idx, post_idx] = synapse.weight
        
        return W
    
    def plot_spikes(
        self,
        neuron_ids: Optional[List[int]] = None,
        start_time: Optional[float] = None,
        end_time: Optional[float] = None,
        title: str = "Spike Raster Plot",
        figsize: Tuple[float, float] = (10, 6)
    ) -> None:
        """
        Plot spike raster for the specified neurons.
        
        Args:
            neuron_ids: List of neuron IDs to include (default: all neurons)
            start_time: Start time for plotting (ms)
            end_time: End time for plotting (ms)
            title: Plot title
            figsize: Figure size in inches
        """
        if neuron_ids is None:
            neuron_ids = sorted(self.neurons.keys())
        
        if start_time is None:
            start_time = 0.0
        
        if end_time is None:
            end_time = self.current_time
        
        # Create figure
        plt.figure(figsize=figsize)
        
        # Plot spike times for each neuron
        for i, neuron_id in enumerate(neuron_ids):
            spike_times = [t for t in self.spike_record.get(neuron_id, []) if start_time <= t < end_time]
            plt.plot(spike_times, [i] * len(spike_times), 'k.', markersize=5)
        
        # Set labels and title
        plt.xlabel('Time (ms)')
        plt.ylabel('Neuron ID')
        plt.title(title)
        plt.yticks(range(len(neuron_ids)), neuron_ids)
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.xlim(start_time, end_time)
        plt.tight_layout()
        
        plt.show()
    
    def plot_voltages(
        self,
        neuron_ids: Optional[List[int]] = None,
        start_time: Optional[float] = None,
        end_time: Optional[float] = None,
        title: str = "Membrane Potential",
        figsize: Tuple[float, float] = (10, 6)
    ) -> None:
        """
        Plot membrane potentials for the specified neurons.
        
        Args:
            neuron_ids: List of neuron IDs to include (default: all recorded neurons)
            start_time: Start time for plotting (ms)
            end_time: End time for plotting (ms)
            title: Plot title
            figsize: Figure size in inches
        """
        if not self.voltage_record:
            logger.warning("No voltage data recorded. Set record_voltages=True in run().")
            return
        
        if neuron_ids is None:
            neuron_ids = sorted(self.voltage_record.keys())
        else:
            # Filter out neurons without voltage recordings
            neuron_ids = [nid for nid in neuron_ids if nid in self.voltage_record]
        
        if not neuron_ids:
            logger.warning("No voltage data for the specified neurons.")
            return
        
        if start_time is None:
            start_time = 0.0
        
        if end_time is None:
            end_time = self.current_time
        
        # Create figure
        plt.figure(figsize=figsize)
        
        # Filter time points within the specified range
        time_mask = [(t >= start_time and t < end_time) for t in self.voltage_times]
        plot_times = [t for t, m in zip(self.voltage_times, time_mask) if m]
        
        # Plot voltage traces for each neuron
        for neuron_id in neuron_ids:
            v_trace = self.voltage_record[neuron_id]
            plot_v = [v for v, m in zip(v_trace, time_mask) if m]
            
            plt.plot(plot_times, plot_v, label=f"Neuron {neuron_id}")
            
            # Add spike markers
            spike_times = [t for t in self.spike_record.get(neuron_id, []) if start_time <= t < end_time]
            if spike_times:
                # Find nearest voltage recording time for each spike
                spike_indices = [np.abs(np.array(plot_times) - t).argmin() for t in spike_times if t < end_time]
                spike_v = [plot_v[i] if i < len(plot_v) else plot_v[-1] for i in spike_indices]
                plt.plot(spike_times, spike_v, 'ro', markersize=5)
        
        # Add threshold line if all neurons have the same threshold
        thresholds = set(self.neurons[neuron_id].v_threshold for neuron_id in neuron_ids)
        if len(thresholds) == 1:
            plt.axhline(y=list(thresholds)[0], color='r', linestyle='--', alpha=0.5, label="Threshold")
            
        # Set labels and title
        plt.xlabel('Time (ms)')
        plt.ylabel('Membrane Potential (mV)')
        plt.title(title)
        plt.legend()
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.xlim(start_time, end_time)
        plt.tight_layout()
        
        plt.show()


class SpikeEncoder:
    """
    Encodes continuous input signals into spike trains.
    """
    
    def __init__(self, encoding_type: str = "rate", **params):
        """
        Initialize spike encoder.
        
        Args:
            encoding_type: Type of encoding scheme ("rate", "temporal", "phase", "ttfs")
            **params: Parameters specific to the encoding scheme
        """
        self.encoding_type = encoding_type.lower()
        self.params = params
    
    def encode(
        self,
        signal: np.ndarray,
        t_start: float,
        t_end: float,
        dt: Optional[float] = None
    ) -> Dict[int, List[float]]:
        """
        Encode signal into spike times.
        
        Args:
            signal: Input signal to encode (1D or 2D array)
            t_start: Start time for encoding (ms)
            t_end: End time for encoding (ms)
            dt: Time resolution for encoding (ms)
            
        Returns:
            Dictionary mapping neuron indices to lists of spike times
        """
        if dt is None:
            dt = DEFAULT_SIMULATION_DT
        
        # Ensure signal is 2D (channels x time)
        if signal.ndim == 1:
            signal = signal.reshape(1, -1)
        
        # Normalize signal to [0, 1] by default
        if self.params.get("normalize", True):
            signal_min = signal.min(axis=1, keepdims=True)
            signal_max = signal.max(axis=1, keepdims=True)
            signal_range = signal_max - signal_min
            signal_range[signal_range == 0] = 1.0  # Avoid division by zero
            normalized_signal = (signal - signal_min) / signal_range
        else:
            normalized_signal = signal
        
        # Choose encoding scheme
        if self.encoding_type == "rate":
            return self._rate_encoding(normalized_signal, t_start, t_end, dt)
        elif self.encoding_type == "temporal":
            return self._temporal_encoding(normalized_signal, t_start, t_end, dt)
        elif self.encoding_type == "phase":
            return self._phase_encoding(normalized_signal, t_start, t_end, dt)
        elif self.encoding_type == "ttfs":
            return self._ttfs_encoding(normalized_signal, t_start, t_end, dt)
        else:
            raise ValueError(f"Unsupported encoding type: {self.encoding_type}")
    
    def _rate_encoding(
        self,
        signal: np.ndarray,
        t_start: float,
        t_end: float,
        dt: float
    ) -> Dict[int, List[float]]:
        """
        Rate encoding: signal amplitude determines firing rate.
        
        Args:
            signal: Normalized input signal (channels x time)
            t_start: Start time for encoding (ms)
            t_end: End time for encoding (ms)
            dt: Time resolution for encoding (ms)
            
        Returns:
            Dictionary mapping neuron indices to lists of spike times
        """
        n_channels, n_samples = signal.shape
        
        # Set encoding parameters
        max_rate = self.params.get("max_rate", 200.0)  # Hz
        min_rate = self.params.get("min_rate", 0.0)    # Hz
        
        # Time points for the encoding window
        encode_duration = t_end - t_start
        
        # Create time bins for signal if needed
        if n_samples == 1:
            # Single value per channel, constant throughout the encoding window
            signal = np.repeat(signal, 2, axis=1)
            t_signal = np.array([t_start, t_end])
        else:
            # Multiple values per channel, interpolate as needed
            t_signal = np.linspace(t_start, t_end, n_samples)
        
        spike_times = {}
        for channel in range(n_channels):
            # Extract signal for this channel
            channel_signal = signal[channel]
            
            spikes = []
            for i in range(len(t_signal) - 1):
                # Get time segment
                t0, t1 = t_signal[i], t_signal[i+1]
                seg_duration = t1 - t0
                
                # Compute average rate in this segment
                avg_value = (channel_signal[i] + channel_signal[i+1]) / 2
                rate = min_rate + avg_value * (max_rate - min_rate)  # Hz
                
                # Expected number of spikes in this segment
                expected_spikes = rate * seg_duration / 1000.0  # Convert ms to s
                
                # Generate spikes using Poisson process
                if expected_spikes > 0:
                    # Determine number of spikes (Poisson distribution)
                    n_spikes = np.random.poisson(expected_spikes)
                    
                    # Distribute spikes uniformly in the segment
                    for _ in range(n_spikes):
                        spike_time = t0 + np.random.random() * seg_duration
                        spikes.append(spike_time)
            
            # Sort spikes by time
            spikes.sort()
            spike_times[channel] = spikes
        
        return spike_times
    
    def _temporal_encoding(
        self,
        signal: np.ndarray,
        t_start: float,
        t_end: float,
        dt: float
    ) -> Dict[int, List[float]]:
        """
        Temporal encoding: signal encoded in precise spike timing patterns.
        
        Args:
            signal: Normalized input signal (channels x time)
            t_start: Start time for encoding (ms)
            t_end: End time for encoding (ms)
            dt: Time resolution for encoding (ms)
            
        Returns:
            Dictionary mapping neuron indices to lists of spike times
        """
        n_channels, n_samples = signal.shape
        
        # Set encoding parameters
        n_neurons_per_channel = self.params.get("n_neurons_per_channel", 8)
        base_rate = self.params.get("base_rate", 20.0)  # Hz
        
        # Time points for the encoding window
        encode_duration = t_end - t_start
        
        # Create time bins for signal if needed
        if n_samples == 1:
            # Single value per channel, constant throughout the encoding window
            signal = np.repeat(signal, 2, axis=1)
            t_signal = np.array([t_start, t_end])
        else:
            # Multiple values per channel, interpolate as needed
            t_signal = np.linspace(t_start, t_end, n_samples)
        
        spike_times = {}
        neuron_index = 0
        
        for channel in range(n_channels):
            # Extract signal for this channel
            channel_signal = signal[channel]
            
            # Create neurons with receptive fields at different signal levels
            for n in range(n_neurons_per_channel):
                # Receptive field center (evenly spaced in [0, 1])
                center = (n + 0.5) / n_neurons_per_channel
                
                # Width of receptive field
                width = 1.0 / (n_neurons_per_channel - 0.5)
                
                spikes = []
                for i in range(len(t_signal) - 1):
                    # Get time segment
                    t0, t1 = t_signal[i], t_signal[i+1]
                    seg_duration = t1 - t0
                    
                    # Compute response for each value in the segment
                    for j in range(int(seg_duration / dt)):
                        t = t0 + j * dt
                        
                        # Interpolate signal value at time t
                        alpha = (t - t0) / (t1 - t0) if t1 > t0 else 0
                        value = channel_signal[i] * (1 - alpha) + channel_signal[i+1] * alpha
                        
                        # Compute neuron's response (Gaussian receptive field)
                        response = np.exp(-((value - center) / width)**2)
                        
                        # Calculate probability of spiking in this time step
                        p_spike = response * base_rate * dt / 1000.0  # Convert to probability
                        
                        # Generate spike
                        if np.random.random() < p_spike:
                            spikes.append(t)
                
                # Sort spikes by time
                spikes.sort()
                spike_times[neuron_index] = spikes
                neuron_index += 1
        
        return spike_times
    
    def _phase_encoding(
        self,
        signal: np.ndarray,
        t_start: float,
        t_end: float,
        dt: float
    ) -> Dict[int, List[float]]:
        """
        Phase encoding: signal encoded as phase relative to a reference oscillation.
        
        Args:
            signal: Normalized input signal (channels x time)
            t_start: Start time for encoding (ms)
            t_end: End time for encoding (ms)
            dt: Time resolution for encoding (ms)
            
        Returns:
            Dictionary mapping neuron indices to lists of spike times
        """
        n_channels, n_samples = signal.shape
        
        # Set encoding parameters
        freq = self.params.get("frequency", 5.0)  # Hz - Carrier frequency
        phase_range = self.params.get("phase_range", np.pi)  # Max phase shift
        
        # Period of oscillation in ms
        T = 1000.0 / freq
        
        # Create time bins for signal if needed
        if n_samples == 1:
            # Single value per channel, constant throughout the encoding window
            signal = np.repeat(signal, 2, axis=1)
            t_signal = np.array([t_start, t_end])
        else:
            # Multiple values per channel, interpolate as needed
            t_signal = np.linspace(t_start, t_end, n_samples)
        
        spike_times = {}
        
        for channel in range(n_channels):
            # Extract signal for this channel
            channel_signal = signal[channel]
            
            spikes = []
            
            # Generate reference oscillation cycles
            for cycle_start in np.arange(t_start, t_end, T):
                if cycle_start + T > t_end:
                    continue
                
                # Find signal value for this cycle
                # Interpolate signal at cycle midpoint
                cycle_mid = cycle_start + T/2
                i = np.searchsorted(t_signal, cycle_mid) - 1
                i = max(0, min(i, len(t_signal) - 2))
                
                t0, t1 = t_signal[i], t_signal[i+1]
                alpha = (cycle_mid - t0) / (t1 - t0) if t1 > t0 else 0
                value = channel_signal[i] * (1 - alpha) + channel_signal[i+1] * alpha
                
                # Convert to phase shift (0 -> 0, 1 -> phase_range)
                phase_shift = value * phase_range
                
                # Calculate spike time
                spike_time = cycle_start + (phase_shift / (2 * np.pi)) * T
                if spike_time < t_end:
                    spikes.append(spike_time)
            
            spike_times[channel] = spikes
        
        return spike_times
    
    def _ttfs_encoding(
        self,
        signal: np.ndarray,
        t_start: float,
        t_end: float,
        dt: float
    ) -> Dict[int, List[float]]:
        """
        Time-to-first-spike encoding: signal encoded in the timing of the first spike.
        
        Args:
            signal: Normalized input signal (channels x time)
            t_start: Start time for encoding (ms)
            t_end: End time for encoding (ms)
            dt: Time resolution for encoding (ms)
            
        Returns:
            Dictionary mapping neuron indices to lists of spike times
        """
        n_channels, n_samples = signal.shape
        
        # Set encoding parameters
        window_size = self.params.get("window_size", 50.0)  # ms
        max_delay = self.params.get("max_delay", 40.0)      # ms
        
        # Create time bins for signal if needed
        if n_samples == 1:
            # Single value per channel, constant throughout the encoding window
            signal = np.repeat(signal, 2, axis=1)
            t_signal = np.array([t_start, t_end])
        else:
            # Multiple values per channel, interpolate as needed
            t_signal = np.linspace(t_start, t_end, n_samples)
        
        spike_times = {}
        
        for channel in range(n_channels):
            # Extract signal for this channel
            channel_signal = signal[channel]
            
            spikes = []
            
            # Process signal in windows
            for window_start in np.arange(t_start, t_end, window_size):
                window_end = min(window_start + window_size, t_end)
                
                # Find signal value for this window
                # Use maximum value in the window
                window_indices = np.where((t_signal >= window_start) & (t_signal < window_end))[0]
                
                if len(window_indices) > 0:
                    window_values = channel_signal[window_indices]
                    max_value = np.max(window_values)
                    
                    # Convert to delay (0 -> max_delay, 1 -> 0)
                    delay = max_delay * (1.0 - max_value)
                    
                    # Calculate spike time
                    spike_time = window_start + delay
                    if spike_time < window_end:
                        spikes.append(spike_time)
                
            spike_times[channel] = spikes
        
        return spike_times


class SpikeDecoder:
    """
    Decodes spike trains into continuous output signals.
    """
    
    def __init__(self, decoding_type: str = "rate", **params):
        """
        Initialize spike decoder.
        
        Args:
            decoding_type: Type of decoding scheme ("rate", "temporal", "first_spike")
            **params: Parameters specific to the decoding scheme
        """
        self.decoding_type = decoding_type.lower()
        self.params = params
    
    def decode(
        self,
        spike_trains: Dict[int, List[float]],
        t_start: float,
        t_end: float,
        dt: Optional[float] = None,
        output_size: Optional[int] = None
    ) -> np.ndarray:
        """
        Decode spike trains into continuous signal.
        
        Args:
            spike_trains: Dictionary mapping neuron indices to lists of spike times
            t_start: Start time for decoding (ms)
            t_end: End time for decoding (ms)
            dt: Time resolution for decoding (ms)
            output_size: Size of output signal (default: automatic based on dt)
            
        Returns:
            Decoded output signal
        """
        if dt is None:
            dt = DEFAULT_SIMULATION_DT
        
        # Determine output size if not specified
        if output_size is None:
            output_size = int((t_end - t_start) / dt) + 1
        
        # Choose decoding scheme
        if self.decoding_type == "rate":
            return self._rate_decoding(spike_trains, t_start, t_end, dt, output_size)
        elif self.decoding_type == "temporal":
            return self._temporal_decoding(spike_trains, t_start, t_end, dt, output_size)
        elif self.decoding_type == "first_spike":
            return self._first_spike_decoding(spike_trains, t_start, t_end, dt, output_size)
        else:
            raise ValueError(f"Unsupported decoding type: {self.decoding_type}")
    
    def _rate_decoding(
        self,
        spike_trains: Dict[int, List[float]],
        t_start: float,
        t_end: float,
        dt: float,
        output_size: int
    ) -> np.ndarray:
        """
        Rate decoding: estimate signal from firing rates.
        
        Args:
            spike_trains: Dictionary mapping neuron indices to lists of spike times
            t_start: Start time for decoding (ms)
            t_end: End time for decoding (ms)
            dt: Time resolution for decoding (ms)
            output_size: Size of output signal
            
        Returns:
            Decoded output signal
        """
        # Set decoding parameters
        tau = self.params.get("tau", 30.0)  # Time constant for kernel (ms)
        
        # Create time points for decoding
        time_points = np.linspace(t_start, t_end, output_size)
        
        # Get neuron IDs
        neuron_ids = sorted(spike_trains.keys())
        n_neurons = len(neuron_ids)
        
        # Initialize output signal
        output_signal = np.zeros((n_neurons, output_size))
        
        # Apply exponential decay kernel to each spike
        for i, neuron_id in enumerate(neuron_ids):
            spike_times = spike_trains.get(neuron_id, [])
            
            for spike_time in spike_times:
                # Calculate time difference from spike to each output time point
                time_diff = time_points - spike_time
                
                # Apply exponential kernel for times after the spike
                kernel = np.zeros_like(time_diff)
                mask = time_diff >= 0
                kernel[mask] = np.exp(-time_diff[mask] / tau)
                
                # Add kernel to output signal
                output_signal[i] += kernel
        
        # Normalize output
        if self.params.get("normalize", True):
            for i in range(n_neurons):
                max_val = np.max(output_signal[i])
                if max_val > 0:
                    output_signal[i] /= max_val
        
        return output_signal
    
    def _temporal_decoding(
        self,
        spike_trains: Dict[int, List[float]],
        t_start: float,
        t_end: float,
        dt: float,
        output_size: int
    ) -> np.ndarray:
        """
        Temporal decoding: decode signal from temporal spike patterns.
        
        Args:
            spike_trains: Dictionary mapping neuron indices to lists of spike times
            t_start: Start time for decoding (ms)
            t_end: End time for decoding (ms)
            dt: Time resolution for decoding (ms)
            output_size: Size of output signal
            
        Returns:
            Decoded output signal
        """
        # Set decoding parameters
        n_neurons_per_channel = self.params.get("n_neurons_per_channel", 8)
        tau = self.params.get("tau", 20.0)  # Time constant for kernel (ms)
        
        # Create time points for decoding
        time_points = np.linspace(t_start, t_end, output_size)
        
        # Determine number of channels
        neuron_ids = sorted(spike_trains.keys())
        n_channels = len(neuron_ids) // n_neurons_per_channel
        if n_channels == 0:
            n_channels = 1
            n_neurons_per_channel = len(neuron_ids)
        
        # Initialize output signal
        output_signal = np.zeros((n_channels, output_size))
        
        # Process each channel
        for c in range(n_channels):
            start_idx = c * n_neurons_per_channel
            end_idx = min((c + 1) * n_neurons_per_channel, len(neuron_ids))
            channel_neurons = neuron_ids[start_idx:end_idx]
            
            # Define receptive field centers
            receptive_field_centers = np.linspace(0, 1, len(channel_neurons))
            
            # Apply weighted decoding based on receptive fields
            for i, neuron_id in enumerate(channel_neurons):
                center = receptive_field_centers[i]
                
                spike_times = spike_trains.get(neuron_id, [])
                
                for spike_time in spike_times:
                    # Calculate time difference from spike to each output time point
                    time_diff = time_points - spike_time
                    
                    # Apply exponential kernel for times after the spike
                    kernel = np.zeros_like(time_diff)
                    mask = time_diff >= 0
                    kernel[mask] = np.exp(-time_diff[mask] / tau)
                    
                    # Add weighted kernel to output signal
                    output_signal[c] += center * kernel
            
            # Normalize channel output
            max_val = np.max(output_signal[c])
            if max_val > 0:
                output_signal[c] /= max_val
        
        return output_signal
    
    def _first_spike_decoding(
        self,
        spike_trains: Dict[int, List[float]],
        t_start: float,
        t_end: float,
        dt: float,
        output_size: int
    ) -> np.ndarray:
        """
        First-spike decoding: decode signal from timing of first spikes.
        
        Args:
            spike_trains: Dictionary mapping neuron indices to lists of spike times
            t_start: Start time for decoding (ms)
            t_end: End time for decoding (ms)
            dt: Time resolution for decoding (ms)
            output_size: Size of output signal
            
        Returns:
            Decoded output signal
        """
        # Set decoding parameters
        window_size = self.params.get("window_size", 50.0)  # ms
        max_delay = self.params.get("max_delay", 40.0)      # ms
        
        # Create time points for decoding
        time_points = np.linspace(t_start, t_end, output_size)
        
        # Get neuron IDs
        neuron_ids = sorted(spike_trains.keys())
        n_neurons = len(neuron_ids)
        
        # Initialize output signal
        output_signal = np.zeros((n_neurons, output_size))
        
        # Process each neuron
        for i, neuron_id in enumerate(neuron_ids):
            spike_times = spike_trains.get(neuron_id, [])
            if not spike_times:
                continue
            
            # Process signal in windows
            for w, window_start in enumerate(np.arange(t_start, t_end, window_size)):
                window_end = min(window_start + window_size, t_end)
                
                # Find first spike in this window
                window_spikes = [s for s in spike_times if window_start <= s < window_end]
                if not window_spikes:
                    continue
                    
                first_spike = min(window_spikes)
                
                # Convert delay to signal value (0 -> 1, max_delay -> 0)
                delay = first_spike - window_start
                value = max(0.0, 1.0 - delay / max_delay)
                
                # Find time indices for this window
                window_indices = np.where((time_points >= window_start) & (time_points < window_end))[0]
                
                # Set output value for this window
                output_signal[i, window_indices] = value
        
        return output_signal


class PopulationRate:
    """
    Calculates population firing rates from spike trains.
    """
    
    def __init__(self, window: float = 50.0, dt: float = DEFAULT_SIMULATION_DT):
        """
        Initialize population rate calculator.
        
        Args:
            window: Sliding window size for rate calculation (ms)
            dt: Time resolution for output (ms)
        """
        self.window = window
        self.dt = dt
    
    def calculate(
        self,
        spike_trains: Dict[int, List[float]],
        t_start: float,
        t_end: float,
        population_ids: Optional[Dict[str, List[int]]] = None
    ) -> Dict[str, np.ndarray]:
        """
        Calculate population firing rates.
        
        Args:
            spike_trains: Dictionary mapping neuron indices to lists of spike times
            t_start: Start time for calculation (ms)
            t_end: End time for calculation (ms)
            population_ids: Optional dictionary mapping population names to lists of neuron IDs
            
        Returns:
            Dictionary mapping population names to rate time series
        """
        # Create time points for rate calculation
        time_points = np.arange(t_start, t_end, self.dt)
        n_points = len(time_points)
        
        # If population_ids not provided, use all neurons as one population
        if population_ids is None:
            population_ids = {"all": list(spike_trains.keys())}
        
        # Initialize result dictionary
        rates = {}
        
        # Calculate rates for each population
        for pop_name, neuron_ids in population_ids.items():
            # Initialize rate array
            pop_rate = np.zeros(n_points)
            
            # Count spikes in each time window
            for i, t in enumerate(time_points):
                window_start = t - self.window / 2
                window_end = t + self.window / 2
                
                spike_count = 0
                for neuron_id in neuron_ids:
                    if neuron_id in spike_trains:
                        spike_times = spike_trains[neuron_id]
                        spike_count += sum(1 for s in spike_times if window_start <= s < window_end)
                
                # Calculate rate in Hz (spikes per second)
                pop_rate[i] = spike_count / (len(neuron_ids) * self.window / 1000.0)
            
            rates[pop_name] = pop_rate
        
        return rates, time_points


class PhaseEncoding:
    """
    Phase-based encoding and analysis of spike trains.
    """
    
    def __init__(
        self,
        reference_freq: float = 5.0,  # Hz
        phase_bins: int = 36
    ):
        """
        Initialize phase encoding analyzer.
        
        Args:
            reference_freq: Frequency of reference oscillation (Hz)
            phase_bins: Number of bins for phase histograms
        """
        self.reference_freq = reference_freq
        self.phase_bins = phase_bins
        self.bin_edges = np.linspace(0, 2*np.pi, phase_bins + 1)
        self.bin_centers = 0.5 * (self.bin_edges[:-1] + self.bin_edges[1:])
    
    def calculate_phases(
        self,
        spike_trains: Dict[int, List[float]],
        t_start: float,
        t_end: float,
        phase_offset: float = 0.0
    ) -> Dict[int, np.ndarray]:
        """
        Calculate spike phases relative to reference oscillation.
        
        Args:
            spike_trains: Dictionary mapping neuron indices to lists of spike times
            t_start: Start time for calculation (ms)
            t_end: End time for calculation (ms)
            phase_offset: Phase offset for reference oscillation
            
        Returns:
            Dictionary mapping neuron IDs to arrays of spike phases
        """
        # Calculate reference period in ms
        period = 1000.0 / self.reference_freq
        
        # Initialize result dictionary
        spike_phases = {}
        
        # Calculate phases for each neuron
        for neuron_id, spike_times in spike_trains.items():
            # Filter spikes within time range
            filtered_spikes = [t for t in spike_times if t_start <= t < t_end]
            
            if not filtered_spikes:
                continue
            
            # Calculate phases (in radians)
            phases = [((t % period) / period * 2 * np.pi + phase_offset) % (2 * np.pi) for t in filtered_spikes]
            
            spike_phases[neuron_id] = np.array(phases)
        
        return spike_phases
    
    def phase_histogram(
        self,
        spike_phases: Dict[int, np.ndarray],
        neuron_id: Optional[int] = None,
        population_ids: Optional[List[int]] = None,
        normalize: bool = True
    ) -> np.ndarray:
        """
        Calculate phase histogram for a neuron or population.
        
        Args:
            spike_phases: Dictionary mapping neuron IDs to arrays of spike phases
            neuron_id: ID of specific neuron (if None, use population_ids)
            population_ids: List of neuron IDs to include in population
            normalize: Whether to normalize histogram
            
        Returns:
            Histogram counts for each phase bin
        """
        # Collect phases
        if neuron_id is not None:
            if neuron_id not in spike_phases:
                return np.zeros(self.phase_bins)
            phases = spike_phases[neuron_id]
        elif population_ids is not None:
            phases = np.concatenate([spike_phases[nid] for nid in population_ids if nid in spike_phases])
        else:
            phases = np.concatenate(list(spike_phases.values()))
        
        if len(phases) == 0:
            return np.zeros(self.phase_bins)
        
        # Calculate histogram
        hist, _ = np.histogram(phases, bins=self.bin_edges)
        
        # Normalize if requested
        if normalize and np.sum(hist) > 0:
            hist = hist / np.sum(hist)
        
        return hist
    
    def phase_locking_value(
        self,
        spike_phases: Dict[int, np.ndarray],
        neuron_id: Optional[int] = None,
        population_ids: Optional[List[int]] = None
    ) -> float:
        """
        Calculate phase-locking value (PLV).
        
        Args:
            spike_phases: Dictionary mapping neuron IDs to arrays of spike phases
            neuron_id: ID of specific neuron (if None, use population_ids)
            population_ids: List of neuron IDs to include in population
            
        Returns:
            Phase-locking value (0-1)
        """
        # Collect phases
        if neuron_id is not None:
            if neuron_id not in spike_phases:
                return 0.0
            phases = spike_phases[neuron_id]
        elif population_ids is not None:
            phases = np.concatenate([spike_phases[nid] for nid in population_ids if nid in spike_phases])
        else:
            phases = np.concatenate(list(spike_phases.values()))
        
        if len(phases) == 0:
            return 0.0
        
        # Convert phases to complex numbers on unit circle
        z = np.exp(1j * phases)
        
        # Calculate mean vector
        mean_vector = np.mean(z)
        
        # PLV is the magnitude of the mean vector
        plv = np.abs(mean_vector)
        
        return plv
    
    def phase_difference(
        self,
        spike_phases1: np.ndarray,
        spike_phases2: np.ndarray
    ) -> float:
        """
        Calculate circular mean of phase differences between two spike trains.
        
        Args:
            spike_phases1: Array of spike phases for first neuron/population
            spike_phases2: Array of spike phases for second neuron/population
            
        Returns:
            Mean phase difference in radians
        """
        if len(spike_phases1) == 0 or len(spike_phases2) == 0:
            return np.nan
        
        # Convert to complex numbers on unit circle
        z1 = np.exp(1j * spike_phases1)
        z2 = np.exp(1j * spike_phases2)
        
        # Calculate mean vectors
        mean_vector1 = np.mean(z1)
        mean_vector2 = np.mean(z2)
        
        # Phase difference is the angle between mean vectors
        phase_diff = np.angle(mean_vector2 / mean_vector1)
        
        return phase_diff


# Factory functions for creating standard networks

def create_izh_neuron_types() -> Dict[str, Dict[str, Any]]:
    """
    Create dictionary of standard Izhikevich neuron parameter sets.
    
    Returns:
        Dictionary mapping neuron types to parameter dictionaries
    """
    return {
        "regular_spiking": {
            "a": 0.02,
            "b": 0.2,
            "c": -65.0,
            "d": 8.0,
            "v_threshold": 30.0
        },
        "intrinsically_bursting": {
            "a": 0.02,
            "b": 0.2,
            "c": -55.0,
            "d": 4.0,
            "v_threshold": 30.0
        },
        "chattering": {
            "a": 0.02,
            "b": 0.2,
            "c": -50.0,
            "d": 2.0,
            "v_threshold": 30.0
        },
        "fast_spiking": {
            "a": 0.1,
            "b": 0.2,
            "c": -65.0,
            "d": 2.0,
            "v_threshold": 30.0
        },
        "low_threshold_spiking": {
            "a": 0.02,
            "b": 0.25,
            "c": -65.0,
            "d": 2.0,
            "v_threshold": 30.0
        },
        "resonator": {
            "a": 0.1,
            "b": 0.26,
            "c": -65.0,
            "d": 2.0,
            "v_threshold": 30.0
        }
    }


def create_balanced_network(
    n_excitatory: int = 800,
    n_inhibitory: int = 200,
    conn_prob: float = 0.1,
    weight_exc: float = 0.05,
    weight_inh: float = -0.45,
    neuron_type: str = "lif",
    **neuron_params
) -> SpikingNetwork:
    """
    Create a balanced network of excitatory and inhibitory neurons.
    
    Args:
        n_excitatory: Number of excitatory neurons
        n_inhibitory: Number of inhibitory neurons
        conn_prob: Connection probability
        weight_exc: Weight of excitatory synapses
        weight_inh: Weight of inhibitory synapses (negative)
        neuron_type: Type of neurons to use
        **neuron_params: Parameters for neurons
        
    Returns:
        Configured SpikingNetwork
    """
    # Create network
    network = SpikingNetwork()
    
    # Set default neuron parameters if not provided
    if neuron_type == "lif":
        default_params = {
            "v_rest": 0.0,
            "v_reset": 0.0,
            "v_threshold": 1.0,
            "membrane_resistance": 1.0,
            "membrane_time_constant": 20.0,
            "refractory_period": 2.0
        }
    elif neuron_type == "adex":
        default_params = {
            "v_rest": -70.0,
            "v_reset": -70.0,
            "v_threshold": -55.0,
            "membrane_resistance": 100.0,
            "membrane_time_constant": 20.0,
            "refractory_period": 2.0,
            "delta_T": 2.0,
            "v_T": -59.0,
            "adaptation_time_constant": 100.0,
            "a": 4.0,
            "b": 80.0
        }
    elif neuron_type == "izhikevich":
        default_params = {
            "a": 0.02,
            "b": 0.2,
            "c": -65.0,
            "d": 8.0,
            "v_threshold": 30.0
        }
    else:
        default_params = {}
    
    # Update with user-provided parameters
    for k, v in neuron_params.items():
        default_params[k] = v
    
    # Create excitatory and inhibitory populations
    exc_ids = network.create_population(n_excitatory, neuron_type, **default_params)
    inh_ids = network.create_population(n_inhibitory, neuron_type, **default_params)
    
    # Connect excitatory neurons to all neurons
    network.connect_random(
        pre_ids=exc_ids,
        post_ids=exc_ids + inh_ids,
        connection_prob=conn_prob,
        weight=weight_exc,
        stdp_enabled=False
    )
    
    # Connect inhibitory neurons to all neurons
    network.connect_random(
        pre_ids=inh_ids,
        post_ids=exc_ids + inh_ids,
        connection_prob=conn_prob,
        weight=weight_inh,
        stdp_enabled=False
    )
    
    return network, exc_ids, inh_ids


def create_synfire_chain(
    n_layers: int = 5,
    n_per_layer: int = 10,
    feed_forward_prob: float = 0.7,
    weight_ff: float = 0.2,
    weight_lateral: float = 0.1,
    noise_prob: float = 0.05,
    noise_weight: float = 0.05,
    neuron_type: str = "lif",
    **neuron_params
) -> SpikingNetwork:
    """
    Create a synfire chain network with feedforward and lateral connections.
    
    Args:
        n_layers: Number of layers in the chain
        n_per_layer: Number of neurons per layer
        feed_forward_prob: Connection probability between layers
        weight_ff: Weight of feedforward connections
        weight_lateral: Weight of lateral connections
        noise_prob: Probability of random connections (noise)
        noise_weight: Weight of noise connections
        neuron_type: Type of neurons to use
        **neuron_params: Parameters for neurons
        
    Returns:
        Configured SpikingNetwork and layer IDs
    """
    # Create network
    network = SpikingNetwork()
    
    # Set default neuron parameters if not provided
    if neuron_type == "lif" and not neuron_params:
        neuron_params = {
            "v_rest": 0.0,
            "v_reset": 0.0,
            "v_threshold": 0.9,
            "membrane_resistance": 1.0,
            "membrane_time_constant": 15.0,
            "refractory_period": 2.0
        }
    
    # Create layers
    layers = []
    for _ in range(n_layers):
        layer_ids = network.create_population(n_per_layer, neuron_type, **neuron_params)
        layers.append(layer_ids)
    
    # Create feedforward connections between consecutive layers
    for i in range(n_layers - 1):
        network.connect_random(
            pre_ids=layers[i],
            post_ids=layers[i + 1],
            connection_prob=feed_forward_prob,
            weight=weight_ff,
            stdp_enabled=False
        )
    
    # Create lateral connections within layers
    for i in range(n_layers):
        network.connect_random(
            pre_ids=layers[i],
            post_ids=layers[i],
            connection_prob=0.3,  # Within-layer connection probability
            weight=weight_lateral,
            stdp_enabled=False
        )
    
    # Add some random "noise" connections for robustness
    if noise_prob > 0:
        all_neurons = [neuron_id for layer in layers for neuron_id in layer]
        network.connect_random(
            pre_ids=all_neurons,
            post_ids=all_neurons,
            connection_prob=noise_prob,
            weight=noise_weight,
            stdp_enabled=False
        )
    
    return network, layers


def create_winner_take_all(
    n_neurons: int = 10,
    excitatory_weight: float = 0.1,
    inhibitory_weight: float = -0.5,
    input_weight: float = 0.2,
    neuron_type: str = "lif",
    **neuron_params
) -> SpikingNetwork:
    """
    Create a winner-take-all (WTA) network.
    
    Args:
        n_neurons: Number of neurons in the WTA circuit
        excitatory_weight: Weight of self-excitation
        inhibitory_weight: Weight of lateral inhibition
        input_weight: Weight of external input
        neuron_type: Type of neurons to use
        **neuron_params: Parameters for neurons
        
    Returns:
        Configured SpikingNetwork
    """
    # Create network
    network = SpikingNetwork()
    
    # Set default neuron parameters if not provided
    if neuron_type == "lif" and not neuron_params:
        neuron_params = {
            "v_rest": 0.0,
            "v_reset": 0.0,
            "v_threshold": 1.0,
            "membrane_resistance": 1.0,
            "membrane_time_constant": 20.0,
            "refractory_period": 5.0
        }
    
    # Create WTA neurons
    neuron_ids = network.create_population(n_neurons, neuron_type, **neuron_params)
    
    # Create inhibitory neuron
    inh_params = dict(neuron_params)
    inh_params.update({
        "v_threshold": 0.5,  # Lower threshold for inhibitory neuron
        "refractory_period": 2.0  # Shorter refractory period
    })
    inh_id = network.add_neuron(neuron_type, **inh_params)
    
    # Connect excitatory neurons to themselves (self-excitation)
    for i in range(n_neurons):
        network.add_synapse(
            pre_id=neuron_ids[i],
            post_id=neuron_ids[i],
            weight=excitatory_weight,
            delay=1.0,
            stdp_enabled=False
        )
    
    # Connect excitatory neurons to inhibitory neuron
    for i in range(n_neurons):
        network.add_synapse(
            pre_id=neuron_ids[i],
            post_id=inh_id,
            weight=input_weight,
            delay=1.0,
            stdp_enabled=False
        )
    
    # Connect inhibitory neuron to all excitatory neurons
    for i in range(n_neurons):
        network.add_synapse(
            pre_id=inh_id,
            post_id=neuron_ids[i],
            weight=inhibitory_weight,
            delay=1.0,
            stdp_enabled=False
        )
    
    return network, neuron_ids, inh_id


def create_stdp_learning_network(
    n_input: int = 100,
    n_output: int = 10,
    input_conn_prob: float = 0.3,
    initial_weight: float = 0.1,
    stdp_params: Optional[Dict[str, float]] = None,
    lateral_inhibition: bool = True,
    neuron_type: str = "lif",
    **neuron_params
) -> SpikingNetwork:
    """
    Create a network with STDP learning.
    
    Args:
        n_input: Number of input neurons
        n_output: Number of output neurons
        input_conn_prob: Connection probability from input to output
        initial_weight: Initial weight of connections
        stdp_params: STDP parameters
        lateral_inhibition: Whether to include lateral inhibition among output neurons
        neuron_type: Type of neurons to use
        **neuron_params: Parameters for neurons
        
    Returns:
        Configured SpikingNetwork
    """
    # Create network
    network = SpikingNetwork()
    
    # Set default neuron parameters if not provided
    if neuron_type == "lif" and not neuron_params:
        neuron_params = {
            "v_rest": 0.0,
            "v_reset": 0.0,
            "v_threshold": 1.0,
            "membrane_resistance": 1.0,
            "membrane_time_constant": 20.0,
            "refractory_period": 5.0
        }
    
    # Set default STDP parameters if not provided
    if stdp_params is None:
        stdp_params = {
            "a_plus": 0.01,
            "a_minus": 0.0105,
            "tau_plus": 20.0,
            "tau_minus": 20.0,
            "w_min": 0.0,
            "w_max": 0.5
        }
    
    # Create input and output populations
    input_ids = network.create_population(n_input, neuron_type, **neuron_params)
    output_ids = network.create_population(n_output, neuron_type, **neuron_params)
    
    # Connect input to output with STDP-enabled synapses
    network.connect_random(
        pre_ids=input_ids,
        post_ids=output_ids,
        connection_prob=input_conn_prob,
        weight=initial_weight,
        stdp_enabled=True,
        **stdp_params
    )
    
    # Add lateral inhibition among output neurons if requested
    if lateral_inhibition:
        for i in range(n_output):
            for j in range(n_output):
                if i != j:
                    network.add_synapse(
                        pre_id=output_ids[i],
                        post_id=output_ids[j],
                        weight=-0.2,  # Fixed inhibitory weight
                        delay=1.0,
                        stdp_enabled=False
                    )
    
    return network, input_ids, output_ids


def create_central_pattern_generator(
    n_oscillators: int = 4,
    connectivity_type: str = "ring",
    excitatory_weight: float = 0.2,
    inhibitory_weight: float = -0.5,
    self_inhibition_weight: float = -0.1,
    neuron_type: str = "lif",
    **neuron_params
) -> SpikingNetwork:
    """
    Create a central pattern generator (CPG) network.
    
    Args:
        n_oscillators: Number of oscillator pairs
        connectivity_type: Type of connectivity ("ring", "chain", "fully_connected")
        excitatory_weight: Weight of excitatory connections
        inhibitory_weight: Weight of inhibitory connections
        self_inhibition_weight: Weight of self-inhibition
        neuron_type: Type of neurons to use
        **neuron_params: Parameters for neurons
        
    Returns:
        Configured SpikingNetwork
    """
    # Create network
    network = SpikingNetwork()
    
    # Set default neuron parameters if not provided
    if neuron_type == "lif" and not neuron_params:
        neuron_params = {
            "v_rest": 0.0,
            "v_reset": 0.0,
            "v_threshold": 1.0,
            "membrane_resistance": 1.0,
            "membrane_time_constant": 20.0,
            "refractory_period": 5.0
        }
    
    # Each oscillator consists of an excitatory and an inhibitory neuron
    exc_ids = []
    inh_ids = []
    
    for i in range(n_oscillators):
        # Create excitatory neuron
        exc_id = network.add_neuron(neuron_type, **neuron_params)
        exc_ids.append(exc_id)
        
        # Create inhibitory neuron with potentially different parameters
        inh_params = dict(neuron_params)
        inh_params.update({
            "v_threshold": 0.8 * neuron_params.get("v_threshold", 1.0),  # Lower threshold
            "refractory_period": 0.8 * neuron_params.get("refractory_period", 5.0)  # Shorter refractory period
        })
        inh_id = network.add_neuron(neuron_type, **inh_params)
        inh_ids.append(inh_id)
        
        # Connect excitatory to inhibitory neuron
        network.add_synapse(
            pre_id=exc_id,
            post_id=inh_id,
            weight=excitatory_weight,
            delay=1.0,
            stdp_enabled=False
        )
        
        # Connect inhibitory to excitatory neuron (self-inhibition)
        network.add_synapse(
            pre_id=inh_id,
            post_id=exc_id,
            weight=self_inhibition_weight,
            delay=1.0,
            stdp_enabled=False
        )
    
    # Connect oscillators based on connectivity type
    if connectivity_type == "ring":
        for i in range(n_oscillators):
            next_idx = (i + 1) % n_oscillators
            
            # Connect excitatory to next excitatory
            network.add_synapse(
                pre_id=exc_ids[i],
                post_id=exc_ids[next_idx],
                weight=excitatory_weight,
                delay=2.0,
                stdp_enabled=False
            )
            
            # Connect inhibitory to next inhibitory
            network.add_synapse(
                pre_id=inh_ids[i],
                post_id=inh_ids[next_idx],
                weight=inhibitory_weight,
                delay=2.0,
                stdp_enabled=False
            )
    
    elif connectivity_type == "chain":
        for i in range(n_oscillators - 1):
            next_idx = i + 1
            
            # Connect excitatory to next excitatory
            network.add_synapse(
                pre_id=exc_ids[i],
                post_id=exc_ids[next_idx],
                weight=excitatory_weight,
                delay=2.0,
                stdp_enabled=False
            )
            
            # Connect inhibitory to next inhibitory
            network.add_synapse(
                pre_id=inh_ids[i],
                post_id=inh_ids[next_idx],
                weight=inhibitory_weight,
                delay=2.0,
                stdp_enabled=False
            )
    
    elif connectivity_type == "fully_connected":
        for i in range(n_oscillators):
            for j in range(n_oscillators):
                if i != j:
                    # Connect excitatory to all other excitatory
                    network.add_synapse(
                        pre_id=exc_ids[i],
                        post_id=exc_ids[j],
                        weight=excitatory_weight,
                        delay=2.0,
                        stdp_enabled=False
                    )
                    
                    # Connect inhibitory to all other inhibitory
                    network.add_synapse(
                        pre_id=inh_ids[i],
                        post_id=inh_ids[j],
                        weight=inhibitory_weight,
                        delay=2.0,
                        stdp_enabled=False
                    )
    
    return network, exc_ids, inh_ids


def create_associative_memory(
    n_patterns: int = 3,
    pattern_size: int = 100,
    pattern_activity: float = 0.2,
    connection_weight: float = 0.1,
    neuron_type: str = "lif",
    **neuron_params
) -> Tuple[SpikingNetwork, List[np.ndarray]]:
    """
    Create an associative memory network using Hebbian learning.
    
    Args:
        n_patterns: Number of memory patterns
        pattern_size: Number of neurons in the memory
        pattern_activity: Fraction of neurons active in each pattern
        connection_weight: Base weight of connections
        neuron_type: Type of neurons to use
        **neuron_params: Parameters for neurons
        
    Returns:
        Configured SpikingNetwork and the memory patterns
    """
    # Create network
    network = SpikingNetwork()
    
    # Set default neuron parameters if not provided
    if neuron_type == "lif" and not neuron_params:
        neuron_params = {
            "v_rest": 0.0,
            "v_reset": 0.0,
            "v_threshold": 1.0,
            "membrane_resistance": 1.0,
            "membrane_time_constant": 20.0,
            "refractory_period": 3.0
        }
    
    # Create memory neurons
    neuron_ids = network.create_population(pattern_size, neuron_type, **neuron_params)
    
    # Generate random memory patterns
    patterns = []
    for _ in range(n_patterns):
        pattern = np.zeros(pattern_size)
        active_indices = np.random.choice(
            pattern_size,
            size=int(pattern_size * pattern_activity),
            replace=False
        )
        pattern[active_indices] = 1
        patterns.append(pattern)
    
    # Create connections based on Hebbian rule
    weight_matrix = np.zeros((pattern_size, pattern_size))
    
    for pattern in patterns:
        # Outer product of pattern with itself
        weight_update = np.outer(pattern, pattern)
        
        # Set diagonal to zero (no self-connections)
        np.fill_diagonal(weight_update, 0)
        
        # Add to weight matrix
        weight_matrix += weight_update
    
    # Normalize and scale weight matrix
    max_weight = np.max(weight_matrix)
    if max_weight > 0:
        weight_matrix = (weight_matrix / max_weight) * connection_weight
    
    # Create connections based on weight matrix
    for i in range(pattern_size):
        for j in range(pattern_size):
            weight = weight_matrix[i, j]
            if weight > 0:
                network.add_synapse(
                    pre_id=neuron_ids[i],
                    post_id=neuron_ids[j],
                    weight=weight,
                    delay=1.0,
                    stdp_enabled=False
                )
    
    return network, neuron_ids, patterns


def create_liquid_state_machine(
    n_input: int = 10,
    n_reservoir: int = 100,
    n_output: int = 5,
    input_conn_prob: float = 0.2,
    reservoir_conn_prob: float = 0.1,
    output_conn_prob: float = 0.5,
    input_weight: float = 0.2,
    reservoir_weight_exc: float = 0.1,
    reservoir_weight_inh: float = -0.4,
    output_weight: float = 0.1,
    neuron_type: str = "lif",
    **neuron_params
) -> SpikingNetwork:
    """
    Create a Liquid State Machine (LSM) with a recurrent reservoir.
    
    Args:
        n_input: Number of input neurons
        n_reservoir: Number of reservoir neurons
        n_output: Number of output neurons
        input_conn_prob: Connection probability from input to reservoir
        reservoir_conn_prob: Connection probability within reservoir
        output_conn_prob: Connection probability from reservoir to output
        input_weight: Weight of input connections
        reservoir_weight_exc: Weight of excitatory connections within reservoir
        reservoir_weight_inh: Weight of inhibitory connections within reservoir
        output_weight: Weight of connections to output
        neuron_type: Type of neurons to use
        **neuron_params: Parameters for neurons
        
    Returns:
        Configured SpikingNetwork
    """
    # Create network
    network = SpikingNetwork()
    
    # Set default neuron parameters if not provided
    if neuron_type == "lif" and not neuron_params:
        neuron_params = {
            "v_rest": 0.0,
            "v_reset": 0.0,
            "v_threshold": 1.0,
            "membrane_resistance": 1.0,
            "membrane_time_constant": 20.0,
            "refractory_period": 2.0
        }
    
    # Create input, reservoir and output populations
    input_ids = network.create_population(n_input, neuron_type, **neuron_params)
    
    # Reservoir neurons - split into excitatory (80%) and inhibitory (20%)
    n_exc = int(0.8 * n_reservoir)
    n_inh = n_reservoir - n_exc
    
    reservoir_exc_ids = network.create_population(n_exc, neuron_type, **neuron_params)
    
    # Inhibitory neurons might have different parameters
    inh_params = dict(neuron_params)
    inh_params.update({
        "v_threshold": 0.8 * neuron_params.get("v_threshold", 1.0),  # Lower threshold
        "refractory_period": 1.0  # Shorter refractory period
    })
    reservoir_inh_ids = network.create_population(n_inh, neuron_type, **inh_params)
    
    # All reservoir neurons
    reservoir_ids = reservoir_exc_ids + reservoir_inh_ids
    
    # Output neurons
    output_ids = network.create_population(n_output, neuron_type, **neuron_params)
    
    # Connect input to reservoir
    network.connect_random(
        pre_ids=input_ids,
        post_ids=reservoir_ids,
        connection_prob=input_conn_prob,
        weight=input_weight,
        stdp_enabled=False
    )
    
    # Connect within reservoir - excitatory neurons
    network.connect_random(
        pre_ids=reservoir_exc_ids,
        post_ids=reservoir_ids,
        connection_prob=reservoir_conn_prob,
        weight=reservoir_weight_exc,
        stdp_enabled=False
    )
    
    # Connect within reservoir - inhibitory neurons
    network.connect_random(
        pre_ids=reservoir_inh_ids,
        post_ids=reservoir_ids,
        connection_prob=reservoir_conn_prob,
        weight=reservoir_weight_inh,
        stdp_enabled=False
    )
    
    # Connect reservoir to output
    network.connect_random(
        pre_ids=reservoir_ids,
        post_ids=output_ids,
        connection_prob=output_conn_prob,
        weight=output_weight,
        stdp_enabled=False
    )
    
    return network, input_ids, reservoir_ids, output_ids


def create_cortical_microcircuit(
    n_columns: int = 4,
    n_layers: int = 4,
    n_exc_per_layer: int = 20,
    n_inh_per_layer: int = 5,
    p_local: float = 0.3,     # Connection probability within layer
    p_feed_forward: float = 0.2,  # Connection probability to next layer
    p_feedback: float = 0.1,   # Connection probability to previous layer
    p_lateral: float = 0.05,   # Connection probability to same layer in different column
    neuron_type: str = "lif",
    **neuron_params
) -> SpikingNetwork:
    """
    Create a cortical microcircuit model with columns and layers.
    
    Args:
        n_columns: Number of cortical columns
        n_layers: Number of layers per column
        n_exc_per_layer: Number of excitatory neurons per layer
        n_inh_per_layer: Number of inhibitory neurons per layer
        p_local: Connection probability within layer
        p_feed_forward: Connection probability to next layer
        p_feedback: Connection probability to previous layer
        p_lateral: Connection probability to same layer in different column
        neuron_type: Type of neurons to use
        **neuron_params: Parameters for neurons
        
    Returns:
        Configured SpikingNetwork
    """
    # Create network
    network = SpikingNetwork()
    
    # Set default neuron parameters if not provided
    if neuron_type == "lif" and not neuron_params:
        neuron_params = {
            "v_rest": 0.0,
            "v_reset": 0.0,
            "v_threshold": 1.0,
            "membrane_resistance": 1.0,
            "membrane_time_constant": 20.0,
            "refractory_period": 2.0
        }
    
    # Create neurons for each column and layer
    columns = []
    
    for col in range(n_columns):
        column_layers = []
        
        for layer in range(n_layers):
            # Excitatory neurons
            exc_ids = network.create_population(n_exc_per_layer, neuron_type, **neuron_params)
            
            # Inhibitory neurons
            inh_params = dict(neuron_params)
            inh_params.update({
                "v_threshold": 0.8 * neuron_params.get("v_threshold", 1.0),  # Lower threshold
                "refractory_period": 1.5  # Different refractory period
            })
            inh_ids = network.create_population(n_inh_per_layer, neuron_type, **inh_params)
            
            # Store layer
            column_layers.append({
                "exc_ids": exc_ids,
                "inh_ids": inh_ids,
                "all_ids": exc_ids + inh_ids
            })
        
        columns.append(column_layers)
    
    # Connect within each layer
    for col in range(n_columns):
        for layer in range(n_layers):
            exc_ids = columns[col][layer]["exc_ids"]
            inh_ids = columns[col][layer]["inh_ids"]
            all_ids = columns[col][layer]["all_ids"]
            
            # Excitatory to all neurons in layer
            network.connect_random(
                pre_ids=exc_ids,
                post_ids=all_ids,
                connection_prob=p_local,
                weight=0.1,  # Excitatory weight
                stdp_enabled=False
            )
            
            # Inhibitory to all neurons in layer
            network.connect_random(
                pre_ids=inh_ids,
                post_ids=all_ids,
                connection_prob=p_local,
                weight=-0.4,  # Inhibitory weight
                stdp_enabled=False
            )
    
    # Feed-forward connections (layer i to layer i+1 within column)
    for col in range(n_columns):
        for layer in range(n_layers - 1):
            pre_ids = columns[col][layer]["exc_ids"]  # Only excitatory neurons project forward
            post_ids = columns[col][layer + 1]["all_ids"]
            
            network.connect_random(
                pre_ids=pre_ids,
                post_ids=post_ids,
                connection_prob=p_feed_forward,
                weight=0.15,  # Stronger feed-forward weight
                stdp_enabled=False
            )
    
    # Feedback connections (layer i to layer i-1 within column)
    if p_feedback > 0:
        for col in range(n_columns):
            for layer in range(1, n_layers):
                pre_ids = columns[col][layer]["exc_ids"]  # Only excitatory neurons project back
                post_ids = columns[col][layer - 1]["all_ids"]
                
                network.connect_random(
                    pre_ids=pre_ids,
                    post_ids=post_ids,
                    connection_prob=p_feedback,
                    weight=0.05,  # Weaker feedback weight
                    stdp_enabled=False
                )
    
    # Lateral connections (between columns, same layer)
    if p_lateral > 0:
        for layer in range(n_layers):
            for col1 in range(n_columns):
                for col2 in range(n_columns):
                    if col1 != col2:
                        pre_ids = columns[col1][layer]["exc_ids"]  # Only excitatory neurons project laterally
                        post_ids = columns[col2][layer]["all_ids"]
                        
                        network.connect_random(
                            pre_ids=pre_ids,
                            post_ids=post_ids,
                            connection_prob=p_lateral,
                            weight=0.05,  # Weak lateral weight
                            stdp_enabled=False
                        )
    
    # Flatten column/layer structure for return
    all_layers = []
    for col in columns:
        all_layers.extend(col)
    
    return network, all_layers


def create_polychronization_network(
    n_neurons: int = 100,
    n_synapses_per_neuron: int = 20,
    min_delay: float = 1.0,
    max_delay: float = 20.0,
    weight: float = 0.1,
    stdp_enabled: bool = True,
    neuron_type: str = "lif",
    **neuron_params
) -> SpikingNetwork:
    """
    Create a network capable of polychronization (time-locked but not synchronous firing patterns).
    
    Based on Izhikevich's polychronization model, with synaptic delays and STDP.
    
    Args:
        n_neurons: Number of neurons in the network
        n_synapses_per_neuron: Number of outgoing synapses per neuron
        min_delay: Minimum synaptic delay (ms)
        max_delay: Maximum synaptic delay (ms)
        weight: Initial synaptic weight
        stdp_enabled: Whether to enable STDP
        neuron_type: Type of neurons to use
        **neuron_params: Parameters for neurons
        
    Returns:
        Configured SpikingNetwork
    """
    # Create network
    network = SpikingNetwork()
    
    # Set default neuron parameters if not provided
    if neuron_type == "izhikevich" and not neuron_params:
        neuron_params = {
            "a": 0.02,
            "b": 0.2,
            "c": -65.0,
            "d": 8.0,
            "v_threshold": 30.0
        }
    elif neuron_type == "lif" and not neuron_params:
        neuron_params = {
            "v_rest": 0.0,
            "v_reset": 0.0,
            "v_threshold": 1.0,
            "membrane_resistance": 1.0,
            "membrane_time_constant": 20.0,
            "refractory_period": 2.0
        }
    
    # Create neurons (80% excitatory, 20% inhibitory)
    n_exc = int(0.8 * n_neurons)
    n_inh = n_neurons - n_exc
    
    exc_ids = network.create_population(n_exc, neuron_type, **neuron_params)
    
    # Different parameters for inhibitory neurons
    inh_params = dict(neuron_params)
    if neuron_type == "izhikevich":
        inh_params.update({
            "a": 0.1,
            "b": 0.2,
            "c": -65.0,
            "d": 2.0
        })
    else:
        inh_params.update({
            "v_threshold": 0.8 * neuron_params.get("v_threshold", 1.0)
        })
    
    inh_ids = network.create_population(n_inh, neuron_type, **inh_params)
    
    all_ids = exc_ids + inh_ids
    
    # STDP parameters
    stdp_params = {
        "a_plus": 0.01,
        "a_minus": 0.0105,
        "tau_plus": 20.0,
        "tau_minus": 20.0,
        "w_min": 0.0,
        "w_max": 0.5
    }
    
    # Create connections
    for pre_id in all_ids:
        # Select random targets for each neuron
        post_ids = np.random.choice(all_ids, size=n_synapses_per_neuron, replace=False)
        
        for post_id in post_ids:
            # Skip self-connections
            if pre_id == post_id:
                continue
            
            # Determine weight (excitatory or inhibitory)
            conn_weight = weight if pre_id in exc_ids else -weight * 4
            
            # Random delay between min and max
            delay = np.random.uniform(min_delay, max_delay)
            
            # Add synapse
            network.add_synapse(
                pre_id=pre_id,
                post_id=post_id,
                weight=conn_weight,
                delay=delay,
                stdp_enabled=stdp_enabled and pre_id in exc_ids,  # Only excitatory synapses use STDP
                **stdp_params
            )
    
    return network, exc_ids, inh_ids


def initialize_spiking_networks(config=None):
    """
    Initialize the spiking networks module with the given configuration.
    
    Args:
        config: Optional configuration dictionary
        
    Returns:
        Initialization status dictionary
    """
    if config is None:
        config = {}
    
    status = {
        'initialized': True,
        'config': config
    }
    
    # Set up default parameters
    default_dt = config.get('default_dt', DEFAULT_SIMULATION_DT)
    
    # Initialize random number generator if seed is provided
    if 'random_seed' in config:
        np.random.seed(config['random_seed'])
    
    # Add supported neuron and synapse types to status
    status['supported_neuron_types'] = [nt.value for nt in NeuronType]
    status['supported_synapse_types'] = [st.value for st in SynapseType]
    
    # Add Izhikevich neuron presets
    status['izh_neuron_presets'] = list(create_izh_neuron_types().keys())
    
    # Configure backend
    backend = config.get('backend', 'numpy')
    if backend == 'torch' and torch.cuda.is_available():
        status['backend'] = 'torch_cuda'
        status['device'] = torch.device('cuda')
    elif backend == 'torch':
        status['backend'] = 'torch_cpu'
        status['device'] = torch.device('cpu')
    else:
        status['backend'] = 'numpy'
    
    # Log initialization
    logger.info(f"Spiking Neural Networks initialized with backend: {status['backend']}")
    
    return status


# If run directly, show a simple demo
if __name__ == "__main__":
    import matplotlib.pyplot as plt
    
    # Create a simple network
    network = SpikingNetwork(dt=0.1)
    
    # Create a small balanced network
    n_exc = 80
    n_inh = 20
    
    # Excitatory population
    exc_ids = []
    for i in range(n_exc):
        neuron_id = network.add_neuron("lif", v_threshold=1.0, v_rest=0.0, v_reset=0.0)
        exc_ids.append(neuron_id)
    
    # Inhibitory population
    inh_ids = []
    for i in range(n_inh):
        neuron_id = network.add_neuron("lif", v_threshold=1.0, v_rest=0.0, v_reset=0.0)
        inh_ids.append(neuron_id)
    
    # Connect excitatory to all neurons
    for pre_id in exc_ids:
        for post_id in exc_ids + inh_ids:
            if pre_id != post_id and np.random.random() < 0.1:
                network.add_synapse(pre_id=pre_id, post_id=post_id, weight=0.05)
    
    # Connect inhibitory to all neurons
    for pre_id in inh_ids:
        for post_id in exc_ids + inh_ids:
            if pre_id != post_id and np.random.random() < 0.1:
                network.add_synapse(pre_id=pre_id, post_id=post_id, weight=-0.15)
    
    # Add some constant input to a subset of excitatory neurons
    for i in range(10):
        network.add_current_input(exc_ids[i], amplitude=1.2, start_time=0, end_time=500)
    
    # Run simulation
    spike_trains = network.run(duration=500, record_voltages=True)
    
    # Plot results
    network.plot_spikes()
    network.plot_voltages(neuron_ids=exc_ids[:5])
    
    plt.show()