#!/usr/bin/env python3
"""
ULTRA: Neuromorphic Processing Layer - Brain Region Emulation

This module implements artificial analogs to specific brain regions for specialized processing
tasks. These emulated brain regions implement neural circuitry and dynamics inspired by 
neuroscience research on the structure and function of biological brain regions.

Key features:
1. Modular emulation of cortical and subcortical brain regions
2. Biologically-inspired neural circuit implementations
3. Region-specific neural dynamics and processing
4. Inter-region connectivity and communication
5. Specialized computations for perception, memory, decision-making, etc.
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Set, Any, Optional, Union, Callable, Type
from enum import Enum, auto
import logging
from collections import defaultdict, deque
import copy
import time
from dataclasses import dataclass, field
from scipy import signal, ndimage
import networkx as nx

# Import from other ULTRA modules
try:
    from ultra.spiking_networks import SpikingNeuron, SpikingNetwork, LIFNeuron
    from ultra.event_based_computing import Event, EventProcessor, EventType, SpikeEvent
    from ultra.memristor_array import MemristorArray
    from ultra.reservoir_computing import ReservoirNetwork
except ImportError:
    # For standalone testing
    class SpikingNeuron:
        def __init__(self, *args, **kwargs): pass
    class SpikingNetwork:
        def __init__(self, *args, **kwargs): pass
    class LIFNeuron:
        def __init__(self, *args, **kwargs): pass
    class Event:
        def __init__(self, *args, **kwargs): pass
    class EventProcessor:
        def __init__(self, *args, **kwargs): pass
    class EventType(Enum):
        SPIKE = auto()
    class SpikeEvent(Event):
        def __init__(self, *args, **kwargs): pass
    class MemristorArray:
        def __init__(self, *args, **kwargs): pass
    class ReservoirNetwork:
        def __init__(self, *args, **kwargs): pass

logger = logging.getLogger(__name__)


class BrainRegionType(Enum):
    """Types of brain regions that can be emulated."""
    VISUAL_CORTEX = auto()
    AUDITORY_CORTEX = auto()
    SOMATOSENSORY_CORTEX = auto()
    MOTOR_CORTEX = auto()
    PREFRONTAL_CORTEX = auto()
    PARIETAL_CORTEX = auto()
    TEMPORAL_CORTEX = auto()
    OCCIPITAL_CORTEX = auto()
    HIPPOCAMPUS = auto()
    BASAL_GANGLIA = auto()
    THALAMUS = auto()
    CEREBELLUM = auto()
    AMYGDALA = auto()
    BRAINSTEM = auto()
    HYPOTHALAMUS = auto()
    SPINAL_CORD = auto()
    ENTORHINAL_CORTEX = auto()
    CUSTOM = auto()


class NeurotransmitterType(Enum):
    """Types of neurotransmitters used in brain region modeling."""
    GLUTAMATE = auto()   # Excitatory
    GABA = auto()        # Inhibitory
    DOPAMINE = auto()    # Reward, motivation
    SEROTONIN = auto()   # Mood, anxiety
    NOREPINEPHRINE = auto()  # Arousal, attention
    ACETYLCHOLINE = auto()   # Learning, memory
    ENDORPHIN = auto()       # Pain reduction
    GLYCINE = auto()         # Inhibitory
    HISTAMINE = auto()       # Wakefulness
    SUBSTANCE_P = auto()     # Pain
    MELATONIN = auto()       # Sleep


@dataclass
class Neurotransmitter:
    """Model of a neurotransmitter and its dynamics."""
    type: NeurotransmitterType
    baseline_level: float = 0.5
    current_level: float = 0.5
    decay_rate: float = 0.01  # Rate at which level returns to baseline
    diffusion_rate: float = 0.05  # Rate at which it spreads to neighboring areas
    receptors: Dict[str, float] = field(default_factory=dict)  # Receptor types and sensitivities
    
    def release(self, amount: float) -> float:
        """
        Release neurotransmitter.
        
        Args:
            amount: Amount to release
            
        Returns:
            New current level
        """
        self.current_level += amount
        # Cap at maximum level 1.0
        self.current_level = min(1.0, self.current_level)
        return self.current_level
    
    def update(self, dt: float = 1.0) -> float:
        """
        Update neurotransmitter level over time.
        
        Args:
            dt: Time step
            
        Returns:
            New current level
        """
        # Decay toward baseline
        diff = self.baseline_level - self.current_level
        self.current_level += self.decay_rate * diff * dt
        return self.current_level


class NeurotransmitterSystem:
    """
    System for managing neurotransmitters in brain regions.
    
    This class tracks neurotransmitter levels across regions and their effects
    on neural activity, implementing a simplified model of neuromodulation.
    """
    
    def __init__(self):
        """Initialize neurotransmitter system."""
        self.neurotransmitters = {}  # region_id -> neurotransmitter_type -> Neurotransmitter
        self.projections = {}  # (source_region, target_region) -> (neurotransmitter_type, strength)
        self.global_modulation = {}  # neurotransmitter_type -> global_level
        
        # Initialize global modulation to neutral levels
        for nt_type in NeurotransmitterType:
            self.global_modulation[nt_type] = 0.5
    
    def add_neurotransmitter(self, region_id: int, nt_type: NeurotransmitterType, 
                           baseline: float = 0.5, decay_rate: float = 0.01,
                           diffusion_rate: float = 0.05) -> None:
        """
        Add a neurotransmitter to a region.
        
        Args:
            region_id: ID of the brain region
            nt_type: Neurotransmitter type
            baseline: Baseline level
            decay_rate: Rate of decay toward baseline
            diffusion_rate: Rate of diffusion to connected regions
        """
        if region_id not in self.neurotransmitters:
            self.neurotransmitters[region_id] = {}
            
        self.neurotransmitters[region_id][nt_type] = Neurotransmitter(
            type=nt_type,
            baseline_level=baseline,
            current_level=baseline,
            decay_rate=decay_rate,
            diffusion_rate=diffusion_rate
        )
    
    def add_projection(self, source_id: int, target_id: int, 
                     nt_type: NeurotransmitterType, strength: float = 0.1) -> None:
        """
        Add a neuromodulatory projection between regions.
        
        Args:
            source_id: Source region ID
            target_id: Target region ID
            nt_type: Neurotransmitter type
            strength: Projection strength
        """
        self.projections[(source_id, target_id)] = (nt_type, strength)
    
    def release(self, region_id: int, nt_type: NeurotransmitterType, amount: float) -> None:
        """
        Release neurotransmitter in a region.
        
        Args:
            region_id: Region ID
            nt_type: Neurotransmitter type
            amount: Amount to release
        """
        if region_id in self.neurotransmitters and nt_type in self.neurotransmitters[region_id]:
            self.neurotransmitters[region_id][nt_type].release(amount)
    
    def get_level(self, region_id: int, nt_type: NeurotransmitterType) -> float:
        """
        Get current neurotransmitter level in a region.
        
        Args:
            region_id: Region ID
            nt_type: Neurotransmitter type
            
        Returns:
            Current level (0.0-1.0) or 0.0 if not present
        """
        if region_id in self.neurotransmitters and nt_type in self.neurotransmitters[region_id]:
            return self.neurotransmitters[region_id][nt_type].current_level
        return 0.0
    
    def update(self, dt: float = 1.0) -> None:
        """
        Update all neurotransmitter levels.
        
        Args:
            dt: Time step
        """
        # First update individual region levels
        for region_id, nt_dict in self.neurotransmitters.items():
            for nt_type, nt in nt_dict.items():
                nt.update(dt)
        
        # Process projections
        projection_updates = defaultdict(float)
        for (source_id, target_id), (nt_type, strength) in self.projections.items():
            # Check if source has this neurotransmitter
            if source_id in self.neurotransmitters and nt_type in self.neurotransmitters[source_id]:
                source_level = self.neurotransmitters[source_id][nt_type].current_level
                
                # Calculate amount to project to target
                diff_amount = source_level * strength * dt
                
                # Create or update target neurotransmitter
                if target_id not in self.neurotransmitters:
                    self.neurotransmitters[target_id] = {}
                    
                if nt_type not in self.neurotransmitters[target_id]:
                    self.neurotransmitters[target_id][nt_type] = Neurotransmitter(
                        type=nt_type,
                        baseline_level=0.1,  # Lower baseline for projected neurotransmitters
                        current_level=0.1
                    )
                
                # Store update (apply all at once to avoid order effects)
                projection_updates[(target_id, nt_type)] += diff_amount
                
                # Reduce source level slightly
                self.neurotransmitters[source_id][nt_type].current_level -= diff_amount * 0.1
                
        # Apply all projection updates
        for (target_id, nt_type), amount in projection_updates.items():
            self.neurotransmitters[target_id][nt_type].release(amount)
    
    def set_global_modulation(self, nt_type: NeurotransmitterType, level: float) -> None:
        """
        Set global modulation level for a neurotransmitter.
        
        Args:
            nt_type: Neurotransmitter type
            level: Modulation level (0.0-1.0)
        """
        self.global_modulation[nt_type] = max(0.0, min(1.0, level))
    
    def get_modulation_factor(self, region_id: int, nt_type: NeurotransmitterType) -> float:
        """
        Get modulation factor combining local and global effects.
        
        Args:
            region_id: Region ID
            nt_type: Neurotransmitter type
            
        Returns:
            Combined modulation factor
        """
        local_level = self.get_level(region_id, nt_type)
        global_level = self.global_modulation.get(nt_type, 0.5)
        
        # Combine local and global effects
        # 0.5 is neutral, below 0.5 is inhibition, above 0.5 is excitation
        local_factor = 2.0 * local_level - 0.5  # Convert to [-0.5, 1.5] range
        global_factor = 2.0 * global_level - 0.5
        
        # Combined factor (multiplicative to allow complete inhibition)
        return max(0.0, local_factor * global_factor + 0.5)  # Rescale to positive range


class SynapticPlasticityRule(Enum):
    """Types of synaptic plasticity rules for brain regions."""
    STDP = auto()                # Spike-Timing-Dependent Plasticity
    HEBBIAN = auto()             # Hebbian plasticity (fire together, wire together)
    TSTDP = auto()               # Triplet STDP
    BCM = auto()                 # Bienenstock-Cooper-Munro rule
    HOMEOSTATIC = auto()         # Homeostatic plasticity
    SHORT_TERM = auto()          # Short-term plasticity (facilitation/depression)
    REWARD_MODULATED = auto()    # Dopamine-modulated plasticity
    L2_REGULARIZATION = auto()   # Weight decay toward zero
    STRUCTURAL = auto()          # Creation/elimination of synapses


class BrainRegionConnectivity:
    """
    Manages connectivity between brain regions.
    
    This class handles the connections between different brain regions,
    implementing various connectivity patterns and managing signal flow.
    """
    
    def __init__(self):
        """Initialize brain region connectivity."""
        self.connections = {}  # (source_id, target_id) -> connection_params
        self.connectivity_matrix = {}  # Maps region IDs to connectivity matrices
        self.regions = set()  # Set of all region IDs
        self.connection_strengths = {}  # (source_id, target_id) -> strength
        self.connection_types = {}  # (source_id, target_id) -> connection type
        self.delays = {}  # (source_id, target_id) -> delay
        
        # For visualizing and analyzing the connectivity graph
        self.graph = nx.DiGraph()
    
    def add_region(self, region_id: int, name: str = None) -> None:
        """
        Add a region to the connectivity framework.
        
        Args:
            region_id: Region ID
            name: Optional name for the region
        """
        self.regions.add(region_id)
        attrs = {"name": name or f"Region {region_id}"}
        self.graph.add_node(region_id, **attrs)
    
    def add_connection(self, source_id: int, target_id: int, 
                     connection_type: str = "feedforward", 
                     strength: float = 1.0,
                     delay: float = 1.0,
                     reciprocal: bool = False) -> None:
        """
        Add a connection between regions.
        
        Args:
            source_id: Source region ID
            target_id: Target region ID
            connection_type: Type of connection
            strength: Connection strength
            delay: Connection delay (ms)
            reciprocal: If True, add connection in both directions
        """
        self.regions.add(source_id)
        self.regions.add(target_id)
        
        self.connections[(source_id, target_id)] = {
            "type": connection_type,
            "strength": strength,
            "delay": delay
        }
        
        self.connection_strengths[(source_id, target_id)] = strength
        self.connection_types[(source_id, target_id)] = connection_type
        self.delays[(source_id, target_id)] = delay
        
        # Add to graph
        self.graph.add_edge(source_id, target_id, 
                          type=connection_type, 
                          strength=strength,
                          delay=delay)
        
        # Add reciprocal connection if requested
        if reciprocal:
            recip_strength = strength * 0.8  # Slightly weaker feedback connection
            
            self.connections[(target_id, source_id)] = {
                "type": "feedback" if connection_type == "feedforward" else "lateral",
                "strength": recip_strength,
                "delay": delay * 1.5  # Slightly longer delay for feedback
            }
            
            self.connection_strengths[(target_id, source_id)] = recip_strength
            self.connection_types[(target_id, source_id)] = "feedback" if connection_type == "feedforward" else "lateral"
            self.delays[(target_id, source_id)] = delay * 1.5
            
            # Add to graph
            self.graph.add_edge(target_id, source_id, 
                              type="feedback" if connection_type == "feedforward" else "lateral", 
                              strength=recip_strength,
                              delay=delay * 1.5)
    
    def update_strength(self, source_id: int, target_id: int, new_strength: float) -> None:
        """
        Update connection strength.
        
        Args:
            source_id: Source region ID
            target_id: Target region ID
            new_strength: New connection strength
        """
        if (source_id, target_id) in self.connections:
            self.connections[(source_id, target_id)]["strength"] = new_strength
            self.connection_strengths[(source_id, target_id)] = new_strength
            
            # Update graph
            self.graph[source_id][target_id]["strength"] = new_strength
    
    def get_connection_params(self, source_id: int, target_id: int) -> Optional[Dict]:
        """
        Get connection parameters.
        
        Args:
            source_id: Source region ID
            target_id: Target region ID
            
        Returns:
            Connection parameters or None if not connected
        """
        return self.connections.get((source_id, target_id))
    
    def get_target_regions(self, source_id: int) -> List[int]:
        """
        Get all regions that receive input from source region.
        
        Args:
            source_id: Source region ID
            
        Returns:
            List of target region IDs
        """
        return [target for (src, target) in self.connections.keys() if src == source_id]
    
    def get_source_regions(self, target_id: int) -> List[int]:
        """
        Get all regions that send input to target region.
        
        Args:
            target_id: Target region ID
            
        Returns:
            List of source region IDs
        """
        return [src for (src, tgt) in self.connections.keys() if tgt == target_id]
    
    def propagate_activity(self, region_activities: Dict[int, float]) -> Dict[int, float]:
        """
        Propagate activity between regions based on connectivity.
        
        Args:
            region_activities: Dictionary mapping region IDs to activity levels
            
        Returns:
            Dictionary of updated activity levels
        """
        new_activities = region_activities.copy()
        
        # For each connection, propagate activity
        for (source, target), params in self.connections.items():
            if source in region_activities:
                # Get source activity and parameters
                src_activity = region_activities[source]
                strength = params["strength"]
                
                # Apply connection
                if target not in new_activities:
                    new_activities[target] = 0.0
                    
                # Add weighted input to target
                new_activities[target] += src_activity * strength
        
        # Cap activities to [0, 1] range
        for region_id in new_activities:
            new_activities[region_id] = max(0.0, min(1.0, new_activities[region_id]))
        
        return new_activities
    
    def compute_connectivity_matrix(self) -> np.ndarray:
        """
        Compute connectivity matrix for all regions.
        
        Returns:
            Connectivity matrix as numpy array
        """
        # Convert region IDs to indices
        region_list = sorted(list(self.regions))
        n_regions = len(region_list)
        region_to_idx = {region_id: i for i, region_id in enumerate(region_list)}
        
        # Create connectivity matrix
        matrix = np.zeros((n_regions, n_regions))
        
        for (source, target), params in self.connections.items():
            if source in region_to_idx and target in region_to_idx:
                src_idx = region_to_idx[source]
                tgt_idx = region_to_idx[target]
                matrix[tgt_idx, src_idx] = params["strength"]
        
        return matrix
    
    def visualize_connectivity(self, figsize=(10, 8), node_size=1000, 
                             font_size=10, show_weights=True) -> None:
        """
        Visualize brain region connectivity as a graph.
        
        Args:
            figsize: Figure size
            node_size: Size of nodes in visualization
            font_size: Font size for labels
            show_weights: Show connection weights
        """
        plt.figure(figsize=figsize)
        
        # Set positions using spring layout
        pos = nx.spring_layout(self.graph, seed=42)
        
        # Get connection types for edge colors
        edge_colors = []
        for _, _, data in self.graph.edges(data=True):
            if data["type"] == "feedforward":
                edge_colors.append("blue")
            elif data["type"] == "feedback":
                edge_colors.append("red")
            elif data["type"] == "lateral":
                edge_colors.append("green")
            else:
                edge_colors.append("gray")
        
        # Get connection strengths for edge widths
        edge_widths = [data["strength"] * 2 for _, _, data in self.graph.edges(data=True)]
        
        # Draw the graph
        nx.draw(self.graph, pos, with_labels=True, 
              node_color="lightblue", node_size=node_size,
              font_size=font_size, edge_color=edge_colors,
              width=edge_widths, arrows=True)
        
        # Add edge labels for weights if requested
        if show_weights:
            edge_labels = {(source, target): f"{data['strength']:.2f}" 
                         for source, target, data in self.graph.edges(data=True)}
            nx.draw_networkx_edge_labels(self.graph, pos, edge_labels=edge_labels, font_size=8)
        
        plt.title("Brain Region Connectivity")
        plt.tight_layout()
        plt.show()
    
    def compute_graph_metrics(self) -> Dict[str, Any]:
        """
        Compute graph theory metrics for connectivity.
        
        Returns:
            Dictionary of graph metrics
        """
        metrics = {}
        
        # Basic graph metrics
        metrics["n_regions"] = len(self.regions)
        metrics["n_connections"] = len(self.connections)
        metrics["density"] = nx.density(self.graph)
        
        # Compute degree statistics
        in_degrees = [deg for _, deg in self.graph.in_degree()]
        out_degrees = [deg for _, deg in self.graph.out_degree()]
        
        metrics["avg_in_degree"] = np.mean(in_degrees) if in_degrees else 0
        metrics["avg_out_degree"] = np.mean(out_degrees) if out_degrees else 0
        metrics["max_in_degree"] = max(in_degrees) if in_degrees else 0
        metrics["max_out_degree"] = max(out_degrees) if out_degrees else 0
        
        # Compute hubs (high degree nodes)
        total_degrees = dict(self.graph.degree())
        metrics["hubs"] = sorted(total_degrees.items(), key=lambda x: x[1], reverse=True)[:3]
        
        # Compute centrality measures
        try:
            metrics["betweenness_centrality"] = nx.betweenness_centrality(self.graph)
            metrics["eigenvector_centrality"] = nx.eigenvector_centrality(self.graph, max_iter=1000)
        except:
            metrics["centrality_error"] = "Could not compute centrality measures"
        
        # Compute clustering coefficient
        try:
            metrics["clustering_coefficient"] = nx.average_clustering(self.graph.to_undirected())
        except:
            metrics["clustering_error"] = "Could not compute clustering coefficient"
        
        # Check for strongly connected components
        metrics["strongly_connected"] = nx.is_strongly_connected(self.graph)
        metrics["n_strong_components"] = nx.number_strongly_connected_components(self.graph)
        
        # Compute path lengths
        if metrics["n_regions"] > 1:
            try:
                # Convert to undirected for average shortest path
                undirected = self.graph.to_undirected()
                if nx.is_connected(undirected):
                    metrics["avg_shortest_path"] = nx.average_shortest_path_length(undirected)
                    metrics["diameter"] = nx.diameter(undirected)
                else:
                    metrics["path_error"] = "Graph is not connected"
            except:
                metrics["path_error"] = "Could not compute path metrics"
        
        return metrics


class BrainRegion(EventProcessor):
    """
    Base class for emulated brain regions.
    
    This class provides the foundation for emulating specific brain regions,
    implementing common functionality and interfaces that are specialized
    in derived classes for specific brain areas.
    """
    
    def __init__(
        self,
        region_id: int,
        region_type: BrainRegionType,
        n_neurons: int = 100,
        neuron_params: Optional[Dict[str, Any]] = None,
        topology: str = "random",
        plasticity_rules: Optional[List[SynapticPlasticityRule]] = None
    ):
        """
        Initialize brain region.
        
        Args:
            region_id: Unique identifier for this region
            region_type: Type of brain region
            n_neurons: Number of neurons in this region
            neuron_params: Parameters for neurons
            topology: Neural connectivity topology
            plasticity_rules: List of plasticity rules to apply
        """
        super().__init__(processor_id=region_id)
        self.region_type = region_type
        self.n_neurons = n_neurons
        self.neuron_params = neuron_params or {}
        self.topology = topology
        
        self.plasticity_rules = plasticity_rules or [SynapticPlasticityRule.STDP]
        
        # Neural network
        self.neurons = []
        self.synaptic_weights = None
        self.internal_state = np.zeros(n_neurons)
        self.membrane_potentials = np.zeros(n_neurons)
        self.firing_rates = np.zeros(n_neurons)
        self.spike_history = []  # List of (neuron_idx, time) tuples
        
        # Region-specific state
        self.activity_level = 0.0
        self.fatigue_level = 0.0
        self.lateral_inhibition = 0.2  # Strength of lateral inhibition
        
        # Initialize neural network
        self._initialize_neurons()
        self._initialize_connectivity()
        
        # Event processing state
        self.last_update_time = 0.0
        self.inputs_buffer = {}  # source_id -> [input_values]
        self.outputs_buffer = []  # [(target_id, value)]
        
        # Neurotransmitter levels
        self.neurotransmitters = {}  # neurotransmitter_type -> level
        
        # Specialized features for this region
        self.features = {}
        
        logger.info(f"Initialized {region_type.name} brain region with {n_neurons} neurons")
    
    def _initialize_neurons(self) -> None:
        """Initialize neurons in this region."""
        self.neurons = []
        
        for i in range(self.n_neurons):
            # Create neuron with slight parameter variations
            params = self.neuron_params.copy()
            
            # Add 5% random variation to parameters for heterogeneity
            for key in params:
                if isinstance(params[key], (int, float)):
                    params[key] *= np.random.uniform(0.95, 1.05)
            
            # Create LIF neuron by default
            neuron = LIFNeuron(
                tau_m=params.get('tau_m', 20.0),
                v_rest=params.get('v_rest', -70.0),
                v_threshold=params.get('v_threshold', -50.0),
                v_reset=params.get('v_reset', -65.0),
                refractory_period=params.get('refractory_period', 2.0)
            )
            
            self.neurons.append(neuron)
            
    def _initialize_connectivity(self) -> None:
        """Initialize connectivity between neurons."""
        # Create weight matrix
        self.synaptic_weights = np.zeros((self.n_neurons, self.n_neurons))
        
        if self.topology == "random":
            # Random connectivity with 10% connection probability
            connection_prob = 0.1
            for i in range(self.n_neurons):
                for j in range(self.n_neurons):
                    if i != j and np.random.random() < connection_prob:
                        # 80% excitatory, 20% inhibitory
                        if np.random.random() < 0.8:
                            self.synaptic_weights[i, j] = np.random.uniform(0, 0.2)
                        else:
                            self.synaptic_weights[i, j] = np.random.uniform(-0.5, -0.1)
        
        elif self.topology == "small-world":
            # Small-world connectivity (based on Watts-Strogatz model)
            # Start with a ring lattice
            k = 4  # Each neuron connects to k nearest neighbors
            for i in range(self.n_neurons):
                for j in range(1, k // 2 + 1):
                    # Connect to neighbors (circular boundary)
                    self.synaptic_weights[i, (i + j) % self.n_neurons] = np.random.uniform(0, 0.2)
                    self.synaptic_weights[i, (i - j) % self.n_neurons] = np.random.uniform(0, 0.2)
            
            # Rewire connections with probability beta
            beta = 0.1
            for i in range(self.n_neurons):
                for j in range(1, k // 2 + 1):
                    # Consider rewiring each edge
                    if np.random.random() < beta:
                        # Remove existing connection
                        target = (i + j) % self.n_neurons
                        self.synaptic_weights[i, target] = 0
                        
                        # Add new random connection
                        new_target = np.random.randint(0, self.n_neurons)
                        while new_target == i or self.synaptic_weights[i, new_target] != 0:
                            new_target = np.random.randint(0, self.n_neurons)
                        
                        self.synaptic_weights[i, new_target] = np.random.uniform(0, 0.2)
                        
            # Add inhibitory neurons (20% of total)
            n_inhibitory = int(0.2 * self.n_neurons)
            inhibitory_idx = np.random.choice(self.n_neurons, n_inhibitory, replace=False)
            
            for i in inhibitory_idx:
                # Convert all outgoing connections to inhibitory
                for j in range(self.n_neurons):
                    if self.synaptic_weights[i, j] > 0:
                        self.synaptic_weights[i, j] = -abs(self.synaptic_weights[i, j]) * 2
                        
        elif self.topology == "layered":
            # Layered connectivity (e.g., for cortical layers)
            n_layers = 4
            neurons_per_layer = self.n_neurons // n_layers
            
            for layer in range(n_layers):
                layer_start = layer * neurons_per_layer
                layer_end = (layer + 1) * neurons_per_layer
                
                # Within-layer connectivity (lateral)
                for i in range(layer_start, layer_end):
                    for j in range(layer_start, layer_end):
                        if i != j and np.random.random() < 0.1:
                            if np.random.random() < 0.8:  # 80% excitatory
                                self.synaptic_weights[i, j] = np.random.uniform(0, 0.15)
                            else:  # 20% inhibitory
                                self.synaptic_weights[i, j] = np.random.uniform(-0.4, -0.1)
                
                # Between-layer connectivity (feedforward)
                if layer < n_layers - 1:
                    next_layer_start = (layer + 1) * neurons_per_layer
                    next_layer_end = (layer + 2) * neurons_per_layer
                    
                    for i in range(layer_start, layer_end):
                        for j in range(next_layer_start, next_layer_end):
                            if np.random.random() < 0.2:  # 20% connection probability
                                self.synaptic_weights[i, j] = np.random.uniform(0, 0.25)
        
        else:
            # Default to random connectivity
            connection_prob = 0.1
            for i in range(self.n_neurons):
                for j in range(self.n_neurons):
                    if i != j and np.random.random() < connection_prob:
                        # 80% excitatory, 20% inhibitory
                        if np.random.random() < 0.8:
                            self.synaptic_weights[i, j] = np.random.uniform(0, 0.2)
                        else:
                            self.synaptic_weights[i, j] = np.random.uniform(-0.5, -0.1)
    
    def process_event(self, event: Event) -> List[Event]:
        """
        Process incoming event.
        
        Args:
            event: Input event
            
        Returns:
            List of output events
        """
        # Update internal state based on event
        if event.event_type == EventType.SPIKE:
            return self._process_spike_event(event)
        else:
            # Default handling for other event types
            return []
    
    def _process_spike_event(self, event: SpikeEvent) -> List[Event]:
        """
        Process spike event.
        
        Args:
            event: Spike event
            
        Returns:
            List of output events
        """
        current_time = event.timestamp
        source_id = event.source_id
        
        # Update time
        dt = current_time - self.last_update_time if self.last_update_time > 0 else 0
        if dt > 0:
            self._update_state(dt)
        
        self.last_update_time = current_time
        
        # Process input
        if hasattr(event, 'payload') and event.payload:
            # Extract input value from payload
            if 'value' in event.payload:
                input_value = event.payload['value']
            elif 'weight' in event.payload:
                input_value = event.payload['weight']
            else:
                input_value = 1.0  # Default value
                
            # Store input
            if source_id not in self.inputs_buffer:
                self.inputs_buffer[source_id] = []
            
            self.inputs_buffer[source_id].append((current_time, input_value))
            
            # Process inputs that have accumulated
            self._process_inputs(current_time)
        
        # Generate output events
        output_events = []
        
        # Process any spikes that occurred
        for i, neuron in enumerate(self.neurons):
            if self.membrane_potentials[i] >= neuron.v_threshold:
                # Record spike
                self.spike_history.append((i, current_time))
                
                # Reset membrane potential
                self.membrane_potentials[i] = neuron.v_reset
                
                # Generate output spike event
                spike_event = SpikeEvent(
                    timestamp=current_time,
                    neuron_id=self.processor_id,
                    payload={
                        'neuron_idx': i,
                        'value': 1.0
                    }
                )
                
                output_events.append(spike_event)
                
                # Update firing rate (simple exponential moving average)
                tau_rate = 50.0  # Time constant for rate estimate (ms)
                self.firing_rates[i] = self.firing_rates[i] * np.exp(-dt / tau_rate) + 1.0
        
        return output_events
    
    def _update_state(self, dt: float) -> None:
        """
        Update internal state over time.
        
        Args:
            dt: Time step
        """
        # Update neuron dynamics
        for i, neuron in enumerate(self.neurons):
            # Skip if in refractory period
            is_refractory = False
            for neuron_idx, spike_time in reversed(self.spike_history):
                if neuron_idx == i and (self.last_update_time - spike_time) < neuron.refractory_period:
                    is_refractory = True
                    break
            
            if is_refractory:
                continue
            
            # Compute synaptic input from other neurons in this region
            synaptic_input = 0.0
            for j in range(self.n_neurons):
                if self.synaptic_weights[j, i] != 0:
                    # Check if neuron j has spiked recently
                    has_spiked = False
                    for neuron_idx, spike_time in reversed(self.spike_history):
                        if neuron_idx == j and (self.last_update_time - spike_time) < 5.0:  # Consider spikes in last 5ms
                            has_spiked = True
                            break
                    
                    if has_spiked:
                        synaptic_input += self.synaptic_weights[j, i]
            
            # Apply lateral inhibition
            if self.lateral_inhibition > 0:
                inhibition = 0.0
                for j in range(self.n_neurons):
                    if i != j and self.firing_rates[j] > 0:
                        inhibition += self.firing_rates[j] * self.lateral_inhibition
                
                synaptic_input -= inhibition
            
            # Update membrane potential using LIF dynamics
            tau_m = neuron.tau_m
            dv = (-self.membrane_potentials[i] + neuron.v_rest + synaptic_input) / tau_m
            self.membrane_potentials[i] += dv * dt
        
        # Apply fatigue (adaptation)
        self.fatigue_level += 0.01 * self.activity_level * dt
        self.fatigue_level -= 0.001 * dt  # Recovery
        self.fatigue_level = max(0.0, min(1.0, self.fatigue_level))
        
        # Adjust neuron thresholds based on fatigue
        for i, neuron in enumerate(self.neurons):
            neuron.v_threshold = self.neuron_params.get('v_threshold', -50.0) * (1.0 + 0.2 * self.fatigue_level)
        
        # Apply plasticity rules
        if SynapticPlasticityRule.STDP in self.plasticity_rules:
            self._apply_stdp()
        
        if SynapticPlasticityRule.HOMEOSTATIC in self.plasticity_rules:
            self._apply_homeostatic_plasticity()
    
    def _process_inputs(self, current_time: float) -> None:
        """
        Process accumulated inputs.
        
        Args:
            current_time: Current simulation time
        """
        # Process each input source
        for source_id, inputs in self.inputs_buffer.items():
            # Only process inputs that are ready (based on connection delay)
            ready_inputs = [(t, val) for t, val in inputs if t <= current_time]
            
            if ready_inputs:
                # Remove processed inputs
                self.inputs_buffer[source_id] = [inp for inp in inputs if inp not in ready_inputs]
                
                # Compute total input from this source
                total_input = sum(val for _, val in ready_inputs)
                
                # Distribute input to neurons (could be more sophisticated)
                # Here we just distribute to random subset of neurons
                target_neurons = np.random.choice(
                    self.n_neurons, 
                    size=int(self.n_neurons * 0.1),  # Connect to 10% of neurons
                    replace=False
                )
                
                for i in target_neurons:
                    # Add to membrane potential
                    self.membrane_potentials[i] += total_input
    
    def _apply_stdp(self) -> None:
        """Apply spike-timing dependent plasticity to synaptic weights."""
        # Skip if no recent spikes
        if len(self.spike_history) < 2:
            return
        
        # STDP parameters
        a_plus = 0.01   # Magnitude of potentiation
        a_minus = 0.0115  # Magnitude of depression (slightly larger for stability)
        tau_plus = 20.0   # Time constant for potentiation (ms)
        tau_minus = 20.0  # Time constant for depression (ms)
        
        # Consider recent spike pairs
        # This is a simplified implementation of STDP
        for i in range(self.n_neurons):
            for j in range(self.n_neurons):
                if i == j or self.synaptic_weights[i, j] == 0:
                    continue
                
                # Find latest spikes for neurons i and j
                last_i_spike = None
                last_j_spike = None
                
                for neuron_idx, spike_time in reversed(self.spike_history):
                    if neuron_idx == i and last_i_spike is None:
                        last_i_spike = spike_time
                    if neuron_idx == j and last_j_spike is None:
                        last_j_spike = spike_time
                    
                    if last_i_spike is not None and last_j_spike is not None:
                        break
                
                if last_i_spike is None or last_j_spike is None:
                    continue
                
                # Compute time difference
                dt = last_j_spike - last_i_spike  # post - pre
                
                # Apply STDP rule
                if dt > 0:  # Potentiation: pre before post
                    dw = a_plus * np.exp(-dt / tau_plus)
                else:  # Depression: post before pre
                    dw = -a_minus * np.exp(dt / tau_minus)
                
                # Update weight
                old_weight = self.synaptic_weights[i, j]
                
                # Apply weight change
                self.synaptic_weights[i, j] += dw
                
                # Apply weight bounds
                # Excitatory synapses stay positive, inhibitory stay negative
                if old_weight > 0:
                    self.synaptic_weights[i, j] = max(0.0, min(1.0, self.synaptic_weights[i, j]))
                else:
                    self.synaptic_weights[i, j] = max(-1.0, min(0.0, self.synaptic_weights[i, j]))
    
    def _apply_homeostatic_plasticity(self) -> None:
        """Apply homeostatic plasticity to maintain target firing rates."""
        # Target firing rate
        target_rate = 0.2  # Target rate in spikes per timestep
        
        # Learning rate for homeostatic plasticity
        eta = 0.001
        
        for i in range(self.n_neurons):
            # Compute rate difference
            rate_diff = target_rate - self.firing_rates[i]
            
            # Scale all incoming weights
            for j in range(self.n_neurons):
                if self.synaptic_weights[j, i] != 0:
                    # Excitatory synapses
                    if self.synaptic_weights[j, i] > 0:
                        self.synaptic_weights[j, i] *= (1.0 + eta * rate_diff)
                        # Apply bounds
                        self.synaptic_weights[j, i] = max(0.0, min(1.0, self.synaptic_weights[j, i]))
    
    def stimulate(self, neuron_indices: List[int], strength: float) -> None:
        """
        Directly stimulate specific neurons.
        
        Args:
            neuron_indices: Indices of neurons to stimulate
            strength: Stimulation strength
        """
        for idx in neuron_indices:
            if 0 <= idx < self.n_neurons:
                self.membrane_potentials[idx] += strength
    
    def get_activity_level(self) -> float:
        """
        Get overall activity level of the region.
        
        Returns:
            Activity level (0.0-1.0)
        """
        # Compute using firing rates
        return np.mean(self.firing_rates) / 0.5  # Normalize assuming 0.5 is max sustainable rate
    
    def reset(self) -> None:
        """Reset region state."""
        super().reset()
        self.membrane_potentials = np.zeros(self.n_neurons)
        self.firing_rates = np.zeros(self.n_neurons)
        self.spike_history = []
        self.activity_level = 0.0
        self.fatigue_level = 0.0
        self.last_update_time = 0.0
        self.inputs_buffer = {}
        self.outputs_buffer = []
    
    def visualize_activity(self, window_size: float = 100.0, figsize=(10, 6)) -> None:
        """
        Visualize neural activity in this region.
        
        Args:
            window_size: Time window to visualize (ms)
            figsize: Figure size
        """
        if not self.spike_history:
            print("No spike history to visualize")
            return
        
        # Extract spike times and neuron indices
        neuron_indices, spike_times = zip(*self.spike_history)
        
        # Create raster plot
        plt.figure(figsize=figsize)
        plt.scatter(spike_times, neuron_indices, marker='|', s=10, color='black')
        
        # Set plot limits
        latest_time = max(spike_times)
        plt.xlim(max(0, latest_time - window_size), latest_time)
        plt.ylim(-1, self.n_neurons)
        
        # Add labels
        plt.xlabel('Time (ms)')
        plt.ylabel('Neuron Index')
        plt.title(f'{self.region_type.name} Activity')
        
        # Add firing rate subplot
        ax2 = plt.twinx()
        # Compute average firing rate over time using binned histogram
        bins = np.linspace(max(0, latest_time - window_size), latest_time, 20)
        spike_hist, _ = np.histogram(spike_times, bins=bins)
        bin_centers = (bins[:-1] + bins[1:]) / 2
        # Convert to Hz (spikes per second)
        firing_rates = spike_hist / (window_size / 1000.0 / 20) / self.n_neurons * 1000
        
        ax2.plot(bin_centers, firing_rates, 'r-', linewidth=2, alpha=0.7)
        ax2.set_ylabel('Firing Rate (Hz)', color='r')
        ax2.tick_params(axis='y', labelcolor='r')
        
        plt.tight_layout()
        plt.show()
    
    def visualize_connectivity(self, figsize=(8, 8)) -> None:
        """
        Visualize synaptic connectivity.
        
        Args:
            figsize: Figure size
        """
        plt.figure(figsize=figsize)
        
        # Create separate matrices for excitatory and inhibitory connections
        exc_weights = self.synaptic_weights.copy()
        exc_weights[exc_weights <= 0] = 0
        
        inh_weights = self.synaptic_weights.copy()
        inh_weights[inh_weights >= 0] = 0
        
        # Plot as a colormap
        plt.subplot(1, 2, 1)
        plt.imshow(exc_weights, cmap='Reds', interpolation='nearest')
        plt.colorbar(label='Excitatory Weight')
        plt.title('Excitatory Connections')
        plt.xlabel('Target Neuron')
        plt.ylabel('Source Neuron')
        
        plt.subplot(1, 2, 2)
        plt.imshow(inh_weights, cmap='Blues_r', interpolation='nearest')
        plt.colorbar(label='Inhibitory Weight')
        plt.title('Inhibitory Connections')
        plt.xlabel('Target Neuron')
        plt.ylabel('Source Neuron')
        
        plt.tight_layout()
        plt.show()


class VisualCortex(BrainRegion):
    """
    Emulation of the visual cortex.
    
    This class implements processing similar to the visual cortex, including
    receptive fields, orientation selectivity, and hierarchical processing.
    """
    
    def __init__(
        self,
        region_id: int,
        input_width: int = 28,
        input_height: int = 28,
        n_simple_cells: int = 100,
        n_complex_cells: int = 50,
        n_hypercomplex_cells: int = 25,
        plasticity_rules: Optional[List[SynapticPlasticityRule]] = None
    ):
        """
        Initialize visual cortex.
        
        Args:
            region_id: Unique identifier for this region
            input_width: Width of input visual field
            input_height: Height of input visual field
            n_simple_cells: Number of simple cells (edge detectors)
            n_complex_cells: Number of complex cells (motion, texture)
            n_hypercomplex_cells: Number of hypercomplex cells (corners, junctions)
            plasticity_rules: List of plasticity rules to apply
        """
        n_total = n_simple_cells + n_complex_cells + n_hypercomplex_cells
        
        neuron_params = {
            'tau_m': 15.0,        # Membrane time constant (ms)
            'v_rest': -70.0,      # Resting potential (mV)
            'v_threshold': -55.0, # Threshold potential (mV)
            'v_reset': -70.0,     # Reset potential (mV)
            'refractory_period': 2.0  # Refractory period (ms)
        }
        
        super().__init__(
            region_id=region_id,
            region_type=BrainRegionType.VISUAL_CORTEX,
            n_neurons=n_total,
            neuron_params=neuron_params,
            topology="layered",
            plasticity_rules=plasticity_rules
        )
        
        # Visual cortex specific parameters
        self.input_width = input_width
        self.input_height = input_height
        self.n_simple_cells = n_simple_cells
        self.n_complex_cells = n_complex_cells
        self.n_hypercomplex_cells = n_hypercomplex_cells
        
        # Neuron indices for different cell types
        self.simple_indices = list(range(0, n_simple_cells))
        self.complex_indices = list(range(n_simple_cells, n_simple_cells + n_complex_cells))
        self.hypercomplex_indices = list(range(n_simple_cells + n_complex_cells, n_total))
        
        # Initialize receptive fields
        self.receptive_fields = []
        self._initialize_receptive_fields()
        
        # Feature maps
        self.feature_maps = {
            'edges': np.zeros((input_height, input_width)),
            'orientations': np.zeros((8, input_height, input_width)),  # 8 orientation bins
            'corners': np.zeros((input_height, input_width))
        }
        
        # Orientation preferences
        self.orientation_preferences = np.zeros(n_total)
        self._initialize_orientation_preferences()
        
        # Visual input buffer
        self.visual_input = np.zeros((input_height, input_width))
        self.visual_input_timestamp = 0.0
        
        logger.info(f"Initialized Visual Cortex with {n_total} neurons " 
                  f"({n_simple_cells} simple, {n_complex_cells} complex, "
                  f"{n_hypercomplex_cells} hypercomplex)")
    
    def _initialize_receptive_fields(self) -> None:
        """Initialize receptive fields for different cell types."""
        # Simple cell receptive fields (Gabor filters)
        for i in self.simple_indices:
            # Random position in visual field
            center_x = np.random.randint(0, self.input_width)
            center_y = np.random.randint(0, self.input_height)
            
            # Random orientation (0, 45, 90, 135 degrees)
            orientation = np.random.choice([0, np.pi/4, np.pi/2, 3*np.pi/4])
            
            # Random phase
            phase = np.random.uniform(0, 2*np.pi)
            
            # Create Gabor filter
            rf = self._create_gabor_filter(
                size=7,
                center=(center_x, center_y),
                orientation=orientation,
                wavelength=4.0,
                phase=phase,
                sigma=1.5
            )
            
            self.receptive_fields.append({
                'type': 'simple',
                'filter': rf,
                'center': (center_x, center_y),
                'orientation': orientation,
                'phase': phase
            })
        
        # Complex cell receptive fields (combination of simple cells)
        for i in self.complex_indices:
            # Connect to multiple simple cells with similar orientations
            orientation = np.random.choice([0, np.pi/4, np.pi/2, 3*np.pi/4])
            
            # Find simple cells with similar orientation
            similar_simple_cells = []
            for j, j_idx in enumerate(self.simple_indices):
                rf = self.receptive_fields[j]
                if abs(rf['orientation'] - orientation) < np.pi/8 or abs(rf['orientation'] - orientation) > 7*np.pi/8:
                    similar_simple_cells.append(j_idx)
            
            # Randomly select a subset of these simple cells
            if similar_simple_cells:
                selected_cells = np.random.choice(
                    similar_simple_cells,
                    size=min(5, len(similar_simple_cells)),
                    replace=False
                )
                
                # Connect to these cells
                for simple_idx in selected_cells:
                    self.synaptic_weights[simple_idx, i] = np.random.uniform(0.2, 0.5)
            
            self.receptive_fields.append({
                'type': 'complex',
                'orientation': orientation,
                'connected_to': similar_simple_cells
            })
        
        # Hypercomplex cell receptive fields (end-stopped cells)
        for i in self.hypercomplex_indices:
            # Connect to complex cells with different orientations
            orientations = np.random.choice([0, np.pi/4, np.pi/2, 3*np.pi/4], size=2, replace=False)
            
            # Find complex cells with these orientations
            connected_cells = []
            for j, j_idx in enumerate(self.complex_indices):
                rf_idx = len(self.simple_indices) + j
                rf = self.receptive_fields[rf_idx]
                if any(abs(rf['orientation'] - o) < np.pi/8 for o in orientations):
                    connected_cells.append(j_idx)
            
            if connected_cells:
                # Connect to these cells
                for complex_idx in connected_cells:
                    self.synaptic_weights[complex_idx, i] = np.random.uniform(0.2, 0.5)
            
            self.receptive_fields.append({
                'type': 'hypercomplex',
                'orientations': orientations,
                'connected_to': connected_cells
            })
    
    def _initialize_orientation_preferences(self) -> None:
        """Initialize orientation preferences for neurons."""
        # Simple cells - based on their receptive fields
        for i, i_idx in enumerate(self.simple_indices):
            self.orientation_preferences[i_idx] = self.receptive_fields[i]['orientation']
        
        # Complex cells - based on their receptive fields
        for i, i_idx in enumerate(self.complex_indices):
            rf_idx = len(self.simple_indices) + i
            self.orientation_preferences[i_idx] = self.receptive_fields[rf_idx]['orientation']
        
        # Hypercomplex cells - average of connected orientations
        for i, i_idx in enumerate(self.hypercomplex_indices):
            rf_idx = len(self.simple_indices) + len(self.complex_indices) + i
            self.orientation_preferences[i_idx] = np.mean(self.receptive_fields[rf_idx]['orientations'])
    
    def _create_gabor_filter(self, size, center, orientation, wavelength, phase, sigma) -> np.ndarray:
        """
        Create a Gabor filter for a simple cell receptive field.
        
        Args:
            size: Filter size
            center: (x, y) center position
            orientation: Filter orientation
            wavelength: Sinusoid wavelength
            phase: Sinusoid phase
            sigma: Gaussian sigma
            
        Returns:
            2D Gabor filter array
        """
        # Create coordinate arrays
        y, x = np.meshgrid(np.arange(self.input_height), np.arange(self.input_width))
        
        # Center the coordinates
        x0 = center[0]
        y0 = center[1]
        
        # Rotation
        x_theta = (x - x0) * np.cos(orientation) + (y - y0) * np.sin(orientation)
        y_theta = -(x - x0) * np.sin(orientation) + (y - y0) * np.cos(orientation)
        
        # Compute Gabor filter
        gb = np.exp(-(x_theta**2 + y_theta**2) / (2 * sigma**2)) * np.cos(2 * np.pi * x_theta / wavelength + phase)
        
        # Normalize
        gb = gb / np.sqrt(np.sum(gb**2))
        
        return gb
    
    def process_event(self, event: Event) -> List[Event]:
        """
        Process event in visual cortex.
        
        Args:
            event: Input event
            
        Returns:
            List of output events
        """
        # Special handling for visual input events
        if event.event_type == EventType.CHANGE and hasattr(event, 'payload') and event.payload:
            if 'image' in event.payload:
                return self._process_image_event(event)
        
        # Default to parent class implementation
        return super().process_event(event)
    
    def _process_image_event(self, event: Event) -> List[Event]:
        """
        Process an image input event.
        
        Args:
            event: Event containing image data
            
        Returns:
            List of output events
        """
        # Extract image from payload
        image = event.payload['image']
        current_time = event.timestamp
        
        # Ensure image has correct dimensions
        if image.shape != (self.input_height, self.input_width):
            # Resize if necessary
            from scipy.ndimage import zoom
            zoom_factors = (self.input_height / image.shape[0], self.input_width / image.shape[1])
            image = zoom(image, zoom_factors, order=1)
        
        # Store the visual input
        self.visual_input = image
        self.visual_input_timestamp = current_time
        
        # Process image through receptive fields
        output_events = []
        
        # Calculate stimulus for each neuron
        for i, rf in enumerate(self.receptive_fields):
            if rf['type'] == 'simple':
                # Apply Gabor filter to image
                response = np.sum(image * rf['filter'])
                
                if response > 0.1:  # Threshold for activation
                    neuron_idx = i
                    self.membrane_potentials[neuron_idx] += response * 10.0  # Scale response
                    
                    # Update feature maps
                    x, y = rf['center']
                    if 0 <= x < self.input_width and 0 <= y < self.input_height:
                        self.feature_maps['edges'][y, x] = max(self.feature_maps['edges'][y, x], response)
                        
                        # Orientation map
                        orientation_bin = int(rf['orientation'] / (np.pi / 8)) % 8
                        self.feature_maps['orientations'][orientation_bin, y, x] = max(
                            self.feature_maps['orientations'][orientation_bin, y, x],
                            response
                        )
        
        # Process any spikes that occurred
        for i in range(self.n_neurons):
            if self.membrane_potentials[i] >= self.neurons[i].v_threshold:
                # Record spike
                self.spike_history.append((i, current_time))
                
                # Reset membrane potential
                self.membrane_potentials[i] = self.neurons[i].v_reset
                
                # Update firing rate
                self.firing_rates[i] += 1.0
                
                # Generate output event
                spike_event = SpikeEvent(
                    timestamp=current_time,
                    neuron_id=self.processor_id,
                    payload={
                        'neuron_idx': i,
                        'value': 1.0,
                        'orientation': self.orientation_preferences[i],
                        'neuron_type': 'simple' if i in self.simple_indices else 
                                      ('complex' if i in self.complex_indices else 'hypercomplex')
                    }
                )
                
                output_events.append(spike_event)
                
                # Update feature maps for hypercomplex cells (corners, junctions)
                if i in self.hypercomplex_indices:
                    hc_index = i - (self.n_simple_cells + self.n_complex_cells)
                    rf_idx = len(self.simple_indices) + len(self.complex_indices) + hc_index
                    rf = self.receptive_fields[rf_idx]
                    
                    # Find connected complex cells
                    for complex_idx in rf['connected_to']:
                        complex_rf_idx = len(self.simple_indices) + (complex_idx - self.n_simple_cells)
                        complex_rf = self.receptive_fields[complex_rf_idx]
                        
                        # Find connected simple cells
                        for simple_idx in complex_rf['connected_to']:
                            simple_rf = self.receptive_fields[simple_idx]
                            x, y = simple_rf['center']
                            
                            if 0 <= x < self.input_width and 0 <= y < self.input_height:
                                self.feature_maps['corners'][y, x] += 0.2
        
        return output_events
    
    def get_feature_maps(self) -> Dict[str, np.ndarray]:
        """
        Get computed feature maps.
        
        Returns:
            Dictionary of feature maps
        """
        return self.feature_maps
    
    def visualize_feature_maps(self, figsize=(12, 8)) -> None:
        """
        Visualize feature maps.
        
        Args:
            figsize: Figure size
        """
        plt.figure(figsize=figsize)
        
        # Plot original input
        plt.subplot(2, 3, 1)
        plt.imshow(self.visual_input, cmap='gray')
        plt.title('Input Image')
        plt.axis('off')
        
        # Plot edge map
        plt.subplot(2, 3, 2)
        plt.imshow(self.feature_maps['edges'], cmap='hot')
        plt.title('Edge Detection')
        plt.axis('off')
        
        # Plot corner map
        plt.subplot(2, 3, 3)
        plt.imshow(self.feature_maps['corners'], cmap='hot')
        plt.title('Corner Detection')
        plt.axis('off')
        
        # Plot orientation maps (max across orientations)
        plt.subplot(2, 3, 4)
        orientation_max = np.max(self.feature_maps['orientations'], axis=0)
        plt.imshow(orientation_max, cmap='hot')
        plt.title('Orientation Strength')
        plt.axis('off')
        
        # Plot dominant orientation
        plt.subplot(2, 3, 5)
        dominant_orientation = np.argmax(self.feature_maps['orientations'], axis=0) * (180 / 8)
        plt.imshow(dominant_orientation, cmap='hsv')
        plt.title('Dominant Orientation')
        plt.axis('off')
        plt.colorbar(label='Orientation (degrees)')
        
        # Plot neuron responses
        plt.subplot(2, 3, 6)
        neuron_activity = np.zeros(self.n_neurons)
        for neuron_idx, _ in self.spike_history:
            if neuron_idx < self.n_neurons:
                neuron_activity[neuron_idx] += 1
        
        plt.bar(['Simple', 'Complex', 'Hypercomplex'], 
              [np.mean(neuron_activity[self.simple_indices]), 
               np.mean(neuron_activity[self.complex_indices]), 
               np.mean(neuron_activity[self.hypercomplex_indices])])
        plt.title('Average Neuron Activity')
        plt.ylabel('Spikes')
        
        plt.tight_layout()
        plt.show()


class ReceptiveField:
    """
    Represents a receptive field for sensory neurons.
    
    This class models the spatial receptive field of a neuron, defining how it
    responds to stimuli at different locations in the sensory space.
    """
    
    def __init__(
        self,
        center: Tuple[float, float],
        size: float,
        field_type: str = 'gaussian',
        orientation: float = 0.0,
        on_center: bool = True,
        params: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize receptive field.
        
        Args:
            center: (x, y) center position
            size: Field size
            field_type: Type of receptive field
            orientation: Field orientation (radians)
            on_center: Whether field is on-center (True) or off-center (False)
            params: Additional field-specific parameters
        """
        self.center = center
        self.size = size
        self.field_type = field_type
        self.orientation = orientation
        self.on_center = on_center
        self.params = params or {}
        
        # Cache computed field
        self.field_data = None
        self.grid_size = None
    
    def compute_field(self, grid_shape: Tuple[int, int]) -> np.ndarray:
        """
        Compute receptive field over a grid.
        
        Args:
            grid_shape: (height, width) of grid
            
        Returns:
            Array of field values
        """
        # Check if we can use cached values
        if (self.field_data is not None and self.grid_size == grid_shape):
            return self.field_data
        
        # Create coordinate grid
        y, x = np.meshgrid(np.arange(grid_shape[0]), np.arange(grid_shape[1]))
        
        # Center and rotate coordinates
        x_c = x - self.center[0]
        y_c = y - self.center[1]
        
        x_r = x_c * np.cos(self.orientation) + y_c * np.sin(self.orientation)
        y_r = -x_c * np.sin(self.orientation) + y_c * np.cos(self.orientation)
        
        # Compute field values based on type
        if self.field_type == 'gaussian':
            # Gaussian receptive field
            sigma = self.params.get('sigma', self.size / 2)
            field = np.exp(-(x_r**2 + y_r**2) / (2 * sigma**2))
            
            # Apply on-center or off-center
            if not self.on_center:
                field = -field
                
        elif self.field_type == 'dog':
            # Difference of Gaussians (Mexican hat)
            sigma_center = self.params.get('sigma_center', self.size / 4)
            sigma_surround = self.params.get('sigma_surround', self.size / 2)
            
            center = np.exp(-(x_r**2 + y_r**2) / (2 * sigma_center**2))
            surround = np.exp(-(x_r**2 + y_r**2) / (2 * sigma_surround**2))
            
            if self.on_center:
                field = center - 0.5 * surround
            else:
                field = 0.5 * surround - center
                
        elif self.field_type == 'gabor':
            # Gabor filter (edge detector)
            sigma = self.params.get('sigma', self.size / 4)
            wavelength = self.params.get('wavelength', self.size)
            phase = self.params.get('phase', 0.0)
            
            gaussian = np.exp(-(x_r**2 + y_r**2) / (2 * sigma**2))
            sinusoid = np.sin(2 * np.pi * x_r / wavelength + phase)
            
            field = gaussian * sinusoid
            
        elif self.field_type == 'edgestop':
            # End-stopped cell (responds to line endings)
            sigma = self.params.get('sigma', self.size / 4)
            length = self.params.get('length', self.size)
            
            gaussian_x = np.exp(-x_r**2 / (2 * sigma**2))
            gaussian_y = np.exp(-y_r**2 / (2 * (length * sigma)**2))
            
            # Subtract activity at the end
            end_mask = np.abs(y_r) > length/2
            end_gaussian = np.exp(-(x_r**2 + (np.abs(y_r) - length/2)**2) / (2 * sigma**2))
            
            field = gaussian_x * gaussian_y - 0.5 * end_mask * end_gaussian
            
        else:
            # Default to simple Gaussian
            sigma = self.size / 2
            field = np.exp(-(x_r**2 + y_r**2) / (2 * sigma**2))
            
            if not self.on_center:
                field = -field
        
        # Normalize
        field = field / np.max(np.abs(field))
        
        # Cache for future use
        self.field_data = field
        self.grid_size = grid_shape
        
        return field
    
    def response(self, input_grid: np.ndarray) -> float:
        """
        Compute response to an input grid.
        
        Args:
            input_grid: Input grid of values
            
        Returns:
            Response value
        """
        # Compute field for this grid size
        field = self.compute_field(input_grid.shape)
        
        # Compute dot product
        response = np.sum(input_grid * field)
        
        return response
    
    def visualize(self, grid_shape: Tuple[int, int] = (50, 50)) -> None:
        """
        Visualize the receptive field.
        
        Args:
            grid_shape: Shape of grid for visualization
        """
        field = self.compute_field(grid_shape)
        
        plt.figure(figsize=(6, 6))
        plt.imshow(field.T, cmap='coolwarm', interpolation='bilinear')
        plt.colorbar(label='Response')
        
        plt.title(f"{self.field_type.capitalize()} Receptive Field")
        plt.xlabel('X')
        plt.ylabel('Y')
        
        # Add center point
        plt.plot(self.center[0], self.center[1], 'ko', markersize=5)
        
        # Add orientation line
        if self.field_type in ['gabor', 'edgestop']:
            line_length = self.size
            dx = line_length * np.cos(self.orientation)
            dy = line_length * np.sin(self.orientation)
            plt.arrow(self.center[0], self.center[1], dx, dy, 
                    head_width=2, head_length=2, fc='k', ec='k')
        
        plt.tight_layout()
        plt.show()


class Hippocampus(BrainRegion):
    """
    Emulation of the hippocampus.
    
    This class implements hippocampal processing, including place cells,
    pattern separation/completion, and memory encoding/retrieval.
    """
    
    def __init__(
        self,
        region_id: int,
        n_place_cells: int = 100,
        n_grid_cells: int = 64,
        n_dg_cells: int = 200,  # Dentate gyrus cells
        n_ca3_cells: int = 100,  # CA3 cells
        n_ca1_cells: int = 100,  # CA1 cells
        environment_size: Tuple[float, float] = (100.0, 100.0),
        plasticity_rules: Optional[List[SynapticPlasticityRule]] = None
    ):
        """
        Initialize hippocampus.
        
        Args:
            region_id: Unique identifier for this region
            n_place_cells: Number of place cells
            n_grid_cells: Number of grid cells
            n_dg_cells: Number of dentate gyrus cells
            n_ca3_cells: Number of CA3 cells
            n_ca1_cells: Number of CA1 cells
            environment_size: Size of the environment
            plasticity_rules: List of plasticity rules to apply
        """
        n_total = n_place_cells + n_grid_cells + n_dg_cells + n_ca3_cells + n_ca1_cells
        
        neuron_params = {
            'tau_m': 20.0,        # Membrane time constant (ms)
            'v_rest': -65.0,      # Resting potential (mV)
            'v_threshold': -50.0, # Threshold potential (mV)
            'v_reset': -65.0,     # Reset potential (mV)
            'refractory_period': 3.0  # Refractory period (ms)
        }
        
        super().__init__(
            region_id=region_id,
            region_type=BrainRegionType.HIPPOCAMPUS,
            n_neurons=n_total,
            neuron_params=neuron_params,
            topology="small-world",
            plasticity_rules=plasticity_rules or [SynapticPlasticityRule.STDP, SynapticPlasticityRule.HEBBIAN]
        )
        
        # Hippocampus specific parameters
        self.environment_size = environment_size
        self.n_place_cells = n_place_cells
        self.n_grid_cells = n_grid_cells
        self.n_dg_cells = n_dg_cells
        self.n_ca3_cells = n_ca3_cells
        self.n_ca1_cells = n_ca1_cells
        
        # Neuron indices for different cell types
        self.place_indices = list(range(0, n_place_cells))
        self.grid_indices = list(range(n_place_cells, n_place_cells + n_grid_cells))
        self.dg_indices = list(range(n_place_cells + n_grid_cells, 
                                   n_place_cells + n_grid_cells + n_dg_cells))
        self.ca3_indices = list(range(n_place_cells + n_grid_cells + n_dg_cells,
                                    n_place_cells + n_grid_cells + n_dg_cells + n_ca3_cells))
        self.ca1_indices = list(range(n_place_cells + n_grid_cells + n_dg_cells + n_ca3_cells, n_total))
        
        # Initialize place fields and grid fields
        self.place_fields = []
        self.grid_fields = []
        self._initialize_place_and_grid_cells()
        
        # Memory storage
        self.memories = []
        self.memory_ca3_patterns = []
        
        # Current position
        self.current_position = (0.0, 0.0)
        self.position_history = []
        
        logger.info(f"Initialized Hippocampus with {n_total} neurons "
                  f"({n_place_cells} place, {n_grid_cells} grid, "
                  f"{n_dg_cells} DG, {n_ca3_cells} CA3, {n_ca1_cells} CA1)")
    
    def _initialize_place_and_grid_cells(self) -> None:
        """Initialize place and grid cell properties."""
        # Initialize place cells
        for i in self.place_indices:
            # Random position in environment
            x = np.random.uniform(0, self.environment_size[0])
            y = np.random.uniform(0, self.environment_size[1])
            
            # Place field size
            size = np.random.uniform(5.0, 15.0)
            
            self.place_fields.append({
                'center': (x, y),
                'size': size
            })
        
        # Initialize grid cells
        for i in self.grid_indices:
            # Grid cell parameters
            spacing = np.random.uniform(10.0, 30.0)  # Grid spacing
            orientation = np.random.uniform(0, np.pi/3)  # Grid orientation
            phase_x = np.random.uniform(0, spacing)  # Phase offset
            phase_y = np.random.uniform(0, spacing)  # Phase offset
            
            self.grid_fields.append({
                'spacing': spacing,
                'orientation': orientation,
                'phase': (phase_x, phase_y)
            })
        
        # Create strong connections from place and grid cells to dentate gyrus
        # Dentate gyrus performs pattern separation
        for dg_idx in self.dg_indices:
            # Connect to a sparse subset of place and grid cells
            n_inputs = int(0.05 * (self.n_place_cells + self.n_grid_cells))
            input_indices = np.random.choice(
                self.place_indices + self.grid_indices,
                size=n_inputs,
                replace=False
            )
            
            for input_idx in input_indices:
                self.synaptic_weights[input_idx, dg_idx] = np.random.uniform(0.2, 0.5)
        
        # Create connections from dentate gyrus to CA3
        # CA3 forms an autoassociative network for pattern completion
        for ca3_idx in self.ca3_indices:
            # Connect to a subset of DG cells
            n_inputs = int(0.2 * self.n_dg_cells)
            input_indices = np.random.choice(
                self.dg_indices,
                size=n_inputs,
                replace=False
            )
            
            for input_idx in input_indices:
                self.synaptic_weights[input_idx, ca3_idx] = np.random.uniform(0.2, 0.5)
        
        # Create recurrent connections within CA3
        connection_prob = 0.1
        for i in self.ca3_indices:
            for j in self.ca3_indices:
                if i != j and np.random.random() < connection_prob:
                    self.synaptic_weights[i, j] = np.random.uniform(0.05, 0.2)
        
        # Create connections from CA3 to CA1
        for ca1_idx in self.ca1_indices:
            # Connect to a subset of CA3 cells
            n_inputs = int(0.3 * self.n_ca3_cells)
            input_indices = np.random.choice(
                self.ca3_indices,
                size=n_inputs,
                replace=False
            )
            
            for input_idx in input_indices:
                self.synaptic_weights[input_idx, ca1_idx] = np.random.uniform(0.2, 0.5)
        
        # Create connections from entorhinal cortex (place and grid cells) to CA1
        # This forms the direct pathway
        for ca1_idx in self.ca1_indices:
            # Connect to a subset of place and grid cells
            n_inputs = int(0.1 * (self.n_place_cells + self.n_grid_cells))
            input_indices = np.random.choice(
                self.place_indices + self.grid_indices,
                size=n_inputs,
                replace=False
            )
            
            for input_idx in input_indices:
                self.synaptic_weights[input_idx, ca1_idx] = np.random.uniform(0.1, 0.3)
    
    def update_position(self, x: float, y: float, timestamp: float) -> None:
        """
        Update the agent's position in the environment.
        
        Args:
            x: X coordinate
            y: Y coordinate
            timestamp: Current time
        """
        # Store position
        self.current_position = (x, y)
        self.position_history.append((timestamp, x, y))
        
        # Update place and grid cell activity
        self._update_spatial_cells(timestamp)
    
    def _update_spatial_cells(self, timestamp: float) -> None:
        """
        Update place and grid cell activities based on current position.
        
        Args:
            timestamp: Current time
        """
        x, y = self.current_position
        
        # Update place cells
        for i, i_idx in enumerate(self.place_indices):
            field = self.place_fields[i]
            center_x, center_y = field['center']
            size = field['size']
            
            # Compute distance to place field center
            distance = np.sqrt((x - center_x)**2 + (y - center_y)**2)
            
            # Compute activation (Gaussian tuning curve)
            activation = np.exp(-distance**2 / (2 * size**2))
            
            # Add to membrane potential
            if activation > 0.1:  # Apply threshold for efficiency
                self.membrane_potentials[i_idx] += activation * 20.0
                
                # Generate spike if threshold exceeded
                if self.membrane_potentials[i_idx] >= self.neurons[i_idx].v_threshold:
                    # Record spike
                    self.spike_history.append((i_idx, timestamp))
                    
                    # Reset membrane potential
                    self.membrane_potentials[i_idx] = self.neurons[i_idx].v_reset
                    
                    # Update firing rate
                    self.firing_rates[i_idx] += 1.0
        
        # Update grid cells
        for i, i_idx in enumerate(self.grid_indices):
            field = self.grid_fields[i]
            spacing = field['spacing']
            orientation = field['orientation']
            phase_x, phase_y = field['phase']
            
            # Transform coordinates based on grid orientation
            x_rot = x * np.cos(orientation) + y * np.sin(orientation)
            y_rot = -x * np.sin(orientation) + y * np.cos(orientation)
            
            # Compute grid cell activation (sum of three cosine gratings)
            k = 4.0 * np.pi / (spacing * np.sqrt(3))
            
            # Three sets of basis vectors at 60-degree intervals
            activation = (np.cos(k * x_rot + phase_x) + 
                        np.cos(k * (-0.5 * x_rot + 0.866 * y_rot) + phase_y) + 
                        np.cos(k * (-0.5 * x_rot - 0.866 * y_rot) + phase_x + phase_y))
            
            # Normalize to [0, 1]
            activation = (activation + 3.0) / 6.0
            
            # Add to membrane potential
            if activation > 0.5:  # Apply threshold for efficiency
                self.membrane_potentials[i_idx] += (activation - 0.5) * 40.0
                
                # Generate spike if threshold exceeded
                if self.membrane_potentials[i_idx] >= self.neurons[i_idx].v_threshold:
                    # Record spike
                    self.spike_history.append((i_idx, timestamp))
                    
                    # Reset membrane potential
                    self.membrane_potentials[i_idx] = self.neurons[i_idx].v_reset
                    
                    # Update firing rate
                    self.firing_rates[i_idx] += 1.0
    
    def store_memory(self, memory_data: Any, context: Optional[Dict[str, Any]] = None) -> int:
        """
        Store a new memory in the hippocampus.
        
        Args:
            memory_data: Data to store
            context: Optional contextual information
            
        Returns:
            Memory ID
        """
        # Create memory object
        memory_id = len(self.memories)
        memory = {
            'id': memory_id,
            'data': memory_data,
            'context': context or {},
            'position': self.current_position,
            'timestamp': time.time(),
            'strength': 1.0  # Initial memory strength
        }
        
        # Store memory
        self.memories.append(memory)
        
        # Generate CA3 pattern for this memory
        ca3_pattern = np.zeros(self.n_ca3_cells)
        
        # Select a subset of CA3 cells to encode this memory
        n_active = int(0.1 * self.n_ca3_cells)
        active_cells = np.random.choice(self.n_ca3_cells, size=n_active, replace=False)
        ca3_pattern[active_cells] = 1.0
        
        # Store pattern
        self.memory_ca3_patterns.append(ca3_pattern)
        
        # Strengthen connections to encode this memory
        self._encode_memory_in_ca3(memory_id, ca3_pattern)
        
        return memory_id
    
    def _encode_memory_in_ca3(self, memory_id: int, ca3_pattern: np.ndarray) -> None:
        """
        Encode memory in CA3 by strengthening recurrent connections.
        
        Args:
            memory_id: Memory ID
            ca3_pattern: CA3 activation pattern
        """
        # Get active CA3 cell indices
        active_cells = [self.ca3_indices[i] for i in range(self.n_ca3_cells) if ca3_pattern[i] > 0]
        
        # Strengthen recurrent connections between active cells
        for i in active_cells:
            for j in active_cells:
                if i != j:
                    # Hebbian learning: strengthen connections between co-active cells
                    self.synaptic_weights[i, j] += 0.1
                    
                    # Apply upper bound
                    self.synaptic_weights[i, j] = min(1.0, self.synaptic_weights[i, j])
    
    def retrieve_memory(self, context: Optional[Dict[str, Any]] = None, 
                      position: Optional[Tuple[float, float]] = None) -> Optional[Dict[str, Any]]:
        """
        Retrieve a memory based on context or position.
        
        Args:
            context: Optional contextual cues
            position: Optional position cue
            
        Returns:
            Retrieved memory or None if no memory found
        """
        # Use current position if none provided
        if position is None:
            position = self.current_position
        
        # Initialize pattern completion
        # Stimulate place cells based on position
        for i, i_idx in enumerate(self.place_indices):
            field = self.place_fields[i]
            center_x, center_y = field['center']
            size = field['size']
            
            # Compute distance to place field center
            x, y = position
            distance = np.sqrt((x - center_x)**2 + (y - center_y)**2)
            
            # Compute activation
            activation = np.exp(-distance**2 / (2 * size**2))
            
            # Stimulate place cell
            if activation > 0.1:
                self.membrane_potentials[i_idx] += activation * 20.0
        
        # Run network dynamics to perform pattern completion in CA3
        # Simulate for a short period
        self._simulate_network_dynamics(50.0)  # 50 ms
        
        # Get CA3 activity pattern
        ca3_activity = np.zeros(self.n_ca3_cells)
        for i, i_idx in enumerate(self.ca3_indices):
            ca3_activity[i] = self.firing_rates[i_idx]
        
        # Normalize
        if np.sum(ca3_activity) > 0:
            ca3_activity = ca3_activity / np.sum(ca3_activity)
        
        # Find closest stored memory pattern
        best_match = -1
        best_similarity = -1.0
        
        for mem_id, pattern in enumerate(self.memory_ca3_patterns):
            # Compute pattern similarity (cosine similarity)
            similarity = np.dot(ca3_activity, pattern) / (np.linalg.norm(ca3_activity) * np.linalg.norm(pattern))
            
            if similarity > best_similarity:
                best_similarity = similarity
                best_match = mem_id
        
        # Return memory if similarity is above threshold
        if best_similarity > 0.3 and best_match >= 0:
            return self.memories[best_match]
        
        return None
    
    def _simulate_network_dynamics(self, duration: float) -> None:
        """
        Simulate network dynamics for a period of time.
        
        Args:
            duration: Simulation duration (ms)
        """
        # Simple simulation with fixed time step
        dt = 1.0  # 1 ms time step
        current_time = self.last_update_time if self.last_update_time > 0 else 0.0
        end_time = current_time + duration
        
        for t in np.arange(current_time, end_time, dt):
            # Update network state
            self._update_state(dt)
            
            # Process any spikes that occurred
            for i in range(self.n_neurons):
                if self.membrane_potentials[i] >= self.neurons[i].v_threshold:
                    # Record spike
                    self.spike_history.append((i, t))
                    
                    # Reset membrane potential
                    self.membrane_potentials[i] = self.neurons[i].v_reset
                    
                    # Update firing rate
                    self.firing_rates[i] += 1.0
                    
                    # Propagate spike to target neurons
                    for j in range(self.n_neurons):
                        if self.synaptic_weights[i, j] != 0:
                            self.membrane_potentials[j] += self.synaptic_weights[i, j]
        
        self.last_update_time = end_time
    
    def process_event(self, event: Event) -> List[Event]:
        """
        Process event in hippocampus.
        
        Args:
            event: Input event
            
        Returns:
            List of output events
        """
        # Special handling for position update events
        if event.event_type == EventType.CHANGE and hasattr(event, 'payload') and event.payload:
            if 'position' in event.payload:
                position = event.payload['position']
                if isinstance(position, (list, tuple)) and len(position) >= 2:
                    self.update_position(position[0], position[1], event.timestamp)
                    return []
        
        # Special handling for memory events
        if event.event_type == EventType.CHANGE and hasattr(event, 'payload') and event.payload:
            if 'store_memory' in event.payload:
                memory_data = event.payload['store_memory']
                context = event.payload.get('context')
                memory_id = self.store_memory(memory_data, context)
                # A memory was stored, now return an event with the memory ID
                return [Event(
                    priority=event.timestamp,
                    timestamp=event.timestamp,
                    source_id=self.processor_id,
                    event_type=EventType.CHANGE,
                    payload={'memory_stored': True, 'memory_id': memory_id}
                )]
                
            elif 'retrieve_memory' in event.payload:
                context = event.payload.get('context')
                position = event.payload.get('position')
                memory = self.retrieve_memory(context, position)
                
                if memory:
                    # Memory retrieved, return an event with the memory data
                    return [Event(
                        priority=event.timestamp,
                        timestamp=event.timestamp,
                        source_id=self.processor_id,
                        event_type=EventType.CHANGE,
                        payload={'memory_retrieved': True, 'memory': memory}
                    )]
                else:
                    # No memory found
                    return [Event(
                        priority=event.timestamp,
                        timestamp=event.timestamp,
                        source_id=self.processor_id,
                        event_type=EventType.CHANGE,
                        payload={'memory_retrieved': False}
                    )]
        
        # Default to parent class implementation
        return super().process_event(event)
    
    def visualize_place_fields(self, figsize=(10, 8)) -> None:
        """
        Visualize place fields in the environment.
        
        Args:
            figsize: Figure size
        """
        plt.figure(figsize=figsize)
        
        # Create environment grid
        x_grid = np.linspace(0, self.environment_size[0], 100)
        y_grid = np.linspace(0, self.environment_size[1], 100)
        X, Y = np.meshgrid(x_grid, y_grid)
        
        # Plot place fields
        Z = np.zeros_like(X)
        
        for i, field in enumerate(self.place_fields):
            if i >= 10:  # Limit to first 10 place fields for clarity
                break
                
            center_x, center_y = field['center']
            size = field['size']
            
            # Compute activation across grid
            for j in range(X.shape[0]):
                for k in range(X.shape[1]):
                    distance = np.sqrt((X[j, k] - center_x)**2 + (Y[j, k] - center_y)**2)
                    activation = np.exp(-distance**2 / (2 * size**2))
                    Z[j, k] += activation
        
        # Plot heatmap
        plt.pcolor(X, Y, Z, cmap='hot', alpha=0.7)
        plt.colorbar(label='Place Cell Activity')
        
        # Plot place field centers
        for i, field in enumerate(self.place_fields):
            if i >= 10:  # Limit to first 10 place fields
                break
                
            center_x, center_y = field['center']
            plt.plot(center_x, center_y, 'o', color=f'C{i}', markersize=10, 
                   label=f'Place Cell {i}')
        
        # Plot agent position history if available
        if self.position_history:
            times, xs, ys = zip(*self.position_history)
            plt.plot(xs, ys, 'k-', alpha=0.5, label='Path')
            plt.plot(xs[-1], ys[-1], 'ko', markersize=8, label='Current Position')
        
        plt.title('Place Fields')
        plt.xlabel('X Position')
        plt.ylabel('Y Position')
        plt.legend(loc='upper right')
        plt.tight_layout()
        plt.show()
    
    def visualize_grid_cells(self, figsize=(15, 10)) -> None:
        """
        Visualize grid cell firing patterns.
        
        Args:
            figsize: Figure size
        """
        plt.figure(figsize=figsize)
        
        # Create environment grid
        x_grid = np.linspace(0, self.environment_size[0], 100)
        y_grid = np.linspace(0, self.environment_size[1], 100)
        X, Y = np.meshgrid(x_grid, y_grid)
        
        # Plot a sample of grid cells
        n_to_plot = min(6, self.n_grid_cells)
        for i in range(n_to_plot):
            plt.subplot(2, 3, i+1)
            
            field = self.grid_fields[i]
            spacing = field['spacing']
            orientation = field['orientation']
            phase_x, phase_y = field['phase']
            
            # Compute grid cell activation across grid
            Z = np.zeros_like(X)
            for j in range(X.shape[0]):
                for k in range(X.shape[1]):
                    x, y = X[j, k], Y[j, k]
                    
                    # Transform coordinates based on grid orientation
                    x_rot = x * np.cos(orientation) + y * np.sin(orientation)
                    y_rot = -x * np.sin(orientation) + y * np.cos(orientation)
                    
                    # Compute activation (sum of three cosine gratings)
                    k_factor = 4.0 * np.pi / (spacing * np.sqrt(3))
                    
                    activation = (np.cos(k_factor * x_rot + phase_x) + 
                                np.cos(k_factor * (-0.5 * x_rot + 0.866 * y_rot) + phase_y) + 
                                np.cos(k_factor * (-0.5 * x_rot - 0.866 * y_rot) + phase_x + phase_y))
                    
                    # Normalize to [0, 1]
                    Z[j, k] = (activation + 3.0) / 6.0
            
            # Plot heatmap
            plt.pcolor(X, Y, Z, cmap='viridis', alpha=0.7)
            plt.colorbar(label='Grid Cell Activity')
            
            plt.title(f'Grid Cell {i}\nSpacing: {spacing:.1f}, Orient: {orientation*180/np.pi:.1f}°')
            plt.xlabel('X Position')
            plt.ylabel('Y Position')
        
        plt.tight_layout()
        plt.show()
    
    def visualize_memory_recall(self, memory_id: int, figsize=(12, 6)) -> None:
        """
        Visualize memory recall process.
        
        Args:
            memory_id: ID of memory to visualize
            figsize: Figure size
        """
        if memory_id >= len(self.memories):
            print(f"Memory ID {memory_id} not found.")
            return
        
        memory = self.memories[memory_id]
        ca3_pattern = self.memory_ca3_patterns[memory_id]
        
        plt.figure(figsize=figsize)
        
        # Plot CA3 pattern
        plt.subplot(1, 2, 1)
        plt.stem(ca3_pattern, basefmt=' ')
        plt.title(f'CA3 Pattern for Memory {memory_id}')
        plt.xlabel('CA3 Cell Index')
        plt.ylabel('Activation')
        
        # Plot spatial info if available
        plt.subplot(1, 2, 2)
        
        # Create environment grid
        x_grid = np.linspace(0, self.environment_size[0], 50)
        y_grid = np.linspace(0, self.environment_size[1], 50)
        X, Y = np.meshgrid(x_grid, y_grid)
        
        # Compute place cell activation for each position
        Z = np.zeros_like(X)
        for j in range(X.shape[0]):
            for k in range(X.shape[1]):
                x, y = X[j, k], Y[j, k]
                
                for i, field in enumerate(self.place_fields):
                    center_x, center_y = field['center']
                    size = field['size']
                    
                    # Compute activation
                    distance = np.sqrt((x - center_x)**2 + (y - center_y)**2)
                    activation = np.exp(-distance**2 / (2 * size**2))
                    
                    # Weight by CA3 connections
                    cell_idx = self.place_indices[i]
                    for ca3_idx, ca3_cell_idx in enumerate(self.ca3_indices):
                        weight = self.synaptic_weights[cell_idx, ca3_cell_idx]
                        if ca3_pattern[ca3_idx] > 0 and weight > 0:
                            Z[j, k] += activation * weight * ca3_pattern[ca3_idx]
        
        # Plot heatmap
        plt.pcolor(X, Y, Z, cmap='hot', alpha=0.7)
        plt.colorbar(label='Memory Activation')
        
        # Plot memory location
        mem_x, mem_y = memory['position']
        plt.plot(mem_x, mem_y, 'ko', markersize=10, label='Memory Location')
        
        # Plot current location
        curr_x, curr_y = self.current_position
        plt.plot(curr_x, curr_y, 'go', markersize=8, label='Current Location')
        
        plt.title(f'Spatial Activation for Memory {memory_id}')
        plt.xlabel('X Position')
        plt.ylabel('Y Position')
        plt.legend()
        
        plt.tight_layout()
        plt.show()


class PlaceCell:
    """
    Model of a hippocampal place cell.
    
    This class implements a place cell that fires when the agent is in a specific
    location in space, with Gaussian tuning around the cell's preferred location.
    """
    
    def __init__(
        self,
        center: Tuple[float, float],
        size: float = 10.0,
        peak_rate: float = 20.0,
        environmental_context: Optional[str] = None
    ):
        """
        Initialize place cell.
        
        Args:
            center: (x, y) center of place field
            size: Size of place field (standard deviation of Gaussian)
            peak_rate: Peak firing rate (Hz)
            environmental_context: Optional context identifier
        """
        self.center = center
        self.size = size
        self.peak_rate = peak_rate
        self.environmental_context = environmental_context
        
        # Firing history
        self.spike_times = []
        self.firing_rate = 0.0
        
        # Experience-dependent parameters
        self.visited = False
        self.visit_count = 0
        self.last_visit_time = 0.0
    
    def compute_firing_rate(self, position: Tuple[float, float], context: Optional[str] = None) -> float:
        """
        Compute firing rate for a given position.
        
        Args:
            position: (x, y) position
            context: Optional environmental context
            
        Returns:
            Firing rate in Hz
        """
        # Check environmental context
        if self.environmental_context is not None and context is not None:
            if self.environmental_context != context:
                return 0.0  # No firing in different context
        
        # Compute distance to place field center
        x, y = position
        center_x, center_y = self.center
        distance = np.sqrt((x - center_x)**2 + (y - center_y)**2)
        
        # Compute firing rate (Gaussian tuning curve)
        rate = self.peak_rate * np.exp(-distance**2 / (2 * self.size**2))
        
        return rate
    
    def update(self, position: Tuple[float, float], time: float, 
             context: Optional[str] = None, dt: float = 0.001) -> List[float]:
        """
        Update place cell state for a given position and time.
        
        Args:
            position: (x, y) position
            time: Current time
            context: Optional environmental context
            dt: Time step
            
        Returns:
            List of spike times generated in this update
        """
        rate = self.compute_firing_rate(position, context)
        self.firing_rate = rate
        
        # Process visit statistics
        x, y = position
        center_x, center_y = self.center
        distance = np.sqrt((x - center_x)**2 + (y - center_y)**2)
        
        if distance < self.size * 1.5:  # Within 1.5 sigma of center
            if not self.visited or (time - self.last_visit_time) > 1.0:  # New visit after >1s
                self.visited = True
                self.visit_count += 1
                self.last_visit_time = time
        
        # Generate spikes based on firing rate
        # Using inhomogeneous Poisson process
        spike_probability = rate * dt
        spikes = []
        
        if np.random.random() < spike_probability:
            # Generate spike
            spike_time = time
            self.spike_times.append(spike_time)
            spikes.append(spike_time)
        
        return spikes
    
    def reset(self) -> None:
        """Reset place cell state."""
        self.spike_times = []
        self.firing_rate = 0.0
        self.visited = False
        self.visit_count = 0
        self.last_visit_time = 0.0


class GridCell:
    """
    Model of an entorhinal cortex grid cell.
    
    This class implements a grid cell that fires in a hexagonal grid pattern
    across the environment, regardless of environmental context.
    """
    
    def __init__(
        self,
        spacing: float = 20.0,
        orientation: float = 0.0,
        phase: Tuple[float, float] = (0.0, 0.0),
        peak_rate: float = 20.0,
        grid_factor: int = 1  # Module/scale factor
    ):
        """
        Initialize grid cell.
        
        Args:
            spacing: Grid spacing
            orientation: Grid orientation (radians)
            phase: (x, y) phase offset
            peak_rate: Peak firing rate (Hz)
            grid_factor: Scale factor for grid spacing
        """
        self.spacing = spacing * grid_factor
        self.orientation = orientation
        self.phase = phase
        self.peak_rate = peak_rate
        self.grid_factor = grid_factor
        
        # Firing history
        self.spike_times = []
        self.firing_rate = 0.0
    
    def compute_firing_rate(self, position: Tuple[float, float]) -> float:
        """
        Compute firing rate for a given position.
        
        Args:
            position: (x, y) position
            
        Returns:
            Firing rate in Hz
        """
        x, y = position
        
        # Transform coordinates based on grid orientation
        x_rot = x * np.cos(self.orientation) + y * np.sin(self.orientation)
        y_rot = -x * np.sin(self.orientation) + y * np.cos(self.orientation)
        
        # Apply phase offset
        x_rot -= self.phase[0]
        y_rot -= self.phase[1]
        
        # Compute grid cell activation (sum of three cosine gratings)
        k = 4.0 * np.pi / (self.spacing * np.sqrt(3))
        
        # Three sets of basis vectors at 60-degree intervals
        activation = (np.cos(k * x_rot) + 
                    np.cos(k * (-0.5 * x_rot + 0.866 * y_rot)) + 
                    np.cos(k * (-0.5 * x_rot - 0.866 * y_rot)))
        
        # Normalize to [0, 1]
        activation = (activation + 3.0) / 6.0
        
        # Apply firing rate
        rate = self.peak_rate * activation
        
        return rate
    
    def update(self, position: Tuple[float, float], time: float, 
             dt: float = 0.001) -> List[float]:
        """
        Update grid cell state for a given position and time.
        
        Args:
            position: (x, y) position
            time: Current time
            dt: Time step
            
        Returns:
            List of spike times generated in this update
        """
        rate = self.compute_firing_rate(position)
        self.firing_rate = rate
        
        # Generate spikes based on firing rate
        # Using inhomogeneous Poisson process
        spike_probability = rate * dt
        spikes = []
        
        if np.random.random() < spike_probability:
            # Generate spike
            spike_time = time
            self.spike_times.append(spike_time)
            spikes.append(spike_time)
        
        return spikes
    
    def reset(self) -> None:
        """Reset grid cell state."""
        self.spike_times = []
        self.firing_rate = 0.0


class PrefrontalCortex(BrainRegion):
    """
    Emulation of the prefrontal cortex.
    
    This class implements prefrontal cortical functions, including working memory,
    decision making, behavioral planning, and executive control.
    """
    
    def __init__(
        self,
        region_id: int,
        n_wm_cells: int = 100,  # Working memory cells
        n_rule_cells: int = 50,  # Rule encoding cells
        n_action_cells: int = 50,  # Action planning cells
        n_context_cells: int = 50,  # Context encoding cells
        plasticity_rules: Optional[List[SynapticPlasticityRule]] = None
    ):
        """
        Initialize prefrontal cortex.
        
        Args:
            region_id: Unique identifier for this region
            n_wm_cells: Number of working memory cells
            n_rule_cells: Number of rule-encoding cells
            n_action_cells: Number of action planning cells
            n_context_cells: Number of context-encoding cells
            plasticity_rules: List of plasticity rules to apply
        """
        n_total = n_wm_cells + n_rule_cells + n_action_cells + n_context_cells
        
        neuron_params = {
            'tau_m': 30.0,        # Longer membrane time constant for sustained activity
            'v_rest': -70.0,      # Resting potential (mV)
            'v_threshold': -50.0, # Threshold potential (mV)
            'v_reset': -55.0,     # Higher reset potential for persistent activity
            'refractory_period': 2.0  # Refractory period (ms)
        }
        
        super().__init__(
            region_id=region_id,
            region_type=BrainRegionType.PREFRONTAL_CORTEX,
            n_neurons=n_total,
            neuron_params=neuron_params,
            topology="small-world",
            plasticity_rules=plasticity_rules or [SynapticPlasticityRule.STDP, SynapticPlasticityRule.HEBBIAN]
        )
        
        # PFC specific parameters
        self.n_wm_cells = n_wm_cells
        self.n_rule_cells = n_rule_cells
        self.n_action_cells = n_action_cells
        self.n_context_cells = n_context_cells
        
        # Neuron indices for different cell types
        self.wm_indices = list(range(0, n_wm_cells))
        self.rule_indices = list(range(n_wm_cells, n_wm_cells + n_rule_cells))
        self.action_indices = list(range(n_wm_cells + n_rule_cells, 
                                      n_wm_cells + n_rule_cells + n_action_cells))
        self.context_indices = list(range(n_wm_cells + n_rule_cells + n_action_cells, n_total))
        
        # Working memory state
        self.wm_content = {}  # key -> memory pattern
        self.wm_strength = {}  # key -> memory strength
        self.wm_decay_rate = 0.001  # Decay rate for working memory
        
        # Rule state
        self.current_rules = []  # List of active rules
        self.rule_mappings = {}  # rule_name -> (condition, action) mapping
        
        # Action state
        self.action_values = np.zeros(n_action_cells)  # Value of each action
        self.planned_actions = []  # List of planned actions
        
        # Context state
        self.current_context = None
        self.context_mappings = {}  # context_id -> pattern mapping
        
        # Recurrent connections for working memory
        self._init_recurrent_connections()
        
        logger.info(f"Initialized Prefrontal Cortex with {n_total} neurons")
    
    def _init_recurrent_connections(self) -> None:
        """Initialize recurrent connections for working memory."""
        # Create strong recurrent connections within working memory cells
        # This enables sustained activity for working memory function
        for i in self.wm_indices:
            # Self-excitation for persistent activity
            self.synaptic_weights[i, i] = 0.9
            
            # Excite a small group of nearby neurons
            n_recurrent = int(0.05 * self.n_wm_cells)
            
            # Select nearby neurons (assuming indices reflect spatial organization)
            nearby_indices = []
            for j in range(max(0, i - 10), min(self.n_wm_cells, i + 10)):
                if i != j:
                    nearby_indices.append(j)
            
            # If we don't have enough nearby neurons, add some random ones
            if len(nearby_indices) < n_recurrent:
                additional_needed = n_recurrent - len(nearby_indices)
                candidate_indices = [j for j in self.wm_indices if j not in nearby_indices and j != i]
                if candidate_indices:
                    random_indices = np.random.choice(
                        candidate_indices,
                        size=min(additional_needed, len(candidate_indices)),
                        replace=False
                    )
                    nearby_indices.extend(random_indices)
            
            # Connect to these neurons
            for j in nearby_indices[:n_recurrent]:
                self.synaptic_weights[i, j] = np.random.uniform(0.1, 0.3)
                self.synaptic_weights[j, i] = np.random.uniform(0.1, 0.3)
        
        # Create connections from rule cells to working memory
        for i in self.rule_indices:
            # Each rule cell connects to a subset of working memory cells
            n_targets = int(0.1 * self.n_wm_cells)
            target_indices = np.random.choice(self.wm_indices, size=n_targets, replace=False)
            
            for j in target_indices:
                # Rules can both excite and inhibit working memory
                if np.random.random() < 0.7:  # 70% excitatory
                    self.synaptic_weights[i, j] = np.random.uniform(0.1, 0.5)
                else:  # 30% inhibitory
                    self.synaptic_weights[i, j] = np.random.uniform(-0.5, -0.1)
        
        # Create connections from working memory to action cells
        for i in self.wm_indices:
            # Each working memory cell influences a subset of action cells
            n_targets = int(0.2 * self.n_action_cells)
            target_indices = np.random.choice(self.action_indices, size=n_targets, replace=False)
            
            for j in target_indices:
                self.synaptic_weights[i, j] = np.random.uniform(0.05, 0.2)
        
        # Create connections from context cells to both working memory and rule cells
        for i in self.context_indices:
            # Connect to working memory
            n_wm_targets = int(0.1 * self.n_wm_cells)
            wm_target_indices = np.random.choice(self.wm_indices, size=n_wm_targets, replace=False)
            
            for j in wm_target_indices:
                self.synaptic_weights[i, j] = np.random.uniform(0.1, 0.3)
            
            # Connect to rules
            n_rule_targets = int(0.2 * self.n_rule_cells)
            rule_target_indices = np.random.choice(self.rule_indices, size=n_rule_targets, replace=False)
            
            for j in rule_target_indices:
                self.synaptic_weights[i, j] = np.random.uniform(0.1, 0.3)
    
    def process_event(self, event: Event) -> List[Event]:
        """
        Process event in prefrontal cortex.
        
        Args:
            event: Input event
            
        Returns:
            List of output events
        """
        # Special handling for working memory events
        if event.event_type == EventType.CHANGE and hasattr(event, 'payload') and event.payload:
            if 'wm_store' in event.payload:
                key = event.payload['wm_store'].get('key', 'default')
                value = event.payload['wm_store'].get('value')
                strength = event.payload['wm_store'].get('strength', 1.0)
                
                self.store_in_wm(key, value, strength)
                
                return [Event(
                    priority=event.timestamp,
                    timestamp=event.timestamp,
                    source_id=self.processor_id,
                    event_type=EventType.CHANGE,
                    payload={'wm_stored': key}
                )]
                
            elif 'wm_retrieve' in event.payload:
                key = event.payload['wm_retrieve']
                value, strength = self.retrieve_from_wm(key)
                
                return [Event(
                    priority=event.timestamp,
                    timestamp=event.timestamp,
                    source_id=self.processor_id,
                    event_type=EventType.CHANGE,
                    payload={
                        'wm_retrieved': True, 
                        'key': key, 
                        'value': value, 
                        'strength': strength
                    }
                )]
                
            elif 'set_rule' in event.payload:
                rule_name = event.payload['set_rule'].get('name')
                condition = event.payload['set_rule'].get('condition')
                action = event.payload['set_rule'].get('action')
                
                self.set_rule(rule_name, condition, action)
                
                return [Event(
                    priority=event.timestamp,
                    timestamp=event.timestamp,
                    source_id=self.processor_id,
                    event_type=EventType.CHANGE,
                    payload={'rule_set': rule_name}
                )]
                
            elif 'set_context' in event.payload:
                context_id = event.payload['set_context']
                self.set_context(context_id)
                
                return [Event(
                    priority=event.timestamp,
                    timestamp=event.timestamp,
                    source_id=self.processor_id,
                    event_type=EventType.CHANGE,
                    payload={'context_set': context_id}
                )]
                
            elif 'evaluate_rules' in event.payload:
                situation = event.payload['evaluate_rules']
                actions = self.evaluate_rules(situation)
                
                return [Event(
                    priority=event.timestamp,
                    timestamp=event.timestamp,
                    source_id=self.processor_id,
                    event_type=EventType.CHANGE,
                    payload={'rule_evaluation': {'situation': situation, 'actions': actions}}
                )]
        
        # Default to parent class implementation
        return super().process_event(event)
    
    def _update_state(self, dt: float) -> None:
        """
        Update internal state over time.
        
        Args:
            dt: Time step
        """
        # Call parent class implementation
        super()._update_state(dt)
        
        # Apply working memory decay
        for key in self.wm_strength:
            self.wm_strength[key] -= self.wm_decay_rate * dt
            self.wm_strength[key] = max(0.0, self.wm_strength[key])
    
    def store_in_wm(self, key: str, value: Any, strength: float = 1.0) -> None:
        """
        Store a value in working memory.
        
        Args:
            key: Memory key
            value: Value to store
            strength: Initial memory strength
        """
        self.wm_content[key] = value
        self.wm_strength[key] = strength
        
        # Encode in working memory cells
        # We'll use a simplified encoding where different keys activate different
        # subsets of working memory cells
        
        # Generate a reproducible pattern for this key
        np.random.seed(hash(key) % 2**32)
        
        # Select a subset of working memory cells
        n_active = int(0.1 * self.n_wm_cells)
        active_cells = np.random.choice(self.wm_indices, size=n_active, replace=False)
        
        # Reset seed
        np.random.seed(None)
        
        # Activate these cells
        for i in active_cells:
            self.membrane_potentials[i] += 10.0 * strength
    
    def retrieve_from_wm(self, key: str) -> Tuple[Any, float]:
        """
        Retrieve a value from working memory.
        
        Args:
            key: Memory key
            
        Returns:
            Tuple of (value, strength) or (None, 0.0) if not found
        """
        if key in self.wm_content and self.wm_strength[key] > 0.1:
            # Reactivate working memory cells for this key
            np.random.seed(hash(key) % 2**32)
            
            # Select the same subset of working memory cells
            n_active = int(0.1 * self.n_wm_cells)
            active_cells = np.random.choice(self.wm_indices, size=n_active, replace=False)
            
            # Reset seed
            np.random.seed(None)
            
            # Activate these cells
            for i in active_cells:
                self.membrane_potentials[i] += 5.0 * self.wm_strength[key]
            
            return self.wm_content[key], self.wm_strength[key]
        
        return None, 0.0
    
    def set_rule(self, rule_name: str, condition: Any, action: Any) -> None:
        """
        Set a rule in the prefrontal cortex.
        
        Args:
            rule_name: Name of the rule
            condition: Condition for rule application
            action: Action to take when rule applies
        """
        # Store rule mapping
        self.rule_mappings[rule_name] = (condition, action)
        
        # Add to current rules if not already present
        if rule_name not in self.current_rules:
            self.current_rules.append(rule_name)
        
        # Encode in rule cells
        # Generate a reproducible pattern for this rule
        np.random.seed(hash(rule_name) % 2**32)
        
        # Select a subset of rule cells
        n_active = int(0.2 * self.n_rule_cells)
        active_cells = np.random.choice(self.rule_indices, size=n_active, replace=False)
        
        # Reset seed
        np.random.seed(None)
        
        # Activate these cells
        for i in active_cells:
            self.membrane_potentials[i] += 15.0
    
    def evaluate_rules(self, situation: Any) -> List[Any]:
        """
        Evaluate rules against a situation.
        
        Args:
            situation: Current situation to evaluate
            
        Returns:
            List of actions to take
        """
        actions = []
        
        # Check each active rule
        for rule_name in self.current_rules:
            if rule_name in self.rule_mappings:
                condition, action = self.rule_mappings[rule_name]
                
                # Simplified condition evaluation
                # In a real implementation, this would be more sophisticated
                condition_met = False
                
                if isinstance(condition, dict) and isinstance(situation, dict):
                    # Check if all condition keys are in situation with matching values
                    condition_met = True
                    for key, value in condition.items():
                        if key not in situation or situation[key] != value:
                            condition_met = False
                            break
                elif condition == situation:
                    condition_met = True
                
                if condition_met:
                    actions.append(action)
                    
                    # Encode in action cells
                    # Generate a reproducible pattern for this action
                    np.random.seed(hash(str(action)) % 2**32)
                    
                    # Select a subset of action cells
                    n_active = int(0.2 * self.n_action_cells)
                    active_cells = np.random.choice(self.action_indices, size=n_active, replace=False)
                    
                    # Reset seed
                    np.random.seed(None)
                    
                    # Activate these cells
                    for i in active_cells:
                        self.membrane_potentials[i] += 20.0
        
        return actions
    
    def set_context(self, context_id: Any) -> None:
        """
        Set current context.
        
        Args:
            context_id: Context identifier
        """
        self.current_context = context_id
        
        # Record context mapping if new
        if context_id not in self.context_mappings:
            # Generate a reproducible pattern for this context
            np.random.seed(hash(str(context_id)) % 2**32)
            
            # Generate encoding pattern
            pattern = np.zeros(self.n_context_cells)
            n_active = int(0.2 * self.n_context_cells)
            active_indices = np.random.choice(self.n_context_cells, size=n_active, replace=False)
            pattern[active_indices] = 1.0
            
            # Reset seed
            np.random.seed(None)
            
            self.context_mappings[context_id] = pattern
        
        # Activate context cells
        pattern = self.context_mappings[context_id]
        for i, activity in enumerate(pattern):
            if activity > 0:
                neuron_idx = self.context_indices[i]
                self.membrane_potentials[neuron_idx] += 15.0 * activity
    
    def plan_action_sequence(self, goal: Any, current_state: Any) -> List[Any]:
        """
        Plan a sequence of actions to reach a goal from current state.
        
        Args:
            goal: Goal state
            current_state: Current state
            
        Returns:
            List of planned actions
        """
        # Simplified planning implementation
        # In a real implementation, this would be more sophisticated
        
        # Store goal in working memory
        self.store_in_wm('goal', goal, 1.0)
        
        # Store current state
        self.store_in_wm('current_state', current_state, 1.0)
        
        # Plan actions
        self.planned_actions = []
        
        # Use rules to plan actions
        situation = {'current_state': current_state, 'goal': goal}
        actions = self.evaluate_rules(situation)
        
        if actions:
            self.planned_actions.extend(actions)
        
        return self.planned_actions
    
    def visualize_working_memory(self, figsize=(10, 6)) -> None:
        """
        Visualize working memory state.
        
        Args:
            figsize: Figure size
        """
        plt.figure(figsize=figsize)
        
        # Plot working memory content
        items = list(self.wm_strength.items())
        if not items:
            plt.text(0.5, 0.5, "Working memory is empty", 
                   horizontalalignment='center', verticalalignment='center')
        else:
            keys, strengths = zip(*sorted(items, key=lambda x: x[1], reverse=True))
            
            plt.bar(keys, strengths)
            plt.ylim(0, 1.1)
            plt.ylabel('Memory Strength')
            plt.title('Working Memory Content')
            
            # Add values as text on the bars
            for i, key in enumerate(keys):
                if key in self.wm_content:
                    value = str(self.wm_content[key])
                    if len(value) > 20:
                        value = value[:17] + "..."
                    plt.text(i, strengths[i] + 0.05, value, 
                           ha='center', va='bottom', rotation=0, fontsize=9)
        
        plt.tight_layout()
        plt.show()
    
    def visualize_rules(self, figsize=(12, 6)) -> None:
        """
        Visualize active rules.
        
        Args:
            figsize: Figure size
        """
        plt.figure(figsize=figsize)
        
        if not self.current_rules:
            plt.text(0.5, 0.5, "No active rules", 
                   horizontalalignment='center', verticalalignment='center')
        else:
            # Create a table of rules
            n_rules = len(self.current_rules)
            
            cell_text = []
            for rule_name in self.current_rules:
                if rule_name in self.rule_mappings:
                    condition, action = self.rule_mappings[rule_name]
                    cell_text.append([rule_name, str(condition), str(action)])
                else:
                    cell_text.append([rule_name, "Unknown", "Unknown"])
            
            plt.axis('off')
            table = plt.table(cellText=cell_text,
                            colLabels=['Rule Name', 'Condition', 'Action'],
                            loc='center',
                            cellLoc='center')
            
            table.auto_set_font_size(False)
            table.set_fontsize(10)
            table.scale(1.2, 1.5)
            
            plt.title('Active Rules')
        
        plt.tight_layout()
        plt.show()


class BasalGanglia(BrainRegion):
    """
    Emulation of the basal ganglia.
    
    This class implements basal ganglia circuits for action selection,
    reinforcement learning, and motor control.
    """
    
    def __init__(
        self,
        region_id: int,
        n_striatum_d1: int = 50,  # Direct pathway (D1 receptors)
        n_striatum_d2: int = 50,  # Indirect pathway (D2 receptors)
        n_gpe: int = 30,          # Globus pallidus external segment
        n_gpi: int = 30,          # Globus pallidus internal segment
        n_stn: int = 30,          # Subthalamic nucleus
        n_snc: int = 20,          # Substantia nigra pars compacta
        n_actions: int = 10,      # Number of possible actions
        plasticity_rules: Optional[List[SynapticPlasticityRule]] = None
    ):
        """
        Initialize basal ganglia.
        
        Args:
            region_id: Unique identifier for this region
            n_striatum_d1: Number of D1 striatal neurons
            n_striatum_d2: Number of D2 striatal neurons
            n_gpe: Number of globus pallidus external segment neurons
            n_gpi: Number of globus pallidus internal segment neurons
            n_stn: Number of subthalamic nucleus neurons
            n_snc: Number of substantia nigra pars compacta neurons
            n_actions: Number of possible actions
            plasticity_rules: List of plasticity rules to apply
        """
        n_total = n_striatum_d1 + n_striatum_d2 + n_gpe + n_gpi + n_stn + n_snc
        
        neuron_params = {
            'tau_m': 15.0,        # Membrane time constant (ms)
            'v_rest': -70.0,      # Resting potential (mV)
            'v_threshold': -50.0, # Threshold potential (mV)
            'v_reset': -60.0,     # Reset potential (mV)
            'refractory_period': 2.0  # Refractory period (ms)
        }
        
        super().__init__(
            region_id=region_id,
            region_type=BrainRegionType.BASAL_GANGLIA,
            n_neurons=n_total,
            neuron_params=neuron_params,
            topology="small-world",
            plasticity_rules=plasticity_rules or [SynapticPlasticityRule.REWARD_MODULATED]
        )
        
        # Basal ganglia specific parameters
        self.n_striatum_d1 = n_striatum_d1
        self.n_striatum_d2 = n_striatum_d2
        self.n_gpe = n_gpe
        self.n_gpi = n_gpi
        self.n_stn = n_stn
        self.n_snc = n_snc
        self.n_actions = n_actions
        
        # Neuron indices for different nuclei
        self.striatum_d1_indices = list(range(0, n_striatum_d1))
        self.striatum_d2_indices = list(range(n_striatum_d1, n_striatum_d1 + n_striatum_d2))
        self.gpe_indices = list(range(n_striatum_d1 + n_striatum_d2, 
                                    n_striatum_d1 + n_striatum_d2 + n_gpe))
        self.gpi_indices = list(range(n_striatum_d1 + n_striatum_d2 + n_gpe,
                                     n_striatum_d1 + n_striatum_d2 + n_gpe + n_gpi))
        self.stn_indices = list(range(n_striatum_d1 + n_striatum_d2 + n_gpe + n_gpi,
                                     n_striatum_d1 + n_striatum_d2 + n_gpe + n_gpi + n_stn))
        self.snc_indices = list(range(n_striatum_d1 + n_striatum_d2 + n_gpe + n_gpi + n_stn, n_total))
        
        # Initialize basal ganglia circuitry
        self._initialize_bg_circuitry()
        
        # Action mapping
        self.action_mapping = {}  # Maps action_id to action details
        for i in range(n_actions):
            self.action_mapping[i] = {
                'name': f"Action_{i}",
                'value': 0.0,
                'count': 0,
                'last_selected': 0.0
            }
        
        # Dopamine levels
        self.dopamine_baseline = 0.5
        self.dopamine_level = self.dopamine_baseline
        self.dopamine_history = []
        
        # Learning parameters
        self.learning_rate = 0.1
        self.discount_factor = 0.9
        
        # Current state and action
        self.current_state = None
        self.current_action = None
        self.previous_state = None
        self.previous_action = None
        self.action_history = []
        
        logger.info(f"Initialized Basal Ganglia with {n_total} neurons "
                  f"({n_striatum_d1+n_striatum_d2} striatum, {n_gpe} GPe, "
                  f"{n_gpi} GPi, {n_stn} STN, {n_snc} SNc)")
    
    def _initialize_bg_circuitry(self) -> None:
        """Initialize basal ganglia circuitry."""
        # Direct pathway: Striatum (D1) -> GPi (inhibitory)
        # This pathway disinhibits the thalamus, facilitating action execution
        for i in self.striatum_d1_indices:
            # Connect to a subset of GPi neurons
            n_targets = max(1, int(0.2 * self.n_gpi))
            target_indices = np.random.choice(self.gpi_indices, size=n_targets, replace=False)
            
            for j in target_indices:
                # Inhibitory connection
                self.synaptic_weights[i, j] = np.random.uniform(-0.8, -0.2)
        
        # Indirect pathway: Striatum (D2) -> GPe (inhibitory) -> GPi & STN
        # This pathway inhibits the thalamus, suppressing unwanted actions
        for i in self.striatum_d2_indices:
            # Connect to GPe
            n_targets = max(1, int(0.3 * self.n_gpe))
            target_indices = np.random.choice(self.gpe_indices, size=n_targets, replace=False)
            
            for j in target_indices:
                # Inhibitory connection
                self.synaptic_weights[i, j] = np.random.uniform(-0.8, -0.2)
        
        # GPe -> GPi (inhibitory)
        for i in self.gpe_indices:
            n_targets = max(1, int(0.3 * self.n_gpi))
            target_indices = np.random.choice(self.gpi_indices, size=n_targets, replace=False)
            
            for j in target_indices:
                # Inhibitory connection
                self.synaptic_weights[i, j] = np.random.uniform(-0.6, -0.2)
        
        # GPe -> STN (inhibitory)
        for i in self.gpe_indices:
            n_targets = max(1, int(0.3 * self.n_stn))
            target_indices = np.random.choice(self.stn_indices, size=n_targets, replace=False)
            
            for j in target_indices:
                # Inhibitory connection
                self.synaptic_weights[i, j] = np.random.uniform(-0.6, -0.2)
        
        # Hyperdirect pathway: Cortex -> STN -> GPi
        # (We'll model this partially - the STN part)
        
        # STN -> GPi (excitatory)
        for i in self.stn_indices:
            n_targets = max(1, int(0.4 * self.n_gpi))
            target_indices = np.random.choice(self.gpi_indices, size=n_targets, replace=False)
            
            for j in target_indices:
                # Excitatory connection
                self.synaptic_weights[i, j] = np.random.uniform(0.4, 0.8)
        
        # SNc dopaminergic projections
        # These modulate plasticity in the striatum
        for i in self.snc_indices:
            # Project to both D1 and D2 striatal neurons
            for j in self.striatum_d1_indices:
                # Excitatory on D1 (will be used for learning, not direct activation)
                self.synaptic_weights[i, j] = np.random.uniform(0.01, 0.05)
            
            for j in self.striatum_d2_indices:
                # Inhibitory on D2 (will be used for learning, not direct activation)
                self.synaptic_weights[i, j] = np.random.uniform(-0.05, -0.01)
    
    def process_event(self, event: Event) -> List[Event]:
        """
        Process event in basal ganglia.
        
        Args:
            event: Input event
            
        Returns:
            List of output events
        """
        # Special handling for reward and action events
        if event.event_type == EventType.CHANGE and hasattr(event, 'payload') and event.payload:
            if 'reward' in event.payload:
                reward = event.payload['reward']
                self.process_reward(reward, event.timestamp)
                
                return [Event(
                    priority=event.timestamp,
                    timestamp=event.timestamp,
                    source_id=self.processor_id,
                    event_type=EventType.CHANGE,
                    payload={'reward_processed': reward, 'dopamine': self.dopamine_level}
                )]
                
            elif 'state' in event.payload:
                state = event.payload['state']
                self.set_state(state)
                
                # Select action based on state
                action, action_id = self.select_action()
                
                return [Event(
                    priority=event.timestamp,
                    timestamp=event.timestamp,
                    source_id=self.processor_id,
                    event_type=EventType.CHANGE,
                    payload={'action_selected': action, 'action_id': action_id}
                )]
                
            elif 'set_action_mapping' in event.payload:
                action_id = event.payload['set_action_mapping'].get('id')
                name = event.payload['set_action_mapping'].get('name')
                
                if action_id is not None and action_id < self.n_actions:
                    self.action_mapping[action_id]['name'] = name
                    
                    return [Event(
                        priority=event.timestamp,
                        timestamp=event.timestamp,
                        source_id=self.processor_id,
                        event_type=EventType.CHANGE,
                        payload={'action_mapping_set': True, 'action_id': action_id, 'name': name}
                    )]
        
        # Default to parent class implementation
        return super().process_event(event)
    
    def set_state(self, state: Any) -> None:
        """
        Set current state.
        
        Args:
            state: Current state representation
        """
        self.previous_state = self.current_state
        self.previous_action = self.current_action
        self.current_state = state
        
        # Encode state in striatal neurons
        self._encode_state_in_striatum(state)
    
    def _encode_state_in_striatum(self, state: Any) -> None:
        """
        Encode state representation in striatal neurons.
        
        Args:
            state: State representation to encode
        """
        # Generate a reproducible pattern for this state
        state_hash = hash(str(state)) % 2**32
        np.random.seed(state_hash)
        
        # Activate a subset of D1 striatal neurons
        n_d1_active = max(1, int(0.2 * self.n_striatum_d1))
        d1_active_cells = np.random.choice(self.striatum_d1_indices, size=n_d1_active, replace=False)
        
        for i in d1_active_cells:
            self.membrane_potentials[i] += 10.0
        
        # Activate a subset of D2 striatal neurons
        n_d2_active = max(1, int(0.2 * self.n_striatum_d2))
        d2_active_cells = np.random.choice(self.striatum_d2_indices, size=n_d2_active, replace=False)
        
        for i in d2_active_cells:
            self.membrane_potentials[i] += 10.0
        
        # Reset random seed
        np.random.seed(None)
    
    def select_action(self) -> Tuple[Any, int]:
        """
        Select an action based on current state using basal ganglia dynamics.
        
        Returns:
            Tuple of (action, action_id)
        """
        # Run basal ganglia dynamics to select action
        self._simulate_network_dynamics(50.0)  # 50 ms
        
        # Compute GPi activity for each action
        # Lower GPi activity means less inhibition of thalamus, facilitating action
        gpi_activity = np.zeros(self.n_actions)
        
        # Divide GPi neurons into action channels
        gpi_per_action = max(1, self.n_gpi // self.n_actions)
        
        for action_id in range(min(self.n_actions, self.n_gpi // gpi_per_action)):
            start_idx = action_id * gpi_per_action
            end_idx = (action_id + 1) * gpi_per_action
            
            # Get corresponding GPi indices
            action_gpi_indices = [self.gpi_indices[i % self.n_gpi] for i in range(start_idx, end_idx)]
            
            # Compute average firing rate
            gpi_activity[action_id] = np.mean([self.firing_rates[i] for i in action_gpi_indices])
        
        # Invert GPi activity (lower GPi = higher action probability)
        action_probabilities = 1.0 / (1.0 + gpi_activity)
        
        # Normalize to probability distribution
        if np.sum(action_probabilities) > 0:
            action_probabilities /= np.sum(action_probabilities)
        else:
            # If all zero, use uniform distribution
            action_probabilities = np.ones(self.n_actions) / self.n_actions
        
        # Select action (probabilistic)
        action_id = np.random.choice(self.n_actions, p=action_probabilities)
        
        # Update action history
        self.current_action = action_id
        self.action_mapping[action_id]['count'] += 1
        self.action_mapping[action_id]['last_selected'] = time.time()
        
        self.action_history.append((time.time(), action_id))
        
        return self.action_mapping[action_id]['name'], action_id
    
    def process_reward(self, reward: float, timestamp: float) -> None:
        """
        Process reward signal, updating dopamine levels and learning.
        
        Args:
            reward: Reward value
            timestamp: Timestamp for the reward
        """
        # Update dopamine level
        self.dopamine_level = self.dopamine_baseline + reward
        self.dopamine_history.append((timestamp, self.dopamine_level))
        
        # Update SNc neurons (dopamine source)
        for i in self.snc_indices:
            self.membrane_potentials[i] += 20.0 * reward
        
        # Apply reinforcement learning if we have previous state-action
        if self.previous_state is not None and self.previous_action is not None:
            self._update_action_values(reward)
            
            # Apply dopamine-modulated plasticity
            self._apply_dopamine_modulated_plasticity(reward)
    
    def _update_action_values(self, reward: float) -> None:
        """
        Update action values using temporal difference learning.
        
        Args:
            reward: Reward value
        """
        # Q-learning update: Q(s,a) <- Q(s,a) + α[r + γ max_a' Q(s',a') - Q(s,a)]
        prev_action_id = self.previous_action
        curr_action_id = self.current_action
        
        if prev_action_id is not None and prev_action_id < self.n_actions:
            # Current action value estimate
            prev_value = self.action_mapping[prev_action_id]['value']
            
            # Get max value of current state
            curr_max_value = 0.0
            if curr_action_id is not None and curr_action_id < self.n_actions:
                curr_max_value = self.action_mapping[curr_action_id]['value']
            
            # TD update
            td_error = reward + self.discount_factor * curr_max_value - prev_value
            new_value = prev_value + self.learning_rate * td_error
            
            # Update value
            self.action_mapping[prev_action_id]['value'] = new_value
    
    def _apply_dopamine_modulated_plasticity(self, reward: float) -> None:
        """
        Apply dopamine-modulated plasticity to striatal synapses.
        
        Args:
            reward: Reward value
        """
        # Implement reward-modulated STDP
        # Positive reward strengthens active D1 synapses and weakens active D2 synapses
        # Negative reward does the opposite
        
        # Modulate learning rate by reward amplitude
        effective_lr = self.learning_rate * abs(reward)
        
        # Apply to D1 synapses (direct pathway)
        for i in self.striatum_d1_indices:
            if self.firing_rates[i] > 0.5:  # Active D1 neuron
                for j in range(self.n_neurons):
                    if self.synaptic_weights[j, i] != 0:
                        if reward > 0:
                            # Strengthen input to active D1 neurons for positive reward
                            self.synaptic_weights[j, i] += effective_lr * self.firing_rates[j] * self.firing_rates[i]
                        else:
                            # Weaken input to active D1 neurons for negative reward
                            self.synaptic_weights[j, i] -= effective_lr * self.firing_rates[j] * self.firing_rates[i]
                        
                        # Apply bounds
                        if self.synaptic_weights[j, i] > 0:
                            self.synaptic_weights[j, i] = min(1.0, self.synaptic_weights[j, i])
                        else:
                            self.synaptic_weights[j, i] = max(-1.0, self.synaptic_weights[j, i])
        
        # Apply to D2 synapses (indirect pathway)
        for i in self.striatum_d2_indices:
            if self.firing_rates[i] > 0.5:  # Active D2 neuron
                for j in range(self.n_neurons):
                    if self.synaptic_weights[j, i] != 0:
                        if reward > 0:
                            # Weaken input to active D2 neurons for positive reward
                            self.synaptic_weights[j, i] -= effective_lr * self.firing_rates[j] * self.firing_rates[i]
                        else:
                            # Strengthen input to active D2 neurons for negative reward
                            self.synaptic_weights[j, i] += effective_lr * self.firing_rates[j] * self.firing_rates[i]
                        
                        # Apply bounds
                        if self.synaptic_weights[j, i] > 0:
                            self.synaptic_weights[j, i] = min(1.0, self.synaptic_weights[j, i])
                        else:
                            self.synaptic_weights[j, i] = max(-1.0, self.synaptic_weights[j, i])
    
    def _simulate_network_dynamics(self, duration: float) -> None:
        """
        Simulate basal ganglia dynamics for action selection.
        
        Args:
            duration: Simulation duration (ms)
        """
        # Simple simulation with fixed time step
        dt = 1.0  # 1 ms time step
        current_time = self.last_update_time if self.last_update_time > 0 else 0.0
        end_time = current_time + duration
        
        for t in np.arange(current_time, end_time, dt):
            # Update network state
            self._update_state(dt)
            
            # Process any spikes that occurred
            for i in range(self.n_neurons):
                if self.membrane_potentials[i] >= self.neurons[i].v_threshold:
                    # Record spike
                    self.spike_history.append((i, t))
                    
                    # Reset membrane potential
                    self.membrane_potentials[i] = self.neurons[i].v_reset
                    
                    # Update firing rate
                    self.firing_rates[i] += 1.0
                    
                    # Propagate spike to target neurons
                    for j in range(self.n_neurons):
                        if self.synaptic_weights[i, j] != 0:
                            self.membrane_potentials[j] += self.synaptic_weights[i, j]
        
        self.last_update_time = end_time
    
    def visualize_action_values(self, figsize=(10, 6)) -> None:
        """
        Visualize action values.
        
        Args:
            figsize: Figure size
        """
        plt.figure(figsize=figsize)
        
        # Extract action names and values
        action_ids = sorted(self.action_mapping.keys())
        action_names = [self.action_mapping[i]['name'] for i in action_ids]
        action_values = [self.action_mapping[i]['value'] for i in action_ids]
        
        # Plot action values
        plt.bar(action_names, action_values)
        plt.axhline(y=0, color='k', linestyle='-', alpha=0.3)
        plt.ylabel('Action Value')
        plt.title('Learned Action Values')
        
        # Add action counts as text
        for i, action_id in enumerate(action_ids):
            count = self.action_mapping[action_id]['count']
            plt.text(i, action_values[i] + 0.1 * np.sign(action_values[i]), 
                   f"n={count}", ha='center', va='center')
        
        plt.tight_layout()
        plt.show()
    
    def visualize_dopamine_history(self, figsize=(10, 6)) -> None:
        """
        Visualize dopamine level history.
        
        Args:
            figsize: Figure size
        """
        plt.figure(figsize=figsize)
        
        if not self.dopamine_history:
            plt.text(0.5, 0.5, "No dopamine history recorded", 
                   horizontalalignment='center', verticalalignment='center')
        else:
            # Extract times and dopamine levels
            times, levels = zip(*self.dopamine_history)
            
            # Convert to relative times
            times = np.array(times) - times[0]
            
            plt.plot(times, levels, 'b-')
            plt.axhline(y=self.dopamine_baseline, color='k', linestyle='--', 
                      label=f'Baseline ({self.dopamine_baseline})')
            
            plt.xlabel('Time (s)')
            plt.ylabel('Dopamine Level')
            plt.title('Dopamine Level History')
            plt.legend()
        
        plt.tight_layout()
        plt.show()


class Thalamus(BrainRegion):
    """
    Emulation of the thalamus.
    
    This class implements thalamic relay and processing, including
    sensory gating, attention modulation, and cortical communication.
    """
    
    def __init__(
        self,
        region_id: int,
        n_relay_neurons: int = 100,
        n_reticular_neurons: int = 50,
        n_interneurons: int = 30,
        n_channels: int = 5,
        plasticity_rules: Optional[List[SynapticPlasticityRule]] = None
    ):
        """
        Initialize thalamus.
        
        Args:
            region_id: Unique identifier for this region
            n_relay_neurons: Number of thalamic relay neurons
            n_reticular_neurons: Number of thalamic reticular neurons
            n_interneurons: Number of interneurons
            n_channels: Number of processing channels
            plasticity_rules: List of plasticity rules to apply
        """
        n_total = n_relay_neurons + n_reticular_neurons + n_interneurons
        
        neuron_params = {
            'tau_m': 15.0,        # Membrane time constant (ms)
            'v_rest': -65.0,      # Resting potential (mV)
            'v_threshold': -50.0, # Threshold potential (mV)
            'v_reset': -60.0,     # Reset potential (mV)
            'refractory_period': 2.0  # Refractory period (ms)
        }
        
        super().__init__(
            region_id=region_id,
            region_type=BrainRegionType.THALAMUS,
            n_neurons=n_total,
            neuron_params=neuron_params,
            topology="small-world",
            plasticity_rules=plasticity_rules
        )
        
        # Thalamus specific parameters
        self.n_relay_neurons = n_relay_neurons
        self.n_reticular_neurons = n_reticular_neurons
        self.n_interneurons = n_interneurons
        self.n_channels = n_channels
        
        # Neuron indices for different cell types
        self.relay_indices = list(range(0, n_relay_neurons))
        self.reticular_indices = list(range(n_relay_neurons, n_relay_neurons + n_reticular_neurons))
        self.interneuron_indices = list(range(n_relay_neurons + n_reticular_neurons, n_total))
        
        # Channel mappings
        self.channel_mapping = {}
        self._initialize_channels()
        
        # Thalamic state
        self.attention_level = 0.5  # Default attention level
        self.gating_state = {}  # Channel_id -> open (True) or closed (False)
        
        # Initialize thalamic circuitry
        self._initialize_thalamic_circuitry()
        
        logger.info(f"Initialized Thalamus with {n_total} neurons "
                  f"({n_relay_neurons} relay, {n_reticular_neurons} reticular, "
                  f"{n_interneurons} interneurons)")
    
    def _initialize_channels(self) -> None:
        """Initialize thalamic processing channels."""
        neurons_per_channel = self.n_relay_neurons // self.n_channels
        
        for channel_id in range(self.n_channels):
            start_idx = channel_id * neurons_per_channel
            end_idx = (channel_id + 1) * neurons_per_channel
            
            channel_neurons = [self.relay_indices[i % self.n_relay_neurons] for i in range(start_idx, end_idx)]
            
            self.channel_mapping[channel_id] = {
                'relay_neurons': channel_neurons,
                'name': f"Channel_{channel_id}",
                'type': "unspecified",
                'state': 0.0,
                'mode': 'relay'  # relay or burst
            }
            
            # Default all channels to open
            self.gating_state[channel_id] = True
    
    def _initialize_thalamic_circuitry(self) -> None:
        """Initialize thalamic circuitry."""
        # Connect reticular neurons to relay neurons (inhibitory)
        for i in self.reticular_indices:
            # Each reticular neuron inhibits a subset of relay neurons
            target_indices = np.random.choice(
                self.relay_indices, 
                size=max(1, int(0.2 * self.n_relay_neurons)),
                replace=False
            )
            
            for j in target_indices:
                # Inhibitory connection
                self.synaptic_weights[i, j] = np.random.uniform(-0.6, -0.2)
        
        # Connect relay neurons to reticular neurons (excitatory)
        for i in self.relay_indices:
            target_indices = np.random.choice(
                self.reticular_indices, 
                size=max(1, int(0.2 * self.n_reticular_neurons)),
                replace=False
            )
            
            for j in target_indices:
                # Excitatory connection
                self.synaptic_weights[i, j] = np.random.uniform(0.1, 0.4)
        
        # Connect interneurons to relay neurons (inhibitory)
        for i in self.interneuron_indices:
            target_indices = np.random.choice(
                self.relay_indices, 
                size=max(1, int(0.1 * self.n_relay_neurons)),
                replace=False
            )
            
            for j in target_indices:
                # Inhibitory connection
                self.synaptic_weights[i, j] = np.random.uniform(-0.4, -0.1)
    
    def process_event(self, event: Event) -> List[Event]:
        """
        Process event in thalamus.
        
        Args:
            event: Input event
            
        Returns:
            List of output events
        """
        # Special handling for thalamus-specific events
        if event.event_type == EventType.CHANGE and hasattr(event, 'payload') and event.payload:
            if 'set_attention' in event.payload:
                attention_level = event.payload['set_attention']
                self.set_attention(attention_level)
                
                return [Event(
                    priority=event.timestamp,
                    timestamp=event.timestamp,
                    source_id=self.processor_id,
                    event_type=EventType.CHANGE,
                    payload={'attention_set': attention_level}
                )]
                
            elif 'gate_channel' in event.payload:
                channel_id = event.payload['gate_channel'].get('channel_id')
                open_state = event.payload['gate_channel'].get('open', True)
                
                self.gate_channel(channel_id, open_state)
                
                return [Event(
                    priority=event.timestamp,
                    timestamp=event.timestamp,
                    source_id=self.processor_id,
                    event_type=EventType.CHANGE,
                    payload={'channel_gated': {'channel_id': channel_id, 'open': open_state}}
                )]
                
            elif 'set_channel_mode' in event.payload:
                channel_id = event.payload['set_channel_mode'].get('channel_id')
                mode = event.payload['set_channel_mode'].get('mode')
                
                self.set_channel_mode(channel_id, mode)
                
                return [Event(
                    priority=event.timestamp,
                    timestamp=event.timestamp,
                    source_id=self.processor_id,
                    event_type=EventType.CHANGE,
                    payload={'channel_mode_set': {'channel_id': channel_id, 'mode': mode}}
                )]
                
            elif 'relay_input' in event.payload:
                channel_id = event.payload['relay_input'].get('channel_id')
                data = event.payload['relay_input'].get('data')
                
                output_data = self.relay_input(channel_id, data, event.timestamp)
                
                return [Event(
                    priority=event.timestamp,
                    timestamp=event.timestamp,
                    source_id=self.processor_id,
                    event_type=EventType.CHANGE,
                    payload={'relay_output': {'channel_id': channel_id, 'data': output_data}}
                )]
        
        # Default to parent class implementation
        return super().process_event(event)
    
    def set_attention(self, level: float) -> None:
        """
        Set global attention level.
        
        Args:
            level: Attention level (0.0-1.0)
        """
        self.attention_level = max(0.0, min(1.0, level))
        
        # Update thalamic state based on attention
        if level > 0.7:
            # High attention: increase reticular inhibition to sharpen relay selectivity
            for i in self.reticular_indices:
                for j in self.relay_indices:
                    if self.synaptic_weights[i, j] < 0:  # Inhibitory connection
                        # Strengthen inhibition
                        self.synaptic_weights[i, j] *= 1.2
        elif level < 0.3:
            # Low attention: decrease reticular inhibition
            for i in self.reticular_indices:
                for j in self.relay_indices:
                    if self.synaptic_weights[i, j] < 0:  # Inhibitory connection
                        # Weaken inhibition
                        self.synaptic_weights[i, j] /= 1.2
    
    def gate_channel(self, channel_id: int, open: bool) -> None:
        """
        Gate a thalamic channel (open or close).
        
        Args:
            channel_id: Channel ID
            open: True to open channel, False to close
        """
        if channel_id in self.channel_mapping:
            self.gating_state[channel_id] = open
            
            # Modulate relay neurons for this channel
            channel_neurons = self.channel_mapping[channel_id]['relay_neurons']
            
            if open:
                # Increase excitability of relay neurons
                for i in channel_neurons:
                    # Reduce threshold
                    self.neurons[i].v_threshold = -55.0
            else:
                # Decrease excitability of relay neurons
                for i in channel_neurons:
                    # Increase threshold
                    self.neurons[i].v_threshold = -45.0
    
    def set_channel_mode(self, channel_id: int, mode: str) -> None:
        """
        Set channel operating mode (relay or burst).
        
        Args:
            channel_id: Channel ID
            mode: 'relay' or 'burst'
        """
        if channel_id in self.channel_mapping and mode in ['relay', 'burst']:
            self.channel_mapping[channel_id]['mode'] = mode
            
            channel_neurons = self.channel_mapping[channel_id]['relay_neurons']
            
            if mode == 'relay':
                # Tonic mode: high baseline, regular spiking
                for i in channel_neurons:
                    self.neurons[i].v_rest = -65.0
                    self.membrane_potentials[i] = -60.0
            else:  # burst mode
                # Burst mode: lower baseline, burstable
                for i in channel_neurons:
                    self.neurons[i].v_rest = -70.0
                    self.membrane_potentials[i] = -68.0
    
    def relay_input(self, channel_id: int, data: Any, timestamp: float) -> Any:
        """
        Relay input through a thalamic channel.
        
        Args:
            channel_id: Channel ID
            data: Input data
            timestamp: Current timestamp
            
        Returns:
            Processed output data
        """
        if channel_id not in self.channel_mapping:
            return None
        
        # Check if channel is gated
        if not self.gating_state.get(channel_id, True):
            return None  # Channel is closed
        
        # Process data through channel
        channel_info = self.channel_mapping[channel_id]
        channel_neurons = channel_info['relay_neurons']
        mode = channel_info['mode']
        
        # Encode data in relay neurons
        data_enc = self._encode_data(data)
        
        if len(data_enc) > 0:
            # Scale by number of neurons
            scaling = min(len(channel_neurons), len(data_enc))
            
            for i in range(scaling):
                neuron_idx = channel_neurons[i]
                # Add to membrane potential
                input_value = data_enc[i % len(data_enc)] * 10.0  # Scale input
                
                if mode == 'relay':
                    # Regular spiking: direct input
                    self.membrane_potentials[neuron_idx] += input_value
                else:  # burst mode
                    # Burst spiking: stronger but nonlinear response
                    if input_value > 5.0:  # Threshold for burst
                        self.membrane_potentials[neuron_idx] += input_value * 1.5
        
        # Run dynamics
        self._simulate_network_dynamics(20.0)  # 20 ms
        
        # Record channel state
        channel_activity = np.mean([self.firing_rates[i] for i in channel_neurons])
        self.channel_mapping[channel_id]['state'] = channel_activity
        
        # Decode output
        output_data = self._decode_output(channel_neurons)
        
        return output_data
    
    def _encode_data(self, data: Any) -> np.ndarray:
        """
        Encode data for thalamic processing.
        
        Args:
            data: Input data
            
        Returns:
            Encoded data as array
        """
        # Handle different data types
        if isinstance(data, (int, float)):
            # Single value: convert to array
            return np.array([data])
        elif isinstance(data, (list, tuple)):
            # List or tuple: convert to array
            return np.array(data)
        elif isinstance(data, np.ndarray):
            # Already an array
            return data
        elif isinstance(data, dict):
            # Dict: use values
            return np.array(list(data.values()))
        elif isinstance(data, str):
            # String: convert to ASCII values
            return np.array([ord(c) for c in data]) / 128.0
        else:
            # Unknown type: empty array
            return np.array([])
    
    def _decode_output(self, channel_neurons: List[int]) -> np.ndarray:
        """
        Decode output from thalamic relay neurons.
        
        Args:
            channel_neurons: List of relay neuron indices
            
        Returns:
            Decoded output data
        """
        # Extract firing rates from relay neurons
        output = np.array([self.firing_rates[i] for i in channel_neurons])
        
        # Apply attention modulation
        output *= (0.5 + self.attention_level)
        
        return output
    
    def _simulate_network_dynamics(self, duration: float) -> None:
        """
        Simulate thalamic network dynamics.
        
        Args:
            duration: Simulation duration (ms)
        """
        # Simple simulation with fixed time step
        dt = 1.0  # 1 ms time step
        current_time = self.last_update_time if self.last_update_time > 0 else 0.0
        end_time = current_time + duration
        
        for t in np.arange(current_time, end_time, dt):
            # Update network state
            self._update_state(dt)
            
            # Process any spikes that occurred
            for i in range(self.n_neurons):
                if self.membrane_potentials[i] >= self.neurons[i].v_threshold:
                    # Record spike
                    self.spike_history.append((i, t))
                    
                    # Reset membrane potential
                    self.membrane_potentials[i] = self.neurons[i].v_reset
                    
                    # Update firing rate
                    self.firing_rates[i] += 1.0
                    
                    # Propagate spike to target neurons
                    for j in range(self.n_neurons):
                        if self.synaptic_weights[i, j] != 0:
                            self.membrane_potentials[j] += self.synaptic_weights[i, j]
        
        self.last_update_time = end_time
    
    def visualize_channel_states(self, figsize=(12, 6)) -> None:
        """
        Visualize thalamic channel states.
        
        Args:
            figsize: Figure size
        """
        plt.figure(figsize=figsize)
        
        # Extract channel information
        channel_ids = sorted(self.channel_mapping.keys())
        channel_names = [self.channel_mapping[i]['name'] for i in channel_ids]
        channel_states = [self.channel_mapping[i]['state'] for i in channel_ids]
        channel_gates = [self.gating_state.get(i, True) for i in channel_ids]
        channel_modes = [self.channel_mapping[i]['mode'] for i in channel_ids]
        
        # Plot channel states
        bars = plt.bar(channel_names, channel_states)
        
        # Color bars based on gate state and mode
        for i, bar in enumerate(bars):
            if not channel_gates[i]:
                # Closed gate: gray
                bar.set_color('gray')
            elif channel_modes[i] == 'relay':
                # Relay mode: blue
                bar.set_color('blue')
            else:
                # Burst mode: red
                bar.set_color('red')
        
        plt.ylabel('Activity Level')
        plt.title(f'Thalamic Channel States (Attention: {self.attention_level:.2f})')
        
        # Add legend
        from matplotlib.patches import Patch
        legend_elements = [
            Patch(facecolor='gray', label='Closed Gate'),
            Patch(facecolor='blue', label='Relay Mode'),
            Patch(facecolor='red', label='Burst Mode')
        ]
        plt.legend(handles=legend_elements)
        
        plt.tight_layout()
        plt.show()


class Cerebellum(BrainRegion):
    """
    Emulation of the cerebellum.
    
    This class implements cerebellar circuits for motor learning,
    timing, and predictive control.
    """
    
    def __init__(
        self,
        region_id: int,
        n_granule_cells: int = 200,
        n_purkinje_cells: int = 20,
        n_dcn_cells: int = 10,  # Deep cerebellar nuclei
        n_io_cells: int = 10,   # Inferior olive
        n_inputs: int = 10,
        n_outputs: int = 5,
        plasticity_rules: Optional[List[SynapticPlasticityRule]] = None
    ):
        """
        Initialize cerebellum.
        
        Args:
            region_id: Unique identifier for this region
            n_granule_cells: Number of granule cells
            n_purkinje_cells: Number of Purkinje cells
            n_dcn_cells: Number of deep cerebellar nuclei cells
            n_io_cells: Number of inferior olive cells
            n_inputs: Number of input channels (mossy fibers)
            n_outputs: Number of output channels
            plasticity_rules: List of plasticity rules to apply
        """
        n_total = n_granule_cells + n_purkinje_cells + n_dcn_cells + n_io_cells
        
        neuron_params = {
            'tau_m': 10.0,        # Membrane time constant (ms)
            'v_rest': -70.0,      # Resting potential (mV)
            'v_threshold': -55.0, # Threshold potential (mV)
            'v_reset': -70.0,     # Reset potential (mV)
            'refractory_period': 1.5  # Refractory period (ms)
        }
        
        super().__init__(
            region_id=region_id,
            region_type=BrainRegionType.CEREBELLUM,
            n_neurons=n_total,
            neuron_params=neuron_params,
            topology="layered",
            plasticity_rules=plasticity_rules or [SynapticPlasticityRule.STDP]
        )
        
        # Cerebellum specific parameters
        self.n_granule_cells = n_granule_cells
        self.n_purkinje_cells = n_purkinje_cells
        self.n_dcn_cells = n_dcn_cells
        self.n_io_cells = n_io_cells
        self.n_inputs = n_inputs
        self.n_outputs = n_outputs
        
        # Neuron indices for different cell types
        self.granule_indices = list(range(0, n_granule_cells))
        self.purkinje_indices = list(range(n_granule_cells, n_granule_cells + n_purkinje_cells))
        self.dcn_indices = list(range(n_granule_cells + n_purkinje_cells, 
                                    n_granule_cells + n_purkinje_cells + n_dcn_cells))
        self.io_indices = list(range(n_granule_cells + n_purkinje_cells + n_dcn_cells, n_total))
        
        # Input and output mappings
        self.input_mapping = {}  # input_id -> properties
        self.output_mapping = {}  # output_id -> properties
        
        # Initialize cerebellum circuitry
        self._initialize_cerebellum_circuitry()
        
        # Learning state
        self.error_trace = np.zeros(n_outputs)
        self.error_history = []
        self.learning_rate = 0.01
        
        # Initialize timing mechanisms
        self.timing_state = {}
        for i in range(n_outputs):
            self.timing_state[i] = {
                'delay': 0.0,
                'precision': 0.0,
                'learned_intervals': []
            }
        
        logger.info(f"Initialized Cerebellum with {n_total} neurons "
                  f"({n_granule_cells} granule, {n_purkinje_cells} Purkinje, "
                  f"{n_dcn_cells} DCN, {n_io_cells} IO)")
    
    def _initialize_cerebellum_circuitry(self) -> None:
        """Initialize cerebellar circuitry."""
        # Granule cell inputs
        # Each granule cell receives input from a small subset of inputs (mossy fibers)
        inputs_per_granule = max(1, min(4, self.n_inputs))
        
        for i in self.granule_indices:
            # Select random inputs
            input_indices = np.random.choice(self.n_inputs, size=inputs_per_granule, replace=False)
            
            # Store input mapping
            self.input_mapping[i] = {'indices': input_indices, 'weights': np.ones(inputs_per_granule)}
        
        # Parallel fiber connections: Granule cells to Purkinje cells
        # This is a sparse but extensive connectivity
        for i in self.granule_indices:
            # Connect to a subset of Purkinje cells
            n_targets = max(1, int(0.1 * self.n_purkinje_cells))
            target_indices = np.random.choice(self.purkinje_indices, size=n_targets, replace=False)
            
            for j in target_indices:
                # Excitatory connection
                self.synaptic_weights[i, j] = np.random.uniform(0.05, 0.15)
        
        # Purkinje cell to DCN connections
        # Each Purkinje cell inhibits a subset of DCN cells
        purkinje_per_dcn = max(1, self.n_purkinje_cells // self.n_dcn_cells)
        
        for i, i_dcn in enumerate(self.dcn_indices):
            # Determine which Purkinje cells inhibit this DCN cell
            start_idx = (i * purkinje_per_dcn) % self.n_purkinje_cells
            purkinje_indices = [(start_idx + j) % self.n_purkinje_cells for j in range(purkinje_per_dcn)]
            
            # Convert to actual neuron indices
            purkinje_indices = [self.purkinje_indices[idx] for idx in purkinje_indices]
            
            for j in purkinje_indices:
                # Inhibitory connection
                self.synaptic_weights[j, i_dcn] = np.random.uniform(-0.8, -0.2)
        
        # Climbing fiber connections: IO to Purkinje cells
        # One-to-one connectivity
        io_per_purkinje = max(1, min(self.n_io_cells, self.n_purkinje_cells))
        
        for i in range(min(self.n_purkinje_cells, self.n_io_cells)):
            # Connect IO cell to Purkinje cell
            io_idx = self.io_indices[i % self.n_io_cells]
            purkinje_idx = self.purkinje_indices[i % self.n_purkinje_cells]
            
            # Strong excitatory connection (triggers complex spike)
            self.synaptic_weights[io_idx, purkinje_idx] = 2.0
        
        # Mossy fiber connections: Inputs to DCN
        # Collaterals that bypass the cerebellar cortex
        for i in range(self.n_inputs):
            for j in self.dcn_indices:
                # Weak excitatory connection
                self.synaptic_weights[i, j] = np.random.uniform(0.05, 0.15)
        
        # Output mapping: DCN to outputs
        dcn_per_output = max(1, self.n_dcn_cells // self.n_outputs)
        
        for i in range(self.n_outputs):
            # Determine which DCN cells drive this output
            start_idx = (i * dcn_per_output) % self.n_dcn_cells
            dcn_indices = [(start_idx + j) % self.n_dcn_cells for j in range(dcn_per_output)]
            
            # Convert to actual neuron indices
            dcn_indices = [self.dcn_indices[idx] for idx in dcn_indices]
            
            # Store output mapping
            self.output_mapping[i] = {'indices': dcn_indices, 'gain': 1.0}
    
    def process_event(self, event: Event) -> List[Event]:
        """
        Process event in cerebellum.
        
        Args:
            event: Input event
            
        Returns:
            List of output events
        """
        # Special handling for cerebellum-specific events
        if event.event_type == EventType.CHANGE and hasattr(event, 'payload') and event.payload:
            if 'input_stimulus' in event.payload:
                input_id = event.payload['input_stimulus'].get('input_id')
                value = event.payload['input_stimulus'].get('value')
                
                if input_id is not None and 0 <= input_id < self.n_inputs:
                    # Process input stimulus
                    output = self.process_input(input_id, value, event.timestamp)
                    
                    return [Event(
                        priority=event.timestamp,
                        timestamp=event.timestamp,
                        source_id=self.processor_id,
                        event_type=EventType.CHANGE,
                        payload={'cerebellum_output': {'values': output}}
                    )]
                    
            elif 'error_signal' in event.payload:
                errors = event.payload['error_signal']
                
                if isinstance(errors, (list, np.ndarray)) and len(errors) == self.n_outputs:
                    # Process error signal
                    self.process_error(errors, event.timestamp)
                    
                    return [Event(
                        priority=event.timestamp,
                        timestamp=event.timestamp,
                        source_id=self.processor_id,
                        event_type=EventType.CHANGE,
                        payload={'error_processed': True}
                    )]
                    
            elif 'timing_learn' in event.payload:
                output_id = event.payload['timing_learn'].get('output_id', 0)
                interval = event.payload['timing_learn'].get('interval', 100.0)
                
                # Learn timing for this output
                self.learn_timing(output_id, interval)
                
                return [Event(
                    priority=event.timestamp,
                    timestamp=event.timestamp,
                    source_id=self.processor_id,
                    event_type=EventType.CHANGE,
                    payload={'timing_learned': {'output_id': output_id, 'interval': interval}}
                )]
        
        # Default to parent class implementation
        return super().process_event(event)
    
    def process_input(self, input_id: int, value: float, timestamp: float) -> List[float]:
        """
        Process an input stimulus.
        
        Args:
            input_id: Input ID
            value: Input value
            timestamp: Current timestamp
            
        Returns:
            List of output values
        """
        # Distribute input to relevant granule cells
        for i in self.granule_indices:
            if input_id in self.input_mapping.get(i, {}).get('indices', []):
                # Find index of this input in the mapping
                idx = list(self.input_mapping[i]['indices']).index(input_id)
                
                # Apply weight
                weighted_value = value * self.input_mapping[i]['weights'][idx]
                
                # Add to membrane potential
                # Add to membrane potential
                self.membrane_potentials[i] += 10.0 * weighted_value
        
        # Also provide direct input to DCN cells (mossy fiber collaterals)
        for i in self.dcn_indices:
            # Weak direct input
            self.membrane_potentials[i] += 2.0 * value
        
        # Run network dynamics
        self._simulate_network_dynamics(50.0)  # 50 ms
        
        # Compute outputs based on DCN cell activity
        outputs = np.zeros(self.n_outputs)
        
        for output_id in range(self.n_outputs):
            if output_id in self.output_mapping:
                # Get DCN cell indices for this output
                dcn_indices = self.output_mapping[output_id]['indices']
                
                # Compute average firing rate
                avg_rate = np.mean([self.firing_rates[i] for i in dcn_indices])
                
                # Apply gain
                outputs[output_id] = avg_rate * self.output_mapping[output_id]['gain']
                
                # Apply timing if learned
                if self.timing_state[output_id]['learned_intervals']:
                    # Modulate output based on timing precision
                    precision = self.timing_state[output_id]['precision']
                    outputs[output_id] *= (0.5 + 0.5 * precision)
        
        return outputs.tolist()
    
    def process_error(self, errors: List[float], timestamp: float) -> None:
        """
        Process error signals for learning.
        
        Args:
            errors: List of error values for each output
            timestamp: Current timestamp
        """
        # Update error trace
        self.error_trace = np.array(errors)
        self.error_history.append((timestamp, errors))
        
        # Simulate climbing fiber activity based on error
        # Climbing fibers encode error signals
        for i, i_io in enumerate(self.io_indices):
            # Map IO cell to corresponding output
            output_id = i % self.n_outputs
            
            # Get error for this output
            if output_id < len(errors):
                error = errors[output_id]
                
                # IO cells respond to positive errors (actual > predicted)
                if error > 0:
                    # Generate climbing fiber signal
                    self.membrane_potentials[i_io] += 30.0 * error
        
        # Run network dynamics to propagate climbing fiber signals
        self._simulate_network_dynamics(20.0)  # 20 ms
        
        # Update synaptic weights based on error-driven plasticity
        self._update_weights_from_error()
    
    def _update_weights_from_error(self) -> None:
        """Update synaptic weights based on error signals."""
        # Cerebellar learning: LTD at parallel fiber-Purkinje synapses 
        # when climbing fibers are active
        
        # Check which Purkinje cells received climbing fiber input (complex spikes)
        purkinje_complex_spikes = {}
        for i in self.purkinje_indices:
            # Check recent spike history for complex spikes
            complex_spike = False
            for neuron_idx, spike_time in reversed(self.spike_history[-50:]):  # Check last 50 spikes
                if neuron_idx == i:
                    # Purkinje cell spiked recently
                    # Check if it received climbing fiber input just before
                    for io_idx in self.io_indices:
                        for io_spike_idx, io_spike_time in reversed(self.spike_history[-100:]):
                            if (io_spike_idx == io_idx and 
                                self.synaptic_weights[io_idx, i] > 0 and  # IO connects to this Purkinje
                                io_spike_time < spike_time and
                                spike_time - io_spike_time < 20.0):  # Within 20ms
                                complex_spike = True
                                break
                        if complex_spike:
                            break
                if complex_spike:
                    break
            
            purkinje_complex_spikes[i] = complex_spike
        
        # Update parallel fiber-Purkinje synapses
        for i in self.granule_indices:
            for j in self.purkinje_indices:
                if self.synaptic_weights[i, j] > 0:  # Parallel fiber synapse exists
                    if purkinje_complex_spikes[j]:
                        # LTD: Granule cell active before Purkinje complex spike
                        granule_active = False
                        for neuron_idx, spike_time in reversed(self.spike_history[-200:]):
                            if neuron_idx == i:
                                granule_active = True
                                break
                        
                        if granule_active:
                            # Decrease weight (LTD)
                            self.synaptic_weights[i, j] *= (1.0 - self.learning_rate)
                    else:
                        # LTP: Granule cell active but no complex spike
                        granule_active = False
                        for neuron_idx, spike_time in reversed(self.spike_history[-100:]):
                            if neuron_idx == i:
                                granule_active = True
                                break
                        
                        if granule_active:
                            # Increase weight (LTP)
                            self.synaptic_weights[i, j] *= (1.0 + 0.1 * self.learning_rate)
                            # Apply upper bound
                            self.synaptic_weights[i, j] = min(1.0, self.synaptic_weights[i, j])
    
    def learn_timing(self, output_id: int, interval: float) -> None:
        """
        Learn timing interval for a specific output.
        
        Args:
            output_id: Output ID
            interval: Timing interval (ms)
        """
        if 0 <= output_id < self.n_outputs:
            # Store the interval
            if interval not in self.timing_state[output_id]['learned_intervals']:
                self.timing_state[output_id]['learned_intervals'].append(interval)
            
            # Update timing parameters
            current_intervals = self.timing_state[output_id]['learned_intervals']
            
            # Set delay to minimum interval
            self.timing_state[output_id]['delay'] = min(current_intervals)
            
            # Calculate precision based on consistency of intervals
            if len(current_intervals) > 1:
                std_dev = np.std(current_intervals)
                mean_interval = np.mean(current_intervals)
                cv = std_dev / mean_interval  # Coefficient of variation
                
                # Precision is inversely related to coefficient of variation
                precision = 1.0 / (1.0 + 5.0 * cv)
                self.timing_state[output_id]['precision'] = precision
            else:
                # Only one interval, moderate precision
                self.timing_state[output_id]['precision'] = 0.5
            
            # Create/update specific timing circuitry for this interval
            self._create_timing_circuit(output_id, interval)
    
    def _create_timing_circuit(self, output_id: int, interval: float) -> None:
        """
        Create a timing circuit for a specific interval.
        
        Args:
            output_id: Output ID
            interval: Timing interval (ms)
        """
        # Select a subset of granule cells to encode this timing interval
        n_timing_cells = max(5, int(0.05 * self.n_granule_cells))
        
        # Generate a reproducible pattern of granule cells for this interval
        np.random.seed(int(interval * 100) % 2**32)
        timing_granule_indices = np.random.choice(self.granule_indices, size=n_timing_cells, replace=False)
        np.random.seed(None)  # Reset seed
        
        # Get relevant Purkinje and DCN cells for this output
        relevant_purkinje = []
        for i in self.purkinje_indices:
            for dcn_idx in self.output_mapping[output_id]['indices']:
                if self.synaptic_weights[i, dcn_idx] < 0:  # Purkinje inhibits this DCN
                    relevant_purkinje.append(i)
                    break
        
        relevant_dcn = self.output_mapping[output_id]['indices']
        
        # Create and strengthen connections for timing
        # 1. Initial excitation of timing granule cells
        for i in timing_granule_indices:
            # Increase excitability of these granule cells
            self.neurons[i].v_threshold -= 2.0  # Slightly lower threshold
        
        # 2. Create delayed inhibition to create a timed response
        for i, i_granule in enumerate(timing_granule_indices):
            # Scale delay proportionally to desired interval
            delay_factor = i / len(timing_granule_indices)
            ideal_delay = interval * delay_factor
            
            # To create the delay, we use multiple synaptic connections
            # Through the granule -> Purkinje -> DCN pathway
            
            # Find or create an appropriate Purkinje cell connection
            target_purkinje = np.random.choice(relevant_purkinje)
            
            # Strengthen this connection
            if self.synaptic_weights[i_granule, target_purkinje] == 0:
                # Create new connection
                self.synaptic_weights[i_granule, target_purkinje] = 0.3
            else:
                # Strengthen existing connection
                self.synaptic_weights[i_granule, target_purkinje] *= 1.2
                # Apply upper bound
                self.synaptic_weights[i_granule, target_purkinje] = min(1.0, self.synaptic_weights[i_granule, target_purkinje])
    
    def _simulate_network_dynamics(self, duration: float) -> None:
        """
        Simulate cerebellar network dynamics.
        
        Args:
            duration: Simulation duration (ms)
        """
        # Simple simulation with fixed time step
        dt = 1.0  # 1 ms time step
        current_time = self.last_update_time if self.last_update_time > 0 else 0.0
        end_time = current_time + duration
        
        for t in np.arange(current_time, end_time, dt):
            # Update network state
            self._update_state(dt)
            
            # Process any spikes that occurred
            for i in range(self.n_neurons):
                if self.membrane_potentials[i] >= self.neurons[i].v_threshold:
                    # Record spike
                    self.spike_history.append((i, t))
                    
                    # Reset membrane potential
                    self.membrane_potentials[i] = self.neurons[i].v_reset
                    
                    # Update firing rate
                    self.firing_rates[i] += 1.0
                    
                    # Propagate spike to target neurons
                    for j in range(self.n_neurons):
                        if self.synaptic_weights[i, j] != 0:
                            self.membrane_potentials[j] += self.synaptic_weights[i, j]
        
        self.last_update_time = end_time
        
        # Apply firing rate decay
        decay_factor = np.exp(-duration / 100.0)  # Time constant of 100 ms
        self.firing_rates *= decay_factor
    
    def visualize_cerebellar_activity(self, figsize=(12, 8)) -> None:
        """
        Visualize cerebellar activity.
        
        Args:
            figsize: Figure size
        """
        plt.figure(figsize=figsize)
        
        # Create subplots for different cell types
        plt.subplot(2, 2, 1)
        self._plot_cell_activity(self.granule_indices, 'Granule Cells')
        
        plt.subplot(2, 2, 2)
        self._plot_cell_activity(self.purkinje_indices, 'Purkinje Cells')
        
        plt.subplot(2, 2, 3)
        self._plot_cell_activity(self.dcn_indices, 'Deep Cerebellar Nuclei')
        
        plt.subplot(2, 2, 4)
        self._plot_cell_activity(self.io_indices, 'Inferior Olive')
        
        plt.tight_layout()
        plt.show()
    
    def _plot_cell_activity(self, indices, title):
        """Helper function to plot cell activity."""
        # Calculate average firing rate
        avg_rate = np.mean([self.firing_rates[i] for i in indices])
        
        # Plot individual cell firing rates
        plt.bar(range(len(indices)), [self.firing_rates[i] for i in indices])
        
        plt.axhline(y=avg_rate, color='r', linestyle='--', 
                  label=f'Avg: {avg_rate:.2f}')
        
        plt.title(title)
        plt.xlabel('Cell Index')
        plt.ylabel('Firing Rate')
        plt.legend()
    
    def visualize_learning_progress(self, figsize=(10, 6)) -> None:
        """
        Visualize cerebellar learning progress.
        
        Args:
            figsize: Figure size
        """
        plt.figure(figsize=figsize)
        
        if not self.error_history:
            plt.text(0.5, 0.5, "No learning data recorded", 
                   horizontalalignment='center', verticalalignment='center')
        else:
            # Extract times and errors
            times, errors = zip(*self.error_history)
            
            # Convert to relative times
            times = np.array(times) - times[0]
            
            # Compute average error per time point
            avg_errors = [np.mean(np.abs(e)) for e in errors]
            
            # Plot average error
            plt.plot(times, avg_errors, 'b-', label='Avg Absolute Error')
            
            # Plot individual output errors
            for i in range(min(self.n_outputs, len(errors[0]))):
                output_errors = [e[i] for e in errors]
                plt.plot(times, output_errors, 'o--', alpha=0.5, 
                       label=f'Output {i} Error')
            
            plt.xlabel('Time (s)')
            plt.ylabel('Error')
            plt.title('Cerebellar Learning Progress')
            plt.legend()
        
        plt.tight_layout()
        plt.show()
    
    def visualize_timing_properties(self, figsize=(10, 6)) -> None:
        """
        Visualize timing properties.
        
        Args:
            figsize: Figure size
        """
        plt.figure(figsize=figsize)
        
        # Extract timing information
        output_ids = range(self.n_outputs)
        delays = [self.timing_state[i]['delay'] for i in output_ids]
        precisions = [self.timing_state[i]['precision'] for i in output_ids]
        
        # Plot timing properties
        plt.subplot(1, 2, 1)
        plt.bar(output_ids, delays)
        plt.title('Timing Delays')
        plt.xlabel('Output ID')
        plt.ylabel('Delay (ms)')
        
        plt.subplot(1, 2, 2)
        plt.bar(output_ids, precisions)
        plt.title('Timing Precision')
        plt.xlabel('Output ID')
        plt.ylabel('Precision (0-1)')
        plt.ylim(0, 1.05)
        
        plt.tight_layout()
        plt.show()


class BrainRegionRegistry:
    """
    Registry for managing brain region implementations.
    
    This class maintains a registry of available brain region implementations,
    facilitating the creation and management of different brain regions.
    """
    
    def __init__(self):
        """Initialize brain region registry."""
        self.registered_regions = {}
        self.instances = {}
        self.next_region_id = 0
        
        # Register built-in region types
        self._register_built_in_regions()
        
    def _register_built_in_regions(self) -> None:
        """Register built-in brain region implementations."""
        # Register built-in region classes
        self.register_region_type(BrainRegionType.VISUAL_CORTEX, VisualCortex)
        self.register_region_type(BrainRegionType.HIPPOCAMPUS, Hippocampus)
        self.register_region_type(BrainRegionType.PREFRONTAL_CORTEX, PrefrontalCortex)
        self.register_region_type(BrainRegionType.BASAL_GANGLIA, BasalGanglia)
        self.register_region_type(BrainRegionType.THALAMUS, Thalamus)
        self.register_region_type(BrainRegionType.CEREBELLUM, Cerebellum)
        self.register_region_type(BrainRegionType.CUSTOM, BrainRegion)
    
    def register_region_type(self, region_type: BrainRegionType, 
                           region_class: Type[BrainRegion]) -> None:
        """
        Register a new brain region implementation.
        
        Args:
            region_type: Type of brain region
            region_class: Brain region class implementation
        """
        self.registered_regions[region_type] = region_class
        logger.info(f"Registered brain region implementation for {region_type.name}")
    
    def create_region(self, region_type: BrainRegionType, **kwargs) -> BrainRegion:
        """
        Create a new brain region instance.
        
        Args:
            region_type: Type of brain region to create
            **kwargs: Additional arguments for the region constructor
            
        Returns:
            New brain region instance
            
        Raises:
            ValueError: If region type is not registered
        """
        if region_type not in self.registered_regions:
            raise ValueError(f"Brain region type {region_type} not registered")
        
        # Assign a unique region ID if not provided
        if 'region_id' not in kwargs:
            kwargs['region_id'] = self.next_region_id
            self.next_region_id += 1
        
        # Create region instance
        region_class = self.registered_regions[region_type]
        region = region_class(region_type=region_type, **kwargs)
        
        # Store instance
        self.instances[region.processor_id] = region
        
        logger.info(f"Created {region_type.name} instance with ID {region.processor_id}")
        return region
    
    def get_region(self, region_id: int) -> Optional[BrainRegion]:
        """
        Get a brain region instance by ID.
        
        Args:
            region_id: Brain region ID
            
        Returns:
            Brain region instance or None if not found
        """
        return self.instances.get(region_id)
    
    def get_regions_by_type(self, region_type: BrainRegionType) -> List[BrainRegion]:
        """
        Get all brain region instances of a specific type.
        
        Args:
            region_type: Brain region type
            
        Returns:
            List of brain region instances
        """
        return [region for region in self.instances.values() 
              if region.region_type == region_type]
    
    def remove_region(self, region_id: int) -> bool:
        """
        Remove a brain region instance.
        
        Args:
            region_id: Brain region ID
            
        Returns:
            True if region was removed, False if not found
        """
        if region_id in self.instances:
            del self.instances[region_id]
            logger.info(f"Removed brain region with ID {region_id}")
            return True
        
        return False
    
    def list_regions(self) -> Dict[int, Tuple[BrainRegionType, str]]:
        """
        List all registered brain region instances.
        
        Returns:
            Dictionary mapping region IDs to (type, name) tuples
        """
        return {region_id: (region.region_type, region.region_type.name) 
              for region_id, region in self.instances.items()}
    
    def list_available_types(self) -> List[Tuple[BrainRegionType, Type[BrainRegion]]]:
        """
        List all registered brain region types.
        
        Returns:
            List of (region_type, region_class) tuples
        """
        return list(self.registered_regions.items())
    
    def connect_regions(self, source_id: int, target_id: int, 
                      connection_type: str = "feedforward", 
                      strength: float = 1.0,
                      delay: float = 1.0) -> bool:
        """
        Connect two brain regions.
        
        Args:
            source_id: Source region ID
            target_id: Target region ID
            connection_type: Type of connection
            strength: Connection strength
            delay: Connection delay (ms)
            
        Returns:
            True if connection was created, False if error
        """
        source_region = self.get_region(source_id)
        target_region = self.get_region(target_id)
        
        if source_region is None or target_region is None:
            return False
        
        # Create event routing between regions
        # This is a simple implementation - in a full system, this would be
        # handled by a more sophisticated event routing system
        
        logger.info(f"Connected region {source_id} to region {target_id} "
                  f"({connection_type}, strength={strength}, delay={delay})")
        
        return True


# Initialize brain region registry as a singleton
brain_region_registry = BrainRegionRegistry()


def initialize_brain_regions(config=None) -> Dict[str, Any]:
    """
    Initialize brain region emulation with the given configuration.
    
    Args:
        config: Optional configuration dictionary
        
    Returns:
        Dictionary with initialization status
    """
    status = {}
    
    try:
        logger.info("Initializing Brain Region Emulation")
        
        # Initialize registry if needed
        global brain_region_registry
        if brain_region_registry is None:
            brain_region_registry = BrainRegionRegistry()
            status['registry_initialized'] = True
        
        # Create default regions if specified in config
        if config and 'default_regions' in config:
            default_regions = config['default_regions']
            created_regions = []
            
            for region_config in default_regions:
                region_type_name = region_config.get('type')
                
                try:
                    region_type = BrainRegionType[region_type_name]
                    region_kwargs = region_config.get('params', {})
                    
                    region = brain_region_registry.create_region(
                        region_type=region_type,
                        **region_kwargs
                    )
                    
                    created_regions.append((region.processor_id, region_type.name))
                except (KeyError, ValueError) as e:
                    logger.warning(f"Failed to create region of type {region_type_name}: {str(e)}")
            
            status['created_regions'] = created_regions
        
        # Create default connections if specified
        if config and 'default_connections' in config:
            default_connections = config['default_connections']
            created_connections = []
            
            for conn_config in default_connections:
                source_id = conn_config.get('source')
                target_id = conn_config.get('target')
                conn_type = conn_config.get('type', 'feedforward')
                strength = conn_config.get('strength', 1.0)
                delay = conn_config.get('delay', 1.0)
                
                success = brain_region_registry.connect_regions(
                    source_id=source_id,
                    target_id=target_id,
                    connection_type=conn_type,
                    strength=strength,
                    delay=delay
                )
                
                if success:
                    created_connections.append((source_id, target_id))
            
            status['created_connections'] = created_connections
        
        logger.info("Brain Region Emulation initialization complete")
        status['overall'] = 'success'
        
    except Exception as e:
        logger.error(f"Error initializing Brain Region Emulation: {str(e)}")
        status['overall'] = 'failed'
        status['error'] = str(e)
    
    return status