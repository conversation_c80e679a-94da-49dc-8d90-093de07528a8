#!/usr/bin/env python3
"""
ULTRA Procedural Knowledge Base
==============================

Procedural knowledge management system for the ULTRA framework.
This module provides procedural knowledge storage and execution capabilities.

Author: ULTRA Development Team
License: MIT
Version: 1.0.0
"""

import logging
import time
import uuid
from typing import Dict, List, Any, Optional, Callable, Tuple
from dataclasses import dataclass, field
from enum import Enum, auto
import inspect

# Set up logging
logger = logging.getLogger(__name__)

class ProcedureStatus(Enum):
    """Status of procedure execution."""
    PENDING = auto()
    RUNNING = auto()
    COMPLETED = auto()
    FAILED = auto()
    CANCELLED = auto()

@dataclass
class ProcedureStep:
    """Represents a single step in a procedure."""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    action: Optional[Callable] = None
    parameters: Dict[str, Any] = field(default_factory=dict)
    preconditions: List[str] = field(default_factory=list)
    postconditions: List[str] = field(default_factory=list)
    timeout: Optional[float] = None
    retry_count: int = 0
    max_retries: int = 3

@dataclass
class Procedure:
    """Represents a procedural knowledge entry."""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    steps: List[ProcedureStep] = field(default_factory=list)
    context: Dict[str, Any] = field(default_factory=dict)
    status: ProcedureStatus = ProcedureStatus.PENDING
    created_at: float = field(default_factory=time.time)
    updated_at: float = field(default_factory=time.time)
    execution_count: int = 0
    success_count: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'steps': [step.__dict__ for step in self.steps],
            'context': self.context,
            'status': self.status.name,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'execution_count': self.execution_count,
            'success_count': self.success_count
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Procedure':
        """Create from dictionary."""
        steps = [ProcedureStep(**step_data) for step_data in data.get('steps', [])]
        data['steps'] = steps
        if 'status' in data:
            data['status'] = ProcedureStatus[data['status']]
        return cls(**data)


class ProceduralKnowledgeBase:
    """
    Procedural knowledge base for storing and executing procedures.
    
    This class manages procedural knowledge with execution tracking and
    context-aware procedure selection.
    """
    
    def __init__(self):
        """Initialize procedural knowledge base."""
        self.procedures: Dict[str, Procedure] = {}
        self.name_index: Dict[str, str] = {}  # name -> procedure_id
        self.context_index: Dict[str, List[str]] = {}  # context_key -> [procedure_ids]
        self.execution_history: List[Tuple[str, float, ProcedureStatus]] = []
        
        logger.info("ProceduralKnowledgeBase initialized")
    
    def add_procedure(self, name: str, description: str = "",
                     steps: Optional[List[ProcedureStep]] = None,
                     context: Optional[Dict[str, Any]] = None) -> str:
        """
        Add a procedure to the knowledge base.
        
        Args:
            name: Procedure name
            description: Procedure description
            steps: List of procedure steps
            context: Contextual information
            
        Returns:
            Procedure ID
        """
        procedure = Procedure(
            name=name,
            description=description,
            steps=steps or [],
            context=context or {}
        )
        
        # Store procedure
        self.procedures[procedure.id] = procedure
        self.name_index[name.lower()] = procedure.id
        
        # Update context index
        for key, value in procedure.context.items():
            context_key = f"{key}:{value}"
            if context_key not in self.context_index:
                self.context_index[context_key] = []
            self.context_index[context_key].append(procedure.id)
        
        logger.debug(f"Added procedure '{name}' with ID {procedure.id}")
        return procedure.id
    
    def get_procedure(self, procedure_id: str) -> Optional[Procedure]:
        """
        Get a procedure by ID.
        
        Args:
            procedure_id: Procedure identifier
            
        Returns:
            Procedure if found, None otherwise
        """
        return self.procedures.get(procedure_id)
    
    def get_procedure_by_name(self, name: str) -> Optional[Procedure]:
        """
        Get a procedure by name.
        
        Args:
            name: Procedure name
            
        Returns:
            Procedure if found, None otherwise
        """
        procedure_id = self.name_index.get(name.lower())
        if procedure_id:
            return self.procedures.get(procedure_id)
        return None
    
    def search_procedures(self, query: Optional[str] = None,
                         context_filter: Optional[Dict[str, Any]] = None,
                         limit: int = 10) -> List[Procedure]:
        """
        Search procedures based on various criteria.
        
        Args:
            query: Search query
            context_filter: Context filter
            limit: Maximum number of results
            
        Returns:
            List of matching procedures
        """
        candidates = list(self.procedures.values())
        
        # Apply context filter
        if context_filter:
            filtered_candidates = []
            for procedure in candidates:
                match = True
                for key, value in context_filter.items():
                    if key not in procedure.context or procedure.context[key] != value:
                        match = False
                        break
                if match:
                    filtered_candidates.append(procedure)
            candidates = filtered_candidates
        
        # Apply query filter
        if query:
            query_lower = query.lower()
            filtered_candidates = []
            for procedure in candidates:
                if (query_lower in procedure.name.lower() or 
                    query_lower in procedure.description.lower()):
                    filtered_candidates.append(procedure)
            candidates = filtered_candidates
        
        # Sort by success rate and execution count
        candidates.sort(key=lambda p: (
            p.success_count / max(p.execution_count, 1),
            p.execution_count
        ), reverse=True)
        
        return candidates[:limit]
    
    def execute_procedure(self, procedure_id: str, 
                         execution_context: Optional[Dict[str, Any]] = None) -> bool:
        """
        Execute a procedure.
        
        Args:
            procedure_id: Procedure identifier
            execution_context: Context for execution
            
        Returns:
            True if successful, False otherwise
        """
        procedure = self.get_procedure(procedure_id)
        if not procedure:
            logger.error(f"Procedure {procedure_id} not found")
            return False
        
        procedure.status = ProcedureStatus.RUNNING
        procedure.execution_count += 1
        start_time = time.time()
        
        try:
            # Execute each step
            for step in procedure.steps:
                if not self._execute_step(step, execution_context):
                    procedure.status = ProcedureStatus.FAILED
                    self.execution_history.append((procedure_id, start_time, ProcedureStatus.FAILED))
                    return False
            
            # Success
            procedure.status = ProcedureStatus.COMPLETED
            procedure.success_count += 1
            procedure.updated_at = time.time()
            self.execution_history.append((procedure_id, start_time, ProcedureStatus.COMPLETED))
            
            logger.debug(f"Successfully executed procedure '{procedure.name}'")
            return True
            
        except Exception as e:
            logger.error(f"Error executing procedure '{procedure.name}': {e}")
            procedure.status = ProcedureStatus.FAILED
            self.execution_history.append((procedure_id, start_time, ProcedureStatus.FAILED))
            return False
    
    def _execute_step(self, step: ProcedureStep, 
                     execution_context: Optional[Dict[str, Any]] = None) -> bool:
        """
        Execute a single procedure step.
        
        Args:
            step: Procedure step to execute
            execution_context: Context for execution
            
        Returns:
            True if successful, False otherwise
        """
        if not step.action:
            logger.warning(f"Step '{step.name}' has no action defined")
            return True  # Consider no-op as success
        
        try:
            # Prepare parameters
            params = step.parameters.copy()
            if execution_context:
                params.update(execution_context)
            
            # Execute action
            if callable(step.action):
                # Get function signature to pass appropriate parameters
                sig = inspect.signature(step.action)
                filtered_params = {k: v for k, v in params.items() if k in sig.parameters}
                result = step.action(**filtered_params)
                
                # Consider any non-False result as success
                return result is not False
            else:
                logger.warning(f"Step '{step.name}' action is not callable")
                return False
                
        except Exception as e:
            logger.error(f"Error executing step '{step.name}': {e}")
            step.retry_count += 1
            
            # Retry if within limit
            if step.retry_count <= step.max_retries:
                logger.info(f"Retrying step '{step.name}' (attempt {step.retry_count})")
                return self._execute_step(step, execution_context)
            
            return False
    
    def get_execution_statistics(self) -> Dict[str, Any]:
        """
        Get execution statistics.
        
        Returns:
            Statistics dictionary
        """
        if not self.procedures:
            return {
                'total_procedures': 0,
                'total_executions': 0,
                'success_rate': 0.0,
                'most_executed': None
            }
        
        total_executions = sum(p.execution_count for p in self.procedures.values())
        total_successes = sum(p.success_count for p in self.procedures.values())
        
        most_executed = max(self.procedures.values(), key=lambda p: p.execution_count)
        
        return {
            'total_procedures': len(self.procedures),
            'total_executions': total_executions,
            'success_rate': total_successes / max(total_executions, 1),
            'most_executed': most_executed.name if most_executed else None,
            'recent_executions': len([h for h in self.execution_history if time.time() - h[1] < 3600])
        }
    
    def export_procedures(self) -> List[Dict[str, Any]]:
        """Export all procedures as dictionaries."""
        return [procedure.to_dict() for procedure in self.procedures.values()]
    
    def import_procedures(self, procedure_data: List[Dict[str, Any]]) -> int:
        """
        Import procedures from dictionaries.
        
        Args:
            procedure_data: List of procedure dictionaries
            
        Returns:
            Number of procedures imported
        """
        imported_count = 0
        
        for data in procedure_data:
            try:
                procedure = Procedure.from_dict(data)
                self.procedures[procedure.id] = procedure
                self.name_index[procedure.name.lower()] = procedure.id
                
                # Update context index
                for key, value in procedure.context.items():
                    context_key = f"{key}:{value}"
                    if context_key not in self.context_index:
                        self.context_index[context_key] = []
                    self.context_index[context_key].append(procedure.id)
                
                imported_count += 1
            except Exception as e:
                logger.warning(f"Failed to import procedure: {e}")
        
        logger.info(f"Imported {imported_count} procedures")
        return imported_count


# Create default instance
default_procedural_kb = ProceduralKnowledgeBase()

logger.info("ULTRA Procedural Knowledge module initialized successfully")
