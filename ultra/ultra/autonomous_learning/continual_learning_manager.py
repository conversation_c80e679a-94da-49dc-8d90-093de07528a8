#!/usr/bin/env python3
"""
ULTRA: Continual Learning Manager

Implements continual learning mechanisms that enable learning new tasks 
without forgetting previously learned knowledge. Uses elastic weight
consolidation, synaptic intelligence, and progressive neural networks.
"""

import logging
import numpy as np
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict, deque

logger = logging.getLogger(__name__)

@dataclass
class ContinualLearningMetrics:
    """Metrics for tracking continual learning performance."""
    forgetting_measure: float = 0.0
    learning_efficiency: float = 0.0
    knowledge_retention: float = 1.0
    interference_level: float = 0.0
    adaptation_speed: float = 0.0

class ContinualLearningManager:
    """
    Manages continual learning to prevent catastrophic forgetting while
    enabling rapid adaptation to new tasks and domains.
    """
    
    def __init__(
        self,
        config: Any,
        neuroplasticity_engine: Optional[Any] = None
    ):
        """Initialize continual learning manager."""
        self.config = config
        self.neuroplasticity_engine = neuroplasticity_engine
        
        # Task and knowledge tracking
        self.task_memory = {}  # Store task-specific knowledge
        self.task_importance = defaultdict(float)  # Importance weights for tasks
        self.task_performance_history = defaultdict(list)
        
        # Consolidation mechanisms
        self.importance_weights = {}  # Fisher Information Matrix approximation
        self.previous_parameters = {}  # Previous optimal parameters
        self.synaptic_importance = defaultdict(float)  # Synaptic intelligence weights
        
        # Progressive neural network components
        self.lateral_connections = {}  # Connections between task columns
        self.task_specific_modules = {}  # Task-specific neural modules
        
        # Experience buffer for rehearsal
        self.experience_buffer = deque(maxlen=config.replay_buffer_size)
        self.task_experience_counts = defaultdict(int)
        
        # Learning state
        self.current_task_id = None
        self.learning_iteration = 0
        self.consolidation_frequency = 100
        
        # Metrics tracking
        self.metrics_history = []
        self.forgetting_threshold = 0.1
        
        logger.info("Continual Learning Manager initialized")
    
    def update_with_experience(
        self,
        experience: Dict[str, Any],
        performance_feedback: Optional[Dict[str, float]] = None
    ) -> Dict[str, Any]:
        """
        Update the continual learning system with a new experience.
        
        Args:
            experience: Learning experience containing task info and data
            performance_feedback: Performance metrics for the experience
            
        Returns:
            Dictionary with update results and metrics
        """
        task_id = experience.get('task_id', 'default')
        self.current_task_id = task_id
        
        results = {
            'task_id': task_id,
            'learning_iteration': self.learning_iteration,
            'updates_applied': [],
            'metrics': {},
            'consolidation_triggered': False
        }
        
        try:
            # 1. Store experience in buffer
            self._store_experience(experience, performance_feedback)
            
            # 2. Check for new task detection
            task_change_detected = self._detect_task_change(experience)
            if task_change_detected:
                results['updates_applied'].append('task_change_detected')
                self._handle_task_transition(task_id)
            
            # 3. Apply elastic weight consolidation
            ewc_results = self._apply_elastic_weight_consolidation(experience, task_id)
            results['updates_applied'].append('elastic_weight_consolidation')
            
            # 4. Update synaptic intelligence
            si_results = self._update_synaptic_intelligence(experience, performance_feedback)
            results['updates_applied'].append('synaptic_intelligence')
            
            # 5. Progressive neural network updates
            pnn_results = self._update_progressive_networks(experience, task_id)
            results['updates_applied'].append('progressive_networks')
            
            # 6. Experience replay for rehearsal
            if self.learning_iteration % 10 == 0:  # Every 10 iterations
                replay_results = self._rehearse_previous_experiences()
                results['updates_applied'].append('experience_rehearsal')
            
            # 7. Consolidation check
            if self.learning_iteration % self.consolidation_frequency == 0:
                consolidation_results = self._consolidate_knowledge()
                results['consolidation_triggered'] = True
                results['updates_applied'].append('knowledge_consolidation')
            
            # 8. Update performance tracking
            self._update_performance_tracking(task_id, performance_feedback)
            
            # 9. Compute current metrics
            current_metrics = self._compute_continual_learning_metrics()
            results['metrics'] = current_metrics
            self.metrics_history.append({
                'iteration': self.learning_iteration,
                'timestamp': time.time(),
                'metrics': current_metrics,
                'task_id': task_id
            })
            
            # 10. Adaptive parameter adjustment
            if current_metrics.forgetting_measure > self.forgetting_threshold:
                self._adjust_consolidation_parameters(current_metrics)
                results['updates_applied'].append('parameter_adjustment')
            
            self.learning_iteration += 1
            results['success'] = True
            
        except Exception as e:
            logger.error(f"Error in continual learning update: {str(e)}")
            results['error'] = str(e)
            results['success'] = False
        
        return results
    
    def _store_experience(
        self,
        experience: Dict[str, Any],
        performance_feedback: Optional[Dict[str, float]]
    ) -> None:
        """Store experience in the experience buffer."""
        experience_entry = {
            'experience': experience.copy(),
            'performance': performance_feedback,
            'timestamp': time.time(),
            'task_id': experience.get('task_id', 'default'),
            'importance': self._compute_experience_importance(experience, performance_feedback)
        }
        
        self.experience_buffer.append(experience_entry)
        self.task_experience_counts[experience_entry['task_id']] += 1
    
    def _detect_task_change(self, experience: Dict[str, Any]) -> bool:
        """Detect if we've transitioned to a new task."""
        task_id = experience.get('task_id', 'default')
        
        # Simple task change detection based on task_id
        if self.current_task_id and self.current_task_id != task_id:
            return True
        
        # More sophisticated detection could analyze experience patterns
        # For now, we rely on explicit task_id changes
        return False
    
    def _handle_task_transition(self, new_task_id: str) -> None:
        """Handle transition to a new task."""
        if self.current_task_id:
            # Consolidate knowledge from previous task
            self._consolidate_task_knowledge(self.current_task_id)
            
            # Update importance weights
            self._update_task_importance(self.current_task_id)
        
        # Initialize structures for new task
        if new_task_id not in self.task_memory:
            self.task_memory[new_task_id] = {
                'knowledge_state': {},
                'performance_baseline': 0.0,
                'learning_trajectory': []
            }
        
        # Create task-specific neural module if using progressive networks
        if new_task_id not in self.task_specific_modules:
            self._create_task_specific_module(new_task_id)
        
        logger.info(f"Transitioned to new task: {new_task_id}")
    
    def _apply_elastic_weight_consolidation(
        self,
        experience: Dict[str, Any],
        task_id: str
    ) -> Dict[str, Any]:
        """Apply Elastic Weight Consolidation to prevent forgetting."""
        results = {
            'consolidation_strength': 0.0,
            'parameters_protected': 0,
            'ewc_loss_contribution': 0.0
        }
        
        # If we have neuroplasticity engine, work with synaptic weights
        if self.neuroplasticity_engine:
            # Get current synaptic weights
            current_weights = self._get_current_weights()
            
            # Calculate importance for each weight
            if task_id in self.importance_weights:
                importance = self.importance_weights[task_id]
                previous_weights = self.previous_parameters.get(task_id, {})
                
                # Compute EWC penalty for weight changes
                ewc_penalty = 0.0
                protected_count = 0
                
                for weight_id, current_weight in current_weights.items():
                    if weight_id in importance and weight_id in previous_weights:
                        weight_diff = current_weight - previous_weights[weight_id]
                        penalty = importance[weight_id] * (weight_diff ** 2)
                        ewc_penalty += penalty
                        
                        # Apply consolidation (reduce learning rate for important weights)
                        if importance[weight_id] > 0.1:  # Threshold for importance
                            self._apply_weight_consolidation(weight_id, importance[weight_id])
                            protected_count += 1
                
                results['consolidation_strength'] = ewc_penalty
                results['parameters_protected'] = protected_count
                results['ewc_loss_contribution'] = ewc_penalty
        
        return results
    
    def _update_synaptic_intelligence(
        self,
        experience: Dict[str, Any],
        performance_feedback: Optional[Dict[str, float]]
    ) -> Dict[str, Any]:
        """Update synaptic intelligence for path-dependent importance."""
        results = {
            'synapses_updated': 0,
            'importance_changes': 0,
            'average_importance': 0.0
        }
        
        if self.neuroplasticity_engine and performance_feedback:
            current_weights = self._get_current_weights()
            
            # Update synaptic importance based on weight changes and performance
            performance_score = np.mean(list(performance_feedback.values()))
            
            for weight_id, current_weight in current_weights.items():
                # Get previous weight
                prev_weight = self.previous_parameters.get('current', {}).get(weight_id, current_weight)
                weight_change = abs(current_weight - prev_weight)
                
                # Update importance based on weight change and performance
                if weight_change > 1e-6:  # Threshold for significant change
                    importance_change = weight_change * performance_score
                    self.synaptic_importance[weight_id] += importance_change
                    results['synapses_updated'] += 1
                    
                    if importance_change > 0.01:
                        results['importance_changes'] += 1
            
            # Update previous weights
            if 'current' not in self.previous_parameters:
                self.previous_parameters['current'] = {}
            self.previous_parameters['current'].update(current_weights)
            
            # Compute average importance
            if self.synaptic_importance:
                results['average_importance'] = np.mean(list(self.synaptic_importance.values()))
        
        return results
    
    def _update_progressive_networks(
        self,
        experience: Dict[str, Any],
        task_id: str
    ) -> Dict[str, Any]:
        """Update progressive neural networks for task-specific learning."""
        results = {
            'lateral_connections_updated': 0,
            'task_module_updates': 0,
            'knowledge_transfer_score': 0.0
        }
        
        # Ensure task-specific module exists
        if task_id not in self.task_specific_modules:
            self._create_task_specific_module(task_id)
            results['task_module_updates'] += 1
        
        # Update lateral connections from previous tasks
        for prev_task_id in self.task_specific_modules:
            if prev_task_id != task_id:
                # Compute similarity between tasks
                similarity = self._compute_task_similarity(prev_task_id, task_id)
                
                # Update lateral connections based on similarity
                if similarity > 0.3:  # Threshold for beneficial transfer
                    connection_strength = self._update_lateral_connection(prev_task_id, task_id, similarity)
                    results['lateral_connections_updated'] += 1
                    results['knowledge_transfer_score'] += similarity
        
        # Update task-specific module
        module_update = self._update_task_module(task_id, experience)
        results['task_module_updates'] += module_update
        
        return results
    
    def _rehearse_previous_experiences(self) -> Dict[str, Any]:
        """Rehearse previous experiences to prevent forgetting."""
        results = {
            'experiences_rehearsed': 0,
            'tasks_rehearsed': set(),
            'rehearsal_effectiveness': 0.0
        }
        
        if not self.experience_buffer:
            return results
        
        # Sample experiences for rehearsal
        rehearsal_batch_size = min(10, len(self.experience_buffer))
        
        # Prioritize important and diverse experiences
        sorted_experiences = sorted(
            self.experience_buffer,
            key=lambda x: x['importance'],
            reverse=True
        )
        
        # Ensure diversity across tasks
        rehearsal_experiences = []
        tasks_included = set()
        
        for exp in sorted_experiences:
            if len(rehearsal_experiences) >= rehearsal_batch_size:
                break
            
            task_id = exp['task_id']
            # Include if we haven't seen this task yet or if it's very important
            if task_id not in tasks_included or exp['importance'] > 0.8:
                rehearsal_experiences.append(exp)
                tasks_included.add(task_id)
        
        # Apply rehearsal
        for exp in rehearsal_experiences:
            self._apply_rehearsal_update(exp)
            results['experiences_rehearsed'] += 1
            results['tasks_rehearsed'].add(exp['task_id'])
        
        # Compute rehearsal effectiveness
        if results['experiences_rehearsed'] > 0:
            results['rehearsal_effectiveness'] = len(results['tasks_rehearsed']) / results['experiences_rehearsed']
        
        return results
    
    def _consolidate_knowledge(self) -> Dict[str, Any]:
        """Consolidate knowledge across tasks."""
        results = {
            'consolidation_operations': 0,
            'knowledge_compression': 0.0,
            'stability_improvement': 0.0
        }
        
        # Consolidate importance weights
        self._consolidate_importance_weights()
        results['consolidation_operations'] += 1
        
        # Compress redundant knowledge
        compression_ratio = self._compress_redundant_knowledge()
        results['knowledge_compression'] = compression_ratio
        results['consolidation_operations'] += 1
        
        # Strengthen stable connections
        if self.neuroplasticity_engine:
            stability_improvement = self._strengthen_stable_connections()
            results['stability_improvement'] = stability_improvement
            results['consolidation_operations'] += 1
        
        return results
    
    def _compute_continual_learning_metrics(self) -> ContinualLearningMetrics:
        """Compute current continual learning metrics."""
        metrics = ContinualLearningMetrics()
        
        # Compute forgetting measure
        if len(self.task_performance_history) > 1:
            forgetting_scores = []
            
            for task_id, performances in self.task_performance_history.items():
                if len(performances) >= 2:
                    # Compare recent performance with peak performance
                    peak_performance = max(performances)
                    recent_performance = np.mean(performances[-3:])  # Last 3 measurements
                    forgetting = max(0, peak_performance - recent_performance)
                    forgetting_scores.append(forgetting)
            
            if forgetting_scores:
                metrics.forgetting_measure = np.mean(forgetting_scores)
        
        # Compute learning efficiency
        if self.learning_iteration > 0:
            total_improvements = sum(
                len([p for p in performances if p > 0.7])
                for performances in self.task_performance_history.values()
            )
            metrics.learning_efficiency = total_improvements / self.learning_iteration
        
        # Compute knowledge retention
        if self.task_performance_history:
            retention_scores = []
            for performances in self.task_performance_history.values():
                if len(performances) >= 2:
                    initial = performances[0]
                    current = performances[-1]
                    retention = min(1.0, current / max(0.01, initial))
                    retention_scores.append(retention)
            
            if retention_scores:
                metrics.knowledge_retention = np.mean(retention_scores)
        
        # Compute interference level
        if len(self.task_performance_history) > 1:
            # Measure how much new tasks interfere with old ones
            interference_scores = []
            task_ids = list(self.task_performance_history.keys())
            
            for i, task_id in enumerate(task_ids[:-1]):  # All but last task
                performances = self.task_performance_history[task_id]
                if len(performances) >= 2:
                    # Performance before and after learning other tasks
                    early_perf = np.mean(performances[:len(performances)//2])
                    late_perf = np.mean(performances[len(performances)//2:])
                    interference = max(0, early_perf - late_perf)
                    interference_scores.append(interference)
            
            if interference_scores:
                metrics.interference_level = np.mean(interference_scores)
        
        # Compute adaptation speed
        if self.current_task_id and self.current_task_id in self.task_performance_history:
            current_performances = self.task_performance_history[self.current_task_id]
            if len(current_performances) >= 3:
                # Rate of improvement in current task
                recent_improvements = np.diff(current_performances[-3:])
                metrics.adaptation_speed = np.mean([imp for imp in recent_improvements if imp > 0])
        
        return metrics
    
    def get_learning_health_metrics(self) -> Dict[str, Any]:
        """Get comprehensive learning health metrics."""
        current_metrics = self._compute_continual_learning_metrics()
        
        health_metrics = {
            'forgetting_health': 'good' if current_metrics.forgetting_measure < 0.1 else 'concerning' if current_metrics.forgetting_measure < 0.3 else 'poor',
            'learning_efficiency': current_metrics.learning_efficiency,
            'knowledge_retention': current_metrics.knowledge_retention,
            'interference_level': current_metrics.interference_level,
            'adaptation_speed': current_metrics.adaptation_speed,
            'total_tasks_learned': len(self.task_memory),
            'experience_buffer_utilization': len(self.experience_buffer) / self.config.replay_buffer_size,
            'consolidation_frequency': self.consolidation_frequency,
            'recent_performance_trend': self._compute_recent_performance_trend()
        }
        
        return health_metrics
    
    # Helper methods (simplified implementations)
    def _get_current_weights(self) -> Dict[str, float]:
        """Get current neural network weights."""
        # Placeholder - in real implementation, this would interface with neuroplasticity engine
        return {}
    
    def _apply_weight_consolidation(self, weight_id: str, importance: float) -> None:
        """Apply consolidation to a specific weight."""
        # Placeholder - would reduce learning rate for important weights
        pass
    
    def _create_task_specific_module(self, task_id: str) -> None:
        """Create a task-specific neural module."""
        self.task_specific_modules[task_id] = {
            'created_at': time.time(),
            'parameters': {},
            'connections': {}
        }
    
    def _compute_task_similarity(self, task_id1: str, task_id2: str) -> float:
        """Compute similarity between two tasks."""
        # Placeholder - would compare task representations
        return np.random.random() * 0.5  # Random similarity for now
    
    def _update_lateral_connection(self, source_task: str, target_task: str, similarity: float) -> float:
        """Update lateral connection strength between tasks."""
        connection_key = (source_task, target_task)
        current_strength = self.lateral_connections.get(connection_key, 0.0)
        new_strength = current_strength + 0.1 * similarity
        self.lateral_connections[connection_key] = min(1.0, new_strength)
        return new_strength
    
    def _update_task_module(self, task_id: str, experience: Dict[str, Any]) -> int:
        """Update task-specific module."""
        # Placeholder - would update module parameters
        return 1
    
    def _apply_rehearsal_update(self, experience_entry: Dict[str, Any]) -> None:
        """Apply a rehearsal update for an experience."""
        # Placeholder - would replay the experience through the network
        pass
    
    def _consolidate_importance_weights(self) -> None:
        """Consolidate importance weights across tasks."""
        # Placeholder - would merge and normalize importance weights
        pass
    
    def _compress_redundant_knowledge(self) -> float:
        """Compress redundant knowledge representations."""
        # Placeholder - would identify and compress similar knowledge
        return 0.1  # 10% compression achieved
    
    def _strengthen_stable_connections(self) -> float:
        """Strengthen connections that have been stable."""
        # Placeholder - would strengthen consistently important connections
        return 0.05  # 5% improvement in stability
    
    def _compute_experience_importance(
        self,
        experience: Dict[str, Any],
        performance_feedback: Optional[Dict[str, float]]
    ) -> float:
        """Compute importance score for an experience."""
        importance = 0.5  # Base importance
        
        if performance_feedback:
            # Higher importance for better performance
            avg_performance = np.mean(list(performance_feedback.values()))
            importance += 0.3 * avg_performance
        
        # Add recency bonus
        importance += 0.2
        
        return min(1.0, importance)
    
    def _consolidate_task_knowledge(self, task_id: str) -> None:
        """Consolidate knowledge for a specific task."""
        if task_id in self.task_memory:
            # Update consolidation timestamp
            self.task_memory[task_id]['last_consolidation'] = time.time()
    
    def _update_task_importance(self, task_id: str) -> None:
        """Update importance weight for a task."""
        if task_id in self.task_performance_history:
            performances = self.task_performance_history[task_id]
            avg_performance = np.mean(performances) if performances else 0.5
            self.task_importance[task_id] = avg_performance
    
    def _update_performance_tracking(
        self,
        task_id: str,
        performance_feedback: Optional[Dict[str, float]]
    ) -> None:
        """Update performance tracking for a task."""
        if performance_feedback:
            avg_performance = np.mean(list(performance_feedback.values()))
            self.task_performance_history[task_id].append(avg_performance)
            
            # Keep only recent performance history
            if len(self.task_performance_history[task_id]) > 100:
                self.task_performance_history[task_id] = self.task_performance_history[task_id][-100:]
    
    def _adjust_consolidation_parameters(self, metrics: ContinualLearningMetrics) -> None:
        """Adjust consolidation parameters based on metrics."""
        if metrics.forgetting_measure > 0.2:
            # Increase consolidation frequency if forgetting is high
            self.consolidation_frequency = max(50, int(self.consolidation_frequency * 0.8))
        elif metrics.forgetting_measure < 0.05:
            # Decrease consolidation frequency if forgetting is low
            self.consolidation_frequency = min(200, int(self.consolidation_frequency * 1.2))
    
    def _compute_recent_performance_trend(self) -> str:
        """Compute recent performance trend across all tasks."""
        if not self.metrics_history or len(self.metrics_history) < 5:
            return 'insufficient_data'
        
        recent_metrics = self.metrics_history[-5:]
        forgetting_trend = [m['metrics'].forgetting_measure for m in recent_metrics]
        efficiency_trend = [m['metrics'].learning_efficiency for m in recent_metrics]
        
        # Simple trend analysis
        forgetting_improving = forgetting_trend[-1] < forgetting_trend[0]
        efficiency_improving = efficiency_trend[-1] > efficiency_trend[0]
        
        if forgetting_improving and efficiency_improving:
            return 'improving'
        elif not forgetting_improving and not efficiency_improving:
            return 'declining'
        else:
            return 'mixed'
    
    def reset(self) -> None:
        """Reset the continual learning manager."""
        self.task_memory.clear()
        self.task_importance.clear()
        self.task_performance_history.clear()
        self.importance_weights.clear()
        self.previous_parameters.clear()
        self.synaptic_importance.clear()
        self.lateral_connections.clear()
        self.task_specific_modules.clear()
        self.experience_buffer.clear()
        self.task_experience_counts.clear()
        self.metrics_history.clear()
        self.learning_iteration = 0
        self.current_task_id = None
    
    def get_state(self) -> Dict[str, Any]:
        """Get current state for saving."""
        return {
            'task_memory': self.task_memory,
            'task_importance': dict(self.task_importance),
            'task_performance_history': dict(self.task_performance_history),
            'importance_weights': self.importance_weights,
            'previous_parameters': self.previous_parameters,
            'synaptic_importance': dict(self.synaptic_importance),
            'lateral_connections': self.lateral_connections,
            'task_specific_modules': self.task_specific_modules,
            'learning_iteration': self.learning_iteration,
            'current_task_id': self.current_task_id,
            'consolidation_frequency': self.consolidation_frequency
        }
    
    def load_state(self, state: Dict[str, Any]) -> None:
        """Load state from saved data."""
        self.task_memory = state.get('task_memory', {})
        self.task_importance = defaultdict(float, state.get('task_importance', {}))
        self.task_performance_history = defaultdict(list, state.get('task_performance_history', {}))
        self.importance_weights = state.get('importance_weights', {})
        self.previous_parameters = state.get('previous_parameters', {})
        self.synaptic_importance = defaultdict(float, state.get('synaptic_importance', {}))
        self.lateral_connections = state.get('lateral_connections', {})
        self.task_specific_modules = state.get('task_specific_modules', {})
        self.learning_iteration = state.get('learning_iteration', 0)
        self.current_task_id = state.get('current_task_id')
        self.consolidation_frequency = state.get('consolidation_frequency', 100)
