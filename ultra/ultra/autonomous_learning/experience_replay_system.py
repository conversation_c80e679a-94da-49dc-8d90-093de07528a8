#!/usr/bin/env python3
"""
ULTRA Autonomous Learning: Experience Replay System

This module implements an intelligent experience replay system that selectively
rehearses important past experiences to enhance learning and prevent forgetting.

Key Features:
1. Priority-Based Replay - Prioritizes important and challenging experiences
2. Diversity Sampling - Ensures diverse experience coverage
3. Memory Consolidation - Strengthens important memory traces
4. Adaptive Replay Scheduling - Dynamically adjusts replay frequency
5. Forgetting Prevention - Maintains performance on previous tasks
"""

import logging
import time
import random
import numpy as np
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass, field
from collections import defaultdict, deque
from enum import Enum, auto
import heapq
import hashlib
import pickle

logger = logging.getLogger(__name__)


class ExperienceType(Enum):
    """Types of learning experiences."""
    SKILL_ACQUISITION = auto()
    PROBLEM_SOLVING = auto()
    ERROR_CORRECTION = auto()
    BREAKTHROUGH = auto()
    ROUTINE_TASK = auto()
    NOVEL_SITUATION = auto()
    FAILURE_ANALYSIS = auto()
    SUCCESS_PATTERN = auto()


@dataclass
class Experience:
    """Represents a learning experience for replay."""
    experience_id: str
    experience_type: ExperienceType
    content: Dict[str, Any]
    performance_metrics: Dict[str, float]
    importance_score: float
    difficulty_level: float
    novelty_score: float
    emotional_impact: float
    timestamp: float = field(default_factory=time.time)
    replay_count: int = 0
    last_replay: float = 0.0
    memory_strength: float = 1.0
    associated_skills: List[str] = field(default_factory=list)
    context_tags: Set[str] = field(default_factory=set)
    learning_outcomes: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ReplayBatch:
    """Represents a batch of experiences for replay."""
    batch_id: str
    experiences: List[Experience]
    replay_strategy: str
    total_importance: float
    diversity_score: float
    timestamp: float = field(default_factory=time.time)


class ExperienceReplaySystem:
    """
    Intelligent system for experience replay and memory management.
    
    This component manages the storage, prioritization, and replay of learning
    experiences to enhance retention and prevent catastrophic forgetting.
    """
    
    def __init__(self, config: Any):
        """Initialize the experience replay system."""
        self.config = config
        
        # Experience storage
        self.experience_buffer: List[Experience] = []
        self.experience_index: Dict[str, int] = {}  # ID to index mapping
        self.priority_queue = []  # Heap for priority-based sampling
        
        # Memory management
        self.max_buffer_size = getattr(config, 'replay_buffer_size', 10000)
        self.compression_threshold = int(self.max_buffer_size * 0.8)
        self.forgetting_curve_alpha = getattr(config, 'forgetting_curve_alpha', 0.1)
        
        # Replay configuration
        self.replay_frequency = getattr(config, 'replay_frequency', 100)
        self.replay_batch_size = getattr(config, 'replay_batch_size', 32)
        self.diversity_weight = 0.3
        self.importance_weight = 0.7
        
        # Replay strategies
        self.replay_strategies = {
            'priority_based': self._priority_based_replay,
            'diversity_sampling': self._diversity_based_replay,
            'temporal_replay': self._temporal_based_replay,
            'adaptive_replay': self._adaptive_replay,
            'consolidation_replay': self._consolidation_replay
        }
        
        # Performance tracking
        self.replay_iteration = 0
        self.total_replays = 0
        self.replay_history: List[Dict[str, Any]] = []
        self.memory_statistics = defaultdict(float)
        
        # Adaptive parameters
        self.current_strategy = 'adaptive_replay'
        self.strategy_performance = defaultdict(list)
        
        logger.info("Experience Replay System initialized")
    
    def store_experience(
        self,
        experience_content: Dict[str, Any],
        performance_metrics: Dict[str, float],
        experience_type: str = "SKILL_ACQUISITION",
        context_tags: Optional[Set[str]] = None
    ) -> str:
        """
        Store a new learning experience in the replay buffer.
        
        Args:
            experience_content: Content of the experience
            performance_metrics: Performance metrics associated with the experience
            experience_type: Type of experience
            context_tags: Context tags for categorization
            
        Returns:
            Experience ID
        """
        # Generate unique ID
        experience_id = hashlib.md5(
            f"{time.time()}_{hash(str(experience_content))}".encode()
        ).hexdigest()[:16]
        
        # Calculate importance and other scores
        importance_score = self._calculate_importance(experience_content, performance_metrics)
        difficulty_level = self._calculate_difficulty(experience_content, performance_metrics)
        novelty_score = self._calculate_novelty(experience_content)
        emotional_impact = self._calculate_emotional_impact(experience_content, performance_metrics)
        
        # Create experience object
        experience = Experience(
            experience_id=experience_id,
            experience_type=ExperienceType[experience_type.upper()],
            content=experience_content,
            performance_metrics=performance_metrics,
            importance_score=importance_score,
            difficulty_level=difficulty_level,
            novelty_score=novelty_score,
            emotional_impact=emotional_impact,
            context_tags=context_tags or set(),
            associated_skills=experience_content.get('skills_involved', [])
        )
        
        # Add to buffer
        self._add_to_buffer(experience)
        
        # Update priority queue
        self._update_priority_queue(experience)
        
        # Manage buffer size
        if len(self.experience_buffer) > self.max_buffer_size:
            self._compress_buffer()
        
        logger.debug(f"Stored experience {experience_id} with importance {importance_score:.3f}")
        return experience_id
    
    def replay_experiences(
        self,
        strategy: Optional[str] = None,
        batch_size: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Replay a batch of important experiences.
        
        Args:
            strategy: Replay strategy to use
            batch_size: Number of experiences to replay
            
        Returns:
            Dictionary with replay results
        """
        results = {
            'replay_iteration': self.replay_iteration,
            'timestamp': time.time(),
            'strategy_used': strategy or self.current_strategy,
            'experiences_replayed': 0,
            'replayed_experiences': [],
            'diversity_score': 0.0,
            'total_importance': 0.0,
            'memory_strengthening': {}
        }
        
        try:
            # Select replay strategy
            replay_strategy = strategy or self.current_strategy
            batch_size = batch_size or self.replay_batch_size
            
            # Generate replay batch
            replay_batch = self._generate_replay_batch(replay_strategy, batch_size)
            
            if not replay_batch.experiences:
                results['error'] = "No experiences available for replay"
                return results
            
            # Apply replay
            replay_results = self._apply_replay(replay_batch)
            
            # Update results
            results['experiences_replayed'] = len(replay_batch.experiences)
            results['replayed_experiences'] = [
                {
                    'id': exp.experience_id,
                    'type': exp.experience_type.name,
                    'importance': exp.importance_score,
                    'replay_count': exp.replay_count
                }
                for exp in replay_batch.experiences
            ]
            results['diversity_score'] = replay_batch.diversity_score
            results['total_importance'] = replay_batch.total_importance
            results['memory_strengthening'] = replay_results['memory_strengthening']
            
            # Update tracking
            self.replay_iteration += 1
            self.total_replays += len(replay_batch.experiences)
            
            # Store replay history
            self.replay_history.append({
                'iteration': self.replay_iteration,
                'timestamp': results['timestamp'],
                'strategy': replay_strategy,
                'batch_size': len(replay_batch.experiences),
                'diversity': replay_batch.diversity_score,
                'importance': replay_batch.total_importance
            })
            
            # Update strategy performance
            self._update_strategy_performance(replay_strategy, replay_results)
            
            logger.info(f"Replay iteration {self.replay_iteration} completed: "
                       f"{results['experiences_replayed']} experiences replayed")
            
        except Exception as e:
            logger.error(f"Error during experience replay: {str(e)}")
            results['error'] = str(e)
        
        return results
    
    def _calculate_importance(
        self,
        experience_content: Dict[str, Any],
        performance_metrics: Dict[str, float]
    ) -> float:
        """Calculate the importance score of an experience."""
        importance = 0.0
        
        # Performance-based importance
        if performance_metrics:
            avg_performance = np.mean(list(performance_metrics.values()))
            # High performance or very low performance (failures) are important
            if avg_performance > 0.8 or avg_performance < 0.3:
                importance += 0.4
            else:
                importance += 0.2
        
        # Content-based importance
        content_indicators = experience_content.get('importance_indicators', {})
        
        # Error recovery experiences are important
        if content_indicators.get('error_recovery', False):
            importance += 0.3
        
        # Breakthrough experiences are very important
        if content_indicators.get('breakthrough', False):
            importance += 0.4
        
        # Novel situations are important
        if content_indicators.get('novel_situation', False):
            importance += 0.3
        
        # Learning progress indicators
        progress = content_indicators.get('learning_progress', 0.0)
        importance += progress * 0.2
        
        # Ensure importance is in [0, 1]
        return min(1.0, max(0.0, importance))
    
    def _calculate_difficulty(
        self,
        experience_content: Dict[str, Any],
        performance_metrics: Dict[str, float]
    ) -> float:
        """Calculate the difficulty level of an experience."""
        difficulty = 0.5  # Default medium difficulty
        
        # Performance-based difficulty
        if performance_metrics:
            avg_performance = np.mean(list(performance_metrics.values()))
            # Lower performance indicates higher difficulty
            difficulty = 1.0 - avg_performance
        
        # Content-based difficulty indicators
        content_indicators = experience_content.get('difficulty_indicators', {})
        
        # Problem complexity
        complexity = content_indicators.get('complexity_score', 0.5)
        difficulty = difficulty * 0.7 + complexity * 0.3
        
        return min(1.0, max(0.0, difficulty))
    
    def _calculate_novelty(self, experience_content: Dict[str, Any]) -> float:
        """Calculate the novelty score of an experience."""
        novelty = 0.5  # Default medium novelty
        
        # Check similarity to existing experiences
        content_hash = hash(str(sorted(experience_content.items())))
        similar_count = 0
        
        for exp in self.experience_buffer[-100:]:  # Check recent experiences
            existing_hash = hash(str(sorted(exp.content.items())))
            if abs(content_hash - existing_hash) < 1000:  # Simple similarity check
                similar_count += 1
        
        # Higher novelty if fewer similar experiences
        if similar_count == 0:
            novelty = 1.0
        elif similar_count < 3:
            novelty = 0.8
        elif similar_count < 10:
            novelty = 0.5
        else:
            novelty = 0.2
        
        # Content-based novelty indicators
        content_indicators = experience_content.get('novelty_indicators', {})
        if content_indicators.get('first_encounter', False):
            novelty = max(novelty, 0.9)
        
        return novelty
    
    def _calculate_emotional_impact(
        self,
        experience_content: Dict[str, Any],
        performance_metrics: Dict[str, float]
    ) -> float:
        """Calculate the emotional impact of an experience."""
        impact = 0.0
        
        # Performance-based emotional impact
        if performance_metrics:
            avg_performance = np.mean(list(performance_metrics.values()))
            # Extreme performance (very good or very bad) has high emotional impact
            if avg_performance > 0.9:
                impact += 0.8  # Success
            elif avg_performance < 0.2:
                impact += 0.7  # Failure
        
        # Content-based emotional indicators
        content_indicators = experience_content.get('emotional_indicators', {})
        
        if content_indicators.get('breakthrough', False):
            impact += 0.9
        elif content_indicators.get('frustration', False):
            impact += 0.6
        elif content_indicators.get('confusion', False):
            impact += 0.4
        
        return min(1.0, impact)
    
    def _add_to_buffer(self, experience: Experience) -> None:
        """Add experience to the buffer."""
        self.experience_buffer.append(experience)
        self.experience_index[experience.experience_id] = len(self.experience_buffer) - 1
    
    def _update_priority_queue(self, experience: Experience) -> None:
        """Update the priority queue with a new experience."""
        # Priority is negative importance (for min-heap)
        priority = -experience.importance_score
        heapq.heappush(self.priority_queue, (priority, experience.experience_id))
    
    def _compress_buffer(self) -> None:
        """Compress the buffer by removing less important experiences."""
        if len(self.experience_buffer) <= self.compression_threshold:
            return
        
        # Sort by importance and recency
        experiences_with_scores = []
        current_time = time.time()
        
        for exp in self.experience_buffer:
            # Combine importance with recency (more recent = higher score)
            recency_factor = 1.0 / (1.0 + (current_time - exp.timestamp) / 86400)  # Days
            combined_score = exp.importance_score * 0.7 + recency_factor * 0.3
            experiences_with_scores.append((combined_score, exp))
        
        # Sort by combined score (descending)
        experiences_with_scores.sort(key=lambda x: x[0], reverse=True)
        
        # Keep top experiences
        target_size = int(self.max_buffer_size * 0.7)
        kept_experiences = [exp for _, exp in experiences_with_scores[:target_size]]
        
        # Update buffer and index
        self.experience_buffer = kept_experiences
        self.experience_index = {
            exp.experience_id: idx for idx, exp in enumerate(kept_experiences)
        }
        
        # Rebuild priority queue
        self.priority_queue = []
        for exp in kept_experiences:
            self._update_priority_queue(exp)
        
        logger.info(f"Compressed experience buffer from {len(experiences_with_scores)} to {len(kept_experiences)}")
    
    def _generate_replay_batch(self, strategy: str, batch_size: int) -> ReplayBatch:
        """Generate a batch of experiences for replay."""
        if strategy in self.replay_strategies:
            experiences = self.replay_strategies[strategy](batch_size)
        else:
            experiences = self._adaptive_replay(batch_size)
        
        # Calculate batch statistics
        total_importance = sum(exp.importance_score for exp in experiences)
        diversity_score = self._calculate_batch_diversity(experiences)
        
        batch = ReplayBatch(
            batch_id=f"batch_{int(time.time())}_{random.randint(1000, 9999)}",
            experiences=experiences,
            replay_strategy=strategy,
            total_importance=total_importance,
            diversity_score=diversity_score
        )
        
        return batch
    
    def _priority_based_replay(self, batch_size: int) -> List[Experience]:
        """Select experiences based on importance priority."""
        if not self.experience_buffer:
            return []
        
        # Sort by importance
        sorted_experiences = sorted(
            self.experience_buffer,
            key=lambda x: x.importance_score,
            reverse=True
        )
        
        return sorted_experiences[:batch_size]
    
    def _diversity_based_replay(self, batch_size: int) -> List[Experience]:
        """Select diverse experiences to maximize coverage."""
        if not self.experience_buffer:
            return []
        
        selected = []
        available = list(self.experience_buffer)
        
        # Start with the most important experience
        if available:
            best = max(available, key=lambda x: x.importance_score)
            selected.append(best)
            available.remove(best)
        
        # Greedily select most diverse remaining experiences
        while len(selected) < batch_size and available:
            best_candidate = None
            best_diversity = -1
            
            for candidate in available:
                diversity = self._calculate_diversity_score(candidate, selected)
                if diversity > best_diversity:
                    best_diversity = diversity
                    best_candidate = candidate
            
            if best_candidate:
                selected.append(best_candidate)
                available.remove(best_candidate)
            else:
                break
        
        return selected
    
    def _temporal_based_replay(self, batch_size: int) -> List[Experience]:
        """Select experiences based on temporal patterns."""
        if not self.experience_buffer:
            return []
        
        current_time = time.time()
        
        # Prioritize older experiences that haven't been replayed recently
        experiences_with_priority = []
        
        for exp in self.experience_buffer:
            # Time since last replay
            time_since_replay = current_time - exp.last_replay if exp.last_replay > 0 else current_time - exp.timestamp
            
            # Memory decay factor
            decay_factor = np.exp(-self.forgetting_curve_alpha * time_since_replay / 86400)  # Days
            
            # Priority combines importance, time since replay, and memory decay
            priority = exp.importance_score * (1 + time_since_replay / 86400) * (1 - decay_factor)
            experiences_with_priority.append((priority, exp))
        
        # Sort by priority and take top experiences
        experiences_with_priority.sort(key=lambda x: x[0], reverse=True)
        return [exp for _, exp in experiences_with_priority[:batch_size]]
    
    def _adaptive_replay(self, batch_size: int) -> List[Experience]:
        """Adaptively select experiences based on multiple criteria."""
        if not self.experience_buffer:
            return []
        
        # Combine multiple selection strategies
        priority_experiences = self._priority_based_replay(batch_size // 2)
        diversity_experiences = self._diversity_based_replay(batch_size // 2)
        temporal_experiences = self._temporal_based_replay(batch_size // 4)
        
        # Combine and deduplicate
        all_experiences = priority_experiences + diversity_experiences + temporal_experiences
        seen_ids = set()
        unique_experiences = []
        
        for exp in all_experiences:
            if exp.experience_id not in seen_ids:
                unique_experiences.append(exp)
                seen_ids.add(exp.experience_id)
                if len(unique_experiences) >= batch_size:
                    break
        
        # Fill remaining slots with random sampling
        while len(unique_experiences) < batch_size and len(seen_ids) < len(self.experience_buffer):
            remaining = [exp for exp in self.experience_buffer if exp.experience_id not in seen_ids]
            if remaining:
                random_exp = random.choice(remaining)
                unique_experiences.append(random_exp)
                seen_ids.add(random_exp.experience_id)
        
        return unique_experiences[:batch_size]
    
    def _consolidation_replay(self, batch_size: int) -> List[Experience]:
        """Select experiences for memory consolidation."""
        if not self.experience_buffer:
            return []
        
        # Focus on experiences with high emotional impact and importance
        consolidation_candidates = []
        
        for exp in self.experience_buffer:
            consolidation_score = (
                exp.importance_score * 0.4 +
                exp.emotional_impact * 0.3 +
                exp.novelty_score * 0.2 +
                exp.difficulty_level * 0.1
            )
            consolidation_candidates.append((consolidation_score, exp))
        
        # Sort by consolidation score
        consolidation_candidates.sort(key=lambda x: x[0], reverse=True)
        return [exp for _, exp in consolidation_candidates[:batch_size]]
    
    def _calculate_batch_diversity(self, experiences: List[Experience]) -> float:
        """Calculate the diversity score of a batch of experiences."""
        if len(experiences) < 2:
            return 0.0
        
        # Calculate diversity based on experience types, contexts, and content
        types = set(exp.experience_type for exp in experiences)
        contexts = set().union(*(exp.context_tags for exp in experiences))
        skills = set().union(*(exp.associated_skills for exp in experiences))
        
        # Normalize by maximum possible diversity
        type_diversity = len(types) / len(ExperienceType)
        context_diversity = min(1.0, len(contexts) / 10)  # Assume max 10 contexts
        skill_diversity = min(1.0, len(skills) / 20)  # Assume max 20 skills
        
        return (type_diversity + context_diversity + skill_diversity) / 3.0
    
    def _calculate_diversity_score(self, candidate: Experience, selected: List[Experience]) -> float:
        """Calculate how diverse a candidate is relative to already selected experiences."""
        if not selected:
            return 1.0
        
        diversity_score = 0.0
        
        for exp in selected:
            # Type diversity
            type_diff = 1.0 if candidate.experience_type != exp.experience_type else 0.0
            
            # Context diversity
            common_contexts = candidate.context_tags.intersection(exp.context_tags)
            context_diff = 1.0 - (len(common_contexts) / max(1, len(candidate.context_tags.union(exp.context_tags))))
            
            # Skill diversity
            common_skills = set(candidate.associated_skills).intersection(set(exp.associated_skills))
            skill_diff = 1.0 - (len(common_skills) / max(1, len(set(candidate.associated_skills).union(set(exp.associated_skills)))))
            
            # Content diversity (simplified)
            content_diff = 0.5  # Placeholder for content similarity
            
            # Combine diversity measures
            exp_diversity = (type_diff + context_diff + skill_diff + content_diff) / 4.0
            diversity_score += exp_diversity
        
        return diversity_score / len(selected)
    
    def _apply_replay(self, replay_batch: ReplayBatch) -> Dict[str, Any]:
        """Apply replay to the selected experiences."""
        results = {
            'replayed_count': 0,
            'memory_strengthening': {},
            'learning_reinforcement': {}
        }
        
        current_time = time.time()
        
        for exp in replay_batch.experiences:
            # Update replay statistics
            exp.replay_count += 1
            exp.last_replay = current_time
            
            # Strengthen memory based on importance and repetition
            strength_increase = exp.importance_score * 0.1 * (1 + 0.1 * exp.replay_count)
            old_strength = exp.memory_strength
            exp.memory_strength = min(1.0, exp.memory_strength + strength_increase)
            
            results['memory_strengthening'][exp.experience_id] = {
                'old_strength': old_strength,
                'new_strength': exp.memory_strength,
                'increase': strength_increase
            }
            
            # Simulate learning reinforcement (placeholder)
            results['learning_reinforcement'][exp.experience_id] = {
                'pattern_reinforcement': exp.importance_score * 0.2,
                'skill_strengthening': len(exp.associated_skills) * 0.1
            }
            
            results['replayed_count'] += 1
        
        return results
    
    def _update_strategy_performance(self, strategy: str, replay_results: Dict[str, Any]) -> None:
        """Update performance tracking for replay strategies."""
        # Use memory strengthening as performance metric
        avg_strengthening = 0.0
        if replay_results['memory_strengthening']:
            strengthening_values = [
                data['increase'] for data in replay_results['memory_strengthening'].values()
            ]
            avg_strengthening = np.mean(strengthening_values)
        
        self.strategy_performance[strategy].append(avg_strengthening)
        
        # Keep only recent performance history
        if len(self.strategy_performance[strategy]) > 20:
            self.strategy_performance[strategy] = self.strategy_performance[strategy][-20:]
    
    def get_high_priority_experiences(self, limit: int = 100) -> List[Experience]:
        """Get high-priority experiences for external use."""
        if not self.experience_buffer:
            return []
        
        # Sort by importance and return top experiences
        sorted_experiences = sorted(
            self.experience_buffer,
            key=lambda x: x.importance_score,
            reverse=True
        )
        
        return sorted_experiences[:limit]
    
    def compress_old_memories(self, age_threshold: int = 1000, compression_ratio: float = 0.8) -> Dict[str, Any]:
        """Compress old memories to save space."""
        results = {
            'compressed_count': 0,
            'space_saved': 0,
            'compression_ratio': compression_ratio
        }
        
        current_time = time.time()
        compressed_count = 0
        
        for exp in self.experience_buffer:
            age_days = (current_time - exp.timestamp) / 86400
            if age_days > age_threshold:
                # Simulate memory compression
                if 'compressed_content' not in exp.content:
                    original_size = len(str(exp.content))
                    exp.content = {'compressed_content': f"compressed_{exp.experience_id}"}
                    compressed_size = len(str(exp.content))
                    
                    results['space_saved'] += original_size - compressed_size
                    compressed_count += 1
        
        results['compressed_count'] = compressed_count
        return results
    
    def get_memory_statistics(self) -> Dict[str, Any]:
        """Get comprehensive memory statistics."""
        stats = {
            'total_experiences': len(self.experience_buffer),
            'total_replays': self.total_replays,
            'replay_iterations': self.replay_iteration,
            'average_importance': 0.0,
            'average_memory_strength': 0.0,
            'experience_type_distribution': {},
            'strategy_performance': dict(self.strategy_performance),
            'buffer_utilization': len(self.experience_buffer) / self.max_buffer_size
        }
        
        if self.experience_buffer:
            stats['average_importance'] = np.mean([exp.importance_score for exp in self.experience_buffer])
            stats['average_memory_strength'] = np.mean([exp.memory_strength for exp in self.experience_buffer])
            
            # Experience type distribution
            type_counts = defaultdict(int)
            for exp in self.experience_buffer:
                type_counts[exp.experience_type.name] += 1
            stats['experience_type_distribution'] = dict(type_counts)
        
        return stats
    
    def get_state(self) -> Dict[str, Any]:
        """Get current state for saving."""
        return {
            'experience_buffer': self.experience_buffer,
            'experience_index': self.experience_index,
            'replay_iteration': self.replay_iteration,
            'total_replays': self.total_replays,
            'replay_history': self.replay_history,
            'memory_statistics': dict(self.memory_statistics),
            'current_strategy': self.current_strategy,
            'strategy_performance': dict(self.strategy_performance)
        }
    
    def load_state(self, state: Dict[str, Any]) -> None:
        """Load state from saved data."""
        self.experience_buffer = state.get('experience_buffer', [])
        self.experience_index = state.get('experience_index', {})
        self.replay_iteration = state.get('replay_iteration', 0)
        self.total_replays = state.get('total_replays', 0)
        self.replay_history = state.get('replay_history', [])
        self.memory_statistics = defaultdict(float, state.get('memory_statistics', {}))
        self.current_strategy = state.get('current_strategy', 'adaptive_replay')
        self.strategy_performance = defaultdict(list, state.get('strategy_performance', {}))
        
        # Rebuild priority queue
        self.priority_queue = []
        for exp in self.experience_buffer:
            self._update_priority_queue(exp)
        
        logger.info("Experience replay system state loaded")
    
    def reset(self) -> None:
        """Reset the experience replay system."""
        self.experience_buffer.clear()
        self.experience_index.clear()
        self.priority_queue.clear()
        self.replay_history.clear()
        self.memory_statistics.clear()
        self.strategy_performance.clear()
        
        self.replay_iteration = 0
        self.total_replays = 0
        self.current_strategy = 'adaptive_replay'
        
        logger.info("Experience replay system reset")
