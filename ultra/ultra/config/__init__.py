"""
ULTRA Configuration Management Package
====================================

Centralized configuration loading and management for the ULTRA AGI system.
This module provides easy access to configuration settings across all subsystems
with support for environment-specific overrides and dynamic updates.
"""

import os
import sys
from typing import Dict, Any, Optional
from pathlib import Path

# Add the parent directory to Python path for direct script execution
if __name__ == "__main__":
    current_dir = Path(__file__).parent
    ultra_dir = current_dir.parent
    sys.path.insert(0, str(ultra_dir))

# Import configuration utilities with fallback for different execution contexts
try:
    # Try relative import first (when used as a module)
    from ultra.utils.config import (
        ULTRAConfigManager,
        ConfigEnvironment,
        ConfigPriority,
        ConfigurationError,
        get_config_manager,
        get_config,
        set_config,
        reload_config
    )
except ImportError:
    try:
        # Try absolute import from the utils package
        from utils.config import (
            ULTRAConfigManager,
            ConfigEnvironment,
            ConfigPriority,
            ConfigurationError,
            get_config_manager,
            get_config,
            set_config,
            reload_config
        )
    except ImportError:
        # If both fail, we're probably running as a script, so add paths manually
        current_dir = Path(__file__).parent
        utils_dir = current_dir.parent / "utils"
        sys.path.insert(0, str(utils_dir))
        from config import (
            ULTRAConfigManager,
            ConfigEnvironment,
            ConfigPriority,
            ConfigurationError,
            get_config_manager,
            get_config,
            set_config,
            reload_config
        )

__all__ = [
    'ULTRAConfigManager',
    'ConfigEnvironment',
    'ConfigPriority', 
    'ConfigurationError',
    'get_config_manager',
    'get_config',
    'set_config',
    'reload_config',
    'load_configuration',
    'get_subsystem_config',
    'validate_configuration',
    'optimize_for_environment'
]

def load_configuration(config_dir: Optional[Path] = None,
                      environment: Optional[str] = None,
                      auto_optimize: bool = True) -> ULTRAConfigManager:
    """
    Load ULTRA configuration with environment detection and optimization
    
    Args:
        config_dir: Directory containing configuration files
        environment: Target environment (development, testing, production)
        auto_optimize: Whether to automatically optimize for hardware
        
    Returns:
        Configured ULTRAConfigManager instance
    """
    
    # Determine environment
    if environment is None:
        environment = os.getenv('ULTRA_ENVIRONMENT', 'development')
        
    try:
        env = ConfigEnvironment(environment.lower())
    except ValueError:
        env = ConfigEnvironment.DEVELOPMENT
        
    # Determine config directory
    if config_dir is None:
        config_dir = Path(__file__).parent
        
    # Create configuration manager
    config_manager = ULTRAConfigManager(
        config_dir=config_dir,
        environment=env,
        auto_watch=True
    )
    
    # Auto-optimize for hardware if requested
    if auto_optimize:
        config_manager.optimize_for_hardware()
        
    # Validate hardware requirements
    warnings = config_manager.validate_hardware_requirements()
    if warnings:
        print("Hardware validation warnings:")
        for warning in warnings:
            print(f"  - {warning}")
            
    return config_manager

def get_subsystem_config(subsystem: str, default: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Get configuration for a specific ULTRA subsystem
    
    Args:
        subsystem: Subsystem name (e.g., 'core_neural', 'hyper_transformer')
        default: Default configuration if subsystem not found
        
    Returns:
        Subsystem configuration dictionary
    """
    config = get_config(subsystem, default or {})
    
    if not isinstance(config, dict):
        return default or {}
        
    return config

def validate_configuration() -> bool:
    """
    Validate the current configuration
    
    Returns:
        True if configuration is valid, False otherwise
    """
    try:
        config_manager = get_config_manager()
        # Validation is done automatically during configuration merging
        return True
    except ConfigurationError as e:
        print(f"Configuration validation failed: {e}")
        return False
    except Exception as e:
        print(f"Configuration validation error: {e}")
        return False

def optimize_for_environment(environment: str):
    """
    Optimize configuration for specific environment
    
    Args:
        environment: Target environment name
    """
    config_manager = get_config_manager()
    
    if environment.lower() == 'production':
        # Production optimizations
        config_manager.set('system.logging.level', 'WARNING')
        config_manager.set('system.performance.mixed_precision', True)
        config_manager.set('system.monitoring.metrics_collection', True)
        config_manager.set('system.performance.compilation_enabled', True)
        
    elif environment.lower() == 'development':
        # Development optimizations
        config_manager.set('system.logging.level', 'DEBUG')
        config_manager.set('system.monitoring.performance_profiling', True)
        config_manager.set('core_neural.neuroplasticity.stdp_enabled', True)
        
    elif environment.lower() == 'testing':
        # Testing optimizations
        config_manager.set('system.logging.level', 'INFO')
        config_manager.set('system.performance.batch_size', 4)
        config_manager.set('meta_cognitive.chain_of_thought.max_paths', 2)
        
    elif environment.lower() == 'training':
        # Training optimizations
        config_manager.set('system.performance.mixed_precision', True)
        config_manager.set('system.performance.gradient_accumulation_steps', 4)
        config_manager.set('core_neural.neuroplasticity.learning_rate', 0.001)
        config_manager.set('hyper_transformer.dynamic_attention.evolution_rate', 0.01)

# Quick access functions for common configurations
def get_neural_config() -> Dict[str, Any]:
    """Get core neural architecture configuration"""
    return get_subsystem_config('core_neural')

def get_transformer_config() -> Dict[str, Any]:
    """Get hyper-dimensional transformer configuration"""
    return get_subsystem_config('hyper_transformer')

def get_diffusion_config() -> Dict[str, Any]:
    """Get diffusion-based reasoning configuration"""
    return get_subsystem_config('diffusion_reasoning')

def get_metacognitive_config() -> Dict[str, Any]:
    """Get meta-cognitive system configuration"""
    return get_subsystem_config('meta_cognitive')

def get_neuromorphic_config() -> Dict[str, Any]:
    """Get neuromorphic processing configuration"""
    return get_subsystem_config('neuromorphic_processing')

def get_consciousness_config() -> Dict[str, Any]:
    """Get emergent consciousness configuration"""
    return get_subsystem_config('emergent_consciousness')

def get_neurosymbolic_config() -> Dict[str, Any]:
    """Get neuro-symbolic integration configuration"""
    return get_subsystem_config('neuro_symbolic')

def get_evolution_config() -> Dict[str, Any]:
    """Get self-evolution system configuration"""
    return get_subsystem_config('self_evolution')

def get_system_config() -> Dict[str, Any]:
    """Get system-level configuration"""
    return get_subsystem_config('system')

# Initialize configuration on import
try:
    _default_config_manager = load_configuration()
except Exception as e:
    print(f"Warning: Failed to load default configuration: {e}")
    _default_config_manager = None