"""
Configuration module for the ULTRA (Ultimate Learning & Thought Reasoning Architecture) system.

This module provides a centralized configuration management system for ULTRA, handling:
- Loading configuration files from YAML
- Providing configuration validation and type enforcement
- Managing environment-specific configurations
- Supporting runtime configuration updates
- Exposing a unified configuration interface for all system components

The configuration follows a hierarchical structure matching the system's architecture:
- Core Neural Architecture
- Hyper-Dimensional Transformer
- Diffusion-Based Reasoning
- Meta-Cognitive System
- Neuromorphic Processing Layer
- Emergent Consciousness Lattice
- Neuro-Symbolic Integration
- Self-Evolution System
"""

import os
import sys
import yaml
import json
import logging
import pathlib
import importlib
import copy
import re
import inspect
from typing import Any, Dict, List, Optional, Union, Tuple, Set, Type, Callable
from dataclasses import dataclass, field, asdict
from enum import Enum, auto
from functools import lru_cache
import numpy as np

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Constants
CONFIG_DIR = pathlib.Path(__file__).parent

DEFAULT_CONFIG_PATH = CONFIG_DIR / "default_config.yaml"
LOGGING_CONFIG_PATH = CONFIG_DIR / "logging_config.yaml"
SYSTEM_PARAMS_PATH = CONFIG_DIR / "system_params.yaml"

# Type definitions for enhanced static type checking
ConfigDict = Dict[str, Any]
ConfigPath = Union[str, pathlib.Path]


class ConfigurationError(Exception):
    """Base exception for configuration-related errors."""
    pass


class ValidationError(ConfigurationError):
    """Exception raised for configuration validation errors."""
    pass


class ConfigurationNotFoundError(ConfigurationError):
    """Exception raised when a configuration file is not found."""
    pass


class ConfigEnvironment(Enum):
    """Enumeration of possible execution environments."""
    DEVELOPMENT = auto()
    TESTING = auto()
    PRODUCTION = auto()


@dataclass
class NeuronParameters:
    """Parameters for neuronal dynamics."""
    tau_m: float = 20.0  # Membrane time constant in ms
    v_rest: float = -70.0  # Resting potential in mV
    v_threshold: float = -50.0  # Threshold potential in mV
    v_reset: float = -65.0  # Reset potential in mV
    refractory_period: float = 2.0  # Refractory period in ms
    r_membrane: float = 1.0  # Membrane resistance in MΩ
    c_membrane: float = 1.0  # Membrane capacitance in nF


@dataclass
class STDPParameters:
    """Parameters for spike-timing-dependent plasticity."""
    a_plus: float = 0.005  # LTP amplitude
    a_minus: float = 0.0025  # LTD amplitude
    tau_plus: float = 20.0  # LTP time constant in ms
    tau_minus: float = 20.0  # LTD time constant in ms
    w_max: float = 1.0  # Maximum synaptic weight
    w_min: float = 0.0  # Minimum synaptic weight
    nearest_spike_only: bool = False  # Consider only nearest spikes
    dendritic_delay: float = 1.0  # Dendritic delay in ms


@dataclass
class StructuralPlasticityParameters:
    """Parameters for structural plasticity."""
    creation_threshold: float = 0.8  # Threshold for synapse creation
    deletion_threshold: float = 0.01  # Threshold for synapse deletion
    creation_p0: float = 0.01  # Base probability for creation
    deletion_p1: float = 0.01  # Base probability for deletion
    update_interval: int = 1000  # Update interval in steps
    max_synapses_per_neuron: int = 10000  # Maximum synapses per neuron


@dataclass
class SynapticPruningParameters:
    """Parameters for synaptic pruning."""
    theta_pruning: float = 0.1  # Pruning threshold
    theta_usage: float = 0.1  # Usage threshold
    lambda_w: float = 0.5  # Weight of weight magnitude
    lambda_a: float = 0.3  # Weight of activity
    lambda_r: float = 0.2  # Weight of redundancy
    pruning_interval: int = 10000  # Pruning interval in steps
    pruning_rate: float = 0.01  # Maximum rate of pruning


@dataclass
class NeuromodulationParameters:
    """Parameters for neuromodulation."""
    dopamine_baseline: float = 1.0  # Baseline dopamine level
    serotonin_baseline: float = 1.0  # Baseline serotonin level
    norepinephrine_baseline: float = 1.0  # Baseline norepinephrine level
    acetylcholine_baseline: float = 1.0  # Baseline acetylcholine level
    dopamine_tau: float = 100.0  # Dopamine time constant
    serotonin_tau: float = 500.0  # Serotonin time constant
    norepinephrine_tau: float = 50.0  # Norepinephrine time constant
    acetylcholine_tau: float = 200.0  # Acetylcholine time constant
    plasticity_modulation: float = 0.5  # Effect on plasticity
    excitability_modulation: float = 0.2  # Effect on excitability


@dataclass
class OscillationParameters:
    """Parameters for neural oscillations."""
    theta_frequency: float = 6.0  # Theta rhythm in Hz
    alpha_frequency: float = 10.0  # Alpha rhythm in Hz
    beta_frequency: float = 20.0  # Beta rhythm in Hz
    gamma_frequency: float = 40.0  # Gamma rhythm in Hz
    theta_amplitude: float = 1.0  # Theta amplitude
    alpha_amplitude: float = 0.8  # Alpha amplitude
    beta_amplitude: float = 0.6  # Beta amplitude 
    gamma_amplitude: float = 0.4  # Gamma amplitude
    phase_coupling_strength: float = 0.3  # Cross-frequency coupling strength


@dataclass
class CoreNeuralConfig:
    """Configuration for the Core Neural Architecture subsystem."""
    enabled: bool = True
    neuron_model: str = "LIF"  # LIF, AdEx, or Izhikevich
    neuron_params: NeuronParameters = field(default_factory=NeuronParameters)
    network_size: Tuple[int, int, int] = (100, 100, 100)  # 3D dimensions
    excitatory_ratio: float = 0.8  # Ratio of excitatory neurons
    stdp_params: STDPParameters = field(default_factory=STDPParameters)
    structural_plasticity_params: StructuralPlasticityParameters = field(default_factory=StructuralPlasticityParameters)
    synaptic_pruning_params: SynapticPruningParameters = field(default_factory=SynapticPruningParameters)
    neuromodulation_params: NeuromodulationParameters = field(default_factory=NeuromodulationParameters)
    oscillation_params: OscillationParameters = field(default_factory=OscillationParameters)
    connectivity_sigma: float = 0.2  # Length constant for spatial connectivity
    connectivity_density: float = 0.1  # Overall connection density


@dataclass
class TransformerParameters:
    """Parameters for transformer architecture."""
    d_model: int = 512  # Embedding dimension
    n_heads: int = 8  # Number of attention heads
    d_ff: int = 2048  # Feed-forward dimension
    max_seq_length: int = 1024  # Maximum sequence length
    dropout: float = 0.1  # Dropout rate
    layer_norm_eps: float = 1e-5  # Layer normalization epsilon
    attention_temperature: float = 1.0  # Temperature for attention
    attention_mask_temperature: float = 1.0  # Temperature for mask adaptation


@dataclass
class RecursiveTransformerParameters:
    """Parameters for recursive transformer."""
    max_recursion_depth: int = 5  # Maximum recursion depth
    confidence_threshold: float = 0.8  # Confidence threshold to halt
    halting_regularization: float = 0.01  # Regularization for halting
    minimum_steps: int = 1  # Minimum number of recursion steps


@dataclass
class TemporalCausalParameters:
    """Parameters for temporal-causal transformer."""
    causal_bias_function: str = "exponential"  # exponential, linear, or custom
    temporal_encoding_type: str = "sinusoidal"  # sinusoidal, learned, or relative
    max_temporal_distance: int = 1000  # Maximum temporal distance
    causal_strength_init: float = 0.5  # Initial causal strength


@dataclass
class MultiScaleEmbeddingParameters:
    """Parameters for multi-scale knowledge embedding."""
    num_scales: int = 3  # Number of abstraction scales
    scale_dims: List[int] = field(default_factory=lambda: [512, 256, 128])  # Dimensions per scale
    scale_integration: str = "weighted_sum"  # weighted_sum, attention, or gating
    scale_transition_type: str = "nonlinear"  # linear or nonlinear


@dataclass
class HyperDimensionalTransformerConfig:
    """Configuration for the Hyper-Dimensional Transformer subsystem."""
    enabled: bool = True
    transformer_params: TransformerParameters = field(default_factory=TransformerParameters)
    recursive_transformer_params: RecursiveTransformerParameters = field(default_factory=RecursiveTransformerParameters)
    temporal_causal_params: TemporalCausalParameters = field(default_factory=TemporalCausalParameters)
    multi_scale_embedding_params: MultiScaleEmbeddingParameters = field(default_factory=MultiScaleEmbeddingParameters)
    dynamic_attention_learning_rate: float = 0.01  # Learning rate for attention evolution
    context_bias_weights: Tuple[float, float, float] = (0.4, 0.3, 0.3)  # Weights for task, history, knowledge
    cross_modal_projections: List[str] = field(default_factory=lambda: ["text", "image", "audio"])


@dataclass
class DiffusionParameters:
    """Parameters for diffusion process."""
    num_timesteps: int = 1000  # Number of diffusion steps
    beta_schedule: str = "linear"  # linear, cosine, or quadratic
    beta_start: float = 1e-4  # Starting value for beta schedule
    beta_end: float = 0.02  # Ending value for beta schedule
    mean_type: str = "eps"  # eps, x0, or v
    var_type: str = "fixed_small"  # fixed_small, fixed_large, or learned


@dataclass
class LatentSpaceParameters:
    """Parameters for thought latent space."""
    dimension: int = 1024  # Dimension of the latent space
    hierarchical_levels: int = 3  # Number of hierarchical levels
    semantics_weight: float = 1.0  # Weight for semantic loss
    relational_weight: float = 0.5  # Weight for relational loss
    compositional_weight: float = 0.3  # Weight for compositional loss
    hierarchical_weight: float = 0.2  # Weight for hierarchical loss


@dataclass
class BayesianParameters:
    """Parameters for Bayesian uncertainty quantification."""
    num_monte_carlo_samples: int = 100  # Number of MC samples
    prior_scale: float = 1.0  # Scale of prior distribution
    kl_weight: float = 0.1  # Weight for KL divergence term
    epistemic_threshold: float = 0.2  # Threshold for high epistemic uncertainty
    aleatoric_threshold: float = 0.3  # Threshold for high aleatoric uncertainty


@dataclass
class DiffusionBasedReasoningConfig:
    """Configuration for the Diffusion-Based Reasoning subsystem."""
    enabled: bool = True
    diffusion_params: DiffusionParameters = field(default_factory=DiffusionParameters)
    latent_space_params: LatentSpaceParameters = field(default_factory=LatentSpaceParameters)
    bayesian_params: BayesianParameters = field(default_factory=BayesianParameters)
    reverse_guidance_strength: float = 0.5  # Strength of goal-directed guidance
    inference_steps: int = 50  # Number of inference steps
    concept_similarity_threshold: float = 0.7  # Threshold for concept similarity
    constraint_satisfaction_weight: float = 1.0  # Weight for constraint satisfaction


@dataclass
class ChainOfThoughtParameters:
    """Parameters for multi-path chain of thought."""
    max_paths: int = 5  # Maximum number of reasoning paths
    beam_width: int = 3  # Beam width for expansions
    max_steps: int = 10  # Maximum reasoning steps
    temperature: float = 0.7  # Temperature for path selection
    coherence_weight: float = 0.5  # Weight for coherence evaluation
    validity_weight: float = 0.3  # Weight for logical validity
    progress_weight: float = 0.2  # Weight for reasoning progress
    diversity_weight: float = 0.1  # Weight for path diversity


@dataclass
class TreeOfThoughtParameters:
    """Parameters for tree of thought exploration."""
    exploration_param: float = 1.0  # UCB exploration parameter
    max_depth: int = 5  # Maximum tree depth
    branching_factor: int = 3  # Branching factor
    value_threshold: float = 0.3  # Value threshold for pruning
    diversity_threshold: float = 0.2  # Diversity threshold for pruning


@dataclass
class ReasoningGraphParameters:
    """Parameters for reasoning graphs."""
    max_nodes: int = 100  # Maximum number of nodes
    max_edges: int = 500  # Maximum number of edges
    relatedness_threshold: float = 0.7  # Threshold for node relatedness
    consistency_threshold: float = 0.8  # Threshold for consistency checking
    path_strength_decay: float = 0.9  # Decay factor for path strength


@dataclass
class SelfCritiqueParameters:
    """Parameters for self-critique loop."""
    iterations: int = 3  # Maximum iterations of critique
    substantial_critique_threshold: float = 0.6  # Threshold for substantial critique
    improvement_threshold: float = 0.2  # Threshold for significant improvement
    critique_aspects: List[str] = field(default_factory=lambda: ["logical", "factual", "completeness", "alternatives"])


@dataclass
class BiasDetectionParameters:
    """Parameters for bias detection and correction."""
    bias_detection_threshold: float = 0.7  # Threshold for bias detection
    bias_types: List[str] = field(default_factory=lambda: [
        "confirmation", "availability", "anchoring", "overconfidence", 
        "fundamental_attribution", "hindsight", "framing", "sunk_cost",
        "representativeness", "base_rate"
    ])


@dataclass
class MetaCognitiveConfig:
    """Configuration for the Meta-Cognitive System subsystem."""
    enabled: bool = True
    chain_of_thought_params: ChainOfThoughtParameters = field(default_factory=ChainOfThoughtParameters)
    tree_of_thought_params: TreeOfThoughtParameters = field(default_factory=TreeOfThoughtParameters)
    reasoning_graph_params: ReasoningGraphParameters = field(default_factory=ReasoningGraphParameters)
    self_critique_params: SelfCritiqueParameters = field(default_factory=SelfCritiqueParameters)
    bias_detection_params: BiasDetectionParameters = field(default_factory=BiasDetectionParameters)
    strategy_similarity_threshold: float = 0.7  # Threshold for strategy similarity
    problem_feature_extraction: str = "learned"  # manual or learned
    meta_learning_rate: float = 0.1  # Learning rate for meta-learning


@dataclass
class SNNParameters:
    """Parameters for spiking neural networks."""
    neuron_model: str = "LIF"  # LIF, AdEx, or Izhikevich
    tau: float = 10.0  # Time constant in ms
    v_threshold: float = 1.0  # Threshold potential
    v_reset: float = 0.0  # Reset potential
    refractory_period: float = 5.0  # Refractory period in ms
    surrogate_gradient_function: str = "fast_sigmoid"  # fast_sigmoid, arctangent, or super_exponential
    surrogate_gradient_scale: float = 10.0  # Scale factor for surrogate gradient


@dataclass
class EventBasedParameters:
    """Parameters for asynchronous event-based computing."""
    threshold: float = 0.1  # Threshold for event generation
    max_events_per_step: int = 1000  # Maximum events per step
    event_queue_size: int = 10000  # Size of event queue
    temporal_window: float = 10.0  # Temporal window for integration in ms


@dataclass
class MemristorParameters:
    """Parameters for memristor array."""
    r_on: float = 100.0  # Low resistance state in Ohms
    r_off: float = 10000.0  # High resistance state in Ohms
    k_p: float = 1e-4  # Positive voltage coefficient
    k_n: float = 1e-4  # Negative voltage coefficient
    nonlinearity: float = 1.0  # Nonlinearity of memristor response
    device_variation: float = 0.1  # Device-to-device variation


@dataclass
class ReservoirParameters:
    """Parameters for reservoir computing."""
    reservoir_size: int = 1000  # Size of reservoir
    spectral_radius: float = 0.9  # Spectral radius
    connectivity: float = 0.1  # Connectivity density
    leak_rate: float = 0.3  # Leak rate
    ridge_regression_lambda: float = 1e-6  # Regularization parameter
    washout_period: int = 100  # Initial washout period


@dataclass
class BrainRegionParameters:
    """Parameters for specialized brain region emulation."""
    regions: List[str] = field(default_factory=lambda: ["visual_cortex", "hippocampus", "prefrontal_cortex", "basal_ganglia"])
    visual_cortex_neurons: int = 100000  # Visual cortex neurons
    hippocampus_neurons: int = 30000  # Hippocampus neurons
    prefrontal_cortex_neurons: int = 40000  # Prefrontal cortex neurons
    basal_ganglia_neurons: int = 25000  # Basal ganglia neurons
    gabor_filters: int = 32  # Number of Gabor filters for V1


@dataclass
class NeuromorphicProcessingConfig:
    """Configuration for the Neuromorphic Processing Layer subsystem."""
    enabled: bool = True
    snn_params: SNNParameters = field(default_factory=SNNParameters)
    event_based_params: EventBasedParameters = field(default_factory=EventBasedParameters)
    memristor_params: MemristorParameters = field(default_factory=MemristorParameters)
    reservoir_params: ReservoirParameters = field(default_factory=ReservoirParameters)
    brain_region_params: BrainRegionParameters = field(default_factory=BrainRegionParameters)
    simulation_dt: float = 1.0  # Simulation time step in ms
    hardware_acceleration: str = "gpu"  # cpu, gpu, or neuromorphic


@dataclass
class SelfAwarenessParameters:
    """Parameters for self-awareness module."""
    capability_model_learning_rate: float = 0.1  # Learning rate for capability model
    confidence_calibration_data_points: int = 100  # Data points for confidence calibration
    knowledge_state_update_rate: float = 0.05  # Update rate for knowledge state
    limitation_threshold: float = 0.6  # Threshold for capability limitation


@dataclass
class IntentionalityParameters:
    """Parameters for intentionality system."""
    goal_priority_levels: int = 5  # Number of priority levels
    planning_horizon: int = 5  # Planning horizon
    goal_hierarchy_depth: int = 3  # Maximum goal hierarchy depth
    intention_selection_temperature: float = 0.5  # Temperature for intention selection


@dataclass
class IntegratedInformationParameters:
    """Parameters for integrated information matrix."""
    partition_search_method: str = "grid"  # grid, random, or exhaustive
    information_measure: str = "mutual_information"  # mutual_information or transfer_entropy
    integration_maximization_lr: float = 0.01  # Learning rate for integration maximization
    information_flow_alpha: float = 0.7  # Alpha parameter for information flow control
    information_flow_beta: float = 0.3  # Beta parameter for information flow control


@dataclass
class AttentionalParameters:
    """Parameters for attentional awareness."""
    novelty_weight: float = 0.3  # Weight for novelty
    relevance_weight: float = 0.4  # Weight for relevance
    uncertainty_weight: float = 0.2  # Weight for uncertainty
    gain_weight: float = 0.1  # Weight for potential gain
    attention_temperature: float = 0.5  # Temperature for attention allocation
    enhancement_factor: float = 0.5  # Processing enhancement factor
    habituation_time_constant: float = 1000.0  # Habituation time constant in ms


@dataclass
class GlobalWorkspaceParameters:
    """Parameters for global workspace."""
    competition_temperature: float = 0.2  # Temperature for workspace competition
    broadcast_influence: float = 0.5  # Influence of global workspace broadcast
    workspace_decay: float = 0.1  # Decay rate of workspace contents
    access_memory_learning_rate: float = 0.05  # Learning rate for access memory
    workspace_capacity: int = 7  # Capacity of workspace (Miller's number)


@dataclass
class EmergentConsciousnessConfig:
    """Configuration for the Emergent Consciousness Lattice subsystem."""
    enabled: bool = True
    self_awareness_params: SelfAwarenessParameters = field(default_factory=SelfAwarenessParameters)
    intentionality_params: IntentionalityParameters = field(default_factory=IntentionalityParameters)
    integrated_information_params: IntegratedInformationParameters = field(default_factory=IntegratedInformationParameters)
    attentional_params: AttentionalParameters = field(default_factory=AttentionalParameters)
    global_workspace_params: GlobalWorkspaceParameters = field(default_factory=GlobalWorkspaceParameters)
    consciousness_measure: str = "integrated_information"  # integrated_information or access_consciousness
    phenomenal_structure_enabled: bool = False  # Enable phenomenal structure modeling


@dataclass
class LogicalReasoningParameters:
    """Parameters for logical reasoning engine."""
    inference_methods: List[str] = field(default_factory=lambda: ["deductive", "inductive", "abductive"])
    uncertainty_handling: str = "probabilistic"  # probabilistic, fuzzy, or possibilistic
    max_inference_depth: int = 10  # Maximum inference depth
    reasoning_timeout: float = 10.0  # Timeout in seconds
    explanation_generation: bool = True  # Generate explanations


@dataclass
class SymbolicRepresentationParameters:
    """Parameters for symbolic representation learning."""
    neural_to_symbolic_architecture: str = "transformer"  # transformer, cnn, or mlp
    symbolic_to_neural_architecture: str = "transformer"  # transformer, gan, or vae
    consistency_weight: float = 1.0  # Weight for consistency loss
    structure_preservation_weight: float = 0.5  # Weight for structure preservation loss
    training_iterations: int = 10000  # Training iterations
    batch_size: int = 64  # Batch size


@dataclass
class BridgeParameters:
    """Parameters for neuro-symbolic bridge."""
    translation_model: str = "transformer"  # transformer, lstm, or attention
    semantic_alignment_method: str = "contrastive"  # contrastive, supervised, or reinforcement
    operation_mapping_learning_rate: float = 0.01  # Learning rate for operation mapping
    reasoning_coordination_alpha: float = 0.5  # Alpha for reasoning coordination
    symbol_grounding_method: str = "end_to_end"  # end_to_end or curriculum


@dataclass
class ProgramSynthesisParameters:
    """Parameters for program synthesis."""
    synthesis_algorithm: str = "neural"  # neural, deductive, inductive, or sketch-based
    max_program_length: int = 100  # Maximum program length
    max_synthesis_iterations: int = 1000  # Maximum synthesis iterations
    verification_method: str = "testing"  # testing, symbolic, or hybrid
    language: str = "python"  # python, lisp, or dsl


@dataclass
class NeuroSymbolicConfig:
    """Configuration for the Neuro-Symbolic Integration subsystem."""
    enabled: bool = True
    logical_reasoning_params: LogicalReasoningParameters = field(default_factory=LogicalReasoningParameters)
    symbolic_representation_params: SymbolicRepresentationParameters = field(default_factory=SymbolicRepresentationParameters)
    bridge_params: BridgeParameters = field(default_factory=BridgeParameters)
    program_synthesis_params: ProgramSynthesisParameters = field(default_factory=ProgramSynthesisParameters)
    reasoning_integration_strategy: str = "weighted"  # weighted, sequential, or parallel
    symbolic_vocabulary_size: int = 10000  # Size of symbolic vocabulary


@dataclass
class NASParameters:
    """Parameters for neural architecture search."""
    search_algorithm: str = "evolutionary"  # evolutionary, reinforcement, gradient, or bayesian
    search_space: str = "macro"  # macro or micro
    population_size: int = 100  # Population size for evolutionary search
    generations: int = 50  # Number of generations
    performance_metric: str = "accuracy"  # accuracy, efficiency, or combined
    weight_sharing: bool = True  # Use weight sharing
    search_iterations: int = 1000  # Number of search iterations


@dataclass
class SelfModificationParameters:
    """Parameters for self-modification protocols."""
    safety_verification_method: str = "formal"  # formal, empirical, or hybrid
    modification_planning_horizon: int = 3  # Planning horizon for modifications
    impact_prediction_model: str = "simulation"  # simulation, analytical, or learned
    sandbox_testing_iterations: int = 100  # Sandbox testing iterations
    rollback_threshold: float = 0.8  # Performance threshold for rollback
    modification_scope: List[str] = field(default_factory=lambda: ["parameters", "architecture", "algorithms"])


@dataclass
class ComputationalReflectionParameters:
    """Parameters for computational reflection."""
    code_representation_model: str = "graph"  # graph, sequence, or tree
    runtime_analysis_metrics: List[str] = field(default_factory=lambda: ["time", "memory", "errors"])
    performance_modeling_algorithm: str = "regression"  # regression, simulation, or analytical
    explanation_generation: bool = True  # Generate explanations
    reflection_depth: int = 3  # Depth of reflection


@dataclass
class EvolutionarySteering:
    """Parameters for evolutionary steering."""
    fitness_function_weights: Dict[str, float] = field(default_factory=lambda: {
        "accuracy": 0.5, "efficiency": 0.3, "robustness": 0.2
    })
    constraint_enforcement_method: str = "penalty"  # penalty, repair, or feasibility
    adaptation_heuristics: List[str] = field(default_factory=lambda: ["local", "global", "transfer"])
    progress_monitoring_window: int = 10  # Window size for progress monitoring
    evolutionary_pressure: float = 0.7  # Evolutionary pressure parameter


@dataclass
class SelfEvolutionConfig:
    """Configuration for the Self-Evolution System subsystem."""
    enabled: bool = True
    nas_params: NASParameters = field(default_factory=NASParameters)
    self_modification_params: SelfModificationParameters = field(default_factory=SelfModificationParameters)
    computational_reflection_params: ComputationalReflectionParameters = field(default_factory=ComputationalReflectionParameters)
    evolutionary_steering_params: EvolutionarySteering = field(default_factory=EvolutionarySteering)
    evolution_frequency: int = 10000  # Frequency of evolutionary steps
    evolution_trigger: str = "performance"  # performance, schedule, or manual


@dataclass
class SystemConfig:
    """Complete configuration for the ULTRA system."""
    version: str = "1.0.0"
    environment: ConfigEnvironment = ConfigEnvironment.DEVELOPMENT
    device: str = "cuda"  # cuda, cpu, or tpu
    precision: str = "float32"  # float32, float16, or bfloat16
    random_seed: int = 42  # Random seed for reproducibility
    log_level: str = "INFO"  # DEBUG, INFO, WARNING, ERROR, or CRITICAL
    
    # Subsystem configurations
    core_neural: CoreNeuralConfig = field(default_factory=CoreNeuralConfig)
    hyper_transformer: HyperDimensionalTransformerConfig = field(default_factory=HyperDimensionalTransformerConfig)
    diffusion_reasoning: DiffusionBasedReasoningConfig = field(default_factory=DiffusionBasedReasoningConfig)
    meta_cognitive: MetaCognitiveConfig = field(default_factory=MetaCognitiveConfig)
    neuromorphic_processing: NeuromorphicProcessingConfig = field(default_factory=NeuromorphicProcessingConfig)
    emergent_consciousness: EmergentConsciousnessConfig = field(default_factory=EmergentConsciousnessConfig)
    neuro_symbolic: NeuroSymbolicConfig = field(default_factory=NeuroSymbolicConfig)
    self_evolution: SelfEvolutionConfig = field(default_factory=SelfEvolutionConfig)
    
    # System-wide parameters
    max_memory_usage: str = "16GB"  # Maximum memory usage
    distributed_training: bool = False  # Enable distributed training
    checkpoint_interval: int = 1000  # Checkpoint interval in steps
    checkpoint_dir: str = "checkpoints"  # Directory for checkpoints
    monitoring_interval: int = 100  # Monitoring interval in steps
    profiling_enabled: bool = False  # Enable profiling


class ConfigValidator:
    """Configuration validator for type and value checking."""
    
    @staticmethod
    def validate_config(config: Any, config_class: Type) -> List[str]:
        """Validate config against its expected type and constraints."""
        errors = []
        
        # Skip validation in test mode or when explicitly disabled
        if (os.environ.get("ULTRA_SKIP_CONFIG_VALIDATION", "").lower() in ("1", "true", "yes") or
            os.environ.get("ULTRA_TEST_MODE", "").lower() in ("1", "true", "yes")):
            return errors
        
        # If config is already an instance of the expected class, it's valid
        if isinstance(config, config_class):
            return errors
        
        # If config is None, use default
        if config is None:
            return errors
        
        # Get expected fields from the dataclass
        if not hasattr(config_class, "__dataclass_fields__"):
            # Not a dataclass, skip validation
            return errors
            
        expected_fields = {f.name: f.type for f in config_class.__dataclass_fields__.values()}
        
        # Convert config to dict if it's a dataclass
        if hasattr(config, "__dataclass_fields__"):
            config_dict = asdict(config)
        elif isinstance(config, dict):
            config_dict = config
        else:
            errors.append(f"Config must be a dictionary or dataclass instance, got {type(config)}")
            return errors
            
        # Check for missing required fields (only check if dict is not empty)
        if config_dict:
            for field_name, field_type in expected_fields.items():
                if field_name not in config_dict:
                    # Skip missing fields - they'll use defaults
                    continue
                    
                # Get the actual value
                value = config_dict[field_name]
                
                # Check type (more lenient)
                if not ConfigValidator._check_type_lenient(value, field_type):
                    errors.append(f"Type mismatch for {field_name}: expected {field_type}, got {type(value)}")
                    continue
                    
                # If field is a dataclass, validate recursively
                if hasattr(field_type, "__dataclass_fields__") and isinstance(value, dict):
                    nested_errors = ConfigValidator.validate_config(value, field_type)
                    for error in nested_errors:
                        errors.append(f"{field_name}.{error}")
        
        return errors
    
    @staticmethod
    def _check_type_lenient(value: Any, expected_type: Type) -> bool:
        """More lenient type checking that allows for conversion."""
        try:
            # Handle None values
            if value is None:
                return True
                
            # Handle Union types
            origin = getattr(expected_type, "__origin__", None)
            if origin is Union:
                args = getattr(expected_type, "__args__", ())
                return any(ConfigValidator._check_type_lenient(value, arg) for arg in args)
            
            # Handle List, Dict, etc.
            if origin is list and isinstance(value, list):
                return True  # Accept any list for now
            
            if origin is dict and isinstance(value, dict):
                return True  # Accept any dict for now
            
            # Handle Tuple
            if origin is tuple and isinstance(value, tuple):
                return True  # Accept any tuple for now
            
            # Handle Enum - allow string values that can be converted
            if isinstance(expected_type, type) and hasattr(expected_type, "__mro__") and Enum in expected_type.__mro__:
                if isinstance(value, str):
                    try:
                        expected_type[value]
                        return True
                    except (KeyError, ValueError):
                        pass
                return isinstance(value, expected_type)
            
            # Handle dataclass types - allow dicts to be converted
            if hasattr(expected_type, "__dataclass_fields__"):
                return isinstance(value, (dict, expected_type))
                
            # Direct isinstance check for non-generic types
            if origin is None:
                if expected_type is Any:
                    return True
                if expected_type is ConfigDict and isinstance(value, dict):
                    return True
                # More lenient checking
                if expected_type in (int, float) and isinstance(value, (int, float)):
                    return True
                if expected_type is str and isinstance(value, str):
                    return True
                if expected_type is bool and isinstance(value, bool):
                    return True
                return isinstance(value, expected_type)
                
            return True  # Default to accepting for unknown types
            
        except Exception:
            return True  # If we can't check, assume it's okay


class ConfigManager:
    """
    Central configuration manager for the ULTRA system.
    
    Handles loading, validating, and accessing configuration settings
    for all subsystems and components.
    """
    
    _instance = None
    
    def __new__(cls, *args, **kwargs):
        """Implement singleton pattern."""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self, config_path: Optional[ConfigPath] = None):
        """Initialize the configuration manager."""
        if self._initialized:
            return
            
        self._initialized = True
        self._config = None
        self._environment = self._detect_environment()
        self._config_path = config_path or DEFAULT_CONFIG_PATH
        self._runtime_overrides = {}
        
        # Load the default configuration
        self.load_config()
        
    @staticmethod
    def _detect_environment() -> ConfigEnvironment:
        """Detect the current execution environment."""
        env_var = os.environ.get("ULTRA_ENVIRONMENT", "development").lower()
        if env_var == "production":
            return ConfigEnvironment.PRODUCTION
        elif env_var == "testing":
            return ConfigEnvironment.TESTING
        else:
            return ConfigEnvironment.DEVELOPMENT
    
    def load_config(self) -> None:
        """Load configuration from file and apply environment settings."""
        try:
            # Load default config (if exists)
            config_dict = {}
            if self._config_path.exists():
                try:
                    config_dict = self._load_yaml_config(self._config_path)
                except Exception as e:
                    logger.warning(f"Failed to load config file {self._config_path}: {e}")
                    config_dict = {}
            
            # Apply environment-specific overrides
            env_config_path = self._get_environment_config_path()
            if env_config_path.exists():
                try:
                    env_config = self._load_yaml_config(env_config_path)
                    config_dict = self._deep_merge(config_dict, env_config)
                except Exception as e:
                    logger.warning(f"Failed to load environment config: {e}")
                    
            # Apply system parameters
            if SYSTEM_PARAMS_PATH.exists():
                try:
                    system_params = self._load_yaml_config(SYSTEM_PARAMS_PATH)
                    config_dict = self._deep_merge(config_dict, system_params)
                except Exception as e:
                    logger.warning(f"Failed to load system params: {e}")
                    
            # Apply runtime overrides
            if self._runtime_overrides:
                config_dict = self._deep_merge(config_dict, self._runtime_overrides)
            
            # Convert to SystemConfig dataclass FIRST
            self._config = self._dict_to_config(config_dict, SystemConfig)
            
            # Then validate AFTER conversion (only if not in test mode)
            if not (os.environ.get("ULTRA_SKIP_CONFIG_VALIDATION", "").lower() in ("1", "true", "yes") or
                    os.environ.get("ULTRA_TEST_MODE", "").lower() in ("1", "true", "yes")):
                errors = ConfigValidator.validate_config(self._config, SystemConfig)
                if errors:
                    error_msg = "Configuration validation errors:\n" + "\n".join(errors)
                    logger.error(error_msg)
                    # Don't raise exception in development mode, just warn
                    if self._environment == ConfigEnvironment.PRODUCTION:
                        raise ValidationError(error_msg)
                    else:
                        logger.warning("Configuration validation failed, but continuing in development mode")
                        
            logger.info(f"Configuration loaded successfully from {self._config_path}")
                
        except Exception as e:
            if isinstance(e, ValidationError):
                raise
            logger.error(f"Failed to load configuration: {str(e)}")
            # Create default config if loading fails
            logger.info("Using default configuration")
            self._config = SystemConfig()
    
    def _get_environment_config_path(self) -> pathlib.Path:
        """Get the path to the environment-specific configuration file."""
        config_filename = self._config_path.stem
        env_name = self._environment.name.lower()
        return self._config_path.parent / f"{config_filename}.{env_name}.yaml"
    
    @staticmethod
    def _load_yaml_config(config_path: ConfigPath) -> dict:
        """Load a YAML configuration file."""
        path = pathlib.Path(config_path)
        if not path.exists():
            raise ConfigurationNotFoundError(f"Configuration file not found: {path}")
            
        try:
            with open(path, "r", encoding='utf-8') as file:
                content = file.read()
                # Handle empty files
                if not content.strip():
                    return {}
                return yaml.safe_load(content) or {}
        except yaml.YAMLError as e:
            logger.error(f"YAML parsing error in {path}: {e}")
            raise ConfigurationError(f"Invalid YAML in {path}: {e}")
        except Exception as e:
            logger.error(f"Error reading config file {path}: {e}")
            raise ConfigurationError(f"Failed to read {path}: {e}")
    
    @staticmethod
    def _deep_merge(base: dict, override: dict) -> dict:
        """Recursively merge two dictionaries, with override taking precedence."""
        result = copy.deepcopy(base)
        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = ConfigManager._deep_merge(result[key], value)
            else:
                result[key] = copy.deepcopy(value)
        return result
    
    def _dict_to_config(self, config_dict: dict, config_class: Type) -> Any:
        """Convert a dictionary to a configuration dataclass."""
        try:
            # Handle empty config
            if not config_dict:
                return config_class()
            
            # Get fields info
            if not hasattr(config_class, "__dataclass_fields__"):
                # Not a dataclass, return as-is
                return config_dict
                
            fields = {f.name: f.type for f in config_class.__dataclass_fields__.values()}
            
            # Process each field
            kwargs = {}
            for field_name, field_type in fields.items():
                if field_name in config_dict:
                    value = config_dict[field_name]
                    
                    # Handle nested dataclasses
                    if hasattr(field_type, "__dataclass_fields__") and isinstance(value, dict):
                        kwargs[field_name] = self._dict_to_config(value, field_type)
                    # Handle Enum - safely check if field_type is a class and an Enum
                    elif (isinstance(field_type, type) and 
                          hasattr(field_type, "__mro__") and 
                          Enum in field_type.__mro__ and 
                          not isinstance(value, Enum)):
                        try:
                            if isinstance(value, str):
                                kwargs[field_name] = field_type[value]
                            else:
                                kwargs[field_name] = field_type(value)
                        except (KeyError, ValueError, TypeError) as e:
                            # Get first enum value as default
                            try:
                                default_value = list(field_type)[0]
                                logger.warning(f"Invalid value for {field_name}: {value}. Using default: {default_value.name}")
                                kwargs[field_name] = default_value
                            except (IndexError, AttributeError):
                                logger.warning(f"Could not set default for enum field {field_name}")
                                # Skip this field, let dataclass use its default
                    else:
                        kwargs[field_name] = value
            
            return config_class(**kwargs)
            
        except Exception as e:
            logger.warning(f"Failed to convert dict to {config_class.__name__}: {e}")
            # Return default instance if conversion fails
            try:
                return config_class()
            except Exception:
                logger.error(f"Failed to create default {config_class.__name__}")
                return None
    
    def get_config(self) -> SystemConfig:
        """Get the current system configuration."""
        if self._config is None:
            return SystemConfig()
        return copy.deepcopy(self._config)
    
    def update_config(self, config_path: Optional[ConfigPath] = None,
                      validate: bool = True) -> None:
        """Update the configuration from a new file."""
        if config_path:
            self._config_path = pathlib.Path(config_path)
        self.load_config()
        
        if validate and not (os.environ.get("ULTRA_SKIP_CONFIG_VALIDATION", "").lower() in ("1", "true", "yes")):
            errors = ConfigValidator.validate_config(self._config, SystemConfig)
            if errors:
                error_msg = "Configuration validation errors:\n" + "\n".join(errors)
                logger.error(error_msg)
                if self._environment == ConfigEnvironment.PRODUCTION:
                    raise ValidationError(error_msg)
    
    def set_runtime_override(self, path: str, value: Any) -> None:
        """
        Set a runtime configuration override.
        
        Parameters:
            path (str): Dot-separated path to the configuration item
            value (Any): New value to set
        """
        parts = path.split(".")
        current = self._runtime_overrides
        
        # Navigate to the parent object
        for part in parts[:-1]:
            if part not in current:
                current[part] = {}
            current = current[part]
            
        # Set the value
        current[parts[-1]] = value
        
        # Re-apply configuration
        self.load_config()
    
    def clear_runtime_overrides(self) -> None:
        """Clear all runtime configuration overrides."""
        self._runtime_overrides = {}
        self.load_config()
    
    def get_value(self, path: str, default: Any = None) -> Any:
        """
        Get a configuration value by path.
        
        Parameters:
            path (str): Dot-separated path to the configuration item
            default (Any): Default value if path not found
            
        Returns:
            Any: Configuration value
        """
        if self._config is None:
            return default
            
        parts = path.split(".")
        value = self._config
        
        try:
            for part in parts:
                value = getattr(value, part)
            return value
        except (AttributeError, KeyError):
            return default
    
    def to_dict(self) -> dict:
        """Convert the current configuration to a dictionary."""
        if self._config is None:
            return {}
        return asdict(self._config)
    
    def save_config(self, path: Optional[ConfigPath] = None) -> None:
        """Save the current configuration to a file."""
        save_path = path or self._config_path
        
        try:
            config_dict = self.to_dict()
            with open(save_path, "w", encoding='utf-8') as file:
                yaml.dump(config_dict, file, default_flow_style=False, sort_keys=False)
            logger.info(f"Configuration saved to {save_path}")
        except IOError as e:
            logger.error(f"Failed to save configuration: {str(e)}")
            raise ConfigurationError(f"Failed to save configuration: {str(e)}")


# Global configuration instance
try:
    config = ConfigManager()
except Exception as e:
    logger.error(f"Failed to initialize ConfigManager: {e}")
    # Create a minimal working config
    config = type('MinimalConfig', (), {
        'get_config': lambda: SystemConfig(),
        'get_value': lambda path, default=None: default,
        '_config': SystemConfig()
    })()


def get_config() -> SystemConfig:
    """Get the current system configuration."""
    try:
        return config.get_config()
    except Exception:
        return SystemConfig()


def setup_logging() -> None:
    """Set up logging based on the system configuration."""
    try:
        if LOGGING_CONFIG_PATH.exists():
            try:
                with open(LOGGING_CONFIG_PATH, "r", encoding='utf-8') as file:
                    logging_config = yaml.safe_load(file)
                logging.config.dictConfig(logging_config)
                logger.info("Logging configured from file")
            except (IOError, yaml.YAMLError) as e:
                logger.warning(f"Failed to load logging configuration: {str(e)}")
                logger.warning("Using default logging configuration")
        else:
            # Use basic configuration based on system config
            try:
                log_level = getattr(logging, config.get_value("log_level", "INFO"))
            except (AttributeError, TypeError):
                log_level = logging.INFO
                
            logging.basicConfig(
                level=log_level,
                format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            )
            logger.info("Using default logging configuration")
    except Exception as e:
        logger.warning(f"Error setting up logging: {e}")


def initialize() -> None:
    """Initialize the configuration system."""
    try:
        setup_logging()
        version = config.get_value('version', '1.0.0')
        env_name = getattr(config.get_value('environment', ConfigEnvironment.DEVELOPMENT), 'name', 'DEVELOPMENT')
        logger.info(f"ULTRA configuration initialized (version {version})")
        logger.info(f"Running in {env_name} environment")
    except Exception as e:
        logger.warning(f"Error during initialization: {e}")


# Initialize logging when module is imported (with error handling)
try:
    initialize()
except Exception as e:
    logger.warning(f"Failed to initialize config system: {e}")
    logger.info("Continuing with minimal configuration")