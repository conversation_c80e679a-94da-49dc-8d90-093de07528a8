# ULTRA: Ultimate Learning & Thought Reasoning Architecture
# Default Configuration

# System-wide parameters
version: "1.0.0"
environment: DEVELOPMENT  # DEVELOPMENT, TESTING, or PRODUCTION
device: "cuda"  # cuda, cpu, or tpu
precision: "float32"  # float32, float16, or bfloat16
random_seed: 42
log_level: "INFO"  # DEBUG, INFO, WARNING, ERROR, or CRITICAL
max_memory_usage: "16GB"
distributed_training: false
checkpoint_interval: 1000  # Steps between checkpoints
checkpoint_dir: "checkpoints"
monitoring_interval: 100  # Steps between monitoring logs
profiling_enabled: false

# -------------------------------------------------------------------------
# Core Neural Architecture Configuration
# -------------------------------------------------------------------------
core_neural:
  enabled: true
  neuron_model: "LIF"  # LIF, AdEx, or <PERSON><PERSON><PERSON>vich
  network_size: [100, 100, 100]  # 3D dimensions of the network
  excitatory_ratio: 0.8  # Ratio of excitatory neurons
  connectivity_sigma: 0.2  # Length constant for spatial connectivity
  connectivity_density: 0.1  # Overall connection density

  # Neuron parameters
  neuron_params:
    tau_m: 20.0  # Membrane time constant in ms
    v_rest: -70.0  # Resting potential in mV
    v_threshold: -50.0  # Threshold potential in mV
    v_reset: -65.0  # Reset potential in mV
    refractory_period: 2.0  # Refractory period in ms
    r_membrane: 1.0  # Membrane resistance in MΩ
    c_membrane: 1.0  # Membrane capacitance in nF

  # Spike-Timing-Dependent Plasticity parameters
  stdp_params:
    a_plus: 0.005  # LTP amplitude
    a_minus: 0.0025  # LTD amplitude
    tau_plus: 20.0  # LTP time constant in ms
    tau_minus: 20.0  # LTD time constant in ms
    w_max: 1.0  # Maximum synaptic weight
    w_min: 0.0  # Minimum synaptic weight
    nearest_spike_only: false  # Consider only nearest spikes
    dendritic_delay: 1.0  # Dendritic delay in ms

  # Structural plasticity parameters
  structural_plasticity_params:
    creation_threshold: 0.8  # Threshold for synapse creation
    deletion_threshold: 0.01  # Threshold for synapse deletion
    creation_p0: 0.01  # Base probability for creation
    deletion_p1: 0.01  # Base probability for deletion
    update_interval: 1000  # Update interval in steps
    max_synapses_per_neuron: 10000  # Maximum synapses per neuron

  # Synaptic pruning parameters
  synaptic_pruning_params:
    theta_pruning: 0.1  # Pruning threshold
    theta_usage: 0.1  # Usage threshold
    lambda_w: 0.5  # Weight of weight magnitude
    lambda_a: 0.3  # Weight of activity
    lambda_r: 0.2  # Weight of redundancy
    pruning_interval: 10000  # Pruning interval in steps
    pruning_rate: 0.01  # Maximum rate of pruning

  # Neuromodulation parameters
  neuromodulation_params:
    dopamine_baseline: 1.0  # Baseline dopamine level
    serotonin_baseline: 1.0  # Baseline serotonin level
    norepinephrine_baseline: 1.0  # Baseline norepinephrine level
    acetylcholine_baseline: 1.0  # Baseline acetylcholine level
    dopamine_tau: 100.0  # Dopamine time constant
    serotonin_tau: 500.0  # Serotonin time constant
    norepinephrine_tau: 50.0  # Norepinephrine time constant
    acetylcholine_tau: 200.0  # Acetylcholine time constant
    plasticity_modulation: 0.5  # Effect on plasticity
    excitability_modulation: 0.2  # Effect on excitability

  # Oscillation parameters
  oscillation_params:
    theta_frequency: 6.0  # Theta rhythm in Hz
    alpha_frequency: 10.0  # Alpha rhythm in Hz
    beta_frequency: 20.0  # Beta rhythm in Hz
    gamma_frequency: 40.0  # Gamma rhythm in Hz
    theta_amplitude: 1.0  # Theta amplitude
    alpha_amplitude: 0.8  # Alpha amplitude
    beta_amplitude: 0.6  # Beta amplitude
    gamma_amplitude: 0.4  # Gamma amplitude
    phase_coupling_strength: 0.3  # Cross-frequency coupling strength

# -------------------------------------------------------------------------
# Hyper-Dimensional Transformer Configuration
# -------------------------------------------------------------------------
hyper_transformer:
  enabled: true
  dynamic_attention_learning_rate: 0.01  # Learning rate for attention evolution
  context_bias_weights: [0.4, 0.3, 0.3]  # Weights for task, history, knowledge
  cross_modal_projections: ["text", "image", "audio"]  # Modalities for projection

  # Transformer parameters
  transformer_params:
    d_model: 512  # Embedding dimension
    n_heads: 8  # Number of attention heads
    d_ff: 2048  # Feed-forward dimension
    max_seq_length: 1024  # Maximum sequence length
    dropout: 0.1  # Dropout rate
    layer_norm_eps: 1.0e-5  # Layer normalization epsilon
    attention_temperature: 1.0  # Temperature for attention
    attention_mask_temperature: 1.0  # Temperature for mask adaptation

  # Recursive transformer parameters
  recursive_transformer_params:
    max_recursion_depth: 5  # Maximum recursion depth
    confidence_threshold: 0.8  # Confidence threshold to halt
    halting_regularization: 0.01  # Regularization for halting
    minimum_steps: 1  # Minimum number of recursion steps

  # Temporal-causal transformer parameters
  temporal_causal_params:
    causal_bias_function: "exponential"  # exponential, linear, or custom
    temporal_encoding_type: "sinusoidal"  # sinusoidal, learned, or relative
    max_temporal_distance: 1000  # Maximum temporal distance
    causal_strength_init: 0.5  # Initial causal strength

  # Multi-scale knowledge embedding parameters
  multi_scale_embedding_params:
    num_scales: 3  # Number of abstraction scales
    scale_dims: [512, 256, 128]  # Dimensions per scale
    scale_integration: "weighted_sum"  # weighted_sum, attention, or gating
    scale_transition_type: "nonlinear"  # linear or nonlinear

# -------------------------------------------------------------------------
# Diffusion-Based Reasoning Configuration
# -------------------------------------------------------------------------
diffusion_reasoning:
  enabled: true
  reverse_guidance_strength: 0.5  # Strength of goal-directed guidance
  inference_steps: 50  # Number of inference steps
  concept_similarity_threshold: 0.7  # Threshold for concept similarity
  constraint_satisfaction_weight: 1.0  # Weight for constraint satisfaction

  # Diffusion process parameters
  diffusion_params:
    num_timesteps: 1000  # Number of diffusion steps
    beta_schedule: "linear"  # linear, cosine, or quadratic
    beta_start: 1.0e-4  # Starting value for beta schedule
    beta_end: 0.02  # Ending value for beta schedule
    mean_type: "eps"  # eps, x0, or v
    var_type: "fixed_small"  # fixed_small, fixed_large, or learned

  # Thought latent space parameters
  latent_space_params:
    dimension: 1024  # Dimension of the latent space
    hierarchical_levels: 3  # Number of hierarchical levels
    semantics_weight: 1.0  # Weight for semantic loss
    relational_weight: 0.5  # Weight for relational loss
    compositional_weight: 0.3  # Weight for compositional loss
    hierarchical_weight: 0.2  # Weight for hierarchical loss

  # Bayesian uncertainty quantification parameters
  bayesian_params:
    num_monte_carlo_samples: 100  # Number of MC samples
    prior_scale: 1.0  # Scale of prior distribution
    kl_weight: 0.1  # Weight for KL divergence term
    epistemic_threshold: 0.2  # Threshold for high epistemic uncertainty
    aleatoric_threshold: 0.3  # Threshold for high aleatoric uncertainty

# -------------------------------------------------------------------------
# Meta-Cognitive System Configuration
# -------------------------------------------------------------------------
meta_cognitive:
  enabled: true
  strategy_similarity_threshold: 0.7  # Threshold for strategy similarity
  problem_feature_extraction: "learned"  # manual or learned
  meta_learning_rate: 0.1  # Learning rate for meta-learning

  # Multi-path chain of thought parameters
  chain_of_thought_params:
    max_paths: 5  # Maximum number of reasoning paths
    beam_width: 3  # Beam width for expansions
    max_steps: 10  # Maximum reasoning steps
    temperature: 0.7  # Temperature for path selection
    coherence_weight: 0.5  # Weight for coherence evaluation
    validity_weight: 0.3  # Weight for logical validity
    progress_weight: 0.2  # Weight for reasoning progress
    diversity_weight: 0.1  # Weight for path diversity

  # Tree of thought exploration parameters
  tree_of_thought_params:
    exploration_param: 1.0  # UCB exploration parameter
    max_depth: 5  # Maximum tree depth
    branching_factor: 3  # Branching factor
    value_threshold: 0.3  # Value threshold for pruning
    diversity_threshold: 0.2  # Diversity threshold for pruning

  # Reasoning graphs parameters
  reasoning_graph_params:
    max_nodes: 100  # Maximum number of nodes
    max_edges: 500  # Maximum number of edges
    relatedness_threshold: 0.7  # Threshold for node relatedness
    consistency_threshold: 0.8  # Threshold for consistency checking
    path_strength_decay: 0.9  # Decay factor for path strength

  # Self-critique loop parameters
  self_critique_params:
    iterations: 3  # Maximum iterations of critique
    substantial_critique_threshold: 0.6  # Threshold for substantial critique
    improvement_threshold: 0.2  # Threshold for significant improvement
    critique_aspects:
      - "logical"
      - "factual"
      - "completeness"
      - "alternatives"

  # Bias detection parameters
  bias_detection_params:
    bias_detection_threshold: 0.7  # Threshold for bias detection
    bias_types:
      - "confirmation"
      - "availability"
      - "anchoring"
      - "overconfidence"
      - "fundamental_attribution"
      - "hindsight"
      - "framing"
      - "sunk_cost"
      - "representativeness"
      - "base_rate"

# -------------------------------------------------------------------------
# Neuromorphic Processing Layer Configuration
# -------------------------------------------------------------------------
neuromorphic_processing:
  enabled: true
  simulation_dt: 1.0  # Simulation time step in ms
  hardware_acceleration: "gpu"  # cpu, gpu, or neuromorphic

  # Spiking neural networks parameters
  snn_params:
    neuron_model: "LIF"  # LIF, AdEx, or Izhikevich
    tau: 10.0  # Time constant in ms
    v_threshold: 1.0  # Threshold potential
    v_reset: 0.0  # Reset potential
    refractory_period: 5.0  # Refractory period in ms
    surrogate_gradient_function: "fast_sigmoid"  # fast_sigmoid, arctangent, or super_exponential
    surrogate_gradient_scale: 10.0  # Scale factor for surrogate gradient

  # Asynchronous event-based computing parameters
  event_based_params:
    threshold: 0.1  # Threshold for event generation
    max_events_per_step: 1000  # Maximum events per step
    event_queue_size: 10000  # Size of event queue
    temporal_window: 10.0  # Temporal window for integration in ms

  # Memristor array parameters
  memristor_params:
    r_on: 100.0  # Low resistance state in Ohms
    r_off: 10000.0  # High resistance state in Ohms
    k_p: 1.0e-4  # Positive voltage coefficient
    k_n: 1.0e-4  # Negative voltage coefficient
    nonlinearity: 1.0  # Nonlinearity of memristor response
    device_variation: 0.1  # Device-to-device variation

  # Reservoir computing parameters
  reservoir_params:
    reservoir_size: 1000  # Size of reservoir
    spectral_radius: 0.9  # Spectral radius
    connectivity: 0.1  # Connectivity density
    leak_rate: 0.3  # Leak rate
    ridge_regression_lambda: 1.0e-6  # Regularization parameter
    washout_period: 100  # Initial washout period

  # Specialized brain region emulation parameters
  brain_region_params:
    regions:
      - "visual_cortex"
      - "hippocampus"
      - "prefrontal_cortex"
      - "basal_ganglia"
    visual_cortex_neurons: 100000  # Visual cortex neurons
    hippocampus_neurons: 30000  # Hippocampus neurons
    prefrontal_cortex_neurons: 40000  # Prefrontal cortex neurons
    basal_ganglia_neurons: 25000  # Basal ganglia neurons
    gabor_filters: 32  # Number of Gabor filters for V1

# -------------------------------------------------------------------------
# Emergent Consciousness Lattice Configuration
# -------------------------------------------------------------------------
emergent_consciousness:
  enabled: true
  consciousness_measure: "integrated_information"  # integrated_information or access_consciousness
  phenomenal_structure_enabled: false  # Enable phenomenal structure modeling

  # Self-awareness module parameters
  self_awareness_params:
    capability_model_learning_rate: 0.1  # Learning rate for capability model
    confidence_calibration_data_points: 100  # Data points for confidence calibration
    knowledge_state_update_rate: 0.05  # Update rate for knowledge state
    limitation_threshold: 0.6  # Threshold for capability limitation

  # Intentionality system parameters
  intentionality_params:
    goal_priority_levels: 5  # Number of priority levels
    planning_horizon: 5  # Planning horizon
    goal_hierarchy_depth: 3  # Maximum goal hierarchy depth
    intention_selection_temperature: 0.5  # Temperature for intention selection

  # Integrated information matrix parameters
  integrated_information_params:
    partition_search_method: "grid"  # grid, random, or exhaustive
    information_measure: "mutual_information"  # mutual_information or transfer_entropy
    integration_maximization_lr: 0.01  # Learning rate for integration maximization
    information_flow_alpha: 0.7  # Alpha parameter for information flow control
    information_flow_beta: 0.3  # Beta parameter for information flow control

  # Attentional awareness parameters
  attentional_params:
    novelty_weight: 0.3  # Weight for novelty
    relevance_weight: 0.4  # Weight for relevance
    uncertainty_weight: 0.2  # Weight for uncertainty
    gain_weight: 0.1  # Weight for potential gain
    attention_temperature: 0.5  # Temperature for attention allocation
    enhancement_factor: 0.5  # Processing enhancement factor
    habituation_time_constant: 1000.0  # Habituation time constant in ms

  # Global workspace parameters
  global_workspace_params:
    competition_temperature: 0.2  # Temperature for workspace competition
    broadcast_influence: 0.5  # Influence of global workspace broadcast
    workspace_decay: 0.1  # Decay rate of workspace contents
    access_memory_learning_rate: 0.05  # Learning rate for access memory
    workspace_capacity: 7  # Capacity of workspace (Miller's number)

# -------------------------------------------------------------------------
# Neuro-Symbolic Integration Configuration
# -------------------------------------------------------------------------
neuro_symbolic:
  enabled: true
  reasoning_integration_strategy: "weighted"  # weighted, sequential, or parallel
  symbolic_vocabulary_size: 10000  # Size of symbolic vocabulary

  # Logical reasoning engine parameters
  logical_reasoning_params:
    inference_methods:
      - "deductive"
      - "inductive"
      - "abductive"
    uncertainty_handling: "probabilistic"  # probabilistic, fuzzy, or possibilistic
    max_inference_depth: 10  # Maximum inference depth
    reasoning_timeout: 10.0  # Timeout in seconds
    explanation_generation: true  # Generate explanations

  # Symbolic representation learning parameters
  symbolic_representation_params:
    neural_to_symbolic_architecture: "transformer"  # transformer, cnn, or mlp
    symbolic_to_neural_architecture: "transformer"  # transformer, gan, or vae
    consistency_weight: 1.0  # Weight for consistency loss
    structure_preservation_weight: 0.5  # Weight for structure preservation loss
    training_iterations: 10000  # Training iterations
    batch_size: 64  # Batch size

  # Neuro-symbolic bridge parameters
  bridge_params:
    translation_model: "transformer"  # transformer, lstm, or attention
    semantic_alignment_method: "contrastive"  # contrastive, supervised, or reinforcement
    operation_mapping_learning_rate: 0.01  # Learning rate for operation mapping
    reasoning_coordination_alpha: 0.5  # Alpha for reasoning coordination
    symbol_grounding_method: "end_to_end"  # end_to_end or curriculum

  # Program synthesis parameters
  program_synthesis_params:
    synthesis_algorithm: "neural"  # neural, deductive, inductive, or sketch-based
    max_program_length: 100  # Maximum program length
    max_synthesis_iterations: 1000  # Maximum synthesis iterations
    verification_method: "testing"  # testing, symbolic, or hybrid
    language: "python"  # python, lisp, or dsl

# -------------------------------------------------------------------------
# Self-Evolution System Configuration
# -------------------------------------------------------------------------
self_evolution:
  enabled: true
  evolution_frequency: 10000  # Frequency of evolutionary steps
  evolution_trigger: "performance"  # performance, schedule, or manual

  # Neural architecture search parameters
  nas_params:
    search_algorithm: "evolutionary"  # evolutionary, reinforcement, gradient, or bayesian
    search_space: "macro"  # macro or micro
    population_size: 100  # Population size for evolutionary search
    generations: 50  # Number of generations
    performance_metric: "accuracy"  # accuracy, efficiency, or combined
    weight_sharing: true  # Use weight sharing
    search_iterations: 1000  # Number of search iterations

  # Self-modification protocols parameters
  self_modification_params:
    safety_verification_method: "formal"  # formal, empirical, or hybrid
    modification_planning_horizon: 3  # Planning horizon for modifications
    impact_prediction_model: "simulation"  # simulation, analytical, or learned
    sandbox_testing_iterations: 100  # Sandbox testing iterations
    rollback_threshold: 0.8  # Performance threshold for rollback
    modification_scope:
      - "parameters"
      - "architecture"
      - "algorithms"

  # Computational reflection parameters
  computational_reflection_params:
    code_representation_model: "graph"  # graph, sequence, or tree
    runtime_analysis_metrics:
      - "time"
      - "memory"
      - "errors"
    performance_modeling_algorithm: "regression"  # regression, simulation, or analytical
    explanation_generation: true  # Generate explanations
    reflection_depth: 3  # Depth of reflection

  # Evolutionary steering parameters
  evolutionary_steering_params:
    fitness_function_weights:
      accuracy: 0.5
      efficiency: 0.3
      robustness: 0.2
    constraint_enforcement_method: "penalty"  # penalty, repair, or feasibility
    adaptation_heuristics:
      - "local"
      - "global"
      - "transfer"
    progress_monitoring_window: 10  # Window size for progress monitoring
    evolutionary_pressure: 0.7  # Evolutionary pressure parameter