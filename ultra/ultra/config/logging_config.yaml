# ULTRA: Ultimate Learning & Thought Reasoning Architecture
# Logging Configuration

# Root logger configuration
version: 1
disable_existing_loggers: false

# Formatters define how log messages look
formatters:
  standard:
    format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    datefmt: '%Y-%m-%d %H:%M:%S'
  
  detailed:
    format: '%(asctime)s - %(name)s - %(levelname)s - %(pathname)s:%(lineno)d - %(funcName)s - %(message)s'
    datefmt: '%Y-%m-%d %H:%M:%S'
  
  json:
    # JSON formatter for machine parsing
    format: '{"timestamp": "%(asctime)s", "name": "%(name)s", "level": "%(levelname)s", "message": "%(message)s", "module": "%(module)s", "lineno": %(lineno)d, "function": "%(funcName)s"}'
    datefmt: '%Y-%m-%d %H:%M:%S'

# Handlers define where logs go (console, file, etc.)
handlers:
  console:
    class: logging.StreamHandler
    level: INFO
    formatter: standard
    stream: ext://sys.stdout

  file:
    class: logging.handlers.RotatingFileHandler
    level: DEBUG
    formatter: detailed
    filename: logs/ultra.log
    maxBytes: 10485760  # 10MB
    backupCount: 20
    encoding: utf8
  
  error_file:
    class: logging.handlers.RotatingFileHandler
    level: ERROR
    formatter: detailed
    filename: logs/error.log
    maxBytes: 10485760  # 10MB
    backupCount: 20
    encoding: utf8
  
  json_file:
    class: logging.handlers.RotatingFileHandler
    level: INFO
    formatter: json
    filename: logs/ultra.json
    maxBytes: 10485760  # 10MB
    backupCount: 20
    encoding: utf8

  performance_file:
    class: logging.handlers.RotatingFileHandler
    level: INFO
    formatter: json
    filename: logs/performance.json
    maxBytes: 10485760  # 10MB
    backupCount: 10
    encoding: utf8

  # Null handler for silencing specific loggers
  null:
    class: logging.NullHandler

# Loggers for different components of the system
loggers:
  # Main Ultra system logger
  ultra:
    level: INFO
    handlers: [console, file, error_file, json_file]
    propagate: false
  
  # Core Neural Architecture
  ultra.core_neural:
    level: INFO
    handlers: [console, file, error_file]
    propagate: false
    
  ultra.core_neural.neuromorphic_core:
    level: INFO
    handlers: [file]
    propagate: false
    
  ultra.core_neural.neuroplasticity:
    level: INFO
    handlers: [file]
    propagate: false
    
  ultra.core_neural.pruning:
    level: INFO
    handlers: [file]
    propagate: false
    
  ultra.core_neural.neuromodulation:
    level: INFO
    handlers: [file]
    propagate: false
    
  ultra.core_neural.timing:
    level: INFO
    handlers: [file]
    propagate: false
  
  # Hyper-Dimensional Transformer
  ultra.hyper_transformer:
    level: INFO
    handlers: [console, file, error_file]
    propagate: false
    
  ultra.hyper_transformer.attention:
    level: INFO
    handlers: [file]
    propagate: false
    
  ultra.hyper_transformer.recursive:
    level: INFO
    handlers: [file]
    propagate: false
    
  ultra.hyper_transformer.temporal_causal:
    level: INFO
    handlers: [file]
    propagate: false
    
  ultra.hyper_transformer.multi_scale:
    level: INFO
    handlers: [file]
    propagate: false
  
  # Diffusion-Based Reasoning
  ultra.diffusion_reasoning:
    level: INFO
    handlers: [console, file, error_file]
    propagate: false
    
  ultra.diffusion_reasoning.conceptual:
    level: INFO
    handlers: [file]
    propagate: false
    
  ultra.diffusion_reasoning.latent_space:
    level: INFO
    handlers: [file]
    propagate: false
    
  ultra.diffusion_reasoning.reverse:
    level: INFO
    handlers: [file]
    propagate: false
    
  ultra.diffusion_reasoning.bayesian:
    level: INFO
    handlers: [file]
    propagate: false
  
  # Meta-Cognitive System
  ultra.meta_cognitive:
    level: INFO
    handlers: [console, file, error_file]
    propagate: false
    
  ultra.meta_cognitive.chain_of_thought:
    level: INFO
    handlers: [file]
    propagate: false
    
  ultra.meta_cognitive.tree_of_thought:
    level: INFO
    handlers: [file]
    propagate: false
    
  ultra.meta_cognitive.reasoning_graph:
    level: INFO
    handlers: [file]
    propagate: false
    
  ultra.meta_cognitive.self_critique:
    level: INFO
    handlers: [file]
    propagate: false
    
  ultra.meta_cognitive.bias_detection:
    level: INFO
    handlers: [file]
    propagate: false
  
  # Neuromorphic Processing Layer
  ultra.neuromorphic_processing:
    level: INFO
    handlers: [console, file, error_file]
    propagate: false
    
  ultra.neuromorphic_processing.snn:
    level: INFO
    handlers: [file]
    propagate: false
    
  ultra.neuromorphic_processing.event_based:
    level: INFO
    handlers: [file]
    propagate: false
    
  ultra.neuromorphic_processing.memristor:
    level: INFO
    handlers: [file]
    propagate: false
    
  ultra.neuromorphic_processing.reservoir:
    level: INFO
    handlers: [file]
    propagate: false
    
  ultra.neuromorphic_processing.brain_region:
    level: INFO
    handlers: [file]
    propagate: false
  
  # Emergent Consciousness Lattice
  ultra.emergent_consciousness:
    level: INFO
    handlers: [console, file, error_file]
    propagate: false
    
  ultra.emergent_consciousness.self_awareness:
    level: INFO
    handlers: [file]
    propagate: false
    
  ultra.emergent_consciousness.intentionality:
    level: INFO
    handlers: [file]
    propagate: false
    
  ultra.emergent_consciousness.integrated_information:
    level: INFO
    handlers: [file]
    propagate: false
    
  ultra.emergent_consciousness.attentional:
    level: INFO
    handlers: [file]
    propagate: false
    
  ultra.emergent_consciousness.global_workspace:
    level: INFO
    handlers: [file]
    propagate: false
  
  # Neuro-Symbolic Integration
  ultra.neuro_symbolic:
    level: INFO
    handlers: [console, file, error_file]
    propagate: false
    
  ultra.neuro_symbolic.logical_reasoning:
    level: INFO
    handlers: [file]
    propagate: false
    
  ultra.neuro_symbolic.symbolic_representation:
    level: INFO
    handlers: [file]
    propagate: false
    
  ultra.neuro_symbolic.bridge:
    level: INFO
    handlers: [file]
    propagate: false
    
  ultra.neuro_symbolic.program_synthesis:
    level: INFO
    handlers: [file]
    propagate: false
  
  # Self-Evolution System
  ultra.self_evolution:
    level: INFO
    handlers: [console, file, error_file]
    propagate: false
    
  ultra.self_evolution.nas:
    level: INFO
    handlers: [file]
    propagate: false
    
  ultra.self_evolution.self_modification:
    level: INFO
    handlers: [file]
    propagate: false
    
  ultra.self_evolution.computational_reflection:
    level: INFO
    handlers: [file]
    propagate: false
    
  ultra.self_evolution.evolutionary_steering:
    level: INFO
    handlers: [file]
    propagate: false
  
  # Utilities and supporting components
  ultra.config:
    level: INFO
    handlers: [console, file, error_file]
    propagate: false
    
  ultra.utils:
    level: INFO
    handlers: [file]
    propagate: false
  
  # Performance monitoring
  ultra.performance:
    level: INFO
    handlers: [performance_file]
    propagate: false
  
  # Third-party libraries - limit verbosity
  matplotlib:
    level: WARNING
    handlers: [file]
    propagate: false
    
  numpy:
    level: WARNING
    handlers: [file]
    propagate: false
    
  torch:
    level: WARNING
    handlers: [file]
    propagate: false
    
  tensorflow:
    level: WARNING
    handlers: [file]
    propagate: false

# Root logger catches everything not handled by specific loggers
root:
  level: WARNING
  handlers: [console, file, error_file]
  propagate: true

# Environment-specific configurations
# These are dynamically loaded by the ConfigManager based on the current environment
# Development environment - more verbose
development:
  # Override root logger for development
  root:
    level: DEBUG
    
  # Make core loggers more verbose
  ultra:
    level: DEBUG
    
  # Make console output more verbose
  handlers:
    console:
      level: DEBUG

# Testing environment - focused on specifics
testing:
  # Special test-related loggers
  loggers:
    ultra.test:
      level: DEBUG
      handlers: [console, file]
      propagate: false
      
    # Capture all errors to console in testing
    handlers:
      console:
        level: DEBUG
        
      # Add a test-specific file
      test_file:
        class: logging.FileHandler
        level: DEBUG
        formatter: detailed
        filename: logs/test.log
        encoding: utf8

# Production environment - focused on important events and errors
production:
  # Less verbose in production
  root:
    level: WARNING
    
  # Make core loggers less verbose
  ultra:
    level: INFO
    
  # Console only shows warnings and above
  handlers:
    console:
      level: WARNING
      
  # Add remote logging in production (example)
  handlers:
    syslog:
      class: logging.handlers.SysLogHandler
      level: WARNING
      formatter: standard
      address: [localhost, 514]
      facility: local7