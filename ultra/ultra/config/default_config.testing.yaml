# ULTRA: Ultimate Learning & Thought Reasoning Architecture
# Testing Configuration

# System-wide parameters
environment: TESTING
device: "cuda"  # Use GPU for realistic performance testing
precision: "float32"  # Use full precision for testing/debugging
random_seed: 42  # Fixed seed for reproducibility
log_level: "DEBUG"  # Verbose logging for testing
max_memory_usage: "8GB"  # Conservative memory limit for testing
distributed_training: false  # Single-node testing for simplicity
checkpoint_interval: 100  # Frequent checkpointing for test validation
checkpoint_dir: "test_checkpoints"  # Separate directory for test checkpoints
monitoring_interval: 10  # Frequent monitoring for detailed analysis
profiling_enabled: true  # Enable profiling for performance testing

# -------------------------------------------------------------------------
# Core Neural Architecture Configuration
# -------------------------------------------------------------------------
core_neural:
  enabled: true
  neuron_model: "LIF"  # LIF model is most standard for testing
  network_size: [50, 50, 50]  # Smaller network for faster testing
  excitatory_ratio: 0.8
  connectivity_sigma: 0.2
  connectivity_density: 0.2  # Higher density for more thorough testing

  # Neuron parameters - standard testing values
  neuron_params:
    tau_m: 20.0
    v_rest: -70.0
    v_threshold: -50.0
    v_reset: -65.0
    refractory_period: 2.0
    r_membrane: 1.0
    c_membrane: 1.0

  # STDP parameters for testing - more prominent effects for validation
  stdp_params:
    a_plus: 0.01  # Higher amplitude for easier validation
    a_minus: 0.005  # Higher amplitude for easier validation
    tau_plus: 20.0
    tau_minus: 20.0
    w_max: 1.0
    w_min: 0.0
    nearest_spike_only: false
    dendritic_delay: 1.0

  # More frequent structural changes for testing
  structural_plasticity_params:
    creation_threshold: 0.7  # Lower threshold for more changes
    deletion_threshold: 0.05  # Higher threshold for more changes
    creation_p0: 0.02  # Higher probability for more thorough testing
    deletion_p1: 0.02  # Higher probability for more thorough testing
    update_interval: 100  # More frequent updates for testing
    max_synapses_per_neuron: 5000  # Reduced for testing efficiency

  # More aggressive pruning for testing edge cases
  synaptic_pruning_params:
    theta_pruning: 0.2  # Higher threshold for more aggressive pruning
    theta_usage: 0.2  # Higher threshold for more aggressive pruning
    lambda_w: 0.5
    lambda_a: 0.3
    lambda_r: 0.2
    pruning_interval: 500  # More frequent pruning for testing
    pruning_rate: 0.05  # Higher rate for testing edge cases

  # Exaggerated neuromodulation for testing
  neuromodulation_params:
    dopamine_baseline: 1.0
    serotonin_baseline: 1.0
    norepinephrine_baseline: 1.0
    acetylcholine_baseline: 1.0
    dopamine_tau: 50.0  # Faster dynamics for testing
    serotonin_tau: 250.0  # Faster dynamics for testing
    norepinephrine_tau: 25.0  # Faster dynamics for testing
    acetylcholine_tau: 100.0  # Faster dynamics for testing
    plasticity_modulation: 0.8  # Stronger modulation for testing
    excitability_modulation: 0.4  # Stronger modulation for testing

  # More pronounced oscillations for testing
  oscillation_params:
    theta_frequency: 6.0
    alpha_frequency: 10.0
    beta_frequency: 20.0
    gamma_frequency: 40.0
    theta_amplitude: 1.5  # Increased amplitude for testing
    alpha_amplitude: 1.2  # Increased amplitude for testing
    beta_amplitude: 1.0  # Increased amplitude for testing
    gamma_amplitude: 0.8  # Increased amplitude for testing
    phase_coupling_strength: 0.5  # Stronger coupling for testing

# -------------------------------------------------------------------------
# Hyper-Dimensional Transformer Configuration
# -------------------------------------------------------------------------
hyper_transformer:
  enabled: true
  dynamic_attention_learning_rate: 0.02  # Higher rate for faster testing
  context_bias_weights: [0.4, 0.3, 0.3]
  cross_modal_projections: ["text", "image", "audio"]

  # Smaller model for faster testing
  transformer_params:
    d_model: 256  # Smaller dimension for faster testing
    n_heads: 4  # Fewer heads for faster testing
    d_ff: 1024  # Smaller feed-forward for faster testing
    max_seq_length: 512  # Shorter sequences for testing
    dropout: 0.2  # Higher dropout for testing robustness
    layer_norm_eps: 1.0e-5
    attention_temperature: 1.2  # Higher temperature for testing
    attention_mask_temperature: 1.2  # Higher temperature for testing

  # More dynamic recursion for testing
  recursive_transformer_params:
    max_recursion_depth: 10  # Higher depth to test recursion limits
    confidence_threshold: 0.6  # Lower threshold for more recursion events
    halting_regularization: 0.005  # Lower value for testing
    minimum_steps: 2  # Higher minimum for testing

  # More explicit causality for testing
  temporal_causal_params:
    causal_bias_function: "exponential"
    temporal_encoding_type: "sinusoidal"
    max_temporal_distance: 500
    causal_strength_init: 0.8  # Stronger initialization for testing

  # Simplified scales for testing
  multi_scale_embedding_params:
    num_scales: 2  # Fewer scales for testing simplicity
    scale_dims: [256, 128]  # Aligned with reduced model dimension
    scale_integration: "weighted_sum"
    scale_transition_type: "nonlinear"

# -------------------------------------------------------------------------
# Diffusion-Based Reasoning Configuration
# -------------------------------------------------------------------------
diffusion_reasoning:
  enabled: true
  reverse_guidance_strength: 0.7  # Stronger guidance for testing
  inference_steps: 20  # Fewer steps for faster testing
  concept_similarity_threshold: 0.6  # Lower threshold for more matches in testing
  constraint_satisfaction_weight: 1.5  # Higher weight for testing

  # Accelerated diffusion for testing
  diffusion_params:
    num_timesteps: 100  # Fewer timesteps for faster testing
    beta_schedule: "linear"  # Simpler schedule for testing
    beta_start: 1.0e-4
    beta_end: 0.02
    mean_type: "eps"
    var_type: "fixed_small"

  # Smaller latent space for testing
  latent_space_params:
    dimension: 512  # Smaller dimension for faster testing
    hierarchical_levels: 2  # Fewer levels for testing simplicity
    semantics_weight: 1.0
    relational_weight: 0.5
    compositional_weight: 0.3
    hierarchical_weight: 0.2

  # Fewer samples for faster testing
  bayesian_params:
    num_monte_carlo_samples: 20  # Fewer samples for faster testing
    prior_scale: 1.0
    kl_weight: 0.1
    epistemic_threshold: 0.15  # Lower threshold for testing uncertainty handling
    aleatoric_threshold: 0.25  # Lower threshold for testing uncertainty handling

# -------------------------------------------------------------------------
# Meta-Cognitive System Configuration
# -------------------------------------------------------------------------
meta_cognitive:
  enabled: true
  strategy_similarity_threshold: 0.6  # Lower threshold for testing
  problem_feature_extraction: "manual"  # Manual for testing simplicity
  meta_learning_rate: 0.2  # Higher rate for faster learning in tests

  # More diverse paths for testing
  chain_of_thought_params:
    max_paths: 8  # More paths to test handling of multiple branches
    beam_width: 5  # Wider beam for testing
    max_steps: 5  # Fewer steps for faster testing
    temperature: 1.0  # Higher temperature for more diversity in testing
    coherence_weight: 0.4
    validity_weight: 0.3
    progress_weight: 0.2
    diversity_weight: 0.2  # Higher weight for more diverse testing

  # Deeper but narrower tree for testing
  tree_of_thought_params:
    exploration_param: 1.5  # Higher exploration for testing
    max_depth: 8  # Deeper tree for testing
    branching_factor: 2  # Narrow branching for faster testing
    value_threshold: 0.2  # Lower threshold for testing
    diversity_threshold: 0.1  # Lower threshold for testing

  # Smaller graph for testing
  reasoning_graph_params:
    max_nodes: 30  # Smaller graph for faster testing
    max_edges: 100  # Fewer edges for faster testing
    relatedness_threshold: 0.5  # Lower threshold for more connections
    consistency_threshold: 0.7
    path_strength_decay: 0.9

  # More iterations for thorough testing
  self_critique_params:
    iterations: 5  # More iterations to test convergence
    substantial_critique_threshold: 0.4  # Lower threshold for more critiques
    improvement_threshold: 0.1  # Lower threshold for more improvements
    critique_aspects:
      - "logical"
      - "factual"
      - "completeness"
      - "alternatives"

  # More sensitive bias detection for testing
  bias_detection_params:
    bias_detection_threshold: 0.5  # Lower threshold for more bias detections
    bias_types:
      - "confirmation"
      - "availability"
      - "anchoring"
      - "overconfidence"
      - "fundamental_attribution"
      - "hindsight"
      - "framing"
      - "sunk_cost"
      - "representativeness"
      - "base_rate"

# -------------------------------------------------------------------------
# Neuromorphic Processing Layer Configuration
# -------------------------------------------------------------------------
neuromorphic_processing:
  enabled: true
  simulation_dt: 0.1  # Finer time step for testing precision
  hardware_acceleration: "cpu"  # CPU for most consistent testing results

  # Multiple neuron models for testing
  snn_params:
    neuron_model: "AdEx"  # More complex model for testing
    tau: 10.0
    v_threshold: 1.0
    v_reset: 0.0
    refractory_period: 5.0
    surrogate_gradient_function: "arctangent"  # Different function for testing
    surrogate_gradient_scale: 10.0

  # Lower thresholds for more events during testing
  event_based_params:
    threshold: 0.05  # Lower threshold for more events
    max_events_per_step: 2000  # Higher limit for testing
    event_queue_size: 5000  # Smaller queue for testing
    temporal_window: 5.0  # Shorter window for testing

  # More diverse memristor properties for testing
  memristor_params:
    r_on: 100.0
    r_off: 10000.0
    k_p: 5.0e-4  # Higher value for testing
    k_n: 5.0e-4  # Higher value for testing
    nonlinearity: 1.5  # More nonlinearity for testing
    device_variation: 0.2  # Higher variation for testing robustness

  # Smaller reservoir for testing
  reservoir_params:
    reservoir_size: 200  # Smaller reservoir for faster testing
    spectral_radius: 0.8
    connectivity: 0.2  # Higher connectivity for testing
    leak_rate: 0.5  # Higher leak rate for testing
    ridge_regression_lambda: 1.0e-4  # Different regularization for testing
    washout_period: 50  # Shorter washout for testing

  # Minimal brain regions for testing
  brain_region_params:
    regions:
      - "visual_cortex"
      - "hippocampus"
    visual_cortex_neurons: 1000  # Reduced size for testing
    hippocampus_neurons: 500  # Reduced size for testing
    prefrontal_cortex_neurons: 800  # Reduced size for testing
    basal_ganglia_neurons: 400  # Reduced size for testing
    gabor_filters: 8  # Fewer filters for testing

# -------------------------------------------------------------------------
# Emergent Consciousness Lattice Configuration
# -------------------------------------------------------------------------
emergent_consciousness:
  enabled: true
  consciousness_measure: "integrated_information"
  phenomenal_structure_enabled: true  # Enable for comprehensive testing

  # Faster learning for testing
  self_awareness_params:
    capability_model_learning_rate: 0.2  # Higher rate for faster testing
    confidence_calibration_data_points: 50  # Fewer data points for testing
    knowledge_state_update_rate: 0.1  # Higher rate for testing
    limitation_threshold: 0.5  # Lower threshold for testing

  # Simplified goal structure for testing
  intentionality_params:
    goal_priority_levels: 3  # Fewer levels for testing
    planning_horizon: 3  # Shorter horizon for testing
    goal_hierarchy_depth: 2  # Shallower hierarchy for testing
    intention_selection_temperature: 0.8  # Higher temperature for testing

  # Simplified partitioning for testing
  integrated_information_params:
    partition_search_method: "random"  # Random search for faster testing
    information_measure: "mutual_information"
    integration_maximization_lr: 0.05  # Higher rate for testing
    information_flow_alpha: 0.7
    information_flow_beta: 0.3

  # Equal weights for testing
  attentional_params:
    novelty_weight: 0.25
    relevance_weight: 0.25
    uncertainty_weight: 0.25
    gain_weight: 0.25
    attention_temperature: 0.8  # Higher temperature for testing
    enhancement_factor: 0.8  # Higher factor for testing
    habituation_time_constant: 500.0  # Shorter time constant for testing

  # More dynamic workspace for testing
  global_workspace_params:
    competition_temperature: 0.5  # Higher temperature for testing
    broadcast_influence: 0.8  # Stronger influence for testing
    workspace_decay: 0.2  # Faster decay for testing
    access_memory_learning_rate: 0.1  # Higher rate for testing
    workspace_capacity: 5  # Smaller capacity for testing edge cases

# -------------------------------------------------------------------------
# Neuro-Symbolic Integration Configuration
# -------------------------------------------------------------------------
neuro_symbolic:
  enabled: true
  reasoning_integration_strategy: "sequential"  # Sequential for easier testing
  symbolic_vocabulary_size: 1000  # Smaller vocabulary for testing

  # All inference methods for testing
  logical_reasoning_params:
    inference_methods:
      - "deductive"
      - "inductive"
      - "abductive"
    uncertainty_handling: "fuzzy"  # Different handling for testing
    max_inference_depth: 5  # Limited depth for testing
    reasoning_timeout: 2.0  # Shorter timeout for testing
    explanation_generation: true

  # Fewer iterations for faster testing
  symbolic_representation_params:
    neural_to_symbolic_architecture: "mlp"  # Simpler architecture for testing
    symbolic_to_neural_architecture: "mlp"  # Simpler architecture for testing
    consistency_weight: 1.0
    structure_preservation_weight: 0.8  # Higher weight for testing
    training_iterations: 1000  # Fewer iterations for testing
    batch_size: 16  # Smaller batch size for testing

  # Alternative methods for testing
  bridge_params:
    translation_model: "lstm"  # Different model for testing
    semantic_alignment_method: "supervised"  # Different method for testing
    operation_mapping_learning_rate: 0.05  # Higher rate for testing
    reasoning_coordination_alpha: 0.5
    symbol_grounding_method: "curriculum"  # Different method for testing

  # Simplified synthesis for testing
  program_synthesis_params:
    synthesis_algorithm: "sketch-based"  # Different algorithm for testing
    max_program_length: 20  # Shorter programs for testing
    max_synthesis_iterations: 100  # Fewer iterations for testing
    verification_method: "symbolic"  # Different method for testing
    language: "lisp"  # Different language for testing

# -------------------------------------------------------------------------
# Self-Evolution System Configuration
# -------------------------------------------------------------------------
self_evolution:
  enabled: true
  evolution_frequency: 1000  # More frequent evolution for testing
  evolution_trigger: "schedule"  # Schedule-based for consistent testing

  # Minimal search for testing
  nas_params:
    search_algorithm: "gradient"  # Different algorithm for testing
    search_space: "micro"  # Different space for testing
    population_size: 10  # Smaller population for testing
    generations: 5  # Fewer generations for testing
    performance_metric: "combined"  # Combined metric for testing
    weight_sharing: false  # Disable for testing
    search_iterations: 100  # Fewer iterations for testing

  # All scopes for comprehensive testing
  self_modification_params:
    safety_verification_method: "empirical"  # Empirical for testing
    modification_planning_horizon: 2  # Shorter horizon for testing
    impact_prediction_model: "analytical"  # Different model for testing
    sandbox_testing_iterations: 20  # Fewer iterations for faster testing
    rollback_threshold: 0.7  # Lower threshold for testing
    modification_scope:
      - "parameters"
      - "architecture"
      - "algorithms"

  # Different models for testing
  computational_reflection_params:
    code_representation_model: "sequence"  # Different model for testing
    runtime_analysis_metrics:
      - "time"
      - "memory"
      - "errors"
      - "complexity"  # Additional metric for testing
    performance_modeling_algorithm: "simulation"  # Different algorithm for testing
    explanation_generation: true
    reflection_depth: 5  # Higher depth for testing

  # Different methods for testing
  evolutionary_steering_params:
    fitness_function_weights:
      accuracy: 0.33
      efficiency: 0.33
      robustness: 0.34
    constraint_enforcement_method: "repair"  # Different method for testing
    adaptation_heuristics:
      - "local"
      - "global"
      - "transfer"
      - "random"  # Additional heuristic for testing
    progress_monitoring_window: 5  # Smaller window for testing
    evolutionary_pressure: 1.0  # Maximum pressure for testing