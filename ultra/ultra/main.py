#!/usr/bin/env python3
"""
ULTRA: Ultimate Learning & Thought Reasoning Architecture
--------------------------------------------------------

Main entry point for running the ULTRA system. This module provides:

1. Command-line interface entry point
2. API server launch capabilities
3. Direct system initialization and execution
4. Service management for production deployments
5. Development utilities for testing and benchmarking

ULTRA integrates eight core subsystems:

- Core Neural Architecture: Biologically-inspired neural networks with neuroplasticity
- Hyper-Dimensional Transformer: Advanced attention mechanisms with temporal reasoning
- Diffusion-Based Reasoning: Reasoning through continuous concept spaces
- Meta-Cognitive System: Self-reflective reasoning and multi-path exploration
- Neuromorphic Processing Layer: Spike-based computation and event processing
- Emergent Consciousness Lattice: Global workspace and integrated information
- Neuro-Symbolic Integration: Bridging neural and symbolic computation
- Self-Evolution System: Architecture search and computational reflection

This module provides various execution modes for different use cases, including
interactive exploration, batch processing, API services, and embedded operation.
"""

import os
import sys
import time
import json
import yaml
import signal
import logging
import argparse
import threading
import multiprocessing
import asyncio
import uvicorn
import psutil
import numpy as np
import torch
from pathlib import Path
from typing import Dict, List, Optional, Union, Any, Tuple, Callable
from enum import Enum

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("ultra.main")

# Ensure ULTRA is in path
SCRIPT_DIR = Path(__file__).parent.parent.parent
if SCRIPT_DIR not in sys.path:
    sys.path.insert(0, str(SCRIPT_DIR))

# Import ULTRA components
try:
    from ultra.ultra import ULTRASystem, get_system
    from ultra.config import ConfigManager, get_config, setup_logging
    import ultra.cli as cli
    import ultra.api as api
except ImportError as e:
    logger.error(f"Failed to import ULTRA components: {e}")
    logger.error("Make sure ULTRA is installed or in the Python path")
    sys.exit(1)

# ULTRA version
__version__ = "1.0.0"

# Execution modes
class ExecutionMode(str, Enum):
    """
    Available execution modes for the ULTRA system.
    """
    INTERACTIVE = "interactive"
    API_SERVER = "api"
    CLI = "cli"
    SERVICE = "service"
    EMBEDDED = "embedded"
    BENCHMARK = "benchmark"

# Configuration for multiprocessing
# On Windows, use 'spawn' instead of 'fork' for more stability
if sys.platform == 'win32':
    multiprocessing.set_start_method('spawn', force=True)


class ULTRAMain:
    """
    Main class for running the ULTRA system. This class provides a centralized 
    entry point for initializing and executing the system in various modes.
    """
    
    def __init__(
        self,
        config_path: Optional[Union[str, Path]] = None,
        execution_mode: ExecutionMode = ExecutionMode.INTERACTIVE,
        log_level: str = "INFO",
        device: Optional[str] = None,
        verbose: bool = False
    ):
        """
        Initialize the ULTRA main system.
        
        Args:
            config_path: Path to configuration file
            execution_mode: Mode of execution
            log_level: Logging level
            device: Compute device (e.g., 'cuda', 'cpu')
            verbose: Enable verbose output
        """
        self.execution_mode = execution_mode
        self.verbose = verbose
        self.running = False
        self.system = None
        self.api_server = None
        
        # Set up logging
        numeric_level = getattr(logging, log_level.upper(), None)
        if not isinstance(numeric_level, int):
            logger.warning(f"Invalid log level: {log_level}, defaulting to INFO")
            numeric_level = logging.INFO
            
        logging.getLogger("ultra").setLevel(numeric_level)
        
        # Initialize configuration
        if config_path:
            try:
                self.config_manager = ConfigManager(config_path)
                setup_logging()
                logger.info(f"Loaded configuration from {config_path}")
            except Exception as e:
                logger.error(f"Failed to load configuration: {e}")
                logger.error("Using default configuration")
                self.config_manager = ConfigManager()
        else:
            self.config_manager = ConfigManager()
            setup_logging()
        
        # Apply device override if provided
        if device:
            self.config_manager.set_runtime_override("device", device)
        
        # Register signal handlers
        self._register_signal_handlers()
    
    def _register_signal_handlers(self):
        """Register signal handlers for graceful shutdown."""
        # Handle interrupt signal (Ctrl+C)
        signal.signal(signal.SIGINT, self._signal_handler)
        
        # Handle termination signal
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        # Handle broken pipe (output pipe closed)
        if hasattr(signal, 'SIGPIPE'):
            signal.signal(signal.SIGPIPE, signal.SIG_DFL)
    
    def _signal_handler(self, sig, frame):
        """
        Handle signals for graceful shutdown.
        
        Args:
            sig: Signal number
            frame: Current stack frame
        """
        signal_name = signal.Signals(sig).name
        logger.info(f"Received signal {signal_name} ({sig})")
        
        if sig in [signal.SIGINT, signal.SIGTERM]:
            self.shutdown()
            
            # Exit with appropriate code
            sys.exit(128 + sig)
    
    def _initialize_system(self):
        """Initialize the ULTRA system if not already initialized."""
        # Get system instance
        self.system = get_system()
        
        # Initialize if not already done
        if not getattr(self.system, "initialized", False):
            logger.info("Initializing ULTRA system...")
            try:
                self.system.initialize()
                logger.info("ULTRA system initialization complete")
            except Exception as e:
                logger.error(f"Failed to initialize ULTRA system: {e}")
                raise
    
    def run_interactive(self):
        """Run the system in interactive mode using the CLI."""
        logger.info("Starting interactive mode")
        self._initialize_system()
        
        # Create and start interactive session through CLI
        session = cli.InteractiveSession(self.system)
        session.start()
    
    def run_api_server(self, host: str = "0.0.0.0", port: int = 8000):
        """
        Run the system as an API server.
        
        Args:
            host: Host address to bind to
            port: Port to listen on
        """
        logger.info(f"Starting API server on {host}:{port}")
        self._initialize_system()
        
        # Set up and start API server
        self.running = True
        
        # Start server in a thread so we can handle signals
        server_thread = threading.Thread(
            target=self._run_api_server_thread,
            args=(host, port),
            daemon=True
        )
        server_thread.start()
        
        # Keep main thread alive to handle signals
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt")
            self.shutdown()
    
    def _run_api_server_thread(self, host: str, port: int):
        """
        Run the API server in a separate thread.
        
        Args:
            host: Host address to bind to
            port: Port to listen on
        """
        self.api_server = uvicorn.Server(
            config=uvicorn.Config(
                app=api.app,
                host=host,
                port=port,
                log_level="info"
            )
        )
        
        # Run server
        try:
            asyncio.run(self.api_server.serve())
        except Exception as e:
            logger.error(f"API server error: {e}")
            self.running = False
    
    def run_service(self):
        """Run the system as a long-running service."""
        logger.info("Starting service mode")
        self._initialize_system()
        
        # Set up monitoring and management
        self.running = True
        
        # Start monitoring thread
        monitor_thread = threading.Thread(
            target=self._service_monitor_thread,
            daemon=True
        )
        monitor_thread.start()
        
        # Main service loop
        try:
            while self.running:
                # Perform periodic tasks
                self._service_periodic_tasks()
                
                # Sleep to avoid busy waiting
                time.sleep(10)
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt")
            self.shutdown()
    
    def _service_monitor_thread(self):
        """Monitor system resources and performance in service mode."""
        while self.running:
            try:
                # Get process info
                process = psutil.Process(os.getpid())
                memory_info = process.memory_info()
                cpu_percent = process.cpu_percent(interval=1)
                
                # Log resource usage
                logger.debug(f"Memory usage: {memory_info.rss / (1024 * 1024):.2f} MB")
                logger.debug(f"CPU usage: {cpu_percent:.2f}%")
                
                # Check system health
                if hasattr(self.system, "get_metrics") and callable(self.system.get_metrics):
                    metrics = self.system.get_metrics()
                    logger.debug(f"System metrics: {metrics}")
                
                # Sleep for monitoring interval
                time.sleep(60)
            except Exception as e:
                logger.error(f"Error in monitor thread: {e}")
                time.sleep(10)  # Wait before retrying
    
    def _service_periodic_tasks(self):
        """Perform periodic tasks in service mode."""
        try:
            # Collect garbage
            import gc
            gc.collect()
            
            # Check for triggers that might require system evolution
            if hasattr(self.system, "subsystems") and "self_evolution" in self.system.subsystems:
                # Check if evolution is due (in a real system this would use more sophisticated triggers)
                if time.time() % 3600 < 10:  # Approximately once per hour
                    logger.info("Triggering periodic system evolution")
                    self.system.evolve()
        except Exception as e:
            logger.error(f"Error in periodic tasks: {e}")
    
    def run_benchmark(self, benchmark_config: Optional[Dict[str, Any]] = None):
        """
        Run benchmarking tests on the system.
        
        Args:
            benchmark_config: Configuration for benchmarking
        """
        logger.info("Starting benchmark mode")
        self._initialize_system()
        
        # Default benchmark configuration
        default_config = {
            "iterations": 10,
            "tasks": ["text_processing", "reasoning", "memory_usage"],
            "warmup_iterations": 3,
            "timeout": 300,  # 5 minutes
            "output_file": "benchmark_results.json"
        }
        
        # Apply custom config
        config = default_config.copy()
        if benchmark_config:
            config.update(benchmark_config)
        
        logger.info(f"Benchmark configuration: {config}")
        
        # Run benchmarks
        results = {}
        
        # Warmup
        logger.info(f"Running {config['warmup_iterations']} warmup iterations...")
        for i in range(config["warmup_iterations"]):
            self._run_single_benchmark("warmup", i)
        
        # Main benchmarks
        for task in config["tasks"]:
            task_results = []
            
            logger.info(f"Benchmarking task: {task}")
            for i in range(config["iterations"]):
                result = self._run_single_benchmark(task, i)
                task_results.append(result)
            
            # Compute statistics
            durations = [r["duration"] for r in task_results]
            results[task] = {
                "iterations": config["iterations"],
                "mean_duration": np.mean(durations),
                "median_duration": np.median(durations),
                "min_duration": np.min(durations),
                "max_duration": np.max(durations),
                "std_dev": np.std(durations),
                "raw_results": task_results
            }
        
        # Save results
        output_file = config["output_file"]
        with open(output_file, "w") as f:
            json.dump(results, f, indent=2)
        
        logger.info(f"Benchmark results saved to {output_file}")
        
        # Print summary
        print("\nBenchmark Summary:")
        print("==================")
        for task, task_results in results.items():
            print(f"\nTask: {task}")
            print(f"  Mean duration: {task_results['mean_duration']:.4f}s")
            print(f"  Median duration: {task_results['median_duration']:.4f}s")
            print(f"  Min duration: {task_results['min_duration']:.4f}s")
            print(f"  Max duration: {task_results['max_duration']:.4f}s")
            print(f"  Std deviation: {task_results['std_dev']:.4f}s")
    
    def _run_single_benchmark(self, task: str, iteration: int) -> Dict[str, Any]:
        """
        Run a single benchmark iteration.
        
        Args:
            task: Task to benchmark
            iteration: Iteration number
            
        Returns:
            Benchmark results
        """
        result = {
            "task": task,
            "iteration": iteration,
            "start_time": time.time(),
            "success": False,
            "error": None
        }
        
        try:
            if task == "warmup":
                # Simple processing task for warmup
                self.system.process("This is a warmup task for the ULTRA system.")
                
            elif task == "text_processing":
                # Text processing benchmark
                sample_text = "The quick brown fox jumps over the lazy dog. " * 10
                processed = self.system.process(sample_text)
                result["output_length"] = len(str(processed))
                
            elif task == "reasoning":
                # Reasoning benchmark
                query = "What are the first 5 prime numbers and why is each considered prime?"
                reasoned = self.system.reason(query)
                result["output_length"] = len(str(reasoned))
                
            elif task == "memory_usage":
                # Memory usage benchmark
                process = psutil.Process(os.getpid())
                memory_before = process.memory_info().rss / (1024 * 1024)  # MB
                
                # Perform memory-intensive operation
                large_data = [i for i in range(1000000)]
                processed = self.system.process(str(large_data[:100]))
                
                # Measure memory after
                memory_after = process.memory_info().rss / (1024 * 1024)  # MB
                
                result["memory_before"] = memory_before
                result["memory_after"] = memory_after
                result["memory_delta"] = memory_after - memory_before
                result["output_length"] = len(str(processed))
                
                # Clean up to avoid affecting subsequent tests
                del large_data
                import gc
                gc.collect()
            
            # Mark as successful
            result["success"] = True
            
        except Exception as e:
            logger.error(f"Benchmark error ({task}, iteration {iteration}): {e}")
            result["error"] = str(e)
        
        # Record end time and duration
        result["end_time"] = time.time()
        result["duration"] = result["end_time"] - result["start_time"]
        
        return result
    
    def run_embedded(self, callback: Optional[Callable] = None):
        """
        Run the system in embedded mode for integration with other applications.
        
        Args:
            callback: Optional callback function to run after initialization
        """
        logger.info("Starting embedded mode")
        self._initialize_system()
        
        # Mark as running but don't block
        self.running = True
        
        # Call callback if provided
        if callback and callable(callback):
            try:
                callback(self.system)
            except Exception as e:
                logger.error(f"Error in embedded callback: {e}")
    
    def run(self):
        """Run the system in the selected execution mode."""
        logger.info(f"Running ULTRA in {self.execution_mode} mode")
        
        try:
            if self.execution_mode == ExecutionMode.INTERACTIVE:
                self.run_interactive()
            elif self.execution_mode == ExecutionMode.API_SERVER:
                self.run_api_server()
            elif self.execution_mode == ExecutionMode.SERVICE:
                self.run_service()
            elif self.execution_mode == ExecutionMode.BENCHMARK:
                self.run_benchmark()
            elif self.execution_mode == ExecutionMode.EMBEDDED:
                self.run_embedded()
            elif self.execution_mode == ExecutionMode.CLI:
                # CLI mode is handled by separate entry point
                # This branch is for completeness
                logger.warning("CLI mode should be invoked via cli.main()")
                cli.main()
            else:
                logger.error(f"Unknown execution mode: {self.execution_mode}")
                sys.exit(1)
        except KeyboardInterrupt:
            logger.info("Interrupted by user")
            self.shutdown()
        except Exception as e:
            logger.error(f"Error running ULTRA: {e}", exc_info=True)
            self.shutdown()
            raise
    
    def shutdown(self):
        """Gracefully shut down the ULTRA system."""
        logger.info("Shutting down ULTRA system...")
        self.running = False
        
        # Shut down API server if running
        if self.api_server:
            logger.info("Shutting down API server...")
            if hasattr(self.api_server, "should_exit"):
                self.api_server.should_exit = True
        
        # Shut down ULTRA system
        if self.system:
            try:
                if hasattr(self.system, "shutdown") and callable(self.system.shutdown):
                    self.system.shutdown()
                    logger.info("ULTRA system shut down successfully")
            except Exception as e:
                logger.error(f"Error shutting down ULTRA system: {e}")
        
        logger.info("Shutdown complete")


def parse_arguments():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(
        description="ULTRA: Ultimate Learning & Thought Reasoning Architecture",
        epilog="""
Examples:
  # Run in interactive mode
  python -m ultra.main
  
  # Run as API server
  python -m ultra.main --mode api --port 8000
  
  # Run as a service
  python -m ultra.main --mode service
  
  # Run benchmarks
  python -m ultra.main --mode benchmark
        """
    )
    
    # Basic arguments
    parser.add_argument("--mode", "-m", type=str, choices=[m.value for m in ExecutionMode],
                      default=ExecutionMode.INTERACTIVE.value, help="Execution mode")
    parser.add_argument("--config", "-c", type=str, help="Path to configuration file")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose output")
    parser.add_argument("--device", "-d", type=str, help="Compute device (e.g., 'cuda', 'cpu')")
    parser.add_argument("--log-level", type=str, default="INFO",
                      choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
                      help="Set logging level")
    parser.add_argument("--version", action="version", version=f"ULTRA v{__version__}")
    
    # API server arguments
    parser.add_argument("--host", type=str, default="0.0.0.0", help="API server host (with --mode api)")
    parser.add_argument("--port", type=int, default=8000, help="API server port (with --mode api)")
    
    # Benchmark arguments
    parser.add_argument("--iterations", type=int, default=10,
                      help="Number of benchmark iterations (with --mode benchmark)")
    parser.add_argument("--output", type=str, default="benchmark_results.json",
                      help="Benchmark results output file (with --mode benchmark)")
    
    return parser.parse_args()


def check_environment():
    """Check system environment for compatibility issues."""
    logger.debug("Checking environment...")
    
    # Check Python version
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
        logger.warning(f"Python {python_version.major}.{python_version.minor} detected. "
                      "ULTRA is designed for Python 3.8 or higher.")
    
    # Check for PyTorch
    try:
        import torch
        logger.debug(f"PyTorch version: {torch.__version__}")
        
        if torch.cuda.is_available():
            logger.debug(f"CUDA available: {torch.cuda.get_device_name(0)}")
        else:
            logger.warning("CUDA not available. Running on CPU only.")
    except ImportError:
        logger.error("PyTorch not found. ULTRA requires PyTorch.")
        return False
    
    # Check available memory
    try:
        import psutil
        mem = psutil.virtual_memory()
        logger.debug(f"Available memory: {mem.available / (1024**3):.2f} GB of {mem.total / (1024**3):.2f} GB")
        
        if mem.available < 2 * (1024**3):  # Less than 2GB
            logger.warning("Low available memory. ULTRA may experience performance issues.")
    except ImportError:
        logger.warning("Could not check system memory (psutil not available).")
    
    return True


def main():
    """Main entry point for running ULTRA as a standalone application."""
    # Parse command-line arguments
    args = parse_arguments()
    
    # Check environment
    if not check_environment():
        sys.exit(1)
    
    # Create benchmark config if in benchmark mode
    benchmark_config = None
    if args.mode == ExecutionMode.BENCHMARK.value:
        benchmark_config = {
            "iterations": args.iterations,
            "output_file": args.output
        }
    
    # Create and run ULTRA system
    ultra_main = ULTRAMain(
        config_path=args.config,
        execution_mode=args.mode,
        log_level=args.log_level,
        device=args.device,
        verbose=args.verbose
    )
    
    if args.mode == ExecutionMode.API_SERVER.value:
        ultra_main.run_api_server(host=args.host, port=args.port)
    elif args.mode == ExecutionMode.BENCHMARK.value and benchmark_config:
        ultra_main.run_benchmark(benchmark_config)
    else:
        ultra_main.run()


if __name__ == "__main__":
    main()