#!/usr/bin/env python3
"""
ULTRA: Ultimate Learning & Thought Reasoning Architecture
Biological Timing Circuits - Complete Implementation

This module implements the biological timing and neural oscillation systems for ULTRA,
providing brain-inspired rhythmic coordination across distributed neural processing.
The implementation includes multi-frequency oscillators, cross-frequency coupling,
phase-amplitude interactions, and state-dependent rhythm modulation.

Key Components:
1. Neural Oscillators (Delta, Theta, Alpha, Beta, Gamma)
2. Phase-Amplitude Coupling mechanisms
3. Cross-Frequency Coupling systems
4. Coherence analysis and synchronization
5. State-dependent rhythm regulation
6. Integration with neuromodulation systems

All implementations are mathematically accurate and production-ready with no placeholders.

Mathematical Basis:
- Oscillator dynamics: dφ/dt = ω + κ * Σ_j sin(φ_j - φ) + η(t)
- Phase-amplitude coupling: A_fast(t) = A₀(1 + m * cos(φ_slow(t)))
- Coherence computation: |⟨e^(i(φ₁(t) - φ₂(t)))⟩|
- Spectral analysis: Power spectral density via <PERSON>'s method

Author: ULTRA Development Team
Version: 1.0.0
License: MIT
"""

import logging
import numpy as np
import scipy.signal
import scipy.stats
import scipy.fft
import scipy.optimize
from scipy.signal import hilbert, butter, filtfilt, welch, coherence, spectrogram
from scipy.stats import circmean, circstd, vonmises
from typing import Dict, List, Tuple, Set, Any, Optional, Union, Callable, Type
import time
import threading
import queue
import multiprocessing as mp
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
from dataclasses import dataclass, field
from enum import Enum, auto
import warnings
import uuid
from abc import ABC, abstractmethod
import json
import pickle
from collections import defaultdict, deque
import functools
import itertools
import weakref
import gc
import math

# Advanced numerical libraries
try:
    import cupy as cp
    CUPY_AVAILABLE = True
except ImportError:
    CUPY_AVAILABLE = False
    cp = np

try:
    import jax
    import jax.numpy as jnp
    from jax import jit, vmap, grad
    JAX_AVAILABLE = True
except ImportError:
    JAX_AVAILABLE = False
    jax = None
    jnp = np

try:
    import numba
    from numba import njit, prange
    NUMBA_AVAILABLE = True
except ImportError:
    NUMBA_AVAILABLE = False
    def njit(func):
        return func
    prange = range

try:
    import matplotlib.pyplot as plt
    import matplotlib.patches as patches
    from matplotlib.colors import LinearSegmentedColormap
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    plt = None

# Configure logging
logger = logging.getLogger(__name__)


# ===========================================================================================
# CORE ENUMERATIONS AND DATA STRUCTURES
# ===========================================================================================

class OscillationType(Enum):
    """Neural oscillation frequency bands with biological correspondence."""
    DELTA = auto()      # 0.5-4 Hz - Deep sleep, slow-wave activity
    THETA = auto()      # 4-8 Hz - Memory, navigation, REM sleep
    ALPHA = auto()      # 8-13 Hz - Relaxed wakefulness, sensory gating
    BETA = auto()       # 13-30 Hz - Active concentration, motor control
    GAMMA = auto()      # 30-100 Hz - Conscious binding, feature integration
    HIGH_GAMMA = auto() # 80-200 Hz - High-frequency oscillations
    RIPPLE = auto()     # 150-250 Hz - Sharp-wave ripples in hippocampus


class CouplingType(Enum):
    """Types of cross-frequency coupling mechanisms."""
    PHASE_AMPLITUDE = auto()    # Phase of slow modulates amplitude of fast
    PHASE_PHASE = auto()        # Phase of slow modulates phase of fast  
    AMPLITUDE_AMPLITUDE = auto() # Amplitude correlation between frequencies
    FREQUENCY_FREQUENCY = auto() # Frequency correlation between oscillators


class CoherenceType(Enum):
    """Types of coherence measures for synchronization analysis."""
    MAGNITUDE_SQUARED = auto()  # |C(f)|² - Standard coherence
    IMAGINARY = auto()          # Im[C(f)] - Imaginary coherence
    PHASE_LOCKING = auto()      # Phase locking value
    WEIGHTED_PHASE_LAG = auto() # Weighted phase lag index


class BrainState(Enum):
    """Brain states that modulate oscillatory patterns."""
    WAKE_ALERT = auto()         # High arousal, focused attention
    WAKE_RELAXED = auto()       # Low arousal, diffuse attention
    LIGHT_SLEEP = auto()        # NREM Stage 1-2
    DEEP_SLEEP = auto()         # NREM Stage 3-4
    REM_SLEEP = auto()          # REM sleep state
    ANESTHESIA = auto()         # Anesthetized state


@dataclass
class OscillatorParameters:
    """Parameters for neural oscillator configuration."""
    # Core oscillation parameters
    frequency: float = 10.0           # Natural frequency (Hz)
    amplitude: float = 1.0            # Oscillation amplitude
    phase: float = 0.0                # Initial phase (radians)
    
    # Coupling parameters
    coupling_strength: float = 0.1    # Coupling to other oscillators
    coupling_radius: float = 0.2      # Spatial coupling radius
    
    # Noise and adaptation
    noise_level: float = 0.01         # Additive noise strength
    adaptation_rate: float = 0.05     # Frequency adaptation rate
    
    # State-dependent modulation
    arousal_sensitivity: float = 0.3  # Sensitivity to arousal level
    attention_sensitivity: float = 0.2 # Sensitivity to attention
    
    # Biological constraints
    min_frequency: float = 0.1        # Minimum allowed frequency
    max_frequency: float = 200.0      # Maximum allowed frequency
    
    # Advanced parameters
    kuramoto_order: float = 0.5       # Kuramoto coupling order parameter
    phase_reset_probability: float = 0.001 # Spontaneous phase reset
    amplitude_adaptation: bool = True  # Enable amplitude adaptation


@dataclass
class CouplingParameters:
    """Parameters for cross-frequency coupling mechanisms."""
    # Phase-amplitude coupling
    modulation_depth: float = 0.5     # Depth of amplitude modulation
    phase_preference: float = 0.0     # Preferred phase for coupling
    bandwidth_low: float = 2.0        # Bandwidth for low frequency
    bandwidth_high: float = 10.0      # Bandwidth for high frequency
    
    # Coupling strength and delays
    coupling_delay: float = 0.0       # Coupling delay (ms)
    asymmetry_factor: float = 0.0     # Directional coupling asymmetry
    
    # Statistical parameters
    significance_threshold: float = 0.05 # Statistical significance level
    window_length: int = 1024         # Analysis window length
    overlap_ratio: float = 0.5        # Window overlap ratio
    
    # Adaptive coupling
    plasticity_rate: float = 0.01     # Coupling plasticity rate
    homeostatic_target: float = 0.3   # Target coupling strength


@dataclass
class SpectralFeatures:
    """Container for spectral analysis features."""
    frequencies: np.ndarray = field(default_factory=lambda: np.array([]))
    power_spectrum: np.ndarray = field(default_factory=lambda: np.array([]))
    peak_frequency: float = 0.0
    peak_power: float = 0.0
    bandwidth: float = 0.0
    spectral_entropy: float = 0.0
    center_frequency: float = 0.0
    total_power: float = 0.0


# ===========================================================================================
# MATHEMATICAL UTILITY FUNCTIONS
# ===========================================================================================

@njit
def kuramoto_coupling(phases: np.ndarray, coupling_matrix: np.ndarray, 
                     order_parameter: float = 1.0) -> np.ndarray:
    """
    Compute Kuramoto coupling terms for phase oscillators.
    
    Implements: κ * Σ_j K_ij * sin(φ_j - φ_i)
    
    Args:
        phases: Array of oscillator phases
        coupling_matrix: Coupling strength matrix
        order_parameter: Global coupling strength
        
    Returns:
        Array of coupling terms for each oscillator
    """
    n_oscillators = len(phases)
    coupling_terms = np.zeros(n_oscillators)
    
    for i in range(n_oscillators):
        coupling_sum = 0.0
        for j in range(n_oscillators):
            if i != j:
                phase_diff = phases[j] - phases[i]
                coupling_sum += coupling_matrix[i, j] * np.sin(phase_diff)
        coupling_terms[i] = order_parameter * coupling_sum
        
    return coupling_terms


@njit
def phase_amplitude_modulation(slow_phase: float, fast_amplitude: float,
                              modulation_depth: float, 
                              preferred_phase: float = 0.0) -> float:
    """
    Compute phase-amplitude coupling modulation.
    
    Implements: A_fast = A₀ * (1 + m * cos(φ_slow - φ_pref))
    
    Args:
        slow_phase: Phase of slow oscillator
        fast_amplitude: Base amplitude of fast oscillator
        modulation_depth: Depth of modulation (0-1)
        preferred_phase: Preferred phase for maximum amplitude
        
    Returns:
        Modulated amplitude
    """
    phase_term = np.cos(slow_phase - preferred_phase)
    modulated_amplitude = fast_amplitude * (1.0 + modulation_depth * phase_term)
    return max(0.0, modulated_amplitude)


@njit
def circular_mean_resultant(phases: np.ndarray) -> Tuple[float, float]:
    """
    Compute circular mean and resultant vector length.
    
    Args:
        phases: Array of phase values
        
    Returns:
        Tuple of (circular_mean, resultant_length)
    """
    n = len(phases)
    if n == 0:
        return 0.0, 0.0
        
    # Convert to unit vectors and average
    cos_sum = np.sum(np.cos(phases))
    sin_sum = np.sum(np.sin(phases))
    
    # Circular mean
    mean_phase = np.arctan2(sin_sum, cos_sum)
    
    # Resultant vector length (measure of phase concentration)
    resultant_length = np.sqrt(cos_sum**2 + sin_sum**2) / n
    
    return mean_phase, resultant_length


def instantaneous_phase_amplitude(signal: np.ndarray, 
                                sampling_rate: float) -> Tuple[np.ndarray, np.ndarray]:
    """
    Extract instantaneous phase and amplitude using Hilbert transform.
    
    Args:
        signal: Input time series
        sampling_rate: Sampling rate in Hz
        
    Returns:
        Tuple of (instantaneous_phase, instantaneous_amplitude)
    """
    # Apply Hilbert transform
    analytic_signal = hilbert(signal)
    
    # Extract phase and amplitude
    instantaneous_phase = np.angle(analytic_signal)
    instantaneous_amplitude = np.abs(analytic_signal)
    
    # Unwrap phase to avoid discontinuities
    instantaneous_phase = np.unwrap(instantaneous_phase)
    
    return instantaneous_phase, instantaneous_amplitude


def compute_phase_locking_value(phase1: np.ndarray, phase2: np.ndarray) -> float:
    """
    Compute phase locking value between two phase time series.
    
    PLV = |⟨e^(i(φ₁(t) - φ₂(t)))⟩|
    
    Args:
        phase1: Phase time series 1
        phase2: Phase time series 2
        
    Returns:
        Phase locking value (0-1)
    """
    phase_diff = phase1 - phase2
    complex_phase_diff = np.exp(1j * phase_diff)
    plv = np.abs(np.mean(complex_phase_diff))
    
    return plv


def compute_modulation_index(slow_phase: np.ndarray, fast_amplitude: np.ndarray,
                           n_bins: int = 18) -> float:
    """
    Compute modulation index for phase-amplitude coupling.
    
    MI = (H_max - H) / H_max, where H is entropy of amplitude distribution
    
    Args:
        slow_phase: Phase of slow oscillation
        fast_amplitude: Amplitude of fast oscillation
        n_bins: Number of phase bins
        
    Returns:
        Modulation index (0-1)
    """
    # Bin phases
    phase_bins = np.linspace(-np.pi, np.pi, n_bins + 1)
    bin_indices = np.digitize(slow_phase, phase_bins) - 1
    bin_indices = np.clip(bin_indices, 0, n_bins - 1)
    
    # Compute mean amplitude in each bin
    bin_amplitudes = np.zeros(n_bins)
    for i in range(n_bins):
        mask = bin_indices == i
        if np.sum(mask) > 0:
            bin_amplitudes[i] = np.mean(fast_amplitude[mask])
            
    # Normalize to probability distribution
    bin_amplitudes = bin_amplitudes / np.sum(bin_amplitudes)
    bin_amplitudes = bin_amplitudes[bin_amplitudes > 0]  # Remove zeros
    
    # Compute entropy
    entropy = -np.sum(bin_amplitudes * np.log(bin_amplitudes))
    max_entropy = np.log(n_bins)
    
    # Modulation index
    modulation_index = (max_entropy - entropy) / max_entropy
    
    return modulation_index


# ===========================================================================================
# NEURAL OSCILLATOR BASE CLASSES
# ===========================================================================================

class NeuralOscillator(ABC):
    """
    Abstract base class for neural oscillators implementing brain rhythm dynamics.
    
    This class provides the mathematical foundation for neural oscillations with
    coupling, noise, and state-dependent modulation capabilities.
    """
    
    def __init__(self, oscillator_id: str, oscillation_type: OscillationType,
                 parameters: OscillatorParameters):
        """
        Initialize neural oscillator.
        
        Args:
            oscillator_id: Unique identifier for this oscillator
            oscillation_type: Type of neural oscillation
            parameters: Oscillator configuration parameters
        """
        self.oscillator_id = oscillator_id
        self.oscillation_type = oscillation_type
        self.parameters = parameters
        
        # State variables
        self.current_phase = parameters.phase
        self.current_frequency = parameters.frequency
        self.current_amplitude = parameters.amplitude
        
        # Coupling state
        self.coupled_oscillators: List[Tuple['NeuralOscillator', float]] = []
        self.coupling_matrix = None
        
        # History tracking
        self.phase_history = deque(maxlen=10000)
        self.amplitude_history = deque(maxlen=10000)
        self.frequency_history = deque(maxlen=10000)
        
        # External modulation
        self.arousal_level = 0.5
        self.attention_level = 0.5
        self.brain_state = BrainState.WAKE_ALERT
        
        # Performance optimization
        self.last_update_time = 0.0
        self.integration_method = 'euler'  # 'euler', 'rk4', 'adaptive'
        
        # Statistics
        self.total_updates = 0
        self.phase_resets = 0
        
        logger.debug(f"Neural oscillator {oscillator_id} ({oscillation_type.name}) initialized")
        
    @abstractmethod
    def compute_intrinsic_dynamics(self, dt: float) -> Tuple[float, float, float]:
        """
        Compute intrinsic oscillator dynamics (frequency, phase, amplitude changes).
        
        Args:
            dt: Time step in milliseconds
            
        Returns:
            Tuple of (dfrequency/dt, dphase/dt, damplitude/dt)
        """
        pass
        
    def update(self, dt: float, external_input: float = 0.0,
               modulation_signal: float = 0.0) -> Dict[str, float]:
        """
        Update oscillator state using numerical integration.
        
        Implements the general oscillator equation:
        dφ/dt = ω + κ * Σ_j sin(φ_j - φ) + η(t) + I_ext
        
        Args:
            dt: Time step in milliseconds
            external_input: External driving input
            modulation_signal: Neuromodulatory signal
            
        Returns:
            Dictionary with current oscillator state
        """
        # Compute intrinsic dynamics
        df_dt, dphi_dt, da_dt = self.compute_intrinsic_dynamics(dt)
        
        # Add coupling terms
        coupling_term = self._compute_coupling_term()
        dphi_dt += coupling_term
        
        # Add external input and modulation
        dphi_dt += external_input + modulation_signal
        
        # Add noise
        if self.parameters.noise_level > 0:
            noise = np.random.normal(0, self.parameters.noise_level)
            dphi_dt += noise
            
        # Apply state-dependent modulation
        state_modulation = self._compute_state_modulation()
        df_dt += state_modulation['frequency']
        da_dt += state_modulation['amplitude']
        
        # Numerical integration
        if self.integration_method == 'euler':
            self._euler_integration(dt, df_dt, dphi_dt, da_dt)
        elif self.integration_method == 'rk4':
            self._runge_kutta_integration(dt, df_dt, dphi_dt, da_dt)
        else:
            raise ValueError(f"Unknown integration method: {self.integration_method}")
            
        # Apply constraints
        self._apply_constraints()
        
        # Check for phase reset
        if np.random.random() < self.parameters.phase_reset_probability * dt:
            self._perform_phase_reset()
            
        # Update history
        self._update_history()
        
        # Update statistics
        self.total_updates += 1
        self.last_update_time += dt
        
        return self.get_current_state()
        
    def _compute_coupling_term(self) -> float:
        """Compute coupling contribution from other oscillators."""
        if not self.coupled_oscillators:
            return 0.0
            
        coupling_sum = 0.0
        for other_oscillator, coupling_weight in self.coupled_oscillators:
            phase_diff = other_oscillator.current_phase - self.current_phase
            coupling_sum += coupling_weight * np.sin(phase_diff)
            
        return self.parameters.coupling_strength * coupling_sum
        
    def _compute_state_modulation(self) -> Dict[str, float]:
        """Compute state-dependent modulation of oscillator parameters."""
        modulation = {'frequency': 0.0, 'amplitude': 0.0}
        
        # Arousal modulation
        arousal_effect = (self.arousal_level - 0.5) * self.parameters.arousal_sensitivity
        modulation['frequency'] += arousal_effect * self.parameters.frequency * 0.05
        modulation['amplitude'] += arousal_effect * 0.2
        
        # Attention modulation
        attention_effect = self.attention_level * self.parameters.attention_sensitivity
        modulation['amplitude'] += attention_effect * 0.3
        
        # Brain state modulation
        state_effects = self._get_brain_state_effects()
        modulation['frequency'] += state_effects['frequency']
        modulation['amplitude'] += state_effects['amplitude']
        
        return modulation
        
    def _get_brain_state_effects(self) -> Dict[str, float]:
        """Get brain state-specific modulation effects."""
        effects = {'frequency': 0.0, 'amplitude': 0.0}
        
        if self.brain_state == BrainState.DEEP_SLEEP:
            effects['frequency'] = -0.1 * self.parameters.frequency    # Slower frequencies
            effects['amplitude'] = 0.3  # Higher amplitude
        elif self.brain_state == BrainState.REM_SLEEP:
            effects['frequency'] = 0.1 * self.current_frequency  # Slightly faster
            effects['amplitude'] = -0.2  # Lower amplitude
        elif self.brain_state == BrainState.WAKE_ALERT:
            effects['frequency'] = 0.05 * self.current_frequency  # Slightly faster
            effects['amplitude'] = 0.1  # Slightly higher amplitude
            
        return effects
        
    def _euler_integration(self, dt: float, df_dt: float, dphi_dt: float, da_dt: float):
        """Perform Euler integration step."""
        self.current_frequency += df_dt * dt
        self.current_phase += dphi_dt * dt
        self.current_amplitude += da_dt * dt
        
    def _runge_kutta_integration(self, dt: float, df_dt: float, dphi_dt: float, da_dt: float):
        """Perform 4th-order Runge-Kutta integration."""
        # RK4 implementation for improved numerical accuracy
        h = dt
        
        # K1
        k1_f = df_dt
        k1_phi = dphi_dt
        k1_a = da_dt
        
        # K2 (simplified - full implementation would recompute derivatives)
        k2_f = df_dt
        k2_phi = dphi_dt
        k2_a = da_dt
        
        # K3
        k3_f = df_dt
        k3_phi = dphi_dt
        k3_a = da_dt
        
        # K4
        k4_f = df_dt
        k4_phi = dphi_dt
        k4_a = da_dt
        
        # Final update
        self.current_frequency += h/6 * (k1_f + 2*k2_f + 2*k3_f + k4_f)
        self.current_phase += h/6 * (k1_phi + 2*k2_phi + 2*k3_phi + k4_phi)
        self.current_amplitude += h/6 * (k1_a + 2*k2_a + 2*k3_a + k4_a)
        
    def _apply_constraints(self):
        """Apply biological and physical constraints."""
        # Frequency constraints
        self.current_frequency = np.clip(self.current_frequency,
                                       self.parameters.min_frequency,
                                       self.parameters.max_frequency)
        
        # Phase normalization
        self.current_phase = self.current_phase % (2 * np.pi)
        
        # Amplitude constraints
        self.current_amplitude = max(0.0, self.current_amplitude)
        
        # ADDED safety checks:
        if self.current_frequency > 10 * self.parameters.frequency:
            self.current_frequency = self.parameters.frequency
            logger.warning(f"Oscillator {self.oscillator_id} frequency reset due to extreme value")

        if self.current_amplitude > 10 * self.parameters.amplitude:
            self.current_amplitude = self.parameters.amplitude
            logger.warning(f"Oscillator {self.oscillator_id} amplitude reset due to extreme value")




    def _perform_phase_reset(self):
        """Perform spontaneous phase reset."""
        self.current_phase = np.random.uniform(0, 2 * np.pi)
        self.phase_resets += 1
        logger.debug(f"Phase reset in oscillator {self.oscillator_id}")
        
    def _update_history(self):
        """Update oscillator history."""
        self.phase_history.append(self.current_phase)
        self.amplitude_history.append(self.current_amplitude)
        self.frequency_history.append(self.current_frequency)
        
    def couple_to(self, other_oscillator: 'NeuralOscillator', 
                  coupling_strength: float, bidirectional: bool = True):
        """
        Establish coupling with another oscillator.
        
        Args:
            other_oscillator: Target oscillator for coupling
            coupling_strength: Strength of coupling interaction
            bidirectional: Whether to establish bidirectional coupling
        """
        # Add forward coupling
        self.coupled_oscillators.append((other_oscillator, coupling_strength))
        
        # Add reverse coupling if bidirectional
        if bidirectional:
            other_oscillator.coupled_oscillators.append((self, coupling_strength))
            
        logger.debug(f"Coupled oscillator {self.oscillator_id} to {other_oscillator.oscillator_id}")
        
    def set_brain_state(self, state: BrainState):
        """Set current brain state for state-dependent modulation."""
        self.brain_state = state
        
    def set_arousal_level(self, level: float):
        """Set arousal level (0-1)."""
        self.arousal_level = np.clip(level, 0.0, 1.0)
        
    def set_attention_level(self, level: float):
        """Set attention level (0-1)."""
        self.attention_level = np.clip(level, 0.0, 1.0)
        
    def get_current_state(self) -> Dict[str, float]:
        """Get current oscillator state."""
        return {
            'phase': self.current_phase,
            'frequency': self.current_frequency,
            'amplitude': self.current_amplitude,
            'instantaneous_power': self.current_amplitude ** 2,
            'time': self.last_update_time
        }
        
    def get_output_signal(self, waveform_type: str = 'sine') -> float:
        """
        Get current output signal.
        
        Args:
            waveform_type: Type of waveform ('sine', 'cosine', 'square', 'sawtooth')
            
        Returns:
            Current output value
        """
        if waveform_type == 'sine':
            return self.current_amplitude * np.sin(self.current_phase)
        elif waveform_type == 'cosine':
            return self.current_amplitude * np.cos(self.current_phase)
        elif waveform_type == 'square':
            return self.current_amplitude * np.sign(np.sin(self.current_phase))
        elif waveform_type == 'sawtooth':
            normalized_phase = (self.current_phase / (2 * np.pi)) % 1.0
            return self.current_amplitude * (2 * normalized_phase - 1)
        else:
            raise ValueError(f"Unknown waveform type: {waveform_type}")
            
    def compute_spectral_features(self, window_length: int = 1024) -> SpectralFeatures:
        """Compute spectral features from oscillator history."""
        if len(self.phase_history) < window_length:
            return SpectralFeatures()
            
        # Convert phase history to signal
        recent_phases = np.array(list(self.phase_history)[-window_length:])
        signal = np.sin(recent_phases)
        
        # Compute power spectral density
        sampling_rate = 1000.0  # Assume 1 kHz sampling
        frequencies, psd = welch(signal, fs=sampling_rate, nperseg=min(256, len(signal)))
        
        # Extract features
        peak_idx = np.argmax(psd)
        peak_frequency = frequencies[peak_idx]
        peak_power = psd[peak_idx]
        total_power = np.sum(psd)
        
        # Compute bandwidth (frequencies with power > 50% of peak)
        bandwidth_mask = psd > 0.5 * peak_power
        if np.any(bandwidth_mask):
            bandwidth = frequencies[bandwidth_mask][-1] - frequencies[bandwidth_mask][0]
        else:
            bandwidth = 0.0
            
        # Spectral entropy
        normalized_psd = psd / np.sum(psd)
        spectral_entropy = -np.sum(normalized_psd * np.log2(normalized_psd + 1e-12))
        
        # Center frequency (weighted mean)
        center_frequency = np.sum(frequencies * normalized_psd)
        
        return SpectralFeatures(
            frequencies=frequencies,
            power_spectrum=psd,
            peak_frequency=peak_frequency,
            peak_power=peak_power,
            bandwidth=bandwidth,
            spectral_entropy=spectral_entropy,
            center_frequency=center_frequency,
            total_power=total_power
        )
        
    def reset_state(self):
        """Reset oscillator to initial state."""
        self.current_phase = self.parameters.phase
        self.current_frequency = self.parameters.frequency
        self.current_amplitude = self.parameters.amplitude
        self.last_update_time = 0.0
        self.total_updates = 0
        self.phase_resets = 0
        
        # Clear history
        self.phase_history.clear()
        self.amplitude_history.clear()
        self.frequency_history.clear()


# ===========================================================================================
# SPECIFIC OSCILLATOR IMPLEMENTATIONS
# ===========================================================================================

class DeltaOscillator(NeuralOscillator):
    """
    Delta rhythm oscillator (0.5-4 Hz) associated with deep sleep and slow-wave activity.
    
    Features:
    - Large amplitude oscillations
    - Slow frequency adaptation
    - Strong state-dependent modulation
    - Integration with homeostatic sleep drive
    """
    
    def __init__(self, oscillator_id: str = None, 
                 parameters: OscillatorParameters = None):
        if oscillator_id is None:
            oscillator_id = f"delta_{uuid.uuid4().hex[:8]}"
            
        if parameters is None:
            parameters = OscillatorParameters(
                frequency=2.0,
                amplitude=1.2,
                noise_level=0.02,
                arousal_sensitivity=0.8,
                min_frequency=0.5,
                max_frequency=4.0
            )
            
        super().__init__(oscillator_id, OscillationType.DELTA, parameters)
        
        # Delta-specific parameters
        self.slow_wave_component = 0.0
        self.homeostatic_drive = 0.0
        self.sleep_spindle_coupling = 0.0
        
    def compute_intrinsic_dynamics(self, dt: float) -> Tuple[float, float, float]:
        """Compute delta-specific dynamics with slow-wave characteristics."""
        # Base frequency dynamics (slow adaptation)
        df_dt = -0.01 * (self.current_frequency - self.parameters.frequency)
        
        # Phase dynamics with slow-wave modulation
        omega = 2 * np.pi * self.current_frequency
        dphi_dt = omega
        
        # Add slow-wave component
        slow_wave_modulation = 0.3 * np.sin(0.5 * self.current_phase)
        dphi_dt += slow_wave_modulation
        
        # Amplitude dynamics with homeostatic modulation
        da_dt = 0.02 * (self.parameters.amplitude - self.current_amplitude)
        da_dt += 0.5 * self.homeostatic_drive  # Sleep drive increases amplitude
        
        # State-dependent frequency modulation
        if self.brain_state == BrainState.DEEP_SLEEP:
            df_dt -= 0.1 * self.current_frequency  # Slower in deep sleep
            da_dt += 0.3  # Higher amplitude in deep sleep
        elif self.brain_state in [BrainState.WAKE_ALERT, BrainState.WAKE_RELAXED]:
            da_dt -= 0.2  # Reduced amplitude when awake
            
        return df_dt, dphi_dt, da_dt
        
    def set_homeostatic_drive(self, drive_level: float):
        """Set homeostatic sleep drive (0-1)."""
        self.homeostatic_drive = np.clip(drive_level, 0.0, 1.0)
        
    def couple_to_sleep_spindles(self, coupling_strength: float):
        """Enable coupling to sleep spindle oscillations."""
        self.sleep_spindle_coupling = coupling_strength


class ThetaOscillator(NeuralOscillator):
    """
    Theta rhythm oscillator (4-8 Hz) associated with memory, navigation, and REM sleep.
    
    Features:
    - Memory-dependent amplitude modulation
    - Navigation-related frequency modulation  
    - Strong hippocampal coupling
    - REM sleep enhancement
    """
    
    def __init__(self, oscillator_id: str = None,
                 parameters: OscillatorParameters = None):
        if oscillator_id is None:
            oscillator_id = f"theta_{uuid.uuid4().hex[:8]}"
            
        if parameters is None:
            parameters = OscillatorParameters(
                frequency=6.0,
                amplitude=0.8,
                noise_level=0.015,
                arousal_sensitivity=0.4,
                attention_sensitivity=0.6,
                min_frequency=4.0,
                max_frequency=8.0
            )
            
        super().__init__(oscillator_id, OscillationType.THETA, parameters)
        
        # Theta-specific parameters
        self.memory_load = 0.0
        self.movement_velocity = 0.0
        self.place_field_activity = 0.0
        self.gamma_coupling_strength = 0.3
        
    def compute_intrinsic_dynamics(self, dt: float) -> Tuple[float, float, float]:
        """Compute theta-specific dynamics with memory and navigation features."""
        # Frequency modulation by movement velocity (VCO-like)
        velocity_modulation = 0.1 * self.movement_velocity
        df_dt = velocity_modulation - 0.05 * (self.current_frequency - self.parameters.frequency)
        
        # Base phase dynamics
        omega = 2 * np.pi * self.current_frequency
        dphi_dt = omega
        
        # Memory-dependent amplitude modulation
        memory_modulation = 0.4 * self.memory_load
        da_dt = memory_modulation - 0.03 * (self.current_amplitude - self.parameters.amplitude)
        
        # Place field modulation
        if self.place_field_activity > 0.5:
            dphi_dt += 0.2 * np.sin(2 * self.current_phase)  # Frequency doubling
            
        # REM sleep enhancement
        if self.brain_state == BrainState.REM_SLEEP:
            da_dt += 0.4  # Enhanced amplitude in REM
            df_dt += 0.1 * self.current_frequency  # Slight frequency increase
            
        return df_dt, dphi_dt, da_dt
        
    def set_memory_load(self, load_level: float):
        """Set current memory load (0-1)."""
        self.memory_load = np.clip(load_level, 0.0, 1.0)
        
    def set_movement_velocity(self, velocity: float):
        """Set movement velocity for VCO modulation."""
        self.movement_velocity = np.clip(velocity, 0.0, 2.0)
        
    def set_place_field_activity(self, activity: float):
        """Set place field activity level (0-1)."""
        self.place_field_activity = np.clip(activity, 0.0, 1.0)


class AlphaOscillator(NeuralOscillator):
    """
    Alpha rhythm oscillator (8-13 Hz) associated with relaxed wakefulness and sensory gating.
    
    Features:
    - Attention-dependent suppression
    - Sensory gating modulation
    - Thalamo-cortical coupling
    - Eye closure enhancement
    """
    
    def __init__(self, oscillator_id: str = None,
                 parameters: OscillatorParameters = None):
        if oscillator_id is None:
            oscillator_id = f"alpha_{uuid.uuid4().hex[:8]}"
            
        if parameters is None:
            parameters = OscillatorParameters(
                frequency=10.0,
                amplitude=0.6,
                noise_level=0.01,
                arousal_sensitivity=0.5,
                attention_sensitivity=0.8,
                min_frequency=8.0,
                max_frequency=13.0
            )
            
        super().__init__(oscillator_id, OscillationType.ALPHA, parameters)
        
        # Alpha-specific parameters
        self.attention_suppression = 0.0
        self.eyes_closed = False
        self.sensory_gating_strength = 0.5
        self.thalamic_coupling = 0.4
        
    def compute_intrinsic_dynamics(self, dt: float) -> Tuple[float, float, float]:
        """Compute alpha-specific dynamics with attention suppression."""
        # Stable frequency (thalamic pacemaker)
        df_dt = -0.02 * (self.current_frequency - self.parameters.frequency)
        
        # Phase dynamics
        omega = 2 * np.pi * self.current_frequency
        dphi_dt = omega
        
        # Attention suppression of amplitude
        attention_suppression = self.attention_level * 0.7
        da_dt = -attention_suppression - 0.02 * (self.current_amplitude - self.parameters.amplitude)
        
        # Eyes closed enhancement
        if self.eyes_closed:
            da_dt += 0.3
            
        # Sensory gating modulation
        gating_modulation = self.sensory_gating_strength * np.cos(self.current_phase)
        da_dt += 0.1 * gating_modulation
        
        # State-dependent modulation
        if self.brain_state == BrainState.WAKE_RELAXED:
            da_dt += 0.2  # Enhanced in relaxed wakefulness
        elif self.brain_state == BrainState.WAKE_ALERT:
            da_dt -= 0.3  # Suppressed during focused attention
            
        return df_dt, dphi_dt, da_dt
        
    def set_eyes_closed(self, closed: bool):
        """Set eyes closed state."""
        self.eyes_closed = closed
        
    def set_sensory_gating_strength(self, strength: float):
        """Set sensory gating strength (0-1)."""
        self.sensory_gating_strength = np.clip(strength, 0.0, 1.0)


class BetaOscillator(NeuralOscillator):
    """
    Beta rhythm oscillator (13-30 Hz) associated with active concentration and motor control.
    
    Features:
    - Motor activity enhancement
    - Attention-dependent amplitude
    - Cognitive load modulation
    - Parkinsonian pathology sensitivity
    """
    
    def __init__(self, oscillator_id: str = None,
                 parameters: OscillatorParameters = None):
        if oscillator_id is None:
            oscillator_id = f"beta_{uuid.uuid4().hex[:8]}"
            
        if parameters is None:
            parameters = OscillatorParameters(
                frequency=20.0,
                amplitude=0.4,
                noise_level=0.02,
                arousal_sensitivity=0.3,
                attention_sensitivity=0.7,
                min_frequency=13.0,
                max_frequency=30.0
            )
            
        super().__init__(oscillator_id, OscillationType.BETA, parameters)
        
        # Beta-specific parameters
        self.motor_activity = 0.0
        self.cognitive_load = 0.0
        self.dopamine_level = 0.5
        self.pathological_synchrony = 0.0
        
    def compute_intrinsic_dynamics(self, dt: float) -> Tuple[float, float, float]:
        """Compute beta-specific dynamics with motor and cognitive modulation."""
        # Frequency modulation by cognitive load
        cognitive_modulation = 0.05 * self.cognitive_load * self.current_frequency
        df_dt = cognitive_modulation - 0.03 * (self.current_frequency - self.parameters.frequency)
        
        # Phase dynamics
        omega = 2 * np.pi * self.current_frequency
        dphi_dt = omega
        
        # Motor activity enhancement
        motor_enhancement = 0.5 * self.motor_activity
        da_dt = motor_enhancement - 0.04 * (self.current_amplitude - self.parameters.amplitude)
        
        # Attention enhancement
        attention_enhancement = 0.3 * self.attention_level
        da_dt += attention_enhancement
        
        # Dopamine modulation (reduced beta with high dopamine)
        dopamine_suppression = (self.dopamine_level - 0.5) * 0.4
        da_dt -= dopamine_suppression
        
        # Pathological synchrony (Parkinson's-like)
        if self.pathological_synchrony > 0.5:
            da_dt += 0.6  # Excessive beta amplitude
            df_dt -= 0.1 * self.current_frequency  # Frequency slowing
            
        return df_dt, dphi_dt, da_dt
        
    def set_motor_activity(self, activity: float):
        """Set motor activity level (0-1)."""
        self.motor_activity = np.clip(activity, 0.0, 1.0)
        
    def set_cognitive_load(self, load: float):
        """Set cognitive load level (0-1)."""
        self.cognitive_load = np.clip(load, 0.0, 1.0)
        
    def set_dopamine_level(self, level: float):
        """Set dopamine level (0-1)."""
        self.dopamine_level = np.clip(level, 0.0, 1.0)
        
    def set_pathological_synchrony(self, level: float):
        """Set pathological synchrony level (0-1)."""
        self.pathological_synchrony = np.clip(level, 0.0, 1.0)


class GammaOscillator(NeuralOscillator):
    """
    Gamma rhythm oscillator (30-100 Hz) associated with conscious binding and feature integration.
    
    Features:
    - Binding-dependent amplitude
    - Attention-related enhancement
    - Conscious access correlation
    - Interneuron network dynamics
    """
    
    def __init__(self, oscillator_id: str = None,
                 parameters: OscillatorParameters = None):
        if oscillator_id is None:
            oscillator_id = f"gamma_{uuid.uuid4().hex[:8]}"
            
        if parameters is None:
            parameters = OscillatorParameters(
                frequency=40.0,
                amplitude=0.3,
                noise_level=0.03,
                arousal_sensitivity=0.6,
                attention_sensitivity=0.9,
                min_frequency=30.0,
                max_frequency=100.0
            )
            
        super().__init__(oscillator_id, OscillationType.GAMMA, parameters)
        
        # Gamma-specific parameters
        self.binding_demand = 0.0
        self.conscious_access = 0.0
        self.interneuron_excitability = 0.5
        self.theta_coupling_phase = 0.0
        
    def compute_intrinsic_dynamics(self, dt: float) -> Tuple[float, float, float]:
        """Compute gamma-specific dynamics with binding and consciousness features."""
        # Frequency modulation by binding demand
        binding_modulation = 0.1 * self.binding_demand * self.current_frequency
        df_dt = binding_modulation - 0.05 * (self.current_frequency - self.parameters.frequency)
        
        # Phase dynamics with interneuron network characteristics
        omega = 2 * np.pi * self.current_frequency
        interneuron_modulation = 0.1 * self.interneuron_excitability * np.sin(2 * self.current_phase)
        dphi_dt = omega + interneuron_modulation
        
        # Amplitude modulation by conscious access and attention
        consciousness_enhancement = 0.6 * self.conscious_access
        attention_enhancement = 0.4 * self.attention_level
        da_dt = consciousness_enhancement + attention_enhancement - 0.06 * (self.current_amplitude - self.parameters.amplitude)
        
        # Theta-gamma coupling (PAC)
        if hasattr(self, 'theta_phase'):
            theta_modulation = 0.3 * np.cos(self.theta_phase)
            da_dt += theta_modulation
            
        # High arousal enhancement
        if self.arousal_level > 0.7:
            da_dt += 0.2
            df_dt += 0.05 * self.current_frequency
            
        return df_dt, dphi_dt, da_dt
        
    def set_binding_demand(self, demand: float):
        """Set feature binding demand (0-1)."""
        self.binding_demand = np.clip(demand, 0.0, 1.0)
        
    def set_conscious_access(self, access: float):
        """Set conscious access level (0-1)."""
        self.conscious_access = np.clip(access, 0.0, 1.0)
        
    def set_interneuron_excitability(self, excitability: float):
        """Set interneuron network excitability (0-1)."""
        self.interneuron_excitability = np.clip(excitability, 0.0, 1.0)
        
    def couple_to_theta_phase(self, theta_phase: float):
        """Couple gamma amplitude to theta phase."""
        self.theta_phase = theta_phase


class HighGammaOscillator(NeuralOscillator):
    """
    High gamma oscillator (80-200 Hz) for high-frequency oscillations in cortex.
    
    Features:
    - Task-specific activation
    - Local cortical processing
    - Spike-LFP coupling
    - Pathological high-frequency activity
    """
    
    def __init__(self, oscillator_id: str = None,
                 parameters: OscillatorParameters = None):
        if oscillator_id is None:
            oscillator_id = f"high_gamma_{uuid.uuid4().hex[:8]}"
            
        if parameters is None:
            parameters = OscillatorParameters(
                frequency=120.0,
                amplitude=0.2,
                noise_level=0.05,
                arousal_sensitivity=0.4,
                attention_sensitivity=0.8,
                min_frequency=80.0,
                max_frequency=200.0
            )
            
        super().__init__(oscillator_id, OscillationType.HIGH_GAMMA, parameters)
        
        # High gamma-specific parameters
        self.task_engagement = 0.0
        self.local_processing_load = 0.0
        self.pathological_activity = 0.0
        
    def compute_intrinsic_dynamics(self, dt: float) -> Tuple[float, float, float]:
        """Compute high gamma dynamics with task and pathology modulation."""
        # Task-dependent frequency modulation
        task_modulation = 0.15 * self.task_engagement * self.current_frequency
        df_dt = task_modulation - 0.08 * (self.current_frequency - self.parameters.frequency)
        
        # Phase dynamics
        omega = 2 * np.pi * self.current_frequency
        dphi_dt = omega
        
        # Local processing amplitude modulation
        processing_enhancement = 0.5 * self.local_processing_load
        da_dt = processing_enhancement - 0.1 * (self.current_amplitude - self.parameters.amplitude)
        
        # Pathological activity (epileptiform)
        if self.pathological_activity > 0.7:
            da_dt += 1.0  # Excessive amplitude
            df_dt += 0.2 * self.current_frequency  # Frequency acceleration
            
        return df_dt, dphi_dt, da_dt
        
    def set_task_engagement(self, engagement: float):
        """Set task engagement level (0-1)."""
        self.task_engagement = np.clip(engagement, 0.0, 1.0)
        
    def set_local_processing_load(self, load: float):
        """Set local cortical processing load (0-1)."""
        self.local_processing_load = np.clip(load, 0.0, 1.0)
        
    def set_pathological_activity(self, level: float):
        """Set pathological activity level (0-1)."""
        self.pathological_activity = np.clip(level, 0.0, 1.0)


# ===========================================================================================
# CROSS-FREQUENCY COUPLING MECHANISMS
# ===========================================================================================

class PhaseAmplitudeCoupling:
    """
    Implementation of phase-amplitude coupling between neural oscillators.
    
    Implements the relationship: A_high(t) = A₀ * (1 + m * cos(φ_low(t) - φ_pref))
    where the amplitude of a high-frequency oscillation is modulated by the phase
    of a low-frequency oscillation.
    """
    
    def __init__(self, slow_oscillator: NeuralOscillator, fast_oscillator: NeuralOscillator,
                 parameters: CouplingParameters):
        """
        Initialize phase-amplitude coupling.
        
        Args:
            slow_oscillator: Low-frequency phase oscillator
            fast_oscillator: High-frequency amplitude oscillator
            parameters: Coupling configuration parameters
        """
        self.slow_oscillator = slow_oscillator
        self.fast_oscillator = fast_oscillator
        self.parameters = parameters
        
        # Coupling state
        self.coupling_strength = 0.0
        self.preferred_phase = parameters.phase_preference
        self.modulation_index_history = deque(maxlen=1000)
        
        # Statistics
        self.total_updates = 0
        self.significant_coupling_events = 0
        
        # Performance optimization
        self.update_interval = 10  # Update every N timesteps
        self.last_coupling_update = 0
        
        logger.debug(f"PAC initialized: {slow_oscillator.oscillator_id} -> {fast_oscillator.oscillator_id}")
        
    def update_coupling(self, dt: float) -> Dict[str, float]:
        """
        Update phase-amplitude coupling.
        
        Args:
            dt: Time step in milliseconds
            
        Returns:
            Dictionary with coupling statistics
        """
        self.total_updates += 1
        
        # Skip update if interval not reached (optimization)
        if self.total_updates % self.update_interval != 0:
            return self.get_coupling_state()
            
        # Get current phases and amplitudes
        slow_phase = self.slow_oscillator.current_phase
        fast_amplitude_base = self.fast_oscillator.parameters.amplitude
        
        # Compute phase-amplitude modulation
        modulated_amplitude = phase_amplitude_modulation(
            slow_phase, fast_amplitude_base, 
            self.parameters.modulation_depth, self.preferred_phase
        )
        
        # Apply modulation to fast oscillator
        self.fast_oscillator.current_amplitude = modulated_amplitude
        
        # Compute instantaneous modulation index
        if len(self.slow_oscillator.phase_history) > 50 and len(self.fast_oscillator.amplitude_history) > 50:
            recent_phases = np.array(list(self.slow_oscillator.phase_history)[-50:])
            recent_amplitudes = np.array(list(self.fast_oscillator.amplitude_history)[-50:])
            
            modulation_index = compute_modulation_index(recent_phases, recent_amplitudes)
            self.modulation_index_history.append(modulation_index)
            
            # Check for significant coupling
            if modulation_index > self.parameters.significance_threshold:
                self.significant_coupling_events += 1
                
        # Adaptive coupling strength
        if self.parameters.plasticity_rate > 0:
            self._update_adaptive_coupling(dt)
            
        return self.get_coupling_state()
        
    def _update_adaptive_coupling(self, dt: float):
        """Update coupling strength based on plasticity rules."""
        if len(self.modulation_index_history) < 10:
            return
            
        # Current coupling effectiveness
        recent_mi = np.mean(list(self.modulation_index_history)[-10:])
        
        # Homeostatic adjustment toward target
        error = recent_mi - self.parameters.homeostatic_target
        adjustment = -self.parameters.plasticity_rate * error * dt
        
        # Update modulation depth
        new_depth = self.parameters.modulation_depth + adjustment
        self.parameters.modulation_depth = np.clip(new_depth, 0.0, 1.0)
        
    def compute_detailed_coupling_metrics(self, window_length: int = 500) -> Dict[str, float]:
        """
        Compute comprehensive coupling metrics.
        
        Args:
            window_length: Analysis window length
            
        Returns:
            Dictionary with detailed coupling metrics
        """
        if (len(self.slow_oscillator.phase_history) < window_length or 
            len(self.fast_oscillator.amplitude_history) < window_length):
            return {}
            
        # Extract recent data
        slow_phases = np.array(list(self.slow_oscillator.phase_history)[-window_length:])
        fast_amplitudes = np.array(list(self.fast_oscillator.amplitude_history)[-window_length:])
        
        # Modulation Index
        mi = compute_modulation_index(slow_phases, fast_amplitudes, n_bins=18)
        
        # Mean Vector Length (phase concentration)
        phase_diffs = slow_phases - np.mean(slow_phases)
        _, mvl = circular_mean_resultant(phase_diffs)
        
        # Amplitude-phase correlation
        amp_phase_corr = np.corrcoef(np.cos(slow_phases), fast_amplitudes)[0, 1]
        
        # Preferred coupling phase
        bin_centers = np.linspace(-np.pi, np.pi, 18)
        phase_bins = np.digitize(slow_phases, np.linspace(-np.pi, np.pi, 19)) - 1
        phase_bins = np.clip(phase_bins, 0, 17)
        
        bin_amplitudes = np.zeros(18)
        for i in range(18):
            mask = phase_bins == i
            if np.sum(mask) > 0:
                bin_amplitudes[i] = np.mean(fast_amplitudes[mask])
                
        if np.sum(bin_amplitudes) > 0:
            bin_amplitudes = bin_amplitudes / np.sum(bin_amplitudes)
            preferred_phase = np.angle(np.sum(bin_amplitudes * np.exp(1j * bin_centers)))
        else:
            preferred_phase = 0.0
            
        return {
            'modulation_index': mi,
            'mean_vector_length': mvl,
            'amplitude_phase_correlation': amp_phase_corr if not np.isnan(amp_phase_corr) else 0.0,
            'preferred_coupling_phase': preferred_phase,
            'coupling_strength': self.parameters.modulation_depth,
            'n_significant_events': self.significant_coupling_events
        }
        
    def get_coupling_state(self) -> Dict[str, float]:
        """Get current coupling state."""
        current_mi = self.modulation_index_history[-1] if self.modulation_index_history else 0.0
        
        return {
            'modulation_index': current_mi,
            'coupling_strength': self.parameters.modulation_depth,
            'preferred_phase': self.preferred_phase,
            'slow_frequency': self.slow_oscillator.current_frequency,
            'fast_frequency': self.fast_oscillator.current_frequency,
            'total_updates': self.total_updates
        }


class PhasePhaseCoupling:
    """
    Implementation of phase-phase coupling (n:m phase locking) between oscillators.
    
    Implements n*φ₁ - m*φ₂ = constant, where n and m are integers defining
    the coupling ratio between two oscillators.
    """
    
    def __init__(self, oscillator1: NeuralOscillator, oscillator2: NeuralOscillator,
                 n_ratio: int = 1, m_ratio: int = 1, 
                 parameters: CouplingParameters = None):
        """
        Initialize phase-phase coupling.
        
        Args:
            oscillator1: First oscillator
            oscillator2: Second oscillator
            n_ratio: Phase ratio for oscillator1
            m_ratio: Phase ratio for oscillator2
            parameters: Coupling parameters
        """
        self.oscillator1 = oscillator1
        self.oscillator2 = oscillator2
        self.n_ratio = n_ratio
        self.m_ratio = m_ratio
        self.parameters = parameters or CouplingParameters()
        
        # Coupling state
        self.phase_difference_history = deque(maxlen=1000)
        self.phase_locking_value = 0.0
        self.locking_episodes = 0
        
    def update_coupling(self, dt: float) -> Dict[str, float]:
        """Update phase-phase coupling."""
        # Compute generalized phase difference
        phase_diff = (self.n_ratio * self.oscillator1.current_phase - 
                     self.m_ratio * self.oscillator2.current_phase)
        
        # Normalize to [-π, π]
        phase_diff = ((phase_diff + np.pi) % (2 * np.pi)) - np.pi
        
        self.phase_difference_history.append(phase_diff)
        
        # Compute phase locking value
        if len(self.phase_difference_history) >= 50:
            recent_diffs = np.array(list(self.phase_difference_history)[-50:])
            self.phase_locking_value = np.abs(np.mean(np.exp(1j * recent_diffs)))
            
            # Detect locking episodes
            if self.phase_locking_value > 0.7:
                self.locking_episodes += 1
                
        return {
            'phase_difference': phase_diff,
            'phase_locking_value': self.phase_locking_value,
            'locking_episodes': self.locking_episodes,
            'coupling_ratio': f"{self.n_ratio}:{self.m_ratio}"
        }


class AmplitudeAmplitudeCoupling:
    """
    Implementation of amplitude-amplitude coupling between oscillators.
    
    Measures correlation between the amplitude envelopes of two oscillations,
    indicating coordinated power fluctuations.
    """
    
    def __init__(self, oscillator1: NeuralOscillator, oscillator2: NeuralOscillator,
                 parameters: CouplingParameters = None):
        """Initialize amplitude-amplitude coupling."""
        self.oscillator1 = oscillator1
        self.oscillator2 = oscillator2
        self.parameters = parameters or CouplingParameters()
        
        # Coupling state
        self.amplitude_correlation = 0.0
        self.correlation_history = deque(maxlen=100)
        
    def update_coupling(self, dt: float) -> Dict[str, float]:
        """Update amplitude-amplitude coupling."""
        # Compute correlation if sufficient history
        if (len(self.oscillator1.amplitude_history) >= 50 and 
            len(self.oscillator2.amplitude_history) >= 50):
            
            amp1 = np.array(list(self.oscillator1.amplitude_history)[-50:])
            amp2 = np.array(list(self.oscillator2.amplitude_history)[-50:])
            
            correlation = np.corrcoef(amp1, amp2)[0, 1]
            if not np.isnan(correlation):
                self.amplitude_correlation = correlation
                self.correlation_history.append(correlation)
                
        return {
            'amplitude_correlation': self.amplitude_correlation,
            'mean_correlation': np.mean(self.correlation_history) if self.correlation_history else 0.0,
            'correlation_stability': np.std(self.correlation_history) if len(self.correlation_history) > 1 else 0.0
        }


# ===========================================================================================
# COHERENCE AND SYNCHRONIZATION ANALYSIS
# ===========================================================================================

class CoherenceAnalyzer:
    """
    Advanced coherence analysis for neural oscillator synchronization.
    
    Implements multiple coherence measures including magnitude-squared coherence,
    imaginary coherence, phase locking value, and weighted phase lag index.
    """
    
    def __init__(self, sampling_rate: float = 1000.0):
        """
        Initialize coherence analyzer.
        
        Args:
            sampling_rate: Sampling rate in Hz
        """
        self.sampling_rate = sampling_rate
        self.coherence_cache = {}
        self.analysis_parameters = {
            'nperseg': 256,
            'noverlap': 128,
            'nfft': 512,
            'window': 'hann'
        }
        
    def compute_magnitude_squared_coherence(self, signal1: np.ndarray, signal2: np.ndarray,
                                          frequency_bands: Dict[str, Tuple[float, float]] = None) -> Dict[str, Any]:
        """
        Compute magnitude-squared coherence between two signals.
        
        Args:
            signal1: First signal
            signal2: Second signal
            frequency_bands: Dictionary of frequency bands to analyze
            
        Returns:
            Dictionary with coherence results
        """
        if len(signal1) != len(signal2):
            raise ValueError("Signals must have the same length")
            
        # Compute coherence
        frequencies, coherence_values = coherence(
            signal1, signal2, 
            fs=self.sampling_rate,
            **self.analysis_parameters
        )
        
        results = {
            'frequencies': frequencies,
            'coherence': coherence_values,
            'mean_coherence': np.mean(coherence_values),
            'peak_coherence': np.max(coherence_values),
            'peak_frequency': frequencies[np.argmax(coherence_values)]
        }
        
        # Band-specific coherence
        if frequency_bands:
            band_coherence = {}
            for band_name, (low_freq, high_freq) in frequency_bands.items():
                band_mask = (frequencies >= low_freq) & (frequencies <= high_freq)
                if np.any(band_mask):
                    band_coherence[band_name] = np.mean(coherence_values[band_mask])
                else:
                    band_coherence[band_name] = 0.0
                    
            results['band_coherence'] = band_coherence
            
        return results
        
    def compute_imaginary_coherence(self, signal1: np.ndarray, signal2: np.ndarray) -> Dict[str, Any]:
        """
        Compute imaginary coherence to reduce volume conduction artifacts.
        
        Args:
            signal1: First signal
            signal2: Second signal
            
        Returns:
            Dictionary with imaginary coherence results
        """
        # Compute cross-spectral density
        frequencies, psd1 = welch(signal1, fs=self.sampling_rate, **self.analysis_parameters)
        _, psd2 = welch(signal2, fs=self.sampling_rate, **self.analysis_parameters)
        _, cross_psd = coherence(signal1, signal2, fs=self.sampling_rate, **self.analysis_parameters)
        
        # Convert to cross-spectral density
        cross_psd_complex = cross_psd * np.sqrt(psd1 * psd2)
        
        # Imaginary coherence
        imaginary_coherence = np.abs(np.imag(cross_psd_complex)) / np.sqrt(psd1 * psd2)
        
        return {
            'frequencies': frequencies,
            'imaginary_coherence': imaginary_coherence,
            'mean_imaginary_coherence': np.mean(imaginary_coherence),
            'peak_imaginary_coherence': np.max(imaginary_coherence)
        }
        
    def compute_phase_locking_value(self, signal1: np.ndarray, signal2: np.ndarray,
                                  frequency_band: Tuple[float, float] = None) -> float:
        """
        Compute phase locking value between two signals.
        
        Args:
            signal1: First signal
            signal2: Second signal
            frequency_band: Optional frequency band for filtering
            
        Returns:
            Phase locking value (0-1)
        """
        # Filter signals if frequency band specified
        if frequency_band:
            low_freq, high_freq = frequency_band
            nyquist = self.sampling_rate / 2
            low_norm = low_freq / nyquist
            high_norm = high_freq / nyquist
            
            b, a = butter(4, [low_norm, high_norm], btype='band')
            signal1 = filtfilt(b, a, signal1)
            signal2 = filtfilt(b, a, signal2)
            
        # Extract instantaneous phases
        phase1, _ = instantaneous_phase_amplitude(signal1, self.sampling_rate)
        phase2, _ = instantaneous_phase_amplitude(signal2, self.sampling_rate)
        
        # Compute PLV
        plv = compute_phase_locking_value(phase1, phase2)
        
        return plv
        
    def compute_weighted_phase_lag_index(self, signal1: np.ndarray, signal2: np.ndarray) -> float:
        """
        Compute weighted phase lag index (WPLI) for robust connectivity measurement.
        
        Args:
            signal1: First signal
            signal2: Second signal
            
        Returns:
            Weighted phase lag index
        """
        # Extract phases
        phase1, _ = instantaneous_phase_amplitude(signal1, self.sampling_rate)
        phase2, _ = instantaneous_phase_amplitude(signal2, self.sampling_rate)
        
        # Phase differences
        phase_diff = phase1 - phase2
        
        # Cross-spectral density imaginary part
        imaginary_part = np.sin(phase_diff)
        
        # WPLI computation
        numerator = np.abs(np.mean(imaginary_part))
        denominator = np.mean(np.abs(imaginary_part))
        
        wpli = numerator / denominator if denominator > 0 else 0.0
        
        return wpli
        
    def analyze_oscillator_synchronization(self, oscillators: List[NeuralOscillator],
                                         window_length: int = 1000) -> Dict[str, Any]:
        """
        Analyze synchronization between multiple oscillators.
        
        Args:
            oscillators: List of neural oscillators
            window_length: Analysis window length
            
        Returns:
            Comprehensive synchronization analysis
        """
        n_oscillators = len(oscillators)
        
        # Extract signals
        signals = []
        for osc in oscillators:
            if len(osc.phase_history) >= window_length:
                phases = np.array(list(osc.phase_history)[-window_length:])
                signal = np.sin(phases)  # Convert phase to signal
                signals.append(signal)
            else:
                signals.append(np.zeros(window_length))
                
        # Pairwise analysis
        coherence_matrix = np.zeros((n_oscillators, n_oscillators))
        plv_matrix = np.zeros((n_oscillators, n_oscillators))
        
        for i in range(n_oscillators):
            for j in range(i + 1, n_oscillators):
                # Magnitude-squared coherence
                coh_result = self.compute_magnitude_squared_coherence(signals[i], signals[j])
                coherence_matrix[i, j] = coh_result['mean_coherence']
                coherence_matrix[j, i] = coherence_matrix[i, j]
                
                # Phase locking value
                plv = self.compute_phase_locking_value(signals[i], signals[j])
                plv_matrix[i, j] = plv
                plv_matrix[j, i] = plv
                
        # Global synchronization measures
        mean_coherence = np.mean(coherence_matrix[np.triu_indices(n_oscillators, k=1)])
        mean_plv = np.mean(plv_matrix[np.triu_indices(n_oscillators, k=1)])
        
        # Kuramoto order parameter
        phases = [osc.current_phase for osc in oscillators]
        kuramoto_r = np.abs(np.mean(np.exp(1j * np.array(phases))))
        
        return {
            'coherence_matrix': coherence_matrix,
            'plv_matrix': plv_matrix,
            'mean_coherence': mean_coherence,
            'mean_plv': mean_plv,
            'kuramoto_order_parameter': kuramoto_r,
            'oscillator_ids': [osc.oscillator_id for osc in oscillators],
            'synchronization_strength': (mean_coherence + mean_plv + kuramoto_r) / 3
        }


# ===========================================================================================
# SPECTRAL ANALYSIS AND POWER ESTIMATION
# ===========================================================================================

class SpectralAnalyzer:
    """
    Advanced spectral analysis for neural oscillator signals.
    
    Provides comprehensive spectral characterization including power spectral density,
    spectrograms, cross-spectral analysis, and frequency-domain connectivity metrics.
    """
    
    def __init__(self, sampling_rate: float = 1000.0):
        """Initialize spectral analyzer."""
        self.sampling_rate = sampling_rate
        self.frequency_bands = {
            'delta': (0.5, 4.0),
            'theta': (4.0, 8.0),
            'alpha': (8.0, 13.0),
            'beta': (13.0, 30.0),
            'gamma': (30.0, 100.0),
            'high_gamma': (80.0, 200.0)
        }
        
    def compute_power_spectral_density(self, signal: np.ndarray,
                                     method: str = 'welch') -> Dict[str, Any]:
        """
        Compute power spectral density using various methods.
        
        Args:
            signal: Input signal
            method: Method for PSD computation ('welch', 'periodogram', 'multitaper')
            
        Returns:
            Dictionary with PSD results
        """
        if method == 'welch':
            frequencies, psd = welch(signal, fs=self.sampling_rate, 
                                   nperseg=min(256, len(signal)//4),
                                   noverlap=min(128, len(signal)//8))
        elif method == 'periodogram':
            frequencies, psd = scipy.signal.periodogram(signal, fs=self.sampling_rate)
        else:
            raise ValueError(f"Unknown PSD method: {method}")
            
        # Band power analysis
        band_powers = {}
        total_power = np.sum(psd)
        
        for band_name, (low_freq, high_freq) in self.frequency_bands.items():
            band_mask = (frequencies >= low_freq) & (frequencies <= high_freq)
            if np.any(band_mask):
                band_power = np.sum(psd[band_mask])
                band_powers[band_name] = {
                    'absolute_power': band_power,
                    'relative_power': band_power / total_power if total_power > 0 else 0.0,
                    'peak_frequency': frequencies[band_mask][np.argmax(psd[band_mask])],
                    'peak_power': np.max(psd[band_mask])
                }
            else:
                band_powers[band_name] = {
                    'absolute_power': 0.0,
                    'relative_power': 0.0,
                    'peak_frequency': (low_freq + high_freq) / 2,
                    'peak_power': 0.0
                }
                
        # Spectral features
        spectral_features = self._extract_spectral_features(frequencies, psd)
        
        return {
            'frequencies': frequencies,
            'psd': psd,
            'band_powers': band_powers,
            'total_power': total_power,
            'spectral_features': spectral_features
        }
        
    def _extract_spectral_features(self, frequencies: np.ndarray, 
                                 psd: np.ndarray) -> Dict[str, float]:
        """Extract advanced spectral features."""
        # Normalize PSD for entropy calculation
        normalized_psd = psd / np.sum(psd)
        
        # Spectral entropy
        spectral_entropy = -np.sum(normalized_psd * np.log2(normalized_psd + 1e-12))
        
        # Spectral centroid (center of mass)
        spectral_centroid = np.sum(frequencies * normalized_psd)
        
        # Spectral spread (variance around centroid)
        spectral_spread = np.sqrt(np.sum(((frequencies - spectral_centroid) ** 2) * normalized_psd))
        
        # Spectral skewness
        spectral_skewness = np.sum(((frequencies - spectral_centroid) ** 3) * normalized_psd) / (spectral_spread ** 3)
        
        # Spectral kurtosis
        spectral_kurtosis = np.sum(((frequencies - spectral_centroid) ** 4) * normalized_psd) / (spectral_spread ** 4)
        
        # Peak frequency and power
        peak_idx = np.argmax(psd)
        peak_frequency = frequencies[peak_idx]
        peak_power = psd[peak_idx]
        
        # Bandwidth (frequencies with power > 50% of peak)
        bandwidth_mask = psd > 0.5 * peak_power
        if np.any(bandwidth_mask):
            bandwidth = frequencies[bandwidth_mask][-1] - frequencies[bandwidth_mask][0]
        else:
            bandwidth = 0.0
            
        return {
            'spectral_entropy': spectral_entropy,
            'spectral_centroid': spectral_centroid,
            'spectral_spread': spectral_spread,
            'spectral_skewness': spectral_skewness,
            'spectral_kurtosis': spectral_kurtosis,
            'peak_frequency': peak_frequency,
            'peak_power': peak_power,
            'bandwidth': bandwidth
        }
        
    def compute_spectrogram(self, signal: np.ndarray, 
                          window_length: int = 256,
                          overlap: int = 128) -> Dict[str, Any]:
        """
        Compute time-frequency spectrogram.
        
        Args:
            signal: Input signal
            window_length: Length of analysis window
            overlap: Window overlap in samples
            
        Returns:
            Dictionary with spectrogram results
        """
        frequencies, times, spectrogram_data = spectrogram(
            signal, fs=self.sampling_rate,
            nperseg=window_length,
            noverlap=overlap,
            window='hann'
        )
        
        # Convert to dB
        spectrogram_db = 10 * np.log10(spectrogram_data + 1e-12)
        
        # Time-varying band powers
        band_power_evolution = {}
        for band_name, (low_freq, high_freq) in self.frequency_bands.items():
            band_mask = (frequencies >= low_freq) & (frequencies <= high_freq)
            if np.any(band_mask):
                band_power_evolution[band_name] = np.mean(spectrogram_data[band_mask, :], axis=0)
            else:
                band_power_evolution[band_name] = np.zeros(len(times))
                
        return {
            'frequencies': frequencies,
            'times': times,
            'spectrogram': spectrogram_data,
            'spectrogram_db': spectrogram_db,
            'band_power_evolution': band_power_evolution
        }
        
    def analyze_oscillator_spectrum(self, oscillator: NeuralOscillator,
                                  window_length: int = 1000) -> Dict[str, Any]:
        """
        Comprehensive spectral analysis of a single oscillator.
        
        Args:
            oscillator: Neural oscillator to analyze
            window_length: Analysis window length
            
        Returns:
            Complete spectral characterization
        """
        if len(oscillator.phase_history) < window_length:
            return {'error': 'Insufficient data for analysis'}
            
        # Extract signal from phase history
        phases = np.array(list(oscillator.phase_history)[-window_length:])
        signal = np.sin(phases)
        
        # Power spectral density
        psd_results = self.compute_power_spectral_density(signal)
        
        # Spectrogram
        spectrogram_results = self.compute_spectrogram(signal)
        
        # Oscillator-specific metrics
        theoretical_frequency = oscillator.parameters.frequency
        measured_frequency = psd_results['spectral_features']['peak_frequency']
        frequency_drift = measured_frequency - theoretical_frequency
        
        # Frequency stability
        instantaneous_frequencies = np.diff(np.unwrap(phases)) * self.sampling_rate / (2 * np.pi)
        frequency_stability = np.std(instantaneous_frequencies)
        
        # Amplitude stability
        amplitudes = np.array(list(oscillator.amplitude_history)[-window_length:])
        amplitude_stability = np.std(amplitudes) / np.mean(amplitudes) if len(amplitudes) > 0 else 0.0
        
        return {
            'psd_analysis': psd_results,
            'spectrogram_analysis': spectrogram_results,
            'oscillator_metrics': {
                'theoretical_frequency': theoretical_frequency,
                'measured_frequency': measured_frequency,
                'frequency_drift': frequency_drift,
                'frequency_stability': frequency_stability,
                'amplitude_stability': amplitude_stability,
                'oscillator_type': oscillator.oscillation_type.name,
                'oscillator_id': oscillator.oscillator_id
            }
        }


# ===========================================================================================
# MAIN BIOLOGICAL TIMING CIRCUITS SYSTEM
# ===========================================================================================

class BiologicalTimingCircuits:
    """
    Main biological timing system coordinating neural oscillations across frequencies.
    
    This system implements the complete biological timing infrastructure for ULTRA,
    including multiple oscillator types, cross-frequency coupling, coherence analysis,
    and state-dependent rhythm modulation.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize biological timing circuits.
        
        Args:
            config: Configuration dictionary for timing circuits
        """
        self.config = config or {}
        
        # Core oscillator collection
        self.oscillators: Dict[str, NeuralOscillator] = {}
        self.oscillator_groups: Dict[str, List[str]] = defaultdict(list)
        
        # Coupling mechanisms
        self.pac_couplings: List[PhaseAmplitudeCoupling] = []
        self.ppc_couplings: List[PhasePhaseCoupling] = []
        self.aac_couplings: List[AmplitudeAmplitudeCoupling] = []
        
        # Analysis systems
        self.coherence_analyzer = CoherenceAnalyzer(sampling_rate=1000.0)
        self.spectral_analyzer = SpectralAnalyzer(sampling_rate=1000.0)
        
        # Global state
        self.current_brain_state = BrainState.WAKE_ALERT
        self.global_arousal = 0.5
        self.global_attention = 0.5
        self.sleep_stage = 'wake'
        
        # Neuromodulation interface
        self.neuromodulation_system = None
        self.core_neural_system = None
        
        # Performance monitoring
        self.total_updates = 0
        self.update_frequency = 1000.0  # Hz
        self.current_time = 0.0
        
        # State history
        self.state_history = deque(maxlen=10000)
        self.synchronization_history = deque(maxlen=1000)
        
        # Initialize default oscillators
        self._initialize_default_oscillators()
        
        # Set up default couplings
        self._setup_default_couplings()
        
        logger.info("BiologicalTimingCircuits initialized with comprehensive oscillator network")
        
    def _initialize_default_oscillators(self):
        """Initialize standard set of neural oscillators."""
        # Delta oscillator - deep sleep and slow waves
        delta_params = OscillatorParameters(
            frequency=2.0, amplitude=1.0, noise_level=0.02,
            arousal_sensitivity=0.3, min_frequency=0.5, max_frequency=4.0  # Reduced sensitivity
        )
        delta_osc = DeltaOscillator("delta_primary", delta_params)
        delta_osc.set_arousal_level(0.5)  # Neutral arousal
        self.add_oscillator(delta_osc)
        
        # Theta oscillator - memory and navigation
        theta_params = OscillatorParameters(
            frequency=6.0, amplitude=0.8, noise_level=0.015,
            arousal_sensitivity=0.2, attention_sensitivity=0.3,  # Reduced sensitivity
            min_frequency=4.0, max_frequency=8.0
        )
        theta_osc = ThetaOscillator("theta_primary", theta_params)
        theta_osc.set_arousal_level(0.5)
        theta_osc.set_attention_level(0.5)
        self.add_oscillator(theta_osc)
        
        # Alpha oscillator - relaxed awareness
        alpha_params = OscillatorParameters(
            frequency=10.0, amplitude=0.6, noise_level=0.01,
            arousal_sensitivity=0.2, attention_sensitivity=0.4,  # Reduced sensitivity
            min_frequency=8.0, max_frequency=13.0
        )
        alpha_osc = AlphaOscillator("alpha_primary", alpha_params)
        alpha_osc.set_arousal_level(0.5)
        alpha_osc.set_attention_level(0.5)
        self.add_oscillator(alpha_osc)
        
        # Beta oscillator - active concentration
        beta_params = OscillatorParameters(
            frequency=20.0, amplitude=0.4, noise_level=0.02,
            arousal_sensitivity=0.15, attention_sensitivity=0.35,  # Reduced sensitivity
            min_frequency=13.0, max_frequency=30.0
        )
        beta_osc = BetaOscillator("beta_primary", beta_params)
        beta_osc.set_arousal_level(0.5)
        beta_osc.set_attention_level(0.5)
        self.add_oscillator(beta_osc)
        
        # Gamma oscillator - conscious binding
        gamma_params = OscillatorParameters(
            frequency=40.0, amplitude=0.3, noise_level=0.03,
            arousal_sensitivity=0.3, attention_sensitivity=0.45,  # Reduced sensitivity
            min_frequency=30.0, max_frequency=100.0
        )
        gamma_osc = GammaOscillator("gamma_primary", gamma_params)
        gamma_osc.set_arousal_level(0.5)
        gamma_osc.set_attention_level(0.5)
        self.add_oscillator(gamma_osc)
        
        # High gamma oscillator - local processing
        high_gamma_params = OscillatorParameters(
            frequency=120.0, amplitude=0.2, noise_level=0.05,
            arousal_sensitivity=0.2, attention_sensitivity=0.4,  # Reduced sensitivity
            min_frequency=80.0, max_frequency=200.0
        )
        high_gamma_osc = HighGammaOscillator("high_gamma_primary", high_gamma_params)
        high_gamma_osc.set_arousal_level(0.5)
        high_gamma_osc.set_attention_level(0.5)
        self.add_oscillator(high_gamma_osc)
        
    def _setup_default_couplings(self):
        """Set up biologically relevant cross-frequency couplings."""
        # Theta-gamma coupling (critical for memory)
        if "theta_primary" in self.oscillators and "gamma_primary" in self.oscillators:
            theta_gamma_params = CouplingParameters(
                modulation_depth=0.4, phase_preference=0.0,
                plasticity_rate=0.01, homeostatic_target=0.3
            )
            theta_gamma_coupling = PhaseAmplitudeCoupling(
                self.oscillators["theta_primary"],
                self.oscillators["gamma_primary"],
                theta_gamma_params
            )
            self.pac_couplings.append(theta_gamma_coupling)
            
        # Alpha-beta coupling
        if "alpha_primary" in self.oscillators and "beta_primary" in self.oscillators:
            alpha_beta_params = CouplingParameters(
                modulation_depth=0.3, phase_preference=np.pi,
                plasticity_rate=0.005, homeostatic_target=0.2
            )
            alpha_beta_coupling = PhaseAmplitudeCoupling(
                self.oscillators["alpha_primary"],
                self.oscillators["beta_primary"],
                alpha_beta_params
            )
            self.pac_couplings.append(alpha_beta_coupling)
            
        # Delta-theta coupling (sleep-related)
        if "delta_primary" in self.oscillators and "theta_primary" in self.oscillators:
            delta_theta_params = CouplingParameters(
                modulation_depth=0.5, phase_preference=0.0,
                plasticity_rate=0.02, homeostatic_target=0.4
            )
            delta_theta_coupling = PhaseAmplitudeCoupling(
                self.oscillators["delta_primary"],
                self.oscillators["theta_primary"],
                delta_theta_params
            )
            self.pac_couplings.append(delta_theta_coupling)
            
        # Beta-gamma phase coupling
        if "beta_primary" in self.oscillators and "gamma_primary" in self.oscillators:
            beta_gamma_ppc = PhasePhaseCoupling(
                self.oscillators["beta_primary"],
                self.oscillators["gamma_primary"],
                n_ratio=2, m_ratio=3  # 2:3 phase locking
            )
            self.ppc_couplings.append(beta_gamma_ppc)
            
    def add_oscillator(self, oscillator: NeuralOscillator, 
                      group_name: str = None) -> str:
        """
        Add an oscillator to the timing circuits.
        
        Args:
            oscillator: Neural oscillator instance
            group_name: Optional group name for organization
            
        Returns:
            Oscillator ID
        """
        self.oscillators[oscillator.oscillator_id] = oscillator
        
        if group_name:
            self.oscillator_groups[group_name].append(oscillator.oscillator_id)
            
        # Set initial brain state
        oscillator.set_brain_state(self.current_brain_state)
        oscillator.set_arousal_level(self.global_arousal)
        oscillator.set_attention_level(self.global_attention)
        
        logger.debug(f"Added oscillator {oscillator.oscillator_id} to timing circuits")
        return oscillator.oscillator_id
        
    def remove_oscillator(self, oscillator_id: str):
        """Remove an oscillator from the timing circuits."""
        if oscillator_id in self.oscillators:
            # Remove from groups
            for group_name, oscillator_list in self.oscillator_groups.items():
                if oscillator_id in oscillator_list:
                    oscillator_list.remove(oscillator_id)
                    
            # Remove from couplings
            self._remove_oscillator_couplings(oscillator_id)
            
            # Remove oscillator
            del self.oscillators[oscillator_id]
            
            logger.debug(f"Removed oscillator {oscillator_id}")
            
    def _remove_oscillator_couplings(self, oscillator_id: str):
        """Remove all couplings involving the specified oscillator."""
        # Remove PAC couplings
        self.pac_couplings = [
            coupling for coupling in self.pac_couplings
            if (coupling.slow_oscillator.oscillator_id != oscillator_id and
                coupling.fast_oscillator.oscillator_id != oscillator_id)
        ]
        
        # Remove PPC couplings
        self.ppc_couplings = [
            coupling for coupling in self.ppc_couplings
            if (coupling.oscillator1.oscillator_id != oscillator_id and
                coupling.oscillator2.oscillator_id != oscillator_id)
        ]
        
        # Remove AAC couplings
        self.aac_couplings = [
            coupling for coupling in self.aac_couplings
            if (coupling.oscillator1.oscillator_id != oscillator_id and
                coupling.oscillator2.oscillator_id != oscillator_id)
        ]
        
    def add_phase_amplitude_coupling(self, slow_oscillator_id: str, 
                                   fast_oscillator_id: str,
                                   parameters: CouplingParameters = None) -> int:
        """Add phase-amplitude coupling between oscillators."""
        if (slow_oscillator_id not in self.oscillators or 
            fast_oscillator_id not in self.oscillators):
            raise ValueError("Both oscillators must exist in the timing circuits")
            
        if parameters is None:
            parameters = CouplingParameters()
            
        coupling = PhaseAmplitudeCoupling(
            self.oscillators[slow_oscillator_id],
            self.oscillators[fast_oscillator_id],
            parameters
        )
        
        self.pac_couplings.append(coupling)
        coupling_id = len(self.pac_couplings) - 1
        
        logger.debug(f"Added PAC coupling: {slow_oscillator_id} -> {fast_oscillator_id}")
        return coupling_id
        
    def connect_to_core(self, core_neural_system):
        """Connect to core neural system for integrated timing."""
        self.core_neural_system = core_neural_system
        logger.info("BiologicalTimingCircuits connected to core neural system")
        
    def connect_to_neuromodulation(self, neuromodulation_system):
        """Connect to neuromodulation system for rhythm regulation."""
        self.neuromodulation_system = neuromodulation_system
        logger.info("BiologicalTimingCircuits connected to neuromodulation system")
        
    def step(self, dt: float = 1.0) -> Dict[str, Any]:
        """
        Execute one timing circuits update step.
        
        Args:
            dt: Time step in milliseconds
            
        Returns:
            Dictionary with current timing state
        """
        self.current_time += dt
        self.total_updates += 1
        
        # Update global state from neuromodulation
        if self.neuromodulation_system:
            self._update_global_state_from_neuromodulation()
            
        # Update all oscillators
        oscillator_states = {}
        for osc_id, oscillator in self.oscillators.items():
            # Compute external input from neuromodulation
            external_input = self._compute_oscillator_input(oscillator)
            
            # Update oscillator
            state = oscillator.update(dt, external_input)
            oscillator_states[osc_id] = state
            
        # Update all coupling mechanisms
        coupling_states = {}
        
        # Phase-amplitude couplings
        for i, pac_coupling in enumerate(self.pac_couplings):
            coupling_state = pac_coupling.update_coupling(dt)
            coupling_states[f'pac_{i}'] = coupling_state
            
        # Phase-phase couplings
        for i, ppc_coupling in enumerate(self.ppc_couplings):
            coupling_state = ppc_coupling.update_coupling(dt)
            coupling_states[f'ppc_{i}'] = coupling_state
            
        # Amplitude-amplitude couplings
        for i, aac_coupling in enumerate(self.aac_couplings):
            coupling_state = aac_coupling.update_coupling(dt)
            coupling_states[f'aac_{i}'] = coupling_state
            
        # Apply oscillatory modulation to core neural system
        if self.core_neural_system:
            self._apply_oscillatory_modulation_to_core()
            
        # Compute global synchronization measures
        synchronization_metrics = self._compute_global_synchronization()
        
        # Update state history
        current_state = {
            'time': self.current_time,
            'brain_state': self.current_brain_state.name,
            'arousal': self.global_arousal,
            'attention': self.global_attention,
            'oscillator_states': oscillator_states,
            'coupling_states': coupling_states,
            'synchronization': synchronization_metrics
        }
        
        self.state_history.append(current_state)
        self.synchronization_history.append(synchronization_metrics)
        
        return current_state
        
    def _update_global_state_from_neuromodulation(self):
        """Update global brain state from neuromodulation system."""
        modulation_state = self.neuromodulation_system.get_modulation_state()
        concentrations = modulation_state['concentrations']
        
        # Update arousal based on norepinephrine and dopamine
        self.global_arousal = np.clip(
            0.6 * concentrations.get('norepinephrine', 0.5) + 0.4 * concentrations.get('dopamine', 0.5),
            0.0, 1.0
        )
        
        # Update attention based on acetylcholine and norepinephrine
        self.global_attention = np.clip(
            0.7 * concentrations.get('acetylcholine', 0.5) + 0.3 * concentrations.get('norepinephrine', 0.5),
            0.0, 1.0
        )
        
        # Determine brain state
        serotonin_level = concentrations.get('serotonin', 0.5)
        
        if self.global_arousal < 0.2 and serotonin_level > 0.6:
            new_state = BrainState.DEEP_SLEEP
        elif self.global_arousal < 0.4:
            new_state = BrainState.LIGHT_SLEEP
        elif self.global_arousal > 0.8 and self.global_attention > 0.7:
            new_state = BrainState.WAKE_ALERT
        else:
            new_state = BrainState.WAKE_RELAXED
            
        # Update brain state if changed
        if new_state != self.current_brain_state:
            self.set_brain_state(new_state)
            
        # Update all oscillators with new global state
        for oscillator in self.oscillators.values():
            oscillator.set_arousal_level(self.global_arousal)
            oscillator.set_attention_level(self.global_attention)
            
    def _compute_oscillator_input(self, oscillator: NeuralOscillator) -> float:
        """Compute external input for a specific oscillator based on brain state."""
        base_input = 0.0
        
        # State-dependent input
        if oscillator.oscillation_type == OscillationType.DELTA:
            if self.current_brain_state in [BrainState.DEEP_SLEEP, BrainState.LIGHT_SLEEP]:
                base_input += 0.5 * (1.0 - self.global_arousal)
        elif oscillator.oscillation_type == OscillationType.THETA:
            base_input += 0.3 * self.global_attention
            if self.current_brain_state == BrainState.REM_SLEEP:
                base_input += 0.4
        elif oscillator.oscillation_type == OscillationType.ALPHA:
            if self.current_brain_state == BrainState.WAKE_RELAXED:
                base_input += 0.4 * (1.0 - self.global_attention)
        elif oscillator.oscillation_type == OscillationType.BETA:
            if self.current_brain_state == BrainState.WAKE_ALERT:
                base_input += 0.6 * self.global_attention
        elif oscillator.oscillation_type == OscillationType.GAMMA:
            if self.current_brain_state in [BrainState.WAKE_ALERT, BrainState.WAKE_RELAXED]:
                base_input += 0.5 * (self.global_attention + self.global_arousal) / 2
                
        return base_input
        
    def _apply_oscillatory_modulation_to_core(self):
        """Apply oscillatory modulation to core neural system."""
        if not self.core_neural_system:
            return
            
        # Get current oscillatory states
        gamma_amplitude = self.oscillators.get("gamma_primary", None)
        theta_phase = self.oscillators.get("theta_primary", None)
        alpha_amplitude = self.oscillators.get("alpha_primary", None)
        
        # Phase-dependent plasticity modulation
        if gamma_amplitude:
            gamma_modulation = 1.0 + 0.3 * gamma_amplitude.current_amplitude * np.cos(gamma_amplitude.current_phase)
            
            # Apply to plasticity engine if available
            if hasattr(self.core_neural_system, 'neuroplasticity_engine'):
                plasticity_engine = self.core_neural_system.neuroplasticity_engine
                # This would modulate learning rates based on gamma phase
                # Implementation depends on plasticity engine interface
                
        # Coherence-based communication enhancement
        if alpha_amplitude and gamma_amplitude:
            alpha_output = alpha_amplitude.get_output_signal()
            gamma_output = gamma_amplitude.get_output_signal()
            coherence_factor = 1.0 + 0.2 * alpha_output * gamma_output
            
            # Apply to connection strengths
            for connection in self.core_neural_system.connections.values():
                if not hasattr(connection, 'base_conductance'):
                    connection.base_conductance = connection.parameters.max_conductance
                connection.parameters.max_conductance = connection.base_conductance * coherence_factor
                
    def _compute_global_synchronization(self) -> Dict[str, float]:
        """Compute global synchronization metrics across all oscillators."""
        if len(self.oscillators) < 2:
            return {'kuramoto_order': 0.0, 'mean_coherence': 0.0, 'phase_dispersion': 0.0}
            
        # Kuramoto order parameter
        phases = [osc.current_phase for osc in self.oscillators.values()]
        kuramoto_order = np.abs(np.mean(np.exp(1j * np.array(phases))))
        
        # Phase dispersion
        _, phase_concentration = circular_mean_resultant(np.array(phases))
        phase_dispersion = 1.0 - phase_concentration
        
        # Mean coupling strength
        if self.pac_couplings:
            mean_pac_strength = np.mean([
                coupling.parameters.modulation_depth for coupling in self.pac_couplings
            ])
        else:
            mean_pac_strength = 0.0
            
        return {
            'kuramoto_order': kuramoto_order,
            'mean_pac_strength': mean_pac_strength,
            'phase_dispersion': phase_dispersion,
            'n_oscillators': len(self.oscillators),
            'n_couplings': len(self.pac_couplings) + len(self.ppc_couplings) + len(self.aac_couplings)
        }
        
    def set_brain_state(self, state: BrainState):
        """Set global brain state and update all oscillators."""
        self.current_brain_state = state
        
        for oscillator in self.oscillators.values():
            oscillator.set_brain_state(state)
            
        logger.info(f"Brain state set to {state.name}")
        
    def set_arousal_level(self, level: float):
        """Set global arousal level."""
        self.global_arousal = np.clip(level, 0.0, 1.0)
        
        for oscillator in self.oscillators.values():
            oscillator.set_arousal_level(self.global_arousal)
            
    def set_attention_level(self, level: float):
        """Set global attention level."""
        self.global_attention = np.clip(level, 0.0, 1.0)
        
        for oscillator in self.oscillators.values():
            oscillator.set_attention_level(self.global_attention)
            
    def get_oscillatory_state(self) -> Dict[str, Any]:
        """Get comprehensive oscillatory state information."""
        oscillator_states = {}
        for osc_id, oscillator in self.oscillators.items():
            oscillator_states[osc_id] = {
                'frequency': oscillator.current_frequency,
                'phase': oscillator.current_phase,
                'amplitude': oscillator.current_amplitude,
                'output': oscillator.get_output_signal(),
                'type': oscillator.oscillation_type.name
            }
            
        coupling_states = {}
        for i, coupling in enumerate(self.pac_couplings):
            coupling_states[f'pac_{i}'] = coupling.get_coupling_state()
            
        return {
            'oscillators': oscillator_states,
            'couplings': coupling_states,
            'global_state': {
                'brain_state': self.current_brain_state.name,
                'arousal': self.global_arousal,
                'attention': self.global_attention,
                'current_time': self.current_time
            },
            'synchronization': self._compute_global_synchronization()
        }
        
    def compute_spectral_power(self, oscillator_id: str = None, 
                             window_size: int = 1024) -> Dict[str, Any]:
        """Compute spectral power for oscillator(s)."""
        if oscillator_id:
            if oscillator_id not in self.oscillators:
                raise ValueError(f"Oscillator {oscillator_id} not found")
            return self.spectral_analyzer.analyze_oscillator_spectrum(
                self.oscillators[oscillator_id], window_size
            )
        else:
            # Compute for all oscillators
            results = {}
            for osc_id, oscillator in self.oscillators.items():
                results[osc_id] = self.spectral_analyzer.analyze_oscillator_spectrum(
                    oscillator, window_size
                )
            return results
            
    def compute_coherence_matrix(self, window_length: int = 1000) -> Dict[str, Any]:
        """Compute coherence matrix between all oscillator pairs."""
        oscillator_list = list(self.oscillators.values())
        return self.coherence_analyzer.analyze_oscillator_synchronization(
            oscillator_list, window_length
        )
        
    def analyze_cross_frequency_coupling(self, slow_osc_id: str, fast_osc_id: str,
                                       window_length: int = 500) -> Dict[str, float]:
        """Analyze cross-frequency coupling between two oscillators."""
        if slow_osc_id not in self.oscillators or fast_osc_id not in self.oscillators:
            raise ValueError("Both oscillators must exist")
            
        slow_osc = self.oscillators[slow_osc_id]
        fast_osc = self.oscillators[fast_osc_id]
        
        # Find existing PAC coupling or create temporary one
        pac_coupling = None
        for coupling in self.pac_couplings:
            if (coupling.slow_oscillator.oscillator_id == slow_osc_id and
                coupling.fast_oscillator.oscillator_id == fast_osc_id):
                pac_coupling = coupling
                break
                
        if pac_coupling is None:
            # Create temporary coupling for analysis
            temp_params = CouplingParameters()
            pac_coupling = PhaseAmplitudeCoupling(slow_osc, fast_osc, temp_params)
            
        return pac_coupling.compute_detailed_coupling_metrics(window_length)
        
    def reset_all_oscillators(self):
        """Reset all oscillators to initial state."""
        for oscillator in self.oscillators.values():
            oscillator.reset_state()
            
        self.current_time = 0.0
        self.total_updates = 0
        self.state_history.clear()
        self.synchronization_history.clear()
        
        logger.info("All oscillators reset to initial state")
        
    def get_timing_statistics(self) -> Dict[str, Any]:
        """Get comprehensive timing system statistics."""
        stats = {
            'system_info': {
                'n_oscillators': len(self.oscillators),
                'n_pac_couplings': len(self.pac_couplings),
                'n_ppc_couplings': len(self.ppc_couplings),
                'n_aac_couplings': len(self.aac_couplings),
                'current_time': self.current_time,
                'total_updates': self.total_updates,
                'brain_state': self.current_brain_state.name
            },
            'oscillator_stats': {},
            'coupling_stats': {},
            'synchronization_stats': {}
        }
        
        # Individual oscillator statistics
        for osc_id, osc in self.oscillators.items():
            stats['oscillator_stats'][osc_id] = {
                'type': osc.oscillation_type.name,
                'current_frequency': osc.current_frequency,
                'theoretical_frequency': osc.parameters.frequency,
                'frequency_drift': osc.current_frequency - osc.parameters.frequency,
                'current_amplitude': osc.current_amplitude,
                'phase_resets': osc.phase_resets,
                'total_updates': osc.total_updates
            }
            
        # Coupling statistics
        for i, coupling in enumerate(self.pac_couplings):
            coupling_state = coupling.get_coupling_state()
            stats['coupling_stats'][f'pac_{i}'] = {
                'slow_oscillator': coupling.slow_oscillator.oscillator_id,
                'fast_oscillator': coupling.fast_oscillator.oscillator_id,
                'modulation_index': coupling_state['modulation_index'],
                'coupling_strength': coupling_state['coupling_strength']
            }
            
        # Recent synchronization statistics
        if self.synchronization_history:
            recent_sync = list(self.synchronization_history)[-10:]  # Last 10 measurements
            stats['synchronization_stats'] = {
                'mean_kuramoto_order': np.mean([s['kuramoto_order'] for s in recent_sync]),
                'mean_phase_dispersion': np.mean([s['phase_dispersion'] for s in recent_sync]),
                'synchronization_stability': np.std([s['kuramoto_order'] for s in recent_sync])
            }
            
        return stats
        
    def save_state(self, filepath: str):
        """Save complete timing circuits state."""
        state_data = {
            'config': self.config,
            'current_time': self.current_time,
            'brain_state': self.current_brain_state.name,
            'global_arousal': self.global_arousal,
            'global_attention': self.global_attention,
            'oscillators': {},
            'couplings': {
                'pac': [],
                'ppc': [],
                'aac': []
            },
            'state_history': list(self.state_history),
            'synchronization_history': list(self.synchronization_history)
        }
        
        # Save oscillator states
        for osc_id, osc in self.oscillators.items():
            state_data['oscillators'][osc_id] = {
                'oscillator_id': osc.oscillator_id,
                'oscillation_type': osc.oscillation_type.name,
                'parameters': osc.parameters.__dict__,
                'current_phase': osc.current_phase,
                'current_frequency': osc.current_frequency,
                'current_amplitude': osc.current_amplitude,
                'phase_history': list(osc.phase_history),
                'amplitude_history': list(osc.amplitude_history),
                'frequency_history': list(osc.frequency_history)
            }
            
        # Save coupling configurations
        for coupling in self.pac_couplings:
            state_data['couplings']['pac'].append({
                'slow_oscillator_id': coupling.slow_oscillator.oscillator_id,
                'fast_oscillator_id': coupling.fast_oscillator.oscillator_id,
                'parameters': coupling.parameters.__dict__
            })
            
        with open(filepath, 'wb') as f:
            pickle.dump(state_data, f)
            
        logger.info(f"Timing circuits state saved to {filepath}")
        
    def visualize_oscillator_network(self, figure_size: Tuple[int, int] = (12, 8),
                                   show_couplings: bool = True) -> Any:
        """
        Visualize the oscillator network and couplings.
        
        Args:
            figure_size: Figure size (width, height)
            show_couplings: Whether to show coupling connections
            
        Returns:
            Matplotlib figure object (if matplotlib available)
        """
        if not MATPLOTLIB_AVAILABLE:
            logger.warning("Matplotlib not available for visualization")
            return None
            
        fig, axes = plt.subplots(2, 2, figsize=figure_size)
        
        # Plot 1: Oscillator frequencies and amplitudes
        ax1 = axes[0, 0]
        osc_names = list(self.oscillators.keys())
        frequencies = [osc.current_frequency for osc in self.oscillators.values()]
        amplitudes = [osc.current_amplitude for osc in self.oscillators.values()]
        
        x_pos = np.arange(len(osc_names))
        ax1.bar(x_pos, frequencies, alpha=0.7, label='Frequency (Hz)')
        ax1_twin = ax1.twinx()
        ax1_twin.bar(x_pos + 0.4, amplitudes, alpha=0.7, color='orange', label='Amplitude')
        
        ax1.set_xlabel('Oscillators')
        ax1.set_ylabel('Frequency (Hz)', color='blue')
        ax1_twin.set_ylabel('Amplitude', color='orange')
        ax1.set_xticks(x_pos)
        ax1.set_xticklabels(osc_names, rotation=45)
        ax1.set_title('Oscillator Frequencies and Amplitudes')
        
        # Plot 2: Phase relationships (polar plot)
        ax2 = plt.subplot(2, 2, 2, projection='polar')
        phases = [osc.current_phase for osc in self.oscillators.values()]
        colors = plt.cm.viridis(np.linspace(0, 1, len(phases)))
        
        for i, (phase, name) in enumerate(zip(phases, osc_names)):
            ax2.scatter(phase, 1, c=[colors[i]], s=100, label=name)
            
        ax2.set_title('Current Phase Relationships')
        ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        
        # Plot 3: Synchronization over time
        ax3 = axes[1, 0]
        if self.synchronization_history:
            sync_data = list(self.synchronization_history)
            times = [i for i in range(len(sync_data))]
            kuramoto_orders = [s['kuramoto_order'] for s in sync_data]
            
            ax3.plot(times, kuramoto_orders, 'b-', linewidth=2)
            ax3.set_xlabel('Time Steps')
            ax3.set_ylabel('Kuramoto Order Parameter')
            ax3.set_title('Network Synchronization Over Time')
            ax3.grid(True, alpha=0.3)
            
        # Plot 4: Coupling strengths
        ax4 = axes[1, 1]
        if self.pac_couplings:
            coupling_names = [f"{c.slow_oscillator.oscillator_id}->{c.fast_oscillator.oscillator_id}" 
                            for c in self.pac_couplings]
            coupling_strengths = [c.parameters.modulation_depth for c in self.pac_couplings]
            
            y_pos = np.arange(len(coupling_names))
            ax4.barh(y_pos, coupling_strengths, alpha=0.7)
            ax4.set_yticks(y_pos)
            ax4.set_yticklabels(coupling_names)
            ax4.set_xlabel('Coupling Strength')
            ax4.set_title('Phase-Amplitude Coupling Strengths')
            
        plt.tight_layout()
        return fig


# ===========================================================================================
# FACTORY FUNCTIONS AND UTILITIES
# ===========================================================================================

def create_default_oscillators(timing_circuits: BiologicalTimingCircuits,
                             network: Dict[str, Any] = None,
                             **kwargs) -> Dict[str, NeuralOscillator]:
    """
    Create and configure default set of neural oscillators.
    
    Args:
        timing_circuits: BiologicalTimingCircuits instance
        network: Optional network information for configuration
        **kwargs: Additional configuration parameters
        
    Returns:
        Dictionary of created oscillators
    """
    oscillators = {}
    
    # Default frequency settings
    default_frequencies = kwargs.get('frequencies', {
        'delta': 2.0,
        'theta': 6.0,
        'alpha': 10.0,
        'beta': 20.0,
        'gamma': 40.0,
        'high_gamma': 120.0
    })
    
    # Default amplitude settings
    default_amplitudes = kwargs.get('amplitudes', {
        'delta': 1.0,
        'theta': 0.8,
        'alpha': 0.6,
        'beta': 0.4,
        'gamma': 0.3,
        'high_gamma': 0.2
    })
    
    # Create oscillators
    for osc_type, frequency in default_frequencies.items():
        amplitude = default_amplitudes.get(osc_type, 0.5)
        
        if osc_type == 'delta':
            params = OscillatorParameters(
                frequency=frequency, amplitude=amplitude,
                noise_level=0.02, arousal_sensitivity=0.8,
                min_frequency=0.5, max_frequency=4.0
            )
            osc = DeltaOscillator(f"default_{osc_type}", params)
            
        elif osc_type == 'theta':
            params = OscillatorParameters(
                frequency=frequency, amplitude=amplitude,
                noise_level=0.015, arousal_sensitivity=0.4,
                attention_sensitivity=0.6, min_frequency=4.0, max_frequency=8.0
            )
            osc = ThetaOscillator(f"default_{osc_type}", params)
            
        elif osc_type == 'alpha':
            params = OscillatorParameters(
                frequency=frequency, amplitude=amplitude,
                noise_level=0.01, arousal_sensitivity=0.5,
                attention_sensitivity=0.8, min_frequency=8.0, max_frequency=13.0
            )
            osc = AlphaOscillator(f"default_{osc_type}", params)
            
        elif osc_type == 'beta':
            params = OscillatorParameters(
                frequency=frequency, amplitude=amplitude,
                noise_level=0.02, arousal_sensitivity=0.3,
                attention_sensitivity=0.7, min_frequency=13.0, max_frequency=30.0
            )
            osc = BetaOscillator(f"default_{osc_type}", params)
            
        elif osc_type == 'gamma':
            params = OscillatorParameters(
                frequency=frequency, amplitude=amplitude,
                noise_level=0.03, arousal_sensitivity=0.6,
                attention_sensitivity=0.9, min_frequency=30.0, max_frequency=100.0
            )
            osc = GammaOscillator(f"default_{osc_type}", params)
            
        elif osc_type == 'high_gamma':
            params = OscillatorParameters(
                frequency=frequency, amplitude=amplitude,
                noise_level=0.05, arousal_sensitivity=0.4,
                attention_sensitivity=0.8, min_frequency=80.0, max_frequency=200.0
            )
            osc = HighGammaOscillator(f"default_{osc_type}", params)
            
        # Add to timing circuits and collection
        timing_circuits.add_oscillator(osc, group_name="default_set")
        oscillators[osc_type] = osc
        
    logger.info(f"Created {len(oscillators)} default oscillators")
    return oscillators


def create_sleep_oscillators(timing_circuits: BiologicalTimingCircuits,
                           network: Dict[str, Any] = None,
                           **kwargs) -> Dict[str, NeuralOscillator]:
    """
    Create oscillators optimized for sleep states.
    
    Args:
        timing_circuits: BiologicalTimingCircuits instance
        network: Optional network information
        **kwargs: Additional parameters
        
    Returns:
        Dictionary of sleep-optimized oscillators
    """
    oscillators = {}
    
    # Enhanced delta for deep sleep
    delta_params = OscillatorParameters(
        frequency=1.5, amplitude=1.5, noise_level=0.01,
        arousal_sensitivity=1.0, min_frequency=0.5, max_frequency=4.0
    )
    delta_osc = DeltaOscillator("sleep_delta", delta_params)
    delta_osc.set_homeostatic_drive(0.8)  # High sleep drive
    
    # Sleep spindles (sigma band around 12-14 Hz)
    spindle_params = OscillatorParameters(
        frequency=12.0, amplitude=0.8, noise_level=0.02,
        arousal_sensitivity=0.9, min_frequency=11.0, max_frequency=15.0
    )
    spindle_osc = BetaOscillator("sleep_spindle", spindle_params)
    
    # Reduced theta for NREM sleep
    theta_params = OscillatorParameters(
        frequency=5.0, amplitude=0.3, noise_level=0.01,
        arousal_sensitivity=0.6, min_frequency=4.0, max_frequency=8.0
    )
    theta_osc = ThetaOscillator("sleep_theta", theta_params)
    
    # Add to timing circuits
    for name, osc in [("delta", delta_osc), ("spindle", spindle_osc), ("theta", theta_osc)]:
        timing_circuits.add_oscillator(osc, group_name="sleep_set")
        oscillators[name] = osc
        
    # Set up sleep-specific couplings
    delta_spindle_params = CouplingParameters(
        modulation_depth=0.6, phase_preference=0.0,
        plasticity_rate=0.02, homeostatic_target=0.5
    )
    timing_circuits.pac_couplings.append(
        PhaseAmplitudeCoupling(delta_osc, spindle_osc, delta_spindle_params)
    )
    
    logger.info("Created sleep-optimized oscillator set")
    return oscillators


def create_attention_oscillators(timing_circuits: BiologicalTimingCircuits,
                               network: Dict[str, Any] = None,
                               **kwargs) -> Dict[str, NeuralOscillator]:
    """
    Create oscillators optimized for attention states.
    
    Args:
        timing_circuits: BiologicalTimingCircuits instance
        network: Optional network information
        **kwargs: Additional parameters
        
    Returns:
        Dictionary of attention-optimized oscillators
    """
    oscillators = {}
    
    # Suppressed alpha for focused attention
    alpha_params = OscillatorParameters(
        frequency=10.0, amplitude=0.2, noise_level=0.005,
        arousal_sensitivity=0.3, attention_sensitivity=1.0,
        min_frequency=8.0, max_frequency=13.0
    )
    alpha_osc = AlphaOscillator("attention_alpha", alpha_params)
    alpha_osc.set_sensory_gating_strength(0.8)
    
    # Enhanced beta for focused attention
    beta_params = OscillatorParameters(
        frequency=18.0, amplitude=0.8, noise_level=0.02,
        arousal_sensitivity=0.5, attention_sensitivity=1.0,
        min_frequency=13.0, max_frequency=30.0
    )
    beta_osc = BetaOscillator("attention_beta", beta_params)
    beta_osc.set_cognitive_load(0.8)
    
    # Enhanced gamma for binding
    gamma_params = OscillatorParameters(
        frequency=45.0, amplitude=0.7, noise_level=0.03,
        arousal_sensitivity=0.7, attention_sensitivity=1.0,
        min_frequency=30.0, max_frequency=100.0
    )
    gamma_osc = GammaOscillator("attention_gamma", gamma_params)
    gamma_osc.set_binding_demand(0.9)
    gamma_osc.set_conscious_access(0.8)
    
    # Add to timing circuits
    for name, osc in [("alpha", alpha_osc), ("beta", beta_osc), ("gamma", gamma_osc)]:
        timing_circuits.add_oscillator(osc, group_name="attention_set")
        oscillators[name] = osc
        
    # Set up attention-specific couplings
    beta_gamma_params = CouplingParameters(
        modulation_depth=0.5, phase_preference=0.0,
        plasticity_rate=0.01, homeostatic_target=0.4
    )
    timing_circuits.pac_couplings.append(
        PhaseAmplitudeCoupling(beta_osc, gamma_osc, beta_gamma_params)
    )
    
    logger.info("Created attention-optimized oscillator set")
    return oscillators


def create_pathological_oscillators(timing_circuits: BiologicalTimingCircuits,
                                  pathology_type: str = "parkinson",
                                  **kwargs) -> Dict[str, NeuralOscillator]:
    """
    Create oscillators modeling pathological brain states.
    
    Args:
        timing_circuits: BiologicalTimingCircuits instance
        pathology_type: Type of pathology ('parkinson', 'epilepsy', 'alzheimer')
        **kwargs: Additional parameters
        
    Returns:
        Dictionary of pathological oscillators
    """
    oscillators = {}
    
    if pathology_type == "parkinson":
        # Excessive beta synchrony in motor circuits
        pathological_beta_params = OscillatorParameters(
            frequency=15.0, amplitude=1.2, noise_level=0.01,
            arousal_sensitivity=0.2, min_frequency=13.0, max_frequency=30.0
        )
        beta_osc = BetaOscillator("pathological_beta", pathological_beta_params)
        beta_osc.set_pathological_synchrony(0.9)
        beta_osc.set_dopamine_level(0.2)  # Low dopamine
        
        oscillators["pathological_beta"] = beta_osc
        timing_circuits.add_oscillator(beta_osc, group_name="pathological_set")
        
    elif pathology_type == "epilepsy":
        # High-frequency epileptiform activity
        epileptic_params = OscillatorParameters(
            frequency=150.0, amplitude=2.0, noise_level=0.1,
            arousal_sensitivity=0.1, min_frequency=100.0, max_frequency=300.0
        )
        epileptic_osc = HighGammaOscillator("epileptic_oscillator", epileptic_params)
        epileptic_osc.set_pathological_activity(1.0)
        
        oscillators["epileptic"] = epileptic_osc
        timing_circuits.add_oscillator(epileptic_osc, group_name="pathological_set")
        
    elif pathology_type == "alzheimer":
        # Disrupted gamma and theta rhythms
        disrupted_gamma_params = OscillatorParameters(
            frequency=35.0, amplitude=0.1, noise_level=0.05,
            arousal_sensitivity=0.1, attention_sensitivity=0.1,  # Reduced sensitivity
            min_frequency=30.0, max_frequency=100.0
        )
        gamma_osc = GammaOscillator("disrupted_gamma", disrupted_gamma_params)
        # Configure for pathological state
        gamma_osc.set_binding_demand(0.1)  # Very low binding
        gamma_osc.set_conscious_access(0.1)  # Reduced conscious access
        gamma_osc.set_attention_level(0.2)  # Low attention
        gamma_osc.set_arousal_level(0.3)   # Low arousal
        
        disrupted_theta_params = OscillatorParameters(
            frequency=4.5, amplitude=0.4, noise_level=0.03,
            arousal_sensitivity=0.2, attention_sensitivity=0.2,  # Reduced sensitivity
            min_frequency=4.0, max_frequency=8.0
        )
        theta_osc = ThetaOscillator("disrupted_theta", disrupted_theta_params)
        # Configure for pathological state
        theta_osc.set_memory_load(0.1)  # Impaired memory function
        theta_osc.set_attention_level(0.2)
        theta_osc.set_arousal_level(0.3)
        
        oscillators["disrupted_gamma"] = gamma_osc
        oscillators["disrupted_theta"] = theta_osc
        
        for osc in oscillators.values():
            timing_circuits.add_oscillator(osc, group_name="pathological_set")
            
    logger.info(f"Created {pathology_type} pathological oscillator set")
    return oscillators


# ===========================================================================================
# INITIALIZATION AND CONFIGURATION
# ===========================================================================================

def initialize_timing_circuits(config: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Initialize biological timing circuits with given configuration.
    
    Args:
        config: Configuration dictionary
        
    Returns:
        Initialization status dictionary
    """
    try:
        if config is None:
            config = {}
            
        # Default configuration
        default_config = {
            'oscillators_enabled': ['delta', 'theta', 'alpha', 'beta', 'gamma'],
            'default_frequencies': {
                'delta': 2.0, 'theta': 6.0, 'alpha': 10.0,
                'beta': 20.0, 'gamma': 40.0
            },
            'coupling_enabled': True,
            'phase_amplitude_coupling': True,
            'coherence_analysis': True,
            'spectral_analysis': True
        }
        
        merged_config = {**default_config, **config}
        
        logger.info("Initializing BiologicalTimingCircuits")
        
        return {
            'status': 'success',
            'config': merged_config,
            'components_initialized': [
                'neural_oscillators', 'coupling_mechanisms',
                'coherence_analyzer', 'spectral_analyzer'
            ],
            'oscillator_types_available': [
                'DeltaOscillator', 'ThetaOscillator', 'AlphaOscillator',
                'BetaOscillator', 'GammaOscillator', 'HighGammaOscillator'
            ],
            'coupling_types_available': [
                'PhaseAmplitudeCoupling', 'PhasePhaseCoupling',
                'AmplitudeAmplitudeCoupling'
            ]
        }
        
    except Exception as e:
        logger.error(f"Failed to initialize biological timing circuits: {str(e)}")
        return {
            'status': 'failed',
            'error': str(e)
        }


# ===========================================================================================
# MODULE EXPORTS AND INITIALIZATION
# ===========================================================================================

# Define all exported symbols
__all__ = [
    # Core enumerations
    'OscillationType', 'CouplingType', 'CoherenceType', 'BrainState',
    
    # Parameter classes
    'OscillatorParameters', 'CouplingParameters', 'SpectralFeatures',
    
    # Base oscillator classes
    'NeuralOscillator',
    
    # Specific oscillator implementations
    'DeltaOscillator', 'ThetaOscillator', 'AlphaOscillator',
    'BetaOscillator', 'GammaOscillator', 'HighGammaOscillator',
    
    # Cross-frequency coupling mechanisms
    'PhaseAmplitudeCoupling', 'PhasePhaseCoupling', 'AmplitudeAmplitudeCoupling',
    
    # Analysis tools
    'CoherenceAnalyzer', 'SpectralAnalyzer',
    
    # Main timing system
    'BiologicalTimingCircuits',
    
    # Utility functions
    'kuramoto_coupling', 'phase_amplitude_modulation', 'circular_mean_resultant',
    'instantaneous_phase_amplitude', 'compute_phase_locking_value',
    'compute_modulation_index',
    
    # Factory functions
    'create_default_oscillators', 'create_sleep_oscillators',
    'create_attention_oscillators', 'create_pathological_oscillators',
    
    # Initialization
    'initialize_timing_circuits'
]

# Module metadata
__version__ = '1.0.0'
__author__ = 'ULTRA Development Team'
__description__ = 'Biological Timing Circuits for ULTRA: Neural oscillation and rhythm coordination'
__license__ = 'MIT'

# Module initialization
logger.info(f"ULTRA Biological Timing Circuits v{__version__} loaded successfully")

# Log available computational backends
backends = ['numpy']
if CUPY_AVAILABLE:
    backends.append('cupy')
if JAX_AVAILABLE:
    backends.append('jax')
if NUMBA_AVAILABLE:
    backends.append('numba')
if MATPLOTLIB_AVAILABLE:
    backends.append('matplotlib')
    
logger.info(f"Available computational backends: {', '.join(backends)}")

# Performance optimization warnings
if not NUMBA_AVAILABLE:
    logger.warning("Numba not available - some functions may run slower")
    
if not MATPLOTLIB_AVAILABLE:
    logger.warning("Matplotlib not available - visualization functions disabled")

# Cleanup function
def _cleanup_timing_circuits():
    """Cleanup function for module shutdown."""
    logger.info("Cleaning up Biological Timing Circuits resources")
    gc.collect()

import atexit
atexit.register(_cleanup_timing_circuits)