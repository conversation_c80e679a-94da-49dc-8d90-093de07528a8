#!/usr/bin/env python3
"""
ULTRA Neuromorphic Core
=======================

This module implements the foundational neuromorphic computing architecture for ULTRA.
The Neuromorphic Core is a biologically-inspired neural network system that closely
resembles brain structure and function, with heterogeneous neuron types, 3D connectivity,
and realistic neural dynamics.

The core implements:
- Multiple neuron models: LIF, AdEx, <PERSON><PERSON><PERSON>vich
- True neuroplasticity with weight modification
- Synaptic pruning based on activity and importance
- Neuromodulation with multiple transmitter types
- Biological timing circuits for oscillatory dynamics
- 3D spatial organization with distance-dependent connectivity
- GPU-accelerated computation for efficiency

This implementation is designed for:
- Performance through vectorized operations
- Memory efficiency for large-scale simulations
- Integration with other ULTRA subsystems
- Visualization and monitoring capabilities
- State persistence for evolutionary optimization
"""

import numpy as np
import scipy.sparse
from scipy.spatial.distance import cdist
import matplotlib.pyplot as plt
from matplotlib import animation
from mpl_toolkits.mplot3d import Axes3D
import time
import logging
from enum import Enum, auto
from typing import Dict, List, Tuple, Set, Any, Optional, Union, Callable
from dataclasses import dataclass, field
from collections import defaultdict, deque
import warnings
import copy
import os
import pickle
import uuid
import time
import threading
import queue
import multiprocessing as mp
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import psutil

# Advanced numerical libraries with fallbacks
try:
    import cupy as cp
    CUPY_AVAILABLE = True
except ImportError:
    CUPY_AVAILABLE = False
    cp = np

try:
    import jax
    import jax.numpy as jnp
    from jax import jit, vmap, grad, random as jax_random
    JAX_AVAILABLE = True
except ImportError:
    JAX_AVAILABLE = False
    jax = None
    jnp = np

try:
    import numba
    from numba import njit, prange, types
    NUMBA_AVAILABLE = True
except ImportError:
    NUMBA_AVAILABLE = False
    def njit(func):
        return func
    prange = range

# ===========================================================================================
# HARDWARE DETECTION AND CONFIGURATION
# ===========================================================================================

def detect_hardware_capabilities() -> Dict[str, Any]:
    """Detect available hardware for neural computation."""
    hardware = {'detected_backends': []}
    
    # Check for CUDA/GPU acceleration
    if CUPY_AVAILABLE:
        try:
            import cupy as cp
            hardware['gpu'] = {
                'available': True,
                'device_count': cp.cuda.runtime.getDeviceCount(),
                'device_name': cp.cuda.runtime.getDeviceProperties(0)['name'].decode(),
                'memory_total': cp.cuda.runtime.memGetInfo()[1],
                'memory_free': cp.cuda.runtime.memGetInfo()[0],
                'compute_capability': cp.cuda.runtime.getDeviceProperties(0)['major']
            }
            hardware['detected_backends'].append('cupy')
        except Exception as e:
            hardware['gpu'] = {'available': False, 'error': str(e)}
    else:
        hardware['gpu'] = {'available': False, 'reason': 'CuPy not installed'}
    
    # Check for JAX acceleration
    if JAX_AVAILABLE:
        try:
            import jax
            hardware['jax'] = {
                'available': True,
                'backend': jax.default_backend(),
                'devices': [str(d) for d in jax.devices()],
                'version': jax.__version__
            }
            hardware['detected_backends'].append('jax')
        except Exception as e:
            hardware['jax'] = {'available': False, 'error': str(e)}
    else:
        hardware['jax'] = {'available': False, 'reason': 'JAX not installed'}
    
    # Check for Numba JIT compilation
    if NUMBA_AVAILABLE:
        try:
            import numba
            hardware['numba'] = {
                'available': True,
                'version': numba.__version__,
                'threading_layer': numba.config.THREADING_LAYER,
                'num_threads': numba.config.NUMBA_NUM_THREADS
            }
            hardware['detected_backends'].append('numba')
        except Exception as e:
            hardware['numba'] = {'available': False, 'error': str(e)}
    else:
        hardware['numba'] = {'available': False, 'reason': 'Numba not installed'}
    
    # Check CPU capabilities
    try:
        hardware['cpu'] = {
            'available': True,
            'count': psutil.cpu_count(),
            'count_logical': psutil.cpu_count(logical=True),
            'freq_current': psutil.cpu_freq().current if psutil.cpu_freq() else None,
            'memory_total': psutil.virtual_memory().total,
            'memory_available': psutil.virtual_memory().available
        }
    except ImportError:
        hardware['cpu'] = {
            'available': True,
            'count': mp.cpu_count(),
            'count_logical': mp.cpu_count()
        }
    
    hardware['recommended_backend'] = _select_optimal_backend(hardware)
    return hardware

def _select_optimal_backend(hardware: Dict[str, Any]) -> str:
    """Select optimal computational backend."""
    if hardware.get('gpu', {}).get('available', False):
        return 'cupy'
    elif hardware.get('jax', {}).get('available', False):
        return 'jax'
    elif hardware.get('numba', {}).get('available', False):
        return 'numba'
    else:
        return 'numpy'

# ===========================================================================================
# OPTIMIZED COMPUTATION KERNELS
# ===========================================================================================

if NUMBA_AVAILABLE:
    @njit(parallel=True)
    def update_neurons_parallel(voltages, synaptic_currents, external_currents,
                               v_rest, v_threshold, v_reset, tau_m, r_m, dt):
        """Numba-optimized parallel neuron update."""
        n_neurons = len(voltages)
        spikes = np.zeros(n_neurons, dtype=np.bool_)
        
        for i in prange(n_neurons):
            # LIF dynamics
            i_total = synaptic_currents[i] + external_currents[i]
            dv = (-(voltages[i] - v_rest[i]) + r_m[i] * i_total) * (dt / tau_m[i])
            voltages[i] += dv
            
            # Check for spike
            if voltages[i] >= v_threshold[i]:
                voltages[i] = v_reset[i]
                spikes[i] = True
        
        return spikes

    @njit(parallel=True)
    def compute_synaptic_currents_parallel(conductances, voltages, reversal_potentials):
        """Numba-optimized synaptic current computation."""
        n_neurons = len(voltages)
        currents = np.zeros(n_neurons)
        
        for i in prange(n_neurons):
            currents[i] = conductances[i] * (voltages[i] - reversal_potentials[i])
        
        return currents
else:
    # Fallback numpy implementations
    def update_neurons_parallel(voltages, synaptic_currents, external_currents,
                               v_rest, v_threshold, v_reset, tau_m, r_m, dt):
        i_total = synaptic_currents + external_currents
        dv = (-(voltages - v_rest) + r_m * i_total) * (dt / tau_m)
        voltages += dv
        
        spikes = voltages >= v_threshold
        voltages[spikes] = v_reset[spikes]
        
        return spikes

    def compute_synaptic_currents_parallel(conductances, voltages, reversal_potentials):
        return conductances * (voltages - reversal_potentials)

# For hardware acceleration
try:
    import torch
    import torch.nn as nn
    HAS_TORCH = True
except ImportError:
    HAS_TORCH = False
    warnings.warn("PyTorch not found. GPU acceleration will not be available.")

try:
    import jax
    import jax.numpy as jnp
    HAS_JAX = True
except ImportError:
    HAS_JAX = False

# Configure module logger
logger = logging.getLogger(__name__)


class NeuronModel(Enum):
    """Supported neuron models in the neuromorphic core."""
    LIF = auto()               # Leaky Integrate-and-Fire
    ADEX = auto()              # Adaptive Exponential Integrate-and-Fire
    IZHIKEVICH = auto()        # Izhikevich
    QIF = auto()               # Quadratic Integrate-and-Fire
    HH = auto()                # Hodgkin-Huxley (complex biophysical model)
    FN = auto()                # FitzHugh-Nagumo (simplified biophysical)


class NeuronType(Enum):
    """Types of neurons in the neuromorphic core."""
    EXCITATORY = auto()        # Regular spiking pyramidal neurons (80%)
    INHIBITORY = auto()        # Fast-spiking inhibitory interneurons (10%)
    ADAPTIVE = auto()          # Adaptive resonance neurons (5%) 
    MODULATORY = auto()        # Neuromodulatory neurons (5%)
    SENSORY = auto()           # Input neurons
    MOTOR = auto()             # Output neurons
    RESONATOR = auto()         # Oscillator neurons with resonance properties
    RELAY = auto()             # Thalamic relay neurons
    CUSTOM = auto()            # User-defined neuron type


class SynapseType(Enum):
    """Types of synapses in the neuromorphic core."""
    EXCITATORY = auto()        # Glutamatergic excitatory synapses
    INHIBITORY = auto()        # GABAergic inhibitory synapses
    MODULATORY = auto()        # Dopaminergic, serotonergic, etc.
    ELECTRICAL = auto()        # Gap junctions (direct electrical)
    PLASTIC = auto()           # Explicitly plastic synapses
    STATIC = auto()            # Non-plastic synapses
    DYNAMIC = auto()           # Short-term plasticity
    CUSTOM = auto()            # User-defined synapse type


class NeuromodulatorType(Enum):
    """Types of neuromodulators in the network."""
    DOPAMINE = auto()          # Reward, motivation, reinforcement
    SEROTONIN = auto()         # Mood, anxiety, sleep
    NOREPINEPHRINE = auto()    # Attention, arousal, vigilance
    ACETYLCHOLINE = auto()     # Learning, memory, attention
    CUSTOM = auto()            # User-defined neuromodulator


class OscillationType(Enum):
    """Types of neural oscillations in the network."""
    DELTA = auto()             # 0.5-4 Hz, deep sleep
    THETA = auto()             # 4-8 Hz, memory formation
    ALPHA = auto()             # 8-12 Hz, relaxed wakefulness
    BETA = auto()              # 12-30 Hz, normal wakefulness
    GAMMA = auto()             # 30-100 Hz, cognitive processing
    CUSTOM = auto()            # User-defined oscillation


@dataclass
class NeuronParameters:
    """Parameters for neuron models."""
    # Core parameters used across multiple models
    v_rest: float = -70.0      # Resting potential (mV)
    v_reset: float = -70.0     # Reset potential (mV)
    v_threshold: float = -50.0 # Threshold potential (mV)
    tau_m: float = 20.0        # Membrane time constant (ms)
    r_membrane: float = 1.0    # Membrane resistance (MΩ)
    c_membrane: float = 20.0   # Membrane capacitance (pF)
    tau_refrac: float = 2.0    # Refractory period (ms)
    
    # LIF specific
    lif_noise_mean: float = 0.0   # Noise mean
    lif_noise_std: float = 0.0    # Noise standard deviation
    
    # AdEx specific
    adex_a: float = 4.0           # Subthreshold adaptation (nS)
    adex_b: float = 80.5          # Spike-triggered adaptation (pA)
    adex_delta_t: float = 2.0     # Slope factor (mV)
    adex_tau_w: float = 144.0     # Adaptation time constant (ms)
    adex_v_spike: float = 0.0     # Spike detection threshold (mV)  # ADD THIS LINE
    
    # Izhikevich specific
    izh_a: float = 0.02     # Recovery parameter
    izh_b: float = 0.2      # Adaptation parameter
    izh_c: float = -65.0    # Reset potential (mV)
    izh_d: float = 8.0      # Reset recovery variable
    
    # QIF specific
    qif_a: float = 0.25         # Quadratic coefficient
    qif_tau_reset: float = 0.5  # Reset time constant (ms)

    def get_model_params(self, model: NeuronModel) -> Dict[str, float]:
        """Get parameters specific to a neuron model."""
        model_params = {
            'v_rest': self.v_rest,
            'v_reset': self.v_reset,
            'v_threshold': self.v_threshold,
            'tau_m': self.tau_m,
            'r_membrane': self.r_membrane,
            'c_membrane': self.c_membrane,
            'tau_refrac': self.tau_refrac
        }
        
        if model == NeuronModel.LIF:
            model_params.update({
                'noise_mean': self.lif_noise_mean,
                'noise_std': self.lif_noise_std
            })
        elif model == NeuronModel.ADEX:
            model_params.update({
                'a': self.adex_a,
                'b': self.adex_b,
                'delta_t': self.adex_delta_t,
                'tau_w': self.adex_tau_w,
                'v_spike': self.adex_v_spike  # ADD THIS LINE
            })
        elif model == NeuronModel.IZHIKEVICH:
            model_params.update({
                'a': self.izh_a,
                'b': self.izh_b,
                'c': self.izh_c,
                'd': self.izh_d
            })
        elif model == NeuronModel.QIF:
            model_params.update({
                'a': self.qif_a,
                'tau_reset': self.qif_tau_reset
            })
        
        return model_params
    
    @classmethod
    def from_neuron_type(cls, neuron_type: NeuronType) -> 'NeuronParameters':
        """
        Create parameters based on neuron type.
        
        Args:
            neuron_type: Type of neuron
            
        Returns:
            NeuronParameters instance
        """
        if neuron_type == NeuronType.EXCITATORY:
            return cls(
                v_rest=-65.0,
                v_reset=-65.0,
                v_threshold=-50.0,
                tau_m=20.0,
                tau_refrac=2.0,
                izh_a=0.02,
                izh_b=0.2,
                izh_c=-65.0,
                izh_d=8.0
            )
        elif neuron_type == NeuronType.INHIBITORY:
            return cls(
                v_rest=-65.0,
                v_reset=-65.0,
                v_threshold=-50.0,
                tau_m=10.0,  # Faster time constant
                tau_refrac=1.0,  # Shorter refractory period
                izh_a=0.1,  # Fast-spiking
                izh_b=0.2,
                izh_c=-65.0,
                izh_d=2.0
            )
        elif neuron_type == NeuronType.ADAPTIVE:
            return cls(
                v_rest=-70.0,
                v_reset=-70.0,
                v_threshold=-50.0,
                tau_m=30.0,
                tau_refrac=2.0,
                adex_a=4.0,  # Strong adaptation
                adex_b=80.5,
                adex_tau_w=144.0,
                izh_a=0.02,
                izh_b=0.25,  # Stronger adaptation
                izh_c=-65.0,
                izh_d=6.0
            )
        elif neuron_type == NeuronType.RESONATOR:
            return cls(
                v_rest=-65.0,
                v_reset=-65.0,
                v_threshold=-50.0,
                tau_m=15.0,
                tau_refrac=2.0,
                izh_a=0.1,
                izh_b=0.26,  # Resonator configuration
                izh_c=-65.0,
                izh_d=0.0
            )
        elif neuron_type == NeuronType.MODULATORY:
            return cls(
                v_rest=-60.0,
                v_reset=-60.0,
                v_threshold=-50.0,
                tau_m=40.0,  # Slow dynamics
                tau_refrac=5.0,  # Longer refractory period
                izh_a=0.02,
                izh_b=0.2,
                izh_c=-55.0,
                izh_d=4.0
            )
        else:
            return cls()  # Default parameters


@dataclass
class SynapseParameters:
    """Parameters for synapse models."""
    # Basic parameters
    weight: float = 1.0        # Synaptic weight
    delay: float = 1.0         # Synaptic delay (ms)
    tau_syn: float = 5.0       # Synaptic time constant (ms)
    
    # Dynamic synapse parameters (short-term plasticity)
    u_se: float = 0.5          # Synaptic efficacy at rest [0-1]
    tau_d: float = 800.0       # Depression time constant (ms)
    tau_f: float = 20.0        # Facilitation time constant (ms)
    
    # Additional parameters
    connectivity_p: float = 1.0  # Connection probability [0-1]
    w_min: float = 0.0           # Minimum weight
    w_max: float = 10.0          # Maximum weight
    
    def get_synapse_params(self, synapse_type: SynapseType) -> Dict[str, float]:
        """
        Get parameters specific to a synapse type.
        
        Args:
            synapse_type: The synapse type
            
        Returns:
            Dictionary of type-specific parameters
        """
        base_params = {
            'weight': self.weight,
            'delay': self.delay,
            'tau_syn': self.tau_syn,
            'w_min': self.w_min,
            'w_max': self.w_max
        }
        
        # Adjust base weight for inhibitory synapses
        if synapse_type == SynapseType.INHIBITORY:
            base_params['weight'] = -abs(self.weight)
        
        # Add short-term plasticity parameters for dynamic synapses
        if synapse_type == SynapseType.DYNAMIC:
            base_params.update({
                'u_se': self.u_se,
                'tau_d': self.tau_d,
                'tau_f': self.tau_f
            })
            
        return base_params
    
    @classmethod
    def from_synapse_type(cls, synapse_type: SynapseType) -> 'SynapseParameters':
        """
        Create parameters based on synapse type.
        
        Args:
            synapse_type: Type of synapse
            
        Returns:
            SynapseParameters instance
        """
        if synapse_type == SynapseType.EXCITATORY:
            return cls(
                weight=1.0,
                delay=1.5,
                tau_syn=5.0,
                u_se=0.5,
                tau_d=800.0,
                tau_f=20.0,
                w_min=0.0,
                w_max=10.0
            )
        elif synapse_type == SynapseType.INHIBITORY:
            return cls(
                weight=-1.0,  # Negative weight for inhibition
                delay=1.0,    # Often faster than excitatory
                tau_syn=10.0, # Often slower decay
                u_se=0.25,    # Lower baseline efficacy
                tau_d=700.0,
                tau_f=20.0,
                w_min=-10.0,  # Negative weight range
                w_max=0.0
            )
        elif synapse_type == SynapseType.MODULATORY:
            return cls(
                weight=0.5,   # Often weaker direct effect
                delay=5.0,    # Slower action
                tau_syn=50.0, # Much slower decay
                u_se=0.1,     # Low efficacy 
                tau_d=1000.0, # Slow depression
                tau_f=1000.0, # Slow facilitation
                w_min=0.0,
                w_max=5.0
            )
        elif synapse_type == SynapseType.ELECTRICAL:
            return cls(
                weight=0.2,   # Gap junctions typically weaker
                delay=0.1,    # Almost instantaneous
                tau_syn=1.0,  # Very fast
                connectivity_p=0.05,  # Typically sparse
                w_min=0.0,
                w_max=1.0
            )
        elif synapse_type == SynapseType.DYNAMIC:
            return cls(
                weight=1.0,
                delay=1.5,
                tau_syn=5.0,
                u_se=0.5,     # Standard value
                tau_d=800.0,  # Depression time constant
                tau_f=20.0,   # Facilitation time constant
                w_min=0.0,
                w_max=10.0
            )
        else:
            return cls()  # Default parameters


class BaseNeuron:
    """
    Base class for all neuron models.

    Provides common interface and functionality for all neuron models.
    """
    def __init__(self, neuron_id: int, params: Optional[NeuronParameters] = None):
        # Your existing initialization code...
        self.id = neuron_id
        self.params = params if params else NeuronParameters()
        
        # State variables
        self.v = self.params.v_rest
        self.t_last_spike = float('-inf')
        self.is_refractory = False
        self.refractory_time_left = 0.0
        
        # *** ADD THESE ENHANCED CALCIUM DYNAMICS ***
        # Calcium dynamics
        self.calcium_concentration = 0.1  # μM, baseline calcium
        self.calcium_tau = 20.0  # ms, calcium removal time constant
        self.calcium_influx_per_spike = 0.5  # μM per spike
        self.calcium_baseline = 0.05  # μM, minimum calcium level
        
        # Calcium-dependent processes
        self.adaptation_current = 0.0  # pA, calcium-activated potassium current
        self.adaptation_tau = 100.0  # ms, adaptation time constant
        self.adaptation_strength = 5.0  # pA/μM, calcium sensitivity
        
        # Enhanced ionic currents tracking
        self.sodium_activation = 0.0    # m, sodium channel activation
        self.sodium_inactivation = 1.0  # h, sodium channel inactivation  
        self.potassium_activation = 0.0  # n, potassium channel activation
        
        # Calcium-activated potassium channels
        self.kca_activation = 0.0  # Calcium-activated K+ channel state
        self.kca_max_conductance = 2.0  # nS, maximum KCa conductance
        
        # Spike history for calcium tracking
        self.spike_times = []
        self.v_history = []
        
        # Your existing code...
        self.neuromod_sensitivity = {
            NeuromodulatorType.DOPAMINE: 1.0,
            NeuromodulatorType.SEROTONIN: 1.0,
            NeuromodulatorType.NOREPINEPHRINE: 1.0,
            NeuromodulatorType.ACETYLCHOLINE: 1.0,
        }
        
        self.is_inhibitory = False
        self.is_excitatory = True

    def update_calcium_dynamics(self, dt: float, spiked: bool = False):
        """Update calcium concentration and calcium-dependent processes."""
        # Calcium influx from spike
        if spiked:
            self.calcium_concentration += self.calcium_influx_per_spike
            
        # Calcium removal (exponential decay)
        self.calcium_concentration += dt * (-self.calcium_concentration + self.calcium_baseline) / self.calcium_tau
        
        # Ensure minimum calcium level
        self.calcium_concentration = max(self.calcium_baseline, self.calcium_concentration)
        
        # Update calcium-activated potassium channels
        # KCa activation follows: activation = Ca²⁺ / (Ca²⁺ + Kd)
        kd_calcium = 0.5  # μM, half-activation constant
        self.kca_activation = self.calcium_concentration / (self.calcium_concentration + kd_calcium)
        
        # Update adaptation current (calcium-activated potassium current)
        adaptation_target = self.adaptation_strength * self.kca_activation
        self.adaptation_current += dt * (adaptation_target - self.adaptation_current) / self.adaptation_tau

    def compute_ionic_currents(self, dt: float) -> float:
        """Compute enhanced ionic currents including calcium effects."""
        v = self.v
        
        # Hodgkin-Huxley gating variables (simplified)
        # Sodium activation (m)
        alpha_m = 0.1 * (v + 40.0) / (1.0 - np.exp(-(v + 40.0) / 10.0)) if abs(v + 40.0) > 1e-6 else 1.0
        beta_m = 4.0 * np.exp(-(v + 65.0) / 18.0)
        m_inf = alpha_m / (alpha_m + beta_m)
        tau_m = 1.0 / (alpha_m + beta_m)
        
        # Sodium inactivation (h)
        alpha_h = 0.07 * np.exp(-(v + 65.0) / 20.0)
        beta_h = 1.0 / (1.0 + np.exp(-(v + 35.0) / 10.0))
        h_inf = alpha_h / (alpha_h + beta_h)
        tau_h = 1.0 / (alpha_h + beta_h)
        
        # Potassium activation (n)
        alpha_n = 0.01 * (v + 55.0) / (1.0 - np.exp(-(v + 55.0) / 10.0)) if abs(v + 55.0) > 1e-6 else 0.1
        beta_n = 0.125 * np.exp(-(v + 65.0) / 80.0)
        n_inf = alpha_n / (alpha_n + beta_n)
        tau_n = 1.0 / (alpha_n + beta_n)
        
        # Update gating variables
        self.sodium_activation += dt * (m_inf - self.sodium_activation) / tau_m
        self.sodium_inactivation += dt * (h_inf - self.sodium_inactivation) / tau_h
        self.potassium_activation += dt * (n_inf - self.potassium_activation) / tau_n
        
        # Compute currents (simplified conductance-based model)
        g_na_max = 120.0  # mS/cm²
        g_k_max = 36.0    # mS/cm²
        g_leak = 0.3      # mS/cm²
        
        e_na = 50.0   # mV
        e_k = -77.0   # mV
        e_leak = -54.4  # mV
        
        # Ionic currents
        i_na = g_na_max * (self.sodium_activation ** 3) * self.sodium_inactivation * (v - e_na)
        i_k = g_k_max * (self.potassium_activation ** 4) * (v - e_k)
        i_leak = g_leak * (v - e_leak)
        
        # Calcium-activated potassium current
        i_kca = self.kca_max_conductance * self.kca_activation * (v - e_k)
        
        # Total current (sign convention: inward negative)
        total_current = -(i_na + i_k + i_leak + i_kca) - self.adaptation_current
        
        return total_current

    def get_enhanced_state(self) -> Dict[str, Any]:
        """Get enhanced neuron state including calcium dynamics."""
        base_state = self.get_state()  # Your existing get_state method
        
        enhanced_state = {
            **base_state,
            'calcium_concentration': self.calcium_concentration,
            'adaptation_current': self.adaptation_current,
            'kca_activation': self.kca_activation,
            'sodium_activation': self.sodium_activation,
            'sodium_inactivation': self.sodium_inactivation,
            'potassium_activation': self.potassium_activation
        }
        
        return enhanced_state

    def reset(self):
        """Reset neuron state to initial conditions."""
        self.v = self.params.v_rest
        self.t_last_spike = float('-inf')
        self.is_refractory = False
        self.refractory_time_left = 0.0
        self.spike_times = []
        self.v_history = []
    
    def step(self, dt: float, t_current: float, i_ext: float = 0.0) -> bool:
        """
        Update neuron state for one time step.
        
        Args:
            dt: Time step size (ms)
            t_current: Current simulation time (ms)
            i_ext: External input current (pA)
            
        Returns:
            True if the neuron fired, False otherwise
        """
        raise NotImplementedError("Subclasses must implement step()")
    
    def receive_spike(self, weight: float, t_current: float):
        """
        Receive a synaptic spike.
        
        Args:
            weight: Synaptic weight
            t_current: Current simulation time (ms)
        """
        raise NotImplementedError("Subclasses must implement receive_spike()")
    
    def get_state(self) -> Dict[str, Any]:
        """
    
            Dictionary with current state
        """
        return {
            'id': self.id,
            'model': 'Base',
            'v': self.v,
            't_last_spike': self.t_last_spike,
            'is_refractory': self.is_refractory,
            'refractory_time_left': self.refractory_time_left,
            'spike_count': len(self.spike_times),
            'is_inhibitory': self.is_inhibitory,
            'is_excitatory': self.is_excitatory
        }
             
    
    def apply_neuromodulation(self, modulators: Dict[NeuromodulatorType, float]):
        """
        Apply neuromodulatory effect to the neuron.
        
        Args:
            modulators: Dictionary mapping neuromodulator types to concentrations
        """
        pass  # Default implementation does nothing


class LIFNeuron(BaseNeuron):
    """
    Leaky Integrate-and-Fire neuron model.
    
    This implements the classic LIF model:
    τ_m * dV/dt = -(V - E_L) + R_m * I(t)
    If V ≥ V_threshold, then spike and V ← V_reset
    """
    
    def __init__(self, neuron_id: int, params: Optional[NeuronParameters] = None):
        super().__init__(neuron_id, params)
        self.model_params = self.params.get_model_params(NeuronModel.LIF)
        self.i_syn = 0.0
        
        # Enhanced synaptic conductances
        self.ampa_conductance = 0.0    # nS, AMPA receptor conductance
        self.nmda_conductance = 0.0    # nS, NMDA receptor conductance
        self.gaba_conductance = 0.0    # nS, GABA receptor conductance
        
        # Synaptic reversal potentials
        self.e_ampa = 0.0      # mV
        self.e_nmda = 0.0      # mV  
        self.e_gaba = -70.0    # mV
    
    def step(self, dt: float, t_current: float, i_ext: float = 0.0) -> bool:
        """Enhanced LIF neuron step with calcium dynamics."""
        # Store membrane potential history
        self.v_history.append(self.v)
        
        # Check refractory period
        if self.is_refractory:
            self.refractory_time_left -= dt
            if self.refractory_time_left <= 0:
                self.is_refractory = False
                self.refractory_time_left = 0.0
            return False
        
        # Compute synaptic currents
        i_synaptic = self.compute_synaptic_currents()
        
        # Compute ionic currents (if using enhanced model)
        if hasattr(self, 'use_enhanced_dynamics') and self.use_enhanced_dynamics:
            i_ionic = self.compute_ionic_currents(dt)
        else:
            i_ionic = 0.0
        
        # Add noise
        i_noise = np.random.normal(
            self.model_params['noise_mean'], 
            self.model_params['noise_std']
        ) if self.model_params['noise_std'] > 0 else 0.0
        
        # Total input current
        i_total = i_synaptic + i_ext + i_noise + i_ionic
        
        # LIF dynamics with calcium-dependent adaptation
        tau_m = self.model_params['tau_m']
        r_m = self.model_params['r_membrane']
        e_l = self.model_params['v_rest']
        
        # Include adaptation current in membrane equation
        dv = (-(self.v - e_l) + r_m * i_total - r_m * self.adaptation_current) * (dt / tau_m)
        self.v += dv
        
        # Update calcium dynamics
        spiked = False
        if self.v >= self.model_params['v_threshold']:
            self.t_last_spike = t_current
            self.spike_times.append(t_current)
            
            # Reset voltage and enter refractory period
            self.v = self.model_params['v_reset']
            self.is_refractory = True
            self.refractory_time_left = self.model_params['tau_refrac']
            
            spiked = True
        
        # Update calcium dynamics (this affects future dynamics)
        self.update_calcium_dynamics(dt, spiked)
        
        # Reset synaptic current
        self.i_syn = 0.0
        
        return spiked
    
    def compute_synaptic_currents(self) -> float:
        """Compute detailed synaptic currents."""
        v = self.v
        
        # AMPA current (fast excitatory)
        i_ampa = self.ampa_conductance * (v - self.e_ampa)
        
        # NMDA current (slow excitatory, voltage-dependent Mg2+ block)
        mg_concentration = 1.0  # mM
        mg_block = 1.0 / (1.0 + (mg_concentration / 3.57) * np.exp(-0.062 * v))
        i_nmda = self.nmda_conductance * mg_block * (v - self.e_nmda)
        
        # GABA current (inhibitory)
        i_gaba = self.gaba_conductance * (v - self.e_gaba)
        
        # Decay conductances
        self.ampa_conductance *= np.exp(-0.1 / 2.0)   # τ = 2 ms
        self.nmda_conductance *= np.exp(-0.1 / 50.0)  # τ = 50 ms
        self.gaba_conductance *= np.exp(-0.1 / 10.0)  # τ = 10 ms
        
        return -(i_ampa + i_nmda + i_gaba)
    
    def receive_spike(self, weight: float, t_current: float, neurotransmitter: str = 'glutamate'):
        """Enhanced spike reception with neurotransmitter-specific responses."""
        if neurotransmitter.lower() == 'glutamate':
            # 80% AMPA, 20% NMDA for glutamate
            self.ampa_conductance += abs(weight) * 0.8
            self.nmda_conductance += abs(weight) * 0.2
        elif neurotransmitter.lower() == 'gaba':
            self.gaba_conductance += abs(weight)
        else:
            # Default to simple current injection (your original method)
            self.i_syn += weight
    
    
    
    def get_state(self) -> Dict[str, Any]:
        """
        Get neuron state.
        
        Returns:
            Dictionary with current state
        """
        return {
            'id': self.id,
            'model': 'LIF',
            'v': self.v,
            'i_syn': self.i_syn,
            't_last_spike': self.t_last_spike,
            'is_refractory': self.is_refractory,
            'spike_count': len(self.spike_times)
        }
    
    def apply_neuromodulation(self, modulators: Dict[NeuromodulatorType, float]):
        """
        Apply neuromodulatory effect to the neuron.
        
        Args:
            modulators: Dictionary mapping neuromodulator types to concentrations
        """
        # Example: Dopamine affects excitability (threshold)
        if NeuromodulatorType.DOPAMINE in modulators:
            dopamine = modulators[NeuromodulatorType.DOPAMINE]
            sensitivity = self.neuromod_sensitivity[NeuromodulatorType.DOPAMINE]
            # Lower threshold with increased dopamine
            self.model_params['v_threshold'] -= (dopamine * sensitivity * 2.0)
        
        # Example: Acetylcholine affects membrane time constant
        if NeuromodulatorType.ACETYLCHOLINE in modulators:
            ach = modulators[NeuromodulatorType.ACETYLCHOLINE]
            sensitivity = self.neuromod_sensitivity[NeuromodulatorType.ACETYLCHOLINE]
            # Decrease time constant with increased ACh (makes neuron more responsive)
            self.model_params['tau_m'] *= 1.0 / (1.0 + ach * sensitivity * 0.5)


class AdExNeuron(BaseNeuron):
    """
    Adaptive Exponential Integrate-and-Fire neuron model.
    
    This implements the AdEx model:
    C * dV/dt = -g_L(V - E_L) + g_L*Δ_T*exp((V - V_T)/Δ_T) - w + I
    τ_w * dw/dt = a(V - E_L) - w
    If V ≥ V_spike, then spike and V ← V_reset, w ← w + b
    """
    
    def __init__(self, neuron_id: int, params: Optional[NeuronParameters] = None):
        super().__init__(neuron_id, params)
        self.model_params = self.params.get_model_params(NeuronModel.ADEX)
        
        # State variables - ENSURE THESE ARE INITIALIZED
        self.w = 0.0  # Adaptation variable (pA)
        self.i_syn = 0.0  # Synaptic current
        
        # Additional history for debugging
        self.w_history = []
        
        print(f"AdEx neuron {neuron_id} initialized with params: {self.model_params}")
    
    def reset(self):
        """Reset neuron state."""
        super().reset()
        self.w = 0.0
        self.i_syn = 0.0
        self.w_history = []
    
    def step(self, dt: float, t_current: float, i_ext: float = 0.0) -> bool:
        """Enhanced AdEx neuron step with detailed adaptation."""
        # Store histories
        self.v_history.append(self.v)
        self.w_history.append(self.w)
        
        # Check refractory period
        if self.is_refractory:
            self.refractory_time_left -= dt
            if self.refractory_time_left <= 0:
                self.is_refractory = False
                self.refractory_time_left = 0.0
            return False
        
        # Get parameters
        c_m = self.model_params['c_membrane']
        g_l = c_m / self.model_params['tau_m']  # Leak conductance (nS)
        e_l = self.model_params['v_rest']
        delta_t = self.model_params['delta_t']
        v_t = self.model_params['v_threshold']
        tau_w = self.model_params['tau_w']
        a = self.model_params['a']
        b = self.model_params['b']
        v_spike = self.model_params.get('v_spike', 0.0)
        v_reset = self.model_params['v_reset']
        
        # Total input current
        i_total = self.i_syn + i_ext
        
        # Update adaptation variable w FIRST
        # τ_w * dw/dt = a(V - E_L) - w
        dw_dt = (a * (self.v - e_l) - self.w) / tau_w
        self.w += dw_dt * dt
        
        # Update membrane potential
        # C * dV/dt = -g_L(V - E_L) + g_L*Δ_T*exp((V - V_T)/Δ_T) - w + I
        v_diff = self.v - v_t
        
        # Prevent exponential overflow
        if v_diff / delta_t > 20:
            exp_term = g_l * delta_t * np.exp(20)
        else:
            exp_term = g_l * delta_t * np.exp(v_diff / delta_t)
        
        # Membrane equation
        dv_dt = (-g_l * (self.v - e_l) + exp_term - self.w + i_total) / c_m
        self.v += dv_dt * dt
        
        # Check for spike
        if self.v >= v_spike:
            self.t_last_spike = t_current
            self.spike_times.append(t_current)
            
            # Reset voltage and add spike-triggered adaptation
            self.v = v_reset
            self.w += b  # Spike-triggered adaptation increment
            
            # Enter refractory period
            self.is_refractory = True
            self.refractory_time_left = self.model_params['tau_refrac']
            
            # Reset synaptic current
            self.i_syn = 0.0
            
            return True
        
        # Reset synaptic current
        self.i_syn = 0.0
        return False
    
    def get_state(self) -> Dict[str, Any]:
        """Get enhanced neuron state."""
        return {
            'id': self.id,
            'model': 'AdEx',
            'v': self.v,
            'w': self.w,
            'i_syn': self.i_syn,
            't_last_spike': self.t_last_spike,
            'is_refractory': self.is_refractory,
            'spike_count': len(self.spike_times),
            'adaptation_level': self.w,
            'w_history_length': len(self.w_history)
        }
    
    def receive_spike(self, weight: float, t_current: float):
        """
        Receive a synaptic spike.
        
        Args:
            weight: Synaptic weight
            t_current: Current simulation time (ms)
        """
        # In a simple model, just add to synaptic current
        self.i_syn += weight
    
    
    
    def apply_neuromodulation(self, modulators: Dict[NeuromodulatorType, float]):
        """
        Apply neuromodulatory effect to the neuron.
        
        Args:
            modulators: Dictionary mapping neuromodulator types to concentrations
        """
        # Example: Serotonin affects adaptation
        if NeuromodulatorType.SEROTONIN in modulators:
            serotonin = modulators[NeuromodulatorType.SEROTONIN]
            sensitivity = self.neuromod_sensitivity[NeuromodulatorType.SEROTONIN]
            # Increase adaptation with serotonin
            self.model_params['a'] *= (1.0 + serotonin * sensitivity * 0.5)
            self.model_params['b'] *= (1.0 + serotonin * sensitivity * 0.5)
        
        # Example: Norepinephrine affects exponential term
        if NeuromodulatorType.NOREPINEPHRINE in modulators:
            ne = modulators[NeuromodulatorType.NOREPINEPHRINE]
            sensitivity = self.neuromod_sensitivity[NeuromodulatorType.NOREPINEPHRINE]
            # Increase exponential term with NE (makes neuron more excitable)
            self.model_params['delta_t'] *= (1.0 + ne * sensitivity * 0.3)


class IzhikevichNeuron(BaseNeuron):
    """
    Izhikevich neuron model.
    
    This implements the Izhikevich model:
    dv/dt = 0.04v² + 5v + 140 - u + I
    du/dt = a(bv - u)
    if v ≥ 30 mV, then v ← c, u ← u + d
    """
    
    def __init__(self, neuron_id: int, params: Optional[NeuronParameters] = None):
        """
        Initialize Izhikevich neuron.
        
        Args:
            neuron_id: Unique neuron identifier
            params: Neuron parameters (optional, will use defaults if None)
        """
        super().__init__(neuron_id, params)
        self.model_params = self.params.get_model_params(NeuronModel.IZHIKEVICH)
        
        # State variables
        self.v = self.model_params['c']  # Initial v is reset potential
        self.u = self.model_params['b'] * self.v  # Initial u
        self.i_syn = 0.0
        
        # Additional history
        self.u_history = []  # History of recovery variable
    
    def reset(self):
        """Reset neuron state."""
        super().reset()
        self.v = self.model_params['c']
        self.u = self.model_params['b'] * self.v
        self.i_syn = 0.0
        self.u_history = []
    
    def step(self, dt: float, t_current: float, i_ext: float = 0.0) -> bool:
        """
        Update neuron state for one time step.
        
        Args:
            dt: Time step size (ms)
            t_current: Current simulation time (ms)
            i_ext: External input current (pA)
            
        Returns:
            True if the neuron fired, False otherwise
        """
        # Store histories
        self.v_history.append(self.v)
        self.u_history.append(self.u)
        
        # Extract parameters
        a = self.model_params['a']
        b = self.model_params['b']
        c = self.model_params['c']
        d = self.model_params['d']
        
        # Scale input current (Izhikevich's model typically uses different current scales)
        i_scaled = (self.i_syn + i_ext) * 0.1
        
        # Update membrane potential
        # dv/dt = 0.04v² + 5v + 140 - u + I
        dv = (0.04 * self.v**2 + 5 * self.v + 140 - self.u + i_scaled) * dt
        self.v += dv
        
        # Update recovery variable
        # du/dt = a(bv - u)
        du = a * (b * self.v - self.u) * dt
        self.u += du
        
        # Check for spike
        if self.v >= 30.0:  # Izhikevich threshold is at 30 mV
            self.t_last_spike = t_current
            self.spike_times.append(t_current)
            
            # Reset
            self.v = c
            self.u += d
            
            # Reset synaptic current
            self.i_syn = 0.0
            
            return True
        
        # Reset synaptic current
        self.i_syn = 0.0
        
        return False
    
    def receive_spike(self, weight: float, t_current: float):
        """
        Receive a synaptic spike.
        
        Args:
            weight: Synaptic weight
            t_current: Current simulation time (ms)
        """
        # In a simple model, just add to synaptic current
        self.i_syn += weight
    
    def get_state(self) -> Dict[str, Any]:
        """
        Get neuron state.
        
        Returns:
            Dictionary with current state
        """
        return {
            'id': self.id,
            'model': 'Izhikevich',
            'v': self.v,
            'u': self.u,
            'i_syn': self.i_syn,
            't_last_spike': self.t_last_spike,
            'spike_count': len(self.spike_times)
        }
    
    def apply_neuromodulation(self, modulators: Dict[NeuromodulatorType, float]):
        """
        Apply neuromodulatory effect to the neuron.
        
        Args:
            modulators: Dictionary mapping neuromodulator types to concentrations
        """
        # Example: Dopamine affects recovery
        if NeuromodulatorType.DOPAMINE in modulators:
            dopamine = modulators[NeuromodulatorType.DOPAMINE]
            sensitivity = self.neuromod_sensitivity[NeuromodulatorType.DOPAMINE]
            # Make reset more excitable with dopamine
            self.model_params['d'] /= (1.0 + dopamine * sensitivity * 0.5)
        
        # Example: Acetylcholine affects recovery time
        if NeuromodulatorType.ACETYLCHOLINE in modulators:
            ach = modulators[NeuromodulatorType.ACETYLCHOLINE]
            sensitivity = self.neuromod_sensitivity[NeuromodulatorType.ACETYLCHOLINE]
            # Speed up recovery with ACh
            self.model_params['a'] *= (1.0 + ach * sensitivity * 0.5)


class BiologicalTimingCircuit:
    """
    Implements oscillatory neural dynamics for timing and coordination.
    
    This class models brain rhythms like theta, alpha, beta, and gamma
    oscillations to coordinate processing across distributed modules.
    """
    
    def __init__(self, oscillation_types: List[OscillationType] = None):
        """
        Initialize biological timing circuit.
        
        Args:
            oscillation_types: List of oscillation types to include
        """
        if oscillation_types is None:
            oscillation_types = [
                OscillationType.DELTA,
                OscillationType.THETA,
                OscillationType.ALPHA,
                OscillationType.BETA,
                OscillationType.GAMMA
            ]
        
        self.oscillation_types = oscillation_types
        
        # Initialize oscillator parameters
        self.frequencies = {
            OscillationType.DELTA: 2.0,   # 2 Hz
            OscillationType.THETA: 6.0,   # 6 Hz
            OscillationType.ALPHA: 10.0,  # 10 Hz
            OscillationType.BETA: 20.0,   # 20 Hz
            OscillationType.GAMMA: 40.0   # 40 Hz
        }
        
        self.amplitudes = {osc: 1.0 for osc in self.oscillation_types}
        self.phases = {osc: 0.0 for osc in self.oscillation_types}
        
        # Add custom oscillation if specified
        if OscillationType.CUSTOM in self.oscillation_types:
            self.frequencies[OscillationType.CUSTOM] = 15.0  # Custom frequency
            self.amplitudes[OscillationType.CUSTOM] = 1.0
            self.phases[OscillationType.CUSTOM] = 0.0
            
        # State
        self.time = 0.0
        
        # Phase coupling parameters (coupling strength between oscillations)
        self.coupling = np.zeros((len(oscillation_types), len(oscillation_types)))
        
        # Set default coupling: Theta modulates gamma
        if OscillationType.THETA in self.oscillation_types and OscillationType.GAMMA in self.oscillation_types:
            theta_idx = self.oscillation_types.index(OscillationType.THETA)
            gamma_idx = self.oscillation_types.index(OscillationType.GAMMA)
            self.coupling[theta_idx, gamma_idx] = 0.3  # Theta -> Gamma coupling
    
    def update(self, dt: float) -> Dict[OscillationType, float]:
        """
        Update oscillations for one time step.
        
        Args:
            dt: Time step size (ms)
            
        Returns:
            Dictionary mapping oscillation types to current values
        """
        self.time += dt
        
        # Calculate raw oscillation values
        raw_values = {}
        for osc in self.oscillation_types:
            freq = self.frequencies[osc]
            amp = self.amplitudes[osc]
            phase = self.phases[osc]
            
            # Calculate oscillation value
            value = amp * np.sin(2 * np.pi * freq * self.time / 1000.0 + phase)
            raw_values[osc] = value
        
        # Apply phase-amplitude coupling
        final_values = raw_values.copy()
        for i, osc_i in enumerate(self.oscillation_types):
            for j, osc_j in enumerate(self.oscillation_types):
                if self.coupling[i, j] > 0:
                    # Modulate amplitude of oscillation j by oscillation i
                    modulation = 1.0 + self.coupling[i, j] * raw_values[osc_i]
                    final_values[osc_j] *= modulation
        
        return final_values
    
    def set_frequency(self, oscillation_type: OscillationType, frequency: float):
        """
        Set the frequency of an oscillation.
        
        Args:
            oscillation_type: Type of oscillation
            frequency: New frequency (Hz)
        """
        if oscillation_type in self.frequencies:
            self.frequencies[oscillation_type] = frequency
    
    def set_amplitude(self, oscillation_type: OscillationType, amplitude: float):
        """
        Set the amplitude of an oscillation.
        
        Args:
            oscillation_type: Type of oscillation
            amplitude: New amplitude
        """
        if oscillation_type in self.amplitudes:
            self.amplitudes[oscillation_type] = amplitude
    
    def set_coupling(self, from_osc: OscillationType, to_osc: OscillationType, strength: float):
        """
        Set coupling strength between oscillations.
        
        Args:
            from_osc: Source oscillation
            to_osc: Target oscillation
            strength: Coupling strength
        """
        if from_osc in self.oscillation_types and to_osc in self.oscillation_types:
            from_idx = self.oscillation_types.index(from_osc)
            to_idx = self.oscillation_types.index(to_osc)
            self.coupling[from_idx, to_idx] = strength
    
    def get_modulation(self, neuron_id: int) -> float:
        """
        Get modulation value for a specific neuron.
        
        Args:
            neuron_id: Neuron identifier
            
        Returns:
            Modulation value
        """
        # In a more complex implementation, different neurons would be sensitive
        # to different oscillations. Here, we use a simple hashing approach.
        osc_index = neuron_id % len(self.oscillation_types)
        osc_type = self.oscillation_types[osc_index]
        
        # Calculate oscillation value
        freq = self.frequencies[osc_type]
        amp = self.amplitudes[osc_type]
        phase = self.phases[osc_type]
        
        return amp * np.sin(2 * np.pi * freq * self.time / 1000.0 + phase)


class NeuromodulationSystem:
    """
    System for global neuromodulatory effects in the network.
    
    This class models neuromodulator dynamics (dopamine, serotonin, etc.)
    that globally regulate network activity, learning, and information processing.
    """
    
    def __init__(self):
        """Initialize neuromodulation system."""
        # Initialize neuromodulator concentrations
        self.concentrations = {
            NeuromodulatorType.DOPAMINE: 0.0,
            NeuromodulatorType.SEROTONIN: 0.0,
            NeuromodulatorType.NOREPINEPHRINE: 0.0,
            NeuromodulatorType.ACETYLCHOLINE: 0.0,
        }
        
        # Time constants for neuromodulator dynamics (ms)
        self.time_constants = {
            NeuromodulatorType.DOPAMINE: 1000.0,        # Slower decay
            NeuromodulatorType.SEROTONIN: 5000.0,       # Very slow decay
            NeuromodulatorType.NOREPINEPHRINE: 500.0,   # Moderate decay
            NeuromodulatorType.ACETYLCHOLINE: 200.0,    # Faster decay
        }
        
        # Inputs to neuromodulator systems
        self.inputs = {nm: 0.0 for nm in NeuromodulatorType if nm != NeuromodulatorType.CUSTOM}
        
        # History of concentrations
        self.history = {nm: [] for nm in NeuromodulatorType if nm != NeuromodulatorType.CUSTOM}
    
    def update(self, dt: float) -> Dict[NeuromodulatorType, float]:
        """
        Update neuromodulator concentrations for one time step.
        
        Args:
            dt: Time step size (ms)
            
        Returns:
            Dictionary mapping neuromodulator types to current concentrations
        """
        # Update each neuromodulator
        for nm in self.concentrations:
            tau = self.time_constants[nm]
            input_val = self.inputs[nm]
            
            # First-order dynamics
            # τ * dc/dt = -c + I
            dc = (-self.concentrations[nm] + input_val) * (dt / tau)
            self.concentrations[nm] += dc
            
            # Store in history
            self.history[nm].append(self.concentrations[nm])
        
        return self.concentrations
    
    def set_input(self, neuromodulator: NeuromodulatorType, value: float):
        """
        Set input to a neuromodulator system.
        
        Args:
            neuromodulator: Type of neuromodulator
            value: Input value
        """
        if neuromodulator in self.inputs:
            self.inputs[neuromodulator] = value
    
    def set_concentration(self, neuromodulator: NeuromodulatorType, value: float):
        """
        Directly set the concentration of a neuromodulator.
        
        Args:
            neuromodulator: Type of neuromodulator
            value: Concentration value
        """
        if neuromodulator in self.concentrations:
            self.concentrations[neuromodulator] = value
    
    def get_concentration(self, neuromodulator: NeuromodulatorType) -> float:
        """
        Get current concentration of a neuromodulator.
        
        Args:
            neuromodulator: Type of neuromodulator
            
        Returns:
            Current concentration
        """
        return self.concentrations.get(neuromodulator, 0.0)
    
    def pulse(self, neuromodulator: NeuromodulatorType, magnitude: float = 1.0):
        """
        Send a pulse of neuromodulator.
        
        Args:
            neuromodulator: Type of neuromodulator
            magnitude: Pulse magnitude
        """
        if neuromodulator in self.concentrations:
            self.concentrations[neuromodulator] += magnitude


class SpikeMonitor:
    """
    Record and analyze spike activity in the network.
    
    Attributes:
        neurons: List of neurons to monitor
        spike_record: Dictionary of neuron ID to list of spike times
        v_record: Dictionary of neuron ID to list of (time, v) tuples
    """
    
    def __init__(self, neurons: List[Any], record_v: bool = False, sampling_interval: float = 1.0):
        """
        Initialize spike monitor.
        
        Args:
            neurons: List of neurons to monitor
            record_v: Whether to record membrane potentials
            sampling_interval: Interval for recording membrane potentials (ms)
        """
        self.neurons = {neuron.id: neuron for neuron in neurons}
        self.spike_record = {neuron_id: [] for neuron_id in self.neurons}
        self.record_v = record_v
        self.sampling_interval = sampling_interval
        self.v_record = {neuron_id: [] for neuron_id in self.neurons} if record_v else None
        self.last_sample_time = -np.inf
    
    def record(self, t_current: float):
        """
        Record neuron activity.
        
        Args:
            t_current: Current simulation time (ms)
        """
        # Record spikes
        for neuron_id, neuron in self.neurons.items():
            spikes = neuron.spike_times
            if spikes and spikes[-1] > t_current - self.sampling_interval:
                self.spike_record[neuron_id].append(spikes[-1])
        
        # Record membrane potentials at intervals
        if self.record_v and t_current >= self.last_sample_time + self.sampling_interval:
            for neuron_id, neuron in self.neurons.items():
                self.v_record[neuron_id].append((t_current, neuron.v))
            self.last_sample_time = t_current
    
    def get_results(self) -> Dict[str, Any]:
        """
        Get recorded results.
        
        Returns:
            Dictionary with recorded data
        """
        results = {
            'spike_record': self.spike_record,
            'neuron_count': len(self.neurons)
        }
        
        if self.record_v:
            results['v_record'] = self.v_record
            
        return results
    
    def plot_spike_raster(self, 
                          title: str = "Spike Raster", 
                          figsize: Tuple[int, int] = (10, 6),
                          time_range: Optional[Tuple[float, float]] = None) -> plt.Figure:
        """
        Plot spike raster.
        
        Args:
            title: Plot title
            figsize: Figure size (width, height)
            time_range: Optional (min_time, max_time) to plot
            
        Returns:
            Matplotlib figure
        """
        fig, ax = plt.subplots(figsize=figsize)
        
        # Collect spike data
        neuron_ids = sorted(self.spike_record.keys())
        spike_data = []
        
        for i, neuron_id in enumerate(neuron_ids):
            spikes = self.spike_record[neuron_id]
            if time_range:
                min_time, max_time = time_range
                spikes = [t for t in spikes if min_time <= t <= max_time]
                
            if spikes:
                spike_data.extend([(t, i) for t in spikes])
        
        if spike_data:
            spike_times, spike_neurons = zip(*spike_data)
            ax.scatter(spike_times, spike_neurons, marker='|', s=100, color='black')
        
        ax.set_xlabel('Time (ms)')
        ax.set_ylabel('Neuron ID')
        ax.set_yticks(range(len(neuron_ids)))
        ax.set_yticklabels([str(nid) for nid in neuron_ids])
        ax.set_title(title)
        
        if time_range:
            ax.set_xlim(*time_range)
        
        plt.tight_layout()
        return fig
    
    def plot_membrane_potentials(self, 
                                neuron_ids: Optional[List[int]] = None,
                                title: str = "Membrane Potentials",
                                figsize: Tuple[int, int] = (10, 6),
                                time_range: Optional[Tuple[float, float]] = None) -> plt.Figure:
        """
        Plot membrane potentials.
        
        Args:
            neuron_ids: IDs of neurons to plot (None = all)
            title: Plot title
            figsize: Figure size (width, height)
            time_range: Optional (min_time, max_time) to plot
            
        Returns:
            Matplotlib figure
        """
        if not self.record_v:
            raise ValueError("Membrane potentials were not recorded.")
            
        fig, ax = plt.subplots(figsize=figsize)
        
        # Determine neurons to plot
        if neuron_ids is None:
            neuron_ids = sorted(self.v_record.keys())
        else:
            neuron_ids = [nid for nid in neuron_ids if nid in self.v_record]
            
        # Plot membrane potentials
        for neuron_id in neuron_ids:
            if not self.v_record[neuron_id]:
                continue
                
            times, potentials = zip(*self.v_record[neuron_id])
            
            if time_range:
                min_time, max_time = time_range
                data = [(t, v) for t, v in zip(times, potentials) if min_time <= t <= max_time]
                if data:
                    times, potentials = zip(*data)
                else:
                    continue
                    
            ax.plot(times, potentials, label=f"Neuron {neuron_id}")
        
        ax.set_xlabel('Time (ms)')
        ax.set_ylabel('Membrane Potential (mV)')
        ax.set_title(title)
        ax.legend()
        
        if time_range:
            ax.set_xlim(*time_range)
        
        plt.tight_layout()
        return fig


class SpikeGenerator:
    """
    Generate spike patterns for input to neural networks.
    
    Attributes:
        neuron_ids: List of neuron IDs to receive spikes
    """
    
    def __init__(self, neuron_ids: List[int]):
        """
        Initialize spike generator.
        
        Args:
            neuron_ids: List of neuron IDs to receive spikes
        """
        self.neuron_ids = neuron_ids
    
    def poisson_spikes(self, 
                      rate: float, 
                      duration: float, 
                      t_start: float = 0.0,
                      dt: float = 0.1) -> Dict[int, List[float]]:
        """
        Generate Poisson-distributed spikes.
        
        Args:
            rate: Firing rate (Hz)
            duration: Duration (ms)
            t_start: Start time (ms)
            dt: Time step (ms)
            
        Returns:
            Dictionary mapping neuron ID to list of spike times
        """
        # Convert rate to probability per time step
        p_spike = rate * dt / 1000.0  # Convert to probability per ms
        
        # Initialize spike times
        spikes = {neuron_id: [] for neuron_id in self.neuron_ids}
        
        # Generate spikes
        t_steps = np.arange(t_start, t_start + duration, dt)
        
        for neuron_id in self.neuron_ids:
            for t in t_steps:
                if np.random.random() < p_spike:
                    spikes[neuron_id].append(t)
                    
        return spikes
    
    def regular_spikes(self, 
                      interval: float, 
                      duration: float, 
                      t_start: float = 0.0,
                      jitter: float = 0.0) -> Dict[int, List[float]]:
        """
        Generate regularly-spaced spikes.
        
        Args:
            interval: Inter-spike interval (ms)
            duration: Duration (ms)
            t_start: Start time (ms)
            jitter: Random jitter to add (ms)
            
        Returns:
            Dictionary mapping neuron ID to list of spike times
        """
        # Initialize spike times
        spikes = {neuron_id: [] for neuron_id in self.neuron_ids}
        
        # Generate spikes
        for neuron_id in self.neuron_ids:
            t = t_start
            while t < t_start + duration:
                if jitter > 0:
                    t += np.random.normal(0, jitter)
                    t = max(t_start, t)  # Ensure spike time isn't before start
                    
                spikes[neuron_id].append(t)
                t += interval
                
        return spikes
    
    def burst_spikes(self, 
                    burst_interval: float, 
                    intra_burst_interval: float,
                    spikes_per_burst: int,
                    duration: float, 
                    t_start: float = 0.0,
                    jitter: float = 0.0) -> Dict[int, List[float]]:
        """
        Generate bursts of spikes.
        
        Args:
            burst_interval: Interval between bursts (ms)
            intra_burst_interval: Interval between spikes in a burst (ms)
            spikes_per_burst: Number of spikes per burst
            duration: Duration (ms)
            t_start: Start time (ms)
            jitter: Random jitter to add (ms)
            
        Returns:
            Dictionary mapping neuron ID to list of spike times
        """
        # Initialize spike times
        spikes = {neuron_id: [] for neuron_id in self.neuron_ids}
        
        # Generate bursts
        for neuron_id in self.neuron_ids:
            burst_start = t_start
            while burst_start < t_start + duration:
                # Generate spikes in burst
                for i in range(spikes_per_burst):
                    spike_time = burst_start + i * intra_burst_interval
                    
                    if jitter > 0:
                        spike_time += np.random.normal(0, jitter)
                        
                    if spike_time <= t_start + duration:
                        spikes[neuron_id].append(spike_time)
                
                # Next burst
                burst_start += burst_interval
                
        return spikes
    
    def custom_pattern(self, 
                      pattern: List[float], 
                      repeats: int = 1,
                      t_start: float = 0.0,
                      jitter: float = 0.0) -> Dict[int, List[float]]:
        """
        Generate a custom spike pattern.
        
        Args:
            pattern: List of spike times (ms) relative to start
            repeats: Number of times to repeat the pattern
            t_start: Start time (ms)
            jitter: Random jitter to add (ms)
            
        Returns:
            Dictionary mapping neuron ID to list of spike times
        """
        # Initialize spike times
        spikes = {neuron_id: [] for neuron_id in self.neuron_ids}
        
        # Find pattern duration
        pattern_duration = max(pattern) if pattern else 0
        
        # Generate spikes
        for neuron_id in self.neuron_ids:
            for r in range(repeats):
                offset = t_start + r * pattern_duration
                
                for t in pattern:
                    spike_time = offset + t
                    
                    if jitter > 0:
                        spike_time += np.random.normal(0, jitter)
                        
                    spikes[neuron_id].append(spike_time)
                    
        return spikes
    
    def apply_spikes_to_neurons(self, spike_times: Dict[int, List[float]], neurons: Dict[int, Any]):
        """
        Apply generated spikes to neurons.
        
        Args:
            spike_times: Dictionary mapping neuron ID to list of spike times
            neurons: Dictionary mapping neuron ID to neuron object
            
        Returns:
            Dictionary with information about applied spikes
        """
        results = {
            'total_spikes': 0,
            'neurons_stimulated': 0,
            'spikes_per_neuron': {}
        }
        
        for neuron_id, times in spike_times.items():
            if neuron_id in neurons:
                for t in times:
                    # Assume neurons have a receive_spike method that accepts a spike time
                    # This would usually be called during simulation, not all at once
                    neurons[neuron_id].spike_times.append(t)
                    
                results['total_spikes'] += len(times)
                results['spikes_per_neuron'][neuron_id] = len(times)
                if len(times) > 0:
                    results['neurons_stimulated'] += 1
                    
        return results


class NeuromorphicCore:
    """
    Core neuromorphic computing system for ULTRA.
    
    This class implements the foundational neural network with biologically-inspired
    dynamics, 3D connectivity, heterogeneous neuron types, and realistic signal propagation.
    
    Attributes:
        dimensions: 3D dimensions for neuron placement (x, y, z sizes)
        neuron_count: Total number of neurons
        neurons: Dictionary of all neurons
        connections: Dictionary of connection information
        current_time: Current simulation time
    """
    

    def __init__(self, 
                dimensions: Tuple[int, int, int] = (10, 10, 10), 
                neuron_distribution: Dict[NeuronType, float] = None,
                config: Optional[Dict[str, Any]] = None):
        """
        Initialize neuromorphic core.
        
        Args:
            dimensions: 3D dimensions for neuron placement (x, y, z sizes)
            neuron_distribution: Distribution of neuron types
            config: Configuration dictionary
        """
        self.dimensions = dimensions
        self.config = config if config is not None else {}
        
        # Default neuron distribution if not provided
        if neuron_distribution is None:
            neuron_distribution = {
                NeuronType.EXCITATORY: 0.8,
                NeuronType.INHIBITORY: 0.1,
                NeuronType.ADAPTIVE: 0.05,
                NeuronType.MODULATORY: 0.05
            }
        self.neuron_distribution = neuron_distribution



        # Validate neuron distribution
        total = sum(neuron_distribution.values())
        if abs(total - 1.0) > 1e-6:
            logger.warning(f"Neuron distribution sum is {total}, normalizing to 1.0.")
            self.neuron_distribution = {k: v / total for k, v in neuron_distribution.items()}
        
        # Set default configuration values
        self._set_default_config()
        
        # Initialize neurons
        self.neuron_count = 0
        self.neurons = {}  # id -> neuron
        self.neuron_types = {}  # id -> neuron type
        self.neuron_positions = {}  # id -> (x, y, z)
        self.neuron_models = {}  # id -> model
        
        # Initialize connections
        self.connections = {}  # (pre_id, post_id) -> connection info
        self.connection_delays = {}  # (pre_id, post_id) -> delay
        self.connection_weights = {}  # (pre_id, post_id) -> weight
        self.connection_ages = {}  # (pre_id, post_id) -> age
        self.connection_activity = {}  # (pre_id, post_id) -> activity level
        
        # Pending spikes (spikes in transit due to delays)
        # (target_neuron_id, delivery_time) -> [(source_neuron_id, weight), ...]
        self.pending_spikes = defaultdict(list)
        
        # Simulation state
        self.current_time = 0.0
        self.external_currents = {}  # neuron_id -> current
        
        # Initialize biological timing circuits
        self.timing_circuits = BiologicalTimingCircuit()
        
        # Initialize neuromodulation system
        self.neuromodulation = NeuromodulationSystem()
        
        # Memory-efficient spike tracking
        self.max_history_length = self.config.get('max_history_length', 10000)
        self.spike_history = np.zeros((0, 0), dtype=bool)  # neurons x timesteps
        self.activity_history = {}  # neuron_id -> activity level
        
        # Statistics
        self.stats = {
            'spikes_fired': 0,
            'spikes_received': 0,
            'simulation_steps': 0
        }
        
        # Hardware acceleration info
        self._setup_hardware_acceleration()
        
        # Plasticity engine (attached later)
        self.plasticity_engine = None
        self.has_plasticity_engine = False
    
    def _set_default_config(self):
        """Set default configuration values."""
        defaults = {
            'neuron_placement': 'grid',
            'default_neuron_model': NeuronModel.LIF,
            'default_conn_prob': 0.1,
            'default_weight_range': (0.1, 1.0),
            'default_delay_range': (1.0, 5.0),
            'use_hardware_acceleration': True,
            'precision': 'float32',
            'random_seed': None,
            'connectivity_sigma': 0.2,  # Length constant for distance-dependent connectivity
            'max_history_length': 10000
        }
        
        for key, value in defaults.items():
            if key not in self.config:
                self.config[key] = value
                
        # Set random seed if specified
        if self.config['random_seed'] is not None:
            np.random.seed(self.config['random_seed'])
    
    def _setup_hardware_acceleration(self):
        """Set up hardware acceleration if available."""
        self.use_gpu = False
        self.use_jax = False
        self.use_numba = False
        self.use_parallel = False  # FIX: Add this missing attribute
        self.num_workers = 1
        self.thread_pool = None
        self.process_pool = None
        self.hardware_info = {}  # FIX: Initialize as empty dict

        if not self.config['use_hardware_acceleration']:
            return
            
        # Check for PyTorch
        if HAS_TORCH:
            if torch.cuda.is_available():
                self.use_gpu = True
                self.device = torch.device("cuda")
                logger.info(f"Using GPU acceleration with PyTorch: {torch.cuda.get_device_name(0)}")
                
                # Initialize GPU tensors
                self.weights_tensor = None
                self.last_spikes_tensor = None
                return
        
        # Check for JAX
        if HAS_JAX:
            self.use_jax = True
            logger.info(f"Using JAX acceleration")
            return
        
        logger.info("No hardware acceleration available or configured.")
    
    def _setup_hardware_acceleration(self):
        """Enhanced hardware acceleration setup - REPLACE YOUR EXISTING METHOD."""
        self.hardware_info = detect_hardware_capabilities()
        self.use_gpu = False
        self.use_jax = False
        self.use_numba = False
        
        if not self.config.get('use_hardware_acceleration', True):
            logger.info("Hardware acceleration disabled by configuration")
            return
        
        backend = self.hardware_info['recommended_backend']
        
        if backend == 'cupy' and CUPY_AVAILABLE:
            self._setup_gpu_acceleration()
        elif backend == 'jax' and JAX_AVAILABLE:
            self._setup_jax_acceleration()
        elif backend == 'numba' and NUMBA_AVAILABLE:
            self._setup_numba_acceleration()
        
        # Setup parallel processing
        self._setup_parallel_processing()
        
        logger.info(f"Hardware acceleration configured: {backend}")

    def _setup_gpu_acceleration(self):
        """Setup GPU acceleration with CuPy."""
        try:
            self.use_gpu = True
            self.device = cp.cuda.Device(0)
            
            # Pre-allocate GPU arrays
            max_neurons = 10000  # Adjust based on your needs
            self.gpu_voltages = cp.zeros(max_neurons, dtype=cp.float32)
            self.gpu_synaptic_currents = cp.zeros(max_neurons, dtype=cp.float32)
            self.gpu_external_currents = cp.zeros(max_neurons, dtype=cp.float32)
            self.gpu_spikes = cp.zeros(max_neurons, dtype=cp.bool_)
            
            # GPU kernel for neuron updates
            self.gpu_update_kernel = cp.ElementwiseKernel(
                'float32 v, float32 i_syn, float32 i_ext, float32 v_rest, '
                'float32 v_threshold, float32 v_reset, float32 tau_m, float32 r_m, float32 dt',
                'float32 v_out, bool spike',
                '''
                float i_total = i_syn + i_ext;
                float dv = (-(v - v_rest) + r_m * i_total) * (dt / tau_m);
                v_out = v + dv;
                spike = false;
                if (v_out >= v_threshold) {
                    v_out = v_reset;
                    spike = true;
                }
                ''',
                'lif_update'
            )
            
            logger.info(f"GPU acceleration enabled: {self.hardware_info['gpu']['device_name']}")
            
        except Exception as e:
            logger.warning(f"Failed to setup GPU acceleration: {e}")
            self.use_gpu = False

    def _setup_jax_acceleration(self):
        """Setup JAX acceleration."""
        try:
            self.use_jax = True
            
            # JIT-compiled neuron update function
            @jax.jit
            def jax_update_neurons(voltages, synaptic_currents, external_currents,
                                v_rest, v_threshold, v_reset, tau_m, r_m, dt):
                i_total = synaptic_currents + external_currents
                dv = (-(voltages - v_rest) + r_m * i_total) * (dt / tau_m)
                new_voltages = voltages + dv
                
                spikes = new_voltages >= v_threshold
                new_voltages = jnp.where(spikes, v_reset, new_voltages)
                
                return new_voltages, spikes
            
            self.jax_update_neurons = jax_update_neurons
            
            logger.info(f"JAX acceleration enabled: {self.hardware_info['jax']['backend']}")
            
        except Exception as e:
            logger.warning(f"Failed to setup JAX acceleration: {e}")
            self.use_jax = False

    def _setup_numba_acceleration(self):
        """Setup Numba JIT acceleration."""
        try:
            self.use_numba = True
            logger.info(f"Numba acceleration enabled: {self.hardware_info['numba']['version']}")
            
        except Exception as e:
            logger.warning(f"Failed to setup Numba acceleration: {e}")
            self.use_numba = False

    def _setup_parallel_processing(self):
        """Setup parallel processing for large networks."""
        self.use_parallel = self.config.get('use_parallel', True)
        
        if self.use_parallel:
            # Determine optimal number of workers
            cpu_count = self.hardware_info['cpu']['count']
            self.num_workers = min(cpu_count, 8)  # Limit to 8 workers max
            
            # Thread pool for I/O bound tasks  
            self.thread_pool = ThreadPoolExecutor(max_workers=self.num_workers)
            
            # Process pool for CPU-bound tasks
            self.process_pool = ProcessPoolExecutor(max_workers=self.num_workers)
            
            logger.info(f"Parallel processing enabled: {self.num_workers} workers")

    def step_accelerated(self, dt: float, external_input: Optional[Dict[int, float]] = None) -> np.ndarray:
        """Accelerated simulation step - REPLACE YOUR EXISTING step() METHOD."""
        t_new = self.current_time + dt
        
        # Update timing circuits and neuromodulation
        oscillations = self.timing_circuits.update(dt)
        neuromodulators = self.neuromodulation.update(dt)
        
        # Process external input
        if external_input is not None:
            for neuron_id, current in external_input.items():
                self.external_currents[neuron_id] = current
        
        # Process pending spikes
        self._process_pending_spikes_accelerated(t_new)
        
        # Update neurons using appropriate acceleration
        if self.use_gpu and len(self.neurons) > 1000:
            spikes = self._update_neurons_gpu(dt, external_input)
        elif self.use_jax and len(self.neurons) > 500:
            spikes = self._update_neurons_jax(dt, external_input)
        elif self.use_numba and len(self.neurons) > 100:
            spikes = self._update_neurons_numba(dt, external_input)
        elif self.use_parallel and len(self.neurons) > 50:
            spikes = self._update_neurons_parallel(dt, external_input)
        else:
            spikes = self._update_neurons_sequential(dt, external_input)
        
        # Update biophysical synapses if they exist
        self.update_biophysical_synapses(dt)
        
        # Process new spikes
        self._process_new_spikes(spikes, t_new)
        
        # Update time and statistics
        self.current_time = t_new
        self.stats['simulation_steps'] += 1
        self.external_currents.clear()
        
        return spikes

    def _update_neurons_gpu(self, dt: float, external_input: Dict[int, float]) -> np.ndarray:
        """GPU-accelerated neuron update."""  
        n_neurons = len(self.neurons)
        
        # Prepare data arrays
        voltages = cp.array([neuron.v for neuron in self.neurons.values()], dtype=cp.float32)
        synaptic_currents = cp.zeros(n_neurons, dtype=cp.float32)
        external_currents = cp.array([external_input.get(i, 0.0) for i in range(n_neurons)], dtype=cp.float32)
        
        # Neuron parameters
        v_rest = cp.array([neuron.params.v_rest for neuron in self.neurons.values()], dtype=cp.float32)
        v_threshold = cp.array([neuron.params.v_threshold for neuron in self.neurons.values()], dtype=cp.float32) 
        v_reset = cp.array([neuron.params.v_reset for neuron in self.neurons.values()], dtype=cp.float32)
        tau_m = cp.array([neuron.params.tau_m for neuron in self.neurons.values()], dtype=cp.float32)
        r_m = cp.array([neuron.params.r_membrane for neuron in self.neurons.values()], dtype=cp.float32)
        
        # Run GPU kernel
        new_voltages, spikes_gpu = self.gpu_update_kernel(
            voltages, synaptic_currents, external_currents,
            v_rest, v_threshold, v_reset, tau_m, r_m, cp.float32(dt)
        )
        
        # Update neuron voltages
        for i, neuron in enumerate(self.neurons.values()):
            neuron.v = float(new_voltages[i])
        
        # Convert spikes back to CPU
        spikes = cp.asnumpy(spikes_gpu).astype(bool)
        
        return spikes

    def _update_neurons_jax(self, dt: float, external_input: Dict[int, float]) -> np.ndarray:
        """JAX-accelerated neuron update."""
        n_neurons = len(self.neurons)
        
        # Prepare arrays
        voltages = jnp.array([neuron.v for neuron in self.neurons.values()])
        synaptic_currents = jnp.zeros(n_neurons)
        external_currents = jnp.array([external_input.get(i, 0.0) for i in range(n_neurons)])
        
        # Parameters
        v_rest = jnp.array([neuron.params.v_rest for neuron in self.neurons.values()])
        v_threshold = jnp.array([neuron.params.v_threshold for neuron in self.neurons.values()])
        v_reset = jnp.array([neuron.params.v_reset for neuron in self.neurons.values()])
        tau_m = jnp.array([neuron.params.tau_m for neuron in self.neurons.values()])
        r_m = jnp.array([neuron.params.r_membrane for neuron in self.neurons.values()])
        
        # Update using JIT-compiled function
        new_voltages, spikes_jax = self.jax_update_neurons(
            voltages, synaptic_currents, external_currents,
            v_rest, v_threshold, v_reset, tau_m, r_m, dt
        )
        
        # Update neuron voltages
        for i, neuron in enumerate(self.neurons.values()):
            neuron.v = float(new_voltages[i])
        
        return np.array(spikes_jax)

    def _update_neurons_numba(self, dt: float, external_input: Dict[int, float]) -> np.ndarray:
        """Numba-accelerated neuron update."""
        n_neurons = len(self.neurons)
        
        # Prepare arrays
        voltages = np.array([neuron.v for neuron in self.neurons.values()], dtype=np.float32)
        synaptic_currents = np.zeros(n_neurons, dtype=np.float32)
        external_currents = np.array([external_input.get(i, 0.0) for i in range(n_neurons)], dtype=np.float32)
        
        # Parameters
        v_rest = np.array([neuron.params.v_rest for neuron in self.neurons.values()], dtype=np.float32)
        v_threshold = np.array([neuron.params.v_threshold for neuron in self.neurons.values()], dtype=np.float32)
        v_reset = np.array([neuron.params.v_reset for neuron in self.neurons.values()], dtype=np.float32)
        tau_m = np.array([neuron.params.tau_m for neuron in self.neurons.values()], dtype=np.float32)
        r_m = np.array([neuron.params.r_membrane for neuron in self.neurons.values()], dtype=np.float32)
        
        # Update using Numba-compiled function
        spikes = update_neurons_parallel(voltages, synaptic_currents, external_currents,
                                    v_rest, v_threshold, v_reset, tau_m, r_m, dt)
        
        # Update neuron voltages
        for i, neuron in enumerate(self.neurons.values()):
            neuron.v = voltages[i]
        
        return spikes

    def _update_neurons_parallel(self, dt: float, external_input: Dict[int, float]) -> np.ndarray:
        """Parallel neuron update using multiprocessing."""
        neuron_list = list(self.neurons.values())
        n_neurons = len(neuron_list)
        
        # Split neurons into batches for parallel processing
        batch_size = max(1, n_neurons // self.num_workers)
        batches = [neuron_list[i:i + batch_size] for i in range(0, n_neurons, batch_size)]
        
        # Process batches in parallel
        futures = []
        for batch in batches:
            future = self.thread_pool.submit(self._update_neuron_batch, batch, dt, external_input)
            futures.append(future)
        
        # Collect results
        spikes = np.zeros(n_neurons, dtype=bool)
        batch_start = 0
        
        for future in futures:
            batch_spikes = future.result()
            batch_end = batch_start + len(batch_spikes)
            spikes[batch_start:batch_end] = batch_spikes
            batch_start = batch_end
        
        return spikes

    def _update_neuron_batch(self, neuron_batch: List, dt: float, 
                            external_input: Dict[int, float]) -> np.ndarray:
        """Update a batch of neurons."""
        batch_spikes = np.zeros(len(neuron_batch), dtype=bool)
        
        for i, neuron in enumerate(neuron_batch):
            i_ext = external_input.get(neuron.id, 0.0)
            
            # Add oscillatory modulation
            if hasattr(self, 'timing_circuits'):
                i_ext += self.timing_circuits.get_modulation(neuron.id) * 0.2
            
            # Update neuron
            if neuron.step(dt, self.current_time, i_ext):
                batch_spikes[i] = True
                self.stats['spikes_fired'] += 1
        
        return batch_spikes

    def _update_neurons_sequential(self, dt: float, external_input: Dict[int, float]) -> np.ndarray:
        """Sequential neuron update (fallback method)."""
        spikes = np.zeros(len(self.neurons), dtype=bool)
        
        for i, (neuron_id, neuron) in enumerate(self.neurons.items()):
            i_ext = external_input.get(neuron_id, 0.0)
            
            # Add oscillatory modulation
            if hasattr(self, 'timing_circuits'):
                i_ext += self.timing_circuits.get_modulation(neuron_id) * 0.2
            
            # Apply neuromodulation
            if hasattr(self, 'neuromodulation'):
                neuromodulators = self.neuromodulation.get_concentration
                neuron.apply_neuromodulation(neuromodulators)
            
            # Update neuron
            if neuron.step(dt, self.current_time, i_ext):
                spikes[i] = True
                self.stats['spikes_fired'] += 1
                
                # Update activity history
                self.activity_history[neuron_id] = self.activity_history.get(neuron_id, 0.0) * 0.95 + 1.0
        
        return spikes

    def get_hardware_info(self) -> Dict[str, Any]:
        """Get hardware acceleration information."""
        return {
            'hardware_detection': getattr(self, 'hardware_info', {}),
            'acceleration_enabled': {
                'gpu': getattr(self, 'use_gpu', False),
                'jax': getattr(self, 'use_jax', False), 
                'numba': getattr(self, 'use_numba', False),
                'parallel': getattr(self, 'use_parallel', False)  # FIX: Use getattr with default
            },
            'performance_stats': {
                'num_workers': getattr(self, 'num_workers', 1),
                'recommended_backend': self.hardware_info.get('recommended_backend', 'numpy') if hasattr(self, 'hardware_info') else 'numpy'
            }
        }




    def create_neurons(self, 
                      count: int, 
                      model: Union[str, NeuronModel] = None, 
                      neuron_type: Union[str, NeuronType] = None,
                      params: Optional[NeuronParameters] = None) -> List[int]:
        """
        Create neurons in the core.
        
        Args:
            count: Number of neurons to create
            model: Neuron model
            neuron_type: Type of neuron
            params: Neuron parameters
            
        Returns:
            List of created neuron IDs
        """
        # Get model
        if model is None:
            model = self.config['default_neuron_model']
            
        if isinstance(model, str):
            try:
                model = NeuronModel[model]
            except KeyError:
                logger.warning(f"Unknown neuron model: {model}. Using default.")
                model = self.config['default_neuron_model']
        
        # Get neuron type
        if neuron_type is None:
            # Assign type based on excitatory/inhibitory ratio
            r = np.random.random(count)
            neuron_types = []
            cumsum = 0.0
            
            for nt, fraction in self.neuron_distribution.items():
                threshold = cumsum + fraction
                count_of_type = np.sum((r >= cumsum) & (r < threshold))
                neuron_types.extend([nt] * count_of_type)
                cumsum = threshold
                
            # Handle any remaining neurons due to rounding
            if len(neuron_types) < count:
                remaining = count - len(neuron_types)
                neuron_types.extend([NeuronType.EXCITATORY] * remaining)
                
        elif isinstance(neuron_type, str):
            try:
                neuron_type = NeuronType[neuron_type]
                neuron_types = [neuron_type] * count
            except KeyError:
                logger.warning(f"Unknown neuron type: {neuron_type}. Using EXCITATORY.")
                neuron_types = [NeuronType.EXCITATORY] * count
        else:
            neuron_types = [neuron_type] * count
            
        # Get parameters
        if params is None:
            # Use different parameters for each neuron type
            params_list = []
            for nt in neuron_types:
                params_list.append(NeuronParameters.from_neuron_type(nt))
        else:
            params_list = [params] * count
            
        # Create neurons
        neuron_ids = []
        
        for i in range(count):
            neuron_id = self.neuron_count
            self.neuron_count += 1
            
            # Create neuron based on model
            if model == NeuronModel.LIF:
                neuron = LIFNeuron(neuron_id, params_list[i])
            elif model == NeuronModel.ADEX:
                neuron = AdExNeuron(neuron_id, params_list[i])
            elif model == NeuronModel.IZHIKEVICH:
                neuron = IzhikevichNeuron(neuron_id, params_list[i])
            else:
                # Default to LIF
                neuron = LIFNeuron(neuron_id, params_list[i])
            
            # Apply type-specific properties
            if neuron_types[i] == NeuronType.INHIBITORY:
                neuron.is_inhibitory = True
                neuron.is_excitatory = False
            
            # Add neuron to core
            self.neurons[neuron_id] = neuron
            self.neuron_types[neuron_id] = neuron_types[i]
            self.neuron_models[neuron_id] = model
            self.activity_history[neuron_id] = 0.0
            
            neuron_ids.append(neuron_id)
            
        # Place neurons in 3D space
        self._place_neurons(neuron_ids)
        
        # Resize spike history matrix
        if self.neuron_count > self.spike_history.shape[0]:
            # Create new larger matrix
            new_history = np.zeros((self.neuron_count, self.spike_history.shape[1]), dtype=bool)
            # Copy old data
            new_history[:self.spike_history.shape[0], :] = self.spike_history
            self.spike_history = new_history
            
        return neuron_ids
    
    def _place_neurons(self, neuron_ids: List[int]):
        """
        Place neurons in 3D space.
        
        Args:
            neuron_ids: List of neuron IDs to place
        """
        placement = self.config['neuron_placement']
        
        if placement == 'grid':
            # Place neurons in a grid
            self._place_neurons_grid(neuron_ids)
        elif placement == 'random':
            # Place neurons randomly
            self._place_neurons_random(neuron_ids)
        else:
            # Default to grid
            self._place_neurons_grid(neuron_ids)
    
    def _place_neurons_grid(self, neuron_ids: List[int]):
        """
        Place neurons in a grid arrangement.
        
        Args:
            neuron_ids: List of neuron IDs to place
        """
        dim_x, dim_y, dim_z = self.dimensions
        count = len(neuron_ids)
        
        # Calculate grid dimensions
        grid_size = int(np.ceil(count**(1/3)))
        
        # Distribute neurons in 3D grid
        idx = 0
        for i in range(min(grid_size, dim_x)):
            for j in range(min(grid_size, dim_y)):
                for k in range(min(grid_size, dim_z)):
                    if idx < count:
                        # Normalize coordinates to [0, 1]
                        x = i / max(1, dim_x - 1)
                        y = j / max(1, dim_y - 1)
                        z = k / max(1, dim_z - 1)
                        
                        self.neuron_positions[neuron_ids[idx]] = (x, y, z)
                        idx += 1
                    else:
                        return
    
    def _place_neurons_random(self, neuron_ids: List[int]):
        """
        Place neurons in random positions.
        
        Args:
            neuron_ids: List of neuron IDs to place
        """
        for neuron_id in neuron_ids:
            # Random position in [0, 1] range for each dimension
            x = np.random.random()
            y = np.random.random()
            z = np.random.random()
            
            self.neuron_positions[neuron_id] = (x, y, z)
    
    def connect_neurons(self, 
                       pre_ids: List[int], 
                       post_ids: List[int],
                       connection_prob: float = None,
                       weight_range: Tuple[float, float] = None,
                       delay_range: Tuple[float, float] = None,
                       synapse_type: Union[str, SynapseType] = None,
                       params: Optional[SynapseParameters] = None) -> List[Tuple[int, int]]:
        """
        Connect neurons with specified parameters.
        
        Args:
            pre_ids: List of presynaptic neuron IDs
            post_ids: List of postsynaptic neuron IDs
            connection_prob: Connection probability
            weight_range: Range for random weight assignment (min, max)
            delay_range: Range for random delay assignment (min, max)
            synapse_type: Type of synapse
            params: Synapse parameters
            
        Returns:
            List of established connections as (pre_id, post_id) tuples
        """
        # Get default values if not specified
        if connection_prob is None:
            connection_prob = self.config['default_conn_prob']
            
        if weight_range is None:
            weight_range = self.config['default_weight_range']
            
        if delay_range is None:
            delay_range = self.config['default_delay_range']
            
        # Get synapse type
        if synapse_type is None:
            # Use EXCITATORY for excitatory neurons, INHIBITORY for inhibitory
            synapse_types = {}
            for pre_id in pre_ids:
                if pre_id in self.neuron_types:
                    if self.neuron_types[pre_id] == NeuronType.EXCITATORY:
                        synapse_types[pre_id] = SynapseType.EXCITATORY
                    elif self.neuron_types[pre_id] == NeuronType.INHIBITORY:
                        synapse_types[pre_id] = SynapseType.INHIBITORY
                    elif self.neuron_types[pre_id] == NeuronType.MODULATORY:
                        synapse_types[pre_id] = SynapseType.MODULATORY
                    else:
                        synapse_types[pre_id] = SynapseType.EXCITATORY
                else:
                    synapse_types[pre_id] = SynapseType.EXCITATORY
        elif isinstance(synapse_type, str):
            try:
                synapse_type = SynapseType[synapse_type]
                synapse_types = {pre_id: synapse_type for pre_id in pre_ids}
            except KeyError:
                logger.warning(f"Unknown synapse type: {synapse_type}. Using EXCITATORY.")
                synapse_types = {pre_id: SynapseType.EXCITATORY for pre_id in pre_ids}
        else:
            synapse_types = {pre_id: synapse_type for pre_id in pre_ids}
            
        # Get parameters
        if params is None:
            # Use different parameters for each synapse type
            params_dict = {}
            for pre_id in pre_ids:
                params_dict[pre_id] = SynapseParameters.from_synapse_type(synapse_types[pre_id])
        else:
            params_dict = {pre_id: params for pre_id in pre_ids}
            
        # Create connections
        connections = []
        
        for pre_id in pre_ids:
            for post_id in post_ids:
                # Skip self-connections
                if pre_id == post_id:
                    continue
                    
                # Probabilistic connection
                if np.random.random() >= connection_prob:
                    continue
                    
                # Get parameters for this connection
                syn_params = params_dict[pre_id].get_synapse_params(synapse_types[pre_id])
                
                # Determine weight
                if synapse_types[pre_id] == SynapseType.INHIBITORY:
                    # Ensure negative weight for inhibitory synapses
                    w_min = min(weight_range[0], weight_range[1])
                    w_max = max(weight_range[0], weight_range[1])
                    weight = -np.random.uniform(abs(w_min), abs(w_max))
                else:
                    # Positive weight for excitatory
                    weight = np.random.uniform(weight_range[0], weight_range[1])
                    
                # Determine delay
                delay = np.random.uniform(delay_range[0], delay_range[1])
                
                # Add connection
                conn_key = (pre_id, post_id)
                self.connections[conn_key] = {
                    'pre_id': pre_id,
                    'post_id': post_id,
                    'weight': weight,
                    'delay': delay,
                    'synapse_type': synapse_types[pre_id],
                    'params': syn_params
                }
                
                self.connection_weights[conn_key] = weight
                self.connection_delays[conn_key] = delay
                self.connection_ages[conn_key] = 0.0
                self.connection_activity[conn_key] = 0.0
                
                connections.append(conn_key)
                
        return connections
    
    def connect_by_distance(self,
                          neuron_ids: List[int],
                          max_distance: float,
                          connection_prob: float = None,
                          weight_func: Optional[Callable[[float], float]] = None,
                          delay_func: Optional[Callable[[float], float]] = None,
                          synapse_type: Union[str, SynapseType] = None,
                          params: Optional[SynapseParameters] = None) -> List[Tuple[int, int]]:
        """
        Connect neurons based on spatial distance.
        
        Args:
            neuron_ids: List of neuron IDs to connect
            max_distance: Maximum connection distance
            connection_prob: Base connection probability
            weight_func: Function to compute weight from distance
            delay_func: Function to compute delay from distance
            synapse_type: Type of synapse
            params: Synapse parameters
            
        Returns:
            List of established connections as (pre_id, post_id) tuples
        """
        if connection_prob is None:
            connection_prob = self.config['default_conn_prob']
            
        # Default weight function: weight = 1.0 * (1 - d/max_d)
        if weight_func is None:
            weight_func = lambda d: max(0.1, 1.0 * (1 - d/max_distance))
            
        # Default delay function: delay = 1.0 + 4.0 * d/max_d
        if delay_func is None:
            delay_func = lambda d: 1.0 + 4.0 * (d/max_distance)
            
        # Calculate distances between neurons
        positions = np.array([self.neuron_positions[nid] for nid in neuron_ids])
        distances = cdist(positions, positions)
        
        # Establish connections
        connections = []
        
        for i, pre_id in enumerate(neuron_ids):
            for j, post_id in enumerate(neuron_ids):
                # Skip self-connections and distant neurons
                if i == j or distances[i, j] > max_distance:
                    continue
                    
                # Probabilistic connection (connection probability decreases with distance)
                p_conn = connection_prob * (1 - distances[i, j]/max_distance)
                if np.random.random() >= p_conn:
                    continue
                    
                # Compute weight and delay based on distance
                weight = weight_func(distances[i, j])
                delay = delay_func(distances[i, j])
                
                # Adjust weight if inhibitory
                if pre_id in self.neuron_types and self.neuron_types[pre_id] == NeuronType.INHIBITORY:
                    weight = -abs(weight)
                
                # Create connection
                conn_key = (pre_id, post_id)
                self.connections[conn_key] = {
                    'pre_id': pre_id,
                    'post_id': post_id,
                    'weight': weight,
                    'delay': delay,
                    'synapse_type': SynapseType.INHIBITORY if weight < 0 else SynapseType.EXCITATORY,
                    'params': params or SynapseParameters()
                }
                
                self.connection_weights[conn_key] = weight
                self.connection_delays[conn_key] = delay
                self.connection_ages[conn_key] = 0.0
                self.connection_activity[conn_key] = 0.0
                
                connections.append(conn_key)
                
        return connections
    
    def create_custom_connections(self,
                                 connectivity_matrix: np.ndarray,
                                 neuron_ids: List[int],
                                 weight_matrix: Optional[np.ndarray] = None,
                                 delay_matrix: Optional[np.ndarray] = None,
                                 synapse_type: Union[str, SynapseType] = None,
                                 params: Optional[SynapseParameters] = None) -> List[Tuple[int, int]]:
        """
        Create connections based on a custom connectivity matrix.
        
        Args:
            connectivity_matrix: Binary matrix indicating connections
            neuron_ids: List of neuron IDs
            weight_matrix: Matrix of connection weights
            delay_matrix: Matrix of connection delays
            synapse_type: Type of synapse
            params: Synapse parameters
            
        Returns:
            List of established connections as (pre_id, post_id) tuples
        """
        if len(neuron_ids) != connectivity_matrix.shape[0] or len(neuron_ids) != connectivity_matrix.shape[1]:
            raise ValueError("Connectivity matrix dimensions must match neuron_ids length.")
            
        # Check weight matrix
        if weight_matrix is not None and (weight_matrix.shape != connectivity_matrix.shape):
            raise ValueError("Weight matrix dimensions must match connectivity matrix.")
            
        # Check delay matrix
        if delay_matrix is not None and (delay_matrix.shape != connectivity_matrix.shape):
            raise ValueError("Delay matrix dimensions must match connectivity matrix.")
            
        # Establish connections
        connections = []
        
        for i, pre_id in enumerate(neuron_ids):
            for j, post_id in enumerate(neuron_ids):
                # Skip if no connection or self-connection
                if connectivity_matrix[i, j] == 0 or pre_id == post_id:
                    continue
                    
                # Get weight and delay
                if weight_matrix is not None:
                    weight = weight_matrix[i, j]
                else:
                    weight_range = self.config['default_weight_range']
                    weight = np.random.uniform(weight_range[0], weight_range[1])
                    
                if delay_matrix is not None:
                    delay = delay_matrix[i, j]
                else:
                    delay_range = self.config['default_delay_range']
                    delay = np.random.uniform(delay_range[0], delay_range[1])
                
                # Determine synapse type from weight if not specified
                st = synapse_type
                if st is None:
                    st = SynapseType.INHIBITORY if weight < 0 else SynapseType.EXCITATORY
                
                # Add connection
                conn_key = (pre_id, post_id)
                self.connections[conn_key] = {
                    'pre_id': pre_id,
                    'post_id': post_id,
                    'weight': weight,
                    'delay': delay,
                    'synapse_type': st,
                    'params': params or SynapseParameters()
                }
                
                self.connection_weights[conn_key] = weight
                self.connection_delays[conn_key] = delay
                self.connection_ages[conn_key] = 0.0
                self.connection_activity[conn_key] = 0.0
                
                connections.append(conn_key)
                
        return connections
    
    def modify_connection(self, pre_id: int, post_id: int, 
                          weight: Optional[float] = None,
                          delay: Optional[float] = None) -> bool:
        """
        Modify an existing connection.
        
        Args:
            pre_id: Presynaptic neuron ID
            post_id: Postsynaptic neuron ID
            weight: New weight (if None, weight is not changed)
            delay: New delay (if None, delay is not changed)
            
        Returns:
            True if connection was modified, False if it doesn't exist
        """
        conn_key = (pre_id, post_id)
        if conn_key not in self.connections:
            return False
        
        # Update weight if specified
        if weight is not None:
            self.connections[conn_key]['weight'] = weight
            self.connection_weights[conn_key] = weight
            
        # Update delay if specified
        if delay is not None:
            self.connections[conn_key]['delay'] = delay
            self.connection_delays[conn_key] = delay
            
        return True
    
    def remove_connection(self, pre_id: int, post_id: int) -> bool:
        """
        Remove a connection.
        
        Args:
            pre_id: Presynaptic neuron ID
            post_id: Postsynaptic neuron ID
            
        Returns:
            True if connection was removed, False if it doesn't exist
        """
        conn_key = (pre_id, post_id)
        if conn_key not in self.connections:
            return False
        
        # Remove connection
        del self.connections[conn_key]
        del self.connection_weights[conn_key]
        del self.connection_delays[conn_key]
        del self.connection_ages[conn_key]
        del self.connection_activity[conn_key]
        
        return True
    
    def set_external_current(self, neuron_id: int, current: float):
        """
        Set external input current for a neuron.
        
        Args:
            neuron_id: Neuron ID
            current: Input current value
        """
        self.external_currents[neuron_id] = current
    
    def deliver_spike(self, source_id: int, target_id: int, t_deliver: float):
        """
        Deliver a spike from source to target neuron at specified time.
        
        Args:
            source_id: Source neuron ID
            target_id: Target neuron ID
            t_deliver: Delivery time
        """
        if (source_id, target_id) in self.connections:
            weight = self.connections[(source_id, target_id)]['weight']
            self.pending_spikes[(target_id, t_deliver)].append((source_id, weight))
    
    def step(self, dt: float, external_input: Optional[Dict[int, float]] = None) -> np.ndarray:
        """
        Advance simulation by one time step.
        
        Args:
            dt: Time step size (ms)
            external_input: External current to neurons (neuron_id -> current)
            
        Returns:
            Array of which neurons fired
        """
        # Update current time
        t_new = self.current_time + dt
        
        # Update biological timing circuits
        oscillations = self.timing_circuits.update(dt)
        
        # Update neuromodulation system
        neuromodulators = self.neuromodulation.update(dt)
        
        # Apply external input if provided
        if external_input is not None:
            for neuron_id, current in external_input.items():
                self.external_currents[neuron_id] = current
        
        # Process pending spikes that should be delivered by the new time
        delivered_keys = []
        
        for (target_id, t_deliver), spikes in self.pending_spikes.items():
            if t_deliver <= t_new:
                if target_id in self.neurons:
                    for source_id, weight in spikes:
                        # Update connection activity
                        conn_key = (source_id, target_id)
                        if conn_key in self.connection_activity:
                            self.connection_activity[conn_key] += 1.0
                        
                        # Deliver spike to target neuron
                        self.neurons[target_id].receive_spike(weight, t_deliver)
                        self.stats['spikes_received'] += 1
                        
                delivered_keys.append((target_id, t_deliver))
                
        # Remove delivered spikes
        for key in delivered_keys:
            del self.pending_spikes[key]
            
        # Update all neurons
        spikes = np.zeros(self.neuron_count, dtype=bool)
        
        for neuron_id, neuron in self.neurons.items():
            # Get external current if any
            i_ext = self.external_currents.get(neuron_id, 0.0)
            
            # Add modulation from biological timing circuits
            if neuron_id < len(oscillations):
                i_ext += self.timing_circuits.get_modulation(neuron_id) * 0.2
            
            # Apply neuromodulation
            neuron.apply_neuromodulation(neuromodulators)
            
            # Update neuron and check if it fired
            if neuron.step(dt, t_new, i_ext):
                spikes[neuron_id] = True
                self.stats['spikes_fired'] += 1
                
                # Update activity history (decay-based)
                self.activity_history[neuron_id] = self.activity_history.get(neuron_id, 0.0) * 0.95 + 1.0
        
        # Process newly fired neurons
        for source_id in np.where(spikes)[0]:
            # Find outgoing connections
            for conn_key, conn in self.connections.items():
                pre, post = conn_key
                if pre == source_id:
                    # Schedule spike delivery
                    delay = conn['delay']
                    t_deliver = t_new + delay
                    
                    self.pending_spikes[(post, t_deliver)].append((source_id, conn['weight']))
                    
                    # Update connection age
                    self.connection_ages[conn_key] += dt
        
        # Update all connection ages
        for conn_key in self.connections:
            self.connection_ages[conn_key] += dt
        
        # Update spike history
        if self.spike_history.shape[1] == 0:
            # First time step
            self.spike_history = spikes.reshape(-1, 1)
        else:
            # Append to history, with size limit
            self.spike_history = np.column_stack((self.spike_history, spikes))
            if self.spike_history.shape[1] > self.max_history_length:
                self.spike_history = self.spike_history[:, -self.max_history_length:]
        
        # Apply synaptic plasticity if engine is attached
        if self.has_plasticity_engine and self.plasticity_engine is not None:
            # Forward spike events to plasticity engine
            fired_neurons = np.where(spikes)[0]
            for neuron_id in fired_neurons:
                self.plasticity_engine.on_spike(neuron_id, self.current_time)
            
            # Update plasticity engine
            self.plasticity_engine.step(dt)
                    
        # Update simulation time
        self.current_time = t_new
        self.stats['simulation_steps'] += 1
        
        # Clear external currents for next step
        self.external_currents.clear()
        
        return spikes
    
    def run(self, duration: float, dt: float = 0.1, monitors: Optional[List[Any]] = None,
            input_func: Optional[Callable[[float], Dict[int, float]]] = None,
            record_output: bool = True) -> Dict[str, Any]:
        """
        Run simulation for a specified duration.
        
        Args:
            duration: Simulation duration (ms)
            dt: Time step size (ms)
            monitors: List of monitors to record data
            input_func: Function that takes time and returns input currents
            record_output: Whether to record spike output
            
        Returns:
            Dictionary with simulation results
        """
        steps = int(duration / dt)
        
        # Initialize output recording if requested
        if record_output:
            output_spikes = np.zeros((self.neuron_count, steps), dtype=bool)
        
        # Run simulation
        for i in range(steps):
            # Get input for this step if input function is provided
            if input_func is not None:
                external_input = input_func(self.current_time)
            else:
                external_input = None
            
            # Take a step
            spikes = self.step(dt, external_input)
            
            # Record output spikes
            if record_output:
                output_spikes[:, i] = spikes
            
            # Record data if monitoring
            if monitors:
                for monitor in monitors:
                    monitor.record(self.current_time)
        
        # Collect results
        results = {
            'simulation_time': self.current_time,
            'stats': self.stats
        }
        
        if record_output:
            results['output_spikes'] = output_spikes
            
        # Include monitor results
        if monitors:
            results['monitors'] = {}
            for i, monitor in enumerate(monitors):
                results['monitors'][f'monitor_{i}'] = monitor.get_results()
                
        return results
    
    def reset(self):
        """Reset simulation state."""
        # Reset all neurons
        for neuron in self.neurons.values():
            neuron.reset()
            
        # Clear pending spikes
        self.pending_spikes.clear()
        
        # Clear external currents
        self.external_currents.clear()
        
        # Reset activity history
        for neuron_id in self.activity_history:
            self.activity_history[neuron_id] = 0.0
        
        # Reset connection activity and ages
        for conn_key in self.connection_activity:
            self.connection_activity[conn_key] = 0.0
            self.connection_ages[conn_key] = 0.0
        
        # Reset simulation time
        self.current_time = 0.0
        
        # Reset statistics
        self.stats = {
            'spikes_fired': 0,
            'spikes_received': 0,
            'simulation_steps': 0
        }
        
        # Reset spike history
        self.spike_history = np.zeros((self.neuron_count, 0), dtype=bool)
        
        # Reset biological timing circuits
        self.timing_circuits = BiologicalTimingCircuit()
        
        # Reset neuromodulation system
        self.neuromodulation = NeuromodulationSystem()
        
        logger.info("Reset neural network state")
    
    def get_neurons(self) -> List[Any]:
        """
        Get all neurons.
        
        Returns:
            List of neuron objects
        """
        return list(self.neurons.values())
    
    def get_connections(self) -> List[Dict[str, Any]]:
        """
        Get all connections.
        
        Returns:
            List of connection information dictionaries
        """
        return list(self.connections.values())
    
    def get_neuron_types(self) -> Dict[int, NeuronType]:
        """
        Get neuron types.
        
        Returns:
            Dictionary mapping neuron ID to neuron type
        """
        return self.neuron_types
    
    def get_neuron_positions(self) -> Dict[int, Tuple[float, float, float]]:
        """
        Get neuron positions.
        
        Returns:
            Dictionary mapping neuron ID to 3D position
        """
        return self.neuron_positions
    
    def get_connectivity_matrix(self) -> np.ndarray:
        """
        Get connectivity matrix.
        
        Returns:
            Binary connectivity matrix
        """
        n = self.neuron_count
        matrix = np.zeros((n, n), dtype=int)
        
        for (pre, post) in self.connections.keys():
            if pre < n and post < n:
                matrix[pre, post] = 1
                
        return matrix
    
    def get_weight_matrix(self) -> np.ndarray:
        """
        Get weight matrix.
        
        Returns:
            Matrix of connection weights
        """
        n = self.neuron_count
        matrix = np.zeros((n, n))
        
        for (pre, post), weight in self.connection_weights.items():
            if pre < n and post < n:
                matrix[pre, post] = weight
                
        return matrix
    
    def get_spike_rate(self, window_ms: float = 100.0) -> np.ndarray:
        """
        Get firing rates of all neurons.
        
        Args:
            window_ms: Time window for rate calculation (ms)
            
        Returns:
            Array of firing rates (Hz)
        """
        # Determine how many time steps to include
        if self.spike_history.shape[1] == 0:
            return np.zeros(self.neuron_count)
        
        # FIX: Handle case where current_time is 0 or very small
        if self.current_time <= 0:
            return np.zeros(self.neuron_count)
            
        dt = self.current_time / self.spike_history.shape[1]
        # FIX: Handle case where dt is 0
        if dt <= 0:
            return np.zeros(self.neuron_count)

        window_steps = int(window_ms / dt)
        
        # Limit to available history
        window_steps = min(window_steps, self.spike_history.shape[1])
        
        if window_steps == 0:
            return np.zeros(self.neuron_count)
            
        # Calculate spike counts in window
        recent_history = self.spike_history[:, -window_steps:]
        spike_counts = np.sum(recent_history, axis=1)
        
        # Convert to rates (Hz)
        rates = spike_counts * 1000.0 / (window_steps * dt)
        
        return rates
    
    def get_average_activity(self) -> float:
        """
        Get average activity level across all neurons.
        
        Returns:
            Average activity level
        """
        if not self.activity_history:
            return 0.0
            
        return np.mean(list(self.activity_history.values()))
    
    def attach_plasticity_engine(self, plasticity_engine) -> bool:
        """
        Attach a neuroplasticity engine to the core.
        
        Args:
            plasticity_engine: Neuroplasticity engine
            
        Returns:
            True if engine was attached successfully
        """
        if plasticity_engine is None:
            return False
            
        self.plasticity_engine = plasticity_engine
        self.has_plasticity_engine = True
        
        # Initialize plasticity engine with core reference
        if hasattr(plasticity_engine, 'initialize'):
            plasticity_engine.initialize(self)
            
        logger.info("Attached plasticity engine to core")
        return True
    
    def attach_synaptic_pruning(self, pruning_module) -> bool:
        """
        Attach a synaptic pruning module to the core.
        
        Args:
            pruning_module: Synaptic pruning module
            
        Returns:
            True if module was attached successfully
        """
        if pruning_module is None:
            return False
            
        self.pruning_module = pruning_module
        
        # Initialize pruning module with core reference
        if hasattr(pruning_module, 'initialize'):
            pruning_module.initialize(self)
            
        logger.info("Attached synaptic pruning module to core")
        return True
    
    def visualize_network(self, figsize: Tuple[int, int] = (10, 8), 
                          show_connections: bool = True,
                          max_connections: int = 500,
                          color_by: str = 'type',
                          size_by: str = 'activity') -> plt.Figure:
        """
        Visualize network in 3D.
        
        Args:
            figsize: Figure size (width, height)
            show_connections: Whether to show connections
            max_connections: Maximum number of connections to show
            color_by: How to color neurons ('type', 'model', or 'activity')
            size_by: How to size neurons ('activity', 'none', or 'degree')
            
        Returns:
            Matplotlib figure
        """
        fig = plt.figure(figsize=figsize)
        ax = fig.add_subplot(111, projection='3d')
        
        # Collect neuron positions and properties
        positions = np.array([pos for pos in self.neuron_positions.values()])
        if len(positions) == 0:
            logger.warning("No neurons to visualize")
            return fig
            
        neuron_ids = list(self.neuron_positions.keys())
        
        # Determine colors based on selected criterion
        colors = []
        if color_by == 'type':
            for nid in neuron_ids:
                nt = self.neuron_types.get(nid, NeuronType.EXCITATORY)
                if nt == NeuronType.EXCITATORY:
                    colors.append('blue')
                elif nt == NeuronType.INHIBITORY:
                    colors.append('red')
                elif nt == NeuronType.ADAPTIVE:
                    colors.append('green')
                elif nt == NeuronType.MODULATORY:
                    colors.append('purple')
                else:
                    colors.append('gray')
        elif color_by == 'model':
            for nid in neuron_ids:
                model = self.neuron_models.get(nid, NeuronModel.LIF)
                if model == NeuronModel.LIF:
                    colors.append('blue')
                elif model == NeuronModel.ADEX:
                    colors.append('green')
                elif model == NeuronModel.IZHIKEVICH:
                    colors.append('red')
                else:
                    colors.append('gray')
        elif color_by == 'activity':
            # Color by activity (blue -> red for increasing activity)
            activities = [self.activity_history.get(nid, 0.0) for nid in neuron_ids]
            if activities:
                max_act = max(activities) if max(activities) > 0 else 1.0
                normalized = [a / max_act for a in activities]
                colors = ['blue' if a < 0.33 else ('green' if a < 0.67 else 'red') for a in normalized]
            else:
                colors = ['blue'] * len(neuron_ids)
        else:
            colors = ['blue'] * len(neuron_ids)
            
        # Determine sizes based on selected criterion
        sizes = []
        if size_by == 'activity':
            activities = [self.activity_history.get(nid, 0.0) for nid in neuron_ids]
            if activities:
                max_act = max(activities) if max(activities) > 0 else 1.0
                sizes = [30 + 100 * (a / max_act) for a in activities]
            else:
                sizes = [30] * len(neuron_ids)
        elif size_by == 'degree':
            degrees = []
            for nid in neuron_ids:
                in_degree = sum(1 for (pre, post), _ in self.connections.items() if post == nid)
                out_degree = sum(1 for (pre, post), _ in self.connections.items() if pre == nid)
                degrees.append(in_degree + out_degree)
            if degrees:
                max_degree = max(degrees) if max(degrees) > 0 else 1
                sizes = [30 + 100 * (d / max_degree) for d in degrees]
            else:
                sizes = [30] * len(neuron_ids)
        else:
            sizes = [30] * len(neuron_ids)
        
        # Plot neurons
        ax.scatter(positions[:, 0], positions[:, 1], positions[:, 2], 
                   c=colors, s=sizes, alpha=0.8)
        
        # Plot connections (limit to avoid cluttering)
        if show_connections and self.connections:
            # Sample connections if there are too many
            conn_keys = list(self.connections.keys())
            if len(conn_keys) > max_connections:
                np.random.shuffle(conn_keys)
                conn_keys = conn_keys[:max_connections]
                
            for pre_id, post_id in conn_keys:
                if pre_id in self.neuron_positions and post_id in self.neuron_positions:
                    pre_pos = self.neuron_positions[pre_id]
                    post_pos = self.neuron_positions[post_id]
                    
                    # Get connection properties
                    weight = self.connection_weights.get((pre_id, post_id), 0.0)
                    
                    # Line color and width based on weight
                    if weight > 0:
                        color = 'blue'
                        alpha = min(0.3 + 0.7 * (weight / 2.0), 1.0)
                    else:
                        color = 'red'
                        alpha = min(0.3 + 0.7 * (abs(weight) / 2.0), 1.0)
                        
                    linewidth = min(3.0, 0.5 + abs(weight))
                    
                    # Plot line
                    ax.plot([pre_pos[0], post_pos[0]], 
                           [pre_pos[1], post_pos[1]], 
                           [pre_pos[2], post_pos[2]], 
                           color=color, linewidth=linewidth, alpha=alpha)
        
        # Set labels and title
        ax.set_xlabel('X')
        ax.set_ylabel('Y')
        ax.set_zlabel('Z')
        ax.set_title(f'Neuromorphic Core Network\n{len(self.neurons)} neurons, {len(self.connections)} connections')
        
        # Add legend
        if color_by == 'type':
            from matplotlib.lines import Line2D
            legend_elements = [
                Line2D([0], [0], marker='o', color='w', label='Excitatory', markerfacecolor='blue', markersize=10),
                Line2D([0], [0], marker='o', color='w', label='Inhibitory', markerfacecolor='red', markersize=10),
                Line2D([0], [0], marker='o', color='w', label='Adaptive', markerfacecolor='green', markersize=10),
                Line2D([0], [0], marker='o', color='w', label='Modulatory', markerfacecolor='purple', markersize=10)
            ]
            ax.legend(handles=legend_elements, loc='upper right')
        elif color_by == 'model':
            from matplotlib.lines import Line2D
            legend_elements = [
                Line2D([0], [0], marker='o', color='w', label='LIF', markerfacecolor='blue', markersize=10),
                Line2D([0], [0], marker='o', color='w', label='AdEx', markerfacecolor='green', markersize=10),
                Line2D([0], [0], marker='o', color='w', label='Izhikevich', markerfacecolor='red', markersize=10)
            ]
            ax.legend(handles=legend_elements, loc='upper right')
        
        plt.tight_layout()
        return fig
    
    def save_state(self, filename: str):
        """
        Save core state to file.
        
        Args:
            filename: Output filename
        """
        # Prepare state dictionary
        state = {
            'neuron_count': self.neuron_count,
            'neuron_types': self.neuron_types,
            'neuron_positions': self.neuron_positions,
            'neuron_models': self.neuron_models,
            'connections': self.connections,
            'connection_weights': self.connection_weights,
            'connection_delays': self.connection_delays,
            'connection_ages': self.connection_ages,
            'connection_activity': self.connection_activity,
            'dimensions': self.dimensions,
            'current_time': self.current_time,
            'stats': self.stats,
            'config': self.config,
            'neuron_distribution': self.neuron_distribution,
            'activity_history': self.activity_history
        }
        
        # Save spike history with reduced precision to save space
        if self.spike_history.shape[1] > 0:
            state['spike_history'] = np.packbits(self.spike_history, axis=1)
            state['spike_history_shape'] = self.spike_history.shape
        
        # Save to file
        with open(filename, 'wb') as f:
            pickle.dump(state, f)
        
        logger.info(f"Saved core state to {filename}")
    
    def load_state(self, filename: str):
        """
        Load core state from file.
        
        Args:
            filename: Input filename
        """
        # Load state from file
        with open(filename, 'rb') as f:
            state = pickle.load(f)
        
        # Restore state
        self.neuron_count = state['neuron_count']
        self.neuron_types = state['neuron_types']
        self.neuron_positions = state['neuron_positions']
        self.neuron_models = state['neuron_models']
        self.connections = state['connections']
        self.connection_weights = state['connection_weights']
        self.connection_delays = state['connection_delays']
        self.connection_ages = state['connection_ages']
        self.connection_activity = state['connection_activity']
        self.dimensions = state['dimensions']
        self.current_time = state['current_time']
        self.stats = state['stats']
        self.config = state['config']
        self.neuron_distribution = state['neuron_distribution']
        self.activity_history = state['activity_history']
        
        # Restore spike history
        if 'spike_history' in state and 'spike_history_shape' in state:
            shape = state['spike_history_shape']
            self.spike_history = np.unpackbits(state['spike_history'], axis=1)
            # Reshape to original shape (may be slightly different due to byte alignment)
            self.spike_history = self.spike_history[:, :shape[1]]
        else:
            self.spike_history = np.zeros((self.neuron_count, 0), dtype=bool)
        
        # Recreate neuron objects
        self.neurons = {}
        for neuron_id in range(self.neuron_count):
            if neuron_id in self.neuron_models:
                model = self.neuron_models[neuron_id]
                params = NeuronParameters.from_neuron_type(self.neuron_types.get(neuron_id, NeuronType.EXCITATORY))
                
                if model == NeuronModel.LIF:
                    neuron = LIFNeuron(neuron_id, params)
                elif model == NeuronModel.ADEX:
                    neuron = AdExNeuron(neuron_id, params)
                elif model == NeuronModel.IZHIKEVICH:
                    neuron = IzhikevichNeuron(neuron_id, params)
                else:
                    neuron = LIFNeuron(neuron_id, params)
                
                # Set inhibitory property
                if self.neuron_types.get(neuron_id) == NeuronType.INHIBITORY:
                    neuron.is_inhibitory = True
                    neuron.is_excitatory = False
                
                self.neurons[neuron_id] = neuron
        
        # Reinitialize auxiliary components
        self.timing_circuits = BiologicalTimingCircuit()
        self.neuromodulation = NeuromodulationSystem()
        self.pending_spikes = defaultdict(list)
        self.external_currents = {}
        
        # Reset plasticity engine attachment
        self.plasticity_engine = None
        self.has_plasticity_engine = False
        
        # Update GPU tensors if using acceleration
        if self.use_gpu:
            self._update_gpu_tensors()
            
        logger.info(f"Loaded core state from {filename}")

    def add_biophysical_synapse(self, pre_id: int, post_id: int,
                           synapse_type: str = 'glutamate', 
                           weight: float = 1.0,
                           delay: float = 1.0) -> str:
        """Add a biophysical synapse with detailed vesicle dynamics."""
        if pre_id not in self.neurons or post_id not in self.neurons:
            raise ValueError("Both neurons must exist before creating synapse")
        
        # Create biophysical synapse
        synapse = BiophysicalSynapse(pre_id, post_id, synapse_type, weight, delay)
        
        # Store in connections dictionary with enhanced info
        conn_key = (pre_id, post_id)
        self.connections[conn_key] = {
            'pre_id': pre_id,
            'post_id': post_id,
            'weight': weight,
            'delay': delay,
            'synapse_type': synapse_type,
            'biophysical_synapse': synapse,  # Store the detailed synapse object
            'params': SynapseParameters.from_synapse_type(
                SynapseType.EXCITATORY if synapse_type == 'glutamate' else SynapseType.INHIBITORY
            )
        }
        
        self.connection_weights[conn_key] = weight
        self.connection_delays[conn_key] = delay
        self.connection_ages[conn_key] = 0.0
        self.connection_activity[conn_key] = 0.0
        
        return synapse.synapse_id

    def update_biophysical_synapses(self, dt: float):
        """Update all biophysical synapses - ADD TO YOUR step() METHOD."""
        for conn_key, conn_info in self.connections.items():
            if 'biophysical_synapse' in conn_info:
                synapse = conn_info['biophysical_synapse']
                
                # Update neurotransmitter dynamics
                synapse.update_neurotransmitter_dynamics(dt)
                
                # Update weight in connection info
                conn_info['weight'] = synapse.weight
                self.connection_weights[conn_key] = synapse.weight
                
                # Apply STDP if plasticity engine is attached
                if self.has_plasticity_engine:
                    synapse.apply_stdp(0.01)

    def deliver_biophysical_spike(self, source_id: int, target_id: int, t_deliver: float):
        """Enhanced spike delivery with biophysical synapses."""
        conn_key = (source_id, target_id)
        if conn_key in self.connections:
            conn_info = self.connections[conn_key]
            
            if 'biophysical_synapse' in conn_info:
                # Use biophysical synapse
                synapse = conn_info['biophysical_synapse']
                released, vesicles = synapse.process_spike(t_deliver)
                
                if released and target_id in self.neurons:
                    conductance = synapse.get_conductance()
                    target_neuron = self.neurons[target_id]
                    
                    # Enhanced spike reception with neurotransmitter type
                    if hasattr(target_neuron, 'receive_spike'):
                        target_neuron.receive_spike(synapse.weight, t_deliver, synapse.synapse_type)
                    
                    # Update plasticity traces
                    synapse.update_plasticity_traces(0.1, pre_spike=True)
            else:
                # Fall back to original method
                weight = conn_info['weight']
                self.pending_spikes[(target_id, t_deliver)].append((source_id, weight))

    
    def benchmark_performance(self, duration: float = 1000.0, dt: float = None) -> Dict[str, Any]:
        """
        Benchmark network performance with comprehensive metrics.
        
        Args:
            duration: Simulation duration in milliseconds
            dt: Time step size (uses self.dt if not specified)
            
        Returns:
            Dictionary with detailed performance metrics
        """
        import time
        
        # Use provided dt or fall back to instance dt
        if dt is None:
            dt = getattr(self, 'dt', 0.1)
        
        logger.info(f"Starting performance benchmark: {duration}ms @ {dt}ms timestep")
        
        # Store original dt and set benchmark dt
        original_dt = getattr(self, 'dt', 0.1)
        self.dt = dt
        
        # Reset network state
        self.reset()
        
        # Performance tracking
        start_time = time.perf_counter()
        memory_start = self._get_memory_usage()
        
        # Calculate steps
        steps = int(duration / dt)
        step_times = []
        
        # Run simulation with detailed timing
        for step in range(steps):
            step_start = time.perf_counter()
            
            # Execute one simulation step
            spikes = self.step(dt)
            
            step_time = time.perf_counter() - step_start
            step_times.append(step_time)
            
            # Sample performance every 100 steps
            if step % 100 == 0 and step > 0:
                progress = (step / steps) * 100
                avg_step_time = np.mean(step_times[-100:])
                logger.debug(f"Benchmark progress: {progress:.1f}% - Avg step time: {avg_step_time*1000:.3f}ms")
        
        # Calculate final metrics
        total_time = time.perf_counter() - start_time
        memory_end = self._get_memory_usage()
        memory_delta = memory_end - memory_start
        
        # Restore original dt
        self.dt = original_dt
        
        # Calculate comprehensive metrics
        steps_per_second = steps / total_time if total_time > 0 else 0
        neurons_per_second = (len(self.neurons) * steps) / total_time if total_time > 0 else 0
        synapses_per_second = (len(self.connections) * steps) / total_time if total_time > 0 else 0
        
        # Step timing statistics
        if step_times:
            mean_step_time = np.mean(step_times)
            std_step_time = np.std(step_times)
            min_step_time = np.min(step_times)
            max_step_time = np.max(step_times)
        else:
            mean_step_time = std_step_time = min_step_time = max_step_time = 0.0
        
        # Network statistics
        total_spikes = sum(self.stats.get('spikes_fired', 0) for _ in [1])
        
        # Compile results
        results = {
            'benchmark_parameters': {
                'duration_ms': duration,
                'timestep_ms': dt,
                'total_steps': steps,
                'neurons': len(self.neurons),
                'connections': len(self.connections)
            },
            'performance_metrics': {
                'total_time_seconds': total_time,
                'steps_per_second': steps_per_second,
                'neurons_per_second': neurons_per_second,
                'synapses_per_second': synapses_per_second,
                'realtime_factor': (duration / 1000.0) / total_time if total_time > 0 else 0,
                'efficiency_score': steps_per_second / len(self.neurons) if len(self.neurons) > 0 else 0
            },
            'timing_statistics': {
                'mean_step_time_ms': mean_step_time * 1000,
                'std_step_time_ms': std_step_time * 1000,
                'min_step_time_ms': min_step_time * 1000,
                'max_step_time_ms': max_step_time * 1000,
                'timing_variability': (std_step_time / mean_step_time) if mean_step_time > 0 else 0
            },
            'memory_usage': {
                'start_mb': memory_start,
                'end_mb': memory_end,
                'delta_mb': memory_delta,
                'memory_per_neuron_kb': (memory_delta * 1024) / len(self.neurons) if len(self.neurons) > 0 else 0
            },
            'network_activity': {
                'total_spikes': getattr(self.stats, 'spikes_fired', 0),
                'average_firing_rate_hz': (getattr(self.stats, 'spikes_fired', 0) / (duration / 1000.0)) / len(self.neurons) if len(self.neurons) > 0 and duration > 0 else 0,
                'spike_efficiency': getattr(self.stats, 'spikes_fired', 0) / steps if steps > 0 else 0
            },
            'hardware_info': {
                'acceleration_used': getattr(self, 'use_gpu', False) or getattr(self, 'use_jax', False) or getattr(self, 'use_numba', False),
                'parallel_processing': getattr(self, 'use_parallel', False),
                'backend': getattr(self, 'hardware_info', {}).get('recommended_backend', 'numpy')
            }
        }
        
        # Log summary
        logger.info(f"Benchmark completed: {steps_per_second:.1f} steps/sec, "
                    f"{neurons_per_second:.0f} neuron-ops/sec, "
                    f"{total_time:.3f}s total time")
        
        return results

    def _get_memory_usage(self) -> float:
        """Get current memory usage in MB."""
        try:
            import psutil
            import os
            process = psutil.Process(os.getpid())
            return process.memory_info().rss / 1024 / 1024  # Convert to MB
        except ImportError:
            # Fallback: estimate based on object counts
            neuron_memory = len(self.neurons) * 0.001  # ~1KB per neuron estimate
            synapse_memory = len(self.connections) * 0.0005  # ~0.5KB per synapse estimate
            return neuron_memory + synapse_memory            



class BiophysicalSynapse:
    """Advanced synaptic model with complete neurotransmitter dynamics."""
    
    def __init__(self, pre_neuron_id: int, post_neuron_id: int,
                 synapse_type: str = 'glutamate', weight: float = 1.0,
                 delay: float = 1.0):
        self.synapse_id = str(uuid.uuid4())
        self.pre_neuron_id = pre_neuron_id
        self.post_neuron_id = post_neuron_id
        self.synapse_type = synapse_type
        self.weight = weight
        self.delay = delay
        
        # Set synaptic parameters based on type
        self._set_synaptic_parameters()
        
        # Vesicle pool dynamics
        self.vesicle_pool = self.max_vesicles  # Available vesicles
        self.release_probability = self.base_p_release
        self.neurotransmitter_concentration = 0.0
        self.receptor_occupancy = 0.0
        
        # Short-term plasticity variables
        self.facilitation = 1.0
        self.depression = 1.0
        self.last_spike_time = -np.inf
        
        # Plasticity traces for STDP
        self.presynaptic_trace = 0.0
        self.postsynaptic_trace = 0.0
        self.eligibility_trace = 0.0
        
        # Statistics and history
        self.spike_times = deque(maxlen=1000)
        self.release_events = deque(maxlen=1000)
        self.total_releases = 0
        
    def _set_synaptic_parameters(self):
        """Set synapse-specific biophysical parameters."""
        if self.synapse_type == 'glutamate':
            # Excitatory synapse parameters
            self.max_vesicles = 100
            self.base_p_release = 0.3
            self.vesicle_refill_rate = 10.0  # vesicles/ms
            self.nt_decay_rate = 0.5         # 1/ms
            self.receptor_binding_rate = 2.0  # 1/(mM⋅ms)
            self.receptor_unbinding_rate = 1.0  # 1/ms
            self.max_conductance = 2.0       # nS
            
            # Short-term plasticity
            self.tau_facilitation = 200.0    # ms
            self.tau_depression = 800.0      # ms
            self.facilitation_increment = 0.1
            self.depression_factor = 0.8
            
        elif self.synapse_type == 'gaba':
            # Inhibitory synapse parameters
            self.max_vesicles = 80
            self.base_p_release = 0.4
            self.vesicle_refill_rate = 8.0
            self.nt_decay_rate = 0.3
            self.receptor_binding_rate = 3.0
            self.receptor_unbinding_rate = 0.8
            self.max_conductance = 3.0
            
            # Short-term plasticity
            self.tau_facilitation = 100.0
            self.tau_depression = 600.0
            self.facilitation_increment = 0.05
            self.depression_factor = 0.7
            
        else:  # Default excitatory
            self.max_vesicles = 90
            self.base_p_release = 0.25
            self.vesicle_refill_rate = 9.0
            self.nt_decay_rate = 0.4
            self.receptor_binding_rate = 2.5
            self.receptor_unbinding_rate = 1.2
            self.max_conductance = 2.5
            
            self.tau_facilitation = 150.0
            self.tau_depression = 700.0
            self.facilitation_increment = 0.08
            self.depression_factor = 0.75
    
    def process_spike(self, current_time: float) -> Tuple[bool, int]:
        """Process presynaptic spike with detailed vesicle dynamics."""
        # Store spike time
        self.spike_times.append(current_time)
        
        # Update short-term plasticity
        self._update_short_term_plasticity(current_time)
        
        # Determine vesicle release
        if self.vesicle_pool > 0:
            # Effective release probability with short-term plasticity
            effective_p_release = min(1.0, self.release_probability * 
                                    self.facilitation * self.depression)
            
            # Binomial vesicle release
            vesicles_released = np.random.binomial(int(self.vesicle_pool), 
                                                 effective_p_release)
            
            if vesicles_released > 0:
                # Update vesicle pool
                self.vesicle_pool = max(0, self.vesicle_pool - vesicles_released)
                
                # Calculate neurotransmitter concentration increase
                # Each vesicle contains ~5000 molecules in ~20 nm³ cleft
                nt_molecules = vesicles_released * 5000
                cleft_volume = 20e-21  # Liters
                concentration_increase = (nt_molecules / (6.022e23)) / cleft_volume * 1000  # mM
                
                self.neurotransmitter_concentration += concentration_increase
                self.release_events.append((current_time, vesicles_released))
                self.total_releases += vesicles_released
                
                self.last_spike_time = current_time
                
                return True, vesicles_released
        
        return False, 0
    
    def _update_short_term_plasticity(self, current_time: float):
        """Update facilitation and depression."""
        if self.last_spike_time > -np.inf:
            dt = current_time - self.last_spike_time
            
            # Decay facilitation and depression
            self.facilitation = 1.0 + (self.facilitation - 1.0) * np.exp(-dt / self.tau_facilitation)
            self.depression = 1.0 + (self.depression - 1.0) * np.exp(-dt / self.tau_depression)
        
        # Update with current spike
        self.facilitation += self.facilitation_increment
        self.depression *= self.depression_factor
        
        # Apply bounds
        self.facilitation = min(3.0, self.facilitation)  # Max 3x facilitation
        self.depression = max(0.1, self.depression)      # Min 10% of original
    
    def update_neurotransmitter_dynamics(self, dt: float):
        """Update neurotransmitter concentration and receptor binding."""
        # Neurotransmitter decay (reuptake and diffusion)
        self.neurotransmitter_concentration *= np.exp(-self.nt_decay_rate * dt)
        
        # Receptor binding kinetics
        binding_rate = (self.receptor_binding_rate * 
                       self.neurotransmitter_concentration * 
                       (1.0 - self.receptor_occupancy))
        unbinding_rate = self.receptor_unbinding_rate * self.receptor_occupancy
        
        self.receptor_occupancy += dt * (binding_rate - unbinding_rate)
        self.receptor_occupancy = np.clip(self.receptor_occupancy, 0.0, 1.0)
        
        # Vesicle pool replenishment
        refill_rate = self.vesicle_refill_rate * (1.0 - self.vesicle_pool / self.max_vesicles)
        self.vesicle_pool += dt * refill_rate
        self.vesicle_pool = min(self.max_vesicles, self.vesicle_pool)
    
    def get_conductance(self) -> float:
        """Calculate synaptic conductance based on receptor occupancy."""
        return self.max_conductance * self.receptor_occupancy * abs(self.weight)
    
    def update_plasticity_traces(self, dt: float, pre_spike: bool = False, 
                                post_spike: bool = False):
        """Update synaptic plasticity traces for STDP."""
        # Decay traces
        self.presynaptic_trace *= np.exp(-dt / 20.0)  # 20 ms time constant
        self.postsynaptic_trace *= np.exp(-dt / 20.0)
        self.eligibility_trace *= np.exp(-dt / 100.0)  # 100 ms time constant
        
        # Update traces on spikes
        if pre_spike:
            self.presynaptic_trace += 1.0
        if post_spike:
            self.postsynaptic_trace += 1.0
            
        # Eligibility trace for reward-modulated plasticity
        if pre_spike and self.postsynaptic_trace > 0.1:
            self.eligibility_trace += self.postsynaptic_trace
        elif post_spike and self.presynaptic_trace > 0.1:
            self.eligibility_trace += self.presynaptic_trace
    
    def apply_stdp(self, learning_rate: float = 0.01):
        """Apply spike-timing dependent plasticity."""
        delta_w = 0.0
        
        # LTP component (pre before post)
        if self.presynaptic_trace > 0.1 and self.postsynaptic_trace > 0.05:
            delta_w += learning_rate * self.presynaptic_trace * 0.01
        
        # LTD component (post before pre)  
        if self.postsynaptic_trace > 0.1 and self.presynaptic_trace > 0.05:
            delta_w -= learning_rate * self.postsynaptic_trace * 0.008
        
        # Apply weight change with bounds
        self.weight += delta_w
        
        # Physiological weight bounds
        if self.synapse_type == 'gaba':
            self.weight = np.clip(self.weight, -10.0, 0.0)
        else:
            self.weight = np.clip(self.weight, 0.01, 10.0)
    
    def get_detailed_statistics(self) -> Dict[str, Any]:
        """Get comprehensive synapse statistics."""
        recent_releases = len([t for t, _ in self.release_events 
                              if time.time() - t < 1.0])  # Last second
        
        return {
            'weight': self.weight,
            'vesicle_pool': self.vesicle_pool,
            'max_vesicles': self.max_vesicles,
            'release_probability': self.release_probability,
            'neurotransmitter_concentration': self.neurotransmitter_concentration,
            'receptor_occupancy': self.receptor_occupancy,
            'current_conductance': self.get_conductance(),
            'facilitation': self.facilitation,
            'depression': self.depression,
            'recent_releases': recent_releases,
            'total_releases': self.total_releases,
            'presynaptic_trace': self.presynaptic_trace,
            'postsynaptic_trace': self.postsynaptic_trace,
            'eligibility_trace': self.eligibility_trace
        }






class SynapticPruningModule:
    """
    Module for pruning synaptic connections based on various criteria.
    
    This implements synaptic pruning similar to the brain, removing weak or
    unnecessary connections while preserving important ones.
    """
    
    def __init__(self, 
                 importance_threshold: float = 0.2,
                 pruning_rate: float = 0.01,
                 pruning_period: float = 1000.0,  # ms
                 age_factor: float = 0.3,
                 activity_factor: float = 0.5,
                 weight_factor: float = 0.2):
        """
        Initialize synaptic pruning module.
        
        Args:
            importance_threshold: Threshold for pruning
            pruning_rate: Maximum fraction of connections to prune at once
            pruning_period: Time period between pruning operations
            age_factor: Weight of connection age in importance
            activity_factor: Weight of connection activity in importance
            weight_factor: Weight of connection weight in importance
        """
        self.importance_threshold = importance_threshold
        self.pruning_rate = pruning_rate
        self.pruning_period = pruning_period
        self.age_factor = age_factor
        self.activity_factor = activity_factor
        self.weight_factor = weight_factor
        
        # Keep track of last pruning time
        self.last_pruning_time = 0.0
        
        # Statistics
        self.total_pruned = 0
        self.pruning_events = 0
        
        # Reference to core (set later)
        self.core = None
    
    def initialize(self, core: NeuromorphicCore):
        """
        Initialize pruning module with core reference.
        
        Args:
            core: Neuromorphic core
        """
        self.core = core
    
    def _calculate_importance(self, conn_key: Tuple[int, int]) -> float:
        """
        Calculate importance score for a connection.
        
        Args:
            conn_key: Connection key (pre_id, post_id)
            
        Returns:
            Importance score [0, 1]
        """
        # Get connection properties
        weight = abs(self.core.connection_weights.get(conn_key, 0.0))
        age = self.core.connection_ages.get(conn_key, 0.0)
        activity = self.core.connection_activity.get(conn_key, 0.0)
        
        # Normalize factors
        max_weight = max(abs(w) for w in self.core.connection_weights.values()) if self.core.connection_weights else 1.0
        max_age = max(self.core.connection_ages.values()) if self.core.connection_ages else 1.0
        max_activity = max(self.core.connection_activity.values()) if self.core.connection_activity else 1.0
        
        weight_norm = weight / max_weight if max_weight > 0 else 0
        age_norm = 1.0 - (age / max_age if max_age > 0 else 0)  # Newer connections are more important
        activity_norm = activity / max_activity if max_activity > 0 else 0
        
        # Compute importance score
        importance = (self.weight_factor * weight_norm + 
                      self.age_factor * age_norm + 
                      self.activity_factor * activity_norm)
        
        return importance
    
    def prune_connections(self, current_time: float) -> int:
        """
        Prune connections based on importance scores.
        
        Args:
            current_time: Current simulation time
            
        Returns:
            Number of pruned connections
        """
        # Skip if not enough time has passed since last pruning
        if current_time - self.last_pruning_time < self.pruning_period:
            return 0
            
        if self.core is None:
            logger.warning("Cannot prune connections: core not initialized")
            return 0
            
        # Calculate importance for all connections
        importance_scores = {}
        for conn_key in self.core.connections.keys():
            importance_scores[conn_key] = self._calculate_importance(conn_key)
        
        # Identify connections to prune
        prune_candidates = [conn_key for conn_key, score in importance_scores.items() 
                           if score < self.importance_threshold]
        
        # Limit number of connections to prune based on pruning rate
        max_to_prune = int(self.pruning_rate * len(self.core.connections))
        if len(prune_candidates) > max_to_prune:
            # Sort by importance and take the least important ones
            prune_candidates.sort(key=lambda conn_key: importance_scores[conn_key])
            prune_candidates = prune_candidates[:max_to_prune]
        
        # Prune connections
        for conn_key in prune_candidates:
            self.core.remove_connection(*conn_key)
        
        # Update statistics
        num_pruned = len(prune_candidates)
        self.total_pruned += num_pruned
        self.pruning_events += 1
        
        # Update last pruning time
        self.last_pruning_time = current_time
        
        logger.info(f"Pruned {num_pruned} connections")
        return num_pruned
    
    def step(self, dt: float, current_time: float) -> int:
        """
        Update pruning module for one time step.
        
        Args:
            dt: Time step size (ms)
            current_time: Current simulation time (ms)
            
        Returns:
            Number of pruned connections
        """
        return self.prune_connections(current_time)
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Get pruning statistics.
        
        Returns:
            Dictionary with statistics
        """
        return {
            'total_pruned': self.total_pruned,
            'pruning_events': self.pruning_events,
            'pruning_rate': self.pruning_rate,
            'importance_threshold': self.importance_threshold
        }


class NeuroplasticityEngine:
    """
    Engine for synaptic plasticity in the network.
    
    This implements multiple forms of plasticity including spike-timing-dependent
    plasticity (STDP), homeostatic plasticity, and structural plasticity.
    """
    
    def __init__(self, 
                learning_rate: float = 0.01,
                stdp_window: float = 20.0,  # ms
                a_plus: float = 1.0,
                a_minus: float = 1.0,
                tau_plus: float = 20.0,
                tau_minus: float = 20.0,
                homeostatic_plasticity: bool = True,
                target_rate: float = 10.0,  # Hz
                structural_plasticity: bool = True):
        """
        Initialize neuroplasticity engine.
        
        Args:
            learning_rate: Global learning rate
            stdp_window: STDP time window (ms)
            a_plus: STDP potentiation factor
            a_minus: STDP depression factor
            tau_plus: STDP potentiation time constant
            tau_minus: STDP depression time constant
            homeostatic_plasticity: Whether to enable homeostatic plasticity
            target_rate: Target firing rate for homeostatic plasticity
            structural_plasticity: Whether to enable structural plasticity
        """
        self.learning_rate = learning_rate
        self.stdp_window = stdp_window
        self.a_plus = a_plus
        self.a_minus = a_minus
        self.tau_plus = tau_plus
        self.tau_minus = tau_minus
        self.homeostatic_plasticity = homeostatic_plasticity
        self.target_rate = target_rate
        self.structural_plasticity = structural_plasticity
        
        # STDP traces for each neuron
        self.pre_trace = {}
        self.post_trace = {}
        
        # Reference to core (set later)
        self.core = None
        
        # Statistics
        self.weight_changes = 0
        self.new_connections = 0
        
        # Adaptive learning rate
        self.adaptive_learning_rate = self.learning_rate
    
    def initialize(self, core: NeuromorphicCore):
        """
        Initialize plasticity engine with core reference.
        
        Args:
            core: Neuromorphic core
        """
        self.core = core
        
        # Initialize traces for all neurons
        for neuron_id in range(core.neuron_count):
            self.pre_trace[neuron_id] = 0.0
            self.post_trace[neuron_id] = 0.0
    
    def on_spike(self, neuron_id: int, t: float):
        """
        Handle a spike event.
        
        Args:
            neuron_id: Neuron ID
            t: Spike time
        """
        if neuron_id not in self.pre_trace:
            self.pre_trace[neuron_id] = 0.0
            self.post_trace[neuron_id] = 0.0
            
        # For presynaptic neurons, update traces and weights of outgoing connections
        # For LTP: When a presynaptic neuron fires, strengthen connections where postsynaptic neuron fired recently
        for post_id in range(self.core.neuron_count):
            conn_key = (neuron_id, post_id)
            if conn_key in self.core.connections:
                # LTP: pre->post spike order (positive time difference)
                dw = self.a_plus * self.post_trace.get(post_id, 0.0)
                self._update_weight(conn_key, dw)
        
        # For postsynaptic neurons, update traces and weights of incoming connections
        # For LTD: When a postsynaptic neuron fires, weaken connections where presynaptic neuron fired recently
        for pre_id in range(self.core.neuron_count):
            conn_key = (pre_id, neuron_id)
            if conn_key in self.core.connections:
                # LTD: post->pre spike order (negative time difference)
                dw = -self.a_minus * self.pre_trace.get(pre_id, 0.0)
                self._update_weight(conn_key, dw)
        
        # Update traces for this neuron
        self.pre_trace[neuron_id] = 1.0  # Reset to peak
        self.post_trace[neuron_id] = 1.0  # Reset to peak
    
    def _update_weight(self, conn_key: Tuple[int, int], dw: float):
        """
        Update weight of a connection.
        
        Args:
            conn_key: Connection key (pre_id, post_id)
            dw: Weight change
        """
        if self.core is None or conn_key not in self.core.connections:
            return
            
        # Apply learning rate
        dw *= self.adaptive_learning_rate
        
        # Add to current weight
        current_weight = self.core.connection_weights[conn_key]
        new_weight = current_weight + dw
        
        # Apply bounds based on synapse type
        synapse_type = self.core.connections[conn_key].get('synapse_type', SynapseType.EXCITATORY)
        
        if synapse_type == SynapseType.INHIBITORY:
            # Inhibitory weights are negative
            new_weight = min(new_weight, 0.0)  # Ensure it stays negative
            new_weight = max(new_weight, -10.0)  # Lower bound
        else:
            # Excitatory weights are positive
            new_weight = max(new_weight, 0.0)  # Ensure it stays positive
            new_weight = min(new_weight, 10.0)  # Upper bound
        
        # Update weight
        if new_weight != current_weight:
            self.core.connection_weights[conn_key] = new_weight
            self.core.connections[conn_key]['weight'] = new_weight
            self.weight_changes += 1
    
    def step(self, dt: float):
        """
        Update plasticity engine for one time step.
        
        Args:
            dt: Time step size (ms)
        """
        if self.core is None:
            return
            
        # Decay STDP traces
        for neuron_id in self.pre_trace:
            # Exponential decay
            self.pre_trace[neuron_id] *= np.exp(-dt / self.tau_plus)
            self.post_trace[neuron_id] *= np.exp(-dt / self.tau_minus)
        
        # Apply homeostatic plasticity if enabled
        if self.homeostatic_plasticity:
            self._apply_homeostatic_plasticity(dt)
            
        # Apply structural plasticity if enabled
        if self.structural_plasticity:
            self._apply_structural_plasticity(dt)
            
        # Update adaptive learning rate
        self._update_adaptive_learning_rate(dt)
    
    def _apply_homeostatic_plasticity(self, dt: float):
        """
        Apply homeostatic plasticity to maintain target firing rates.
        
        Args:
            dt: Time step size (ms)
        """
        # Get current firing rates
        firing_rates = self.core.get_spike_rate(window_ms=1000.0)  # 1 second window
        
        for neuron_id, rate in enumerate(firing_rates):
            if neuron_id >= self.core.neuron_count:
                continue
                
            # Skip neurons without connections
            if not any((neuron_id, post_id) in self.core.connections for post_id in range(self.core.neuron_count)):
                continue
                
            # Calculate rate difference from target
            rate_diff = self.target_rate - rate
            
            # Scale factor for weight changes
            scale = 0.0001 * dt * rate_diff
            
            # Adjust outgoing weights
            for post_id in range(self.core.neuron_count):
                conn_key = (neuron_id, post_id)
                if conn_key in self.core.connections:
                    current_weight = self.core.connection_weights[conn_key]
                    
                    # Increase weights if firing below target, decrease if above
                    dw = scale * current_weight
                    self._update_weight(conn_key, dw)
    
    def _apply_structural_plasticity(self, dt: float):
        """
        Apply structural plasticity to create and remove connections.
        
        Args:
            dt: Time step size (ms)
        """
        # Only apply occasionally (every 1000 ms)
        if self.core.current_time % 1000.0 > dt:
            return
            
        # Probability of creating a new connection
        p_create = 0.001 * dt
        
        # Get neurons with high activity
        active_neurons = []
        for neuron_id, activity in self.core.activity_history.items():
            if activity > 0.5:  # Threshold for "active"
                active_neurons.append(neuron_id)
                
        # Limit to a reasonable number
        if len(active_neurons) > 10:
            np.random.shuffle(active_neurons)
            active_neurons = active_neurons[:10]
            
        # Try to create connections between active neurons
        for i, pre_id in enumerate(active_neurons):
            for post_id in active_neurons[i+1:]:
                # Skip if connection already exists
                if (pre_id, post_id) in self.core.connections:
                    continue
                    
                # Create with small probability
                if np.random.random() < p_create:
                    # Determine synapse type based on neuron types
                    pre_type = self.core.neuron_types.get(pre_id, NeuronType.EXCITATORY)
                    
                    if pre_type == NeuronType.INHIBITORY:
                        weight = -np.random.uniform(0.1, 0.5)
                        synapse_type = SynapseType.INHIBITORY
                    else:
                        weight = np.random.uniform(0.1, 0.5)
                        synapse_type = SynapseType.EXCITATORY
                        
                    # Create new connection
                    conn_key = (pre_id, post_id)
                    self.core.connections[conn_key] = {
                        'pre_id': pre_id,
                        'post_id': post_id,
                        'weight': weight,
                        'delay': np.random.uniform(1.0, 5.0),
                        'synapse_type': synapse_type,
                        'params': SynapseParameters()
                    }
                    
                    self.core.connection_weights[conn_key] = weight
                    self.core.connection_delays[conn_key] = np.random.uniform(1.0, 5.0)
                    self.core.connection_ages[conn_key] = 0.0
                    self.core.connection_activity[conn_key] = 0.0
                    
                    self.new_connections += 1
    
    def _update_adaptive_learning_rate(self, dt: float):
        """
        Update adaptive learning rate based on network activity.
        
        Args:
            dt: Time step size (ms)
        """
        # Get overall network activity
        overall_activity = self.core.get_average_activity()
        
        # Adjust learning rate based on activity
        # Higher activity -> lower learning rate to prevent instability
        self.adaptive_learning_rate = self.learning_rate / (1.0 + overall_activity)
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Get plasticity statistics.
        
        Returns:
            Dictionary with statistics
        """
        return {
            'weight_changes': self.weight_changes,
            'new_connections': self.new_connections,
            'learning_rate': self.learning_rate,
            'adaptive_learning_rate': self.adaptive_learning_rate
        }


# Network creation utility functions

def create_balanced_network(core: NeuromorphicCore, 
                          dimensions: Tuple[int, int, int] = (10, 10, 10),
                          num_excitatory: int = 800,
                          num_inhibitory: int = 200,
                          connectivity_prob: float = 0.1,
                          excitatory_weight: float = 0.1,
                          inhibitory_weight: float = -0.5) -> Dict[str, Any]:
    """
    Create a balanced network of excitatory and inhibitory neurons.
    
    Args:
        core: Neuromorphic core
        dimensions: Network dimensions
        num_excitatory: Number of excitatory neurons
        num_inhibitory: Number of inhibitory neurons
        connectivity_prob: Connection probability
        excitatory_weight: Weight for excitatory connections
        inhibitory_weight: Weight for inhibitory connections
        
    Returns:
        Dictionary with network information
    """
    # Create excitatory and inhibitory neurons
    excitatory_ids = core.create_neurons(
        count=num_excitatory,
        model=NeuronModel.LIF,
        neuron_type=NeuronType.EXCITATORY
    )
    
    inhibitory_ids = core.create_neurons(
        count=num_inhibitory,
        model=NeuronModel.LIF,
        neuron_type=NeuronType.INHIBITORY
    )
    
    # Create connections between neurons
    # Excitatory to all
    e_to_e = core.connect_neurons(
        pre_ids=excitatory_ids,
        post_ids=excitatory_ids,
        connection_prob=connectivity_prob,
        weight_range=(excitatory_weight * 0.9, excitatory_weight * 1.1),
        delay_range=(1.0, 2.0)
    )
    
    e_to_i = core.connect_neurons(
        pre_ids=excitatory_ids,
        post_ids=inhibitory_ids,
        connection_prob=connectivity_prob,
        weight_range=(excitatory_weight * 0.9, excitatory_weight * 1.1),
        delay_range=(1.0, 2.0)
    )
    
    # Inhibitory to all
    i_to_e = core.connect_neurons(
        pre_ids=inhibitory_ids,
        post_ids=excitatory_ids,
        connection_prob=connectivity_prob,
        weight_range=(inhibitory_weight * 0.9, inhibitory_weight * 1.1),
        delay_range=(1.0, 2.0)
    )
    
    i_to_i = core.connect_neurons(
        pre_ids=inhibitory_ids,
        post_ids=inhibitory_ids,
        connection_prob=connectivity_prob,
        weight_range=(inhibitory_weight * 0.9, inhibitory_weight * 1.1),
        delay_range=(1.0, 2.0)
    )
    
    # Return network information
    return {
        'excitatory_ids': excitatory_ids,
        'inhibitory_ids': inhibitory_ids,
        'connections': {
            'e_to_e': e_to_e,
            'e_to_i': e_to_i,
            'i_to_e': i_to_e,
            'i_to_i': i_to_i
        },
        'total_neurons': len(excitatory_ids) + len(inhibitory_ids),
        'total_connections': len(e_to_e) + len(e_to_i) + len(i_to_e) + len(i_to_i)
    }


def create_feedforward_network(core: NeuromorphicCore,
                             dimensions: Tuple[int, int, int] = (10, 10, 10),
                             layer_sizes: List[int] = [100, 80, 60, 40, 20],
                             feedforward_prob: float = 0.1,
                             feedback_prob: float = 0.01,
                             weight_scale: float = 1.0) -> Dict[str, Any]:
    """
    Create a feedforward network with optional feedback connections.
    
    Args:
        core: Neuromorphic core
        dimensions: Network dimensions
        layer_sizes: Size of each layer
        feedforward_prob: Probability of feedforward connections
        feedback_prob: Probability of feedback connections
        weight_scale: Scale factor for connection weights
        
    Returns:
        Dictionary with network information
    """
    # Create neurons for each layer
    layers = []
    all_neurons = []
    
    for i, size in enumerate(layer_sizes):
        # Create neurons
        layer_ids = core.create_neurons(
            count=size,
            model=NeuronModel.LIF,
            neuron_type=NeuronType.EXCITATORY if i < len(layer_sizes) - 1 else NeuronType.ADAPTIVE
        )
        
        layers.append(layer_ids)
        all_neurons.extend(layer_ids)
    
    # Create feedforward connections
    feedforward_connections = []
    
    for i in range(len(layers) - 1):
        pre_ids = layers[i]
        post_ids = layers[i + 1]
        
        # Connect pre to post
        connections = core.connect_neurons(
            pre_ids=pre_ids,
            post_ids=post_ids,
            connection_prob=feedforward_prob,
            weight_range=(0.8 * weight_scale, 1.2 * weight_scale),
            delay_range=(1.0, 2.0)
        )
        
        feedforward_connections.extend(connections)
    
    # Create feedback connections if enabled
    feedback_connections = []
    
    if feedback_prob > 0:
        for i in range(1, len(layers)):
            pre_ids = layers[i]
            post_ids = layers[i - 1]
            
            # Connect pre to post (feedback)
            connections = core.connect_neurons(
                pre_ids=pre_ids,
                post_ids=post_ids,
                connection_prob=feedback_prob,
                weight_range=(0.2 * weight_scale, 0.5 * weight_scale),
                delay_range=(2.0, 4.0)
            )
            
            feedback_connections.extend(connections)
    
    # Return network information
    return {
        'layers': layers,
        'feedforward_connections': feedforward_connections,
        'feedback_connections': feedback_connections,
        'total_neurons': sum(len(layer) for layer in layers),
        'total_connections': len(feedforward_connections) + len(feedback_connections)
    }


def create_recurrent_network(core: NeuromorphicCore,
                           dimensions: Tuple[int, int, int] = (10, 10, 10),
                           n_excitatory: int = 200,
                           n_inhibitory: int = 50,
                           recurrent_connectivity: float = 0.15,
                           input_size: int = 20,
                           output_size: int = 10,
                           recurrent_weight_scale: float = 1.0) -> Dict[str, Any]:
    """
    Create a recurrent neural network with input and output layers.
    
    Args:
        core: Neuromorphic core
        dimensions: Network dimensions
        n_excitatory: Number of excitatory neurons in recurrent layer
        n_inhibitory: Number of inhibitory neurons in recurrent layer
        recurrent_connectivity: Connection probability within recurrent layer
        input_size: Size of input layer
        output_size: Size of output layer
        recurrent_weight_scale: Scale factor for recurrent connection weights
        
    Returns:
        Dictionary with network information
    """
    # Create input layer
    input_ids = core.create_neurons(
        count=input_size,
        model=NeuronModel.LIF,
        neuron_type=NeuronType.EXCITATORY
    )
    
    # Create recurrent excitatory layer
    recurrent_exc_ids = core.create_neurons(
        count=n_excitatory,
        model=NeuronModel.LIF,
        neuron_type=NeuronType.EXCITATORY
    )
    
    # Create recurrent inhibitory layer
    recurrent_inh_ids = core.create_neurons(
        count=n_inhibitory,
        model=NeuronModel.LIF,
        neuron_type=NeuronType.INHIBITORY
    )
    
    # Create output layer
    output_ids = core.create_neurons(
        count=output_size,
        model=NeuronModel.LIF,
        neuron_type=NeuronType.EXCITATORY
    )
    
    # Connect input to recurrent excitatory
    input_connections = core.connect_neurons(
        pre_ids=input_ids,
        post_ids=recurrent_exc_ids,
        connection_prob=0.2,
        weight_range=(0.8, 1.2),
        delay_range=(1.0, 2.0)
    )
    
    # Connect recurrent excitatory to output
    output_connections = core.connect_neurons(
        pre_ids=recurrent_exc_ids,
        post_ids=output_ids,
        connection_prob=0.2,
        weight_range=(0.8, 1.2),
        delay_range=(1.0, 2.0)
    )
    
    # Recurrent connections
    recurrent_connections = []
    
    # Excitatory to excitatory
    ee_conns = core.connect_neurons(
        pre_ids=recurrent_exc_ids,
        post_ids=recurrent_exc_ids,
        connection_prob=recurrent_connectivity,
        weight_range=(0.1 * recurrent_weight_scale, 0.3 * recurrent_weight_scale),
        delay_range=(1.0, 2.0)
    )
    recurrent_connections.extend(ee_conns)
    
    # Excitatory to inhibitory
    ei_conns = core.connect_neurons(
        pre_ids=recurrent_exc_ids,
        post_ids=recurrent_inh_ids,
        connection_prob=recurrent_connectivity * 1.2,  # Higher connectivity to inhibitory
        weight_range=(0.2 * recurrent_weight_scale, 0.4 * recurrent_weight_scale),
        delay_range=(1.0, 1.5)
    )
    recurrent_connections.extend(ei_conns)
    
    # Inhibitory to excitatory
    ie_conns = core.connect_neurons(
        pre_ids=recurrent_inh_ids,
        post_ids=recurrent_exc_ids,
        connection_prob=recurrent_connectivity * 1.2,  # Higher connectivity from inhibitory
        weight_range=(-0.6 * recurrent_weight_scale, -0.4 * recurrent_weight_scale),
        delay_range=(1.0, 1.5)
    )
    recurrent_connections.extend(ie_conns)
    
    # Inhibitory to inhibitory
    ii_conns = core.connect_neurons(
        pre_ids=recurrent_inh_ids,
        post_ids=recurrent_inh_ids,
        connection_prob=recurrent_connectivity * 0.8,  # Lower inhibitory to inhibitory
        weight_range=(-0.3 * recurrent_weight_scale, -0.1 * recurrent_weight_scale),
        delay_range=(1.0, 1.5)
    )
    recurrent_connections.extend(ii_conns)
    
    # Return network information
    return {
        'input_ids': input_ids,
        'recurrent_exc_ids': recurrent_exc_ids,
        'recurrent_inh_ids': recurrent_inh_ids,
        'output_ids': output_ids,
        'input_connections': input_connections,
        'output_connections': output_connections,
        'recurrent_connections': recurrent_connections,
        'total_neurons': len(input_ids) + len(recurrent_exc_ids) + len(recurrent_inh_ids) + len(output_ids),
        'total_connections': len(input_connections) + len(output_connections) + len(recurrent_connections)
    }


def create_cortical_microcircuit(core: NeuromorphicCore,
                               dimensions: Tuple[int, int, int] = (10, 10, 10),
                               scale_factor: float = 1.0,
                               layer_sizes: Dict[str, int] = None,
                               excitatory_weight_scale: float = 1.0,
                               inhibitory_weight_scale: float = 1.0) -> Dict[str, Any]:
    """
    Create a cortical microcircuit with layered structure and biologically-inspired connectivity.
    
    This implements a simplified version of the Potjans-Diesmann model of a cortical microcircuit
    with layers 2/3, 4, 5, and 6, each with excitatory and inhibitory populations.
    
    Args:
        core: Neuromorphic core
        dimensions: Network dimensions
        scale_factor: Scale factor for all layer sizes
        layer_sizes: Dictionary with custom layer sizes
        excitatory_weight_scale: Scale factor for excitatory connection weights
        inhibitory_weight_scale: Scale factor for inhibitory connection weights
        
    Returns:
        Dictionary with network information
    """
    # Default layer sizes (roughly based on Potjans & Diesmann 2014)
    default_layer_sizes = {
        'L23_exc': 100,
        'L23_inh': 25,
        'L4_exc': 100,
        'L4_inh': 25,
        'L5_exc': 65,
        'L5_inh': 15,
        'L6_exc': 80,
        'L6_inh': 20
    }
    
    # Use custom layer sizes if provided
    if layer_sizes is None:
        layer_sizes = default_layer_sizes
    else:
        # Fill in missing layers with defaults
        for layer, size in default_layer_sizes.items():
            if layer not in layer_sizes:
                layer_sizes[layer] = size
    
    # Apply scale factor
    for layer in layer_sizes:
        layer_sizes[layer] = int(layer_sizes[layer] * scale_factor)
    
    # Create neurons for each layer
    layers = {}
    
    for layer, size in layer_sizes.items():
        # Determine neuron type based on layer name
        if '_exc' in layer:
            neuron_type = NeuronType.EXCITATORY
        else:
            neuron_type = NeuronType.INHIBITORY
        
        # Create neurons
        layer_ids = core.create_neurons(
            count=size,
            model=NeuronModel.LIF,
            neuron_type=neuron_type
        )
        
        layers[layer] = layer_ids
    
    # Connection probability matrix (simplified from Potjans & Diesmann 2014)
    # Rows: pre-synaptic, Columns: post-synaptic
    # Order: L23_exc, L23_inh, L4_exc, L4_inh, L5_exc, L5_inh, L6_exc, L6_inh
    conn_probabilities = np.array([
        [0.10, 0.12, 0.05, 0.04, 0.08, 0.03, 0.03, 0.01],  # L23_exc
        [0.12, 0.15, 0.03, 0.05, 0.06, 0.02, 0.02, 0.01],  # L23_inh
        [0.08, 0.06, 0.10, 0.12, 0.05, 0.02, 0.04, 0.01],  # L4_exc
        [0.06, 0.05, 0.12, 0.15, 0.03, 0.03, 0.02, 0.02],  # L4_inh
        [0.05, 0.04, 0.05, 0.03, 0.12, 0.12, 0.06, 0.02],  # L5_exc
        [0.04, 0.04, 0.01, 0.02, 0.12, 0.15, 0.03, 0.03],  # L5_inh
        [0.03, 0.01, 0.05, 0.02, 0.06, 0.03, 0.10, 0.12],  # L6_exc
        [0.01, 0.01, 0.01, 0.02, 0.03, 0.04, 0.12, 0.15]   # L6_inh
    ])
    
    # Weight matrix (simplified, based on excitatory/inhibitory)
    weight_matrix = np.zeros_like(conn_probabilities)
    
    # Set weights based on pre-synaptic type
    for i, pre_layer in enumerate(layers.keys()):
        for j, post_layer in enumerate(layers.keys()):
            if '_exc' in pre_layer:
                weight_matrix[i, j] = 0.15 * excitatory_weight_scale
            else:
                weight_matrix[i, j] = -0.6 * inhibitory_weight_scale
    
    # Create connections
    connections = {}
    layer_names = list(layers.keys())
    
    for i, pre_layer in enumerate(layer_names):
        for j, post_layer in enumerate(layer_names):
            # Skip if connection probability is zero
            if conn_probabilities[i, j] <= 0:
                continue
                
            # Get neuron IDs
            pre_ids = layers[pre_layer]
            post_ids = layers[post_layer]
            
            # Determine weight range
            weight = weight_matrix[i, j]
            weight_range = (weight * 0.9, weight * 1.1)
            
            # Create connections
            conns = core.connect_neurons(
                pre_ids=pre_ids,
                post_ids=post_ids,
                connection_prob=conn_probabilities[i, j],
                weight_range=weight_range,
                delay_range=(1.0, 2.0)
            )
            
            connections[f"{pre_layer}_to_{post_layer}"] = conns
    
    # Return network information
    return {
        'layers': layers,
        'connections': connections,
        'total_neurons': sum(len(ids) for ids in layers.values()),
        'total_connections': sum(len(conns) for conns in connections.values())
    }


def create_neuromodulatory_network(core: NeuromorphicCore,
                                 dimensions: Tuple[int, int, int] = (10, 10, 10),
                                 n_dopamine: int = 10,
                                 n_serotonin: int = 10,
                                 n_norepinephrine: int = 10,
                                 n_acetylcholine: int = 10,
                                 n_target: int = 100,
                                 connectivity_prob: float = 0.3,
                                 weight_scale: float = 0.5) -> Dict[str, Any]:
    """
    Create a network with neuromodulatory neurons.
    
    Args:
        core: Neuromorphic core
        dimensions: Network dimensions
        n_dopamine: Number of dopaminergic neurons
        n_serotonin: Number of serotonergic neurons
        n_norepinephrine: Number of noradrenergic neurons
        n_acetylcholine: Number of cholinergic neurons
        n_target: Number of target neurons
        connectivity_prob: Connection probability
        weight_scale: Scale factor for connection weights
        
    Returns:
        Dictionary with network information
    """
    # Create neuromodulatory neurons
    dopamine_ids = core.create_neurons(
        count=n_dopamine,
        model=NeuronModel.IZHIKEVICH,
        neuron_type=NeuronType.MODULATORY
    )
    
    serotonin_ids = core.create_neurons(
        count=n_serotonin,
        model=NeuronModel.IZHIKEVICH,
        neuron_type=NeuronType.MODULATORY
    )
    
    norepinephrine_ids = core.create_neurons(
        count=n_norepinephrine,
        model=NeuronModel.IZHIKEVICH,
        neuron_type=NeuronType.MODULATORY
    )
    
    acetylcholine_ids = core.create_neurons(
        count=n_acetylcholine,
        model=NeuronModel.IZHIKEVICH,
        neuron_type=NeuronType.MODULATORY
    )
    
    # Create target neurons
    target_ids = core.create_neurons(
        count=n_target,
        model=NeuronModel.LIF,
        neuron_type=NeuronType.EXCITATORY
    )
    
    # Connect neuromodulatory neurons to targets
    dopamine_connections = core.connect_neurons(
        pre_ids=dopamine_ids,
        post_ids=target_ids,
        connection_prob=connectivity_prob,
        weight_range=(0.1 * weight_scale, 0.3 * weight_scale),
        synapse_type=SynapseType.MODULATORY
    )
    
    serotonin_connections = core.connect_neurons(
        pre_ids=serotonin_ids,
        post_ids=target_ids,
        connection_prob=connectivity_prob,
        weight_range=(0.1 * weight_scale, 0.3 * weight_scale),
        synapse_type=SynapseType.MODULATORY
    )
    
    norepinephrine_connections = core.connect_neurons(
        pre_ids=norepinephrine_ids,
        post_ids=target_ids,
        connection_prob=connectivity_prob,
        weight_range=(0.1 * weight_scale, 0.3 * weight_scale),
        synapse_type=SynapseType.MODULATORY
    )
    
    acetylcholine_connections = core.connect_neurons(
        pre_ids=acetylcholine_ids,
        post_ids=target_ids,
        connection_prob=connectivity_prob,
        weight_range=(0.1 * weight_scale, 0.3 * weight_scale),
        synapse_type=SynapseType.MODULATORY
    )
    
    # Link neuromodulatory neurons to the neuromodulation system
    core.neuromodulation.set_input(NeuromodulatorType.DOPAMINE, 0.1)
    core.neuromodulation.set_input(NeuromodulatorType.SEROTONIN, 0.1)
    core.neuromodulation.set_input(NeuromodulatorType.NOREPINEPHRINE, 0.1)
    core.neuromodulation.set_input(NeuromodulatorType.ACETYLCHOLINE, 0.1)
    
    # Return network information
    return {
        'dopamine_ids': dopamine_ids,
        'serotonin_ids': serotonin_ids,
        'norepinephrine_ids': norepinephrine_ids,
        'acetylcholine_ids': acetylcholine_ids,
        'target_ids': target_ids,
        'connections': {
            'dopamine': dopamine_connections,
            'serotonin': serotonin_connections,
            'norepinephrine': norepinephrine_connections,
            'acetylcholine': acetylcholine_connections
        },
        'total_neurons': n_dopamine + n_serotonin + n_norepinephrine + n_acetylcholine + n_target,
        'total_connections': len(dopamine_connections) + len(serotonin_connections) + 
                            len(norepinephrine_connections) + len(acetylcholine_connections)
    }


def initialize_neuromorphic_core(config=None):
    """
    Initialize neuromorphic core.
    
    Args:
        config: Configuration dictionary
        
    Returns:
        Initialized NeuromorphicCore
    """
    logger.info("Initializing Neuromorphic Core")
    
    # Default configuration
    default_config = {
        'dimensions': (10, 10, 10),
        'default_neuron_model': NeuronModel.LIF,
        'default_conn_prob': 0.1,
        'neuron_distribution': {
            NeuronType.EXCITATORY: 0.8,
            NeuronType.INHIBITORY: 0.1,
            NeuronType.ADAPTIVE: 0.05,
            NeuronType.MODULATORY: 0.05
        },
        'use_hardware_acceleration': True,
        'random_seed': None
    }
    
    # Update with provided config
    if config is not None:
        if 'neuromorphic_core' in config:
            custom_config = config['neuromorphic_core']
            default_config.update(custom_config)
        else:
            default_config.update(config)
    
    # Initialize core with config
    try:
        core = NeuromorphicCore(
            dimensions=default_config['dimensions'],
            neuron_distribution=default_config['neuron_distribution'],
            config=default_config
        )
        
        logger.info("Neuromorphic Core initialized successfully")
        return core
    except Exception as e:
        logger.error(f"Failed to initialize Neuromorphic Core: {str(e)}")
        raise



 # ===========================================================================================
# 9. EXAMPLE USAGE WITH ENHANCED FEATURES
# ===========================================================================================

# ADD this example function to demonstrate the enhanced features:

def create_enhanced_demo_network():
    """Create a demonstration network with all enhanced features."""
    
    # Initialize with hardware acceleration
    config = {
        'use_hardware_acceleration': True,
        'use_biophysical_synapses': True,
        'enable_calcium_dynamics': True,
        'dimensions': (20, 20, 20),
        'random_seed': 42
    }
    
    core = initialize_neuromorphic_core(config)
    
    # Create enhanced network
    network_info = create_balanced_network(
        core, 
        dimensions=(20, 20, 20),
        num_excitatory=160,
        num_inhibitory=40, 
        connectivity_prob=0.15,
        use_biophysical_synapses=True
    )
    
    # Add plasticity and pruning
    plasticity_engine = NeuroplasticityEngine(learning_rate=0.01)
    pruning_module = SynapticPruningModule(importance_threshold=0.2)
    
    core.attach_plasticity_engine(plasticity_engine)
    core.attach_synaptic_pruning(pruning_module)
    
    # Run benchmark
    benchmark_results = core.benchmark_performance(duration=1000.0)
    
    logger.info("Enhanced demo network created successfully")
    logger.info(f"Performance: {benchmark_results['benchmark_results']['steps_per_second']:.1f} steps/sec")
    
    return core, network_info, benchmark_results

# ===========================================================================================
# 10. TESTING AND VALIDATION
# ===========================================================================================

def test_enhanced_features():
    """Test all enhanced features."""
    
    logger.info("Testing enhanced neuromorphic core features...")
    
    # Test 1: Hardware detection
    hardware_info = detect_hardware_capabilities()
    assert 'detected_backends' in hardware_info
    logger.info("✓ Hardware detection working")
    
    # Test 2: Enhanced neuron creation
    core = initialize_neuromorphic_core()
    neuron_ids = core.create_neurons(10, NeuronModel.LIF, NeuronType.EXCITATORY)
    assert len(neuron_ids) == 10
    logger.info("✓ Enhanced neuron creation working")
    
    # Test 3: Biophysical synapses
    synapse_id = core.add_biophysical_synapse(0, 1, 'glutamate', 1.0, 1.5)
    assert synapse_id is not None
    logger.info("✓ Biophysical synapses working")
    
    # Test 4: Calcium dynamics
    neuron = core.neurons[0]
    assert hasattr(neuron, 'calcium_concentration')
    assert hasattr(neuron, 'update_calcium_dynamics')
    logger.info("✓ Calcium dynamics working")
    
    # Test 5: Hardware acceleration
    spikes = core.step(0.1)
    assert spikes is not None
    logger.info("✓ Hardware-accelerated simulation working")
    
    # Test 6: Performance monitoring
    perf_stats = core.get_performance_stats()
    assert 'step_performance' in perf_stats
    logger.info("✓ Performance monitoring working")
    
    logger.info("All enhanced features tested successfully! ✅")
    
    return True   