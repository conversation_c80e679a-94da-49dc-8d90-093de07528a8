#!/usr/bin/env python3
"""
ULTRA Biological Timing Circuits - Comprehensive Test Example

This script provides a complete test suite and example usage for the ULTRA
Biological Timing Circuits module. It demonstrates all key functionality
including oscillator creation, coupling mechanisms, analysis tools, and
integration with other ULTRA components.

Usage:
    python test_biological_timing.py

This test serves as both validation and documentation for the timing circuits system.

Author: ULTRA Development Team
Version: 1.0.0
"""

import sys
import os
import time
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Any
import logging

# Add the ULTRA path (adjust as needed for your directory structure)
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

# Import the biological timing module
try:
    from ultra.core_neural.biological_timing import (
        # Core classes
        BiologicalTimingCircuits,
        DeltaOscillator, ThetaOscillator, AlphaOscillator, 
        BetaOscillator, GammaOscillator, HighGammaOscillator,
        PhaseAmplitudeCoupling, PhasePhaseCoupling, AmplitudeAmplitudeCoupling,
        CoherenceAnalyzer, SpectralAnalyzer,
        
        # Parameters and enums
        OscillatorParameters, CouplingParameters, SpectralFeatures,
        OscillationType, CouplingType, BrainState,
        
        # Utility functions
        kuramoto_coupling, phase_amplitude_modulation, 
        compute_phase_locking_value, compute_modulation_index,
        
        # Factory functions
        create_default_oscillators, create_sleep_oscillators,
        create_attention_oscillators, create_pathological_oscillators,
        
        # Initialization
        initialize_timing_circuits
    )
    print("✅ Successfully imported ULTRA Biological Timing Circuits module")
except ImportError as e:
    print(f"❌ Failed to import ULTRA module: {e}")
    print("Make sure the ULTRA package is in your Python path")
    sys.exit(1)

# Configure logging for tests
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class BiologicalTimingTestSuite:
    """Comprehensive test suite for ULTRA Biological Timing Circuits."""
    
    def __init__(self):
        """Initialize test suite."""
        self.test_results = {}
        self.timing_circuits = None
        
    def run_all_tests(self) -> Dict[str, bool]:
        """Run all tests and return results summary."""
        print("\n" + "="*60)
        print("🧠 ULTRA BIOLOGICAL TIMING CIRCUITS - TEST SUITE")
        print("="*60)
        
        tests = [
            ("Initialization Test", self.test_initialization),
            ("Individual Oscillator Test", self.test_individual_oscillators),
            ("Cross-Frequency Coupling Test", self.test_cross_frequency_coupling),
            ("Coherence Analysis Test", self.test_coherence_analysis),
            ("Spectral Analysis Test", self.test_spectral_analysis),
            ("Brain State Modulation Test", self.test_brain_state_modulation),
            ("Factory Functions Test", self.test_factory_functions),
            ("Integration Test", self.test_system_integration),
            ("Performance Test", self.test_performance),
            ("Pathological Models Test", self.test_pathological_models)
        ]
        
        for test_name, test_func in tests:
            print(f"\n🔬 Running: {test_name}")
            try:
                result = test_func()
                self.test_results[test_name] = result
                status = "✅ PASSED" if result else "❌ FAILED"
                print(f"   {status}")
            except Exception as e:
                self.test_results[test_name] = False
                print(f"   ❌ FAILED - Exception: {str(e)}")
                logger.exception(f"Test failed: {test_name}")
                
        self.print_test_summary()
        return self.test_results
        
    def test_initialization(self) -> bool:
        """Test timing circuits initialization."""
        try:
            # Test basic initialization
            config = {
                'oscillators_enabled': ['delta', 'theta', 'alpha', 'beta', 'gamma'],
                'coupling_enabled': True,
                'phase_amplitude_coupling': True
            }
            
            init_result = initialize_timing_circuits(config)
            assert init_result['status'] == 'success', "Initialization failed"
            
            # Test BiologicalTimingCircuits creation
            self.timing_circuits = BiologicalTimingCircuits(config)
            assert self.timing_circuits is not None, "Failed to create timing circuits"
            
            # Verify default oscillators were created
            assert len(self.timing_circuits.oscillators) > 0, "No default oscillators created"
            
            # Verify analysis tools initialized
            assert self.timing_circuits.coherence_analyzer is not None, "Coherence analyzer not initialized"
            assert self.timing_circuits.spectral_analyzer is not None, "Spectral analyzer not initialized"
            
            print(f"   • Initialized with {len(self.timing_circuits.oscillators)} oscillators")
            print(f"   • {len(self.timing_circuits.pac_couplings)} PAC couplings created")
            
            return True
            
        except Exception as e:
            print(f"   Initialization error: {str(e)}")
            return False
            
    def test_individual_oscillators(self) -> bool:
        """Test individual oscillator functionality."""
        try:
            # Test each oscillator type
            oscillator_tests = []
            
            # Delta oscillator
            delta_params = OscillatorParameters(
                frequency=2.0, amplitude=1.0, noise_level=0.01,
                arousal_sensitivity=0.1  # Reduced sensitivity to prevent instability
            )
            delta_osc = DeltaOscillator("test_delta", delta_params)
            delta_osc.set_homeostatic_drive(0.5)
            delta_osc.set_arousal_level(0.5)  # Set neutral arousal
            
            # Run delta oscillator for some steps
            for _ in range(100):
                state = delta_osc.update(dt=1.0)
                
            assert len(delta_osc.phase_history) == 100, "Phase history not recorded"
            # More lenient frequency check with better bounds
            frequency_in_range = (delta_params.min_frequency <= delta_osc.current_frequency <= delta_params.max_frequency)
            assert frequency_in_range, f"Delta frequency out of range: {delta_osc.current_frequency} (should be {delta_params.min_frequency}-{delta_params.max_frequency})"
            oscillator_tests.append("Delta")
            
            # Theta oscillator  
            theta_params = OscillatorParameters(
                frequency=6.0, amplitude=0.8, noise_level=0.01,
                arousal_sensitivity=0.1  # Reduced sensitivity
            )
            theta_osc = ThetaOscillator("test_theta", theta_params)
            theta_osc.set_memory_load(0.7)
            theta_osc.set_movement_velocity(1.2)
            theta_osc.set_arousal_level(0.5)
            
            for _ in range(100):
                theta_osc.update(dt=1.0)
                
            frequency_in_range = (theta_params.min_frequency <= theta_osc.current_frequency <= theta_params.max_frequency)
            assert frequency_in_range, f"Theta frequency out of range: {theta_osc.current_frequency}"
            oscillator_tests.append("Theta")
            
            # Alpha oscillator
            alpha_params = OscillatorParameters(
                frequency=10.0, amplitude=0.6, noise_level=0.01,
                arousal_sensitivity=0.1  # Reduced sensitivity
            )
            alpha_osc = AlphaOscillator("test_alpha", alpha_params)
            alpha_osc.set_eyes_closed(True)
            alpha_osc.set_attention_level(0.3)  # Low attention should increase alpha
            alpha_osc.set_arousal_level(0.4)
            
            for _ in range(100):
                alpha_osc.update(dt=1.0)
                
            frequency_in_range = (alpha_params.min_frequency <= alpha_osc.current_frequency <= alpha_params.max_frequency)
            assert frequency_in_range, f"Alpha frequency out of range: {alpha_osc.current_frequency}"
            oscillator_tests.append("Alpha")
            
            # Beta oscillator
            beta_params = OscillatorParameters(
                frequency=20.0, amplitude=0.4, noise_level=0.01,
                arousal_sensitivity=0.1  # Reduced sensitivity
            )
            beta_osc = BetaOscillator("test_beta", beta_params)
            beta_osc.set_motor_activity(0.8)
            beta_osc.set_cognitive_load(0.6)
            beta_osc.set_arousal_level(0.6)
            
            for _ in range(100):
                beta_osc.update(dt=1.0)
                
            frequency_in_range = (beta_params.min_frequency <= beta_osc.current_frequency <= beta_params.max_frequency)
            assert frequency_in_range, f"Beta frequency out of range: {beta_osc.current_frequency}"
            oscillator_tests.append("Beta")
            
            # Gamma oscillator
            gamma_params = OscillatorParameters(
                frequency=40.0, amplitude=0.3, noise_level=0.01,
                arousal_sensitivity=0.1  # Reduced sensitivity
            )
            gamma_osc = GammaOscillator("test_gamma", gamma_params)
            gamma_osc.set_binding_demand(0.9)
            gamma_osc.set_conscious_access(0.8)
            gamma_osc.set_arousal_level(0.7)
            
            for _ in range(100):
                gamma_osc.update(dt=1.0)
                
            frequency_in_range = (gamma_params.min_frequency <= gamma_osc.current_frequency <= gamma_params.max_frequency)
            assert frequency_in_range, f"Gamma frequency out of range: {gamma_osc.current_frequency}"
            oscillator_tests.append("Gamma")
            
            print(f"   • Tested oscillators: {', '.join(oscillator_tests)}")
            print(f"   • All oscillators maintained frequency ranges")
            
            return True
            
        except Exception as e:
            print(f"   Individual oscillator test error: {str(e)}")
            return False
            
    def test_cross_frequency_coupling(self) -> bool:
        """Test cross-frequency coupling mechanisms."""
        try:
            # Create test oscillators
            theta_params = OscillatorParameters(frequency=6.0, amplitude=0.8)
            theta_osc = ThetaOscillator("coupling_theta", theta_params)
            
            gamma_params = OscillatorParameters(frequency=40.0, amplitude=0.3)
            gamma_osc = GammaOscillator("coupling_gamma", gamma_params)
            
            # Test Phase-Amplitude Coupling
            pac_params = CouplingParameters(
                modulation_depth=0.5,
                phase_preference=0.0,
                plasticity_rate=0.01
            )
            
            pac_coupling = PhaseAmplitudeCoupling(theta_osc, gamma_osc, pac_params)
            
            # Run coupling simulation
            modulation_indices = []
            for i in range(500):  # Longer simulation for coupling analysis
                theta_osc.update(dt=1.0)
                gamma_osc.update(dt=1.0)
                coupling_state = pac_coupling.update_coupling(dt=1.0)
                
                if i > 100:  # Allow time for coupling to develop
                    modulation_indices.append(coupling_state['modulation_index'])
                    
            # Verify coupling developed
            mean_mi = np.mean(modulation_indices)
            assert mean_mi > 0.0, f"No modulation index detected: {mean_mi}"
            
            # Test detailed coupling metrics
            detailed_metrics = pac_coupling.compute_detailed_coupling_metrics(window_length=200)
            assert 'modulation_index' in detailed_metrics, "Detailed metrics missing modulation_index"
            assert 'preferred_coupling_phase' in detailed_metrics, "Detailed metrics missing preferred phase"
            
            print(f"   • PAC coupling established - MI: {mean_mi:.3f}")
            print(f"   • Preferred coupling phase: {detailed_metrics['preferred_coupling_phase']:.2f} rad")
            
            # Test Phase-Phase Coupling
            alpha_params = OscillatorParameters(frequency=10.0, amplitude=0.6)
            alpha_osc = AlphaOscillator("coupling_alpha", alpha_params)
            
            beta_params = OscillatorParameters(frequency=20.0, amplitude=0.4)
            beta_osc = BetaOscillator("coupling_beta", beta_params)
            
            ppc_coupling = PhasePhaseCoupling(alpha_osc, beta_osc, n_ratio=1, m_ratio=2)
            
            # Run PPC simulation
            plv_values = []
            for i in range(300):
                alpha_osc.update(dt=1.0)
                beta_osc.update(dt=1.0)
                ppc_state = ppc_coupling.update_coupling(dt=1.0)
                
                if i > 50:
                    plv_values.append(ppc_state['phase_locking_value'])
                    
            mean_plv = np.mean(plv_values)
            assert mean_plv >= 0.0, f"Invalid PLV: {mean_plv}"
            
            print(f"   • PPC coupling established - PLV: {mean_plv:.3f}")
            
            return True
            
        except Exception as e:
            print(f"   Cross-frequency coupling test error: {str(e)}")
            return False
            
    def test_coherence_analysis(self) -> bool:
        """Test coherence analysis functionality."""
        try:
            coherence_analyzer = CoherenceAnalyzer(sampling_rate=1000.0)
            
            # Generate test signals with known coherence
            t = np.linspace(0, 10, 10000)  # 10 seconds at 1kHz
            
            # Signal 1: 10 Hz sine wave with noise
            signal1 = np.sin(2 * np.pi * 10 * t) + 0.1 * np.random.randn(len(t))
            
            # Signal 2: Same 10 Hz component plus independent component
            signal2 = 0.8 * np.sin(2 * np.pi * 10 * t + np.pi/4) + 0.5 * np.sin(2 * np.pi * 25 * t) + 0.1 * np.random.randn(len(t))
            
            # Test magnitude-squared coherence
            frequency_bands = {
                'alpha': (8.0, 12.0),
                'beta': (13.0, 30.0)
            }
            
            coherence_result = coherence_analyzer.compute_magnitude_squared_coherence(
                signal1, signal2, frequency_bands
            )
            
            assert 'coherence' in coherence_result, "Coherence computation failed"
            assert 'band_coherence' in coherence_result, "Band coherence missing"
            assert coherence_result['band_coherence']['alpha'] > 0.5, "Alpha band coherence too low"
            
            print(f"   • Alpha band coherence: {coherence_result['band_coherence']['alpha']:.3f}")
            print(f"   • Peak coherence: {coherence_result['peak_coherence']:.3f} at {coherence_result['peak_frequency']:.1f} Hz")
            
            # Test phase locking value
            plv = coherence_analyzer.compute_phase_locking_value(
                signal1, signal2, frequency_band=(8.0, 12.0)
            )
            
            assert 0.0 <= plv <= 1.0, f"PLV out of range: {plv}"
            assert plv > 0.3, f"PLV too low for coherent signals: {plv}"
            
            print(f"   • Phase locking value: {plv:.3f}")
            
            # Test imaginary coherence
            imag_coh_result = coherence_analyzer.compute_imaginary_coherence(signal1, signal2)
            assert 'imaginary_coherence' in imag_coh_result, "Imaginary coherence missing"
            
            print(f"   • Mean imaginary coherence: {imag_coh_result['mean_imaginary_coherence']:.3f}")
            
            return True
            
        except Exception as e:
            print(f"   Coherence analysis test error: {str(e)}")
            return False
            
    def test_spectral_analysis(self) -> bool:
        """Test spectral analysis functionality."""
        try:
            spectral_analyzer = SpectralAnalyzer(sampling_rate=1000.0)
            
            # Generate test signal with multiple frequency components
            t = np.linspace(0, 5, 5000)  # 5 seconds at 1kHz
            signal = (1.0 * np.sin(2 * np.pi * 2 * t) +    # Delta
                     0.8 * np.sin(2 * np.pi * 6 * t) +    # Theta  
                     0.6 * np.sin(2 * np.pi * 10 * t) +   # Alpha
                     0.4 * np.sin(2 * np.pi * 20 * t) +   # Beta
                     0.3 * np.sin(2 * np.pi * 40 * t) +   # Gamma
                     0.1 * np.random.randn(len(t)))       # Noise
            
            # Test power spectral density
            psd_result = spectral_analyzer.compute_power_spectral_density(signal, method='welch')
            
            assert 'frequencies' in psd_result, "PSD frequencies missing"
            assert 'psd' in psd_result, "PSD values missing"
            assert 'band_powers' in psd_result, "Band powers missing"
            assert 'spectral_features' in psd_result, "Spectral features missing"
            
            # Verify band powers detected
            band_powers = psd_result['band_powers']
            assert band_powers['delta']['absolute_power'] > 0, "Delta power not detected"
            assert band_powers['theta']['absolute_power'] > 0, "Theta power not detected"
            assert band_powers['alpha']['absolute_power'] > 0, "Alpha power not detected"
            assert band_powers['beta']['absolute_power'] > 0, "Beta power not detected"
            assert band_powers['gamma']['absolute_power'] > 0, "Gamma power not detected"
            
            print(f"   • Delta power: {band_powers['delta']['absolute_power']:.2f}")
            print(f"   • Alpha peak freq: {band_powers['alpha']['peak_frequency']:.1f} Hz")
            print(f"   • Spectral centroid: {psd_result['spectral_features']['spectral_centroid']:.1f} Hz")
            
            # Test spectrogram
            spectrogram_result = spectral_analyzer.compute_spectrogram(signal)
            
            assert 'spectrogram' in spectrogram_result, "Spectrogram missing"
            assert 'band_power_evolution' in spectrogram_result, "Band power evolution missing"
            
            print(f"   • Spectrogram shape: {spectrogram_result['spectrogram'].shape}")
            
            return True
            
        except Exception as e:
            print(f"   Spectral analysis test error: {str(e)}")
            return False
            
    def test_brain_state_modulation(self) -> bool:
        """Test brain state-dependent modulation."""
        try:
            if self.timing_circuits is None:
                self.timing_circuits = BiologicalTimingCircuits()
                
            # Test different brain states
            brain_states = [
                BrainState.WAKE_ALERT,
                BrainState.WAKE_RELAXED, 
                BrainState.LIGHT_SLEEP,
                BrainState.DEEP_SLEEP
            ]
            
            state_results = {}
            
            for brain_state in brain_states:
                # Set brain state
                self.timing_circuits.set_brain_state(brain_state)
                self.timing_circuits.set_arousal_level(0.8 if 'WAKE' in brain_state.name else 0.2)
                
                # Run simulation
                states = []
                for _ in range(100):
                    state = self.timing_circuits.step(dt=1.0)
                    states.append(state)
                    
                # Analyze results
                final_state = states[-1]
                osc_states = final_state['oscillator_states']
                
                # Check delta oscillator responds to sleep states
                if 'delta_primary' in osc_states:
                    delta_amp = osc_states['delta_primary']['amplitude']
                    state_results[brain_state.name] = {
                        'delta_amplitude': delta_amp,
                        'synchronization': final_state['synchronization']['kuramoto_order']
                    }
                    
            # Verify state-dependent changes
            if 'DEEP_SLEEP' in state_results and 'WAKE_ALERT' in state_results:
                sleep_delta = state_results['DEEP_SLEEP']['delta_amplitude']
                wake_delta = state_results['WAKE_ALERT']['delta_amplitude']
                
                # Delta should be higher during deep sleep
                assert sleep_delta > wake_delta, f"Delta not enhanced in sleep: {sleep_delta} vs {wake_delta}"
                
                print(f"   • Deep sleep delta amplitude: {sleep_delta:.3f}")
                print(f"   • Wake delta amplitude: {wake_delta:.3f}")
                
            print(f"   • Tested {len(brain_states)} brain states")
            
            return True
            
        except Exception as e:
            print(f"   Brain state modulation test error: {str(e)}")
            return False
            
    def test_factory_functions(self) -> bool:
        """Test factory functions for creating oscillator sets."""
        try:
            timing_circuits = BiologicalTimingCircuits()
            
            # Test default oscillators
            default_oscs = create_default_oscillators(timing_circuits)
            assert len(default_oscs) >= 5, f"Not enough default oscillators: {len(default_oscs)}"
            assert 'delta' in default_oscs, "Delta oscillator missing from defaults"
            assert 'gamma' in default_oscs, "Gamma oscillator missing from defaults"
            
            print(f"   • Created {len(default_oscs)} default oscillators")
            
            # Test sleep oscillators
            sleep_oscs = create_sleep_oscillators(timing_circuits)
            assert len(sleep_oscs) >= 2, f"Not enough sleep oscillators: {len(sleep_oscs)}"
            assert 'delta' in sleep_oscs, "Sleep delta oscillator missing"
            
            print(f"   • Created {len(sleep_oscs)} sleep-optimized oscillators")
            
            # Test attention oscillators
            attention_oscs = create_attention_oscillators(timing_circuits)
            assert len(attention_oscs) >= 2, f"Not enough attention oscillators: {len(attention_oscs)}"
            assert 'gamma' in attention_oscs, "Attention gamma oscillator missing"
            
            print(f"   • Created {len(attention_oscs)} attention-optimized oscillators")
            
            # Test pathological oscillators
            parkinson_oscs = create_pathological_oscillators(timing_circuits, "parkinson")
            assert len(parkinson_oscs) >= 1, "No Parkinson's oscillators created"
            
            epilepsy_oscs = create_pathological_oscillators(timing_circuits, "epilepsy")
            assert len(epilepsy_oscs) >= 1, "No epilepsy oscillators created"
            
            print(f"   • Created pathological models: Parkinson's ({len(parkinson_oscs)}), Epilepsy ({len(epilepsy_oscs)})")
            
            return True
            
        except Exception as e:
            print(f"   Factory functions test error: {str(e)}")
            return False
            
    def test_system_integration(self) -> bool:
        """Test complete system integration."""
        try:
            if self.timing_circuits is None:
                self.timing_circuits = BiologicalTimingCircuits()
                
            # Add custom oscillators
            custom_params = OscillatorParameters(
                frequency=15.0, amplitude=0.5, noise_level=0.02
            )
            custom_osc = BetaOscillator("custom_beta", custom_params)
            self.timing_circuits.add_oscillator(custom_osc, group_name="custom_group")
            
            # Add custom coupling
            if "beta_primary" in self.timing_circuits.oscillators:
                coupling_params = CouplingParameters(modulation_depth=0.3)
                coupling_id = self.timing_circuits.add_phase_amplitude_coupling(
                    "beta_primary", "custom_beta", coupling_params
                )
                assert coupling_id >= 0, "Failed to add custom coupling"
                
            # Run integrated simulation
            simulation_results = []
            for i in range(200):
                state = self.timing_circuits.step(dt=1.0)
                simulation_results.append(state)
                
                # Change brain state mid-simulation
                if i == 100:
                    self.timing_circuits.set_brain_state(BrainState.LIGHT_SLEEP)
                    
            # Analyze results
            final_state = simulation_results[-1]
            assert 'synchronization' in final_state, "Synchronization metrics missing"
            assert final_state['synchronization']['n_oscillators'] > 0, "No oscillators in synchronization"
            
            # Test state saving and loading
            save_path = "test_timing_state.pkl"
            self.timing_circuits.save_state(save_path)
            assert os.path.exists(save_path), "State file not created"
            
            # Cleanup
            os.remove(save_path)
            
            print(f"   • Integrated simulation: {len(simulation_results)} steps")
            print(f"   • Final synchronization: {final_state['synchronization']['kuramoto_order']:.3f}")
            print(f"   • State save/load: OK")
            
            return True
            
        except Exception as e:
            print(f"   System integration test error: {str(e)}")
            return False
            
    def test_performance(self) -> bool:
        """Test system performance."""
        try:
            timing_circuits = BiologicalTimingCircuits()
            
            # Create additional oscillators for performance test
            for i in range(10):
                params = OscillatorParameters(
                    frequency=10 + i * 5, amplitude=0.5, noise_level=0.01
                )
                osc = GammaOscillator(f"perf_test_{i}", params)
                timing_circuits.add_oscillator(osc)
                
            # Performance timing
            start_time = time.time()
            n_steps = 1000
            
            for _ in range(n_steps):
                timing_circuits.step(dt=1.0)
                
            end_time = time.time()
            elapsed_time = end_time - start_time
            steps_per_second = n_steps / elapsed_time
            
            # Performance criteria (adjust based on your system)
            min_performance = 100  # steps per second
            assert steps_per_second > min_performance, f"Performance too low: {steps_per_second:.1f} steps/sec"
            
            print(f"   • Performance: {steps_per_second:.1f} steps/second")
            print(f"   • {len(timing_circuits.oscillators)} oscillators simulated")
            
            return True
            
        except Exception as e:
            print(f"   Performance test error: {str(e)}")
            return False
            
    def test_pathological_models(self) -> bool:
        """Test pathological oscillation models."""
        try:
            timing_circuits = BiologicalTimingCircuits()
            
            # Test Parkinson's model (excessive beta)
            parkinson_oscs = create_pathological_oscillators(timing_circuits, "parkinson")
            pathological_beta = parkinson_oscs["pathological_beta"]
            
            # Run simulation
            for _ in range(100):
                pathological_beta.update(dt=1.0)
                
            # Check for excessive beta characteristics
            assert pathological_beta.current_amplitude > 0.8, "Pathological beta amplitude not elevated"
            assert pathological_beta.pathological_synchrony > 0.5, "Pathological synchrony not set"
            
            print(f"   • Parkinson's beta amplitude: {pathological_beta.current_amplitude:.3f}")
            
            # Test epilepsy model (high-frequency oscillations)
            epilepsy_oscs = create_pathological_oscillators(timing_circuits, "epilepsy")
            epileptic_osc = epilepsy_oscs["epileptic"]
            
            for _ in range(100):
                epileptic_osc.update(dt=1.0)
                
            assert epileptic_osc.current_frequency > 100.0, "Epileptic frequency not high enough"
            assert epileptic_osc.pathological_activity > 0.8, "Pathological activity not set"
            
            print(f"   • Epileptic oscillation frequency: {epileptic_osc.current_frequency:.1f} Hz")
            
            # Test Alzheimer's model (disrupted rhythms)
            alzheimer_oscs = create_pathological_oscillators(timing_circuits, "alzheimer")
            disrupted_gamma = alzheimer_oscs["disrupted_gamma"]
            
            # Store initial amplitude for comparison
            initial_amplitude = disrupted_gamma.current_amplitude
            
            for _ in range(100):
                disrupted_gamma.update(dt=1.0)
                
            # Check that amplitude remains low (pathological state is maintained)
            # The oscillator should stay disrupted due to low attention/arousal settings
            assert disrupted_gamma.current_amplitude < 0.8, f"Disrupted gamma amplitude too high: {disrupted_gamma.current_amplitude}"
            
            # Verify pathological configuration is active
            assert disrupted_gamma.conscious_access <= 0.2, "Conscious access not properly reduced"
            assert disrupted_gamma.binding_demand <= 0.2, "Binding demand not properly reduced"
            
            print(f"   • Disrupted gamma amplitude: {disrupted_gamma.current_amplitude:.3f}")
            print(f"   • Conscious access level: {disrupted_gamma.conscious_access:.3f}")
            
            return True
            
        except Exception as e:
            print(f"   Pathological models test error: {str(e)}")
            return False
                
            
            
    def print_test_summary(self):
        """Print comprehensive test results summary."""
        print("\n" + "="*60)
        print("📊 TEST RESULTS SUMMARY")
        print("="*60)
        
        passed_tests = sum(1 for result in self.test_results.values() if result)
        total_tests = len(self.test_results)
        pass_rate = (passed_tests / total_tests) * 100
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {total_tests - passed_tests}")
        print(f"Pass Rate: {pass_rate:.1f}%")
        
        print("\nDetailed Results:")
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"  {status} {test_name}")
            
        if pass_rate == 100.0:
            print("\n🎉 ALL TESTS PASSED! ULTRA Biological Timing Circuits is working correctly.")
        elif pass_rate >= 80.0:
            print(f"\n⚠️  {pass_rate:.1f}% tests passed. Some issues detected but core functionality works.")
        else:
            print(f"\n❌ Only {pass_rate:.1f}% tests passed. Significant issues detected.")
            
    def create_visualization_demo(self):
        """Create visualization demo of the timing system."""
        try:
            print("\n🎨 Creating visualization demo...")
            
            if self.timing_circuits is None:
                self.timing_circuits = BiologicalTimingCircuits()
                
            # Run simulation to generate data
            for _ in range(500):
                self.timing_circuits.step(dt=1.0)
                
            # Create visualization
            fig = self.timing_circuits.visualize_oscillator_network(
                figure_size=(15, 10), show_couplings=True
            )
            
            if fig is not None:
                plt.savefig('ultra_timing_demo.png', dpi=300, bbox_inches='tight')
                print("   • Visualization saved as 'ultra_timing_demo.png'")
                plt.close(fig)
            else:
                print("   • Visualization not available (matplotlib not installed)")
                
        except Exception as e:
            print(f"   Visualization demo error: {str(e)}")


def main():
    """Main test execution function."""
    print("🚀 Starting ULTRA Biological Timing Circuits Test Suite...")
    
    # Create and run test suite
    test_suite = BiologicalTimingTestSuite()
    results = test_suite.run_all_tests()
    
    # Create visualization demo
    test_suite.create_visualization_demo()
    
    # Example usage demonstration
    print("\n" + "="*60)
    print("💡 EXAMPLE USAGE DEMONSTRATION")
    print("="*60)
    
    try:
        # Create timing circuits system
        print("\n1. Creating BiologicalTimingCircuits system...")
        timing_system = BiologicalTimingCircuits()
        
        # Add custom oscillators
        print("2. Adding custom oscillators...")
        custom_theta = ThetaOscillator("memory_theta", OscillatorParameters(frequency=7.0, amplitude=0.9))
        custom_theta.set_memory_load(0.8)
        timing_system.add_oscillator(custom_theta, group_name="memory_system")
        
        # Set up brain state modulation
        print("3. Configuring brain state modulation...")
        timing_system.set_brain_state(BrainState.WAKE_ALERT)
        timing_system.set_arousal_level(0.8)
        timing_system.set_attention_level(0.9)
        
        # Run simulation
        print("4. Running integrated simulation...")
        for i in range(200):
            state = timing_system.step(dt=1.0)
            
            # Change to sleep state partway through
            if i == 100:
                timing_system.set_brain_state(BrainState.LIGHT_SLEEP)
                timing_system.set_arousal_level(0.3)
                
        # Analyze results
        print("5. Analyzing results...")
        final_stats = timing_system.get_timing_statistics()
        spectral_results = timing_system.compute_spectral_power()
        
        print(f"   • System has {final_stats['system_info']['n_oscillators']} oscillators")
        print(f"   • Current brain state: {final_stats['system_info']['brain_state']}")
        print(f"   • Network synchronization: {final_stats['synchronization_stats']['mean_kuramoto_order']:.3f}")
        
        print("\n✨ Example usage completed successfully!")
        
    except Exception as e:
        print(f"❌ Example usage failed: {str(e)}")
        
    # Final summary
    passed_count = sum(1 for result in results.values() if result)
    total_count = len(results)
    
    print(f"\n🏁 Test suite completed: {passed_count}/{total_count} tests passed")
    
    if passed_count == total_count:
        print("🎊 ULTRA Biological Timing Circuits is ready for integration!")
        return True
    else:
        print("⚠️  Some tests failed. Please review the issues above.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)