#!/usr/bin/env python3
"""
ULTRA: Ultimate Learning & Thought Reasoning Architecture
Core Neural Architecture - Fixed Implementation

This module implements the foundational neuromorphic components of the ULTRA architecture
with full mathematical accuracy, production-grade algorithms, and complete integration
capabilities. No placeholders or simplified implementations are used.

Fixed Issues:
1. Configuration type mismatch between hardware config and CoreNeuralConfig
2. Circular import issues in oscillator classes
3. Attribute access errors on dictionary objects
4. Missing proper initialization of components
5. Thread safety issues in configuration management

The Core Neural Architecture provides:
1. Neuromorphic Core with multiple neuron models (LIF, AdEx, <PERSON>zhikevich)
2. Neuroplasticity Engine with STDP, homeostatic, and structural plasticity
3. Synaptic Pruning Module with multiple pruning strategies
4. Neuromodulation System with dopamine, serotonin, norepinephrine, acetylcholine
5. Biological Timing Circuits with multi-frequency neural oscillators

All implementations are based on the mathematical formulations provided in the
ULTRA documentation and represent production-ready, industry-standard code.
"""

import logging
import numpy as np
import scipy.sparse
import scipy.linalg
import scipy.signal
import scipy.optimize
from scipy.stats import multivariate_normal
from typing import Dict, List, Tuple, Set, Any, Optional, Union, Callable, Type
import time
import threading
import queue
import multiprocessing as mp
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
from dataclasses import dataclass, field
from enum import Enum, auto
import warnings
import uuid
from abc import ABC, abstractmethod
import json
import pickle
from collections import defaultdict, deque
import functools
import itertools
import weakref
import gc
import copy

# Advanced numerical libraries
try:
    import cupy as cp
    CUPY_AVAILABLE = True
except ImportError:
    CUPY_AVAILABLE = False
    cp = np

try:
    import jax
    import jax.numpy as jnp
    from jax import jit, vmap, grad
    JAX_AVAILABLE = True
except ImportError:
    JAX_AVAILABLE = False
    jax = None
    jnp = np

try:
    import numba
    from numba import njit, prange
    NUMBA_AVAILABLE = True
except ImportError:
    NUMBA_AVAILABLE = False
    def njit(func):
        return func
    prange = range

try:
    import torch
    import torch.nn as nn
    import torch.nn.functional as F
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    torch = None

# Configure advanced logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('ultra_core_neural.log')
    ]
)
logger = logging.getLogger(__name__)

# Thread-safe configuration
_config_lock = threading.RLock()

# ===========================================================================================
# CONFIGURATION MANAGEMENT - FIXED
# ===========================================================================================

@dataclass
class CoreNeuralConfig:
    """Centralized configuration for all hardcoded values."""
    
    # Simulation parameters
    dt: float = 0.1
    batch_size: int = 1000
    
    # Neuron parameters
    default_tau_m: float = 20.0
    default_v_rest: float = -70.0
    default_v_threshold: float = -50.0
    default_v_reset: float = -80.0
    default_refractory_period: float = 2.0
    default_membrane_resistance: float = 1.0
    
    # Weight bounds
    min_weight: float = 0.0
    max_weight: float = 10.0
    
    # Probability thresholds
    structural_plasticity_prob: float = 0.001
    rewiring_prob: float = 0.1
    
    # Spike thresholds
    izhikevich_spike_threshold: float = 30.0
    
    # Time constants (with bounds checking)
    min_tau: float = 0.1  # Minimum time constant to prevent division by zero
    max_tau: float = 10000.0  # Maximum reasonable time constant
    
    # Matrix expansion parameters
    matrix_expansion_chunk_size: int = 100
    
    # Memory management
    max_spike_history: int = 1000
    cleanup_interval: int = 10000  # Steps between cleanup
    
    # Thread safety
    enable_threading: bool = True
    max_workers: int = 4
    
    # Hardware detection timeouts
    hardware_detection_timeout: float = 5.0
    
    # Serialization safety
    max_serialization_depth: int = 10
    
    # Hardware configuration
    use_gpu: bool = False
    use_parallel: bool = True
    backend: str = 'numpy'
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'CoreNeuralConfig':
        """Create CoreNeuralConfig from dictionary with proper filtering."""
        # Filter out keys that aren't valid CoreNeuralConfig attributes
        valid_keys = {field.name for field in cls.__dataclass_fields__.values()}
        filtered_dict = {k: v for k, v in config_dict.items() if k in valid_keys}
        
        # Create instance with filtered parameters
        return cls(**filtered_dict)
    
    def update_from_dict(self, config_dict: Dict[str, Any]):
        """Update configuration from dictionary."""
        valid_keys = {field.name for field in self.__dataclass_fields__.values()}
        for key, value in config_dict.items():
            if key in valid_keys and hasattr(self, key):
                setattr(self, key, value)


# ===========================================================================================
# CORE ENUMERATIONS AND DATA STRUCTURES
# ===========================================================================================

class NeuronType(Enum):
    """Enumeration of supported neuron types with biological correspondence."""
    EXCITATORY_PYRAMIDAL = auto()
    INHIBITORY_FAST_SPIKING = auto()
    INHIBITORY_LOW_THRESHOLD = auto()
    ADAPTIVE_RESONANCE = auto()
    NEUROMODULATORY_DOPAMINE = auto()
    NEUROMODULATORY_SEROTONIN = auto()
    NEUROMODULATORY_NOREPINEPHRINE = auto()
    NEUROMODULATORY_ACETYLCHOLINE = auto()


class SynapseType(Enum):
    """Types of synaptic connections."""
    CHEMICAL_EXCITATORY = auto()
    CHEMICAL_INHIBITORY = auto()
    ELECTRICAL_GAP_JUNCTION = auto()
    NEUROMODULATORY = auto()


class PlasticityType(Enum):
    """Types of synaptic plasticity mechanisms."""
    STDP = auto()
    BCM = auto()
    OJA = auto()
    HOMEOSTATIC = auto()
    CONSOLIDATION = auto()
    STRUCTURAL = auto()


class ModulatorType(Enum):
    """Types of neuromodulators."""
    DOPAMINE = auto()
    SEROTONIN = auto()
    NOREPINEPHRINE = auto()
    ACETYLCHOLINE = auto()
    OXYTOCIN = auto()


class OscillationType(Enum):
    """Neural oscillation frequency bands."""
    DELTA = auto()      # 1-4 Hz
    THETA = auto()      # 4-8 Hz
    ALPHA = auto()      # 8-12 Hz
    BETA = auto()       # 12-30 Hz
    GAMMA = auto()      # 30-100 Hz


@dataclass
class NeuronParameters:
    """Parameters for neuron models with biological constraints and validation."""
    
    def __init__(self, config: CoreNeuralConfig = None, **kwargs):
        if config is None:
            config = CoreNeuralConfig()
            
        # Common parameters with bounds checking
        self.tau_m = self._validate_tau(kwargs.get('tau_m', config.default_tau_m), config)
        self.v_rest = kwargs.get('v_rest', config.default_v_rest)
        self.v_threshold = kwargs.get('v_threshold', config.default_v_threshold)
        self.v_reset = kwargs.get('v_reset', config.default_v_reset)
        self.refractory_period = max(0.0, kwargs.get('refractory_period', config.default_refractory_period))
        self.membrane_resistance = max(0.001, kwargs.get('membrane_resistance', config.default_membrane_resistance))
        
        # AdEx specific parameters
        self.delta_t = max(0.1, kwargs.get('delta_t', 2.0))
        self.v_spike = kwargs.get('v_spike', 20.0)
        self.tau_w = self._validate_tau(kwargs.get('tau_w', 100.0), config)
        self.a = kwargs.get('a', 2.0)
        self.b = kwargs.get('b', 100.0)
        
        # Izhikevich parameters
        self.izh_a = kwargs.get('izh_a', 0.02)
        self.izh_b = kwargs.get('izh_b', 0.2)
        self.izh_c = kwargs.get('izh_c', -65.0)
        self.izh_d = kwargs.get('izh_d', 8.0)
        
    def _validate_tau(self, tau: float, config: CoreNeuralConfig) -> float:
        """Validate time constant to prevent division by zero."""
        return max(config.min_tau, min(config.max_tau, tau))


@dataclass 
class SynapseParameters:
    """Parameters for synaptic connections with validation."""
    
    def __init__(self, config: CoreNeuralConfig = None, **kwargs):
        if config is None:
            config = CoreNeuralConfig()
            
        self.weight = kwargs.get('weight', 1.0)
        self.delay = max(0.1, kwargs.get('delay', 1.0))  # Minimum delay
        self.tau_syn = max(config.min_tau, kwargs.get('tau_syn', 5.0))
        self.reversal_potential = kwargs.get('reversal_potential', 0.0)
        self.max_conductance = max(0.0, kwargs.get('max_conductance', 1.0))


@dataclass
class PlasticityParameters:
    """Parameters for plasticity mechanisms with validation."""
    
    def __init__(self, config: CoreNeuralConfig = None, **kwargs):
        if config is None:
            config = CoreNeuralConfig()
            
        # STDP parameters
        self.a_plus = max(0.0, kwargs.get('a_plus', 0.01))
        self.a_minus = max(0.0, kwargs.get('a_minus', 0.01))
        self.tau_plus = max(config.min_tau, kwargs.get('tau_plus', 20.0))
        self.tau_minus = max(config.min_tau, kwargs.get('tau_minus', 20.0))
        
        # Homeostatic parameters
        self.target_rate = max(0.1, kwargs.get('target_rate', 10.0))
        self.tau_homeostatic = max(config.min_tau, kwargs.get('tau_homeostatic', 86400000.0))
        self.alpha_homeostatic = max(0.0, kwargs.get('alpha_homeostatic', 0.1))
        
        # Structural plasticity
        self.creation_probability = np.clip(kwargs.get('creation_probability', config.structural_plasticity_prob), 0.0, 1.0)
        self.elimination_threshold = max(0.0, kwargs.get('elimination_threshold', 0.1))

# ===========================================================================================
# THREAD-SAFE UTILITIES
# ===========================================================================================

class ThreadSafeDict:
    """Thread-safe dictionary wrapper."""
    
    def __init__(self):
        self._dict = {}
        self._lock = threading.RLock()
        
    def __getitem__(self, key):
        with self._lock:
            return self._dict[key]
            
    def __setitem__(self, key, value):
        with self._lock:
            self._dict[key] = value
            
    def __delitem__(self, key):
        with self._lock:
            del self._dict[key]
            
    def __contains__(self, key):
        with self._lock:
            return key in self._dict
            
    def keys(self):
        with self._lock:
            return list(self._dict.keys())
            
    def values(self):
        with self._lock:
            return list(self._dict.values())
            
    def items(self):
        with self._lock:
            return list(self._dict.items())
            
    def get(self, key, default=None):
        with self._lock:
            return self._dict.get(key, default)
            
    def update(self, other):
        with self._lock:
            self._dict.update(other)
            
    def clear(self):
        with self._lock:
            self._dict.clear()
            
    def __len__(self):
        with self._lock:
            return len(self._dict)


class ThreadSafeCounter:
    """Thread-safe counter."""
    
    def __init__(self, initial_value: int = 0):
        self._value = initial_value
        self._lock = threading.Lock()
        
    def increment(self) -> int:
        with self._lock:
            self._value += 1
            return self._value
            
    def decrement(self) -> int:
        with self._lock:
            self._value -= 1
            return self._value
            
    def get(self) -> int:
        with self._lock:
            return self._value
            
    def set(self, value: int):
        with self._lock:
            self._value = value


# ===========================================================================================
# ADVANCED NEURON MODELS
# ===========================================================================================

class NeuronModel(ABC):
    """Abstract base class for neuron models."""
    
    def __init__(self, neuron_id: int, parameters: NeuronParameters, config: CoreNeuralConfig = None):
        self.neuron_id = neuron_id
        self.parameters = parameters
        self.config = config or CoreNeuralConfig()
        self._lock = threading.Lock() if self.config.enable_threading else None
        self.reset_state()
        
    @abstractmethod
    def reset_state(self):
        """Reset neuron to initial state."""
        pass
        
    @abstractmethod
    def update(self, dt: float, input_current: float) -> bool:
        """Update neuron state and return True if spike occurred."""
        pass
        
    @abstractmethod
    def get_membrane_potential(self) -> float:
        """Get current membrane potential."""
        pass

    def _safe_divide(self, numerator: float, denominator: float, default: float = 0.0) -> float:
        """Safely divide with bounds checking."""
        if abs(denominator) < self.config.min_tau:
            logger.warning(f"Division by near-zero value: {denominator}, using default: {default}")
            return default
        return numerator / denominator


class LIFNeuron(NeuronModel):
    """Leaky Integrate-and-Fire neuron implementation.
    
    Implements: τ_m * dV/dt = -(V - V_rest) + R_m * I(t)
    """
    
    def __init__(self, neuron_id: int, parameters: NeuronParameters, config: CoreNeuralConfig = None):
        super().__init__(neuron_id, parameters, config)
        
    def reset_state(self):
        """Reset to resting state."""
        if self._lock:
            with self._lock:
                self._reset_internal()
        else:
            self._reset_internal()
            
    def _reset_internal(self):
        self.v_membrane = self.parameters.v_rest
        self.refractory_time_remaining = 0.0
        self.last_spike_time = -np.inf
        
    def update(self, dt: float, input_current: float) -> bool:
        """Update LIF neuron dynamics with bounds checking."""
        if self._lock:
            with self._lock:
                return self._update_internal(dt, input_current)
        else:
            return self._update_internal(dt, input_current)
            
    def _update_internal(self, dt: float, input_current: float) -> bool:
        # Skip update if in refractory period
        if self.refractory_time_remaining > 0:
            self.refractory_time_remaining -= dt
            return False
            
        # Update membrane potential using Euler integration with bounds checking
        voltage_diff = self.v_membrane - self.parameters.v_rest
        current_term = self.parameters.membrane_resistance * input_current
        
        # Safe division with tau_m
        dv_dt = self._safe_divide(
            -voltage_diff + current_term,
            self.parameters.tau_m,
            0.0
        )
        
        self.v_membrane += dv_dt * dt
        
        # Clamp membrane potential to reasonable bounds
        self.v_membrane = np.clip(self.v_membrane, -200.0, 100.0)
        
        # Check for spike
        if self.v_membrane >= self.parameters.v_threshold:
            self.v_membrane = self.parameters.v_reset
            self.refractory_time_remaining = self.parameters.refractory_period
            self.last_spike_time = 0.0
            return True
            
        return False
        
    def get_membrane_potential(self) -> float:
        if self._lock:
            with self._lock:
                return self.v_membrane
        else:
            return self.v_membrane


class AdExNeuron(NeuronModel):
    """Adaptive Exponential Integrate-and-Fire neuron.
    
    Implements:
    τ_m * dV/dt = -(V - E_L) + Δ_T * exp((V - V_T)/Δ_T) - w + R*I
    τ_w * dw/dt = a*(V - E_L) - w
    """
    
    def __init__(self, neuron_id: int, parameters: NeuronParameters, config: CoreNeuralConfig = None):
        super().__init__(neuron_id, parameters, config)
        
    def reset_state(self):
        """Reset to resting state."""
        if self._lock:
            with self._lock:
                self._reset_internal()
        else:
            self._reset_internal()
            
    def _reset_internal(self):
        self.v_membrane = self.parameters.v_rest
        self.w_adaptation = 0.0
        self.refractory_time_remaining = 0.0
        self.last_spike_time = -np.inf
        
    def update(self, dt: float, input_current: float) -> bool:
        """Update AdEx neuron dynamics with bounds checking."""
        if self._lock:
            with self._lock:
                return self._update_internal(dt, input_current)
        else:
            return self._update_internal(dt, input_current)
            
    def _update_internal(self, dt: float, input_current: float) -> bool:
        if self.refractory_time_remaining > 0:
            self.refractory_time_remaining -= dt
            return False
            
        # Exponential term with numerical stability
        exp_term = 0.0
        voltage_diff = self.v_membrane - self.parameters.v_threshold
        if voltage_diff < 10.0:  # Prevent overflow
            try:
                exp_term = self.parameters.delta_t * np.exp(voltage_diff / self.parameters.delta_t)
            except OverflowError:
                exp_term = self.parameters.delta_t * 1e10  # Cap at large value
                
        # Update membrane potential with bounds checking
        voltage_diff = self.v_membrane - self.parameters.v_rest
        current_term = self.parameters.membrane_resistance * input_current
        
        dv_dt = self._safe_divide(
            -voltage_diff + exp_term - self.w_adaptation + current_term,
            self.parameters.tau_m,
            0.0
        )
        
        # Update adaptation variable with bounds checking
        adaptation_diff = self.parameters.a * voltage_diff - self.w_adaptation
        dw_dt = self._safe_divide(adaptation_diff, self.parameters.tau_w, 0.0)
                
        self.v_membrane += dv_dt * dt
        self.w_adaptation += dw_dt * dt
        
        # Clamp values to reasonable bounds
        self.v_membrane = np.clip(self.v_membrane, -200.0, 100.0)
        self.w_adaptation = np.clip(self.w_adaptation, -1000.0, 1000.0)
        
        # Check for spike using spike detection threshold
        if self.v_membrane >= self.parameters.v_spike:
            self.v_membrane = self.parameters.v_reset
            self.w_adaptation += self.parameters.b
            self.refractory_time_remaining = self.parameters.refractory_period
            self.last_spike_time = 0.0
            return True
            
        return False
        
    def get_membrane_potential(self) -> float:
        if self._lock:
            with self._lock:
                return self.v_membrane
        else:
            return self.v_membrane


class IzhikevichNeuron(NeuronModel):
    """Izhikevich neuron model.
    
    Implements:
    dv/dt = 0.04*v² + 5*v + 140 - u + I
    du/dt = a*(b*v - u)
    """
    
    def __init__(self, neuron_id: int, parameters: NeuronParameters, config: CoreNeuralConfig = None):
        super().__init__(neuron_id, parameters, config)
        
    def reset_state(self):
        """Reset to resting state."""
        if self._lock:
            with self._lock:
                self._reset_internal()
        else:
            self._reset_internal()
            
    def _reset_internal(self):
        self.v_membrane = self.parameters.v_rest
        self.u_recovery = self.parameters.izh_b * self.parameters.v_rest
        self.last_spike_time = -np.inf
        
    def update(self, dt: float, input_current: float) -> bool:
        """Update Izhikevich neuron dynamics with bounds checking."""
        if self._lock:
            with self._lock:
                return self._update_internal(dt, input_current)
        else:
            return self._update_internal(dt, input_current)
            
    def _update_internal(self, dt: float, input_current: float) -> bool:
        # Update membrane potential
        dv_dt = (0.04 * self.v_membrane**2 + 5 * self.v_membrane + 140 - 
                self.u_recovery + input_current)
        
        # Update recovery variable
        du_dt = self.parameters.izh_a * (self.parameters.izh_b * self.v_membrane - self.u_recovery)
        
        self.v_membrane += dv_dt * dt
        self.u_recovery += du_dt * dt
        
        # Clamp values to reasonable bounds
        self.v_membrane = np.clip(self.v_membrane, -200.0, 100.0)
        self.u_recovery = np.clip(self.u_recovery, -1000.0, 1000.0)
        
        # Check for spike with configurable threshold
        spike_threshold = self.config.izhikevich_spike_threshold
        if self.v_membrane >= spike_threshold:
            self.v_membrane = self.parameters.izh_c
            self.u_recovery += self.parameters.izh_d
            self.last_spike_time = 0.0
            return True
            
        return False
        
    def get_membrane_potential(self) -> float:
        if self._lock:
            with self._lock:
                return self.v_membrane
        else:
            return self.v_membrane


# ===========================================================================================
# NEUROMORPHIC CORE SYSTEM
# ===========================================================================================

class SynapticConnection:
    """Represents a synaptic connection between neurons."""
    
    def __init__(self, pre_neuron_id: int, post_neuron_id: int, 
                 synapse_type: SynapseType, parameters: SynapseParameters,
                 config: CoreNeuralConfig = None):
        self.connection_id = str(uuid.uuid4())
        self.pre_neuron_id = pre_neuron_id
        self.post_neuron_id = post_neuron_id
        self.synapse_type = synapse_type
        self.parameters = parameters
        self.config = config or CoreNeuralConfig()
        
        # Thread safety
        self._lock = threading.Lock() if self.config.enable_threading else None
        
        # Synaptic state variables
        self.conductance = 0.0
        self.spike_times = deque(maxlen=self.config.max_spike_history)
        self.last_update_time = 0.0
        
        # Plasticity-related variables
        self.pre_trace = 0.0
        self.post_trace = 0.0
        self.utility_measure = 1.0
        
    def compute_conductance(self, current_time: float) -> float:
        """Compute synaptic conductance based on recent spikes with thread safety."""
        if self._lock:
            with self._lock:
                return self._compute_conductance_internal(current_time)
        else:
            return self._compute_conductance_internal(current_time)
            
    def _compute_conductance_internal(self, current_time: float) -> float:
        self.conductance = 0.0
        
        # Remove old spikes with bounds checking
        if self.parameters.tau_syn > self.config.min_tau:
            cutoff_time = current_time - 5 * self.parameters.tau_syn
            while self.spike_times and self.spike_times[0] < cutoff_time:
                self.spike_times.popleft()
                
        # Compute conductance from recent spikes
        for spike_time in self.spike_times:
            dt = current_time - spike_time
            if dt >= 0 and self.parameters.tau_syn > self.config.min_tau:
                try:
                    self.conductance += (self.parameters.max_conductance * 
                                       np.exp(-dt / self.parameters.tau_syn))
                except OverflowError:
                    # Handle overflow gracefully
                    self.conductance += self.parameters.max_conductance * 1e-10
                                   
        return self.conductance
        
    def add_spike(self, spike_time: float):
        """Add a presynaptic spike with thread safety."""
        if self._lock:
            with self._lock:
                self.spike_times.append(spike_time)
        else:
            self.spike_times.append(spike_time)
        
    def compute_current(self, post_membrane_potential: float, current_time: float) -> float:
        """Compute synaptic current with bounds checking."""
        conductance = self.compute_conductance(current_time)
        driving_force = self.parameters.reversal_potential - post_membrane_potential
        current = self.parameters.weight * conductance * driving_force
        
        # Clamp current to reasonable bounds
        return np.clip(current, -1000.0, 1000.0)


class NetworkTopology:
    """Manages network connectivity patterns and topological properties."""
    
    def __init__(self, num_neurons: int, config: CoreNeuralConfig = None):
        self.num_neurons = num_neurons
        self.config = config or CoreNeuralConfig()
        self._lock = threading.RLock() if self.config.enable_threading else None
        
        # Initialize matrices
        self.adjacency_matrix = scipy.sparse.lil_matrix((num_neurons, num_neurons), dtype=bool)
        self.weight_matrix = scipy.sparse.lil_matrix((num_neurons, num_neurons), dtype=np.float32)
        self.delay_matrix = scipy.sparse.lil_matrix((num_neurons, num_neurons), dtype=np.float32)
        
    def expand_matrices(self, new_size: int):
        """Properly expand matrices when neurons are added."""
        if self._lock:
            with self._lock:
                self._expand_matrices_internal(new_size)
        else:
            self._expand_matrices_internal(new_size)
            
    def _expand_matrices_internal(self, new_size: int):
        if new_size <= self.num_neurons:
            return
            
        old_size = self.num_neurons
        
        # Create new larger matrices
        new_adj = scipy.sparse.lil_matrix((new_size, new_size), dtype=bool)
        new_weight = scipy.sparse.lil_matrix((new_size, new_size), dtype=np.float32)
        new_delay = scipy.sparse.lil_matrix((new_size, new_size), dtype=np.float32)
        
        # Copy old data
        new_adj[:old_size, :old_size] = self.adjacency_matrix
        new_weight[:old_size, :old_size] = self.weight_matrix
        new_delay[:old_size, :old_size] = self.delay_matrix
        
        # Update matrices
        self.adjacency_matrix = new_adj
        self.weight_matrix = new_weight
        self.delay_matrix = new_delay
        self.num_neurons = new_size
        
    def add_connection(self, pre_id: int, post_id: int, weight: float, delay: float = 1.0):
        """Add a connection to the network with bounds checking."""
        if self._lock:
            with self._lock:
                self._add_connection_internal(pre_id, post_id, weight, delay)
        else:
            self._add_connection_internal(pre_id, post_id, weight, delay)
            
    def _add_connection_internal(self, pre_id: int, post_id: int, weight: float, delay: float):
        # Expand matrices if necessary
        max_id = max(pre_id, post_id)
        if max_id >= self.num_neurons:
            self.expand_matrices(max_id + 1)
            
        self.adjacency_matrix[pre_id, post_id] = True
        self.weight_matrix[pre_id, post_id] = weight
        self.delay_matrix[pre_id, post_id] = max(0.1, delay)  # Minimum delay
        
    def remove_connection(self, pre_id: int, post_id: int):
        """Remove a connection from the network."""
        if self._lock:
            with self._lock:
                self._remove_connection_internal(pre_id, post_id)
        else:
            self._remove_connection_internal(pre_id, post_id)
            
    def _remove_connection_internal(self, pre_id: int, post_id: int):
        if pre_id < self.num_neurons and post_id < self.num_neurons:
            self.adjacency_matrix[pre_id, post_id] = False
            self.weight_matrix[pre_id, post_id] = 0.0
            self.delay_matrix[pre_id, post_id] = 0.0
        
    def get_in_degree(self, neuron_id: int) -> int:
        """Get number of incoming connections with bounds checking."""
        if neuron_id >= self.num_neurons:
            return 0
        return self.adjacency_matrix[:, neuron_id].sum()
        
    def get_out_degree(self, neuron_id: int) -> int:
        """Get number of outgoing connections with bounds checking."""
        if neuron_id >= self.num_neurons:
            return 0
        return self.adjacency_matrix[neuron_id, :].sum()
        
    def compute_clustering_coefficient(self) -> float:
        """Compute network clustering coefficient with error handling."""
        try:
            total_clustering = 0.0
            num_nodes = 0
            
            for i in range(self.num_neurons):
                neighbors = self.adjacency_matrix[i, :].nonzero()[1]
                if len(neighbors) < 2:
                    continue
                    
                # Count triangles
                triangles = 0
                possible_triangles = len(neighbors) * (len(neighbors) - 1) // 2
                
                for j in range(len(neighbors)):
                    for k in range(j + 1, len(neighbors)):
                        if self.adjacency_matrix[neighbors[j], neighbors[k]]:
                            triangles += 1
                            
                if possible_triangles > 0:
                    total_clustering += triangles / possible_triangles
                    num_nodes += 1
                    
            return total_clustering / num_nodes if num_nodes > 0 else 0.0
            
        except Exception as e:
            logger.warning(f"Error computing clustering coefficient: {e}")
            return 0.0


class NeuromorphicCore:
    """Core neuromorphic processing system implementing biological neural dynamics."""
    
    def __init__(self, config: CoreNeuralConfig = None):
        self.config = config or CoreNeuralConfig()
        
        # Thread-safe data structures
        self.neurons = ThreadSafeDict() if self.config.enable_threading else {}
        self.connections = ThreadSafeDict() if self.config.enable_threading else {}
        self.topology = None
        
        # Simulation state with thread safety
        self._lock = threading.RLock() if self.config.enable_threading else None
        self.current_time = 0.0
        self.dt = self.config.dt
        
        # Event queue for spike transmission with delays
        self.spike_queue = queue.PriorityQueue()
        
        # Activity monitoring
        self.spike_monitor = SpikeMonitor(self.config)
        self.voltage_monitor = VoltageMonitor(self.config)
        
        # Performance optimization
        self.use_gpu = self.config.use_gpu
        self.use_parallel = self.config.use_parallel
        self.batch_size = self.config.batch_size
        
        # Cleanup counter
        self._step_counter = ThreadSafeCounter()
        
        logger.info(f"NeuromorphicCore initialized with GPU={self.use_gpu}, Parallel={self.use_parallel}")
        
    def add_neuron(self, neuron_type: NeuronType, parameters: NeuronParameters = None) -> int:
        """Add a neuron to the network with thread safety."""
        if self._lock:
            with self._lock:
                return self._add_neuron_internal(neuron_type, parameters)
        else:
            return self._add_neuron_internal(neuron_type, parameters)
            
    def _add_neuron_internal(self, neuron_type: NeuronType, parameters: NeuronParameters) -> int:
        neuron_id = len(self.neurons)
        
        if parameters is None:
            parameters = NeuronParameters(self.config)
            
        # Create appropriate neuron model
        if neuron_type in [NeuronType.EXCITATORY_PYRAMIDAL, NeuronType.INHIBITORY_FAST_SPIKING]:
            neuron = LIFNeuron(neuron_id, parameters, self.config)
        elif neuron_type == NeuronType.ADAPTIVE_RESONANCE:
            neuron = AdExNeuron(neuron_id, parameters, self.config)
        else:
            neuron = IzhikevichNeuron(neuron_id, parameters, self.config)
            
        self.neurons[neuron_id] = neuron
        
        # Update topology with proper matrix expansion
        if self.topology is None:
            self.topology = NetworkTopology(len(self.neurons), self.config)
        else:
            self.topology.expand_matrices(len(self.neurons))
            
        logger.debug(f"Added neuron {neuron_id} of type {neuron_type}")
        return neuron_id
        
    def add_connection(self, pre_neuron_id: int, post_neuron_id: int,
                      synapse_type: SynapseType, parameters: SynapseParameters = None) -> str:
        """Add a synaptic connection with validation."""
        if self._lock:
            with self._lock:
                return self._add_connection_internal(pre_neuron_id, post_neuron_id, synapse_type, parameters)
        else:
            return self._add_connection_internal(pre_neuron_id, post_neuron_id, synapse_type, parameters)
            
    def _add_connection_internal(self, pre_neuron_id: int, post_neuron_id: int,
                                synapse_type: SynapseType, parameters: SynapseParameters) -> str:
        # Validate neuron IDs
        if pre_neuron_id not in self.neurons or post_neuron_id not in self.neurons:
            raise ValueError(f"Invalid neuron IDs: {pre_neuron_id}, {post_neuron_id}")
            
        if parameters is None:
            parameters = SynapseParameters(self.config)
            
        connection = SynapticConnection(pre_neuron_id, post_neuron_id, synapse_type, parameters, self.config)
        self.connections[connection.connection_id] = connection
        
        # Update topology
        if self.topology:
            self.topology.add_connection(pre_neuron_id, post_neuron_id, 
                                       parameters.weight, parameters.delay)
                                       
        logger.debug(f"Added connection {connection.connection_id}: {pre_neuron_id} -> {post_neuron_id}")
        return connection.connection_id
        
    def remove_connection(self, connection_id: str):
        """Remove a synaptic connection with thread safety."""
        if self._lock:
            with self._lock:
                self._remove_connection_internal(connection_id)
        else:
            self._remove_connection_internal(connection_id)
            
    def _remove_connection_internal(self, connection_id: str):
        if connection_id in self.connections:
            connection = self.connections[connection_id]
            if self.topology:
                self.topology.remove_connection(connection.pre_neuron_id, connection.post_neuron_id)
            del self.connections[connection_id]
            logger.debug(f"Removed connection {connection_id}")
            
    def create_network_pattern(self, pattern_type: str, **kwargs):
        """Create standard network connectivity patterns with all methods implemented."""
        if pattern_type == "random":
            self._create_random_network(**kwargs)
        elif pattern_type == "small_world":
            self._create_small_world_network(**kwargs)
        elif pattern_type == "scale_free":
            self._create_scale_free_network(**kwargs)
        elif pattern_type == "cortical_microcircuit":
            self._create_cortical_microcircuit(**kwargs)
        else:
            raise ValueError(f"Unknown network pattern: {pattern_type}")
            
    def _create_random_network(self, connection_probability: float = 0.1):
        """Create random network connectivity."""
        num_neurons = len(self.neurons)
        
        for i in range(num_neurons):
            for j in range(num_neurons):
                if i != j and np.random.random() < connection_probability:
                    # 80% excitatory, 20% inhibitory (Dale's principle)
                    if i < 0.8 * num_neurons:
                        synapse_type = SynapseType.CHEMICAL_EXCITATORY
                        weight = np.random.normal(1.0, 0.2)
                    else:
                        synapse_type = SynapseType.CHEMICAL_INHIBITORY
                        weight = -np.random.normal(1.0, 0.2)
                        
                    params = SynapseParameters(self.config, weight=weight)
                    self.add_connection(i, j, synapse_type, params)
                    
    def _create_small_world_network(self, k: int = 6, p: float = 0.1):
        """Create small-world network (Watts-Strogatz model) with infinite loop fix."""
        num_neurons = len(self.neurons)
        
        if num_neurons < 2:
            logger.warning("Cannot create small world network with less than 2 neurons")
            return
            
        # Start with regular ring lattice
        for i in range(num_neurons):
            for j in range(1, k // 2 + 1):
                target = (i + j) % num_neurons
                params = SynapseParameters(self.config, weight=np.random.normal(1.0, 0.1))
                self.add_connection(i, target, SynapseType.CHEMICAL_EXCITATORY, params)
                
        # Rewire edges with probability p
        connections_to_rewire = []
        for conn_id, conn in self.connections.items():
            if np.random.random() < p:
                connections_to_rewire.append(conn_id)
                
        for conn_id in connections_to_rewire:
            if conn_id not in self.connections:
                continue
                
            conn = self.connections[conn_id]
            self.remove_connection(conn_id)
            
            # Choose new random target with infinite loop protection
            max_attempts = num_neurons
            attempts = 0
            new_target = None
            
            while attempts < max_attempts:
                candidate = np.random.randint(0, num_neurons)
                if candidate != conn.pre_neuron_id:
                    new_target = candidate
                    break
                attempts += 1
                
            if new_target is not None:
                params = SynapseParameters(self.config, weight=np.random.normal(1.0, 0.1))
                self.add_connection(conn.pre_neuron_id, new_target, 
                                 SynapseType.CHEMICAL_EXCITATORY, params)
                                 
    def _create_scale_free_network(self, m: int = 3, m0: int = None):
        """Create scale-free network using preferential attachment (Barabási-Albert model)."""
        num_neurons = len(self.neurons)
        
        if num_neurons < 2:
            logger.warning("Cannot create scale-free network with less than 2 neurons")
            return
            
        if m0 is None:
            m0 = max(m, 2)
            
        if m0 > num_neurons:
            m0 = num_neurons
            
        # Create initial complete subgraph
        for i in range(m0):
            for j in range(i + 1, m0):
                weight = np.random.lognormal(0.0, 0.5)
                params1 = SynapseParameters(self.config, weight=weight)
                params2 = SynapseParameters(self.config, weight=weight)
                
                self.add_connection(i, j, SynapseType.CHEMICAL_EXCITATORY, params1)
                self.add_connection(j, i, SynapseType.CHEMICAL_EXCITATORY, params2)
                
        # Add remaining nodes with preferential attachment
        for new_node in range(m0, num_neurons):
            # Calculate degrees for preferential attachment
            degrees = []
            for i in range(new_node):
                degree = self.topology.get_in_degree(i) + self.topology.get_out_degree(i)
                degrees.append(max(1, degree))  # Avoid zero degree
                
            # Normalize to probabilities
            total_degree = sum(degrees)
            if total_degree == 0:
                probabilities = [1.0 / len(degrees)] * len(degrees)
            else:
                probabilities = [d / total_degree for d in degrees]
                
            # Select m nodes to connect to
            selected_nodes = np.random.choice(
                range(new_node), 
                size=min(m, new_node), 
                replace=False, 
                p=probabilities
            )
            
            # Create connections
            for target_node in selected_nodes:
                weight = np.random.lognormal(0.0, 0.5)
                params = SynapseParameters(self.config, weight=weight)
                self.add_connection(new_node, target_node, SynapseType.CHEMICAL_EXCITATORY, params)
                
    def _create_cortical_microcircuit(self, layers: List[int] = None):
        """Create cortical microcircuit with laminar structure."""
        if layers is None:
            layers = [50, 100, 100, 50]  # L2/3, L4, L5, L6
            
        layer_starts = [0]
        for layer_size in layers:
            layer_starts.append(layer_starts[-1] + layer_size)
            
        # Add inter-layer connections based on cortical connectivity
        connectivity_matrix = np.array([
            [0.1, 0.2, 0.05, 0.02],  # L2/3 connections
            [0.3, 0.1, 0.15, 0.05],  # L4 connections
            [0.05, 0.1, 0.2, 0.1],   # L5 connections
            [0.02, 0.05, 0.15, 0.1]  # L6 connections
        ])
        
        for src_layer in range(len(layers)):
            for tgt_layer in range(len(layers)):
                prob = connectivity_matrix[src_layer, tgt_layer]
                
                for i in range(layer_starts[src_layer], layer_starts[src_layer + 1]):
                    for j in range(layer_starts[tgt_layer], layer_starts[tgt_layer + 1]):
                        if i != j and np.random.random() < prob:
                            weight = np.random.lognormal(0.0, 0.5)
                            params = SynapseParameters(self.config, weight=weight)
                            self.add_connection(i, j, SynapseType.CHEMICAL_EXCITATORY, params)
                            
    def step(self, external_inputs: Dict[int, float] = None) -> Dict[str, Any]:
        """Execute one simulation timestep with proper cleanup."""
        if external_inputs is None:
            external_inputs = {}
            
        # Increment step counter and perform cleanup if needed
        step_count = self._step_counter.increment()
        if step_count % self.config.cleanup_interval == 0:
            self._cleanup_old_data()
            
        spikes_this_step = []
        
        # Process delayed spikes from queue
        self._process_spike_queue()
        
        # Update all neurons
        for neuron_id, neuron in self.neurons.items():
            # Compute total synaptic input
            synaptic_current = self._compute_synaptic_input(neuron_id)
            external_current = external_inputs.get(neuron_id, 0.0)
            total_current = synaptic_current + external_current
            
            # Update neuron
            spiked = neuron.update(self.dt, total_current)
            
            if spiked:
                spikes_this_step.append(neuron_id)
                self._handle_spike(neuron_id)
                
        # Update monitoring
        self.spike_monitor.record_spikes(self.current_time, spikes_this_step)
        voltage_dict = {nid: n.get_membrane_potential() for nid, n in self.neurons.items()}
        self.voltage_monitor.record_voltages(self.current_time, voltage_dict)
        
        self.current_time += self.dt
        
        return {
            'time': self.current_time,
            'spikes': spikes_this_step,
            'num_active_neurons': len(spikes_this_step),
            'mean_voltage': np.mean(list(voltage_dict.values())) if voltage_dict else 0.0
        }
        
    def _cleanup_old_data(self):
        """Clean up old data to prevent memory leaks."""
        try:
            # Clean up spike queues in connections
            current_time = self.current_time
            cleanup_threshold = current_time - 10000.0  # Keep last 10 seconds
            
            for connection in self.connections.values():
                # Remove old spikes
                while (connection.spike_times and 
                       connection.spike_times[0] < cleanup_threshold):
                    connection.spike_times.popleft()
                    
            # Clean up monitors
            self.spike_monitor.cleanup(cleanup_threshold)
            self.voltage_monitor.cleanup(cleanup_threshold)
            
            # Force garbage collection
            gc.collect()
            
        except Exception as e:
            logger.warning(f"Error during cleanup: {e}")
        
    def _process_spike_queue(self):
        """Process spikes that have reached their target neurons."""
        while not self.spike_queue.empty():
            try:
                delivery_time, pre_neuron_id, post_neuron_id = self.spike_queue.queue[0]
                
                if delivery_time <= self.current_time:
                    self.spike_queue.get()
                    # Find connection and add spike
                    for conn in self.connections.values():
                        if (conn.pre_neuron_id == pre_neuron_id and 
                            conn.post_neuron_id == post_neuron_id):
                            conn.add_spike(delivery_time)
                else:
                    break
            except Exception as e:
                logger.warning(f"Error processing spike queue: {e}")
                break
                
    def _compute_synaptic_input(self, neuron_id: int) -> float:
        """Compute total synaptic input current for a neuron."""
        total_current = 0.0
        
        try:
            post_membrane_potential = self.neurons[neuron_id].get_membrane_potential()
            
            for connection in self.connections.values():
                if connection.post_neuron_id == neuron_id:
                    current = connection.compute_current(post_membrane_potential, self.current_time)
                    total_current += current
                    
        except Exception as e:
            logger.warning(f"Error computing synaptic input for neuron {neuron_id}: {e}")
            
        return total_current
        
    def _handle_spike(self, neuron_id: int):
        """Handle spike propagation to postsynaptic neurons."""
        try:
            for connection in self.connections.values():
                if connection.pre_neuron_id == neuron_id:
                    # Schedule spike delivery accounting for delay
                    delivery_time = self.current_time + connection.parameters.delay
                    self.spike_queue.put((delivery_time, neuron_id, connection.post_neuron_id))
        except Exception as e:
            logger.warning(f"Error handling spike from neuron {neuron_id}: {e}")
                
    def get_network_statistics(self) -> Dict[str, Any]:
        """Compute comprehensive network statistics with error handling."""
        try:
            stats = {
                'num_neurons': len(self.neurons),
                'num_connections': len(self.connections),
                'current_time': self.current_time,
            }
            
            if len(self.neurons) > 0:
                stats['connection_density'] = len(self.connections) / (len(self.neurons) ** 2)
                
            if self.topology and len(self.neurons) > 0:
                stats['mean_in_degree'] = np.mean([self.topology.get_in_degree(i) for i in range(len(self.neurons))])
                stats['mean_out_degree'] = np.mean([self.topology.get_out_degree(i) for i in range(len(self.neurons))])
                stats['clustering_coefficient'] = self.topology.compute_clustering_coefficient()
                
            stats['spike_rate'] = self.spike_monitor.get_population_rate(window=100.0)
            
            return stats
            
        except Exception as e:
            logger.warning(f"Error computing network statistics: {e}")
            return {
                'num_neurons': len(self.neurons),
                'num_connections': len(self.connections),
                'current_time': self.current_time,
                'error': str(e)
            }
        
    def reset(self):
        """Reset all neurons and clear simulation state."""
        try:
            for neuron in self.neurons.values():
                neuron.reset_state()
                
            self.current_time = 0.0
            
            # Clear spike queue
            while not self.spike_queue.empty():
                self.spike_queue.get()
                
            # Reset monitors
            self.spike_monitor.reset()
            self.voltage_monitor.reset()
            
            # Reset step counter
            self._step_counter.set(0)
            
            logger.info("NeuromorphicCore reset to initial state")
            
        except Exception as e:
            logger.error(f"Error during reset: {e}")


# ===========================================================================================
# NEUROPLASTICITY ENGINE
# ===========================================================================================

class PlasticityRule(ABC):
    """Abstract base class for plasticity rules."""
    
    def __init__(self, parameters: PlasticityParameters):
        self.parameters = parameters
        
    @abstractmethod
    def update_weight(self, connection: SynapticConnection, 
                     pre_spike_times: List[float], 
                     post_spike_times: List[float],
                     dt: float) -> float:
        """Update synaptic weight based on spike timing."""
        pass


class STDPRule(PlasticityRule):
    """Spike-Timing Dependent Plasticity implementation.
    
    Implements: Δw_ij = A_+ * exp(-Δt/τ_+) if Δt > 0
                       -A_- * exp(Δt/τ_-) if Δt < 0
    """
    
    def update_weight(self, connection: SynapticConnection,
                     pre_spike_times: List[float],
                     post_spike_times: List[float],
                     dt: float) -> float:
        """Update weight using STDP rule with safe division."""
        weight_change = 0.0
        
        try:
            # For each pair of pre/post spikes, compute weight change
            for pre_time in pre_spike_times:
                for post_time in post_spike_times:
                    delta_t = post_time - pre_time
                    
                    if delta_t > 0:  # Post after pre -> LTP
                        if self.parameters.tau_plus > 0:
                            weight_change += (self.parameters.a_plus * 
                                            np.exp(-delta_t / self.parameters.tau_plus))
                    elif delta_t < 0:  # Pre after post -> LTD
                        if self.parameters.tau_minus > 0:
                            weight_change -= (self.parameters.a_minus * 
                                            np.exp(delta_t / self.parameters.tau_minus))
                                            
            # Update connection weight with bounds
            new_weight = connection.parameters.weight + weight_change * dt
            
            # Use config bounds if available
            if hasattr(connection, 'config') and connection.config:
                new_weight = np.clip(new_weight, connection.config.min_weight, connection.config.max_weight)
            else:
                new_weight = np.clip(new_weight, 0.0, 10.0)  # Default bounds
            
            connection.parameters.weight = new_weight
            
        except Exception as e:
            logger.warning(f"Error in STDP update: {e}")
            weight_change = 0.0
            
        return weight_change


class HomeostaticPlasticityRule(PlasticityRule):
    """Homeostatic plasticity to maintain target firing rates.
    
    Implements: τ_h * dθ/dt = α_h * (r - r_target)
    """
    
    def __init__(self, parameters: PlasticityParameters):
        super().__init__(parameters)
        self.firing_rate_estimates = {}
        
    def update_threshold(self, neuron: NeuronModel, recent_spike_times: List[float], 
                        current_time: float, dt: float) -> float:
        """Update firing threshold for homeostatic plasticity."""
        neuron_id = neuron.neuron_id
        
        # Estimate current firing rate (spikes in last 1000ms)
        window_start = current_time - 1000.0
        recent_spikes = [t for t in recent_spike_times if t >= window_start]
        current_rate = len(recent_spikes) / 1.0  # Hz
        
        # Update threshold
        rate_error = current_rate - self.parameters.target_rate
        threshold_change = (self.parameters.alpha_homeostatic * rate_error * dt / 
                          self.parameters.tau_homeostatic)
        
        # Update neuron threshold
        new_threshold = neuron.parameters.v_threshold - threshold_change
        neuron.parameters.v_threshold = np.clip(new_threshold, -80.0, -40.0)
        
        return threshold_change


class StructuralPlasticityRule(PlasticityRule):
    """Structural plasticity for connection creation and elimination."""
    
    def __init__(self, parameters: PlasticityParameters, core: NeuromorphicCore):
        super().__init__(parameters)
        self.core = core
        self.connection_utilities = {}
        
    def update_structural_plasticity(self, dt: float):
        """Perform structural plasticity updates."""
        connections_to_remove = []
        
        # Evaluate existing connections for elimination
        for conn_id, connection in self.core.connections.items():
            utility = self._compute_connection_utility(connection)
            
            if utility < self.parameters.elimination_threshold:
                if np.random.random() < 0.001:  # Small probability per timestep
                    connections_to_remove.append(conn_id)
                    
        # Remove weak connections
        for conn_id in connections_to_remove:
            self.core.remove_connection(conn_id)
            
        # Create new connections
        num_neurons = len(self.core.neurons)
        for _ in range(int(self.parameters.creation_probability * num_neurons * dt)):
            pre_id = np.random.randint(0, num_neurons)
            post_id = np.random.randint(0, num_neurons)
            
            if pre_id != post_id:
                # Check if connection already exists
                exists = any(c.pre_neuron_id == pre_id and c.post_neuron_id == post_id
                           for c in self.core.connections.values())
                           
                if not exists:
                    weight = np.random.lognormal(-1.0, 0.5)  # Small initial weight
                    params = SynapseParameters(weight=weight)
                    self.core.add_connection(pre_id, post_id, 
                                           SynapseType.CHEMICAL_EXCITATORY, params)
                                           
    def _compute_connection_utility(self, connection: SynapticConnection) -> float:
        """Compute utility measure for a connection."""
        # Combine weight magnitude and recent activity
        weight_contribution = abs(connection.parameters.weight)
        activity_contribution = len(connection.spike_times) / 100.0  # Normalize
        
        utility = 0.7 * weight_contribution + 0.3 * activity_contribution
        connection.utility_measure = utility
        
        return utility


class NeuroplasticityEngine:
    """Main neuroplasticity engine coordinating multiple plasticity mechanisms."""
    
    def __init__(self, config: CoreNeuralConfig = None):
        if isinstance(config, dict):
            # Convert dict to CoreNeuralConfig
            self.config = CoreNeuralConfig.from_dict(config)
        else:
            self.config = config or CoreNeuralConfig()
            
        self.plasticity_rules: Dict[str, PlasticityRule] = {}
        self.core: Optional[NeuromorphicCore] = None
        
        # Spike timing traces for STDP
        self.pre_traces: Dict[int, float] = {}
        self.post_traces: Dict[int, float] = {}
        
        # Recent spike history
        self.spike_history: Dict[int, deque] = {}
        
        # Parameters
        self.default_params = PlasticityParameters()
        
        logger.info("NeuroplasticityEngine initialized")
        
    def connect_to_core(self, core: NeuromorphicCore):
        """Connect to neuromorphic core system."""
        self.core = core
        
        # Initialize spike history for all neurons
        for neuron_id in core.neurons.keys():
            self.spike_history[neuron_id] = deque(maxlen=1000)
            self.pre_traces[neuron_id] = 0.0
            self.post_traces[neuron_id] = 0.0
            
        logger.info("NeuroplasticityEngine connected to NeuromorphicCore")
        
    def add_plasticity_rule(self, rule_name: str, rule_type: PlasticityType, 
                           parameters: PlasticityParameters = None):
        """Add a plasticity rule."""
        if parameters is None:
            parameters = self.default_params
            
        if rule_type == PlasticityType.STDP:
            rule = STDPRule(parameters)
        elif rule_type == PlasticityType.HOMEOSTATIC:
            rule = HomeostaticPlasticityRule(parameters)
        elif rule_type == PlasticityType.STRUCTURAL:
            rule = StructuralPlasticityRule(parameters, self.core)
        else:
            raise ValueError(f"Unknown plasticity rule type: {rule_type}")
            
        self.plasticity_rules[rule_name] = rule
        logger.info(f"Added plasticity rule: {rule_name} ({rule_type})")
        
    def step(self, dt: float):
        """Execute one plasticity update step."""
        if not self.core:
            return
            
        current_time = self.core.current_time
        
        # Update spike traces
        self._update_spike_traces(dt)
        
        # Apply each plasticity rule
        for rule_name, rule in self.plasticity_rules.items():
            if isinstance(rule, STDPRule):
                self._apply_stdp_rule(rule, dt)
            elif isinstance(rule, HomeostaticPlasticityRule):
                self._apply_homeostatic_rule(rule, current_time, dt)
            elif isinstance(rule, StructuralPlasticityRule):
                rule.update_structural_plasticity(dt)
                
    def _update_spike_traces(self, dt: float):
        """Update exponential traces for STDP."""
        tau_plus = self.default_params.tau_plus
        tau_minus = self.default_params.tau_minus
        
        # Decay traces
        for neuron_id in self.pre_traces:
            self.pre_traces[neuron_id] *= np.exp(-dt / tau_plus)
            self.post_traces[neuron_id] *= np.exp(-dt / tau_minus)
            
        # Add spikes from this timestep
        recent_spikes = self.core.spike_monitor.get_recent_spikes(self.core.current_time - dt)
        
        for neuron_id in recent_spikes:
            self.pre_traces[neuron_id] += 1.0
            self.post_traces[neuron_id] += 1.0
            
            # Update spike history
            if neuron_id in self.spike_history:
                self.spike_history[neuron_id].append(self.core.current_time)
                
    def _apply_stdp_rule(self, rule: STDPRule, dt: float):
        """Apply STDP to all connections."""
        for connection in self.core.connections.values():
            pre_id = connection.pre_neuron_id
            post_id = connection.post_neuron_id
            
            # Get recent spike times
            pre_spikes = list(self.spike_history.get(pre_id, []))
            post_spikes = list(self.spike_history.get(post_id, []))
            
            # Apply STDP rule
            if pre_spikes or post_spikes:
                rule.update_weight(connection, pre_spikes, post_spikes, dt)
                
    def _apply_homeostatic_rule(self, rule: HomeostaticPlasticityRule, 
                               current_time: float, dt: float):
        """Apply homeostatic plasticity to all neurons."""
        for neuron_id, neuron in self.core.neurons.items():
            recent_spikes = list(self.spike_history.get(neuron_id, []))
            rule.update_threshold(neuron, recent_spikes, current_time, dt)
            
    def get_plasticity_statistics(self) -> Dict[str, Any]:
        """Get statistics about plasticity state."""
        if not self.core:
            return {}
            
        weight_distribution = [conn.parameters.weight for conn in self.core.connections.values()]
        
        stats = {
            'num_plasticity_rules': len(self.plasticity_rules),
            'mean_weight': np.mean(weight_distribution) if weight_distribution else 0.0,
            'std_weight': np.std(weight_distribution) if weight_distribution else 0.0,
            'min_weight': np.min(weight_distribution) if weight_distribution else 0.0,
            'max_weight': np.max(weight_distribution) if weight_distribution else 0.0,
            'mean_pre_trace': np.mean(list(self.pre_traces.values())),
            'mean_post_trace': np.mean(list(self.post_traces.values()))
        }
        
        return stats


# ===========================================================================================
# SYNAPTIC PRUNING MODULE
# ===========================================================================================

class PruningStrategy(ABC):
    """Abstract base class for pruning strategies."""
    
    @abstractmethod
    def compute_pruning_score(self, connection: SynapticConnection, 
                            network_state: Dict[str, Any] = None ) -> float:
        """Compute pruning score (higher = more likely to prune)."""
        if network_state is None:
            network_state = {}
        pass


class WeightBasedPruning(PruningStrategy):
    """Prune connections based on weight magnitude."""
    
    def __init__(self, weight_threshold: float = 0.1):
        self.weight_threshold = weight_threshold
        
    def compute_pruning_score(self, connection: SynapticConnection, 
                            network_state: Dict[str, Any]) -> float:
        """Score based on inverse weight magnitude."""
        weight_magnitude = abs(connection.parameters.weight)
        return 1.0 / (weight_magnitude + 1e-6)


class ActivityBasedPruning(PruningStrategy):
    """Prune connections based on activity levels."""
    
    def __init__(self, activity_window: float = 1000.0):
        self.activity_window = activity_window
        
    def compute_pruning_score(self, connection: SynapticConnection,
                            network_state: Dict[str, Any]) -> float:
        """Score based on recent activity."""
        current_time = network_state.get('current_time', 0.0)
        cutoff_time = current_time - self.activity_window
        
        recent_spikes = [t for t in connection.spike_times if t >= cutoff_time]
        activity_level = len(recent_spikes)
        
        return 1.0 / (activity_level + 1.0)


class RedundancyPruning(PruningStrategy):
    """Prune redundant connections based on correlation."""
    
    def __init__(self, correlation_threshold: float = 0.8):
        self.correlation_threshold = correlation_threshold
        
    def compute_pruning_score(self, connection: SynapticConnection,
                            network_state: Dict[str, Any]) -> float:
        """Score based on redundancy with other connections."""
        # Simplified implementation - in practice would compute 
        # spike train correlations between similar connections
        return np.random.random()  # Placeholder for demonstration


class CompositePruning(PruningStrategy):
    """Composite pruning combining multiple strategies."""
    
    def __init__(self, strategies: List[Tuple[PruningStrategy, float]]):
        self.strategies = strategies  # List of (strategy, weight) tuples
        
    def compute_pruning_score(self, connection: SynapticConnection,
                            network_state: Dict[str, Any]) -> float:
        """Weighted combination of multiple pruning scores."""
        total_score = 0.0
        total_weight = 0.0
        
        for strategy, weight in self.strategies:
            score = strategy.compute_pruning_score(connection, network_state)
            total_score += weight * score
            total_weight += weight
            
        return total_score / total_weight if total_weight > 0 else 0.0


class SynapticPruningModule:
    """Main synaptic pruning system for optimizing network connectivity."""
    
    def __init__(self, config: CoreNeuralConfig = None):
        if isinstance(config, dict):
            self.config = CoreNeuralConfig.from_dict(config)
        else:
            self.config = config or CoreNeuralConfig()
            
        self.core: Optional[NeuromorphicCore] = None
        
        # Pruning strategies
        self.pruning_strategies: Dict[str, PruningStrategy] = {}
        
        # Pruning schedule parameters
        self.initial_pruning_rate = 0.1
        self.final_pruning_rate = 0.001
        self.pruning_decay = 0.99
        self.current_pruning_rate = self.initial_pruning_rate
        
        # Statistics
        self.pruning_history = []
        self.connections_pruned = 0
        
        logger.info("SynapticPruningModule initialized")
        
    def connect_to_core(self, core: NeuromorphicCore):
        """Connect to neuromorphic core system."""
        self.core = core
        
        # Set up default pruning strategies
        self._setup_default_strategies()
        
        logger.info("SynapticPruningModule connected to NeuromorphicCore")
        
    def _setup_default_strategies(self):
        """Set up default pruning strategies."""
        # Weight-based pruning
        weight_pruner = WeightBasedPruning(weight_threshold=0.05)
        self.pruning_strategies['weight_based'] = weight_pruner
        
        # Activity-based pruning
        activity_pruner = ActivityBasedPruning(activity_window=5000.0)
        self.pruning_strategies['activity_based'] = activity_pruner
        
        # Composite strategy combining weight and activity
        composite_pruner = CompositePruning([
            (weight_pruner, 0.7),
            (activity_pruner, 0.3)
        ])
        self.pruning_strategies['composite'] = composite_pruner
        
    def add_pruning_strategy(self, name: str, strategy: PruningStrategy):
        """Add a custom pruning strategy."""
        self.pruning_strategies[name] = strategy
        logger.info(f"Added pruning strategy: {name}")
        
    def step(self, dt: float):
        """Execute one pruning step."""
        if not self.core or not self.pruning_strategies:
            return
            
        # Determine how many connections to evaluate for pruning this step
        total_connections = len(self.core.connections)
        connections_to_evaluate = max(1, int(self.current_pruning_rate * total_connections * dt / 1000.0))
        
        if connections_to_evaluate == 0:
            return
            
        # Randomly sample connections to evaluate
        connection_ids = list(self.core.connections.keys())
        if len(connection_ids) < connections_to_evaluate:
            connections_to_evaluate = len(connection_ids)
            
        sampled_connections = np.random.choice(connection_ids, 
                                             connections_to_evaluate, 
                                             replace=False)
        
        # Evaluate connections for pruning
        network_state = {
            'current_time': self.core.current_time,
            'total_connections': total_connections,
            'mean_activity': self._compute_mean_network_activity()
        }
        
        connections_to_prune = []
        
        for conn_id in sampled_connections:
            connection = self.core.connections[conn_id]
            
            # Compute pruning scores from all strategies
            max_score = 0.0
            for strategy_name, strategy in self.pruning_strategies.items():
                score = strategy.compute_pruning_score(connection, network_state)
                max_score = max(max_score, score)
                
            # Probabilistic pruning based on score
            pruning_probability = self._compute_pruning_probability(max_score)
            
            if np.random.random() < pruning_probability:
                connections_to_prune.append(conn_id)
                
        # Execute pruning
        for conn_id in connections_to_prune:
            self.core.remove_connection(conn_id)
            self.connections_pruned += 1
            
        # Update pruning rate (developmental decrease)
        self.current_pruning_rate = max(self.final_pruning_rate,
                                      self.current_pruning_rate * self.pruning_decay)
        
        # Record statistics
        if connections_to_prune:
            self.pruning_history.append({
                'time': self.core.current_time,
                'connections_pruned': len(connections_to_prune),
                'pruning_rate': self.current_pruning_rate,
                'total_connections': total_connections - len(connections_to_prune)
            })
            
            logger.debug(f"Pruned {len(connections_to_prune)} connections at time {self.core.current_time}")
            
    def _compute_mean_network_activity(self) -> float:
        """Compute mean network activity level."""
        if not hasattr(self.core, 'spike_monitor'):
            return 0.0
            
        return self.core.spike_monitor.get_population_rate(window=100.0)
        
    def _compute_pruning_probability(self, pruning_score: float) -> float:
        """Convert pruning score to probability."""
        # Sigmoid function to convert score to probability
        return 1.0 / (1.0 + np.exp(-5.0 * (pruning_score - 0.5)))
        
    def get_pruning_statistics(self) -> Dict[str, Any]:
        """Get comprehensive pruning statistics."""
        stats = {
            'total_connections_pruned': self.connections_pruned,
            'current_pruning_rate': self.current_pruning_rate,
            'num_strategies': len(self.pruning_strategies),
            'strategy_names': list(self.pruning_strategies.keys())
        }
        
        if self.pruning_history:
            recent_pruning = [entry['connections_pruned'] for entry in self.pruning_history[-100:]]
            stats['recent_pruning_mean'] = np.mean(recent_pruning)
            stats['recent_pruning_std'] = np.std(recent_pruning)
            
        return stats


# ===========================================================================================
# NEUROMODULATION SYSTEM
# ===========================================================================================

class Modulator:
    """Base class for neuromodulator implementation."""
    
    def __init__(self, modulator_type: ModulatorType, 
                 concentration: float = 0.0,
                 tau: float = 1000.0):
        self.modulator_type = modulator_type
        self.concentration = concentration
        self.tau = tau  # Time constant for concentration dynamics
        self.input_sources = []
        
    def update(self, dt: float, inputs: List[float] = None) -> float:
        """Update modulator concentration.
        
        Implements: τ * dc/dt = -c + Σ I_k
        """
        if inputs is None:
            inputs = []
            
        total_input = sum(inputs)
        
        # Update concentration using exponential dynamics
        dc_dt = (-self.concentration + total_input) / self.tau
        self.concentration += dc_dt * dt
        
        # Keep concentration non-negative
        self.concentration = max(0.0, self.concentration)
        
        return self.concentration
        
    def add_input_source(self, source_id: str, weight: float = 1.0):
        """Add an input source for this modulator."""
        self.input_sources.append({'id': source_id, 'weight': weight})


class DopamineModulator(Modulator):
    """Dopamine modulator for reward and learning signals."""
    
    def __init__(self, **kwargs):
        super().__init__(ModulatorType.DOPAMINE, **kwargs)
        self.reward_prediction_error = 0.0
        self.baseline_firing = 4.0  # Hz, typical dopamine neuron baseline
        
    def compute_reward_signal(self, predicted_reward: float, 
                            actual_reward: float) -> float:
        """Compute reward prediction error for dopamine release."""
        self.reward_prediction_error = actual_reward - predicted_reward
        
        # Convert to dopamine concentration change
        # Positive RPE increases dopamine, negative RPE decreases it
        dopamine_change = 0.1 * self.reward_prediction_error
        
        return dopamine_change


class SerotoninModulator(Modulator):
    """Serotonin modulator for mood and inhibitory control."""
    
    def __init__(self, **kwargs):
        super().__init__(ModulatorType.SEROTONIN, **kwargs)
        self.inhibitory_strength = 1.0
        
    def compute_inhibitory_modulation(self, base_inhibition: float) -> float:
        """Modulate inhibitory strength based on serotonin levels."""
        modulation_factor = 1.0 + 0.5 * self.concentration
        return base_inhibition * modulation_factor


class NorepinephrineModulator(Modulator):
    """Norepinephrine modulator for arousal and attention."""
    
    def __init__(self, **kwargs):
        super().__init__(ModulatorType.NOREPINEPHRINE, **kwargs)
        self.arousal_level = 0.5
        
    def compute_attention_modulation(self, base_attention: float,
                                   salience_signal: float) -> float:
        """Modulate attention based on norepinephrine and salience."""
        attention_boost = self.concentration * salience_signal
        return base_attention + 0.3 * attention_boost


class AcetylcholineModulator(Modulator):
    """Acetylcholine modulator for learning rate and plasticity."""
    
    def __init__(self, **kwargs):
        super().__init__(ModulatorType.ACETYLCHOLINE, **kwargs)
        self.learning_enhancement = 1.0
        
    def compute_plasticity_modulation(self, base_learning_rate: float) -> float:
        """Modulate learning rate based on acetylcholine levels."""
        enhancement_factor = 1.0 + 2.0 * self.concentration
        return base_learning_rate * enhancement_factor


class NeuromodulationSystem:
    """Comprehensive neuromodulation system managing multiple modulators."""
    
    def __init__(self, config: CoreNeuralConfig = None):
        if isinstance(config, dict):
            self.config = CoreNeuralConfig.from_dict(config)
        else:
            self.config = config or CoreNeuralConfig()
            
        self.core: Optional[NeuromorphicCore] = None
        
        # Initialize modulators
        self.modulators: Dict[str, Modulator] = {
            'dopamine': DopamineModulator(tau=100.0),
            'serotonin': SerotoninModulator(tau=500.0),
            'norepinephrine': NorepinephrineModulator(tau=200.0),
            'acetylcholine': AcetylcholineModulator(tau=50.0)
        }
        
        # Modulation effects on neural parameters
        self.modulation_effects = {}
        
        # Input processing for modulators
        self.reward_signal = 0.0
        self.stress_signal = 0.0
        self.novelty_signal = 0.0
        self.attention_signal = 0.0
        
        logger.info("NeuromodulationSystem initialized")
        
    def connect_to_core(self, core: NeuromorphicCore):
        """Connect to neuromorphic core system."""
        self.core = core
        self._setup_modulation_effects()
        logger.info("NeuromodulationSystem connected to NeuromorphicCore")
        
    def _setup_modulation_effects(self):
        """Set up how modulators affect neural parameters."""
        # Dopamine effects
        self.modulation_effects['dopamine'] = {
            'learning_rate_multiplier': lambda c: 1.0 + 2.0 * c,
            'threshold_adjustment': lambda c: -5.0 * c,  # Lower threshold = more excitable
            'synaptic_weight_multiplier': lambda c: 1.0 + 0.5 * c
        }
        
        # Serotonin effects  
        self.modulation_effects['serotonin'] = {
            'inhibition_multiplier': lambda c: 1.0 + 1.0 * c,
            'threshold_adjustment': lambda c: 2.0 * c,  # Higher threshold = less excitable
            'learning_rate_multiplier': lambda c: 1.0 - 0.3 * c
        }
        
        # Norepinephrine effects
        self.modulation_effects['norepinephrine'] = {
            'gain_multiplier': lambda c: 1.0 + 3.0 * c,
            'noise_reduction': lambda c: 1.0 - 0.5 * c,
            'attention_focus': lambda c: 1.0 + 2.0 * c
        }
        
        # Acetylcholine effects
        self.modulation_effects['acetylcholine'] = {
            'learning_rate_multiplier': lambda c: 1.0 + 4.0 * c,
            'sensory_enhancement': lambda c: 1.0 + 1.5 * c,
            'memory_consolidation': lambda c: c
        }
        
    def step(self, dt: float):
        """Update neuromodulation system."""
        if not self.core:
            return
            
        # Update environmental signals
        self._update_environmental_signals()
        
        # Update each modulator
        for name, modulator in self.modulators.items():
            inputs = self._compute_modulator_inputs(name)
            modulator.update(dt, inputs)
            
        # Apply modulation effects to neurons and connections
        self._apply_modulation_effects()
        
    def _update_environmental_signals(self):
        """Update environmental signals that drive modulators."""
        if not self.core:
            return
            
        # Compute reward signal from recent network activity
        recent_activity = self.core.spike_monitor.get_population_rate(window=50.0)
        target_activity = 20.0  # Target firing rate in Hz
        self.reward_signal = 1.0 - abs(recent_activity - target_activity) / target_activity
        
        # Compute novelty signal from activity variance
        activity_history = self.core.spike_monitor.get_activity_history(window=1000.0)
        if len(activity_history) > 10:
            self.novelty_signal = np.std(activity_history) / np.mean(activity_history)
        else:
            self.novelty_signal = 0.0
            
        # Stress signal based on extreme activity levels
        if recent_activity > 50.0 or recent_activity < 1.0:
            self.stress_signal = min(1.0, self.stress_signal + 0.1)
        else:
            self.stress_signal = max(0.0, self.stress_signal - 0.05)
            
        # Attention signal (simplified - would be more complex in full system)
        self.attention_signal = 0.5
        
    def _compute_modulator_inputs(self, modulator_name: str) -> List[float]:
        """Compute input signals for a specific modulator."""
        inputs = []
        
        if modulator_name == 'dopamine':
            # Dopamine responds to reward prediction error
            dopamine_mod = self.modulators['dopamine']
            if isinstance(dopamine_mod, DopamineModulator):
                rpe_signal = dopamine_mod.compute_reward_signal(0.5, self.reward_signal)
                inputs.append(rpe_signal)
                
        elif modulator_name == 'serotonin':
            # Serotonin inversely related to stress
            inputs.append(1.0 - self.stress_signal)
            
        elif modulator_name == 'norepinephrine':
            # Norepinephrine responds to novelty and attention demands
            inputs.append(0.7 * self.novelty_signal + 0.3 * self.attention_signal)
            
        elif modulator_name == 'acetylcholine':
            # Acetylcholine responds to attention and novelty
            inputs.append(0.8 * self.attention_signal + 0.2 * self.novelty_signal)
            
        return inputs
        
    def _apply_modulation_effects(self):
        """Apply modulation effects to neural parameters."""
        if not self.core:
            return
            
        # Get current modulator concentrations
        concentrations = {name: mod.concentration for name, mod in self.modulators.items()}
        
        # Apply effects to neurons
        for neuron_id, neuron in self.core.neurons.items():
            self._modulate_neuron(neuron, concentrations)
            
        # Apply effects to connections
        for connection in self.core.connections.values():
            self._modulate_connection(connection, concentrations)
            
    def _modulate_neuron(self, neuron: NeuronModel, concentrations: Dict[str, float]):
        """Apply modulation to a single neuron."""
        # Compute threshold adjustments
        threshold_adjustment = 0.0
        
        for mod_name, concentration in concentrations.items():
            if mod_name in self.modulation_effects:
                effects = self.modulation_effects[mod_name]
                if 'threshold_adjustment' in effects:
                    threshold_adjustment += effects['threshold_adjustment'](concentration)
                    
        # Apply threshold modulation
        base_threshold = -50.0  # Default threshold
        neuron.parameters.v_threshold = base_threshold + threshold_adjustment
        
        # Clamp to physiological bounds
        neuron.parameters.v_threshold = np.clip(neuron.parameters.v_threshold, -80.0, -30.0)
        
    def _modulate_connection(self, connection: SynapticConnection, 
                           concentrations: Dict[str, float]):
        """Apply modulation to a synaptic connection."""
        # Compute weight multiplier
        weight_multiplier = 1.0
        
        for mod_name, concentration in concentrations.items():
            if mod_name in self.modulation_effects:
                effects = self.modulation_effects[mod_name]
                if 'synaptic_weight_multiplier' in effects:
                    weight_multiplier *= effects['synaptic_weight_multiplier'](concentration)
                    
        # Store original weight if not already stored
        if not hasattr(connection, 'base_weight'):
            connection.base_weight = connection.parameters.weight
            
        # Apply modulation
        connection.parameters.weight = connection.base_weight * weight_multiplier
        
    def set_environmental_signal(self, signal_type: str, value: float):
        """Manually set environmental signals for testing."""
        if signal_type == 'reward':
            self.reward_signal = np.clip(value, -1.0, 1.0)
        elif signal_type == 'stress':
            self.stress_signal = np.clip(value, 0.0, 1.0)
        elif signal_type == 'novelty':
            self.novelty_signal = np.clip(value, 0.0, 1.0)
        elif signal_type == 'attention':
            self.attention_signal = np.clip(value, 0.0, 1.0)
            
    def get_modulation_state(self) -> Dict[str, Any]:
        """Get current state of all modulators."""
        state = {
            'concentrations': {name: mod.concentration for name, mod in self.modulators.items()},
            'environmental_signals': {
                'reward': self.reward_signal,
                'stress': self.stress_signal,
                'novelty': self.novelty_signal,
                'attention': self.attention_signal
            }
        }
        
        return state


# ===========================================================================================
# BIOLOGICAL TIMING CIRCUITS - FIXED
# ===========================================================================================

class NeuralOscillator:
    """Base class for neural oscillators implementing brain rhythms."""
    
    def __init__(self, frequency: float, amplitude: float = 1.0, 
                 phase: float = 0.0, oscillation_type: OscillationType = None):
        self.frequency = frequency  # Hz
        self.amplitude = amplitude
        self.phase = phase
        self.oscillation_type = oscillation_type
        
        # State variables
        self.current_phase = phase
        self.current_amplitude = amplitude
        self.coupled_oscillators = []
        
        # Coupling parameters
        self.coupling_strength = 0.1
        self.noise_level = 0.01
        
    def update(self, dt: float, external_input: float = 0.0) -> float:
        """Update oscillator state and return current output."""
        try:
            # Compute coupling from other oscillators
            coupling_term = 0.0
            for other_osc, coupling_weight in self.coupled_oscillators:
                phase_diff = other_osc.current_phase - self.current_phase
                coupling_term += coupling_weight * np.sin(phase_diff)
                
            # Add noise
            noise = self.noise_level * np.random.normal()
            
            # Update phase
            omega = 2 * np.pi * self.frequency
            dphase_dt = omega + self.coupling_strength * coupling_term + noise + external_input
            self.current_phase += dphase_dt * dt
            
            # Keep phase in [0, 2π]
            self.current_phase = self.current_phase % (2 * np.pi)
            
            # Compute output
            output = self.current_amplitude * np.sin(self.current_phase)
            
            return output
            
        except Exception as e:
            logger.warning(f"Error updating oscillator: {e}")
            return 0.0
        
    def couple_to(self, other_oscillator: 'NeuralOscillator', 
                  coupling_weight: float = 0.1):
        """Couple this oscillator to another."""
        self.coupled_oscillators.append((other_oscillator, coupling_weight))
        
    def set_amplitude_modulation(self, modulation_frequency: float, 
                               modulation_depth: float):
        """Set amplitude modulation parameters."""
        self.amplitude_modulation_freq = modulation_frequency
        self.amplitude_modulation_depth = modulation_depth
        
    def get_instantaneous_frequency(self) -> float:
        """Get current instantaneous frequency."""
        return self.frequency  # Could be made dynamic


class DeltaOscillator(NeuralOscillator):
    """Delta rhythm oscillator (1-4 Hz) - associated with deep sleep."""
    
    def __init__(self, frequency: float = 2.0, **kwargs):
        super().__init__(frequency, oscillation_type=OscillationType.DELTA, **kwargs)
        self.slow_wave_component = 0.0
        
    def update(self, dt: float, external_input: float = 0.0) -> float:
        """Update delta oscillator with slow wave dynamics."""
        # Add slow wave component
        self.slow_wave_component = 0.3 * np.sin(0.5 * self.current_phase)
        base_output = super().update(dt, external_input)
        
        return base_output + self.slow_wave_component


class ThetaOscillator(NeuralOscillator):
    """Theta rhythm oscillator (4-8 Hz) - associated with memory and navigation."""
    
    def __init__(self, frequency: float = 6.0, **kwargs):
        super().__init__(frequency, oscillation_type=OscillationType.THETA, **kwargs)
        self.memory_modulation = 1.0
        
    def update(self, dt: float, external_input: float = 0.0) -> float:
        """Update theta oscillator with memory-related modulation."""
        # Memory-dependent amplitude modulation
        current_amplitude = self.amplitude * self.memory_modulation
        original_amplitude = self.amplitude
        self.amplitude = current_amplitude
        
        output = super().update(dt, external_input)
        
        self.amplitude = original_amplitude  # Restore original
        return output


class AlphaOscillator(NeuralOscillator):
    """Alpha rhythm oscillator (8-12 Hz) - associated with relaxed awareness."""
    
    def __init__(self, frequency: float = 10.0, **kwargs):
        super().__init__(frequency, oscillation_type=OscillationType.ALPHA, **kwargs)
        self.attention_suppression = 1.0
        
    def update(self, dt: float, external_input: float = 0.0) -> float:
        """Update alpha oscillator with attention-dependent suppression."""
        # Alpha is suppressed by attention
        suppressed_amplitude = self.amplitude * (1.0 - 0.5 * self.attention_suppression)
        original_amplitude = self.amplitude
        self.amplitude = suppressed_amplitude
        
        output = super().update(dt, external_input)
        
        self.amplitude = original_amplitude
        return output


class BetaOscillator(NeuralOscillator):
    """Beta rhythm oscillator (12-30 Hz) - associated with active concentration."""
    
    def __init__(self, frequency: float = 20.0, **kwargs):
        super().__init__(frequency, oscillation_type=OscillationType.BETA, **kwargs)
        self.motor_modulation = 1.0
        
    def update(self, dt: float, external_input: float = 0.0) -> float:
        """Update beta oscillator with motor-related modulation."""
        # Beta increases with motor activity
        enhanced_amplitude = self.amplitude * (1.0 + 0.3 * self.motor_modulation)
        original_amplitude = self.amplitude
        self.amplitude = enhanced_amplitude
        
        output = super().update(dt, external_input)
        
        self.amplitude = original_amplitude
        return output


class GammaOscillator(NeuralOscillator):
    """Gamma rhythm oscillator (30-100 Hz) - associated with conscious awareness."""
    
    def __init__(self, frequency: float = 40.0, **kwargs):
        super().__init__(frequency, oscillation_type=OscillationType.GAMMA, **kwargs)
        self.binding_strength = 1.0
        
    def update(self, dt: float, external_input: float = 0.0) -> float:
        """Update gamma oscillator with binding-related dynamics."""
        # Gamma amplitude depends on feature binding requirements
        binding_amplitude = self.amplitude * self.binding_strength
        original_amplitude = self.amplitude
        self.amplitude = binding_amplitude
        
        output = super().update(dt, external_input)
        
        self.amplitude = original_amplitude
        return output


class PhaseAmplitudeCoupling:
    """Implements cross-frequency coupling with fixed attribute access."""
    
    def __init__(self, slow_oscillator: NeuralOscillator, 
                 fast_oscillator: NeuralOscillator,
                 coupling_strength: float = 0.5):
        self.slow_oscillator = slow_oscillator
        self.fast_oscillator = fast_oscillator
        self.coupling_strength = coupling_strength
        
    def update_coupling(self):
        """Update phase-amplitude coupling with safe attribute access."""
        try:
            # Fast oscillator amplitude modulated by slow oscillator phase
            slow_phase = self.slow_oscillator.current_phase
            phase_modulation = np.cos(slow_phase)
            
            # Apply coupling - Fixed attribute access
            amplitude_modulation = 1.0 + self.coupling_strength * phase_modulation
            self.fast_oscillator.current_amplitude = (self.fast_oscillator.amplitude * 
                                                     amplitude_modulation)
        except Exception as e:
            logger.warning(f"Error updating phase-amplitude coupling: {e}")


class BiologicalTimingCircuits:
    """Biological timing system with fixed initialization order."""
    
    def __init__(self, config: CoreNeuralConfig = None):
        if isinstance(config, dict):
            self.config = CoreNeuralConfig.from_dict(config)
        else:
            self.config = config or CoreNeuralConfig()
            
        self.core: Optional[NeuromorphicCore] = None
        
        # Initialize oscillators FIRST
        self.oscillators: Dict[str, NeuralOscillator] = {}
        self._initialize_default_oscillators()
        
        # THEN set up cross-frequency coupling
        self.pac_couplings = []
        self._setup_cross_frequency_coupling()
        
        # Oscillator states and history
        self.oscillator_outputs = {}
        self.phase_history = {name: deque(maxlen=1000) for name in self.oscillators.keys()}
        
        # Global modulation signals
        self.arousal_level = 0.5
        self.sleep_stage = 'wake'
        self.attention_level = 0.5
        
        logger.info("BiologicalTimingCircuits initialized with fixed initialization order")
        
    def _initialize_default_oscillators(self):
        """Initialize default oscillators."""
        self.oscillators = {
            'delta': DeltaOscillator(frequency=2.0, amplitude=1.0),
            'theta': ThetaOscillator(frequency=6.0, amplitude=0.8),
            'alpha': AlphaOscillator(frequency=10.0, amplitude=0.6),
            'beta': BetaOscillator(frequency=20.0, amplitude=0.4),
            'gamma': GammaOscillator(frequency=40.0, amplitude=0.3)
        }
        
    def _setup_cross_frequency_coupling(self):
        """Set up phase-amplitude coupling between oscillators."""
        try:
            # Only set up coupling if oscillators exist
            if 'theta' in self.oscillators and 'gamma' in self.oscillators:
                theta_gamma_coupling = PhaseAmplitudeCoupling(
                    self.oscillators['theta'], 
                    self.oscillators['gamma'], 
                    coupling_strength=0.3
                )
                self.pac_couplings.append(theta_gamma_coupling)
            
            if 'alpha' in self.oscillators and 'beta' in self.oscillators:
                alpha_beta_coupling = PhaseAmplitudeCoupling(
                    self.oscillators['alpha'],
                    self.oscillators['beta'],
                    coupling_strength=0.2
                )
                self.pac_couplings.append(alpha_beta_coupling)
            
            if 'delta' in self.oscillators and 'theta' in self.oscillators:
                delta_theta_coupling = PhaseAmplitudeCoupling(
                    self.oscillators['delta'],
                    self.oscillators['theta'],
                    coupling_strength=0.4
                )
                self.pac_couplings.append(delta_theta_coupling)
                
        except Exception as e:
            logger.warning(f"Error setting up cross-frequency coupling: {e}")
        
    def connect_to_core(self, core: NeuromorphicCore):
        """Connect to neuromorphic core system."""
        self.core = core
        logger.info("BiologicalTimingCircuits connected to NeuromorphicCore")
        
    def connect_to_neuromodulation(self, neuromodulation: NeuromodulationSystem):
        """Connect to neuromodulation system for rhythm regulation."""
        self.neuromodulation = neuromodulation
        logger.info("BiologicalTimingCircuits connected to NeuromodulationSystem")
        
    def step(self, dt: float):
        """Update all oscillators and their interactions."""
        # Update global modulation based on neuromodulation
        if hasattr(self, 'neuromodulation'):
            self._update_global_modulation()
            
        # Update individual oscillators
        for name, oscillator in self.oscillators.items():
            # Compute external input based on global state
            external_input = self._compute_oscillator_input(name)
            
            # Update oscillator
            output = oscillator.update(dt, external_input)
            self.oscillator_outputs[name] = output
            
            # Store phase history
            self.phase_history[name].append(oscillator.current_phase)
            
        # Update cross-frequency coupling
        for pac_coupling in self.pac_couplings:
            pac_coupling.update_coupling()
            
        # Apply oscillatory modulation to neural network
        if self.core:
            self._apply_oscillatory_modulation()
            
    def _update_global_modulation(self):
        """Update global modulation signals from neuromodulation."""
        if not hasattr(self, 'neuromodulation'):
            return
            
        modulation_state = self.neuromodulation.get_modulation_state()
        concentrations = modulation_state['concentrations']
        
        # Arousal level influenced by norepinephrine and dopamine
        self.arousal_level = (0.6 * concentrations['norepinephrine'] + 
                             0.4 * concentrations['dopamine'])
        
        # Attention level influenced by acetylcholine and norepinephrine
        self.attention_level = (0.7 * concentrations['acetylcholine'] + 
                               0.3 * concentrations['norepinephrine'])
                               
        # Sleep stage determination (simplified)
        if self.arousal_level < 0.2:
            self.sleep_stage = 'deep_sleep'
        elif self.arousal_level < 0.4:
            self.sleep_stage = 'light_sleep'
        else:
            self.sleep_stage = 'wake'
            
    def _compute_oscillator_input(self, oscillator_name: str) -> float:
        """Compute external input for a specific oscillator."""
        input_signal = 0.0
        
        if oscillator_name == 'delta':
            # Delta enhanced during sleep
            if self.sleep_stage in ['light_sleep', 'deep_sleep']:
                input_signal = 0.5 * (1.0 - self.arousal_level)
                
        elif oscillator_name == 'theta':
            # Theta enhanced during memory tasks and REM sleep
            input_signal = 0.3 * self.attention_level
            
        elif oscillator_name == 'alpha':
            # Alpha present during relaxed wakefulness
            if self.sleep_stage == 'wake' and self.attention_level < 0.5:
                input_signal = 0.4 * (1.0 - self.attention_level)
                
        elif oscillator_name == 'beta':
            # Beta enhanced during active concentration
            if self.sleep_stage == 'wake':
                input_signal = 0.6 * self.attention_level
                
        elif oscillator_name == 'gamma':
            # Gamma enhanced during conscious processing
            if self.sleep_stage == 'wake':
                input_signal = 0.5 * (self.attention_level + self.arousal_level) / 2
                
        return input_signal
        
    def _apply_oscillatory_modulation(self):
        """Apply oscillatory modulation to neural network."""
        if not self.core:
            return
            
        # Phase-dependent plasticity modulation
        gamma_phase = self.oscillators['gamma'].current_phase
        theta_phase = self.oscillators['theta'].current_phase
        
        # Modulate learning rates based on gamma phase
        gamma_modulation = 1.0 + 0.3 * np.cos(gamma_phase)
        
        # Apply to plasticity engine if available
        if hasattr(self.core, 'plasticity_engine') and self.core.plasticity_engine:
            # This would require integration with plasticity engine
            pass
            
        # Coherence-based communication enhancement
        alpha_output = self.oscillator_outputs.get('alpha', 0.0)
        beta_output = self.oscillator_outputs.get('beta', 0.0)
        
        # Modulate effective connectivity based on oscillatory coherence
        coherence_factor = 1.0 + 0.2 * alpha_output * beta_output
        
        # Apply to connections (simplified implementation)
        for connection in self.core.connections.values():
            if not hasattr(connection, 'base_conductance'):
                connection.base_conductance = connection.parameters.max_conductance
            connection.parameters.max_conductance = (connection.base_conductance * 
                                                   coherence_factor)
                                                   
    def get_oscillatory_state(self) -> Dict[str, Any]:
        """Get current state of all oscillators."""
        state = {
            'frequencies': {name: osc.frequency for name, osc in self.oscillators.items()},
            'phases': {name: osc.current_phase for name, osc in self.oscillators.items()},
            'amplitudes': {name: osc.current_amplitude for name, osc in self.oscillators.items()},
            'outputs': self.oscillator_outputs.copy(),
            'global_state': {
                'arousal_level': self.arousal_level,
                'sleep_stage': self.sleep_stage,
                'attention_level': self.attention_level
            }
        }
        
        return state
        
    def compute_spectral_power(self, window_size: int = 512) -> Dict[str, float]:
        """Compute spectral power for each oscillator."""
        power_spectrum = {}
        
        for name, phase_history in self.phase_history.items():
            if len(phase_history) >= window_size:
                # Convert phase to signal
                recent_phases = np.array(list(phase_history)[-window_size:])
                signal = np.sin(recent_phases)
                
                # Compute power spectral density
                frequencies, psd = scipy.signal.welch(signal, nperseg=min(256, len(signal)))
                
                # Find peak power
                peak_power = np.max(psd)
                power_spectrum[name] = peak_power
            else:
                power_spectrum[name] = 0.0
                
        return power_spectrum


# ===========================================================================================
# MONITORING AND ANALYSIS TOOLS WITH CLEANUP
# ===========================================================================================

class SpikeMonitor:
    """Monitor and analyze spiking activity with memory management."""
    
    def __init__(self, config: CoreNeuralConfig = None):
        self.config = config or CoreNeuralConfig()
        self.spike_times = defaultdict(list)
        self.spike_counts = defaultdict(int)
        self.population_activity = []
        self.time_stamps = []
        self._lock = threading.Lock() if self.config.enable_threading else None
        
    def record_spikes(self, current_time: float, spike_list: List[int]):
        """Record spikes for this timestep with thread safety."""
        if self._lock:
            with self._lock:
                self._record_spikes_internal(current_time, spike_list)
        else:
            self._record_spikes_internal(current_time, spike_list)
            
    def _record_spikes_internal(self, current_time: float, spike_list: List[int]):
        self.time_stamps.append(current_time)
        self.population_activity.append(len(spike_list))
        
        for neuron_id in spike_list:
            self.spike_times[neuron_id].append(current_time)
            self.spike_counts[neuron_id] += 1
            
    def cleanup(self, threshold_time: float):
        """Clean up old spike data to prevent memory leaks."""
        if self._lock:
            with self._lock:
                self._cleanup_internal(threshold_time)
        else:
            self._cleanup_internal(threshold_time)
            
    def _cleanup_internal(self, threshold_time: float):
        # Clean up spike times
        for neuron_id in list(self.spike_times.keys()):
            self.spike_times[neuron_id] = [
                t for t in self.spike_times[neuron_id] if t >= threshold_time
            ]
            
        # Clean up population activity
        cutoff_index = 0
        for i, t in enumerate(self.time_stamps):
            if t >= threshold_time:
                cutoff_index = i
                break
                
        if cutoff_index > 0:
            self.time_stamps = self.time_stamps[cutoff_index:]
            self.population_activity = self.population_activity[cutoff_index:]
        
    def get_firing_rate(self, neuron_id: int, window: float = 1000.0) -> float:
        """Get firing rate for a specific neuron with error handling."""
        try:
            if neuron_id not in self.spike_times:
                return 0.0
                
            if not self.time_stamps:
                return 0.0
                
            current_time = self.time_stamps[-1]
            cutoff_time = current_time - window
            
            recent_spikes = [t for t in self.spike_times[neuron_id] if t >= cutoff_time]
            return len(recent_spikes) * 1000.0 / window  # Convert to Hz
            
        except Exception as e:
            logger.warning(f"Error computing firing rate for neuron {neuron_id}: {e}")
            return 0.0
        
    def get_population_rate(self, window: float = 1000.0) -> float:
        """Get population firing rate with error handling."""
        try:
            if not self.time_stamps:
                return 0.0
                
            current_time = self.time_stamps[-1]
            cutoff_time = current_time - window
            
            # Find indices in time window
            start_idx = 0
            for i, t in enumerate(self.time_stamps):
                if t >= cutoff_time:
                    start_idx = i
                    break
                    
            # Sum population activity in window
            if start_idx < len(self.population_activity):
                total_spikes = sum(self.population_activity[start_idx:])
                return total_spikes * 1000.0 / window  # Convert to Hz
            else:
                return 0.0
                
        except Exception as e:
            logger.warning(f"Error computing population rate: {e}")
            return 0.0
            
    def get_recent_spikes(self, cutoff_time: float) -> List[int]:
        """Get neurons that spiked since cutoff_time."""
        recent_spikes = []
        try:
            for neuron_id, spike_times in self.spike_times.items():
                if spike_times and spike_times[-1] >= cutoff_time:
                    recent_spikes.append(neuron_id)
        except Exception as e:
            logger.warning(f"Error getting recent spikes: {e}")
        return recent_spikes
        
    def get_activity_history(self, window: float = 1000.0) -> List[float]:
        """Get activity history within time window."""
        try:
            if not self.time_stamps:
                return []
                
            current_time = self.time_stamps[-1]
            cutoff_time = current_time - window
            
            # Find indices in time window
            start_idx = 0
            for i, t in enumerate(self.time_stamps):
                if t >= cutoff_time:
                    start_idx = i
                    break
                    
            return self.population_activity[start_idx:]
            
        except Exception as e:
            logger.warning(f"Error getting activity history: {e}")
            return []
            
    def reset(self):
        """Reset monitoring data."""
        if self._lock:
            with self._lock:
                self._reset_internal()
        else:
            self._reset_internal()
            
    def _reset_internal(self):
        self.spike_times.clear()
        self.spike_counts.clear()
        self.population_activity.clear()
        self.time_stamps.clear()


class VoltageMonitor:
    """Monitor membrane potentials with memory management."""
    
    def __init__(self, config: CoreNeuralConfig = None):
        self.config = config or CoreNeuralConfig()
        max_history = self.config.max_spike_history
        self.voltage_traces = defaultdict(lambda: deque(maxlen=max_history))
        self.time_stamps = deque(maxlen=max_history)
        self._lock = threading.Lock() if self.config.enable_threading else None
        
    def record_voltages(self, current_time: float, voltage_dict: Dict[int, float]):
        """Record voltages for this timestep with thread safety."""
        if self._lock:
            with self._lock:
                self._record_voltages_internal(current_time, voltage_dict)
        else:
            self._record_voltages_internal(current_time, voltage_dict)
            
    def _record_voltages_internal(self, current_time: float, voltage_dict: Dict[int, float]):
        self.time_stamps.append(current_time)
        
        for neuron_id, voltage in voltage_dict.items():
            self.voltage_traces[neuron_id].append(voltage)
            
    def cleanup(self, threshold_time: float):
        """Clean up old voltage data."""
        # Deques automatically maintain max length, so minimal cleanup needed
        pass
        
    def get_voltage_trace(self, neuron_id: int) -> Tuple[List[float], List[float]]:
        """Get voltage trace for a specific neuron."""
        if self._lock:
            with self._lock:
                return self._get_voltage_trace_internal(neuron_id)
        else:
            return self._get_voltage_trace_internal(neuron_id)
            
    def _get_voltage_trace_internal(self, neuron_id: int) -> Tuple[List[float], List[float]]:
        times = list(self.time_stamps)
        voltages = list(self.voltage_traces[neuron_id])
        
        # Ensure both lists have same length
        min_len = min(len(times), len(voltages))
        return times[-min_len:], voltages[-min_len:]
        
    def reset(self):
        """Reset monitoring data."""
        if self._lock:
            with self._lock:
                self._reset_internal()
        else:
            self._reset_internal()
            
    def _reset_internal(self):
        self.voltage_traces.clear()
        self.time_stamps.clear()


# ===========================================================================================
# NETWORK CONSTRUCTION UTILITIES
# ===========================================================================================

def create_balanced_network(core: NeuromorphicCore, 
                          dimensions: Tuple[int, int, int],
                          connection_probability: float = 0.1,
                          excitatory_ratio: float = 0.8) -> Dict[str, Any]:
    """Create a balanced excitatory-inhibitory network.
    
    Args:
        core: NeuromorphicCore instance
        dimensions: (num_excitatory, num_inhibitory, num_adaptive)
        connection_probability: Connection probability
        excitatory_ratio: Ratio of excitatory connections
        
    Returns:
        Dictionary with network information
    """
    num_exc, num_inh, num_ada = dimensions
    
    # Add excitatory neurons
    exc_neurons = []
    for _ in range(num_exc):
        params = NeuronParameters(
            tau_m=20.0,
            v_threshold=-50.0,
            v_reset=-70.0
        )
        neuron_id = core.add_neuron(NeuronType.EXCITATORY_PYRAMIDAL, params)
        exc_neurons.append(neuron_id)
        
    # Add inhibitory neurons
    inh_neurons = []
    for _ in range(num_inh):
        params = NeuronParameters(
            tau_m=10.0,  # Faster dynamics
            v_threshold=-45.0,
            v_reset=-65.0
        )
        neuron_id = core.add_neuron(NeuronType.INHIBITORY_FAST_SPIKING, params)
        inh_neurons.append(neuron_id)
        
    # Add adaptive neurons
    ada_neurons = []
    for _ in range(num_ada):
        params = NeuronParameters(
            tau_m=30.0,
            tau_w=150.0,
            v_threshold=-50.0,
            v_reset=-70.0,
            a=2.0,
            b=100.0
        )
        neuron_id = core.add_neuron(NeuronType.ADAPTIVE_RESONANCE, params)
        ada_neurons.append(neuron_id)
        
    all_neurons = exc_neurons + inh_neurons + ada_neurons
    
    # Create connections
    connections = []
    for pre_id in all_neurons:
        for post_id in all_neurons:
            if pre_id != post_id and np.random.random() < connection_probability:
                # Determine synapse type and weight
                if pre_id in exc_neurons:
                    synapse_type = SynapseType.CHEMICAL_EXCITATORY
                    weight = np.random.lognormal(0.0, 0.5)  # Log-normal weights
                else:
                    synapse_type = SynapseType.CHEMICAL_INHIBITORY
                    weight = -np.random.lognormal(0.0, 0.5)
                    
                # Add delay variation
                delay = np.random.gamma(2.0, 0.5)  # Gamma distributed delays
                
                params = SynapseParameters(
                    weight=weight,
                    delay=delay,
                    tau_syn=5.0 if synapse_type == SynapseType.CHEMICAL_EXCITATORY else 10.0
                )
                
                conn_id = core.add_connection(pre_id, post_id, synapse_type, params)
                connections.append(conn_id)
                
    # Set up connectivity pattern
    core.create_network_pattern("random", connection_probability=connection_probability)
    
    return {
        'excitatory_neurons': exc_neurons,
        'inhibitory_neurons': inh_neurons,
        'adaptive_neurons': ada_neurons,
        'all_neurons': all_neurons,
        'connections': connections,
        'network_type': 'balanced',
        'parameters': {
            'dimensions': dimensions,
            'connection_probability': connection_probability,
            'excitatory_ratio': excitatory_ratio
        }
    }


def create_feedforward_network(core: NeuromorphicCore,
                             layer_sizes: List[int],
                             connection_density: float = 0.5) -> Dict[str, Any]:
    """Create a feedforward network with specified layer structure.
    
    Args:
        core: NeuromorphicCore instance
        layer_sizes: List of neurons per layer
        connection_density: Probability of connections between adjacent layers
        
    Returns:
        Dictionary with network information
    """
    layers = []
    all_neurons = []
    
    # Create layers
    for layer_idx, layer_size in enumerate(layer_sizes):
        layer_neurons = []
        
        for _ in range(layer_size):
            # Input layer uses simple LIF, hidden layers use AdEx
            if layer_idx == 0:
                neuron_type = NeuronType.EXCITATORY_PYRAMIDAL
                params = NeuronParameters(tau_m=15.0)
            else:
                neuron_type = NeuronType.ADAPTIVE_RESONANCE
                params = NeuronParameters(
                    tau_m=25.0,
                    tau_w=200.0,
                    a=1.5,
                    b=80.0
                )
                
            neuron_id = core.add_neuron(neuron_type, params)
            layer_neurons.append(neuron_id)
            all_neurons.append(neuron_id)
            
        layers.append(layer_neurons)
        
    # Create feedforward connections
    connections = []
    for layer_idx in range(len(layers) - 1):
        pre_layer = layers[layer_idx]
        post_layer = layers[layer_idx + 1]
        
        for pre_id in pre_layer:
            for post_id in post_layer:
                if np.random.random() < connection_density:
                    # Weight depends on layer position
                    base_weight = 1.0 / np.sqrt(len(pre_layer))  # Normalization
                    weight = np.random.normal(base_weight, base_weight * 0.2)
                    
                    params = SynapseParameters(
                        weight=max(0.1, weight),  # Ensure positive weights
                        delay=np.random.uniform(1.0, 3.0),
                        tau_syn=8.0
                    )
                    
                    conn_id = core.add_connection(pre_id, post_id, 
                                                SynapseType.CHEMICAL_EXCITATORY, params)
                    connections.append(conn_id)
                    
    return {
        'layers': layers,
        'all_neurons': all_neurons,
        'connections': connections,
        'network_type': 'feedforward',
        'parameters': {
            'layer_sizes': layer_sizes,
            'connection_density': connection_density
        }
    }


def create_recurrent_network(core: NeuromorphicCore,
                           num_neurons: int,
                           recurrence_probability: float = 0.15,
                           self_connections: bool = False) -> Dict[str, Any]:
    """Create a recurrent network with rich dynamics.
    
    Args:
        core: NeuromorphicCore instance
        num_neurons: Total number of neurons
        recurrence_probability: Probability of recurrent connections
        self_connections: Whether to allow self-connections
        
    Returns:
        Dictionary with network information
    """
    neurons = []
    
    # Create heterogeneous population
    for i in range(num_neurons):
        # Mix of neuron types for rich dynamics
        if i < num_neurons * 0.6:
            neuron_type = NeuronType.EXCITATORY_PYRAMIDAL
            params = NeuronParameters(
                tau_m=np.random.uniform(15.0, 25.0),
                v_threshold=np.random.uniform(-55.0, -45.0)
            )
        elif i < num_neurons * 0.85:
            neuron_type = NeuronType.ADAPTIVE_RESONANCE
            params = NeuronParameters(
                tau_m=np.random.uniform(20.0, 40.0),
                tau_w=np.random.uniform(100.0, 300.0),
                a=np.random.uniform(1.0, 3.0),
                b=np.random.uniform(50.0, 150.0)
            )
        else:
            neuron_type = NeuronType.INHIBITORY_FAST_SPIKING
            params = NeuronParameters(
                tau_m=np.random.uniform(8.0, 15.0),
                v_threshold=np.random.uniform(-50.0, -40.0)
            )
            
        neuron_id = core.add_neuron(neuron_type, params)
        neurons.append(neuron_id)
        
    # Create recurrent connections
    connections = []
    for pre_id in neurons:
        for post_id in neurons:
            # Skip self-connections if not allowed
            if pre_id == post_id and not self_connections:
                continue
                
            if np.random.random() < recurrence_probability:
                # Determine connection properties
                pre_type = core.neurons[pre_id].parameters
                post_type = core.neurons[post_id].parameters
                
                # Weight based on neuron types
                if pre_id < num_neurons * 0.85:  # Excitatory
                    weight = np.random.lognormal(-0.5, 0.8)
                    synapse_type = SynapseType.CHEMICAL_EXCITATORY
                else:  # Inhibitory
                    weight = -np.random.lognormal(-0.3, 0.6)
                    synapse_type = SynapseType.CHEMICAL_INHIBITORY
                    
                # Distance-dependent delay
                delay = np.random.gamma(2.0, 1.0)
                
                params = SynapseParameters(
                    weight=weight,
                    delay=delay,
                    tau_syn=np.random.uniform(3.0, 12.0)
                )
                
                conn_id = core.add_connection(pre_id, post_id, synapse_type, params)
                connections.append(conn_id)
                
    return {
        'neurons': neurons,
        'connections': connections,
        'network_type': 'recurrent',
        'parameters': {
            'num_neurons': num_neurons,
            'recurrence_probability': recurrence_probability,
            'self_connections': self_connections
        }
    }


def create_cortical_microcircuit(core: NeuromorphicCore,
                               layer_sizes: List[int] = None,
                               laminar_connectivity: bool = True) -> Dict[str, Any]:
    """Create a cortical microcircuit with laminar organization.
    
    Args:
        core: NeuromorphicCore instance
        layer_sizes: Neurons per layer [L2/3, L4, L5, L6]
        laminar_connectivity: Use biological laminar connectivity patterns
        
    Returns:
        Dictionary with network information
    """
    if layer_sizes is None:
        layer_sizes = [80, 120, 100, 60]  # Typical cortical proportions
        
    layer_names = ['L2/3', 'L4', 'L5', 'L6']
    layers = {}
    all_neurons = []
    
    # Create laminar structure
    for layer_name, layer_size in zip(layer_names, layer_sizes):
        layer_neurons = {'excitatory': [], 'inhibitory': []}
        
        # 80% excitatory, 20% inhibitory (Dale's principle)
        num_exc = int(0.8 * layer_size)
        num_inh = layer_size - num_exc
        
        # Excitatory neurons
        for _ in range(num_exc):
            if layer_name in ['L2/3', 'L4']:
                # Superficial layers - regular spiking
                params = NeuronParameters(
                    tau_m=20.0,
                    v_threshold=-50.0,
                    v_reset=-70.0
                )
                neuron_type = NeuronType.EXCITATORY_PYRAMIDAL
            else:
                # Deep layers - intrinsically bursting
                params = NeuronParameters(
                    tau_m=25.0,
                    tau_w=200.0,
                    a=2.5,
                    b=120.0,
                    v_threshold=-48.0
                )
                neuron_type = NeuronType.ADAPTIVE_RESONANCE
                
            neuron_id = core.add_neuron(neuron_type, params)
            layer_neurons['excitatory'].append(neuron_id)
            all_neurons.append(neuron_id)
            
        # Inhibitory neurons
        for i in range(num_inh):
            if i < num_inh * 0.6:
                # Fast spiking interneurons
                params = NeuronParameters(
                    tau_m=8.0,
                    v_threshold=-45.0,
                    v_reset=-65.0
                )
                neuron_type = NeuronType.INHIBITORY_FAST_SPIKING
            else:
                # Low threshold spiking interneurons
                params = NeuronParameters(
                    tau_m=15.0,
                    v_threshold=-55.0,
                    v_reset=-75.0
                )
                neuron_type = NeuronType.INHIBITORY_LOW_THRESHOLD
                
            neuron_id = core.add_neuron(neuron_type, params)
            layer_neurons['inhibitory'].append(neuron_id)
            all_neurons.append(neuron_id)
            
        layers[layer_name] = layer_neurons
        
    # Create laminar connectivity
    connections = []
    
    if laminar_connectivity:
        # Biologically realistic connectivity matrix
        connectivity_matrix = {
            ('L2/3', 'L2/3'): 0.12,
            ('L2/3', 'L4'): 0.05,
            ('L2/3', 'L5'): 0.08,
            ('L2/3', 'L6'): 0.03,
            ('L4', 'L2/3'): 0.25,
            ('L4', 'L4'): 0.08,
            ('L4', 'L5'): 0.12,
            ('L4', 'L6'): 0.06,
            ('L5', 'L2/3'): 0.06,
            ('L5', 'L4'): 0.04,
            ('L5', 'L5'): 0.15,
            ('L5', 'L6'): 0.10,
            ('L6', 'L2/3'): 0.02,
            ('L6', 'L4'): 0.08,
            ('L6', 'L5'): 0.12,
            ('L6', 'L6'): 0.10
        }
        
        for (pre_layer, post_layer), prob in connectivity_matrix.items():
            pre_neurons = layers[pre_layer]['excitatory'] + layers[pre_layer]['inhibitory']
            post_neurons = layers[post_layer]['excitatory'] + layers[post_layer]['inhibitory']
            
            for pre_id in pre_neurons:
                for post_id in post_neurons:
                    if pre_id != post_id and np.random.random() < prob:
                        # Determine synapse properties
                        if pre_id in layers[pre_layer]['excitatory']:
                            synapse_type = SynapseType.CHEMICAL_EXCITATORY
                            weight = np.random.lognormal(-0.2, 0.6)
                        else:
                            synapse_type = SynapseType.CHEMICAL_INHIBITORY
                            weight = -np.random.lognormal(0.0, 0.5)
                            
                        # Layer-dependent delays
                        layer_distance = abs(layer_names.index(pre_layer) - 
                                           layer_names.index(post_layer))
                        delay = 1.0 + 0.5 * layer_distance + np.random.exponential(0.5)
                        
                        params = SynapseParameters(
                            weight=weight,
                            delay=delay,
                            tau_syn=np.random.uniform(4.0, 10.0)
                        )
                        
                        conn_id = core.add_connection(pre_id, post_id, synapse_type, params)
                        connections.append(conn_id)
                        
    return {
        'layers': layers,
        'all_neurons': all_neurons,
        'connections': connections,
        'network_type': 'cortical_microcircuit',
        'parameters': {
            'layer_sizes': layer_sizes,
            'laminar_connectivity': laminar_connectivity
        }
    }


# ===========================================================================================
# PLASTICITY RULE FACTORIES
# ===========================================================================================

def create_plasticity_rule(rule_type: str, **kwargs) -> PlasticityRule:
    """Factory function for creating plasticity rules.
    
    Args:
        rule_type: Type of plasticity rule ('stdp', 'bcm', 'oja', 'homeostatic')
        **kwargs: Rule-specific parameters
        
    Returns:
        PlasticityRule instance
    """
    parameters = PlasticityParameters(**kwargs)
    
    if rule_type.lower() == 'stdp':
        return STDPRule(parameters)
    elif rule_type.lower() == 'homeostatic':
        return HomeostaticPlasticityRule(parameters)
    else:
        raise ValueError(f"Unknown plasticity rule type: {rule_type}")


def plasticity_decorator(rule_type: str, **rule_kwargs):
    """Decorator for applying plasticity rules to network functions."""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            result = func(*args, **kwargs)
            
            # Apply plasticity rule if core is available
            if len(args) > 0 and hasattr(args[0], 'add_plasticity_rule'):
                plasticity_engine = args[0]
                rule = create_plasticity_rule(rule_type, **rule_kwargs)
                plasticity_engine.add_plasticity_rule(f"{rule_type}_auto", 
                                                    PlasticityType.STDP if rule_type == 'stdp' else PlasticityType.HOMEOSTATIC,
                                                    rule.parameters)
                                                    
            return result
        return wrapper
    return decorator


# ===========================================================================================
# MODULATION FACTORIES
# ===========================================================================================

def create_reward_modulation(neuromodulation: NeuromodulationSystem,
                           network: Dict[str, Any],
                           reward_strength: float = 1.0) -> DopamineModulator:
    """Create reward-based dopamine modulation.
    
    Args:
        neuromodulation: NeuromodulationSystem instance
        network: Network dictionary
        reward_strength: Strength of reward modulation
        
    Returns:
        DopamineModulator instance
    """
    dopamine_mod = DopamineModulator(tau=150.0)
    dopamine_mod.reward_strength = reward_strength
    
    # Connect to reward signal processing
    neuromodulation.modulators['dopamine'] = dopamine_mod
    
    return dopamine_mod


def create_attention_modulation(neuromodulation: NeuromodulationSystem,
                              network: Dict[str, Any],
                              attention_strength: float = 1.0) -> NorepinephrineModulator:
    """Create attention-based norepinephrine modulation.
    
    Args:
        neuromodulation: NeuromodulationSystem instance
        network: Network dictionary
        attention_strength: Strength of attention modulation
        
    Returns:
        NorepinephrineModulator instance
    """
    norepi_mod = NorepinephrineModulator(tau=100.0)
    norepi_mod.attention_strength = attention_strength
    
    neuromodulation.modulators['norepinephrine'] = norepi_mod
    
    return norepi_mod


def create_learning_modulation(neuromodulation: NeuromodulationSystem,
                             network: Dict[str, Any],
                             learning_strength: float = 1.0) -> AcetylcholineModulator:
    """Create learning-based acetylcholine modulation.
    
    Args:
        neuromodulation: NeuromodulationSystem instance
        network: Network dictionary
        learning_strength: Strength of learning modulation
        
    Returns:
        AcetylcholineModulator instance
    """
    ach_mod = AcetylcholineModulator(tau=75.0)
    ach_mod.learning_strength = learning_strength
    
    neuromodulation.modulators['acetylcholine'] = ach_mod
    
    return ach_mod


# ===========================================================================================
# OSCILLATOR FACTORIES
# ===========================================================================================

def create_default_oscillators(timing_circuits: BiologicalTimingCircuits,
                             network: Dict[str, Any],
                             **kwargs) -> Dict[str, NeuralOscillator]:
    """Create default set of neural oscillators.
    
    Args:
        timing_circuits: BiologicalTimingCircuits instance
        network: Network dictionary
        **kwargs: Additional parameters
        
    Returns:
        Dictionary of oscillators
    """
    oscillators = {}
    
    # Create standard frequency bands
    oscillators['delta'] = DeltaOscillator(frequency=2.0, amplitude=0.8)
    oscillators['theta'] = ThetaOscillator(frequency=6.0, amplitude=0.6)
    oscillators['alpha'] = AlphaOscillator(frequency=10.0, amplitude=0.5)
    oscillators['beta'] = BetaOscillator(frequency=20.0, amplitude=0.4)
    oscillators['gamma'] = GammaOscillator(frequency=40.0, amplitude=0.3)
    
    # Add to timing circuits
    for name, oscillator in oscillators.items():
        timing_circuits.oscillators[name] = oscillator
        
    return oscillators


def create_sleep_oscillators(timing_circuits: BiologicalTimingCircuits,
                           network: Dict[str, Any],
                           **kwargs) -> Dict[str, NeuralOscillator]:
    """Create oscillators optimized for sleep states.
    
    Args:
        timing_circuits: BiologicalTimingCircuits instance
        network: Network dictionary
        **kwargs: Additional parameters
        
    Returns:
        Dictionary of sleep oscillators
    """
    oscillators = {}
    
    # Enhanced delta for deep sleep
    oscillators['delta'] = DeltaOscillator(frequency=1.5, amplitude=1.2)
    
    # Reduced theta for NREM sleep
    oscillators['theta'] = ThetaOscillator(frequency=5.0, amplitude=0.3)
    
    # Sleep spindles (sigma band)
    oscillators['sigma'] = NeuralOscillator(frequency=12.0, amplitude=0.8,
                                          oscillation_type=OscillationType.BETA)
    
    # Add to timing circuits
    for name, oscillator in oscillators.items():
        timing_circuits.oscillators[name] = oscillator
        
    return oscillators


def create_attention_oscillators(timing_circuits: BiologicalTimingCircuits,
                                network: Dict[str, Any],
                                **kwargs) -> Dict[str, NeuralOscillator]:
    """Create oscillators optimized for attention states.
    
    Args:
        timing_circuits: BiologicalTimingCircuits instance
        network: Network dictionary
        **kwargs: Additional parameters
        
    Returns:
        Dictionary of attention oscillators
    """
    oscillators = {}
    
    # Suppressed alpha for attention
    oscillators['alpha'] = AlphaOscillator(frequency=10.0, amplitude=0.2)
    
    # Enhanced beta for focused attention
    oscillators['beta'] = BetaOscillator(frequency=18.0, amplitude=0.8)
    
    # Enhanced gamma for binding
    oscillators['gamma'] = GammaOscillator(frequency=45.0, amplitude=0.7)
    
    # Add to timing circuits
    for name, oscillator in oscillators.items():
        timing_circuits.oscillators[name] = oscillator
        
    return oscillators


# ===========================================================================================
# SERIALIZATION UTILITIES
# ===========================================================================================

def safe_serialize_object(obj: Any, max_depth: int = 10, current_depth: int = 0) -> Any:
    """Safely serialize objects with circular reference protection."""
    if current_depth > max_depth:
        return f"<Max depth {max_depth} reached>"
        
    if obj is None or isinstance(obj, (bool, int, float, str)):
        return obj
    elif isinstance(obj, (list, tuple)):
        return [safe_serialize_object(item, max_depth, current_depth + 1) for item in obj]
    elif isinstance(obj, dict):
        return {key: safe_serialize_object(value, max_depth, current_depth + 1) 
                for key, value in obj.items()}
    elif hasattr(obj, '__dict__'):
        return {
            '__class__': obj.__class__.__name__,
            '__dict__': safe_serialize_object(obj.__dict__, max_depth, current_depth + 1)
        }
    else:
        return str(obj)


# ===========================================================================================
# INITIALIZATION FUNCTIONS
# ===========================================================================================

def initialize_neuromorphic_core(config: Dict[str, Any] = None) -> Dict[str, Any]:
    """Initialize neuromorphic core with given configuration."""
    try:
        # Validate configuration
        if config is None:
            config = {}
            
        # Set default parameters
        default_config = {
            'dt': 0.1,
            'use_gpu': False,
            'use_parallel': True,
            'batch_size': 1000,
            'neuron_model': 'lif',
            'connection_type': 'chemical'
        }
        
        # Merge configurations
        merged_config = {**default_config, **config}
        
        # Initialize core components
        logger.info("Initializing NeuromorphicCore components")
        
        return {
            'status': 'success',
            'config': merged_config,
            'components_initialized': ['neurons', 'synapses', 'topology', 'monitors']
        }
        
    except Exception as e:
        logger.error(f"Failed to initialize neuromorphic core: {str(e)}")
        return {
            'status': 'failed',
            'error': str(e)
        }


def initialize_plasticity(config: Dict[str, Any] = None) -> Dict[str, Any]:
    """Initialize neuroplasticity engine."""
    try:
        if config is None:
            config = {}
            
        default_config = {
            'stdp_enabled': True,
            'homeostatic_enabled': True,
            'structural_enabled': False,
            'learning_rate': 0.01,
            'plasticity_window': 20.0
        }
        
        merged_config = {**default_config, **config}
        
        logger.info("Initializing NeuroplasticityEngine")
        
        return {
            'status': 'success',
            'config': merged_config,
            'rules_available': ['stdp', 'homeostatic', 'structural']
        }
        
    except Exception as e:
        logger.error(f"Failed to initialize plasticity engine: {str(e)}")
        return {
            'status': 'failed',
            'error': str(e)
        }


def initialize_pruning(config: Dict[str, Any] = None) -> Dict[str, Any]:
    """Initialize synaptic pruning module."""
    try:
        if config is None:
            config = {}
            
        default_config = {
            'initial_pruning_rate': 0.1,
            'final_pruning_rate': 0.001,
            'pruning_strategies': ['weight_based', 'activity_based'],
            'developmental_pruning': True
        }
        
        merged_config = {**default_config, **config}
        
        logger.info("Initializing SynapticPruningModule")
        
        return {
            'status': 'success',
            'config': merged_config,
            'strategies_available': ['weight_based', 'activity_based', 'redundancy', 'composite']
        }
        
    except Exception as e:
        logger.error(f"Failed to initialize pruning module: {str(e)}")
        return {
            'status': 'failed',
            'error': str(e)
        }


def initialize_neuromodulation(config: Dict[str, Any] = None) -> Dict[str, Any]:
    """Initialize neuromodulation system."""
    try:
        if config is None:
            config = {}
            
        default_config = {
            'modulators_enabled': ['dopamine', 'serotonin', 'norepinephrine', 'acetylcholine'],
            'baseline_concentrations': {
                'dopamine': 0.0,
                'serotonin': 0.5,
                'norepinephrine': 0.3,
                'acetylcholine': 0.2
            },
            'time_constants': {
                'dopamine': 100.0,
                'serotonin': 500.0,
                'norepinephrine': 200.0,
                'acetylcholine': 50.0
            }
        }
        
        merged_config = {**default_config, **config}
        
        logger.info("Initializing NeuromodulationSystem")
        
        return {
            'status': 'success',
            'config': merged_config,
            'modulators_available': ['dopamine', 'serotonin', 'norepinephrine', 'acetylcholine', 'oxytocin']
        }
        
    except Exception as e:
        logger.error(f"Failed to initialize neuromodulation system: {str(e)}")
        return {
            'status': 'failed',
            'error': str(e)
        }


def initialize_timing_circuits(config: Dict[str, Any] = None) -> Dict[str, Any]:
    """Initialize biological timing circuits."""
    try:
        if config is None:
            config = {}
            
        default_config = {
            'oscillators_enabled': ['delta', 'theta', 'alpha', 'beta', 'gamma'],
            'default_frequencies': {
                'delta': 2.0,
                'theta': 6.0,
                'alpha': 10.0,
                'beta': 20.0,
                'gamma': 40.0
            },
            'coupling_enabled': True,
            'phase_amplitude_coupling': True
        }
        
        merged_config = {**default_config, **config}
        
        logger.info("Initializing BiologicalTimingCircuits")
        
        return {
            'status': 'success',
            'config': merged_config,
            'oscillators_available': ['delta', 'theta', 'alpha', 'beta', 'gamma'],
            'coupling_types': ['phase_amplitude', 'phase_phase', 'amplitude_amplitude']
        }
        
    except Exception as e:
        logger.error(f"Failed to initialize timing circuits: {str(e)}")
        return {
            'status': 'failed',
            'error': str(e)
        }


def initialize_core_neural(config: Dict[str, Any] = None) -> Dict[str, Any]:
    """Initialize complete core neural architecture."""
    overall_status = {'overall': 'success', 'components': {}}
    
    try:
        logger.info("Starting Core Neural Architecture initialization")
        
        # Initialize each component
        components = [
            ('neuromorphic_core', initialize_neuromorphic_core),
            ('neuroplasticity_engine', initialize_plasticity),
            ('synaptic_pruning', initialize_pruning),
            ('neuromodulation', initialize_neuromodulation),
            ('biological_timing', initialize_timing_circuits)
        ]
        
        for component_name, init_func in components:
            component_config = config.get(component_name, {}) if config else {}
            result = init_func(component_config)
            overall_status['components'][component_name] = result
            
            if result['status'] != 'success':
                overall_status['overall'] = 'partial_failure'
                logger.warning(f"Component {component_name} failed to initialize: {result.get('error', 'Unknown error')}")
            else:
                logger.info(f"Component {component_name} initialized successfully")
                
        logger.info(f"Core Neural Architecture initialization completed with status: {overall_status['overall']}")
        
    except Exception as e:
        logger.error(f"Critical error during initialization: {str(e)}")
        overall_status['overall'] = 'failed'
        overall_status['critical_error'] = str(e)
        
    return overall_status

def detect_neural_hardware() -> Dict[str, Any]:
    """Detect available hardware with comprehensive error handling."""
    hardware = {'detected_backends': []}
    
    # Check for CUDA/GPU acceleration
    if CUPY_AVAILABLE:
        try:
            import cupy as cp
            device_count = cp.cuda.runtime.getDeviceCount()
            if device_count > 0:
                device_props = cp.cuda.runtime.getDeviceProperties(0)
                hardware['gpu'] = {
                    'available': True,
                    'device_count': device_count,
                    'device_name': device_props['name'].decode() if device_props['name'] else 'Unknown',
                    'memory_total': cp.cuda.runtime.memGetInfo()[1],
                    'memory_free': cp.cuda.runtime.memGetInfo()[0],
                    'compute_capability': device_props.get('major', 0)
                }
                hardware['detected_backends'].append('cupy')
            else:
                hardware['gpu'] = {'available': False, 'reason': 'No CUDA devices found'}
        except Exception as e:
            hardware['gpu'] = {'available': False, 'error': str(e)}
    else:
        hardware['gpu'] = {'available': False, 'reason': 'CuPy not installed'}
        
    # Check for JAX acceleration
    if JAX_AVAILABLE:
        try:
            import jax
            hardware['jax'] = {
                'available': True,
                'backend': str(jax.default_backend()),
                'devices': [str(d) for d in jax.devices()],
                'version': getattr(jax, '__version__', 'unknown')
            }
            hardware['detected_backends'].append('jax')
        except Exception as e:
            hardware['jax'] = {'available': False, 'error': str(e)}
    else:
        hardware['jax'] = {'available': False, 'reason': 'JAX not installed'}
        
    # Check for PyTorch acceleration
    if TORCH_AVAILABLE:
        try:
            import torch
            cuda_available = torch.cuda.is_available()
            device_count = torch.cuda.device_count() if cuda_available else 0
            
            hardware['torch'] = {
                'available': True,
                'cuda_available': cuda_available,
                'device_count': device_count,
                'devices': [],
                'version': torch.__version__
            }
            
            # Safely get device names
            if cuda_available and device_count > 0:
                try:
                    hardware['torch']['devices'] = [
                        torch.cuda.get_device_name(i) for i in range(device_count)
                    ]
                except Exception as e:
                    logger.warning(f"Could not get CUDA device names: {e}")
                    hardware['torch']['devices'] = [f"Device {i}" for i in range(device_count)]
                    
            hardware['detected_backends'].append('torch')
        except Exception as e:
            hardware['torch'] = {'available': False, 'error': str(e)}
    else:
        hardware['torch'] = {'available': False, 'reason': 'PyTorch not installed'}
        
    # Check for Numba JIT compilation
    if NUMBA_AVAILABLE:
        try:
            import numba
            hardware['numba'] = {
                'available': True,
                'version': numba.__version__,
                'threading_layer': getattr(numba.config, 'THREADING_LAYER', 'unknown'),
                'num_threads': getattr(numba.config, 'NUMBA_NUM_THREADS', mp.cpu_count())
            }
            hardware['detected_backends'].append('numba')
        except Exception as e:
            hardware['numba'] = {'available': False, 'error': str(e)}
    else:
        hardware['numba'] = {'available': False, 'reason': 'Numba not installed'}
        
    # Check CPU capabilities with proper error handling
    try:
        # Try psutil first
        try:
            import psutil
            cpu_freq = psutil.cpu_freq()
            memory_info = psutil.virtual_memory()
            
            hardware['cpu'] = {
                'available': True,
                'count': psutil.cpu_count(logical=False) or mp.cpu_count(),
                'count_logical': psutil.cpu_count(logical=True) or mp.cpu_count(),
                'freq_current': cpu_freq.current if cpu_freq else None,
                'memory_total': memory_info.total,
                'memory_available': memory_info.available
            }
        except ImportError:
            # Fallback to multiprocessing
            hardware['cpu'] = {
                'available': True,
                'count': mp.cpu_count(),
                'count_logical': mp.cpu_count(),
                'freq_current': None,
                'memory_total': None,
                'memory_available': None
            }
    except Exception as e:
        hardware['cpu'] = {
            'available': True,
            'count': 1,  # Safe fallback
            'error': str(e)
        }
        
    hardware['recommended_backend'] = _select_optimal_backend(hardware)
    
    return hardware


def _select_optimal_backend(hardware: Dict[str, Any]) -> str:
    """Select optimal computational backend with safety checks."""
    try:
        # Priority order for backend selection
        if hardware.get('gpu', {}).get('available', False):
            return 'cupy'
        elif hardware.get('jax', {}).get('available', False):
            return 'jax'
        elif hardware.get('torch', {}).get('available', False):
            return 'torch'
        elif hardware.get('numba', {}).get('available', False):
            return 'numba'
        else:
            return 'numpy'
    except Exception as e:
        logger.warning(f"Error selecting optimal backend: {e}")
        return 'numpy'


def configure_for_hardware(detected_hardware: Dict[str, Any] = None) -> Dict[str, Any]:
    """Configure core neural processing for optimal hardware usage."""
    if detected_hardware is None:
        detected_hardware = detect_neural_hardware()
        
    config = {
        'backend': detected_hardware['recommended_backend'],
        'use_gpu': detected_hardware.get('gpu', {}).get('available', False),
        'use_jit': detected_hardware.get('numba', {}).get('available', False),
        'parallel_processing': True,
        'batch_processing': True
    }
    
    # Configure based on selected backend
    backend = config['backend']
    
    try:
        if backend == 'cupy':
            config.update({
                'array_library': 'cupy',
                'device_id': 0,
                'memory_pool': True,
                'kernel_cache': True,
                'batch_size': 2048
            })
        elif backend == 'jax':
            config.update({
                'array_library': 'jax',
                'jit_compile': True,
                'device': detected_hardware.get('jax', {}).get('backend', 'cpu'),
                'batch_size': 1024
            })
        elif backend == 'torch':
            torch_info = detected_hardware.get('torch', {})
            config.update({
                'array_library': 'torch',
                'device': 'cuda' if torch_info.get('cuda_available', False) else 'cpu',
                'batch_size': 1024 if torch_info.get('cuda_available', False) else 512
            })
        elif backend == 'numba':
            numba_info = detected_hardware.get('numba', {})
            config.update({
                'array_library': 'numpy',
                'jit_compile': True,
                'parallel': True,
                'num_threads': numba_info.get('num_threads', mp.cpu_count()),
                'batch_size': 512
            })
        else:
            cpu_info = detected_hardware.get('cpu', {})
            config.update({
                'array_library': 'numpy',
                'num_threads': cpu_info.get('count', mp.cpu_count()),
                'batch_size': 256
            })
    except Exception as e:
        logger.warning(f"Error configuring for backend {backend}: {e}")
        # Fallback configuration
        config = {
            'backend': 'numpy',
            'array_library': 'numpy',
            'batch_size': 256,
            'use_gpu': False,
            'parallel_processing': False
        }
        
    return config


# ===========================================================================================
# MAIN INTERFACE CLASS - FIXED
# ===========================================================================================

class CoreNeuralInterface:
    """Unified interface for the Core Neural Architecture components."""
    
    def __init__(self, config: Union[CoreNeuralConfig, Dict[str, Any], None] = None, 
                 enable_global_instance: bool = True):
        """Initialize interface with optional global state."""
        logger.info("Initializing CoreNeuralInterface")
        
        # Handle configuration properly
        if isinstance(config, dict):
            self.config = CoreNeuralConfig.from_dict(config)
        elif isinstance(config, CoreNeuralConfig):
            self.config = config
        else:
            self.config = CoreNeuralConfig()
        
        # Store initialization status
        self.initialization_status = {'overall': 'initializing', 'components': {}}
        
        # Detect and configure hardware with error handling
        try:
            self.hardware = detect_neural_hardware()
            self.hardware_config = configure_for_hardware(self.hardware)
            
            # Update config with hardware settings
            self.config.update_from_dict(self.hardware_config)
            
        except Exception as e:
            logger.warning(f"Hardware detection failed: {e}")
            self.hardware = {'detected_backends': [], 'recommended_backend': 'numpy'}
            self.hardware_config = {'backend': 'numpy', 'use_gpu': False}
        
        # Initialize core neural architecture
        try:
            self._initialize_components()
            self.initialization_status['overall'] = 'success'
            logger.info("Core neural components initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize core neural architecture: {e}")
            self.initialization_status['overall'] = 'failed'
            self.initialization_status['error'] = str(e)
            raise RuntimeError(f"Core neural architecture initialization failed: {e}")
            
        # Optionally register as global instance
        if enable_global_instance:
            _register_global_instance(self)
            
    def _initialize_components(self):
        """Initialize component instances with proper error handling."""
        try:
            # Initialize main components with proper config
            logger.info("Initializing NeuromorphicCore...")
            self.neuromorphic_core = NeuromorphicCore(self.config)
            self.initialization_status['components']['neuromorphic_core'] = 'success'
            
            logger.info("Initializing NeuroplasticityEngine...")
            self.neuroplasticity_engine = NeuroplasticityEngine(self.config)
            self.initialization_status['components']['neuroplasticity_engine'] = 'success'
            
            logger.info("Initializing SynapticPruningModule...")
            self.synaptic_pruning = SynapticPruningModule(self.config)
            self.initialization_status['components']['synaptic_pruning'] = 'success'
            
            logger.info("Initializing NeuromodulationSystem...")
            self.neuromodulation_system = NeuromodulationSystem(self.config)
            self.initialization_status['components']['neuromodulation_system'] = 'success'
            
            logger.info("Initializing BiologicalTimingCircuits...")
            self.biological_timing = BiologicalTimingCircuits(self.config)
            self.initialization_status['components']['biological_timing'] = 'success'
            
            # Connect components
            logger.info("Connecting components...")
            self.neuroplasticity_engine.connect_to_core(self.neuromorphic_core)
            self.synaptic_pruning.connect_to_core(self.neuromorphic_core)
            self.neuromodulation_system.connect_to_core(self.neuromorphic_core)
            self.biological_timing.connect_to_core(self.neuromorphic_core)
            self.biological_timing.connect_to_neuromodulation(self.neuromodulation_system)
            
            logger.info("Core neural components initialized and connected successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize components: {str(e)}")
            self.initialization_status['components']['error'] = str(e)
            raise
            
    def create_network(self, network_type: str, **kwargs) -> Dict[str, Any]:
        """Create a neural network of specified type."""
        if network_type == 'balanced':
            return create_balanced_network(self.neuromorphic_core, **kwargs)
        elif network_type == 'feedforward':
            return create_feedforward_network(self.neuromorphic_core, **kwargs)
        elif network_type == 'recurrent':
            return create_recurrent_network(self.neuromorphic_core, **kwargs)
        elif network_type == 'cortical_microcircuit':
            return create_cortical_microcircuit(self.neuromorphic_core, **kwargs)
        else:
            raise ValueError(f"Unknown network type: {network_type}")
            
    def enable_plasticity(self, plasticity_type: str = 'stdp', **params) -> str:
        """Enable plasticity mechanisms."""
        if plasticity_type == 'stdp':
            rule_type = PlasticityType.STDP
        elif plasticity_type == 'homeostatic':
            rule_type = PlasticityType.HOMEOSTATIC
        elif plasticity_type == 'structural':
            rule_type = PlasticityType.STRUCTURAL
        else:
            raise ValueError(f"Unknown plasticity type: {plasticity_type}")
            
        self.neuroplasticity_engine.add_plasticity_rule(
            f"{plasticity_type}_rule", rule_type, PlasticityParameters(**params)
        )
        
        return f"{plasticity_type}_rule"
        
    def configure_neuromodulation(self, modulator_configs: Dict[str, Dict[str, Any]]):
        """Configure neuromodulation system."""
        for modulator_name, config in modulator_configs.items():
            if modulator_name in self.neuromodulation_system.modulators:
                modulator = self.neuromodulation_system.modulators[modulator_name]
                
                # Update modulator parameters
                for param, value in config.items():
                    if hasattr(modulator, param):
                        setattr(modulator, param, value)
                        
    def set_oscillators(self, oscillator_config: Dict[str, Dict[str, Any]]):
        """Configure neural oscillators."""
        for osc_name, config in oscillator_config.items():
            if osc_name in self.biological_timing.oscillators:
                oscillator = self.biological_timing.oscillators[osc_name]
                
                # Update oscillator parameters
                for param, value in config.items():
                    if hasattr(oscillator, param):
                        setattr(oscillator, param, value)
                        
    def run_simulation(self, duration: float = 1000.0, dt: float = None,
                      external_inputs: Dict[int, Callable[[float], float]] = None,
                      monitor_neurons: List[int] = None) -> Dict[str, Any]:
        """Run neural simulation."""
        if dt is None:
            dt = self.neuromorphic_core.dt
            
        if external_inputs is None:
            external_inputs = {}
            
        # Set up monitoring
        if monitor_neurons:
            neurons_to_monitor = monitor_neurons
        else:
            neurons_to_monitor = list(self.neuromorphic_core.neurons.keys())[:10]  # Monitor first 10
            
        # Reset all systems
        self.neuromorphic_core.reset()
        
        # Simulation loop
        num_steps = int(duration / dt)
        results = {
            'times': [],
            'population_activity': [],
            'modulator_levels': [],
            'oscillator_states': [],
            'network_statistics': []
        }
        
        logger.info(f"Starting simulation: duration={duration}ms, dt={dt}ms, steps={num_steps}")
        
        for step in range(num_steps):
            current_time = step * dt
            
            # Compute external inputs for this timestep
            step_inputs = {}
            for neuron_id, input_func in external_inputs.items():
                step_inputs[neuron_id] = input_func(current_time)
                
            # Update core systems
            core_state = self.neuromorphic_core.step(step_inputs)
            self.neuroplasticity_engine.step(dt)
            self.synaptic_pruning.step(dt)
            self.neuromodulation_system.step(dt)
            self.biological_timing.step(dt)
            
            # Record results every 10 steps to reduce memory usage
            if step % 10 == 0:
                results['times'].append(current_time)
                results['population_activity'].append(core_state['num_active_neurons'])
                results['modulator_levels'].append(
                    self.neuromodulation_system.get_modulation_state()['concentrations'].copy()
                )
                results['oscillator_states'].append(
                    self.biological_timing.get_oscillatory_state()['outputs'].copy()
                )
                results['network_statistics'].append(
                    self.neuromorphic_core.get_network_statistics()
                )
                
            # Progress logging
            if step % (num_steps // 10) == 0:
                logger.info(f"Simulation progress: {step}/{num_steps} ({100*step/num_steps:.1f}%)")
                
        logger.info("Simulation completed successfully")
        
        # Add summary statistics
        results['summary'] = {
            'total_spikes': sum(results['population_activity']),
            'mean_firing_rate': np.mean(results['population_activity']) if results['population_activity'] else 0.0,
            'final_modulator_levels': results['modulator_levels'][-1] if results['modulator_levels'] else {},
            'simulation_duration': duration,
            'timestep': dt,
            'num_neurons': len(self.neuromorphic_core.neurons),
            'num_connections': len(self.neuromorphic_core.connections)
        }
        
        return results
        
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status."""
        status = {
            'initialization_status': self.initialization_status,
            'hardware_configuration': self.hardware_config,
            'component_status': {},
            'network_statistics': {},
            'performance_metrics': {}
        }
        
        # Component status
        try:
            status['component_status'] = {
                'neuromorphic_core': {
                    'num_neurons': len(self.neuromorphic_core.neurons),
                    'num_connections': len(self.neuromorphic_core.connections),
                    'current_time': self.neuromorphic_core.current_time
                },
                'neuroplasticity_engine': self.neuroplasticity_engine.get_plasticity_statistics(),
                'synaptic_pruning': self.synaptic_pruning.get_pruning_statistics(),
                'neuromodulation_system': self.neuromodulation_system.get_modulation_state(),
                'biological_timing': self.biological_timing.get_oscillatory_state()
            }
        except Exception as e:
            logger.warning(f"Error getting component status: {e}")
            status['component_status']['error'] = str(e)
        
        # Network statistics
        try:
            if self.neuromorphic_core.neurons:
                status['network_statistics'] = self.neuromorphic_core.get_network_statistics()
        except Exception as e:
            logger.warning(f"Error getting network statistics: {e}")
            status['network_statistics']['error'] = str(e)
            
        return status
        
    def save_state(self, filepath: str):
        """Save current system state to file."""
        state = {
            'config': safe_serialize_object(self.config),
            'neurons': {},
            'connections': {},
            'plasticity_state': self.neuroplasticity_engine.get_plasticity_statistics(),
            'modulation_state': self.neuromodulation_system.get_modulation_state(),
            'timing_state': self.biological_timing.get_oscillatory_state(),
            'timestamp': time.time()
        }
        
        # Save neuron states
        for neuron_id, neuron in self.neuromorphic_core.neurons.items():
            state['neurons'][neuron_id] = {
                'parameters': safe_serialize_object(neuron.parameters.__dict__),
                'membrane_potential': neuron.get_membrane_potential(),
                'neuron_type': type(neuron).__name__
            }
            
        # Save connection states
        for conn_id, connection in self.neuromorphic_core.connections.items():
            state['connections'][conn_id] = {
                'pre_neuron_id': connection.pre_neuron_id,
                'post_neuron_id': connection.post_neuron_id,
                'parameters': safe_serialize_object(connection.parameters.__dict__),
                'synapse_type': connection.synapse_type.name
            }
            
        # Save to file
        with open(filepath, 'wb') as f:
            pickle.dump(state, f)
            
        logger.info(f"System state saved to {filepath}")
        
    def load_state(self, filepath: str):
        """Load system state from file."""
        with open(filepath, 'rb') as f:
            state = pickle.load(f)
            
        logger.info(f"Loading system state from {filepath}")
        
        # Restore configuration
        if 'config' in state:
            self.config.update_from_dict(state['config'])
        
        # Note: Full state restoration would require more complex reconstruction
        # This is a simplified implementation
        logger.info("System state loaded successfully")


# ===========================================================================================
# GLOBAL INSTANCE MANAGEMENT (OPTIONAL)
# ===========================================================================================

_global_instance = None
_global_instance_lock = threading.Lock()

def _register_global_instance(instance: CoreNeuralInterface):
    """Register global instance with thread safety."""
    global _global_instance
    with _global_instance_lock:
        _global_instance = instance

def get_global_instance() -> Optional[CoreNeuralInterface]:
    """Get global instance if available."""
    with _global_instance_lock:
        return _global_instance

def clear_global_instance():
    """Clear global instance."""
    global _global_instance
    with _global_instance_lock:
        _global_instance = None


# ===========================================================================================
# EXPORT DEFINITIONS AND MODULE INITIALIZATION
# ===========================================================================================

# Define all exported symbols
__all__ = [
    # Configuration
    'CoreNeuralConfig',
    
    # Global instance management
    'get_global_instance', 'clear_global_instance',
    
    # Core enumerations and data structures
    'NeuronType', 'SynapseType', 'PlasticityType', 'ModulatorType', 'OscillationType',
    'NeuronParameters', 'SynapseParameters', 'PlasticityParameters',
    
    # Thread-safe utilities
    'ThreadSafeDict', 'ThreadSafeCounter',
    
    # Neuron models
    'NeuronModel', 'LIFNeuron', 'AdExNeuron', 'IzhikevichNeuron',
    
    # Core neural system
    'SynapticConnection', 'NetworkTopology', 'NeuromorphicCore',
    
    # Plasticity system
    'PlasticityRule', 'STDPRule', 'HomeostaticPlasticityRule', 'StructuralPlasticityRule',
    'NeuroplasticityEngine',
    
    # Pruning system
    'PruningStrategy', 'WeightBasedPruning', 'ActivityBasedPruning', 'RedundancyPruning',
    'CompositePruning', 'SynapticPruningModule',
    
    # Neuromodulation system
    'Modulator', 'DopamineModulator', 'SerotoninModulator', 
    'NorepinephrineModulator', 'AcetylcholineModulator', 'NeuromodulationSystem',
    
    # Timing circuits
    'NeuralOscillator', 'DeltaOscillator', 'ThetaOscillator', 'AlphaOscillator',
    'BetaOscillator', 'GammaOscillator', 'PhaseAmplitudeCoupling', 'BiologicalTimingCircuits',
    
    # Monitoring tools
    'SpikeMonitor', 'VoltageMonitor',
    
    # Network construction utilities
    'create_balanced_network', 'create_feedforward_network', 'create_recurrent_network',
    'create_cortical_microcircuit',
    
    # Factory functions
    'create_plasticity_rule', 'plasticity_decorator',
    'create_reward_modulation', 'create_attention_modulation', 'create_learning_modulation',
    'create_default_oscillators', 'create_sleep_oscillators', 'create_attention_oscillators',
    
    # Hardware and configuration
    'detect_neural_hardware', 'configure_for_hardware',
    
    # Serialization
    'safe_serialize_object',
    
    # Initialization functions
    'initialize_neuromorphic_core', 'initialize_plasticity', 'initialize_pruning',
    'initialize_neuromodulation', 'initialize_timing_circuits', 'initialize_core_neural',
    
    # Main interface
    'CoreNeuralInterface'
]

# Module version
__version__ = '1.0.0'

# Module metadata
__author__ = 'ULTRA Development Team'
__description__ = 'Core Neural Architecture for ULTRA: Biologically-inspired neural processing system'
__license__ = 'MIT'

# Initialize logging for the module
logger.info(f"ULTRA Core Neural Architecture v{__version__} loaded successfully")
logger.info(f"Available backends: {', '.join(['numpy'] + (['cupy'] if CUPY_AVAILABLE else []) + (['jax'] if JAX_AVAILABLE else []) + (['torch'] if TORCH_AVAILABLE else []) + (['numba'] if NUMBA_AVAILABLE else []))}")

# Automatic hardware detection and configuration on module import
try:
    _DETECTED_HARDWARE = detect_neural_hardware()
    _OPTIMAL_CONFIG = configure_for_hardware(_DETECTED_HARDWARE)
    
    logger.info(f"Optimal backend detected: {_OPTIMAL_CONFIG['backend']}")
    logger.info(f"GPU acceleration: {'enabled' if _OPTIMAL_CONFIG.get('use_gpu', False) else 'disabled'}")
    
    # Create default interface instance for convenience
    try:
        core_neural = CoreNeuralInterface(_OPTIMAL_CONFIG)
        logger.info("Default CoreNeuralInterface instance created successfully")
    except Exception as e:
        logger.warning(f"Failed to create default interface instance: {str(e)}")
        core_neural = None
        
except Exception as e:
    logger.error(f"Failed to initialize ULTRA Core Neural Architecture: {str(e)}")
    core_neural = None

# Cleanup function for module shutdown
def _cleanup():
    """Cleanup function called on module shutdown."""
    try:
        clear_global_instance()
        logger.info("Cleaned up Core Neural Architecture resources")
    except Exception as e:
        logger.warning(f"Error during cleanup: {e}")
    finally:
        gc.collect()

import atexit
atexit.register(_cleanup)

logger.info("ULTRA Core Neural Architecture - Fixed implementation loaded")