#!/usr/bin/env python3
"""
ULTRA Neuromorphic Core - Comprehensive Test Suite
=================================================

This test suite validates both the original neuromorphic core functionality
and the enhanced biophysical components integration.

Tests cover:
- Original class functionality (BaseNeuron, LIFNeuron, etc.)
- Enhanced biophysical components (calcium dynamics, vesicle modeling)
- Hardware acceleration capabilities
- Network creation utilities
- Integration between original and enhanced components
- Performance benchmarking
- Error handling and edge cases

Author: ULTRA Development Team
Version: 1.0.0
"""

import unittest
import numpy as np
import time
import logging
import warnings
import sys
import os
from typing import Dict, List, Tuple, Any, Optional
from unittest.mock import Mock, patch
import tempfile
import pickle

# Import your neuromorphic core modules
# Adjust these imports based on your actual file structure
try:
    from neuromorphic_core import (
        # Original classes from your script
        NeuronModel, NeuronType, SynapseType, NeuromodulatorType, OscillationType,
        NeuronParameters, SynapseParameters,
        BaseNeuron, LIFNeuron, AdExNeuron, IzhikevichNeuron,
        BiologicalTimingCircuit, NeuromodulationSystem,
        SpikeMonitor, SpikeGenerator,
        NeuromorphicCore, SynapticPruningModule, NeuroplasticityEngine,
        
        # Network creation utilities
        create_balanced_network, create_feedforward_network, create_recurrent_network,
        create_cortical_microcircuit, create_neuromodulatory_network,
        
        # Enhanced components (my additions)
        BiophysicalSynapse, detect_hardware_capabilities,
        
        # Initialization function
        initialize_neuromorphic_core
    )
    IMPORTS_SUCCESSFUL = True
except ImportError as e:
    print(f"Warning: Could not import all modules: {e}")
    print("Some tests may be skipped.")
    IMPORTS_SUCCESSFUL = False

# Configure test logging
logging.basicConfig(level=logging.WARNING)  # Reduce noise during testing
test_logger = logging.getLogger(__name__)


class TestNeuronParameters(unittest.TestCase):
    """Test NeuronParameters class from original script."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.default_params = NeuronParameters()
    
    def test_default_parameters(self):
        """Test default parameter values."""
        self.assertEqual(self.default_params.v_rest, -70.0)
        self.assertEqual(self.default_params.v_threshold, -50.0)
        self.assertEqual(self.default_params.tau_m, 20.0)
        self.assertGreaterEqual(self.default_params.tau_refrac, 0.0)
    
    def test_model_specific_parameters(self):
        """Test model-specific parameter extraction."""
        # Test LIF parameters
        lif_params = self.default_params.get_model_params(NeuronModel.LIF)
        self.assertIn('v_rest', lif_params)
        self.assertIn('tau_m', lif_params)
        self.assertIn('noise_mean', lif_params)
        
        # Test AdEx parameters
        adex_params = self.default_params.get_model_params(NeuronModel.ADEX)
        self.assertIn('a', adex_params)
        self.assertIn('b', adex_params)
        self.assertIn('delta_t', adex_params)
        
        # Test Izhikevich parameters
        izh_params = self.default_params.get_model_params(NeuronModel.IZHIKEVICH)
        self.assertIn('a', izh_params)
        self.assertIn('b', izh_params)
        self.assertIn('c', izh_params)
        self.assertIn('d', izh_params)
    
    def test_neuron_type_parameters(self):
        """Test parameters based on neuron type."""
        # Test excitatory neuron parameters
        exc_params = NeuronParameters.from_neuron_type(NeuronType.EXCITATORY)
        self.assertIsInstance(exc_params, NeuronParameters)
        
        # Test inhibitory neuron parameters
        inh_params = NeuronParameters.from_neuron_type(NeuronType.INHIBITORY)
        self.assertLess(inh_params.tau_m, exc_params.tau_m)  # Faster dynamics
        
        # Test adaptive neuron parameters
        ada_params = NeuronParameters.from_neuron_type(NeuronType.ADAPTIVE)
        self.assertGreater(ada_params.tau_m, exc_params.tau_m)  # Slower dynamics


class TestBaseNeuron(unittest.TestCase):
    """Test BaseNeuron class with enhanced calcium dynamics."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.neuron = BaseNeuron(0)
    
    def test_initialization(self):
        """Test neuron initialization."""
        self.assertEqual(self.neuron.id, 0)
        self.assertEqual(self.neuron.v, self.neuron.params.v_rest)
        self.assertFalse(self.neuron.is_refractory)
        
        # Test enhanced calcium dynamics
        if hasattr(self.neuron, 'calcium_concentration'):
            self.assertGreater(self.neuron.calcium_concentration, 0.0)
            self.assertEqual(self.neuron.adaptation_current, 0.0)
    
    def test_calcium_dynamics(self):
        """Test enhanced calcium dynamics."""
        if not hasattr(self.neuron, 'update_calcium_dynamics'):
            self.skipTest("Enhanced calcium dynamics not available")
        
        initial_calcium = self.neuron.calcium_concentration
        
        # Test calcium influx from spike
        self.neuron.update_calcium_dynamics(0.1, spiked=True)
        self.assertGreater(self.neuron.calcium_concentration, initial_calcium)
        
        # Test calcium decay
        for _ in range(100):  # 10 ms
            self.neuron.update_calcium_dynamics(0.1, spiked=False)
        
        self.assertLess(self.neuron.calcium_concentration, initial_calcium + 0.5)
        self.assertGreaterEqual(self.neuron.calcium_concentration, self.neuron.calcium_baseline)
    
    def test_ionic_currents(self):
        """Test enhanced ionic current computation."""
        if not hasattr(self.neuron, 'compute_ionic_currents'):
            self.skipTest("Enhanced ionic currents not available")
        
        # Test current computation
        current = self.neuron.compute_ionic_currents(0.1)
        self.assertIsInstance(current, (int, float))
        
        # Test with different voltages
        self.neuron.v = -60.0
        current_depol = self.neuron.compute_ionic_currents(0.1)
        
        self.neuron.v = -80.0
        current_hyperpol = self.neuron.compute_ionic_currents(0.1)
        
        # Currents should be different for different voltages
        self.assertNotEqual(current_depol, current_hyperpol)
    
    def test_neuromodulation(self):
        """Test neuromodulation application."""
        modulators = {
            NeuromodulatorType.DOPAMINE: 0.5,
            NeuromodulatorType.SEROTONIN: 0.3,
            NeuromodulatorType.NOREPINEPHRINE: 0.2,
            NeuromodulatorType.ACETYLCHOLINE: 0.4
        }
        
        # This should not raise an error
        self.neuron.apply_neuromodulation(modulators)
    
    def test_enhanced_state(self):
        """Test enhanced state retrieval."""
        state = self.neuron.get_state()
        
        # Check basic state components
        self.assertIn('id', state)
        self.assertIn('v', state)
        
        # Check enhanced state if available
        if hasattr(self.neuron, 'get_enhanced_state'):
            enhanced_state = self.neuron.get_enhanced_state()
            self.assertIn('calcium_concentration', enhanced_state)
            self.assertIn('adaptation_current', enhanced_state)


class TestLIFNeuron(unittest.TestCase):
    """Test LIFNeuron class with enhancements."""
    
    def setUp(self):
        """Set up test fixtures."""
        params = NeuronParameters.from_neuron_type(NeuronType.EXCITATORY)
        self.neuron = LIFNeuron(1, params)
    
    def test_lif_dynamics(self):
        """Test basic LIF dynamics."""
        initial_v = self.neuron.v
        
        # Test sub-threshold dynamics
        spiked = self.neuron.step(0.1, 0.0, 5.0)  # Small current
        self.assertFalse(spiked)
        self.assertGreater(self.neuron.v, initial_v)
        
        # Test supra-threshold dynamics
        for _ in range(100):  # Apply strong current
            spiked = self.neuron.step(0.1, 0.0, 50.0)
            if spiked:
                break
        
        self.assertTrue(spiked)
        self.assertEqual(self.neuron.v, self.neuron.params.v_reset)
        self.assertTrue(self.neuron.is_refractory)
    
    def test_refractory_period(self):
        """Test refractory period mechanics."""
        # Force a spike
        self.neuron.v = self.neuron.params.v_threshold + 1.0
        spiked = self.neuron.step(0.1, 0.0, 0.0)
        self.assertTrue(spiked)
        
        # During refractory period, neuron should not spike
        spiked = self.neuron.step(0.1, 0.0, 100.0)  # Very strong current
        self.assertFalse(spiked)
        
        # After refractory period, neuron should be able to spike
        for _ in range(100):  # Wait for refractory period to end
            self.neuron.step(0.1, 0.0, 0.0)
        
        self.assertFalse(self.neuron.is_refractory)
    
    def test_enhanced_synaptic_reception(self):
        """Test enhanced synaptic spike reception."""
        if not hasattr(self.neuron, 'receive_spike'):
            self.skipTest("Enhanced synaptic reception not available")
        
        # Test glutamate reception
        initial_v = self.neuron.v
        self.neuron.receive_spike(2.0, 0.0, 'glutamate')
        
        # Should affect conductances if enhanced
        if hasattr(self.neuron, 'ampa_conductance'):
            self.assertGreater(self.neuron.ampa_conductance, 0.0)
            self.assertGreater(self.neuron.nmda_conductance, 0.0)
        
        # Test GABA reception
        self.neuron.receive_spike(2.0, 0.0, 'gaba')
        if hasattr(self.neuron, 'gaba_conductance'):
            self.assertGreater(self.neuron.gaba_conductance, 0.0)
    
    def test_synaptic_current_computation(self):
        """Test enhanced synaptic current computation."""
        if not hasattr(self.neuron, 'compute_synaptic_currents'):
            self.skipTest("Enhanced synaptic currents not available")
        
        # Add some conductances
        if hasattr(self.neuron, 'ampa_conductance'):
            self.neuron.ampa_conductance = 1.0
            self.neuron.nmda_conductance = 0.5
            self.neuron.gaba_conductance = 0.8
        
        # Compute currents
        current = self.neuron.compute_synaptic_currents()
        self.assertIsInstance(current, (int, float))
        
        # Conductances should decay over time
        if hasattr(self.neuron, 'ampa_conductance'):
            initial_ampa = self.neuron.ampa_conductance
            self.neuron.compute_synaptic_currents()
            self.assertLess(self.neuron.ampa_conductance, initial_ampa)


class TestAdExNeuron(unittest.TestCase):
    """Test AdExNeuron class."""
    
    def setUp(self):
        """Set up test fixtures."""
        params = NeuronParameters.from_neuron_type(NeuronType.ADAPTIVE)
        self.neuron = AdExNeuron(2, params)
    
    def test_adex_dynamics(self):
        """Test AdEx neuron dynamics - FIXED VERSION."""
        print("Testing AdEx neuron dynamics...")
        
        # Store initial values
        initial_v = self.neuron.v
        initial_w = self.neuron.w
        
        print(f"Initial state: v={initial_v:.3f}mV, w={initial_w:.3f}pA")
        
        # Apply sustained current to see adaptation build up
        dt = 0.1  # ms
        current = 50.0  # pA - strong enough to cause changes
        
        # Run for longer to see adaptation effects
        total_steps = 50  # 5ms total
        voltages = []
        adaptations = []
        
        for step in range(total_steps):
            # Step the neuron with current input
            self.neuron.step(dt, step * dt, current)
            voltages.append(self.neuron.v)
            adaptations.append(self.neuron.w)
            
            # Print every 10 steps for debugging
            if step % 10 == 0:
                print(f"Step {step}: v={self.neuron.v:.3f}mV, w={self.neuron.w:.3f}pA")
        
        final_v = self.neuron.v
        final_w = self.neuron.w
        
        print(f"Final state: v={final_v:.3f}mV, w={final_w:.3f}pA")
        
        # Check that voltage changed from initial (due to input current)
        self.assertNotEqual(final_v, initial_v, 
                        f"Voltage should change with current input: {final_v} != {initial_v}")
        
        # Check that adaptation variable changed from initial
        # With sustained current, w should increase over time
        self.assertNotEqual(final_w, initial_w, 
                        f"Adaptation variable should change with current: {final_w} != {initial_w}")
        
        # If we have spikes, adaptation should be positive
        if len(self.neuron.spike_times) > 0:
            self.assertGreater(final_w, initial_w, 
                            "Adaptation should increase after spikes")
            print(f"✅ Spikes detected: {len(self.neuron.spike_times)}")
        
        # Check that adaptation affects membrane dynamics
        # With higher w, the neuron should be less excitable
        max_w = max(adaptations)
        self.assertGreaterEqual(max_w, initial_w, 
                            "Maximum adaptation should be >= initial value")
        
        print(f"✅ AdEx dynamics test passed - adaptation range: {min(adaptations):.3f} to {max(adaptations):.3f}")

    
    def test_adaptation_mechanism(self):
        """Test spike-triggered adaptation - FIXED VERSION."""
        print("Testing AdEx adaptation mechanism...")
        
        # Initialize adaptation variable
        initial_w = self.neuron.w
        initial_v = self.neuron.v
        
        # Get the correct spike threshold from model params
        model_params = self.neuron.params.get_model_params(NeuronModel.ADEX)
        v_spike = model_params.get('v_spike', 0.0)  # This is the correct way to access it
        
        # Alternative: directly access the adex_v_spike parameter
        # v_spike = self.neuron.params.adex_v_spike
        
        # Force neuron above spike threshold
        self.neuron.v = v_spike + 1.0
        
        # Record initial state
        print(f"Initial state: v={self.neuron.v:.2f}, w={self.neuron.w:.2f}")
        print(f"Spike threshold: {v_spike:.2f}")
        
        # Step the neuron to trigger spike and adaptation
        dt = 0.1
        spiked = self.neuron.step(dt, 0.0, 0.0)
        
        print(f"After step: spiked={spiked}, v={self.neuron.v:.2f}, w={self.neuron.w:.2f}")
        
        # Check that spike occurred
        self.assertTrue(spiked, "Neuron should have spiked when above threshold")
        
        # Check that adaptation variable increased after spike
        self.assertGreater(self.neuron.w, initial_w, 
                        f"Adaptation variable should increase after spike: {self.neuron.w} > {initial_w}")
        
        # Check that voltage was reset
        reset_voltage = self.neuron.params.v_reset
        self.assertAlmostEqual(self.neuron.v, reset_voltage, places=2,
                            msg=f"Voltage should be reset to {reset_voltage} after spike")
        
        print("✅ AdEx adaptation mechanism test passed")


class TestIzhikevichNeuron(unittest.TestCase):
    """Test IzhikevichNeuron class."""
    
    def setUp(self):
        """Set up test fixtures."""
        params = NeuronParameters.from_neuron_type(NeuronType.RESONATOR)
        self.neuron = IzhikevichNeuron(3, params)
    
    def test_izhikevich_dynamics(self):
        """Test Izhikevich neuron dynamics."""
        initial_v = self.neuron.v
        initial_u = self.neuron.u
        
        # Test basic dynamics
        spiked = self.neuron.step(0.1, 0.0, 20.0)
        self.assertNotEqual(self.neuron.v, initial_v)
        self.assertNotEqual(self.neuron.u, initial_u)
        
        # Test spike mechanism
        for _ in range(100):
            spiked = self.neuron.step(0.1, 0.0, 30.0)
            if spiked:
                break
        
        self.assertTrue(spiked)
        self.assertEqual(self.neuron.v, self.neuron.params.izh_c)  # Reset value


class TestBiophysicalSynapse(unittest.TestCase):
    """Test BiophysicalSynapse class (enhanced component)."""
    
    def setUp(self):
        """Set up test fixtures."""
        if not IMPORTS_SUCCESSFUL:
            self.skipTest("BiophysicalSynapse not available")
        
        try:
            self.synapse = BiophysicalSynapse(0, 1, 'glutamate', 1.0, 1.5)
        except NameError:
            self.skipTest("BiophysicalSynapse class not found")
    
    def test_initialization(self):
        """Test synapse initialization."""
        self.assertEqual(self.synapse.pre_neuron_id, 0)
        self.assertEqual(self.synapse.post_neuron_id, 1)
        self.assertEqual(self.synapse.synapse_type, 'glutamate')
        self.assertEqual(self.synapse.weight, 1.0)
        self.assertEqual(self.synapse.delay, 1.5)
        
        # Test vesicle pool initialization
        self.assertGreater(self.synapse.vesicle_pool, 0)
        self.assertGreater(self.synapse.max_vesicles, 0)
        self.assertGreaterEqual(self.synapse.release_probability, 0.0)
        self.assertLessEqual(self.synapse.release_probability, 1.0)
    
    def test_spike_processing(self):
        """Test spike processing and vesicle release."""
        initial_vesicles = self.synapse.vesicle_pool
        initial_nt = self.synapse.neurotransmitter_concentration
        
        # Process a spike
        released, vesicles = self.synapse.process_spike(0.0)
        
        if released:
            self.assertGreater(vesicles, 0)
            self.assertLessEqual(self.synapse.vesicle_pool, initial_vesicles)
            self.assertGreater(self.synapse.neurotransmitter_concentration, initial_nt)
    
    def test_neurotransmitter_dynamics(self):
        """Test neurotransmitter concentration dynamics."""
        # Set initial concentration
        self.synapse.neurotransmitter_concentration = 1.0
        self.synapse.receptor_occupancy = 0.0
        
        initial_nt = self.synapse.neurotransmitter_concentration
        
        # Update dynamics
        self.synapse.update_neurotransmitter_dynamics(1.0)  # 1 ms
        
        # Concentration should decay
        self.assertLess(self.synapse.neurotransmitter_concentration, initial_nt)
        
        # Receptor occupancy should change
        if self.synapse.neurotransmitter_concentration > 0:
            self.assertGreater(self.synapse.receptor_occupancy, 0.0)
    
    def test_short_term_plasticity(self):
        """Test short-term plasticity (facilitation/depression)."""
        initial_facilitation = self.synapse.facilitation
        initial_depression = self.synapse.depression
        
        # Process multiple spikes
        for i in range(5):
            self.synapse.process_spike(i * 10.0)  # Spikes every 10 ms
        
        # Facilitation should increase, depression should decrease
        self.assertGreaterEqual(self.synapse.facilitation, initial_facilitation)
        self.assertLessEqual(self.synapse.depression, initial_depression)
    
    def test_conductance_calculation(self):
        """Test synaptic conductance calculation."""
        self.synapse.receptor_occupancy = 0.5
        conductance = self.synapse.get_conductance()
        
        expected = self.synapse.max_conductance * 0.5 * abs(self.synapse.weight)
        self.assertAlmostEqual(conductance, expected, places=5)
    
    def test_stdp_application(self):
        """Test STDP weight updates."""
        initial_weight = self.synapse.weight
        
        # Set up traces for LTP
        self.synapse.presynaptic_trace = 1.0
        self.synapse.postsynaptic_trace = 0.8
        
        # Apply STDP
        self.synapse.apply_stdp(0.1)
        
        # Weight should change
        self.assertNotEqual(self.synapse.weight, initial_weight)


class TestBiologicalTimingCircuit(unittest.TestCase):
    """Test BiologicalTimingCircuit class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.timing_circuit = BiologicalTimingCircuit()
    
    def test_initialization(self):
        """Test timing circuit initialization."""
        self.assertIn(OscillationType.DELTA, self.timing_circuit.frequencies)
        self.assertIn(OscillationType.THETA, self.timing_circuit.frequencies)
        self.assertIn(OscillationType.GAMMA, self.timing_circuit.frequencies)
        
        # Check frequency ranges
        self.assertLess(self.timing_circuit.frequencies[OscillationType.DELTA], 5.0)
        self.assertGreater(self.timing_circuit.frequencies[OscillationType.GAMMA], 25.0)
    
    def test_oscillation_update(self):
        """Test oscillation updates."""
        initial_time = self.timing_circuit.time
        
        oscillations = self.timing_circuit.update(1.0)  # 1 ms
        
        self.assertGreater(self.timing_circuit.time, initial_time)
        self.assertIsInstance(oscillations, dict)
        
        for osc_type, value in oscillations.items():
            self.assertIsInstance(value, (int, float))
            self.assertGreaterEqual(value, -2.0)  # Reasonable amplitude range
            self.assertLessEqual(value, 2.0)
    
    def test_frequency_modification(self):
        """Test frequency and amplitude modification."""
        original_freq = self.timing_circuit.frequencies[OscillationType.ALPHA]
        
        self.timing_circuit.set_frequency(OscillationType.ALPHA, 12.0)
        self.assertEqual(self.timing_circuit.frequencies[OscillationType.ALPHA], 12.0)
        
        self.timing_circuit.set_amplitude(OscillationType.ALPHA, 2.0)
        self.assertEqual(self.timing_circuit.amplitudes[OscillationType.ALPHA], 2.0)
    
    def test_phase_coupling(self):
        """Test phase-amplitude coupling."""
        # Set up coupling
        self.timing_circuit.set_coupling(OscillationType.THETA, OscillationType.GAMMA, 0.5)
        
        # Check coupling matrix
        theta_idx = self.timing_circuit.oscillation_types.index(OscillationType.THETA)
        gamma_idx = self.timing_circuit.oscillation_types.index(OscillationType.GAMMA)
        
        self.assertEqual(self.timing_circuit.coupling[theta_idx, gamma_idx], 0.5)
    
    def test_neuron_modulation(self):
        """Test neuron-specific modulation."""
        modulation = self.timing_circuit.get_modulation(5)
        self.assertIsInstance(modulation, (int, float))


class TestNeuromodulationSystem(unittest.TestCase):
    """Test NeuromodulationSystem class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.neuromod_system = NeuromodulationSystem()
    
    def test_initialization(self):
        """Test neuromodulation system initialization."""
        self.assertEqual(self.neuromod_system.concentrations[NeuromodulatorType.DOPAMINE], 0.0)
        self.assertGreater(self.neuromod_system.time_constants[NeuromodulatorType.DOPAMINE], 0.0)
    
    def test_concentration_updates(self):
        """Test concentration updates."""
        # Set input
        self.neuromod_system.set_input(NeuromodulatorType.DOPAMINE, 1.0)
        
        initial_conc = self.neuromod_system.concentrations[NeuromodulatorType.DOPAMINE]
        
        # Update
        concentrations = self.neuromod_system.update(10.0)  # 10 ms
        
        self.assertGreater(concentrations[NeuromodulatorType.DOPAMINE], initial_conc)
    
    def test_pulse_application(self):
        """Test neuromodulator pulse."""
        initial_conc = self.neuromod_system.concentrations[NeuromodulatorType.SEROTONIN]
        
        self.neuromod_system.pulse(NeuromodulatorType.SEROTONIN, 0.5)
        
        self.assertGreater(self.neuromod_system.concentrations[NeuromodulatorType.SEROTONIN], 
                          initial_conc)
    
    def test_concentration_access(self):
        """Test concentration getter/setter."""
        self.neuromod_system.set_concentration(NeuromodulatorType.NOREPINEPHRINE, 0.7)
        
        conc = self.neuromod_system.get_concentration(NeuromodulatorType.NOREPINEPHRINE)
        self.assertEqual(conc, 0.7)


class TestSpikeMonitor(unittest.TestCase):
    """Test SpikeMonitor class."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create some dummy neurons
        self.neurons = [LIFNeuron(i) for i in range(5)]
        self.monitor = SpikeMonitor(self.neurons, record_v=True)
    
    def test_initialization(self):
        """Test monitor initialization."""
        self.assertEqual(len(self.monitor.neurons), 5)
        self.assertTrue(self.monitor.record_v)
        
        for neuron_id in range(5):
            self.assertIn(neuron_id, self.monitor.spike_record)
            self.assertEqual(len(self.monitor.spike_record[neuron_id]), 0)
    
    def test_spike_recording(self):
        """Test spike recording."""
        # Simulate some spikes
        self.neurons[0].spike_times.append(10.0)
        self.neurons[2].spike_times.append(15.0)
        
        self.monitor.record(20.0)
        
        # Check if spikes were recorded
        results = self.monitor.get_results()
        self.assertIn('spike_record', results)
    
    def test_voltage_recording(self):
        """Test voltage recording."""
        # Set some voltages
        for i, neuron in enumerate(self.neurons):
            neuron.v = -60.0 + i * 2.0
        
        self.monitor.record(5.0)
        
        results = self.monitor.get_results()
        if 'v_record' in results:
            self.assertGreater(len(results['v_record'][0]), 0)


class TestNeuromorphicCore(unittest.TestCase):
    """Test NeuromorphicCore class - the main integration test."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = {
            'dimensions': (5, 5, 5),
            'neuron_placement': 'random',
            'default_neuron_model': NeuronModel.LIF,
            'use_hardware_acceleration': False,  # Disable for testing
            'random_seed': 42
        }
        
        self.core = NeuromorphicCore(
            dimensions=(5, 5, 5),
            config=self.config
        )
    
    def test_core_initialization(self):
        """Test core initialization."""
        self.assertEqual(self.core.dimensions, (5, 5, 5))
        self.assertEqual(self.core.neuron_count, 0)
        self.assertEqual(len(self.core.neurons), 0)
        self.assertEqual(len(self.core.connections), 0)
        
        # Test timing circuits and neuromodulation
        self.assertIsInstance(self.core.timing_circuits, BiologicalTimingCircuit)
        self.assertIsInstance(self.core.neuromodulation, NeuromodulationSystem)
    
    def test_neuron_creation(self):
        """Test neuron creation."""
        # Create neurons
        neuron_ids = self.core.create_neurons(10, NeuronModel.LIF, NeuronType.EXCITATORY)
        
        self.assertEqual(len(neuron_ids), 10)
        self.assertEqual(self.core.neuron_count, 10)
        self.assertEqual(len(self.core.neurons), 10)
        
        # Check neuron types
        for neuron_id in neuron_ids:
            self.assertIn(neuron_id, self.core.neuron_types)
            self.assertEqual(self.core.neuron_types[neuron_id], NeuronType.EXCITATORY)
            
            # Check position assignment
            self.assertIn(neuron_id, self.core.neuron_positions)
            pos = self.core.neuron_positions[neuron_id]
            self.assertEqual(len(pos), 3)  # 3D position
    
    def test_connection_creation(self):
        """Test connection creation."""
        # Create neurons first
        exc_ids = self.core.create_neurons(5, NeuronModel.LIF, NeuronType.EXCITATORY)
        inh_ids = self.core.create_neurons(3, NeuronModel.LIF, NeuronType.INHIBITORY)
        
        # Create connections
        connections = self.core.connect_neurons(
            pre_ids=exc_ids,
            post_ids=inh_ids,
            connection_prob=0.5,
            weight_range=(0.1, 0.3),
            delay_range=(1.0, 2.0)
        )
        
        self.assertGreater(len(connections), 0)
        
        # Check connection properties
        for pre_id, post_id in connections:
            self.assertIn((pre_id, post_id), self.core.connections)
            conn_info = self.core.connections[(pre_id, post_id)]
            
            self.assertEqual(conn_info['pre_id'], pre_id)
            self.assertEqual(conn_info['post_id'], post_id)
            self.assertGreaterEqual(conn_info['weight'], 0.1)
            self.assertLessEqual(conn_info['weight'], 0.3)
            self.assertGreaterEqual(conn_info['delay'], 1.0)
            self.assertLessEqual(conn_info['delay'], 2.0)
    
    def test_biophysical_synapse_creation(self):
        """Test biophysical synapse creation (enhanced feature)."""
        if not hasattr(self.core, 'add_biophysical_synapse'):
            self.skipTest("Biophysical synapses not available")
        
        # Create neurons
        neuron_ids = self.core.create_neurons(3, NeuronModel.LIF, NeuronType.EXCITATORY)
        
        # Create biophysical synapse
        synapse_id = self.core.add_biophysical_synapse(
            neuron_ids[0], neuron_ids[1], 'glutamate', 1.5, 2.0
        )
        
        self.assertIsNotNone(synapse_id)
        
        # Check if connection was created
        conn_key = (neuron_ids[0], neuron_ids[1])
        self.assertIn(conn_key, self.core.connections)
        
        # Check for biophysical synapse object
        conn_info = self.core.connections[conn_key]
        if 'biophysical_synapse' in conn_info:
            synapse = conn_info['biophysical_synapse']
            self.assertIsInstance(synapse, BiophysicalSynapse)
    
    def test_simulation_step(self):
        """Test simulation step."""
        # Create a small network
        neuron_ids = self.core.create_neurons(5, NeuronModel.LIF, NeuronType.EXCITATORY)
        connections = self.core.connect_neurons(
            pre_ids=neuron_ids[:3],
            post_ids=neuron_ids[2:],
            connection_prob=0.3
        )
        
        # Test step without input
        spikes = self.core.step(0.1)
        self.assertEqual(len(spikes), len(neuron_ids))
        self.assertIsInstance(spikes, np.ndarray)
        self.assertEqual(spikes.dtype, bool)
        
        # Test step with external input
        external_input = {neuron_ids[0]: 50.0}  # Strong input
        spikes = self.core.step(0.1, external_input)
        
        # Should advance time
        self.assertEqual(self.core.current_time, 0.2)
    
    def test_hardware_acceleration_detection(self):
        """Test hardware acceleration detection."""
        if hasattr(self.core, 'get_hardware_info'):
            hardware_info = self.core.get_hardware_info()
            self.assertIsInstance(hardware_info, dict)
            
            if 'hardware_detection' in hardware_info:
                detection = hardware_info['hardware_detection']
                self.assertIn('detected_backends', detection)
                self.assertIn('recommended_backend', detection)
    
    def test_network_statistics(self):
        """Test network statistics."""
        # Create network
        neuron_ids = self.core.create_neurons(10)
        connections = self.core.connect_neurons(neuron_ids, neuron_ids, connection_prob=0.1)
        
        # Get statistics
        conn_matrix = self.core.get_connectivity_matrix()
        weight_matrix = self.core.get_weight_matrix()
        
        self.assertEqual(conn_matrix.shape, (10, 10))
        self.assertEqual(weight_matrix.shape, (10, 10))
        
        # Check firing rates
        rates = self.core.get_spike_rate()
        self.assertEqual(len(rates), 10)
    
    def test_plasticity_attachment(self):
        """Test plasticity engine attachment."""
        plasticity_engine = NeuroplasticityEngine()
        
        success = self.core.attach_plasticity_engine(plasticity_engine)
        self.assertTrue(success)
        self.assertTrue(self.core.has_plasticity_engine)
        self.assertEqual(self.core.plasticity_engine, plasticity_engine)
    
    def test_state_persistence(self):
        """Test state saving and loading."""
        # Create network
        neuron_ids = self.core.create_neurons(5)
        connections = self.core.connect_neurons(neuron_ids, neuron_ids, connection_prob=0.2)
        
        # Run some simulation
        for _ in range(10):
            self.core.step(0.1)
        
        # Save state
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_filename = temp_file.name
        
        try:
            self.core.save_state(temp_filename)
            
            # Load state
            new_core = NeuromorphicCore(config=self.config)
            new_core.load_state(temp_filename)
            
            # Check if state was restored
            self.assertEqual(new_core.neuron_count, self.core.neuron_count)
            self.assertEqual(len(new_core.connections), len(self.core.connections))
            
        finally:
            os.unlink(temp_filename)
    
    def test_reset_functionality(self):
        """Test network reset."""
        # Create network and run simulation
        neuron_ids = self.core.create_neurons(5)
        for _ in range(10):
            self.core.step(0.1)
        
        initial_time = self.core.current_time
        self.assertGreater(initial_time, 0)
        
        # Reset
        self.core.reset()
        
        self.assertEqual(self.core.current_time, 0.0)
        
        # Check neuron reset
        for neuron in self.core.neurons.values():
            self.assertEqual(neuron.v, neuron.params.v_rest)


class TestNetworkCreationUtilities(unittest.TestCase):
    """Test network creation utility functions."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.core = NeuromorphicCore(dimensions=(10, 10, 10))
    
    def test_balanced_network_creation(self):
        """Test balanced network creation."""
        network_info = create_balanced_network(
            self.core,
            num_excitatory=20,
            num_inhibitory=5,
            connectivity_prob=0.1
        )
        
        self.assertEqual(len(network_info['excitatory_ids']), 20)
        self.assertEqual(len(network_info['inhibitory_ids']), 5)
        self.assertGreater(network_info['total_connections'], 0)
        
        # Check neuron types
        for exc_id in network_info['excitatory_ids']:
            self.assertEqual(self.core.neuron_types[exc_id], NeuronType.EXCITATORY)
        
        for inh_id in network_info['inhibitory_ids']:
            self.assertEqual(self.core.neuron_types[inh_id], NeuronType.INHIBITORY)
    
    def test_feedforward_network_creation(self):
        """Test feedforward network creation."""
        network_info = create_feedforward_network(
            self.core,
            layer_sizes=[10, 8, 6, 4],
            feedforward_prob=0.2
        )
        
        self.assertEqual(len(network_info['layers']), 4)
        self.assertEqual(len(network_info['layers'][0]), 10)
        self.assertEqual(len(network_info['layers'][-1]), 4)
        self.assertGreater(len(network_info['feedforward_connections']), 0)
    
    def test_recurrent_network_creation(self):
        """Test recurrent network creation."""
        network_info = create_recurrent_network(
            self.core,
            n_excitatory=15,
            n_inhibitory=5,
            input_size=3,
            output_size=2
        )
        
        self.assertEqual(len(network_info['input_ids']), 3)
        self.assertEqual(len(network_info['output_ids']), 2)
        self.assertEqual(len(network_info['recurrent_exc_ids']), 15)
        self.assertEqual(len(network_info['recurrent_inh_ids']), 5)
    
    def test_cortical_microcircuit_creation(self):
        """Test cortical microcircuit creation."""
        network_info = create_cortical_microcircuit(
            self.core,
            scale_factor=0.1  # Small network for testing
        )
        
        self.assertIn('layers', network_info)
        self.assertIn('connections', network_info)
        
        # Check for expected layers
        expected_layers = ['L23_exc', 'L23_inh', 'L4_exc', 'L4_inh', 
                          'L5_exc', 'L5_inh', 'L6_exc', 'L6_inh']
        
        for layer in expected_layers:
            self.assertIn(layer, network_info['layers'])
    
    def test_neuromodulatory_network_creation(self):
        """Test neuromodulatory network creation."""
        network_info = create_neuromodulatory_network(
            self.core,
            n_dopamine=2,
            n_serotonin=2,
            n_target=10
        )
        
        self.assertEqual(len(network_info['dopamine_ids']), 2)
        self.assertEqual(len(network_info['serotonin_ids']), 2)
        self.assertEqual(len(network_info['target_ids']), 10)


class TestSynapticPruningModule(unittest.TestCase):
    """Test SynapticPruningModule class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.core = NeuromorphicCore()
        neuron_ids = self.core.create_neurons(10)
        self.core.connect_neurons(neuron_ids, neuron_ids, connection_prob=0.3)
        
        self.pruning_module = SynapticPruningModule()
        self.pruning_module.initialize(self.core)
    
    def test_pruning_module_initialization(self):
        """Test pruning module initialization."""
        self.assertEqual(self.pruning_module.core, self.core)
        self.assertGreater(self.pruning_module.importance_threshold, 0)
        self.assertGreater(self.pruning_module.pruning_rate, 0)
    
    def test_importance_calculation(self):
        """Test connection importance calculation."""
        if not self.core.connections:
            self.skipTest("No connections to test")
        
        conn_key = list(self.core.connections.keys())[0]
        importance = self.pruning_module._calculate_importance(conn_key)
        
        self.assertIsInstance(importance, (int, float))
        self.assertGreaterEqual(importance, 0.0)
        self.assertLessEqual(importance, 1.0)
    
    def test_pruning_execution(self):
        """Test pruning execution."""
        initial_connections = len(self.core.connections)
        
        # Set aggressive pruning for testing
        self.pruning_module.importance_threshold = 0.9
        self.pruning_module.pruning_rate = 0.5
        
        # Run pruning
        pruned = self.pruning_module.prune_connections(1000.0)  # Beyond pruning period
        
        if pruned > 0:
            self.assertLess(len(self.core.connections), initial_connections)


class TestNeuroplasticityEngine(unittest.TestCase):
    """Test NeuroplasticityEngine class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.core = NeuromorphicCore()
        neuron_ids = self.core.create_neurons(5)
        self.core.connect_neurons(neuron_ids, neuron_ids, connection_prob=0.4)
        
        self.plasticity_engine = NeuroplasticityEngine()
        self.plasticity_engine.initialize(self.core)
    
    def test_plasticity_initialization(self):
        """Test plasticity engine initialization."""
        self.assertEqual(self.plasticity_engine.core, self.core)
        self.assertGreater(self.plasticity_engine.learning_rate, 0)
        
        # Check trace initialization
        for neuron_id in range(self.core.neuron_count):
            self.assertIn(neuron_id, self.plasticity_engine.pre_trace)
            self.assertIn(neuron_id, self.plasticity_engine.post_trace)
    
    def test_spike_handling(self):
        """Test spike event handling."""
        initial_traces = self.plasticity_engine.pre_trace[0]
        
        # Trigger spike
        self.plasticity_engine.on_spike(0, 10.0)
        
        # Check trace update
        self.assertGreater(self.plasticity_engine.pre_trace[0], initial_traces)
    
    def test_weight_updates(self):
        """Test synaptic weight updates."""
        if not self.core.connections:
            self.skipTest("No connections to test")
        
        # Get a connection
        conn_key = list(self.core.connections.keys())[0]
        initial_weight = self.core.connection_weights[conn_key]
        
        # Set up traces for plasticity
        pre_id, post_id = conn_key
        self.plasticity_engine.pre_trace[pre_id] = 1.0
        self.plasticity_engine.post_trace[post_id] = 0.8
        
        # Trigger spike to update weights
        self.plasticity_engine.on_spike(pre_id, 10.0)
        
        # Weight may or may not change depending on implementation
        # Just check that the method doesn't crash
        final_weight = self.core.connection_weights[conn_key]
        self.assertIsInstance(final_weight, (int, float))


class TestHardwareAcceleration(unittest.TestCase):
    """Test hardware acceleration capabilities."""
    
    def test_hardware_detection(self):
        """Test hardware capability detection."""
        if not IMPORTS_SUCCESSFUL:
            self.skipTest("Hardware detection not available")
        
        try:
            from neuromorphic_core import detect_hardware_capabilities
            hardware_info = detect_hardware_capabilities()
            
            self.assertIn('detected_backends', hardware_info)
            self.assertIn('recommended_backend', hardware_info)
            self.assertIn('cpu', hardware_info)
            
            # Should detect at least CPU
            self.assertTrue(hardware_info['cpu']['available'])
            
        except (ImportError, NameError):
            self.skipTest("Hardware detection function not available")


class TestPerformanceBenchmarks(unittest.TestCase):
    """Test performance benchmarking capabilities."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.core = NeuromorphicCore(config={'use_hardware_acceleration': False})
    
    def test_small_network_performance(self):
        """Test performance with small network."""
        # Create small network
        neuron_ids = self.core.create_neurons(20)
        self.core.connect_neurons(neuron_ids, neuron_ids, connection_prob=0.1)
        
        # Benchmark
        start_time = time.perf_counter()
        
        for _ in range(100):  # 10 ms simulation
            self.core.step(0.1)
        
        elapsed_time = time.perf_counter() - start_time
        
        # Should complete reasonably quickly
        self.assertLess(elapsed_time, 10.0)  # Less than 10 seconds
        
        # Check performance stats if available
        if hasattr(self.core, 'get_performance_stats'):
            perf_stats = self.core.get_performance_stats()
            if 'step_performance' in perf_stats:
                self.assertGreater(perf_stats['step_performance']['steps_per_second'], 0)
    
    def test_benchmark_method(self):
        """Test built-in benchmark method."""
        if not hasattr(self.core, 'benchmark_performance'):
            self.skipTest("Benchmark method not available")
        
        # Create small network
        neuron_ids = self.core.create_neurons(10)
        self.core.connect_neurons(neuron_ids, neuron_ids, connection_prob=0.2)
        
        # Run benchmark
        results = self.core.benchmark_performance(duration=100.0, dt=0.1)
        
        self.assertIn('performance_metrics', results)
        self.assertIn('steps_per_second', results['performance_metrics'])
        self.assertGreater(results['performance_metrics']['steps_per_second'], 0)


class TestIntegrationScenarios(unittest.TestCase):
    """Test integration scenarios between original and enhanced components."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.core = NeuromorphicCore(
            config={
                'use_hardware_acceleration': False,
                'enable_calcium_dynamics': True,
                'use_biophysical_synapses': True
            }
        )
    
    def test_full_system_integration(self):
        """Test full system with all components."""
        # Create network
        network_info = create_balanced_network(
            self.core,
            num_excitatory=20,
            num_inhibitory=5,
            connectivity_prob=0.2
        )
        
        # Add plasticity and pruning
        plasticity_engine = NeuroplasticityEngine()
        pruning_module = SynapticPruningModule()
        
        self.core.attach_plasticity_engine(plasticity_engine)
        
        # Create monitors
        neurons = list(self.core.neurons.values())
        monitor = SpikeMonitor(neurons[:5], record_v=True)
        
        # Run simulation with all components
        for step in range(100):
            # Provide some input
            external_input = {0: 10.0 * np.sin(step * 0.1)} if step < 50 else {}
            
            spikes = self.core.step(0.1, external_input)
            monitor.record(self.core.current_time)
            
            # Periodically update plasticity and pruning
            if step % 10 == 0:
                plasticity_engine.step(1.0)
                pruning_module.step(1.0, self.core.current_time)
        
        # Verify simulation completed
        self.assertGreater(self.core.current_time, 0)
        
        # Check monitor results
        results = monitor.get_results()
        self.assertIn('spike_record', results)
    
    def test_calcium_dynamics_integration(self):
        """Test calcium dynamics integration with network simulation."""
        # Create neurons with calcium dynamics
        neuron_ids = self.core.create_neurons(5, NeuronModel.LIF, NeuronType.EXCITATORY)
        
        # Check if neurons have calcium dynamics
        test_neuron = self.core.neurons[neuron_ids[0]]
        if not hasattr(test_neuron, 'calcium_concentration'):
            self.skipTest("Calcium dynamics not available")
        
        initial_calcium = test_neuron.calcium_concentration
        
        # Stimulate neuron to spike
        for _ in range(50):
            spikes = self.core.step(0.1, {neuron_ids[0]: 50.0})
            if spikes[neuron_ids[0]]:
                break
        
        # Check if calcium increased after spike
        final_calcium = test_neuron.calcium_concentration
        if any(spikes):
            self.assertGreater(final_calcium, initial_calcium)
    
    def test_biophysical_synapse_integration(self):
        """Test biophysical synapse integration."""
        if not hasattr(self.core, 'add_biophysical_synapse'):
            self.skipTest("Biophysical synapses not available")
        
        # Create neurons
        neuron_ids = self.core.create_neurons(3)
        
        # Add biophysical synapse
        synapse_id = self.core.add_biophysical_synapse(
            neuron_ids[0], neuron_ids[1], 'glutamate', 2.0, 1.0
        )
        
        # Stimulate pre-synaptic neuron
        spike_occurred = False
        for _ in range(50):
            spikes = self.core.step(0.1, {neuron_ids[0]: 80.0})
            if spikes[neuron_ids[0]]:
                spike_occurred = True
                break
        
        self.assertTrue(spike_occurred)
        
        # Continue simulation to see post-synaptic effects
        for _ in range(20):
            self.core.step(0.1)
        
        # Test should complete without errors


# ===========================================================================================
# TEST SUITE RUNNER AND UTILITIES
# ===========================================================================================

class ULTRATestResult(unittest.TextTestResult):
    """Custom test result class for detailed reporting."""
    
    def __init__(self, stream, descriptions, verbosity):
        super().__init__(stream, descriptions, verbosity)
        self.test_results = []
    
    def addSuccess(self, test):
        super().addSuccess(test)
        self.test_results.append({
            'test': str(test),
            'status': 'PASS',
            'message': 'Test passed successfully'
        })
    
    def addError(self, test, err):
        super().addError(test, err)
        self.test_results.append({
            'test': str(test),
            'status': 'ERROR',
            'message': str(err[1])
        })
    
    def addFailure(self, test, err):
        super().addFailure(test, err)
        self.test_results.append({
            'test': str(test),
            'status': 'FAIL',
            'message': str(err[1])
        })
    
    def addSkip(self, test, reason):
        super().addSkip(test, reason)
        self.test_results.append({
            'test': str(test),
            'status': 'SKIP',
            'message': reason
        })


def run_comprehensive_tests(verbosity=2):
    """Run the comprehensive test suite."""
    print("🧪 ULTRA Neuromorphic Core - Comprehensive Test Suite")
    print("=" * 60)
    
    # Check imports
    if not IMPORTS_SUCCESSFUL:
        print("⚠️  WARNING: Some imports failed. Some tests will be skipped.")
        print()
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestNeuronParameters,
        TestBaseNeuron,
        TestLIFNeuron,
        TestAdExNeuron,
        TestIzhikevichNeuron,
        TestBiophysicalSynapse,
        TestBiologicalTimingCircuit,
        TestNeuromodulationSystem,
        TestSpikeMonitor,
        TestNeuromorphicCore,
        TestNetworkCreationUtilities,
        TestSynapticPruningModule,
        TestNeuroplasticityEngine,
        TestHardwareAcceleration,
        TestPerformanceBenchmarks,
        TestIntegrationScenarios
    ]
    
    for test_class in test_classes:
        tests = loader.loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    # Run tests with custom result class
    runner = unittest.TextTestRunner(
        verbosity=verbosity,
        resultclass=ULTRATestResult,
        stream=sys.stdout
    )
    
    print(f"Running {suite.countTestCases()} tests...")
    print()
    
    start_time = time.time()
    result = runner.run(suite)
    end_time = time.time()
    
    # Print summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    total_tests = result.testsRun
    passed_tests = total_tests - len(result.failures) - len(result.errors)
    
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests} ✅")
    print(f"Failed: {len(result.failures)} ❌")
    print(f"Errors: {len(result.errors)} 💥")
    print(f"Skipped: {len(result.skipped)} ⏭️")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    print(f"Execution Time: {end_time - start_time:.2f} seconds")
    
    # Print detailed results if there are failures or errors
    if result.failures or result.errors:
        print("\n" + "=" * 60)
        print("🔍 DETAILED FAILURE/ERROR ANALYSIS")
        print("=" * 60)
        
        for test, traceback in result.failures + result.errors:
            print(f"\n❌ {test}")
            print("-" * 40)
            print(traceback)
    
    # Return result for programmatic use
    return {
        'total': total_tests,
        'passed': passed_tests,
        'failed': len(result.failures),
        'errors': len(result.errors),
        'skipped': len(result.skipped),
        'success_rate': (passed_tests/total_tests)*100 if total_tests > 0 else 0,
        'execution_time': end_time - start_time,
        'all_passed': len(result.failures) == 0 and len(result.errors) == 0
    }


def run_quick_tests():
    """Run a quick subset of tests for rapid validation."""
    print("🚀 ULTRA Neuromorphic Core - Quick Test Suite")
    print("=" * 50)
    
    # Quick test classes
    quick_test_classes = [
        TestNeuronParameters,
        TestBaseNeuron,
        TestLIFNeuron,
        TestNeuromorphicCore
    ]
    
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    for test_class in quick_test_classes:
        tests = loader.loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    runner = unittest.TextTestRunner(verbosity=1)
    start_time = time.time()
    result = runner.run(suite)
    end_time = time.time()
    
    print(f"\n⚡ Quick tests completed in {end_time - start_time:.2f} seconds")
    return len(result.failures) == 0 and len(result.errors) == 0


def validate_integration():
    """Validate that enhanced components integrate properly with original code."""
    print("🔄 ULTRA Integration Validation")
    print("=" * 40)
    
    validation_results = []
    
    # Test 1: Core initialization
    try:
        core = NeuromorphicCore()
        validation_results.append(("Core initialization", True, "✅"))
    except Exception as e:
        validation_results.append(("Core initialization", False, f"❌ {e}"))
    
    # Test 2: Neuron creation
    try:
        neuron_ids = core.create_neurons(5)
        validation_results.append(("Neuron creation", len(neuron_ids) == 5, "✅" if len(neuron_ids) == 5 else "❌"))
    except Exception as e:
        validation_results.append(("Neuron creation", False, f"❌ {e}"))
    
    # Test 3: Connection creation
    try:
        connections = core.connect_neurons(neuron_ids, neuron_ids, connection_prob=0.2)
        validation_results.append(("Connection creation", len(connections) > 0, "✅" if len(connections) > 0 else "❌"))
    except Exception as e:
        validation_results.append(("Connection creation", False, f"❌ {e}"))
    
    # Test 4: Simulation step
    try:
        spikes = core.step(0.1)
        validation_results.append(("Simulation step", True, "✅"))
    except Exception as e:
        validation_results.append(("Simulation step", False, f"❌ {e}"))
    
    # Test 5: Enhanced features
    try:
        if hasattr(core, 'add_biophysical_synapse'):
            synapse_id = core.add_biophysical_synapse(neuron_ids[0], neuron_ids[1], 'glutamate', 1.0, 1.0)
            validation_results.append(("Biophysical synapses", True, "✅"))
        else:
            validation_results.append(("Biophysical synapses", False, "⏭️ Not available"))
    except Exception as e:
        validation_results.append(("Biophysical synapses", False, f"❌ {e}"))
    
    # Print results
    print()
    for test_name, success, status in validation_results:
        print(f"{test_name:<25} {status}")
    
    print()
    successful_tests = sum(1 for _, success, _ in validation_results if success)
    total_tests = len(validation_results)
    print(f"Integration validation: {successful_tests}/{total_tests} tests passed")
    
    return successful_tests == total_tests


# ===========================================================================================
# MAIN TEST EXECUTION
# ===========================================================================================

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="ULTRA Neuromorphic Core Test Suite")
    parser.add_argument("--quick", action="store_true", help="Run quick tests only")
    parser.add_argument("--validate", action="store_true", help="Run integration validation only")
    parser.add_argument("--verbose", "-v", action="count", default=1, help="Increase verbosity")
    
    args = parser.parse_args()
    
    if args.validate:
        success = validate_integration()
        sys.exit(0 if success else 1)
    elif args.quick:
        success = run_quick_tests()
        sys.exit(0 if success else 1)
    else:
        results = run_comprehensive_tests(verbosity=args.verbose)
        sys.exit(0 if results['all_passed'] else 1)