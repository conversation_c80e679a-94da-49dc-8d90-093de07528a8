#!/usr/bin/env python3
"""
ULTRA: Neuroplasticity Engine

This module implements the neuroplasticity mechanisms for the ULTRA architecture.
The Neuroplasticity Engine enables neuronal networks to adapt based on experience,
implementing both Hebbian (e.g., STDP) and homeostatic plasticity mechanisms, as
well as structural plasticity for creating and removing connections.

Components:
- Various plasticity rules (STDP, BCM, Oja, Homeostatic)
- Structural plasticity for creating/removing connections
- Synaptic tagging and capture for long-term memory
- Metaplasticity for adjusting learning rates
- Advanced STDP with weight dependence and dendritic effects
"""

import numpy as np
import scipy.sparse
import time
import logging
import warnings
from enum import Enum, auto
from typing import Dict, List, Tuple, Set, Any, Optional, Union, Callable
from dataclasses import dataclass, field
from collections import defaultdict, deque
import copy
import functools

# Optional visualization support
try:
    import matplotlib.pyplot as plt
    HAS_MATPLOTLIB = True
except ImportError:
    HAS_MATPLOTLIB = False
    warnings.warn("Matplotlib not found, visualization features unavailable", ImportWarning)

# For hardware acceleration
try:
    import torch
    HAS_TORCH = True
except ImportError:
    HAS_TORCH = False
    warnings.warn("PyTorch not found, hardware acceleration unavailable", ImportWarning)

try:
    import jax
    import jax.numpy as jnp
    HAS_JAX = True
except ImportError:
    HAS_JAX = False
    warnings.warn("JAX not found, advanced hardware acceleration unavailable", ImportWarning)

# Optional graph visualization
try:
    import networkx as nx
    HAS_NETWORKX = True
except ImportError:
    HAS_NETWORKX = False
    warnings.warn("NetworkX not found, connectivity visualization unavailable", ImportWarning)

# Configure module logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# Create console handler if no handlers exist
if not logger.handlers:
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)


class PlasticityRule(Enum):
    """Types of plasticity rules."""
    STDP = "Spike-Timing-Dependent Plasticity"
    BCM = "Bienenstock-Cooper-Munro"
    OJA = "Oja's Rule"
    HOMEOSTATIC = "Homeostatic Plasticity"
    STRUCTURAL = "Structural Plasticity"
    CONSOLIDATED = "Consolidated Plasticity"
    TRIPLET = "Triplet STDP"
    VOLTAGE = "Voltage-Based Plasticity"
    CUSTOM = "Custom Plasticity Rule"


class TraceType(Enum):
    """Types of synaptic traces for plasticity."""
    PRE_SPIKE = auto()  # Presynaptic spike trace
    POST_SPIKE = auto()  # Postsynaptic spike trace
    PRE_VOLTAGE = auto()  # Presynaptic voltage trace
    POST_VOLTAGE = auto()  # Postsynaptic voltage trace
    PRE_SLOW = auto()  # Slow presynaptic trace (for triplet rules)
    POST_SLOW = auto()  # Slow postsynaptic trace (for triplet rules)
    ELIGIBILITY = auto()  # Eligibility trace
    TAG = auto()  # Synaptic tag
    CALCIUM = auto()  # Calcium trace


@dataclass
class PlasticityParameters:
    """Parameters for plasticity rules."""
    # STDP parameters
    a_plus: float = 0.01  # Potentiation factor
    a_minus: float = 0.0105  # Depression factor
    tau_plus: float = 20.0  # Potentiation time constant (ms)
    tau_minus: float = 20.0  # Depression time constant (ms)
    
    # BCM parameters
    learning_rate: float = 0.01  # Learning rate
    target_activity: float = 0.1  # Target activity level
    sliding_threshold_tau: float = 1000.0  # Time constant for sliding threshold (ms)
    
    # Oja parameters
    alpha: float = 0.01  # Learning rate for Oja's rule
    
    # Homeostatic parameters
    homeostatic_tau: float = 10000.0  # Time constant for homeostatic adjustment (ms)
    target_rate: float = 5.0  # Target firing rate (Hz)
    homeostatic_scale: float = 0.1  # Scale factor for homeostatic adjustment
    
    # Structural plasticity parameters
    creation_p: float = 0.001  # Probability of creating a new connection
    elimination_p: float = 0.0005  # Probability of eliminating a connection
    min_weight: float = 0.01  # Minimum weight for stable connections
    growth_factor: float = 0.05  # Growth factor for new connections
    correlation_window: int = 100  # Window size for computing activity correlations
    max_connections_per_step: int = 100  # Maximum connections to modify per step
    
    # Consolidated plasticity parameters
    consolidation_threshold: float = 0.5  # Threshold for consolidation
    consolidation_rate: float = 0.0001  # Rate of consolidation
    tagging_rate: float = 0.01  # Rate of tagging
    
    # Triplet STDP parameters
    a_plus_plus: float = 5.0e-3  # Triplet potentiation factor
    a_minus_minus: float = 7.0e-3  # Triplet depression factor
    tau_plus_slow: float = 200.0  # Slow potentiation time constant (ms)
    tau_minus_slow: float = 200.0  # Slow depression time constant (ms)
    
    # Weight dependence parameters
    mu_plus: float = 0.0  # Potentiation weight dependence factor
    mu_minus: float = 0.0  # Depression weight dependence factor
    
    # Voltage-based parameters
    theta_low: float = -70.0  # Lower threshold (mV)
    theta_high: float = -50.0  # Upper threshold (mV)
    
    # Weight bounds
    w_min: float = 0.0  # Minimum weight
    w_max: float = 1.0  # Maximum weight
    
    # General parameters
    enable_metaplasticity: bool = False  # Enable metaplasticity
    enable_heterosynaptic: bool = False  # Enable heterosynaptic plasticity
    update_interval: float = 1.0  # Update interval (ms)
    
    def get_rule_params(self, rule: PlasticityRule) -> Dict[str, Any]:
        """
        Get parameters specific to a plasticity rule.
        
        Args:
            rule: The plasticity rule
            
        Returns:
            Dictionary of rule-specific parameters
        """
        if rule == PlasticityRule.STDP:
            return {
                'a_plus': self.a_plus,
                'a_minus': self.a_minus,
                'tau_plus': self.tau_plus,
                'tau_minus': self.tau_minus,
                'mu_plus': self.mu_plus,
                'mu_minus': self.mu_minus,
                'w_min': self.w_min,
                'w_max': self.w_max
            }
        elif rule == PlasticityRule.BCM:
            return {
                'learning_rate': self.learning_rate,
                'target_activity': self.target_activity,
                'sliding_threshold_tau': self.sliding_threshold_tau,
                'w_min': self.w_min,
                'w_max': self.w_max
            }
        elif rule == PlasticityRule.OJA:
            return {
                'alpha': self.alpha,
                'w_min': self.w_min,
                'w_max': self.w_max
            }
        elif rule == PlasticityRule.HOMEOSTATIC:
            return {
                'homeostatic_tau': self.homeostatic_tau,
                'target_rate': self.target_rate,
                'homeostatic_scale': self.homeostatic_scale,
                'w_min': self.w_min,
                'w_max': self.w_max
            }
        elif rule == PlasticityRule.STRUCTURAL:
            return {
                'creation_p': self.creation_p,
                'elimination_p': self.elimination_p,
                'min_weight': self.min_weight,
                'growth_factor': self.growth_factor,
                'correlation_window': self.correlation_window,
                'max_connections_per_step': self.max_connections_per_step
            }
        elif rule == PlasticityRule.CONSOLIDATED:
            return {
                'consolidation_threshold': self.consolidation_threshold,
                'consolidation_rate': self.consolidation_rate,
                'tagging_rate': self.tagging_rate,
                'w_min': self.w_min,
                'w_max': self.w_max
            }
        elif rule == PlasticityRule.TRIPLET:
            return {
                'a_plus': self.a_plus,
                'a_minus': self.a_minus,
                'a_plus_plus': self.a_plus_plus,
                'a_minus_minus': self.a_minus_minus,
                'tau_plus': self.tau_plus,
                'tau_minus': self.tau_minus,
                'tau_plus_slow': self.tau_plus_slow,
                'tau_minus_slow': self.tau_minus_slow,
                'w_min': self.w_min,
                'w_max': self.w_max
            }
        elif rule == PlasticityRule.VOLTAGE:
            return {
                'theta_low': self.theta_low,
                'theta_high': self.theta_high,
                'learning_rate': self.learning_rate,
                'w_min': self.w_min,
                'w_max': self.w_max
            }
        else:
            # Return all parameters for custom rules
            return self.__dict__


class SynapticTrace:
    """
    Trace of synaptic activity for plasticity rules.
    
    Attributes:
        trace_type: Type of trace
        value: Current trace value
        tau: Time constant
        last_update: Time of last update
    """
    
    def __init__(self, trace_type: TraceType, tau: float):
        """
        Initialize synaptic trace.
        
        Args:
            trace_type: Type of trace
            tau: Time constant
        """
        self.trace_type = trace_type
        self.value = 0.0
        self.tau = tau
        self.last_update = 0.0
    
    def update(self, t_current: float):
        """
        Update trace with exponential decay.
        
        Args:
            t_current: Current time
            
        Returns:
            Updated trace value
        """
        if t_current <= self.last_update:
            return self.value
            
        dt = t_current - self.last_update
        decay = np.exp(-dt / self.tau)
        self.value *= decay
        self.last_update = t_current
        
        return self.value
    
    def add_spike(self, t_current: float, amplitude: float = 1.0):
        """
        Add a spike to the trace.
        
        Args:
            t_current: Current time
            amplitude: Spike amplitude
            
        Returns:
            Updated trace value
        """
        # First update the trace with decay
        self.update(t_current)
        
        # Then add the spike
        self.value += amplitude
        
        return self.value
    
    def reset(self):
        """Reset trace to zero."""
        self.value = 0.0


class STDPRule:
    """
    Spike-Timing-Dependent Plasticity rule.
    
    This implements standard STDP:
    - If post neuron fires after pre (t_post > t_pre): w += a_plus * exp(-(t_post - t_pre)/tau_plus)
    - If pre neuron fires after post (t_pre > t_post): w -= a_minus * exp(-(t_pre - t_post)/tau_minus)
    
    Attributes:
        params: STDP parameters
        pre_traces: Presynaptic traces for each neuron
        post_traces: Postsynaptic traces for each neuron
    """
    
    def __init__(self, params: Optional[PlasticityParameters] = None):
        """
        Initialize STDP rule.
        
        Args:
            params: Parameters for STDP rule
        """
        self.params = params if params else PlasticityParameters()
        self.rule_params = self.params.get_rule_params(PlasticityRule.STDP)
        
        # Traces for each neuron
        self.pre_traces = {}  # neuron_id -> trace
        self.post_traces = {}  # neuron_id -> trace
        
        # Weight changes for each synapse
        self.weight_changes = {}  # (pre_id, post_id) -> weight_change
        
        # Last update time
        self.last_update_time = 0.0
    
    def initialize_traces(self, neuron_ids: List[int]):
        """
        Initialize traces for neurons.
        
        Args:
            neuron_ids: List of neuron IDs
        """
        for neuron_id in neuron_ids:
            if neuron_id not in self.pre_traces:
                self.pre_traces[neuron_id] = SynapticTrace(
                    trace_type=TraceType.PRE_SPIKE,
                    tau=self.rule_params['tau_plus']
                )
            
            if neuron_id not in self.post_traces:
                self.post_traces[neuron_id] = SynapticTrace(
                    trace_type=TraceType.POST_SPIKE,
                    tau=self.rule_params['tau_minus']
                )
    
    def on_pre_spike(self, pre_id: int, post_id: int, t: float, w: float) -> float:
        """
        Process presynaptic spike.
        
        Args:
            pre_id: Presynaptic neuron ID
            post_id: Postsynaptic neuron ID
            t: Current time
            w: Current weight
            
        Returns:
            Weight change
        """
        # Initialize traces if needed
        if pre_id not in self.pre_traces:
            self.pre_traces[pre_id] = SynapticTrace(
                trace_type=TraceType.PRE_SPIKE,
                tau=self.rule_params['tau_plus']
            )
        
        if post_id not in self.post_traces:
            self.post_traces[post_id] = SynapticTrace(
                trace_type=TraceType.POST_SPIKE,
                tau=self.rule_params['tau_minus']
            )
        
        # Update traces
        self.pre_traces[pre_id].add_spike(t)
        post_trace = self.post_traces[post_id].update(t)
        
        # Calculate weight change for LTD (post-before-pre)
        # With optional weight dependence: dw = -a_minus * post_trace * (w_max - w)^mu_minus
        w_max = self.rule_params['w_max']
        mu_minus = self.rule_params['mu_minus']
        
        if mu_minus > 0:
            # Weight-dependent depression
            dw = -self.rule_params['a_minus'] * post_trace * ((w_max - w) ** mu_minus)
        else:
            # Standard depression
            dw = -self.rule_params['a_minus'] * post_trace
        
        # Record weight change
        synapse_key = (pre_id, post_id)
        self.weight_changes[synapse_key] = self.weight_changes.get(synapse_key, 0.0) + dw
        
        return dw
    
    def on_post_spike(self, pre_id: int, post_id: int, t: float, w: float) -> float:
        """
        Process postsynaptic spike.
        
        Args:
            pre_id: Presynaptic neuron ID
            post_id: Postsynaptic neuron ID
            t: Current time
            w: Current weight
            
        Returns:
            Weight change
        """
        # Initialize traces if needed
        if pre_id not in self.pre_traces:
            self.pre_traces[pre_id] = SynapticTrace(
                trace_type=TraceType.PRE_SPIKE,
                tau=self.rule_params['tau_plus']
            )
        
        if post_id not in self.post_traces:
            self.post_traces[post_id] = SynapticTrace(
                trace_type=TraceType.POST_SPIKE,
                tau=self.rule_params['tau_minus']
            )
        
        # Update traces
        self.post_traces[post_id].add_spike(t)
        pre_trace = self.pre_traces[pre_id].update(t)
        
        # Calculate weight change for LTP (pre-before-post)
        # With optional weight dependence: dw = a_plus * pre_trace * (w_max - w)^mu_plus
        w_max = self.rule_params['w_max']
        mu_plus = self.rule_params['mu_plus']
        
        if mu_plus > 0:
            # Weight-dependent potentiation
            dw = self.rule_params['a_plus'] * pre_trace * ((w_max - w) ** mu_plus)
        else:
            # Standard potentiation
            dw = self.rule_params['a_plus'] * pre_trace
        
        # Record weight change
        synapse_key = (pre_id, post_id)
        self.weight_changes[synapse_key] = self.weight_changes.get(synapse_key, 0.0) + dw
        
        return dw
    
    def apply_weight_changes(self, connections: Dict[Tuple[int, int], Dict[str, Any]]):
        """
        Apply accumulated weight changes to connections.
        
        Args:
            connections: Dictionary of connections
            
        Returns:
            Dictionary with information about applied changes
        """
        applied_changes = {
            'total_changes': 0,
            'total_abs_change': 0.0,
            'max_change': 0.0,
            'changes_per_synapse': {}
        }
        
        for synapse_key, dw in self.weight_changes.items():
            if synapse_key in connections:
                # Get current weight
                w_old = connections[synapse_key]['weight']
                
                # Apply weight change with bounds
                w_new = np.clip(
                    w_old + dw,
                    self.rule_params['w_min'],
                    self.rule_params['w_max']
                )
                
                # Update weight
                connections[synapse_key]['weight'] = w_new
                
                # Record change
                applied_changes['total_changes'] += 1
                applied_changes['total_abs_change'] += abs(w_new - w_old)
                applied_changes['max_change'] = max(applied_changes['max_change'], abs(w_new - w_old))
                applied_changes['changes_per_synapse'][synapse_key] = w_new - w_old
        
        # Clear weight changes
        self.weight_changes.clear()
        
        return applied_changes
    
    def step(self, t_current: float, dt: float):
        """
        Update STDP rule for a time step.
        
        Args:
            t_current: Current simulation time
            dt: Time step
            
        Returns:
            Dictionary with update information
        """
        # Usually nothing to do for time-driven update in STDP
        # Most updates happen at spike times
        
        # But we could decay traces if needed
        for trace in self.pre_traces.values():
            trace.update(t_current)
            
        for trace in self.post_traces.values():
            trace.update(t_current)
            
        self.last_update_time = t_current
        
        return {
            'rule': 'STDP',
            'time': t_current,
            'traces_updated': len(self.pre_traces) + len(self.post_traces)
        }
    
    def reset(self):
        """Reset STDP rule state."""
        # Reset all traces
        for trace in self.pre_traces.values():
            trace.reset()
            
        for trace in self.post_traces.values():
            trace.reset()
            
        # Clear weight changes
        self.weight_changes.clear()
        
        # Reset time
        self.last_update_time = 0.0


class BCMRule:
    """
    Bienenstock-Cooper-Munro (BCM) plasticity rule.
    
    This implements the BCM rule:
    dw/dt = η * o * (o - θ_m) * x
    where:
    - o is the postsynaptic activity
    - θ_m is the sliding threshold
    - x is the presynaptic activity
    - η is the learning rate
    
    The sliding threshold θ_m is updated based on the recent activity:
    θ_m = <o^2>
    
    Attributes:
        params: BCM parameters
        thresholds: Activity thresholds for each neuron
        neuron_activities: Recent activity for each neuron
    """
    
    def __init__(self, params: Optional[PlasticityParameters] = None):
        """
        Initialize BCM rule.
        
        Args:
            params: Parameters for BCM rule
        """
        self.params = params if params else PlasticityParameters()
        self.rule_params = self.params.get_rule_params(PlasticityRule.BCM)
        
        # Neuron state
        self.thresholds = {}  # neuron_id -> threshold
        self.neuron_activities = {}  # neuron_id -> recent activity
        
        # Weight changes for each synapse
        self.weight_changes = {}  # (pre_id, post_id) -> weight_change
        
        # Last update time
        self.last_update_time = 0.0
    
    def initialize_neuron(self, neuron_id: int, initial_activity: float = 0.0):
        """
        Initialize neuron state.
        
        Args:
            neuron_id: Neuron ID
            initial_activity: Initial activity
        """
        self.thresholds[neuron_id] = self.rule_params['target_activity']
        self.neuron_activities[neuron_id] = initial_activity
    
    def update_threshold(self, neuron_id: int, t_current: float, dt: float):
        """
        Update sliding threshold based on recent activity.
        
        Args:
            neuron_id: Neuron ID
            t_current: Current time
            dt: Time step
            
        Returns:
            Updated threshold
        """
        if neuron_id not in self.thresholds:
            self.initialize_neuron(neuron_id)
            
        # Get recent activity
        activity = self.neuron_activities.get(neuron_id, 0.0)
        
        # Update threshold: τ_θ * dθ/dt = o^2 - θ
        tau = self.rule_params['sliding_threshold_tau']
        target = activity**2
        
        dthreshold = (target - self.thresholds[neuron_id]) * (dt / tau)
        self.thresholds[neuron_id] += dthreshold
        
        # Ensure threshold is positive
        self.thresholds[neuron_id] = max(1e-6, self.thresholds[neuron_id])
        
        return self.thresholds[neuron_id]
    
    def compute_weight_change(self, pre_id: int, post_id: int, pre_activity: float, post_activity: float) -> float:
        """
        Compute weight change based on BCM rule.
        
        Args:
            pre_id: Presynaptic neuron ID
            post_id: Postsynaptic neuron ID
            pre_activity: Presynaptic activity
            post_activity: Postsynaptic activity
            
        Returns:
            Weight change
        """
        # Get threshold
        if post_id not in self.thresholds:
            self.initialize_neuron(post_id)
            
        threshold = self.thresholds[post_id]
        
        # Compute BCM weight change: dw = η * o * (o - θ) * x
        learning_rate = self.rule_params['learning_rate']
        dw = learning_rate * post_activity * (post_activity - threshold) * pre_activity
        
        # Record weight change
        synapse_key = (pre_id, post_id)
        self.weight_changes[synapse_key] = self.weight_changes.get(synapse_key, 0.0) + dw
        
        return dw
    
    def apply_weight_changes(self, connections: Dict[Tuple[int, int], Dict[str, Any]]):
        """
        Apply accumulated weight changes to connections.
        
        Args:
            connections: Dictionary of connections
            
        Returns:
            Dictionary with information about applied changes
        """
        applied_changes = {
            'total_changes': 0,
            'total_abs_change': 0.0,
            'max_change': 0.0,
            'changes_per_synapse': {}
        }
        
        for synapse_key, dw in self.weight_changes.items():
            if synapse_key in connections:
                # Get current weight
                w_old = connections[synapse_key]['weight']
                
                # Apply weight change with bounds
                w_new = np.clip(
                    w_old + dw,
                    self.rule_params['w_min'],
                    self.rule_params['w_max']
                )
                
                # Update weight
                connections[synapse_key]['weight'] = w_new
                
                # Record change
                applied_changes['total_changes'] += 1
                applied_changes['total_abs_change'] += abs(w_new - w_old)
                applied_changes['max_change'] = max(applied_changes['max_change'], abs(w_new - w_old))
                applied_changes['changes_per_synapse'][synapse_key] = w_new - w_old
        
        # Clear weight changes
        self.weight_changes.clear()
        
        return applied_changes
    
    def on_spike(self, neuron_id: int, t: float):
        """
        Process a neuron spike.
        
        Args:
            neuron_id: Neuron ID
            t: Spike time
        """
        # Increase neuron activity
        if neuron_id not in self.neuron_activities:
            self.initialize_neuron(neuron_id)
            
        # Simple exponential activity model: each spike increases activity
        # and it decays over time in the step() method
        self.neuron_activities[neuron_id] += 1.0
    
    def step(self, t_current: float, dt: float):
        """
        Update BCM rule for a time step.
        
        Args:
            t_current: Current simulation time
            dt: Time step
            
        Returns:
            Dictionary with update information
        """
        # Decay neuron activities
        decay_factor = np.exp(-dt / 100.0)  # 100ms time constant for activity
        
        for neuron_id in self.neuron_activities:
            # Decay activity
            self.neuron_activities[neuron_id] *= decay_factor
            
            # Update threshold
            self.update_threshold(neuron_id, t_current, dt)
            
        self.last_update_time = t_current
        
        return {
            'rule': 'BCM',
            'time': t_current,
            'neurons_updated': len(self.neuron_activities)
        }
    
    def reset(self):
        """Reset BCM rule state."""
        # Reset thresholds to target activity
        for neuron_id in self.thresholds:
            self.thresholds[neuron_id] = self.rule_params['target_activity']
            
        # Clear activities
        self.neuron_activities.clear()
            
        # Clear weight changes
        self.weight_changes.clear()
        
        # Reset time
        self.last_update_time = 0.0


class OjaRule:
    """
    Oja's plasticity rule.
    
    This implements Oja's rule:
    dw/dt = η * y * (x - y * w)
    where:
    - y is the postsynaptic activity
    - x is the presynaptic activity
    - η is the learning rate
    
    Oja's rule performs principal component analysis and
    ensures normalized weights.
    
    Attributes:
        params: Oja rule parameters
        neuron_activities: Activity for each neuron
    """
    
    def __init__(self, params: Optional[PlasticityParameters] = None):
        """
        Initialize Oja's rule.
        
        Args:
            params: Parameters for Oja's rule
        """
        self.params = params if params else PlasticityParameters()
        self.rule_params = self.params.get_rule_params(PlasticityRule.OJA)
        
        # Neuron state
        self.neuron_activities = {}  # neuron_id -> activity
        
        # Weight changes for each synapse
        self.weight_changes = {}  # (pre_id, post_id) -> weight_change
        
        # Last update time
        self.last_update_time = 0.0
    
    def initialize_neuron(self, neuron_id: int, initial_activity: float = 0.0):
        """
        Initialize neuron state.
        
        Args:
            neuron_id: Neuron ID
            initial_activity: Initial activity
        """
        self.neuron_activities[neuron_id] = initial_activity
    
    def compute_weight_change(self, pre_id: int, post_id: int, pre_activity: float, post_activity: float, w: float) -> float:
        """
        Compute weight change based on Oja's rule.
        
        Args:
            pre_id: Presynaptic neuron ID
            post_id: Postsynaptic neuron ID
            pre_activity: Presynaptic activity
            post_activity: Postsynaptic activity
            w: Current weight
            
        Returns:
            Weight change
        """
        # Ensure neuron activities are initialized
        if pre_id not in self.neuron_activities:
            self.initialize_neuron(pre_id, pre_activity)
            
        if post_id not in self.neuron_activities:
            self.initialize_neuron(post_id, post_activity)
            
        # Compute Oja's rule: dw = η * y * (x - y * w)
        alpha = self.rule_params['alpha']
        dw = alpha * post_activity * (pre_activity - post_activity * w)
        
        # Record weight change
        synapse_key = (pre_id, post_id)
        self.weight_changes[synapse_key] = self.weight_changes.get(synapse_key, 0.0) + dw
        
        return dw
    
    def apply_weight_changes(self, connections: Dict[Tuple[int, int], Dict[str, Any]]):
        """
        Apply accumulated weight changes to connections.
        
        Args:
            connections: Dictionary of connections
            
        Returns:
            Dictionary with information about applied changes
        """
        applied_changes = {
            'total_changes': 0,
            'total_abs_change': 0.0,
            'max_change': 0.0,
            'changes_per_synapse': {}
        }
        
        for synapse_key, dw in self.weight_changes.items():
            if synapse_key in connections:
                # Get current weight
                w_old = connections[synapse_key]['weight']
                
                # Apply weight change with bounds
                w_new = np.clip(
                    w_old + dw,
                    self.rule_params['w_min'],
                    self.rule_params['w_max']
                )
                
                # Update weight
                connections[synapse_key]['weight'] = w_new
                
                # Record change
                applied_changes['total_changes'] += 1
                applied_changes['total_abs_change'] += abs(w_new - w_old)
                applied_changes['max_change'] = max(applied_changes['max_change'], abs(w_new - w_old))
                applied_changes['changes_per_synapse'][synapse_key] = w_new - w_old
        
        # Clear weight changes
        self.weight_changes.clear()
        
        return applied_changes
    
    def on_spike(self, neuron_id: int, t: float):
        """
        Process a neuron spike.
        
        Args:
            neuron_id: Neuron ID
            t: Spike time
        """
        # Increase neuron activity
        if neuron_id not in self.neuron_activities:
            self.initialize_neuron(neuron_id)
            
        # Simple activity model
        self.neuron_activities[neuron_id] += 1.0
    
    def step(self, t_current: float, dt: float):
        """
        Update Oja's rule for a time step.
        
        Args:
            t_current: Current simulation time
            dt: Time step
            
        Returns:
            Dictionary with update information
        """
        # Decay neuron activities
        decay_factor = np.exp(-dt / 100.0)  # 100ms time constant for activity
        
        for neuron_id in self.neuron_activities:
            self.neuron_activities[neuron_id] *= decay_factor
            
        self.last_update_time = t_current
        
        return {
            'rule': 'Oja',
            'time': t_current,
            'neurons_updated': len(self.neuron_activities)
        }
    
    def reset(self):
        """Reset Oja's rule state."""
        # Clear activities
        self.neuron_activities.clear()
            
        # Clear weight changes
        self.weight_changes.clear()
        
        # Reset time
        self.last_update_time = 0.0


class HomeostaticRule:
    """
    Homeostatic plasticity rule.
    
    This implements homeostatic plasticity, which adjusts neuronal properties
    to maintain target activity levels.
    
    Attributes:
        params: Homeostatic plasticity parameters
        target_rates: Target firing rates for each neuron
        actual_rates: Actual firing rates for each neuron
    """
    
    def __init__(self, params: Optional[PlasticityParameters] = None):
        """
        Initialize homeostatic plasticity rule.
        
        Args:
            params: Parameters for homeostatic plasticity
        """
        self.params = params if params else PlasticityParameters()
        self.rule_params = self.params.get_rule_params(PlasticityRule.HOMEOSTATIC)
        
        # Neuron state
        self.target_rates = {}  # neuron_id -> target rate
        self.actual_rates = {}  # neuron_id -> actual rate
        self.spike_counts = {}  # neuron_id -> spike count
        self.last_spike_times = {}  # neuron_id -> last spike time
        
        # Threshold adjustments
        self.threshold_changes = {}  # neuron_id -> threshold change
        
        # Last update time
        self.last_update_time = 0.0
    
    def initialize_neuron(self, neuron_id: int, target_rate: Optional[float] = None):
        """
        Initialize neuron state.
        
        Args:
            neuron_id: Neuron ID
            target_rate: Target firing rate (Hz)
        """
        if target_rate is None:
            target_rate = self.rule_params['target_rate']
            
        self.target_rates[neuron_id] = target_rate
        self.actual_rates[neuron_id] = 0.0
        self.spike_counts[neuron_id] = 0
        self.last_spike_times[neuron_id] = 0.0
    
    def update_firing_rate(self, neuron_id: int, t_current: float, window: float = 1000.0):
        """
        Update firing rate estimate.
        
        Args:
            neuron_id: Neuron ID
            t_current: Current time
            window: Time window for rate estimation (ms)
            
        Returns:
            Estimated firing rate (Hz)
        """
        if neuron_id not in self.spike_counts:
            self.initialize_neuron(neuron_id)
            
        # If no spikes yet, return 0
        if self.spike_counts[neuron_id] == 0:
            self.actual_rates[neuron_id] = 0.0
            return 0.0
            
        # Compute time since first spike
        time_period = max(1.0, t_current - self.last_spike_times[neuron_id] + window)
        
        # Compute rate (spikes per second)
        rate = self.spike_counts[neuron_id] * 1000.0 / time_period
        
        # Update rate with exponential moving average
        alpha = 0.1
        self.actual_rates[neuron_id] = (1 - alpha) * self.actual_rates[neuron_id] + alpha * rate
        
        return self.actual_rates[neuron_id]
    
    def compute_threshold_adjustment(self, neuron_id: int, t_current: float, dt: float) -> float:
        """
        Compute threshold adjustment based on rate difference.
        
        Args:
            neuron_id: Neuron ID
            t_current: Current time
            dt: Time step
            
        Returns:
            Threshold adjustment
        """
        # Ensure neuron is initialized
        if neuron_id not in self.target_rates:
            self.initialize_neuron(neuron_id)
            
        # Update firing rate estimate
        actual_rate = self.update_firing_rate(neuron_id, t_current)
        target_rate = self.target_rates[neuron_id]
        
        # Compute rate difference
        rate_diff = actual_rate - target_rate
        
        # Compute adjustment: faster homeostasis for large deviations
        tau = self.rule_params['homeostatic_tau']
        scale = self.rule_params['homeostatic_scale']
        
        # Threshold adjustment (postive adjustment means raising threshold to decrease rate)
        adjustment = scale * rate_diff * (dt / tau)
        
        # Record adjustment
        self.threshold_changes[neuron_id] = self.threshold_changes.get(neuron_id, 0.0) + adjustment
        
        return adjustment
    
    def normalize_weights(self, connections, neuron_id, target_sum=1.0):
        """
        Normalize weights of incoming connections to a neuron.
        
        Args:
            connections: Dictionary of connections
            neuron_id: Neuron ID
            target_sum: Target sum for normalized weights
            
        Returns:
            Number of weights normalized
        """
        # Find all incoming connections to this neuron
        incoming_connections = {}
        for key, conn in connections.items():
            pre_id, post_id = key
            if post_id == neuron_id:
                incoming_connections[key] = conn
        
        if not incoming_connections:
            return 0
        
        # Calculate sum of weights
        weights_sum = sum(conn['weight'] for conn in incoming_connections.values())
        
        if weights_sum < 1e-10:
            return 0
        
        # Normalize weights
        normalization_factor = target_sum / weights_sum
        normalized_count = 0
        
        for key, conn in incoming_connections.items():
            old_weight = conn['weight']
            new_weight = old_weight * normalization_factor
            connections[key]['weight'] = new_weight
            normalized_count += 1
        
        return normalized_count
    
    def apply_threshold_adjustments(self, neurons: Dict[int, Any]):
        """
        Apply threshold adjustments to neurons.
        
        Args:
            neurons: Dictionary of neurons
            
        Returns:
            Dictionary with information about applied adjustments
        """
        applied_adjustments = {
            'total_adjustments': 0,
            'total_abs_adjustment': 0.0,
            'max_adjustment': 0.0,
            'adjustments_per_neuron': {}
        }
        
        for neuron_id, adjustment in self.threshold_changes.items():
            if neuron_id in neurons:
                # Get current threshold (if the neuron has a v_threshold attribute)
                if hasattr(neurons[neuron_id], 'params'):
                    old_threshold = neurons[neuron_id].params.v_threshold
                    
                    # Apply adjustment
                    new_threshold = old_threshold + adjustment
                    
                    # Ensure threshold stays positive
                    new_threshold = max(0.1, new_threshold)
                    
                    # Update threshold
                    neurons[neuron_id].params.v_threshold = new_threshold
                    
                    # Record adjustment
                    applied_adjustments['total_adjustments'] += 1
                    applied_adjustments['total_abs_adjustment'] += abs(adjustment)
                    applied_adjustments['max_adjustment'] = max(applied_adjustments['max_adjustment'], abs(adjustment))
                    applied_adjustments['adjustments_per_neuron'][neuron_id] = adjustment
        
        # Clear adjustments
        self.threshold_changes.clear()
        
        return applied_adjustments
    
    def on_spike(self, neuron_id: int, t: float):
        """
        Process a neuron spike.
        
        Args:
            neuron_id: Neuron ID
            t: Spike time
        """
        # Ensure neuron is initialized
        if neuron_id not in self.spike_counts:
            self.initialize_neuron(neuron_id)
            
        # Increment spike count
        self.spike_counts[neuron_id] += 1
        
        # Update last spike time
        self.last_spike_times[neuron_id] = t
    
    def step(self, t_current: float, dt: float):
        """
        Update homeostatic rule for a time step.
        
        Args:
            t_current: Current simulation time
            dt: Time step
            
        Returns:
            Dictionary with update information
        """
        # Update firing rate estimates and compute threshold adjustments
        for neuron_id in self.target_rates:
            self.compute_threshold_adjustment(neuron_id, t_current, dt)
            
        self.last_update_time = t_current
        
        return {
            'rule': 'Homeostatic',
            'time': t_current,
            'neurons_updated': len(self.target_rates)
        }
    
    def reset(self):
        """Reset homeostatic rule state."""
        # Clear spike counts and rates
        self.actual_rates.clear()
        self.spike_counts.clear()
        self.last_spike_times.clear()
            
        # Clear threshold changes
        self.threshold_changes.clear()
        
        # Reset time
        self.last_update_time = 0.0


class StructuralPlasticityRule:
    """
    Structural plasticity rule.
    
    This implements mechanisms for creating and removing synaptic connections
    based on activity and correlation.
    
    Attributes:
        params: Structural plasticity parameters
        neuron_activities: Activity for each neuron
        potential_synapses: Dictionary of potential synapse information
    """
    
    def __init__(self, params: Optional[PlasticityParameters] = None):
        """
        Initialize structural plasticity rule.
        
        Args:
            params: Parameters for structural plasticity
        """
        self.params = params if params else PlasticityParameters()
        self.rule_params = self.params.get_rule_params(PlasticityRule.STRUCTURAL)
        
        # Neuron state
        self.neuron_activities = {}  # neuron_id -> activity trace
        
        # Potential synapses that might be created
        self.potential_synapses = {}  # (pre_id, post_id) -> connectivity potential
        
        # Synapses marked for creation or removal
        self.synapses_to_create = set()
        self.synapses_to_remove = set()
        
        # Activity correlation matrix
        self.activity_correlation = None
        
        # Last update time
        self.last_update_time = 0.0
    
    def initialize_neuron(self, neuron_id: int):
        """
        Initialize neuron state.
        
        Args:
            neuron_id: Neuron ID
        """
        self.neuron_activities[neuron_id] = SynapticTrace(
            trace_type=TraceType.POST_SPIKE,
            tau=100.0  # 100ms time constant for activity
        )
    
    def compute_activity_correlation(self, neurons: Dict[int, Any], spike_history: Optional[np.ndarray] = None):
        """
        Compute correlation of activity between neuron pairs.
        
        Args:
            neurons: Dictionary of neurons
            spike_history: Optional spike history matrix
            
        Returns:
            Activity correlation matrix
        """
        neuron_ids = list(neurons.keys())
        n_neurons = len(neuron_ids)
        
        # If we have spike history, use it
        if spike_history is not None:
            # Use recent spike history limited by correlation window
            history_length = min(spike_history.shape[1], self.rule_params['correlation_window'])
            recent_history = spike_history[:, -history_length:].astype(float)
            
            # Normalize each neuron's activity
            for i in range(n_neurons):
                if np.std(recent_history[i]) > 1e-10:
                    recent_history[i] = (recent_history[i] - np.mean(recent_history[i])) / np.std(recent_history[i])
            
            # Compute correlation matrix
            correlation_matrix = np.zeros((n_neurons, n_neurons))
            for i in range(n_neurons):
                for j in range(i+1, n_neurons):
                    corr = np.mean(recent_history[i] * recent_history[j]) / history_length
                    correlation_matrix[i, j] = correlation_matrix[j, i] = corr
            
            return correlation_matrix
        
        # Otherwise use activity traces
        correlation_matrix = np.zeros((n_neurons, n_neurons))
        
        for i, neuron_i in enumerate(neuron_ids):
            for j, neuron_j in enumerate(neuron_ids[i+1:], i+1):
                # Use normalized activity values
                if neuron_i in self.neuron_activities and neuron_j in self.neuron_activities:
                    corr = (self.neuron_activities[neuron_i].value * self.neuron_activities[neuron_j].value) / 1.0
                    correlation_matrix[i, j] = correlation_matrix[j, i] = corr
        
        return correlation_matrix
    
    def evaluate_synapse_creation(self, pre_id: int, post_id: int, t_current: float, connections: Dict[Tuple[int, int], Dict[str, Any]]):
        """
        Evaluate whether to create a synapse.
        
        Args:
            pre_id: Presynaptic neuron ID
            post_id: Postsynaptic neuron ID
            t_current: Current time
            connections: Existing connections
            
        Returns:
            True if synapse should be created, False otherwise
        """
        # Check if synapse already exists
        if (pre_id, post_id) in connections:
            return False
            
        # Check if neurons exist
        if pre_id not in self.neuron_activities or post_id not in self.neuron_activities:
            return False
            
        # Get activity traces
        pre_activity = self.neuron_activities[pre_id].update(t_current)
        post_activity = self.neuron_activities[post_id].update(t_current)
        
        # Compute correlation: simple product of activities
        correlation = pre_activity * post_activity
        
        # Update synapse creation potential
        synapse_key = (pre_id, post_id)
        current_potential = self.potential_synapses.get(synapse_key, 0.0)
        
        # Update potential with decay and new correlation
        decay_factor = 0.995  # Slow decay
        new_potential = current_potential * decay_factor + correlation
        
        self.potential_synapses[synapse_key] = new_potential
        
        # Decide whether to create synapse based on potential
        # Higher correlation -> higher chance of creation
        creation_prob = min(1.0, self.rule_params['creation_p'] * new_potential)
        should_create = np.random.random() < creation_prob
        
        if should_create:
            self.synapses_to_create.add(synapse_key)
            self.potential_synapses[synapse_key] = 0.0  # Reset potential
            
        return should_create
    
    def evaluate_synapse_removal(self, pre_id: int, post_id: int, weight: float):
        """
        Evaluate whether to remove a synapse.
        
        Args:
            pre_id: Presynaptic neuron ID
            post_id: Postsynaptic neuron ID
            weight: Synapse weight
            
        Returns:
            True if synapse should be removed, False otherwise
        """
        # Check if weight is below minimum threshold
        if abs(weight) < self.rule_params['min_weight']:
            # Higher chance of removal for very weak synapses
            removal_prob = self.rule_params['elimination_p'] * (1.0 - abs(weight) / self.rule_params['min_weight'])
            should_remove = np.random.random() < removal_prob
            
            if should_remove:
                self.synapses_to_remove.add((pre_id, post_id))
                
            return should_remove
            
        return False
    
    def apply_structural_changes(self, connections: Dict[Tuple[int, int], Dict[str, Any]], connection_creator: Callable):
        """
        Apply structural changes to connections.
        
        Args:
            connections: Dictionary of connections
            connection_creator: Function to create new connections
            
        Returns:
            Dictionary with information about applied changes
        """
        applied_changes = {
            'created': [],
            'removed': [],
            'total_created': 0,
            'total_removed': 0
        }
        
        # Remove synapses
        for pre_id, post_id in self.synapses_to_remove:
            if (pre_id, post_id) in connections:
                # Remove connection
                del connections[(pre_id, post_id)]
                
                # Record removal
                applied_changes['removed'].append((pre_id, post_id))
                applied_changes['total_removed'] += 1
                
        # Create synapses
        for pre_id, post_id in self.synapses_to_create:
            if (pre_id, post_id) not in connections:
                # Create connection with initial weight
                initial_weight = self.rule_params['growth_factor']
                
                # Use provided connection creator function
                new_connections = connection_creator(
                    pre_ids=[pre_id],
                    post_ids=[post_id],
                    connection_prob=1.0,
                    weight_range=(initial_weight, initial_weight),
                    delay_range=(1.0, 2.0)
                )
                
                # Record creation
                if new_connections:
                    applied_changes['created'].append((pre_id, post_id))
                    applied_changes['total_created'] += 1
                
        # Clear lists
        self.synapses_to_create.clear()
        self.synapses_to_remove.clear()
        
        return applied_changes
    
    def on_spike(self, neuron_id: int, t: float):
        """
        Process a neuron spike.
        
        Args:
            neuron_id: Neuron ID
            t: Spike time
        """
        # Ensure neuron is initialized
        if neuron_id not in self.neuron_activities:
            self.initialize_neuron(neuron_id)
            
        # Update activity trace
        self.neuron_activities[neuron_id].add_spike(t)
    
    def step(self, t_current: float, dt: float, connections: Dict[Tuple[int, int], Dict[str, Any]], neurons: Dict[int, Any], spike_history: Optional[np.ndarray] = None):
        """
        Update structural plasticity rule for a time step.
        
        Args:
            t_current: Current simulation time
            dt: Time step
            connections: Dictionary of connections
            neurons: Dictionary of neurons
            spike_history: Optional spike history matrix
            
        Returns:
            Dictionary with update information
        """
        # Update activity traces
        for neuron_id, trace in self.neuron_activities.items():
            trace.update(t_current)
            
        # Update activity correlation periodically (every 100ms)
        if int(t_current / 100.0) > int(self.last_update_time / 100.0):
            self.activity_correlation = self.compute_activity_correlation(neurons, spike_history)
            
        # Probabilistic evaluation of potential new synapses
        # (Too expensive to check all possible pairs, so we sample)
        
        # Sample some neuron pairs
        neuron_ids = list(neurons.keys())
        n_samples = min(self.rule_params['max_connections_per_step'], len(neuron_ids) * (len(neuron_ids) - 1) // 2)
        
        for _ in range(n_samples):
            # Sample two different neurons
            i, j = np.random.choice(len(neuron_ids), 2, replace=False)
            pre_id, post_id = neuron_ids[i], neuron_ids[j]
            
            # Evaluate synapse creation
            self.evaluate_synapse_creation(pre_id, post_id, t_current, connections)
            
        # Check existing synapses for removal
        for (pre_id, post_id), conn in list(connections.items())[:self.rule_params['max_connections_per_step']]:
            weight = conn['weight']
            self.evaluate_synapse_removal(pre_id, post_id, weight)
            
        self.last_update_time = t_current
        
        return {
            'rule': 'Structural',
            'time': t_current,
            'neurons_updated': len(self.neuron_activities),
            'synapses_to_create': len(self.synapses_to_create),
            'synapses_to_remove': len(self.synapses_to_remove)
        }
    
    def reset(self):
        """Reset structural plasticity rule state."""
        # Clear activity traces
        for trace in self.neuron_activities.values():
            trace.reset()
            
        # Clear potential synapses
        self.potential_synapses.clear()
        
        # Clear creation/removal lists
        self.synapses_to_create.clear()
        self.synapses_to_remove.clear()
        
        # Reset time
        self.last_update_time = 0.0


class ConsolidationRule:
    """
    Consolidated plasticity rule for long-term memory.
    
    This implements synaptic tagging and capture for synaptic consolidation.
    
    Attributes:
        params: Consolidation parameters
        synaptic_tags: Tags for each synapse
        consolidated_weights: Consolidated weights for each synapse
    """
    
    def __init__(self, params: Optional[PlasticityParameters] = None):
        """
        Initialize consolidation rule.
        
        Args:
            params: Parameters for consolidation
        """
        self.params = params if params else PlasticityParameters()
        self.rule_params = self.params.get_rule_params(PlasticityRule.CONSOLIDATED)
        
        # Synapse state
        self.synaptic_tags = {}  # (pre_id, post_id) -> tag value
        self.consolidated_weights = {}  # (pre_id, post_id) -> consolidated weight
        
        # Last update time
        self.last_update_time = 0.0
    
    def initialize_synapse(self, pre_id: int, post_id: int, weight: float):
        """
        Initialize synapse state.
        
        Args:
            pre_id: Presynaptic neuron ID
            post_id: Postsynaptic neuron ID
            weight: Initial weight
        """
        synapse_key = (pre_id, post_id)
        self.synaptic_tags[synapse_key] = 0.0
        self.consolidated_weights[synapse_key] = weight
    
    def update_tag(self, pre_id: int, post_id: int, weight_change: float):
        """
        Update synaptic tag based on weight change.
        
        Args:
            pre_id: Presynaptic neuron ID
            post_id: Postsynaptic neuron ID
            weight_change: Change in synaptic weight
            
        Returns:
            Updated tag value
        """
        synapse_key = (pre_id, post_id)
        
        # Ensure synapse is initialized
        if synapse_key not in self.synaptic_tags:
            # Use current weight as initial consolidated weight
            initial_weight = weight_change  # Assume initial weight is 0
            self.initialize_synapse(pre_id, post_id, initial_weight)
        
        # Update tag based on weight change
        # Larger changes produce stronger tags
        tagging_rate = self.rule_params['tagging_rate']
        self.synaptic_tags[synapse_key] += tagging_rate * abs(weight_change)
        
        # Cap tag value
        self.synaptic_tags[synapse_key] = min(1.0, self.synaptic_tags[synapse_key])
        
        return self.synaptic_tags[synapse_key]
    
    def update_consolidation(self, pre_id: int, post_id: int, current_weight: float, dt: float):
        """
        Update consolidation based on tag value.
        
        Args:
            pre_id: Presynaptic neuron ID
            post_id: Postsynaptic neuron ID
            current_weight: Current synaptic weight
            dt: Time step
            
        Returns:
            Consolidated weight
        """
        synapse_key = (pre_id, post_id)
        
        # Ensure synapse is initialized
        if synapse_key not in self.consolidated_weights:
            self.initialize_synapse(pre_id, post_id, current_weight)
            
        # Get tag and consolidated weight
        tag = self.synaptic_tags[synapse_key]
        consolidated = self.consolidated_weights[synapse_key]
        
        # Decay tag
        tag_decay = tag * 0.1 * (dt / 1000.0)  # Decay over seconds
        self.synaptic_tags[synapse_key] = max(0.0, tag - tag_decay)
        
        # Consolidate weight if tag is above threshold
        if tag > self.rule_params['consolidation_threshold']:
            # Move consolidated weight toward current weight
            consolidation_rate = self.rule_params['consolidation_rate'] * (dt / 1.0)  # Per ms
            weight_diff = current_weight - consolidated
            consolidation_change = consolidation_rate * weight_diff
            
            self.consolidated_weights[synapse_key] += consolidation_change
            
        return self.consolidated_weights[synapse_key]
    
    def get_consolidated_weight(self, pre_id: int, post_id: int):
        """
        Get consolidated weight for a synapse.
        
        Args:
            pre_id: Presynaptic neuron ID
            post_id: Postsynaptic neuron ID
            
        Returns:
            Consolidated weight
        """
        synapse_key = (pre_id, post_id)
        return self.consolidated_weights.get(synapse_key, 0.0)
    
    def step(self, t_current: float, dt: float, connections: Dict[Tuple[int, int], Dict[str, Any]]):
        """
        Update consolidation rule for a time step.
        
        Args:
            t_current: Current simulation time
            dt: Time step
            connections: Dictionary of connections
            
        Returns:
            Dictionary with update information
        """
        # Update consolidation for all synapses
        for (pre_id, post_id), conn in connections.items():
            weight = conn['weight']
            self.update_consolidation(pre_id, post_id, weight, dt)
            
        self.last_update_time = t_current
        
        return {
            'rule': 'Consolidation',
            'time': t_current,
            'synapses_updated': len(self.consolidated_weights)
        }
    
    def reset(self):
        """Reset consolidation rule state."""
        # Clear tags and consolidated weights
        self.synaptic_tags.clear()
        self.consolidated_weights.clear()
        
        # Reset time
        self.last_update_time = 0.0


class NeuromorphicCoreInterface:
    """
    Interface for connecting the Neuroplasticity Engine to a NeuromorphicCore.
    
    This provides an abstraction layer to work with different implementations
    of neuromorphic cores.
    """
    
    def __init__(self, core):
        """
        Initialize the interface.
        
        Args:
            core: The neuromorphic core
        """
        self.core = core
        
        # Cache core properties
        self.has_spike_history = hasattr(core, 'spike_history')
        self.has_activity_history = hasattr(core, 'activity_history')
        self.has_connection_weights = hasattr(core, 'connection_weights') or hasattr(core, 'weights')
        self.has_neuron_positions = hasattr(core, 'positions')
        
        # Determine connection attribute names
        if hasattr(core, 'connections'):
            self.connections_attr = 'connections'
        elif hasattr(core, 'weights'):
            self.connections_attr = 'weights'
        else:
            self.connections_attr = None
            
        # Determine weights attribute names
        if hasattr(core, 'connection_weights'):
            self.weights_attr = 'connection_weights'
        elif hasattr(core, 'weights'):
            self.weights_attr = 'weights'
        else:
            self.weights_attr = None
            
    def get_neurons(self) -> Dict[int, Any]:
        """
        Get neurons from the core.
        
        Returns:
            Dictionary of neurons
        """
        if hasattr(self.core, 'neurons'):
            return self.core.neurons
        elif hasattr(self.core, 'get_neurons'):
            return self.core.get_neurons()
        else:
            return {}
            
    def get_connections(self) -> Dict[Tuple[int, int], Dict[str, Any]]:
        """
        Get connections from the core.
        
        Returns:
            Dictionary of connections
        """
        if self.connections_attr is not None:
            connections = getattr(self.core, self.connections_attr)
            
            # Convert to dictionary if needed
            if isinstance(connections, dict):
                return connections
            elif scipy.sparse.issparse(connections):
                # Convert sparse matrix to dictionary
                result = {}
                coo = connections.tocoo()
                for i, j, v in zip(coo.row, coo.col, coo.data):
                    if v != 0:
                        if self.weights_attr is not None:
                            weight = getattr(self.core, self.weights_attr)[i, j]
                        else:
                            weight = v
                        result[(i, j)] = {'weight': weight}
                return result
            else:
                return {}
        elif hasattr(self.core, 'get_connections'):
            return self.core.get_connections()
        else:
            return {}
            
    def modify_weight(self, pre_id: int, post_id: int, new_weight: float) -> bool:
        """
        Modify a connection weight.
        
        Args:
            pre_id: Presynaptic neuron ID
            post_id: Postsynaptic neuron ID
            new_weight: New weight value
            
        Returns:
            True if successful, False otherwise
        """
        # Try different methods to modify weight
        if hasattr(self.core, 'modify_weight'):
            return self.core.modify_weight(pre_id, post_id, new_weight)
        elif hasattr(self.core, 'update_synapse_weight'):
            return self.core.update_synapse_weight(pre_id, post_id, new_weight)
        elif self.weights_attr is not None:
            weights = getattr(self.core, self.weights_attr)
            if isinstance(weights, dict):
                weights[(pre_id, post_id)] = new_weight
                return True
            elif scipy.sparse.issparse(weights):
                weights[pre_id, post_id] = new_weight
                return True
        
        # No method found
        return False
            
    def add_connection(self, pre_id: int, post_id: int, weight: float) -> bool:
        """
        Add a new connection.
        
        Args:
            pre_id: Presynaptic neuron ID
            post_id: Postsynaptic neuron ID
            weight: Connection weight
            
        Returns:
            True if successful, False otherwise
        """
        # Try different methods to add connection
        if hasattr(self.core, 'add_connection'):
            return self.core.add_connection(pre_id, post_id, weight)
        elif hasattr(self.core, 'connect_neurons'):
            new_connections = self.core.connect_neurons([pre_id], [post_id], 1.0, 
                                                       weight_range=(weight, weight))
            return len(new_connections) > 0
        elif self.connections_attr is not None:
            connections = getattr(self.core, self.connections_attr)
            if isinstance(connections, dict):
                connections[(pre_id, post_id)] = {'weight': weight}
                return True
            elif scipy.sparse.issparse(connections):
                # Need to update both connections and weights
                connections[pre_id, post_id] = 1
                if self.weights_attr is not None:
                    weights = getattr(self.core, self.weights_attr)
                    weights[pre_id, post_id] = weight
                return True
        
        # No method found
        return False
            
    def remove_connection(self, pre_id: int, post_id: int) -> bool:
        """
        Remove a connection.
        
        Args:
            pre_id: Presynaptic neuron ID
            post_id: Postsynaptic neuron ID
            
        Returns:
            True if successful, False otherwise
        """
        # Try different methods to remove connection
        if hasattr(self.core, 'remove_connection'):
            return self.core.remove_connection(pre_id, post_id)
        elif self.connections_attr is not None:
            connections = getattr(self.core, self.connections_attr)
            if isinstance(connections, dict):
                if (pre_id, post_id) in connections:
                    del connections[(pre_id, post_id)]
                    return True
            elif scipy.sparse.issparse(connections):
                connections[pre_id, post_id] = 0
                if self.weights_attr is not None:
                    weights = getattr(self.core, self.weights_attr)
                    weights[pre_id, post_id] = 0
                return True
        
        # No method found
        return False
    
    def get_spike_history(self):
        """
        Get spike history from the core.
        
        Returns:
            Spike history array
        """
        if self.has_spike_history:
            return self.core.spike_history
        else:
            return None
    
    def get_activity_history(self):
        """
        Get activity history from the core.
        
        Returns:
            Activity history array
        """
        if self.has_activity_history:
            return self.core.activity_history
        else:
            return None


class ConnectionCreator:
    """
    Helper class for creating connections during structural plasticity.
    
    This serves as a wrapper around the core.connect_neurons method.
    """
    
    def __init__(self, core_connect_method: Callable):
        """
        Initialize connection creator.
        
        Args:
            core_connect_method: Method for creating connections in the core
        """
        self.core_connect_method = core_connect_method
    
    def __call__(self, pre_ids: List[int], post_ids: List[int], **kwargs):
        """
        Create connections.
        
        Args:
            pre_ids: Presynaptic neuron IDs
            post_ids: Postsynaptic neuron IDs
            **kwargs: Additional arguments for connection creation
            
        Returns:
            List of created connections
        """
        return self.core_connect_method(pre_ids, post_ids, **kwargs)


class NeuroplasticityEngine:
    """
    Engine for managing synaptic plasticity in neural networks.
    
    This implements various plasticity rules and coordinates their application
    to synaptic weights and neural properties.
    
    Attributes:
        params: Plasticity parameters
        rules: Dictionary of active plasticity rules
        core: Reference to neuromorphic core
    """
    
    def __init__(self, params: Optional[Dict[str, Any]] = None):
        """
        Initialize neuroplasticity engine.
        
        Args:
            params: Plasticity parameters dictionary
        """
        self.params = params if params else {}
        
        # Set default parameters
        self._set_default_params()
        
        # Active plasticity rules
        self.rules = {}  # rule_name -> rule_instance
        
        # Neural network core (set later via connect_to_core)
        self.core = None
        self.core_interface = None
        
        # Connection creator callback
        self.connection_creator = None
        
        # Track neurons and synapses
        self.neuron_ids = set()
        self.connections = {}
        
        # Statistics
        self.stats = {
            'weight_changes': 0,
            'new_connections': 0,
            'removed_connections': 0,
            'total_updates': 0
        }
        
        # Current time
        self.current_time = 0.0
        
        # Performance optimization
        self.update_interval = 1.0  # ms
        self.last_full_update = 0.0
        self.selective_update = True
        
        # Rule priorities for selective update
        self.rule_priorities = {
            'stdp': 1,           # High priority - update every step
            'bcm': 2,            # Medium priority - update every few steps
            'oja': 2,            # Medium priority
            'homeostatic': 3,    # Lower priority - update less frequently
            'structural': 4,     # Lowest priority - most expensive
            'consolidated': 3    # Lower priority
        }
    
    def _set_default_params(self):
        """Set default parameters."""
        defaults = {
            'stdp_enabled': True,
            'homeostatic_enabled': True,
            'structural_enabled': False,
            'consolidation_enabled': False,
            'default_params': PlasticityParameters()
        }
        
        for key, value in defaults.items():
            if key not in self.params:
                self.params[key] = value
    
    def connect_to_core(self, core):
        """
        Connect to neuromorphic core.
        
        Args:
            core: Neuromorphic core instance
        """
        self.core = core
        self.core_interface = NeuromorphicCoreInterface(core)
        
        # Create connection creator
        if hasattr(core, 'connect_neurons'):
            self.connection_creator = ConnectionCreator(core.connect_neurons)
        
        # Initialize default rules if enabled
        if self.params['stdp_enabled']:
            self.add_rule(PlasticityRule.STDP)
            
        if self.params['homeostatic_enabled']:
            self.add_rule(PlasticityRule.HOMEOSTATIC)
            
        if self.params['structural_enabled']:
            self.add_rule(PlasticityRule.STRUCTURAL)
            
        if self.params['consolidation_enabled']:
            self.add_rule(PlasticityRule.CONSOLIDATED)
            
        # Load neurons and connections from core
        self.load_state_from_core()
        
        logger.info(f"Connected to core with {len(self.neuron_ids)} neurons and {len(self.connections)} connections")
    
    def load_state_from_core(self):
        """Load neurons and connections from the core."""
        if self.core_interface is None:
            return
            
        # Load neurons
        neurons = self.core_interface.get_neurons()
        for neuron_id in neurons:
            self.neuron_ids.add(neuron_id)
            
        # Load connections
        connections = self.core_interface.get_connections()
        for (pre_id, post_id), conn in connections.items():
            self.connections[(pre_id, post_id)] = conn.copy()
    
    def add_rule(self, rule_type: PlasticityRule, custom_params: Optional[PlasticityParameters] = None):
        """
        Add a plasticity rule.
        
        Args:
            rule_type: Type of plasticity rule
            custom_params: Custom parameters for the rule
            
        Returns:
            Plasticity rule instance
        """
        # Use custom parameters or default
        params = custom_params if custom_params else self.params['default_params']
        
        # Create rule instance
        if rule_type == PlasticityRule.STDP:
            rule = STDPRule(params)
        elif rule_type == PlasticityRule.BCM:
            rule = BCMRule(params)
        elif rule_type == PlasticityRule.OJA:
            rule = OjaRule(params)
        elif rule_type == PlasticityRule.HOMEOSTATIC:
            rule = HomeostaticRule(params)
        elif rule_type == PlasticityRule.STRUCTURAL:
            rule = StructuralPlasticityRule(params)
        elif rule_type == PlasticityRule.CONSOLIDATED:
            rule = ConsolidationRule(params)
        else:
            raise ValueError(f"Unsupported plasticity rule: {rule_type}")
            
        # Add rule to active rules
        rule_name = rule_type.name.lower()
        self.rules[rule_name] = rule
        
        # Register existing neurons with the rule
        self._register_neurons_with_rule(rule_name, rule)
        
        return rule
    
    def _register_neurons_with_rule(self, rule_name: str, rule: Any):
        """
        Register existing neurons with a new rule.
        
        Args:
            rule_name: Name of the rule
            rule: Rule instance
        """
        for neuron_id in self.neuron_ids:
            if rule_name == 'stdp':
                rule.initialize_traces([neuron_id])
            elif rule_name == 'bcm':
                rule.initialize_neuron(neuron_id)
            elif rule_name == 'oja':
                rule.initialize_neuron(neuron_id)
            elif rule_name == 'homeostatic':
                rule.initialize_neuron(neuron_id)
            elif rule_name == 'structural':
                rule.initialize_neuron(neuron_id)
    
    def remove_rule(self, rule_name: str):
        """
        Remove a plasticity rule.
        
        Args:
            rule_name: Name of the rule to remove
        """
        if rule_name in self.rules:
            del self.rules[rule_name]
    
    def register_neuron(self, neuron_id: int):
        """
        Register a neuron with the plasticity engine.
        
        Args:
            neuron_id: Neuron ID
        """
        self.neuron_ids.add(neuron_id)
        
        # Initialize neuron in applicable rules
        for rule_name, rule in self.rules.items():
            if rule_name == 'stdp':
                rule.initialize_traces([neuron_id])
            elif rule_name == 'bcm':
                rule.initialize_neuron(neuron_id)
            elif rule_name == 'oja':
                rule.initialize_neuron(neuron_id)
            elif rule_name == 'homeostatic':
                rule.initialize_neuron(neuron_id)
            elif rule_name == 'structural':
                rule.initialize_neuron(neuron_id)
    
    def register_synapse(self, pre_id: int, post_id: int, weight: float):
        """
        Register a synapse with the plasticity engine.
        
        Args:
            pre_id: Presynaptic neuron ID
            post_id: Postsynaptic neuron ID
            weight: Synapse weight
        """
        synapse_key = (pre_id, post_id)
        self.connections[synapse_key] = {'weight': weight}
        
        # Initialize synapse in applicable rules
        for rule_name, rule in self.rules.items():
            if rule_name == 'consolidated':
                rule.initialize_synapse(pre_id, post_id, weight)
    
    def on_pre_spike(self, pre_id: int, post_id: int, t: float):
        """
        Process a presynaptic spike.
        
        Args:
            pre_id: Presynaptic neuron ID
            post_id: Postsynaptic neuron ID
            t: Spike time
            
        Returns:
            Dictionary with processing information
        """
        results = {}
        
        # Get current weight
        synapse_key = (pre_id, post_id)
        if synapse_key not in self.connections and self.core_interface:
            # Try to get from core
            connections = self.core_interface.get_connections()
            if synapse_key in connections:
                weight = connections[synapse_key]['weight']
                self.connections[synapse_key] = {'weight': weight}
            else:
                # Default weight
                self.connections[synapse_key] = {'weight': 1.0}
                
        if synapse_key in self.connections:
            weight = self.connections[synapse_key]['weight']
            
            # Process in each applicable rule
            for rule_name, rule in self.rules.items():
                if rule_name == 'stdp':
                    dw = rule.on_pre_spike(pre_id, post_id, t, weight)
                    results[f'{rule_name}_dw'] = dw
                    
            # Register neurons if needed
            if pre_id not in self.neuron_ids:
                self.register_neuron(pre_id)
                
            if post_id not in self.neuron_ids:
                self.register_neuron(post_id)
                
        return results
    
    def on_post_spike(self, pre_id: int, post_id: int, t: float):
        """
        Process a postsynaptic spike.
        
        Args:
            pre_id: Presynaptic neuron ID
            post_id: Postsynaptic neuron ID
            t: Spike time
            
        Returns:
            Dictionary with processing information
        """
        results = {}
        
        # Get current weight
        synapse_key = (pre_id, post_id)
        if synapse_key not in self.connections and self.core_interface:
            # Try to get from core
            connections = self.core_interface.get_connections()
            if synapse_key in connections:
                weight = connections[synapse_key]['weight']
                self.connections[synapse_key] = {'weight': weight}
            else:
                # Default weight
                self.connections[synapse_key] = {'weight': 1.0}
                
        if synapse_key in self.connections:
            weight = self.connections[synapse_key]['weight']
            
            # Process in each applicable rule
            for rule_name, rule in self.rules.items():
                if rule_name == 'stdp':
                    dw = rule.on_post_spike(pre_id, post_id, t, weight)
                    results[f'{rule_name}_dw'] = dw
                    
            # Register neurons if needed
            if pre_id not in self.neuron_ids:
                self.register_neuron(pre_id)
                
            if post_id not in self.neuron_ids:
                self.register_neuron(post_id)
                
        return results
    
    def on_spike(self, neuron_id: int, t: float):
        """
        Process a neuron spike.
        
        Args:
            neuron_id: Neuron ID
            t: Spike time
            
        Returns:
            Dictionary with processing information
        """
        # Register neuron if needed
        if neuron_id not in self.neuron_ids:
            self.register_neuron(neuron_id)
            
        # Process in each applicable rule
        for rule_name, rule in self.rules.items():
            if rule_name == 'bcm':
                rule.on_spike(neuron_id, t)
            elif rule_name == 'oja':
                rule.on_spike(neuron_id, t)
            elif rule_name == 'homeostatic':
                rule.on_spike(neuron_id, t)
            elif rule_name == 'structural':
                rule.on_spike(neuron_id, t)
                
        return {'neuron_id': neuron_id, 'time': t}
    
    def apply_weight_changes(self):
        """
        Apply accumulated weight changes to connections.
        
        Returns:
            Dictionary with information about applied changes
        """
        results = {}
        
        # Apply changes from each applicable rule
        for rule_name, rule in self.rules.items():
            if rule_name in ['stdp', 'bcm', 'oja']:
                # Apply weight changes to local connections
                changes = rule.apply_weight_changes(self.connections)
                results[rule_name] = changes
                
                # Update core if connected
                if self.core_interface:
                    # Update weights in core
                    for synapse_key, dw in changes.get('changes_per_synapse', {}).items():
                        if synapse_key in self.connections:
                            weight = self.connections[synapse_key]['weight']
                            self.core_interface.modify_weight(synapse_key[0], synapse_key[1], weight)
                            
                # Update statistics
                self.stats['weight_changes'] += changes.get('total_changes', 0)
                
        return results
    
    def apply_structural_changes(self):
        """
        Apply structural changes (create/remove connections).
        
        Returns:
            Dictionary with information about applied changes
        """
        results = {}
        
        # Only proceed if structural plasticity is enabled
        if 'structural' in self.rules and self.connection_creator:
            # Apply structural changes
            changes = self.rules['structural'].apply_structural_changes(
                self.connections,
                self.connection_creator
            )
            
            results['structural'] = changes
            
            # Update statistics
            self.stats['new_connections'] += changes.get('total_created', 0)
            self.stats['removed_connections'] += changes.get('total_removed', 0)
            
            # Update core if connected
            if self.core_interface:
                # Remove connections in core
                for pre_id, post_id in changes.get('removed', []):
                    self.core_interface.remove_connection(pre_id, post_id)
                    
        return results
    
    def apply_homeostatic_adjustments(self):
        """
        Apply homeostatic adjustments to neurons.
        
        Returns:
            Dictionary with information about applied adjustments
        """
        results = {}
        
        # Only proceed if homeostatic plasticity is enabled and core is connected
        if 'homeostatic' in self.rules and self.core_interface:
            # Get neurons from core
            neurons = self.core_interface.get_neurons()
            
            # Apply homeostatic adjustments
            adjustments = self.rules['homeostatic'].apply_threshold_adjustments(neurons)
            results['homeostatic'] = adjustments
            
        return results
    
    def step(self, dt: float):
        """
        Update plasticity state for a time step.
        
        Args:
            dt: Time step
            
        Returns:
            Dictionary with update information
        """
        # Update current time
        t_new = self.current_time + dt
        
        # Determine if this is a full update step
        do_full_update = (t_new - self.last_full_update) >= self.update_interval
        
        if do_full_update:
            self.last_full_update = t_new
        
        # Update rules
        rule_results = {}
        
        # If using selective update, determine which rules to update
        rules_to_update = {}
        
        if self.selective_update:
            for rule_name, rule in self.rules.items():
                priority = self.rule_priorities.get(rule_name, 3)
                
                # Higher priority = update less frequently
                update_interval = priority * self.update_interval
                
                if do_full_update or (t_new % update_interval) < dt:
                    rules_to_update[rule_name] = rule
        else:
            rules_to_update = self.rules
        
        # Update selected rules
        for rule_name, rule in rules_to_update.items():
            # Update most rules
            if rule_name in ['stdp', 'bcm', 'oja', 'homeostatic']:
                result = rule.step(t_new, dt)
                rule_results[rule_name] = result
            # Update structural plasticity with connections and spike history
            elif rule_name == 'structural' and self.core_interface:
                # Get neurons and spike history from core
                neurons = self.core_interface.get_neurons()
                spike_history = self.core_interface.get_spike_history()
                
                # Update local connections from core
                if do_full_update:
                    connections = self.core_interface.get_connections()
                    for synapse_key, conn in connections.items():
                        if synapse_key not in self.connections:
                            self.connections[synapse_key] = conn.copy()
                            
                result = rule.step(t_new, dt, self.connections, neurons, spike_history)
                rule_results[rule_name] = result
            # Update consolidation with connections
            elif rule_name == 'consolidated':
                result = rule.step(t_new, dt, self.connections)
                rule_results[rule_name] = result
                
        # Apply weight changes on every step
        weight_results = self.apply_weight_changes()
        rule_results['weight_changes'] = weight_results
        
        # Apply structural changes less frequently
        if do_full_update:
            structural_results = self.apply_structural_changes()
            rule_results['structural_changes'] = structural_results
            
            # Apply homeostatic adjustments
            homeostatic_results = self.apply_homeostatic_adjustments()
            rule_results['homeostatic_adjustments'] = homeostatic_results
            
        # Update current time
        self.current_time = t_new
        self.stats['total_updates'] += 1
        
        return {
            'time': t_new,
            'results': rule_results,
            'full_update': do_full_update
        }
    
    def apply_to_network(self, network, rule_type, **kwargs):
        """
        Apply a plasticity rule to a network.
        
        Args:
            network: Network instance
            rule_type: Type of plasticity rule
            **kwargs: Additional parameters
            
        Returns:
            Plasticity rule instance
        """
        # Add rule if not already active
        rule_name = rule_type.name.lower()
        if rule_name not in self.rules:
            rule = self.add_rule(rule_type, **kwargs)
        else:
            rule = self.rules[rule_name]
            
        # Register all neurons in the network
        if hasattr(network, 'get_neurons'):
            neurons = network.get_neurons()
            for neuron in neurons:
                self.register_neuron(neuron.id)
                
        # Register all synapses in the network
        if hasattr(network, 'get_connections'):
            connections = network.get_connections()
            for conn in connections:
                pre_id = conn['pre_id']
                post_id = conn['post_id']
                weight = conn['weight']
                self.register_synapse(pre_id, post_id, weight)
                
        return rule
    
    def reset(self):
        """Reset neuroplasticity engine state."""
        # Reset all rules
        for rule in self.rules.values():
            rule.reset()
            
        # Reset statistics
        self.stats = {
            'weight_changes': 0,
            'new_connections': 0,
            'removed_connections': 0,
            'total_updates': 0
        }
        
        # Reset time
        self.current_time = 0.0
        self.last_full_update = 0.0
    
    def set_update_interval(self, interval: float):
        """
        Set the update interval for the engine.
        
        Args:
            interval: Update interval in ms
        """
        self.update_interval = max(0.1, interval)
        logger.info(f"Update interval set to {self.update_interval} ms")
    
    def set_selective_update(self, enabled: bool):
        """
        Enable or disable selective rule updates.
        
        Args:
            enabled: Whether to enable selective updates
        """
        self.selective_update = enabled
        logger.info(f"Selective rule updates {'enabled' if enabled else 'disabled'}")
    
    def set_rule_priority(self, rule_name: str, priority: int):
        """
        Set the priority for a rule.
        
        Lower priority = updated more frequently.
        
        Args:
            rule_name: Name of the rule
            priority: Priority level (1-5, 1 is highest)
        """
        priority = max(1, min(5, priority))
        self.rule_priorities[rule_name] = priority
        logger.info(f"Priority for rule '{rule_name}' set to {priority}")
    
    # Visualization methods - only available if matplotlib is installed
    
    def visualize_weight_distribution(self, ax=None):
        """
        Visualize the distribution of synaptic weights.
        
        Args:
            ax: Optional matplotlib axis
            
        Returns:
            matplotlib.axes.Axes
        """
        if not HAS_MATPLOTLIB:
            logger.warning("Matplotlib is required for visualization")
            return None
            
        if ax is None:
            fig, ax = plt.subplots(figsize=(10, 6))
        
        # Get weights
        weights = [conn['weight'] for conn in self.connections.values()]
        
        if not weights:
            ax.text(0.5, 0.5, "No weights available", horizontalalignment='center',
                   verticalalignment='center', transform=ax.transAxes)
            return ax
        
        # Plot histogram
        ax.hist(weights, bins=50, alpha=0.7, color='blue')
        ax.set_xlabel('Synaptic Weight')
        ax.set_ylabel('Frequency')
        ax.set_title('Distribution of Synaptic Weights')
        
        # Add vertical line at zero
        ax.axvline(x=0, color='red', linestyle='--', alpha=0.7)
        
        # Add statistics
        ax.text(0.05, 0.95, f"Mean: {np.mean(weights):.4f}\nStd: {np.std(weights):.4f}\n"
                          f"Min: {np.min(weights):.4f}\nMax: {np.max(weights):.4f}",
                transform=ax.transAxes, bbox=dict(facecolor='white', alpha=0.8))
        
        return ax
    
    def visualize_plasticity_effects(self, duration: float = 1000.0, dt: float = 1.0, input_func=None):
        """
        Visualize the effects of plasticity over time by simulating the network.
        
        Args:
            duration: Simulation duration (ms)
            dt: Time step (ms)
            input_func: Function that takes time as input and returns external input array
            
        Returns:
            Tuple[Figure, Dict]: Figure and data dictionary
        """
        if not HAS_MATPLOTLIB:
            logger.warning("Matplotlib is required for visualization")
            return None, {}
            
        if self.core is None:
            raise ValueError("No core connected, cannot simulate")
            
        # Record initial state
        initial_weights = {key: conn['weight'] for key, conn in self.connections.items()}
        initial_connection_count = len(initial_weights)
        
        # Prepare recording arrays
        num_steps = int(duration / dt)
        time_points = np.arange(0, duration, dt)
        weight_changes = np.zeros(num_steps)
        connection_counts = np.zeros(num_steps)
        threshold_values = {}
        
        # Select random neurons to track thresholds (up to 5)
        neurons = self.core_interface.get_neurons()
        tracked_neurons = np.random.choice(list(neurons.keys()), 
                                          min(5, len(neurons)), 
                                          replace=False)
        
        for neuron_id in tracked_neurons:
            if hasattr(neurons[neuron_id], 'params') and hasattr(neurons[neuron_id].params, 'v_threshold'):
                threshold_values[neuron_id] = np.zeros(num_steps)
                threshold_values[neuron_id][0] = neurons[neuron_id].params.v_threshold
        
        # Initial connection count
        connection_counts[0] = initial_connection_count
        
        # Run simulation with plasticity
        for step in range(1, num_steps):
            # Generate input if provided
            if input_func is not None:
                t = step * dt
                external_input = input_func(t)
                
                # Apply input to core
                if hasattr(self.core, 'step'):
                    self.core.step(external_input, dt)
            
            # Apply plasticity
            self.step(dt)
            
            # Record metrics
            current_weights = {key: conn['weight'] for key, conn in self.connections.items()}
            
            # Calculate mean absolute weight change
            weight_diff_sum = 0.0
            count = 0
            for key, weight in current_weights.items():
                if key in initial_weights:
                    weight_diff_sum += abs(weight - initial_weights[key])
                    count += 1
            
            if count > 0:
                weight_changes[step] = weight_diff_sum / count
            
            connection_counts[step] = len(self.connections)
            
            # Record thresholds
            for neuron_id in threshold_values:
                if neuron_id in neurons:
                    if hasattr(neurons[neuron_id], 'params') and hasattr(neurons[neuron_id].params, 'v_threshold'):
                        threshold_values[neuron_id][step] = neurons[neuron_id].params.v_threshold
        
        # Create figure
        fig, axs = plt.subplots(3, 1, figsize=(12, 15), sharex=True)
        
        # Plot weight changes
        axs[0].plot(time_points, weight_changes)
        axs[0].set_ylabel('Mean Absolute Weight Change')
        axs[0].set_title('Effects of Plasticity Over Time')
        axs[0].grid(True)
        
        # Plot connection counts
        axs[1].plot(time_points, connection_counts)
        axs[1].set_ylabel('Number of Connections')
        axs[1].grid(True)
        
        # Plot threshold values
        for neuron_id, thresholds in threshold_values.items():
            axs[2].plot(time_points, thresholds, label=f'Neuron {neuron_id}')
        
        axs[2].set_xlabel('Time (ms)')
        axs[2].set_ylabel('Threshold Values')
        if threshold_values:
            axs[2].legend()
        axs[2].grid(True)
        
        plt.tight_layout()
        
        # Collect data for return
        data = {
            'time_points': time_points,
            'weight_changes': weight_changes,
            'connection_counts': connection_counts,
            'threshold_values': threshold_values
        }
        
        return fig, data
    
    def visualize_connectivity(self, ax=None, layout='circular'):
        """
        Visualize network connectivity.
        
        Args:
            ax: Optional matplotlib axis
            layout: Layout type ('circular', 'spring', or 'positions')
            
        Returns:
            matplotlib.axes.Axes
        """
        if not HAS_MATPLOTLIB or not HAS_NETWORKX:
            logger.warning("Matplotlib and NetworkX are required for connectivity visualization")
            return None
            
        if ax is None:
            fig, ax = plt.subplots(figsize=(10, 8))
        
        # Create graph
        G = nx.DiGraph()
        
        # Add nodes
        for neuron_id in self.neuron_ids:
            G.add_node(neuron_id)
        
        # Add edges with weights
        for (pre_id, post_id), conn in self.connections.items():
            weight = conn['weight']
            G.add_edge(pre_id, post_id, weight=weight)
        
        # Determine node positions
        if layout == 'circular':
            pos = nx.circular_layout(G)
        elif layout == 'spring':
            pos = nx.spring_layout(G)
        elif layout == 'positions' and self.core_interface and self.core_interface.has_neuron_positions:
            # Use neuron positions from core
            positions = getattr(self.core, 'positions')
            pos = {i: (positions[i][0], positions[i][1]) for i in G.nodes()}
        else:
            pos = nx.spring_layout(G)
        
        # Get edge weights for width and color
        edge_weights = [G[u][v]['weight'] for u, v in G.edges()]
        
        # Normalize edge weights to [0, 1] for color mapping
        if edge_weights:
            min_weight = min(edge_weights)
            max_weight = max(edge_weights)
            norm_weights = [(w - min_weight) / (max_weight - min_weight) if max_weight > min_weight else 0.5 for w in edge_weights]
        else:
            norm_weights = []
        
        # Draw nodes
        nx.draw_networkx_nodes(G, pos, node_size=100, alpha=0.8, node_color='lightblue', ax=ax)
        
        # Draw edges with color based on weight (red for negative, blue for positive)
        if edge_weights:
            # Create color map: negative weights -> red, positive weights -> blue
            edge_colors = ['red' if w < 0 else 'blue' for w in edge_weights]
            
            # Edge width based on absolute weight
            edge_widths = [1.0 + 2.0 * abs(w) for w in norm_weights]
            
            # Draw edges
            nx.draw_networkx_edges(G, pos, width=edge_widths, edge_color=edge_colors, alpha=0.6, ax=ax)
        
        # Draw node labels
        if len(G.nodes()) <= 20:  # Only show labels if there aren't too many
            nx.draw_networkx_labels(G, pos, font_size=8, ax=ax)
        
        # Set title and remove axis
        ax.set_title(f'Network Connectivity ({len(G.nodes())} neurons, {len(G.edges())} connections)')
        ax.axis('off')
        
        return ax
    
    def visualize_activity_correlation(self, ax=None):
        """
        Visualize activity correlation matrix.
        
        Args:
            ax: Optional matplotlib axis
            
        Returns:
            matplotlib.axes.Axes
        """
        if not HAS_MATPLOTLIB:
            logger.warning("Matplotlib is required for visualization")
            return None
            
        if 'structural' not in self.rules or self.rules['structural'].activity_correlation is None:
            # Calculate correlation if not available
            if self.core_interface:
                neurons = self.core_interface.get_neurons()
                spike_history = self.core_interface.get_spike_history()
                if spike_history is not None and 'structural' in self.rules:
                    correlation = self.rules['structural'].compute_activity_correlation(neurons, spike_history)
                else:
                    if ax is None:
                        fig, ax = plt.subplots(figsize=(8, 8))
                    ax.text(0.5, 0.5, "No activity correlation data available", 
                           horizontalalignment='center', verticalalignment='center',
                           transform=ax.transAxes)
                    return ax
            else:
                if ax is None:
                    fig, ax = plt.subplots(figsize=(8, 8))
                ax.text(0.5, 0.5, "No activity correlation data available", 
                       horizontalalignment='center', verticalalignment='center',
                       transform=ax.transAxes)
                return ax
        else:
            correlation = self.rules['structural'].activity_correlation
        
        if ax is None:
            fig, ax = plt.subplots(figsize=(8, 8))
        
        # Plot correlation matrix as heatmap
        cax = ax.imshow(correlation, cmap='coolwarm', vmin=-1, vmax=1, interpolation='nearest')
        
        # Add colorbar
        plt.colorbar(cax, ax=ax, label='Correlation')
        
        # Set labels
        ax.set_title('Neuron Activity Correlation Matrix')
        ax.set_xlabel('Neuron ID')
        ax.set_ylabel('Neuron ID')
        
        # Set ticks if not too many neurons
        if correlation.shape[0] <= 20:
            ax.set_xticks(np.arange(correlation.shape[1]))
            ax.set_yticks(np.arange(correlation.shape[0]))
        
        return ax


def create_plasticity_rule(rule_type: str, **kwargs) -> Any:
    """
    Create a plasticity rule instance.
    
    Args:
        rule_type: Type of plasticity rule
        **kwargs: Additional parameters
        
    Returns:
        Plasticity rule instance
    """
    # Convert string to enum if needed
    if isinstance(rule_type, str):
        try:
            rule_type = PlasticityRule[rule_type.upper()]
        except KeyError:
            raise ValueError(f"Unknown plasticity rule: {rule_type}")
            
    # Create parameters with overrides
    params = PlasticityParameters()
    for key, value in kwargs.items():
        if hasattr(params, key):
            setattr(params, key, value)
            
    # Create rule instance
    if rule_type == PlasticityRule.STDP:
        return STDPRule(params)
    elif rule_type == PlasticityRule.BCM:
        return BCMRule(params)
    elif rule_type == PlasticityRule.OJA:
        return OjaRule(params)
    elif rule_type == PlasticityRule.HOMEOSTATIC:
        return HomeostaticRule(params)
    elif rule_type == PlasticityRule.STRUCTURAL:
        return StructuralPlasticityRule(params)
    elif rule_type == PlasticityRule.CONSOLIDATED:
        return ConsolidationRule(params)
    else:
        raise ValueError(f"Unsupported plasticity rule: {rule_type}")


def plasticity_decorator(rule_type: str, **rule_params):
    """
    Decorator to apply plasticity to a network creation function.
    
    Args:
        rule_type: Type of plasticity rule
        **rule_params: Rule parameters
        
    Returns:
        Decorator function
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(engine, *args, **kwargs):
            # Create network
            network = func(*args, **kwargs)
            
            # Apply plasticity rule
            engine.apply_to_network(network, rule_type, **rule_params)
            
            return network
        return wrapper
    return decorator


# Simplified façade for common use cases
class SimplePlasticityEngine:
    """
    Simplified façade for the Neuroplasticity Engine.
    
    This provides a more streamlined API for common use cases.
    """
    
    def __init__(self, core=None):
        """
        Initialize the simplified plasticity engine.
        
        Args:
            core: Optional neuromorphic core
        """
        # Create the full engine
        self.engine = NeuroplasticityEngine()
        
        # Connect to core if provided
        if core is not None:
            self.connect_to_core(core)
    
    def connect_to_core(self, core):
        """
        Connect to a neuromorphic core.
        
        Args:
            core: Neuromorphic core
        """
        self.engine.connect_to_core(core)
    
    def enable_stdp(self, lr_pos=0.01, lr_neg=0.01, tau_pos=20.0, tau_neg=20.0):
        """
        Enable STDP plasticity.
        
        Args:
            lr_pos: Learning rate for potentiation
            lr_neg: Learning rate for depression
            tau_pos: Time constant for potentiation
            tau_neg: Time constant for depression
        """
        params = PlasticityParameters(
            a_plus=lr_pos,
            a_minus=lr_neg,
            tau_plus=tau_pos,
            tau_minus=tau_neg
        )
        
        if 'stdp' in self.engine.rules:
            self.engine.remove_rule('stdp')
            
        self.engine.add_rule(PlasticityRule.STDP, params)
    
    def enable_homeostatic(self, target_rate=5.0, tau=10000.0, scale=0.1):
        """
        Enable homeostatic plasticity.
        
        Args:
            target_rate: Target firing rate (Hz)
            tau: Time constant (ms)
            scale: Scale factor
        """
        params = PlasticityParameters(
            target_rate=target_rate,
            homeostatic_tau=tau,
            homeostatic_scale=scale
        )
        
        if 'homeostatic' in self.engine.rules:
            self.engine.remove_rule('homeostatic')
            
        self.engine.add_rule(PlasticityRule.HOMEOSTATIC, params)
    
    def enable_structural(self, creation_p=0.001, elimination_p=0.0005, window=100):
        """
        Enable structural plasticity.
        
        Args:
            creation_p: Connection creation probability
            elimination_p: Connection elimination probability
            window: Correlation window size
        """
        params = PlasticityParameters(
            creation_p=creation_p,
            elimination_p=elimination_p,
            correlation_window=window
        )
        
        if 'structural' in self.engine.rules:
            self.engine.remove_rule('structural')
            
        self.engine.add_rule(PlasticityRule.STRUCTURAL, params)
    
    def step(self, dt=1.0):
        """
        Perform a simulation step.
        
        Args:
            dt: Time step (ms)
            
        Returns:
            Step result
        """
        return self.engine.step(dt)
    
    def reset(self):
        """Reset the engine."""
        self.engine.reset()
    
    def get_stats(self):
        """
        Get statistics.
        
        Returns:
            Statistics dictionary
        """
        return self.engine.stats
    
    def visualize(self, what='weights'):
        """
        Visualize plasticity effects.
        
        Args:
            what: What to visualize ('weights', 'connectivity', 'correlation', 'effects')
            
        Returns:
            Visualization figure
        """
        if not HAS_MATPLOTLIB:
            logger.warning("Matplotlib is required for visualization")
            return None
            
        fig = plt.figure(figsize=(10, 8))
        
        if what == 'weights':
            ax = fig.add_subplot(111)
            self.engine.visualize_weight_distribution(ax)
        elif what == 'connectivity':
            ax = fig.add_subplot(111)
            self.engine.visualize_connectivity(ax)
        elif what == 'correlation':
            ax = fig.add_subplot(111)
            self.engine.visualize_activity_correlation(ax)
        elif what == 'effects':
            # This creates its own figure
            fig, _ = self.engine.visualize_plasticity_effects()
            
        return fig


def initialize_plasticity(core=None, config=None):
    """
    Initialize plasticity engine.
    
    Args:
        core: Optional neuromorphic core
        config: Configuration dictionary
        
    Returns:
        Initialized plasticity engine
    """
    logger.info("Initializing Neuroplasticity Engine")
    
    # Default configuration
    default_config = {
        'stdp_enabled': True,
        'homeostatic_enabled': True,
        'structural_enabled': False,
        'consolidation_enabled': False,
        'selective_update': True,
        'update_interval': 1.0
    }
    
    # Update with provided config
    if config is not None and 'neuroplasticity_engine' in config:
        custom_config = config['neuroplasticity_engine']
        default_config.update(custom_config)
    
    # Initialize engine with config
    try:
        engine = NeuroplasticityEngine(default_config)
        
        # Connect to core if provided
        if core is not None:
            engine.connect_to_core(core)
            
        # Set additional config options
        engine.set_selective_update(default_config['selective_update'])
        engine.set_update_interval(default_config['update_interval'])
        
        logger.info("Neuroplasticity Engine initialized successfully")
        return engine
    except Exception as e:
        logger.error(f"Failed to initialize Neuroplasticity Engine: {str(e)}")
        raise