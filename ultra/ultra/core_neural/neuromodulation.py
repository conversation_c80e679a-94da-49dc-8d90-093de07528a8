#!/usr/bin/env python3
"""
ULTRA: Ultimate Learning & Thought Reasoning Architecture
Neuromodulation System - Complete Production Implementation

This module implements a comprehensive neuromodulation system that accurately models
the complex dynamics of biological neuromodulators and their effects on neural
processing. The implementation includes detailed receptor modeling, concentration
dynamics, interaction effects, and integration with all core neural components.

Mathematical Foundation:
- Modulator concentration dynamics: τₘ dc/dt = -c + Σ Iₖ + f(receptors, feedback)
- Receptor binding kinetics: dR/dt = kon·c·(Rmax-R) - koff·R
- Synaptic modulation: w_eff = w_base × (1 + Σ αᵢ·cᵢ·gᵢ(R))
- Neural excitability: θ_eff = θ_base + Σ βᵢ·cᵢ·hᵢ(R)
- Plasticity modulation: η_eff = η_base × Π (1 + γᵢ·cᵢ)

The system implements four primary neuromodulators:
1. Dopamine (DA) - Reward prediction, motivation, motor control
2. Serotonin (5-HT) - Mood regulation, impulse control, sleep-wake cycles
3. Norepinephrine (NE) - Arousal, attention, stress response
4. Acetylcholine (ACh) - Learning, memory, attention, sensory processing

Each modulator includes:
- Realistic pharmacokinetics and pharmacodynamics
- Multiple receptor subtypes with distinct binding properties
- Complex interaction networks and cross-modulation effects
- Spatial diffusion and regional concentration gradients
- Temporal dynamics across multiple timescales
- Integration with neural plasticity, oscillations, and network dynamics
"""

import logging
import numpy as np
import scipy.sparse
import scipy.spatial
import scipy.integrate
import scipy.optimize
import scipy.signal
import scipy.stats
from scipy.spatial.distance import cdist
from typing import Dict, List, Tuple, Set, Any, Optional, Union, Callable, NamedTuple
import time
import threading
import queue
import multiprocessing as mp
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
from dataclasses import dataclass, field
from enum import Enum, auto
import warnings
import uuid
from abc import ABC, abstractmethod
import json
import pickle
import h5py
from collections import defaultdict, deque, OrderedDict
import functools
import itertools
import weakref
import gc
import math
from pathlib import Path

# Advanced numerical libraries
try:
    import cupy as cp
    CUPY_AVAILABLE = True
except ImportError:
    CUPY_AVAILABLE = False
    cp = np

try:
    import jax
    import jax.numpy as jnp
    from jax import jit, vmap, grad, hessian
    JAX_AVAILABLE = True
except ImportError:
    JAX_AVAILABLE = False
    jax = None
    jnp = np

try:
    import numba
    from numba import njit, prange, cuda
    NUMBA_AVAILABLE = True
except ImportError:
    NUMBA_AVAILABLE = False
    def njit(func):
        return func
    prange = range

try:
    import torch
    import torch.nn as nn
    import torch.nn.functional as F
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    torch = None

# Specialized neuroscience libraries
try:
    from neuron import h
    NEURON_AVAILABLE = True
except ImportError:
    NEURON_AVAILABLE = False
    h = None

try:
    import brian2
    BRIAN2_AVAILABLE = True
except ImportError:
    BRIAN2_AVAILABLE = False
    brian2 = None

# Configure comprehensive logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('ultra_neuromodulation.log')
    ]
)
logger = logging.getLogger(__name__)


# ===========================================================================================
# CORE ENUMERATIONS AND DATA STRUCTURES
# ===========================================================================================

class ModulatorType(Enum):
    """Comprehensive enumeration of neuromodulator types with biological accuracy."""
    # Classical monoamines
    DOPAMINE = "dopamine"
    SEROTONIN = "serotonin" 
    NOREPINEPHRINE = "norepinephrine"
    EPINEPHRINE = "epinephrine"
    HISTAMINE = "histamine"
    
    # Cholinergic
    ACETYLCHOLINE = "acetylcholine"
    
    # Neuropeptides
    OXYTOCIN = "oxytocin"
    VASOPRESSIN = "vasopressin"
    ENDORPHINS = "endorphins"
    SUBSTANCE_P = "substance_p"
    NEUROPEPTIDE_Y = "neuropeptide_y"
    
    # GABA modulators
    GABA = "gaba"
    
    # Glutamate modulators
    GLUTAMATE = "glutamate"
    
    # Endocannabinoids
    ANANDAMIDE = "anandamide"
    
    # Custom modulators for AI applications
    ATTENTION_MODULATOR = "attention_modulator"
    LEARNING_MODULATOR = "learning_modulator"
    MEMORY_MODULATOR = "memory_modulator"


class ReceptorSubtype(Enum):
    """Receptor subtypes for each neuromodulator system."""
    # Dopamine receptors
    D1 = "D1"
    D2 = "D2"
    D3 = "D3"
    D4 = "D4"
    D5 = "D5"
    
    # Serotonin receptors
    HTR1A = "5-HT1A"
    HTR1B = "5-HT1B"
    HTR2A = "5-HT2A"
    HTR2B = "5-HT2B"
    HTR2C = "5-HT2C"
    HTR3 = "5-HT3"
    HTR4 = "5-HT4"
    HTR5A = "5-HT5A"
    HTR6 = "5-HT6"
    HTR7 = "5-HT7"
    
    # Adrenergic receptors
    ALPHA1A = "α1A"
    ALPHA1B = "α1B"
    ALPHA1D = "α1D"
    ALPHA2A = "α2A"
    ALPHA2B = "α2B"
    ALPHA2C = "α2C"
    BETA1 = "β1"
    BETA2 = "β2"
    BETA3 = "β3"
    
    # Cholinergic receptors
    NICOTINIC_ALPHA4BETA2 = "nAChR-α4β2"
    NICOTINIC_ALPHA7 = "nAChR-α7"
    MUSCARINIC_M1 = "M1"
    MUSCARINIC_M2 = "M2"
    MUSCARINIC_M3 = "M3"
    MUSCARINIC_M4 = "M4"
    MUSCARINIC_M5 = "M5"


class ModulationTarget(Enum):
    """Targets of neuromodulation effects."""
    MEMBRANE_POTENTIAL = "membrane_potential"
    FIRING_THRESHOLD = "firing_threshold"
    REFRACTORY_PERIOD = "refractory_period"
    MEMBRANE_RESISTANCE = "membrane_resistance"
    TIME_CONSTANT = "time_constant"
    SYNAPTIC_WEIGHT = "synaptic_weight"
    SYNAPTIC_CONDUCTANCE = "synaptic_conductance"
    PLASTICITY_RATE = "plasticity_rate"
    INHIBITION_STRENGTH = "inhibition_strength"
    NOISE_LEVEL = "noise_level"
    ADAPTATION_STRENGTH = "adaptation_strength"


class TemporalScale(Enum):
    """Temporal scales of neuromodulation effects."""
    MILLISECONDS = "milliseconds"     # 1-100 ms
    SECONDS = "seconds"               # 1-60 s  
    MINUTES = "minutes"               # 1-60 min
    HOURS = "hours"                   # 1-24 h
    DAYS = "days"                     # 1-7 days
    WEEKS = "weeks"                   # 1-4 weeks


@dataclass
class ReceptorParameters:
    """Parameters for neurotransmitter receptor modeling with FIXED values."""
    # Binding kinetics - FIXED: Better kinetic consistency
    kon: float = 1e7          # Association rate constant (M⁻¹s⁻¹) - INCREASED
    koff: float = 1e-3        # Dissociation rate constant (s⁻¹) - DECREASED for stability
    kd: float = 1e-10         # Dissociation constant (M) - DECREASED for higher affinity
    
    # Receptor density and distribution
    density: float = 1000.0   # Receptors per μm²
    max_binding: float = 1.0  # Maximum binding capacity
    
    # Efficacy and coupling
    efficacy: float = 1.0     # Intrinsic activity (0-1)
    coupling_strength: float = 1.0  # G-protein coupling efficiency
    
    # Desensitization and internalization - FIXED: Reduced by 10x for realistic equilibrium
    desensitization_rate: float = 0.001    # FIXED: Was 0.01, now 0.001
    internalization_rate: float = 0.0001   # FIXED: Was 0.001, now 0.0001
    recovery_rate: float = 0.01            # FIXED: Increased for better recovery
    
    # Spatial distribution
    membrane_fraction: float = 0.8       # Fraction on membrane vs internal
    synaptic_fraction: float = 0.6       # Fraction at synapses vs extrasynaptic
    
    # Pharmacological properties
    hill_coefficient: float = 1.0        # Hill coefficient for dose-response
    ec50: float = 1e-10                  # FIXED: Matches kd value
    
    def __post_init__(self):
        """Validate parameters after initialization with FIXED validation."""
        if self.kon <= 0:
            raise ValueError(f"kon must be positive, got {self.kon}")
        if self.koff <= 0:
            raise ValueError(f"koff must be positive, got {self.koff}")
        if self.kd <= 0:
            raise ValueError(f"kd must be positive, got {self.kd}")
            
        # FIXED: Verify kinetic consistency (Kd = koff/kon)
        calculated_kd = self.koff / self.kon
        if abs(calculated_kd - self.kd) > self.kd * 0.1:  # 10% tolerance
            logger.warning(f"Kinetic inconsistency: Kd={self.kd}, koff/kon={calculated_kd}")
            self.kd = calculated_kd  # Auto-correct
            
        if self.density < 0:
            raise ValueError(f"density must be non-negative, got {self.density}")
        if not 0 <= self.efficacy <= 1:
            raise ValueError(f"efficacy must be between 0 and 1, got {self.efficacy}")
        if self.coupling_strength <= 0:
            raise ValueError(f"coupling_strength must be positive, got {self.coupling_strength}")
        if self.desensitization_rate < 0:
            raise ValueError(f"desensitization_rate must be non-negative, got {self.desensitization_rate}")
        if self.internalization_rate < 0:
            raise ValueError(f"internalization_rate must be non-negative, got {self.internalization_rate}")
        if self.recovery_rate < 0:
            raise ValueError(f"recovery_rate must be non-negative, got {self.recovery_rate}")
        if not 0 <= self.membrane_fraction <= 1:
            raise ValueError(f"membrane_fraction must be between 0 and 1, got {self.membrane_fraction}")
        if not 0 <= self.synaptic_fraction <= 1:
            raise ValueError(f"synaptic_fraction must be between 0 and 1, got {self.synaptic_fraction}")
        if self.hill_coefficient <= 0:
            raise ValueError(f"hill_coefficient must be positive, got {self.hill_coefficient}")
        if self.ec50 <= 0:
            raise ValueError(f"ec50 must be positive, got {self.ec50}")


@dataclass  
class ModulatorParameters:
    """Comprehensive parameters for neuromodulator systems with FIXED values."""
    # Basic properties
    molecular_weight: float = 153.0      # g/mol (dopamine default)
    diffusion_coefficient: float = 6e-10 # m²/s in brain tissue
    
    # Synthesis and release - FIXED: More realistic values
    synthesis_rate: float = 1e-10         # FIXED: Was 1e-9, reduced for stability
    release_probability: float = 0.3     # Probability of vesicle release
    vesicle_content: float = 100.0       # FIXED: Was 1000.0, reduced by 10x
    
    # Uptake and metabolism - FIXED: Better balance
    reuptake_rate: float = 0.5           # FIXED: Was 1.0, reduced
    metabolism_rate: float = 0.05        # FIXED: Was 0.1, reduced
    diffusion_rate: float = 0.02         # Diffusion clearance rate
    
    # Concentration dynamics - FIXED: Better ranges
    baseline_concentration: float = 1e-9  # FIXED: Was 1e-8, reduced
    peak_concentration: float = 1e-7      # FIXED: Was 1e-6, reduced
    tau_fast: float = 1000.0              # Fast clearance time constant (ms)
    tau_slow: float = 10000.0            # Slow clearance time constant (ms)
    
    # Spatial properties
    diffusion_radius: float = 50.0       # Effective diffusion radius (μm)
    volume_transmission: bool = True     # Volume vs synaptic transmission
    
    # Receptor interactions
    primary_receptors: List[ReceptorSubtype] = field(default_factory=list)
    receptor_affinities: Dict[ReceptorSubtype, float] = field(default_factory=dict)
    
    # Cross-modulation effects
    modulation_targets: Dict[ModulatorType, float] = field(default_factory=dict)
    interaction_matrix: Dict[ModulatorType, float] = field(default_factory=dict)
    
    def __post_init__(self):
        """Validate parameters after initialization with FIXED validation."""
        if self.molecular_weight <= 0:
            raise ValueError(f"molecular_weight must be positive, got {self.molecular_weight}")
        if self.diffusion_coefficient <= 0:
            raise ValueError(f"diffusion_coefficient must be positive, got {self.diffusion_coefficient}")
        if self.synthesis_rate < 0:
            raise ValueError(f"synthesis_rate must be non-negative, got {self.synthesis_rate}")
        if not 0 <= self.release_probability <= 1:
            raise ValueError(f"release_probability must be between 0 and 1, got {self.release_probability}")
        if self.vesicle_content <= 0:
            raise ValueError(f"vesicle_content must be positive, got {self.vesicle_content}")
        if self.reuptake_rate < 0:
            raise ValueError(f"reuptake_rate must be non-negative, got {self.reuptake_rate}")
        if self.metabolism_rate < 0:
            raise ValueError(f"metabolism_rate must be non-negative, got {self.metabolism_rate}")
        if self.diffusion_rate < 0:
            raise ValueError(f"diffusion_rate must be non-negative, got {self.diffusion_rate}")
        if self.baseline_concentration <= 0:
            raise ValueError(f"baseline_concentration must be positive, got {self.baseline_concentration}")
        if self.peak_concentration <= 0:
            raise ValueError(f"peak_concentration must be positive, got {self.peak_concentration}")
        if self.peak_concentration <= self.baseline_concentration:
            raise ValueError(f"peak_concentration must be greater than baseline_concentration")
        if self.tau_fast <= 0:
            raise ValueError(f"tau_fast must be positive, got {self.tau_fast}")
        if self.tau_slow <= 0:
            raise ValueError(f"tau_slow must be positive, got {self.tau_slow}")
        if self.diffusion_radius <= 0:
            raise ValueError(f"diffusion_radius must be positive, got {self.diffusion_radius}")
            
        # FIXED: Auto-adjust peak concentration if too high relative to baseline
        if self.peak_concentration > self.baseline_concentration * 1000:
            logger.warning(f"Peak concentration very high, adjusting from {self.peak_concentration} to {self.baseline_concentration * 100}")
            self.peak_concentration = self.baseline_concentration * 100


@dataclass
class SpatialConfiguration:
    """Spatial configuration for neuromodulator systems."""
    # 3D coordinates and regions
    coordinates: np.ndarray = field(default_factory=lambda: np.zeros((0, 3)))
    regions: Dict[str, List[int]] = field(default_factory=dict)
    
    # Distance matrices and connectivity
    distance_matrix: Optional[np.ndarray] = None
    connectivity_matrix: Optional[scipy.sparse.csr_matrix] = None
    
    # Regional properties
    regional_densities: Dict[str, float] = field(default_factory=dict)
    regional_affinities: Dict[str, Dict[ReceptorSubtype, float]] = field(default_factory=dict)
    
    # Diffusion barriers and gradients
    diffusion_barriers: List[Tuple[int, int, float]] = field(default_factory=list)
    concentration_gradients: Dict[str, Callable[[np.ndarray], float]] = field(default_factory=dict)


# ===========================================================================================
# RECEPTOR MODELING SYSTEM
# ===========================================================================================

class ReceptorKinetics:
    """Advanced receptor binding kinetics with FIXED state transitions."""
    
    def __init__(self, receptor_type: ReceptorSubtype, parameters: ReceptorParameters):
        self.receptor_type = receptor_type
        self.params = parameters
        
        # Receptor states: Unbound (U), Bound (B), Desensitized (D), Internalized (I)
        self.states = {
            'unbound': 1.0,
            'bound': 0.0, 
            'desensitized': 0.0,
            'internalized': 0.0
        }
        
        # State transition rates - FIXED
        self.transition_rates = self._initialize_transition_rates()
        
        # Binding history for analysis
        self.binding_history = deque(maxlen=10000)
        self.occupancy_history = deque(maxlen=10000)
        
        logger.debug(f"ReceptorKinetics initialized for {receptor_type}")
        
    def _initialize_transition_rates(self) -> Dict[str, Dict[str, float]]:
        """Initialize state transition rate matrix with FIXED rates."""
        return {
            'unbound': {
                'bound': self.params.kon,  # Ligand binding
                'desensitized': 0.0,
                'internalized': 0.0
            },
            'bound': {
                'unbound': self.params.koff,  # Ligand dissociation
                'desensitized': self.params.desensitization_rate,  # FIXED: Now much lower
                'internalized': self.params.internalization_rate   # FIXED: Now much lower
            },
            'desensitized': {
                'unbound': self.params.recovery_rate * 0.5,  # FIXED: Faster recovery
                'bound': 0.0,
                'internalized': self.params.internalization_rate * 0.5  # FIXED: Reduced
            },
            'internalized': {
                'unbound': self.params.recovery_rate,  # Recovery from internalization
                'bound': 0.0,
                'desensitized': 0.0
            }
        }
        
    def update(self, dt: float, ligand_concentration: float, 
           temperature: float = 37.0, ph: float = 7.4) -> Dict[str, float]:
        """Update receptor state with FIXED numerical stability."""
        
        # FIXED: Input validation and bounds
        dt = max(0.0, min(dt, 1.0))  # Clamp timestep
        ligand_concentration = max(0.0, ligand_concentration)
        temperature = max(4.0, min(temperature, 50.0))  # Reasonable temp range
        ph = max(6.0, min(ph, 8.5))  # Reasonable pH range
        
        # Temperature and pH corrections
        temp_factor = np.exp((temperature - 37.0) * 0.03)  # Q10 ~ 2-3
        ph_factor = 1.0 / (1.0 + np.exp((ph - 7.4) * 2.0))  # pH sensitivity
        
        # Effective rates with environmental corrections
        effective_rates = {}
        for from_state, transitions in self.transition_rates.items():
            effective_rates[from_state] = {}
            for to_state, rate in transitions.items():
                if from_state == 'unbound' and to_state == 'bound':
                    # Binding rate depends on ligand concentration
                    effective_rates[from_state][to_state] = (rate * ligand_concentration * 
                                                        temp_factor * ph_factor)
                else:
                    effective_rates[from_state][to_state] = rate * temp_factor
                    
        # FIXED: Solve differential equations with better numerical stability
        current_states = np.array([self.states[state] for state in 
                                ['unbound', 'bound', 'desensitized', 'internalized']])
        
        # Build transition matrix with FIXED construction
        transition_matrix = np.zeros((4, 4))
        state_names = ['unbound', 'bound', 'desensitized', 'internalized']
        
        for i, from_state in enumerate(state_names):
            total_outflow = 0.0
            for j, to_state in enumerate(state_names):
                if from_state != to_state and to_state in effective_rates[from_state]:
                    # Forward rate
                    rate = effective_rates[from_state][to_state]
                    transition_matrix[j, i] = rate
                    total_outflow += rate
            # Diagonal element (conservation)
            transition_matrix[i, i] = -total_outflow
                    
        # FIXED: Update states with improved numerical method
        if dt > 0 and np.any(current_states > 1e-12):
            try:
                # Use adaptive integration for better stability
                if dt < 0.001:  # Very small timestep - use Euler
                    new_states = current_states + transition_matrix @ current_states * dt
                elif dt < 0.1:  # Medium timestep - use RK2
                    k1 = transition_matrix @ current_states
                    k2 = transition_matrix @ (current_states + 0.5 * dt * k1)
                    new_states = current_states + dt * k2
                else:  # Large timestep - use matrix exponential
                    state_evolution = scipy.linalg.expm(transition_matrix * dt)
                    new_states = state_evolution @ current_states
                
                # FIXED: Ensure states remain valid with better bounds checking
                new_states = np.maximum(new_states, 0.0)  # Non-negative
                new_states = np.minimum(new_states, 1.0)  # Not > 1 individually
                
                total = np.sum(new_states)
                if total > 1e-12:
                    new_states = new_states / total  # Normalize
                else:
                    # Reset to unbound if total became zero
                    new_states = np.array([1.0, 0.0, 0.0, 0.0])
                    
                # Update state dictionary
                for i, state_name in enumerate(state_names):
                    self.states[state_name] = float(new_states[i])  # FIXED: Ensure float
                    
            except Exception as e:
                logger.warning(f"Matrix update failed, using Euler method: {e}")
                # FIXED: More robust fallback
                dstates_dt = transition_matrix @ current_states
                new_states = current_states + dstates_dt * dt
                new_states = np.maximum(new_states, 0.0)
                new_states = np.minimum(new_states, 1.0)
                total = np.sum(new_states)
                if total > 1e-12:
                    new_states = new_states / total
                else:
                    new_states = np.array([1.0, 0.0, 0.0, 0.0])
                    
                for i, state_name in enumerate(state_names):
                    self.states[state_name] = float(new_states[i])
                    
        # Calculate effective occupancy and efficacy
        bound_fraction = float(self.states['bound'])
        effective_occupancy = bound_fraction * self.params.efficacy  # FIXED: Ensure float
        
        # FIXED: Store history with timestamps
        current_time = time.time()
        self.binding_history.append((current_time, ligand_concentration, bound_fraction))
        self.occupancy_history.append((current_time, effective_occupancy))
        
        # FIXED: Return comprehensive state information with validation
        return {
            'states': {k: float(v) for k, v in self.states.items()},  # Ensure all floats
            'bound_fraction': float(bound_fraction),
            'effective_occupancy': float(effective_occupancy),
            'active_fraction': float(bound_fraction * self.params.efficacy),
            'available_fraction': float(self.states['unbound']),
            'desensitized_fraction': float(self.states['desensitized']),
            'internalized_fraction': float(self.states['internalized']),
            'total_states': float(sum(self.states.values())),  # Should be ~1.0
            'numerical_stability': float(abs(sum(self.states.values()) - 1.0))  # Should be ~0.0
        }
        
    def compute_dose_response(self, concentrations: np.ndarray, 
                            steady_state: bool = True) -> np.ndarray:
        """Compute dose-response curve with FIXED steady-state calculation."""
        responses = np.zeros_like(concentrations, dtype=np.float64)  # FIXED: Explicit dtype
        
        if steady_state:
            # FIXED: Analytical steady-state solution for 4-state model
            for i, conc in enumerate(concentrations):
                if conc <= 0:
                    responses[i] = 0.0
                    continue
                    
                # Solve steady-state 4-state system
                # State variables: [U, B, D, I]
                # At steady state: dX/dt = 0
                
                # Transition rates
                k_bind = self.params.kon * conc
                k_unbind = self.params.koff
                k_desens = self.params.desensitization_rate
                k_intern_from_bound = self.params.internalization_rate
                k_intern_from_desens = self.params.internalization_rate * 0.5
                k_recover_from_desens = self.params.recovery_rate * 0.5
                k_recover_from_intern = self.params.recovery_rate
                
                # Build steady-state matrix equation: A * x = b
                # U + B + D + I = 1 (conservation)
                # At steady state, net flux into each state = 0
                
                A = np.array([
                    [1.0, 1.0, 1.0, 1.0],  # Conservation
                    [k_bind, -(k_unbind + k_desens + k_intern_from_bound), k_recover_from_desens, k_recover_from_intern],  # dU/dt = 0
                    [0.0, k_desens, -(k_recover_from_desens + k_intern_from_desens), 0.0],  # dD/dt = 0
                    [0.0, k_intern_from_bound, k_intern_from_desens, -k_recover_from_intern]   # dI/dt = 0
                ])
                
                b = np.array([1.0, 0.0, 0.0, 0.0])
                
                try:
                    # Solve for steady-state
                    steady_states = np.linalg.solve(A, b)
                    
                    # Ensure valid solution
                    steady_states = np.maximum(steady_states, 0.0)
                    steady_states = steady_states / np.sum(steady_states)
                    
                    bound_fraction = steady_states[1]  # B state
                    responses[i] = bound_fraction * self.params.efficacy
                    
                except np.linalg.LinAlgError:
                    # Fallback to simple Hill equation if matrix is singular
                    kd_eff = self.params.kd * (1 + k_desens / k_recover_from_desens + 
                                             k_intern_from_bound / k_recover_from_intern)
                    response = (conc**self.params.hill_coefficient / 
                              (conc**self.params.hill_coefficient + kd_eff**self.params.hill_coefficient))
                    responses[i] = response * self.params.efficacy
                    
        else:
            # Dynamic simulation - FIXED with proper state restoration
            original_state = self.states.copy()
            
            for i, conc in enumerate(concentrations):
                # Reset to baseline
                self.states = {'unbound': 1.0, 'bound': 0.0, 'desensitized': 0.0, 'internalized': 0.0}
                
                # FIXED: Simulate to equilibrium with adaptive timesteps
                total_time = 0.0
                max_time = 1000.0  # 1000 seconds maximum
                dt_base = 0.01
                
                while total_time < max_time:
                    # Adaptive timestep based on rate of change
                    prev_bound = self.states['bound']
                    result = self.update(dt_base, conc)
                    current_bound = result['bound_fraction']
                    
                    change_rate = abs(current_bound - prev_bound) / dt_base
                    if change_rate < 1e-6:  # Converged
                        break
                        
                    total_time += dt_base
                    
                responses[i] = self.states['bound'] * self.params.efficacy
                
            # Restore original state
            self.states = original_state
            
        return responses
    
    
    def _fit_dose_response_curve(self, doses: np.ndarray, responses: np.ndarray) -> Tuple[float, float, float]:
        """Fit Hill equation to dose-response data."""
        
        if len(doses) < 4 or np.ptp(responses) < 1e-6:
            return np.median(doses), 1.0, np.max(responses)
        
        try:
            # Hill equation: R = Rmax * D^n / (D^n + EC50^n)
            def hill_equation(dose, ec50, hill_coeff, max_resp):
                return max_resp * (dose ** hill_coeff) / (dose ** hill_coeff + ec50 ** hill_coeff)
            
            # Initial parameter guesses
            max_resp_guess = np.max(responses)
            ec50_guess = doses[np.argmin(np.abs(responses - max_resp_guess / 2))]
            hill_guess = 1.0
            
            # Fit curve
            from scipy.optimize import curve_fit
            popt, _ = curve_fit(hill_equation, doses, responses, 
                            p0=[ec50_guess, hill_guess, max_resp_guess],
                            bounds=([np.min(doses), 0.1, 0], 
                                    [np.max(doses), 10.0, max_resp_guess * 2]),
                            maxfev=2000)
            
            return popt[0], popt[1], popt[2]
            
        except Exception as e:
            logger.warning(f"Dose-response fitting failed: {e}")
            return np.median(doses), 1.0, np.max(responses)



        
    def get_kinetic_parameters(self) -> Dict[str, float]:
        """Get derived kinetic parameters."""
        return {
            'kd': self.params.kd,
            'kon': self.params.kon,
            'koff': self.params.koff,
            'tau_on': 1.0 / (self.params.kon * 1e-6),  # Time constant for 1μM ligand
            'tau_off': 1.0 / self.params.koff,
            'hill_coefficient': self.params.hill_coefficient,
            'efficacy': self.params.efficacy,
            'cooperativity': self.params.hill_coefficient > 1.0
        }
    


class ReceptorPopulation:
    """Population of receptors with spatial distribution and interactions."""
    
    def __init__(self, receptor_type: ReceptorSubtype, 
                 population_size: int = 1000,
                 spatial_config: SpatialConfiguration = None,
                 parameters: ReceptorParameters = None):
        self.receptor_type = receptor_type
        self.population_size = population_size
        self.spatial_config = spatial_config or SpatialConfiguration()
        self.parameters = parameters or ReceptorParameters()
        
        # Initialize individual receptors
        self.receptors = [ReceptorKinetics(receptor_type, self.parameters) 
                         for _ in range(population_size)]
        
        # Spatial distribution
        self.positions = self._initialize_spatial_distribution()
        
        # Population-level states
        self.population_states = {
            'mean_occupancy': 0.0,
            'occupancy_variance': 0.0,
            'spatial_correlation': 0.0,
            'synchronization_index': 0.0
        }
        
        # Interaction network
        self.interaction_network = self._build_interaction_network()
        
        logger.info(f"ReceptorPopulation initialized: {population_size} {receptor_type} receptors")
        
    def _initialize_spatial_distribution(self) -> np.ndarray:
        """Initialize spatial distribution of receptors."""
        if len(self.spatial_config.coordinates) == 0:
            # Default random distribution in 100μm cube
            positions = np.random.uniform(-50.0, 50.0, (self.population_size, 3))  # Ensure float
        else:
            # Sample from provided coordinates
            n_coords = len(self.spatial_config.coordinates)
            indices = np.random.choice(n_coords, self.population_size, replace=True)
            positions = self.spatial_config.coordinates[indices].astype(np.float64)  # Ensure float
            
            # Add local jitter
            jitter = np.random.normal(0, 1.0, (self.population_size, 3))
            positions = positions + jitter  # Now both are float arrays
            
        return positions
        
    def _build_interaction_network(self) -> scipy.sparse.csr_matrix:
        """Build receptor-receptor interaction network based on spatial proximity."""
        distances = cdist(self.positions, self.positions)
        
        # Interaction strength decreases with distance
        interaction_radius = 10.0  # μm
        interaction_matrix = np.exp(-distances / interaction_radius)
        
        # Remove self-interactions
        np.fill_diagonal(interaction_matrix, 0.0)
        
        # Threshold weak interactions
        interaction_matrix[interaction_matrix < 0.01] = 0.0
        
        return scipy.sparse.csr_matrix(interaction_matrix)
        
    def update_population(self, dt: float, 
                     concentration_field: Union[float, np.ndarray],
                     environmental_conditions: Dict[str, float] = None) -> Dict[str, Any]:
        """Update entire receptor population with spatial interactions."""
        if environmental_conditions is None:
            environmental_conditions = {'temperature': 37.0, 'ph': 7.4}
            
        # Handle concentration field
        if isinstance(concentration_field, (int, float)):
            concentrations = np.full(self.population_size, concentration_field)
        else:
            concentrations = concentration_field
            
        # Update individual receptors
        individual_results = []
        occupancies = np.zeros(self.population_size)
        
        for i, receptor in enumerate(self.receptors):
            # Local concentration with spatial effects
            local_concentration = concentrations[i] if len(concentrations) > i else concentrations[0]
            
            # Add spatial interactions (cooperative effects)
            neighbor_influence = 0.0
            # FIX: Change from ultra.has_data to .nnz > 0
            if self.interaction_network.nnz > 0:  # Check if matrix has non-zero elements
                neighbor_occupancies = np.array([r.states['bound'] for r in self.receptors])
                neighbor_weights = self.interaction_network[i].toarray().flatten()
                neighbor_influence = 0.1 * np.sum(neighbor_weights * neighbor_occupancies)
                
            # Modified concentration accounting for cooperativity
            effective_concentration = local_concentration * (1.0 + neighbor_influence)
            
            # Update receptor
            result = receptor.update(dt, effective_concentration, 
                                environmental_conditions['temperature'],
                                environmental_conditions['ph'])
            
            individual_results.append(result)
            occupancies[i] = result['bound_fraction']
            
        # Compute population statistics
        mean_occupancy = np.mean(occupancies)
        occupancy_variance = np.var(occupancies)
        
        # Spatial correlation
        if len(occupancies) > 1:
            distances_flat = cdist(self.positions, self.positions).flatten()
            occupancy_diffs = np.subtract.outer(occupancies, occupancies).flatten()
            
            # Remove diagonal elements
            mask = distances_flat > 0
            distances_masked = distances_flat[mask]
            diffs_masked = np.abs(occupancy_diffs[mask])
            
            if len(distances_masked) > 0:
                spatial_correlation = -np.corrcoef(distances_masked, diffs_masked)[0, 1]
                if np.isnan(spatial_correlation):
                    spatial_correlation = 0.0
            else:
                spatial_correlation = 0.0
        else:
            spatial_correlation = 0.0
            
        # Synchronization index (variance/mean ratio)
        synchronization_index = occupancy_variance / (mean_occupancy + 1e-10)
        
        # Update population states
        self.population_states.update({
            'mean_occupancy': mean_occupancy,
            'occupancy_variance': occupancy_variance,
            'spatial_correlation': spatial_correlation,
            'synchronization_index': synchronization_index,
            'individual_occupancies': occupancies,
            'effective_concentration': np.mean(concentrations),
            'population_response': mean_occupancy * self.parameters.efficacy
        })
        
        return {
            'population_states': self.population_states.copy(),
            'individual_results': individual_results,
            'spatial_statistics': {
                'mean_position': np.mean(self.positions, axis=0),
                'position_variance': np.var(self.positions, axis=0),
                'density': self.population_size / self._compute_volume()
            }
        }
        
    def _compute_volume(self) -> float:
        """Compute effective volume occupied by receptor population."""
        if len(self.positions) == 0:
            return 1.0
            
        # Convex hull volume
        try:
            from scipy.spatial import ConvexHull
            hull = ConvexHull(self.positions)
            return hull.volume
        except:
            # Fallback: bounding box volume
            ranges = np.ptp(self.positions, axis=0)
            return np.prod(ranges)
            
    def get_population_dose_response(self, concentrations: np.ndarray) -> Dict[str, np.ndarray]:
        """Compute population-level dose-response characteristics."""
        n_concentrations = len(concentrations)
        mean_responses = np.zeros(n_concentrations)
        std_responses = np.zeros(n_concentrations)
        
        for i, conc in enumerate(concentrations):
            # Simulate population response at this concentration
            temp_results = self.update_population(1.0, conc)
            
            mean_responses[i] = temp_results['population_states']['mean_occupancy']
            std_responses[i] = np.sqrt(temp_results['population_states']['occupancy_variance'])
            
        return {
            'concentrations': concentrations,
            'mean_response': mean_responses,
            'std_response': std_responses,
            'cv_response': std_responses / (mean_responses + 1e-10),
            'dynamic_range': np.ptp(mean_responses),
            'ec50': self._estimate_ec50(concentrations, mean_responses),
            'hill_coefficient': self._estimate_hill_coefficient(concentrations, mean_responses)
        }
        
    def _estimate_ec50(self, concentrations: np.ndarray, responses: np.ndarray) -> float:
        """Estimate EC50 from dose-response data."""
        if len(responses) == 0 or np.max(responses) == 0:
            return np.nan
            
        normalized_responses = responses / np.max(responses)
        half_max = 0.5
        
        # Find concentration closest to half-maximal response
        diff_from_half = np.abs(normalized_responses - half_max)
        ec50_idx = np.argmin(diff_from_half)
        
        return concentrations[ec50_idx]
        
    def _estimate_hill_coefficient(self, concentrations: np.ndarray, responses: np.ndarray) -> float:
        """Estimate Hill coefficient from dose-response data."""
        if len(responses) < 4 or np.max(responses) == 0:
            return 1.0
            
        # Fit Hill equation: R = Rmax * C^n / (C^n + EC50^n)
        normalized_responses = responses / np.max(responses)
        log_concentrations = np.log10(concentrations + 1e-12)
        
        # Linearized Hill plot: log(R/(1-R)) = n*log(C) - n*log(EC50)
        valid_mask = (normalized_responses > 0.01) & (normalized_responses < 0.99)
        
        if np.sum(valid_mask) < 3:
            return 1.0
            
        hill_y = np.log(normalized_responses[valid_mask] / (1 - normalized_responses[valid_mask]))
        hill_x = log_concentrations[valid_mask]
        
        try:
            slope, intercept = np.polyfit(hill_x, hill_y, 1)
            return abs(slope)
        except:
            return 1.0


# ===========================================================================================
# NEUROMODULATOR SYSTEMS
# ===========================================================================================

class BaseNeuromodulator(ABC):
    """Abstract base class for neuromodulator systems with FIXED concentration management."""
    
    def __init__(self, modulator_type: ModulatorType, 
             parameters: ModulatorParameters = None,
             spatial_config: SpatialConfiguration = None):
        self.modulator_type = modulator_type
        self.parameters = parameters or ModulatorParameters()
        self.spatial_config = spatial_config or SpatialConfiguration()
        
        # FIXED: Concentration dynamics with proper bounds
        self.concentration = self.parameters.baseline_concentration
        self.concentration_history = deque(maxlen=100000)
        
        # FIXED: Concentration bounds for safety
        self.min_concentration = 1e-12  # 1 pM minimum
        self.max_concentration = 1e-4   # 100 μM maximum (biological limit)
        
        # Release and uptake dynamics
        self.release_sites = []
        self.uptake_sites = []
        self.synthesis_sites = []
        
        # Initialize default release sites
        self._initialize_default_sites()
        
        # Receptor populations
        self.receptor_populations = {}
        self._initialize_receptors()
        
        # Spatial concentration field
        self.spatial_field = None
        self._initialize_spatial_field()
        
        # Modulation effects
        self.current_effects = {}
        self.effect_history = deque(maxlen=10000)
        
        logger.info(f"BaseNeuromodulator initialized: {modulator_type}")

    def _clamp_concentration(self, concentration: float) -> float:
        """FIXED: Clamp concentration to biological ranges."""
        return float(np.clip(concentration, self.min_concentration, self.max_concentration))

    def _initialize_default_sites(self):
        """Initialize default release and uptake sites with FIXED scaling."""
        # Add at least one release site at origin
        default_position = np.array([0.0, 0.0, 0.0])
        self.add_release_site(default_position, strength=1.0, vesicle_pool_size=100)  # FIXED: Reduced pool
        
        # Add default uptake site
        self.add_uptake_site(default_position, transporter_density=1.0, uptake_capacity=1.0)
        
        # Add default synthesis site
        self.add_synthesis_site(default_position, capacity=1.0, enzyme_concentration=1.0)
        
    def add_release_site(self, position: np.ndarray, 
                        strength: float = 1.0, 
                        vesicle_pool_size: int = 100) -> int:  # FIXED: Reduced default pool
        """Add a release site with FIXED vesicle pool size."""
        site_id = len(self.release_sites)
        site = {
            'id': site_id,
            'position': position.copy(),
            'strength': strength,
            'vesicle_pool_size': vesicle_pool_size,
            'available_vesicles': vesicle_pool_size,
            'release_history': deque(maxlen=1000),
            'last_release_time': 0.0,  # FIXED: Track timing
            'release_scaling': 1e-9    # FIXED: Scale factor for concentration
        }
        self.release_sites.append(site)
        
        logger.debug(f"Added release site {site_id} for {self.modulator_type}")
        return site_id
        
    def trigger_release(self, site_id: int, intensity: float) -> float:
        """Trigger release with FIXED scaling and bounds."""
        if site_id >= len(self.release_sites):
            logger.warning(f"Invalid release site ID: {site_id}")
            return 0.0
            
        site = self.release_sites[site_id]
        
        # FIXED: Clamp intensity
        intensity = np.clip(intensity, 0.0, 1.0)
        
        # FIXED: Probability-based vesicle release with scaling
        n_vesicles_to_release = 0
        max_vesicles = min(10, site['available_vesicles'])  # FIXED: Limit max release
        
        for _ in range(max_vesicles):
            if np.random.random() < self.parameters.release_probability * intensity:
                n_vesicles_to_release += 1
                
        # FIXED: Amount released with proper scaling
        base_amount = n_vesicles_to_release * self.parameters.vesicle_content
        scaled_amount = base_amount * site['release_scaling']  # FIXED: Apply scaling
        
        # FIXED: Prevent concentration explosions
        max_allowed_release = self.max_concentration * 0.01  # 1% of max concentration
        scaled_amount = min(scaled_amount, max_allowed_release)
        
        # Update vesicle pool
        site['available_vesicles'] = max(0, site['available_vesicles'] - n_vesicles_to_release)
        
        # FIXED: Replenish vesicles with rate limiting
        current_time = time.time()
        time_since_last = current_time - site['last_release_time']
        replenishment = min(5, int(time_since_last * 10), site['vesicle_pool_size'] - site['available_vesicles'])
        site['available_vesicles'] += replenishment
        site['last_release_time'] = current_time
        
        # Record release
        site['release_history'].append((current_time, scaled_amount, intensity))
        
        logger.debug(f"Released {scaled_amount:.2e} from site {site_id} (vesicles: {n_vesicles_to_release})")
        return scaled_amount
        
    def update_concentration(self, dt: float, 
                           release_events: List[Tuple[int, float]] = None,
                           synthesis_modulation: float = 1.0,
                           uptake_modulation: float = 1.0) -> Dict[str, Any]:
        """Update modulator concentration with FIXED kinetics and bounds."""
        if release_events is None:
            release_events = []
            
        # FIXED: Clamp inputs
        dt = np.clip(dt, 1e-6, 1.0)  # Reasonable timestep bounds
        synthesis_modulation = np.clip(synthesis_modulation, 0.1, 10.0)
        uptake_modulation = np.clip(uptake_modulation, 0.1, 10.0)
            
        # Compute synthesis rate
        synthesis_rate = self._compute_synthesis_rate() * synthesis_modulation
        
        # Compute uptake and metabolism rates
        uptake_rate = self.parameters.reuptake_rate * uptake_modulation
        metabolism_rate = self.parameters.metabolism_rate
        diffusion_rate = self.parameters.diffusion_rate
        
        # FIXED: Process release events with proper scaling
        total_release = 0.0
        spatial_releases = np.zeros_like(self.spatial_field)
        
        for site_index, amount in release_events:
            if 0 <= site_index < len(spatial_releases):
                # FIXED: Scale release amount
                scaled_amount = amount * 1e-6  # Scale down by 1M
                spatial_releases[site_index] += scaled_amount
                total_release += scaled_amount
            else:
                # Uniform release if site index invalid
                scaled_amount = amount * 1e-6
                spatial_releases += scaled_amount / len(spatial_releases)
                total_release += scaled_amount
                
        # FIXED: Update spatial concentration field with bounds
        if len(self.spatial_field) == 1:
            # Single compartment model
            dc_dt = (synthesis_rate + total_release / dt - 
                    (uptake_rate + metabolism_rate + diffusion_rate) * self.concentration)
            
            new_concentration = self.concentration + dc_dt * dt
            self.concentration = self._clamp_concentration(new_concentration)  # FIXED: Apply bounds
            self.spatial_field[0] = self.concentration
            
        else:
            # Multi-compartment spatial model
            self._update_spatial_field(dt, spatial_releases, synthesis_rate, 
                                     uptake_rate, metabolism_rate, diffusion_rate)
            self.concentration = np.mean(self.spatial_field)
            
        # FIXED: Ensure spatial field is also bounded
        self.spatial_field = np.clip(self.spatial_field, self.min_concentration, self.max_concentration)
        
        # Update receptor populations
        receptor_results = {}
        for receptor_type, population in self.receptor_populations.items():
            try:
                receptor_results[receptor_type] = population.update_population(
                    dt, self.spatial_field, {'temperature': 37.0, 'ph': 7.4}
                )
            except Exception as e:
                logger.warning(f"Receptor update failed for {receptor_type}: {e}")
                receptor_results[receptor_type] = {'error': str(e)}
            
        # Compute modulation effects
        try:
            self.current_effects = self._compute_modulation_effects()
        except Exception as e:
            logger.warning(f"Effect computation failed: {e}")
            self.current_effects = {}
        
        # Store history with bounds checking
        current_time = time.time()
        self.concentration_history.append((current_time, float(self.concentration)))
        self.effect_history.append((current_time, self.current_effects.copy()))
        
        # FIXED: Return comprehensive results with validation
        return {
            'concentration': float(self.concentration),
            'spatial_field': self.spatial_field.copy(),
            'synthesis_rate': float(synthesis_rate),
            'uptake_rate': float(uptake_rate),
            'total_release': float(total_release),
            'receptor_results': receptor_results,
            'modulation_effects': self.current_effects.copy(),
            'field_statistics': {
                'mean': float(np.mean(self.spatial_field)),
                'std': float(np.std(self.spatial_field)),
                'min': float(np.min(self.spatial_field)),
                'max': float(np.max(self.spatial_field)),
                'gradient_magnitude': self._compute_spatial_gradient_magnitude(),
                'concentration_valid': self.min_concentration <= self.concentration <= self.max_concentration
            },
            'numerical_stability': {
                'concentration_bounded': self.min_concentration <= self.concentration <= self.max_concentration,
                'spatial_field_bounded': np.all((self.spatial_field >= self.min_concentration) & 
                                               (self.spatial_field <= self.max_concentration)),
                'synthesis_rate_valid': 0.0 <= synthesis_rate <= 1e-6,
                'total_release_valid': 0.0 <= total_release <= self.max_concentration * 0.1
            }
        }

        
    @abstractmethod
    def _initialize_receptors(self):
        """Initialize receptor populations specific to this modulator with generic implementation."""
        # Generic receptor initialization - can be overridden by specific modulator systems
        
        # Create default receptor parameters based on modulator type
        default_receptor_params = ReceptorParameters(
            kon=1e7,  # Default association rate
            koff=5e-3,  # Default dissociation rate
            kd=5e-10,  # Default dissociation constant
            density=500.0,  # Default receptor density
            efficacy=1.0,  # Default efficacy
            coupling_strength=1.0,  # Default coupling
            hill_coefficient=1.0,  # Default Hill coefficient
            desensitization_rate=0.1,
            internalization_rate=0.01,
            recovery_rate=0.05
        )
        
        # Create generic receptor populations based on modulator type
        if self.modulator_type == ModulatorType.DOPAMINE:
            # Default dopamine receptors
            self.receptor_populations[ReceptorSubtype.D1] = ReceptorPopulation(
                ReceptorSubtype.D1, 800, self.spatial_config, default_receptor_params
            )
            self.receptor_populations[ReceptorSubtype.D2] = ReceptorPopulation(
                ReceptorSubtype.D2, 600, self.spatial_config, default_receptor_params
            )
            
        elif self.modulator_type == ModulatorType.SEROTONIN:
            # Default serotonin receptors
            self.receptor_populations[ReceptorSubtype.HTR1A] = ReceptorPopulation(
                ReceptorSubtype.HTR1A, 1000, self.spatial_config, default_receptor_params
            )
            self.receptor_populations[ReceptorSubtype.HTR2A] = ReceptorPopulation(
                ReceptorSubtype.HTR2A, 700, self.spatial_config, default_receptor_params
            )
            
        elif self.modulator_type == ModulatorType.NOREPINEPHRINE:
            # Default adrenergic receptors
            self.receptor_populations[ReceptorSubtype.ALPHA1A] = ReceptorPopulation(
                ReceptorSubtype.ALPHA1A, 500, self.spatial_config, default_receptor_params
            )
            self.receptor_populations[ReceptorSubtype.BETA1] = ReceptorPopulation(
                ReceptorSubtype.BETA1, 400, self.spatial_config, default_receptor_params
            )
            
        elif self.modulator_type == ModulatorType.ACETYLCHOLINE:
            # Default cholinergic receptors
            self.receptor_populations[ReceptorSubtype.NICOTINIC_ALPHA4BETA2] = ReceptorPopulation(
                ReceptorSubtype.NICOTINIC_ALPHA4BETA2, 800, self.spatial_config, default_receptor_params
            )
            self.receptor_populations[ReceptorSubtype.MUSCARINIC_M1] = ReceptorPopulation(
                ReceptorSubtype.MUSCARINIC_M1, 600, self.spatial_config, default_receptor_params
            )
            
        else:
            # Generic receptor for unknown modulator types
            generic_receptor = ReceptorSubtype.D1  # Use D1 as placeholder
            self.receptor_populations[generic_receptor] = ReceptorPopulation(
                generic_receptor, 500, self.spatial_config, default_receptor_params
            )
            
        logger.debug(f"Initialized {len(self.receptor_populations)} receptor populations for {self.modulator_type}")



    def _safe_divide(self, numerator: float, denominator: float, default: float = 0.0) -> float:
        """Safe division with default value for zero denominator."""
        if abs(denominator) < 1e-12:
            return default
        return numerator / denominator

    def _safe_log(self, x: float, default: float = -12.0) -> float:
        """Safe logarithm with default for non-positive values."""
        if x <= 0:
            return default
        return np.log(x)

    def _ensure_finite(self, value: float, default: float = 0.0) -> float:
        """Ensure value is finite, replace with default if not."""
        if not np.isfinite(value):
            return default
        return value



    def _compute_synthesis_rate(self, **kwargs) -> float:
        """Compute current synthesis rate based on physiological signals with generic implementation."""
        
        # Base synthesis rate from parameters
        base_rate = self.parameters.synthesis_rate
        
        # Activity-dependent synthesis modulation
        neural_activity = kwargs.get('neural_activity', 0.0)
        activity_factor = 1.0 + 0.3 * neural_activity
        
        # Circadian modulation (generic 24-hour cycle)
        circadian_phase = kwargs.get('circadian_phase', 0.0)
        circadian_factor = 1.0 + 0.2 * np.cos(circadian_phase)
        
        # Stress modulation (general stress response)
        stress_level = kwargs.get('stress_level', 0.0)
        stress_factor = 1.0 + 0.4 * stress_level
        
        # Feedback inhibition based on current concentration
        concentration_ratio = self.concentration / (self.parameters.baseline_concentration + 1e-12)
        feedback_factor = 1.0 / (1.0 + 0.5 * concentration_ratio)
        
        # Substrate availability (generic precursor availability)
        substrate_availability = kwargs.get('substrate_level', 1.0)
        substrate_factor = substrate_availability / (0.5 + substrate_availability)  # Michaelis-Menten kinetics
        
        # Environmental temperature effects (Q10 = 2-3 for enzymatic reactions)
        temperature = kwargs.get('temperature', 37.0)  # Default body temperature
        q10 = 2.5  # Temperature coefficient
        temp_factor = q10 ** ((temperature - 37.0) / 10.0)
        
        # pH effects on enzymatic synthesis
        ph = kwargs.get('ph', 7.4)  # Default physiological pH
        optimal_ph = 7.4
        ph_factor = np.exp(-((ph - optimal_ph) / 0.5) ** 2)  # Gaussian around optimal pH
        
        # Combine all factors
        total_synthesis_rate = (base_rate * activity_factor * circadian_factor * 
                            stress_factor * feedback_factor * substrate_factor * 
                            temp_factor * ph_factor)
        
        # Ensure non-negative rate
        total_synthesis_rate = max(0.0, total_synthesis_rate)
        
        logger.debug(f"Computed synthesis rate for {self.modulator_type}: {total_synthesis_rate:.6f}")
        
        return total_synthesis_rate

    def _compute_release_rate(self, **kwargs) -> float:
        """Compute current release rate based on neural activity with generic implementation."""
        
        # Base release rate (tonic release)
        base_release = self.parameters.synthesis_rate * 0.1  # 10% of synthesis as baseline
        
        # Neural activity-dependent release
        neural_activity = kwargs.get('neural_activity', 0.0)
        activity_release = neural_activity * 0.05
        
        # Stimulus-evoked release
        stimulus_intensity = kwargs.get('stimulus_intensity', 0.0)
        stimulus_release = stimulus_intensity * 0.08
        
        # Frequency-dependent facilitation/depression
        stimulation_frequency = kwargs.get('stimulation_frequency', 0.0)
        if stimulation_frequency > 0:
            # High frequency facilitates, very high frequency depresses
            if stimulation_frequency < 20.0:  # Hz
                freq_factor = 1.0 + 0.5 * (stimulation_frequency / 20.0)
            else:
                freq_factor = 1.5 - 0.3 * ((stimulation_frequency - 20.0) / 80.0)
            freq_factor = max(0.2, freq_factor)
        else:
            freq_factor = 1.0
            
        # Calcium-dependent release
        calcium_level = kwargs.get('calcium_concentration', 1.0)
        # Hill equation for calcium dependence (cooperative binding)
        hill_coeff = 4.0  # Typical for calcium-dependent exocytosis
        calcium_factor = (calcium_level ** hill_coeff) / (calcium_level ** hill_coeff + 1.0)
        
        # Vesicle pool depletion
        vesicle_availability = kwargs.get('vesicle_pool_fraction', 1.0)
        depletion_factor = vesicle_availability
        
        # Presynaptic inhibition/facilitation
        presynaptic_modulation = kwargs.get('presynaptic_modulation', 1.0)
        
        # Temperature effects on vesicle fusion
        temperature = kwargs.get('temperature', 37.0)
        temp_factor = 2.0 ** ((temperature - 37.0) / 10.0)  # Q10 = 2 for vesicle fusion
        
        # Osmolarity effects
        osmolarity = kwargs.get('osmolarity', 300.0)  # mOsm
        optimal_osmolarity = 300.0
        osmotic_factor = 1.0 / (1.0 + abs(osmolarity - optimal_osmolarity) / 50.0)
        
        # Combine all release factors
        total_release_rate = (base_release + activity_release + stimulus_release) * \
                            freq_factor * calcium_factor * depletion_factor * \
                            presynaptic_modulation * temp_factor * osmotic_factor
        
        # Ensure non-negative rate
        total_release_rate = max(0.0, total_release_rate)
        
        logger.debug(f"Computed release rate for {self.modulator_type}: {total_release_rate:.6f}")
        
        return total_release_rate

    def _compute_modulation_effects(self) -> Dict[str, float]:
        """Compute current modulation effects on neural parameters with generic implementation."""
        
        effects = {}
        
        # Get current concentration relative to baseline
        concentration_ratio = self.concentration / (self.parameters.baseline_concentration + 1e-12)
        
        # Get mean receptor occupancy across all receptor populations
        mean_occupancy = 0.0
        total_populations = 0
        
        for receptor_type, population in self.receptor_populations.items():
            if hasattr(population, 'population_states'):
                occupancy = population.population_states.get('mean_occupancy', 0.0)
                mean_occupancy += occupancy
                total_populations += 1
                
        if total_populations > 0:
            mean_occupancy /= total_populations
        
        # Generic modulation effects based on concentration and receptor occupancy
        
        # Synaptic weight modulation (typically enhancing)
        effects[ModulationTarget.SYNAPTIC_WEIGHT.value] = 1.0 + 0.3 * mean_occupancy
        
        # Synaptic conductance modulation
        effects[ModulationTarget.SYNAPTIC_CONDUCTANCE.value] = 1.0 + 0.25 * mean_occupancy
        
        # Firing threshold modulation (lower threshold = more excitable)
        if concentration_ratio > 1.0:
            threshold_change = -0.1 * np.log(concentration_ratio)  # Logarithmic relationship
        else:
            threshold_change = 0.1 * (1.0 - concentration_ratio)
        effects[ModulationTarget.FIRING_THRESHOLD.value] = 1.0 + threshold_change
        
        # Membrane resistance modulation
        effects[ModulationTarget.MEMBRANE_RESISTANCE.value] = 1.0 + 0.2 * (concentration_ratio - 1.0)
        
        # Time constant modulation
        effects[ModulationTarget.TIME_CONSTANT.value] = 1.0 + 0.15 * (concentration_ratio - 1.0)
        
        # Plasticity rate modulation (most modulators enhance plasticity)
        plasticity_enhancement = 0.5 * mean_occupancy * concentration_ratio
        effects[ModulationTarget.PLASTICITY_RATE.value] = 1.0 + plasticity_enhancement
        
        # Noise level modulation (usually reduces noise)
        effects[ModulationTarget.NOISE_LEVEL.value] = 1.0 - 0.3 * mean_occupancy
        
        # Inhibition strength modulation
        effects[ModulationTarget.INHIBITION_STRENGTH.value] = 1.0 + 0.2 * mean_occupancy
        
        # Refractory period modulation
        effects[ModulationTarget.REFRACTORY_PERIOD.value] = 1.0 - 0.1 * mean_occupancy
        
        # Adaptation strength modulation
        effects[ModulationTarget.ADAPTATION_STRENGTH.value] = 1.0 + 0.25 * mean_occupancy
        
        # Modulator-specific effects based on type
        if self.modulator_type == ModulatorType.DOPAMINE:
            effects['reward_sensitivity'] = 1.0 + 0.6 * concentration_ratio
            effects['motor_facilitation'] = 1.0 + 0.4 * mean_occupancy
            effects['working_memory_gain'] = 1.0 + 0.5 * mean_occupancy
            
        elif self.modulator_type == ModulatorType.SEROTONIN:
            effects['mood_regulation'] = 1.0 + 0.5 * concentration_ratio
            effects['impulse_control'] = 1.0 + 0.4 * mean_occupancy
            effects['sleep_regulation'] = 1.0 + 0.3 * concentration_ratio
            
        elif self.modulator_type == ModulatorType.NOREPINEPHRINE:
            effects['arousal_level'] = 1.0 + 0.8 * concentration_ratio
            effects['attention_focus'] = 1.0 + 0.6 * mean_occupancy
            effects['stress_response'] = 1.0 + 0.7 * concentration_ratio
            
        elif self.modulator_type == ModulatorType.ACETYLCHOLINE:
            effects['attention_enhancement'] = 1.0 + 0.7 * mean_occupancy
            effects['learning_facilitation'] = 1.0 + 0.8 * mean_occupancy
            effects['sensory_gating'] = 1.0 + 0.5 * mean_occupancy
            
        # Apply saturation and bounds to all effects
        for key, value in effects.items():
            # Apply sigmoid saturation for very high concentrations
            if value > 2.0:
                effects[key] = 2.0 - 1.0 / (1.0 + np.exp(-(value - 2.0)))
            # Ensure minimum effect (never completely eliminate)
            effects[key] = max(0.1, effects[key])
            
        # Compute temporal dynamics for effects
        self._apply_temporal_dynamics(effects)
        
        logger.debug(f"Computed {len(effects)} modulation effects for {self.modulator_type}")
        
        return effects

    def _apply_temporal_dynamics(self, effects: Dict[str, float]):
        """Apply temporal dynamics to modulation effects."""
        
        # Get time since last update (approximate)
        current_time = time.time()
        if hasattr(self, '_last_effect_time'):
            dt = current_time - self._last_effect_time
        else:
            dt = 0.1  # Default timestep
        self._last_effect_time = current_time
        
        # Apply temporal filtering to smooth rapid changes
        if hasattr(self, '_previous_effects'):
            tau_effect = 1.0  # Effect time constant in seconds
            alpha = dt / (tau_effect + dt)  # Exponential filter coefficient
            
            for key, new_value in effects.items():
                if key in self._previous_effects:
                    old_value = self._previous_effects[key]
                    # Exponential smoothing
                    effects[key] = old_value + alpha * (new_value - old_value)
        
        # Store effects for next iteration
        self._previous_effects = effects.copy()

    def add_synthesis_site(self, position: np.ndarray, 
                        capacity: float = 1.0,
                        enzyme_concentration: float = 1.0) -> int:
        """Add a synthesis site for this modulator."""
        site_id = len(self.synthesis_sites)
        site = {
            'id': site_id,
            'position': position.copy(),
            'capacity': capacity,
            'enzyme_concentration': enzyme_concentration,
            'current_synthesis_rate': 0.0,
            'substrate_levels': {},
            'synthesis_history': deque(maxlen=1000)
        }
        self.synthesis_sites.append(site)
        
        logger.debug(f"Added synthesis site {site_id} for {self.modulator_type}")
        return site_id

    def add_uptake_site(self, position: np.ndarray,
                    transporter_density: float = 1.0,
                    uptake_capacity: float = 1.0) -> int:
        """Add an uptake site for this modulator."""
        site_id = len(self.uptake_sites)
        site = {
            'id': site_id,
            'position': position.copy(),
            'transporter_density': transporter_density,
            'uptake_capacity': uptake_capacity,
            'current_uptake_rate': 0.0,
            'saturation_level': 0.0,
            'uptake_history': deque(maxlen=1000)
        }
        self.uptake_sites.append(site)
        
        logger.debug(f"Added uptake site {site_id} for {self.modulator_type}")
        return site_id

    def compute_uptake_rate(self, local_concentration: float,
                        site_id: int = 0) -> float:
        """Compute uptake rate at a specific site using Michaelis-Menten kinetics."""
        
        if site_id >= len(self.uptake_sites):
            logger.warning(f"Invalid uptake site ID: {site_id}")
            return 0.0
            
        site = self.uptake_sites[site_id]
        
        # Michaelis-Menten parameters
        vmax = site['uptake_capacity'] * site['transporter_density']
        km = self.parameters.baseline_concentration  # Use baseline as approximate Km
        
        # Michaelis-Menten equation
        uptake_rate = (vmax * local_concentration) / (km + local_concentration)
        
        # Apply competitive inhibition if multiple substrates
        inhibition_factor = 1.0
        for other_modulator_conc in getattr(self, '_competing_concentrations', {}).values():
            ki = km  # Assume similar binding affinity
            inhibition_factor /= (1.0 + other_modulator_conc / ki)
        
        uptake_rate *= inhibition_factor
        
        # Update site state
        site['current_uptake_rate'] = uptake_rate
        site['saturation_level'] = local_concentration / (km + local_concentration)
        site['uptake_history'].append((time.time(), uptake_rate))
        
        return uptake_rate

    def compute_spatial_diffusion(self, dt: float) -> np.ndarray:
        """Compute spatial diffusion of modulator concentration."""
        
        if self.spatial_field is None or len(self.spatial_field) <= 1:
            return np.zeros_like(self.spatial_field) if self.spatial_field is not None else np.array([0.0])
        
        # Build diffusion matrix if needed
        if not hasattr(self, '_diffusion_matrix'):
            self._build_diffusion_matrix()
        
        # Fick's second law: ∂C/∂t = D ∇²C
        diffusion_coefficient = self.parameters.diffusion_coefficient
        
        # Discrete diffusion using finite differences
        diffusion_flux = np.zeros_like(self.spatial_field)
        
        if hasattr(self, '_diffusion_matrix'):
            # Use precomputed diffusion matrix
            diffusion_flux = diffusion_coefficient * (self._diffusion_matrix @ self.spatial_field)
        else:
            # Simple nearest-neighbor diffusion
            for i in range(len(self.spatial_field)):
                neighbors = self._get_spatial_neighbors(i)
                if neighbors:
                    neighbor_avg = np.mean([self.spatial_field[j] for j in neighbors])
                    diffusion_flux[i] = diffusion_coefficient * (neighbor_avg - self.spatial_field[i])
        
        return diffusion_flux

    def _build_diffusion_matrix(self):
        """Build diffusion matrix for spatial calculations."""
        
        n_points = len(self.spatial_field)
        self._diffusion_matrix = np.zeros((n_points, n_points))
        
        if len(self.spatial_config.coordinates) > 0:
            # Use actual spatial coordinates
            distances = cdist(self.spatial_config.coordinates, self.spatial_config.coordinates)
            
            # Diffusion kernel (Gaussian)
            diffusion_radius = self.parameters.diffusion_radius
            kernel = np.exp(-(distances ** 2) / (2 * diffusion_radius ** 2))
            
            # Normalize and remove self-connections
            np.fill_diagonal(kernel, 0.0)
            row_sums = np.sum(kernel, axis=1)
            row_sums[row_sums == 0] = 1.0
            kernel = kernel / row_sums[:, np.newaxis]
            
            # Diffusion matrix: (neighbors - self)
            self._diffusion_matrix = kernel - np.eye(n_points)
        else:
            # Default 1D chain diffusion
            for i in range(n_points):
                if i > 0:
                    self._diffusion_matrix[i, i-1] = 1.0
                if i < n_points - 1:
                    self._diffusion_matrix[i, i+1] = 1.0
                self._diffusion_matrix[i, i] = -np.sum(self._diffusion_matrix[i, :])

    def _get_spatial_neighbors(self, point_index: int, radius: float = None) -> List[int]:
        """Get spatial neighbors of a point within given radius."""
        
        if len(self.spatial_config.coordinates) == 0:
            # Default nearest neighbors for 1D case
            neighbors = []
            if point_index > 0:
                neighbors.append(point_index - 1)
            if point_index < len(self.spatial_field) - 1:
                neighbors.append(point_index + 1)
            return neighbors
        
        if radius is None:
            radius = self.parameters.diffusion_radius
        
        neighbors = []
        point_pos = self.spatial_config.coordinates[point_index]
        
        for i, other_pos in enumerate(self.spatial_config.coordinates):
            if i != point_index:
                distance = np.linalg.norm(point_pos - other_pos)
                if distance <= radius:
                    neighbors.append(i)
        
        return neighbors

    def compute_metabolic_degradation(self, local_concentration: float,
                                    enzyme_activity: float = 1.0) -> float:
        """Compute metabolic degradation rate using enzyme kinetics."""
        
        # Michaelis-Menten kinetics for enzymatic degradation
        vmax = self.parameters.metabolism_rate * enzyme_activity
        km = self.parameters.baseline_concentration * 2.0  # Assume Km is 2x baseline
        
        degradation_rate = (vmax * local_concentration) / (km + local_concentration)
        
        # Temperature dependence (Q10 = 2-3 for enzymes)
        temperature = getattr(self, '_current_temperature', 37.0)
        q10 = 2.5
        temp_factor = q10 ** ((temperature - 37.0) / 10.0)
        degradation_rate *= temp_factor
        
        # pH dependence
        ph = getattr(self, '_current_ph', 7.4)
        optimal_ph = 7.4
        ph_factor = np.exp(-((ph - optimal_ph) / 0.4) ** 2)
        degradation_rate *= ph_factor
        
        # Competitive inhibition by metabolites
        inhibition_factor = 1.0
        if hasattr(self, '_metabolite_concentrations'):
            for metabolite_conc in self._metabolite_concentrations.values():
                ki = km * 0.5  # Assume metabolites have moderate inhibition
                inhibition_factor /= (1.0 + metabolite_conc / ki)
        
        degradation_rate *= inhibition_factor
        
        return max(0.0, degradation_rate)

    def get_pharmacokinetic_parameters(self) -> Dict[str, float]:
        """Get current pharmacokinetic parameters."""
        
        # Half-life calculation
        clearance_rate = (self.parameters.reuptake_rate + 
                        self.parameters.metabolism_rate + 
                        self.parameters.diffusion_rate)
        half_life = np.log(2) / (clearance_rate + 1e-12)
        
        # Volume of distribution (simplified)
        volume_distribution = 1.0  # Assume 1 L default
        if hasattr(self.spatial_config, 'coordinates') and len(self.spatial_config.coordinates) > 0:
            # Estimate from spatial configuration
            coords = self.spatial_config.coordinates
            volume_distribution = np.prod(np.ptp(coords, axis=0)) / 1000.0  # Convert to L
        
        # Clearance
        clearance = clearance_rate * volume_distribution
        
        # Area under curve
        auc = self.concentration / (clearance_rate + 1e-12)
        
        # Bioavailability (assume 100% for endogenous modulators)
        bioavailability = 1.0
        
        # Time to peak (simplified)
        synthesis_rate = self._compute_synthesis_rate()
        time_to_peak = half_life * np.log(synthesis_rate / clearance_rate + 1.0)
        
        return {
            'half_life': half_life,
            'clearance': clearance,
            'volume_distribution': volume_distribution,
            'auc': auc,
            'bioavailability': bioavailability,
            'time_to_peak': max(0.0, time_to_peak),
            'steady_state_time': 5 * half_life,  # ~5 half-lives to steady state
            'elimination_rate_constant': clearance_rate,
            'mean_residence_time': 1.0 / clearance_rate
        }

    def simulate_dose_response(self, dose_range: np.ndarray,
                            response_metric: str = 'receptor_occupancy') -> Dict[str, np.ndarray]:
        """Simulate dose-response relationship for this modulator."""
        
        responses = np.zeros_like(dose_range)
        original_concentration = self.concentration
        
        try:
            for i, dose in enumerate(dose_range):
                # Set concentration to dose level
                self.concentration = dose
                
                # Update receptor populations
                for receptor_type, population in self.receptor_populations.items():
                    population.update_population(0.1, dose)
                
                # Compute response based on metric
                if response_metric == 'receptor_occupancy':
                    total_occupancy = 0.0
                    for population in self.receptor_populations.values():
                        total_occupancy += population.population_states.get('mean_occupancy', 0.0)
                    responses[i] = total_occupancy / len(self.receptor_populations)
                    
                elif response_metric == 'modulation_effects':
                    effects = self._compute_modulation_effects()
                    # Use average of all effects as response
                    effect_values = [v for k, v in effects.items() if isinstance(v, (int, float))]
                    responses[i] = np.mean(effect_values) if effect_values else 1.0
                    
                elif response_metric == 'synthesis_rate':
                    responses[i] = self._compute_synthesis_rate()
                    
                elif response_metric == 'release_rate':
                    responses[i] = self._compute_release_rate()
                    
                else:
                    # Default to concentration
                    responses[i] = dose
                    
        finally:
            # Restore original concentration
            self.concentration = original_concentration
        
        # Fit dose-response parameters
        ec50, hill_coefficient, max_response = self._fit_dose_response_curve(dose_range, responses)
        
        return {
            'doses': dose_range,
            'responses': responses,
            'ec50': ec50,
            'hill_coefficient': hill_coefficient,
            'max_response': max_response,
            'dynamic_range': np.ptp(responses),
            'threshold_dose': self._find_threshold_dose(dose_range, responses),
            'saturation_dose': self._find_saturation_dose(dose_range, responses)
        }

    def _fit_dose_response_curve(self, doses: np.ndarray, responses: np.ndarray) -> Tuple[float, float, float]:
        """Fit Hill equation to dose-response data."""
        
        if len(doses) < 4 or np.ptp(responses) < 1e-6:
            return np.median(doses), 1.0, np.max(responses)
        
        try:
            # Hill equation: R = Rmax * D^n / (D^n + EC50^n)
            def hill_equation(dose, ec50, hill_coeff, max_resp):
                return max_resp * (dose ** hill_coeff) / (dose ** hill_coeff + ec50 ** hill_coeff)
            
            # Initial parameter guesses
            max_resp_guess = np.max(responses)
            ec50_guess = doses[np.argmin(np.abs(responses - max_resp_guess / 2))]
            hill_guess = 1.0
            
            # Fit curve
            from scipy.optimize import curve_fit
            popt, _ = curve_fit(hill_equation, doses, responses, 
                            p0=[ec50_guess, hill_guess, max_resp_guess],
                            bounds=([np.min(doses), 0.1, 0], 
                                    [np.max(doses), 10.0, max_resp_guess * 2]))
            
            return popt[0], popt[1], popt[2]
            
        except Exception as e:
            logger.warning(f"Dose-response fitting failed: {e}")
            return np.median(doses), 1.0, np.max(responses)

    def _find_threshold_dose(self, doses: np.ndarray, responses: np.ndarray) -> float:
        """Find threshold dose (10% of maximum response)."""
        max_response = np.max(responses)
        threshold_response = 0.1 * max_response
        
        # Find first dose that exceeds threshold
        above_threshold = responses >= threshold_response
        if np.any(above_threshold):
            return doses[np.argmax(above_threshold)]
        else:
            return doses[-1]

    def _find_saturation_dose(self, doses: np.ndarray, responses: np.ndarray) -> float:
        """Find saturation dose (90% of maximum response)."""
        max_response = np.max(responses)
        saturation_response = 0.9 * max_response
        
        # Find first dose that exceeds saturation
        above_saturation = responses >= saturation_response
        if np.any(above_saturation):
            return doses[np.argmax(above_saturation)]
        else:
            return doses[-1]
        
    def _initialize_spatial_field(self):
        """Initialize spatial concentration field."""
        if len(self.spatial_config.coordinates) > 0:
            n_points = len(self.spatial_config.coordinates)
            self.spatial_field = np.full(n_points, self.parameters.baseline_concentration)
        else:
            # Default single-compartment model
            self.spatial_field = np.array([self.parameters.baseline_concentration])
            
    
        
    def _update_spatial_field(self, dt: float, releases: np.ndarray,
                            synthesis_rate: float, uptake_rate: float,
                            metabolism_rate: float, diffusion_rate: float):
        """Update spatial concentration field with diffusion."""
        # Build diffusion matrix if not available
        if self.spatial_config.distance_matrix is None:
            distances = cdist(self.spatial_config.coordinates, 
                            self.spatial_config.coordinates)
            self.spatial_config.distance_matrix = distances
            
        # Diffusion term using distance-based kernel
        diffusion_kernel = np.exp(-self.spatial_config.distance_matrix / 
                                self.parameters.diffusion_radius)
        np.fill_diagonal(diffusion_kernel, 0.0)
        
        # Normalize diffusion kernel
        row_sums = np.sum(diffusion_kernel, axis=1)
        row_sums[row_sums == 0] = 1.0
        diffusion_kernel = diffusion_kernel / row_sums[:, np.newaxis]
        
        # Compute diffusion flux
        diffusion_flux = self.parameters.diffusion_coefficient * (
            diffusion_kernel @ self.spatial_field - self.spatial_field
        )
        
        # Update concentration at each point
        dc_dt = (synthesis_rate + releases / dt + diffusion_flux - 
                (uptake_rate + metabolism_rate) * self.spatial_field)
        
        self.spatial_field = np.maximum(0.0, self.spatial_field + dc_dt * dt)
        
    def _compute_spatial_gradient_magnitude(self) -> float:
        """Compute magnitude of spatial concentration gradient."""
        if len(self.spatial_field) <= 1:
            return 0.0
            
        if self.spatial_config.distance_matrix is None:
            return 0.0
            
        # Compute local gradients
        gradients = []
        for i in range(len(self.spatial_field)):
            neighbors = np.where(self.spatial_config.distance_matrix[i] > 0)[0]
            if len(neighbors) > 0:
                neighbor_distances = self.spatial_config.distance_matrix[i][neighbors]
                neighbor_concentrations = self.spatial_field[neighbors]
                
                # Gradient magnitude for this point
                concentration_diffs = neighbor_concentrations - self.spatial_field[i]
                gradient_components = concentration_diffs / (neighbor_distances + 1e-9)
                gradient_magnitude = np.sqrt(np.sum(gradient_components**2))
                gradients.append(gradient_magnitude)
                
        return np.mean(gradients) if gradients else 0.0
        
    
        
    
        
    def get_modulation_effects(self) -> Dict[str, float]:
        """Get current modulation effects."""
        return self.current_effects.copy()
        
    def get_concentration_statistics(self, window_duration: float = 60.0) -> Dict[str, float]:
        """Get concentration statistics over specified time window."""
        current_time = time.time()
        cutoff_time = current_time - window_duration
        
        # Filter recent data
        recent_data = [(t, c) for t, c in self.concentration_history if t >= cutoff_time]
        
        if not recent_data:
            return {'mean': self.concentration, 'std': 0.0, 'min': self.concentration, 
                   'max': self.concentration, 'cv': 0.0}
            
        concentrations = [c for _, c in recent_data]
        
        return {
            'mean': np.mean(concentrations),
            'std': np.std(concentrations),
            'min': np.min(concentrations),
            'max': np.max(concentrations),
            'cv': np.std(concentrations) / (np.mean(concentrations) + 1e-10),
            'baseline_ratio': np.mean(concentrations) / self.parameters.baseline_concentration,
            'peak_ratio': np.max(concentrations) / self.parameters.baseline_concentration
        }


class DopamineSystem(BaseNeuromodulator):
    """Comprehensive dopamine neuromodulation system with FIXED initialization."""
    
    def __init__(self, parameters: ModulatorParameters = None, 
                 spatial_config: SpatialConfiguration = None):
        # Set dopamine-specific default parameters
        if parameters is None:
            parameters = ModulatorParameters(
                molecular_weight=153.18,  # g/mol
                diffusion_coefficient=6.0e-10,  # m²/s
                baseline_concentration=1e-9,  # FIXED: Reduced from 1e-8
                peak_concentration=1e-7,      # FIXED: Reduced from 1e-6
                tau_fast=200.0,  # ms
                tau_slow=15000.0,  # ms
                reuptake_rate=0.08,    # FIXED: Reduced from 0.15
                metabolism_rate=0.04,  # FIXED: Reduced from 0.08
                vesicle_content=50.0   # FIXED: Reduced from 100.0
            )
            
        super().__init__(ModulatorType.DOPAMINE, parameters, spatial_config)
        
        # FIXED: Dopamine-specific state variables with proper initialization
        self.reward_prediction_error = 0.0  # FIXED: Correct initial value
        self.tonic_activity = 1.0  # Baseline firing rate
        self.phasic_activity = 0.0  # Burst firing
        self.expected_reward = 0.0
        self.actual_reward = 0.0
        
        # Dopamine pathway regions
        self.regions = {
            'vta': 0.0,        # Ventral tegmental area
            'snc': 0.0,        # Substantia nigra pars compacta
            'striatum': 0.0,   # Striatum (caudate + putamen)
            'nucleus_accumbens': 0.0,  # Nucleus accumbens
            'prefrontal_cortex': 0.0,  # Prefrontal cortex
            'amygdala': 0.0    # Amygdala
        }
        
        logger.info("DopamineSystem initialized")
        
    def _initialize_receptors(self):
        """Initialize dopamine receptor populations with FIXED parameters."""
        # FIXED: D1 receptors (excitatory, Gs-coupled) with corrected parameters
        d1_params = ReceptorParameters(
            kon=1e7, koff=1e-3, kd=1e-10,  # FIXED: Better kinetic consistency
            density=800.0, efficacy=1.0,
            coupling_strength=1.2,
            hill_coefficient=1.0,
            desensitization_rate=0.001,  # FIXED: Reduced
            internalization_rate=0.0001  # FIXED: Reduced
        )
        self.receptor_populations[ReceptorSubtype.D1] = ReceptorPopulation(
            ReceptorSubtype.D1, 1000, self.spatial_config, d1_params
        )
        
        # FIXED: D2 receptors (inhibitory, Gi/o-coupled) with corrected parameters
        d2_params = ReceptorParameters(
            kon=5e6, koff=5e-4, kd=1e-10,  # FIXED: Consistent kinetics
            density=600.0, efficacy=0.8,
            coupling_strength=1.0,
            hill_coefficient=1.2,
            desensitization_rate=0.002,  # FIXED: Reduced
            internalization_rate=0.0002  # FIXED: Reduced
        )
        self.receptor_populations[ReceptorSubtype.D2] = ReceptorPopulation(
            ReceptorSubtype.D2, 800, self.spatial_config, d2_params
        )
        
        # FIXED: D3 receptors (high affinity, limbic regions) with corrected parameters
        d3_params = ReceptorParameters(
            kon=8e6, koff=2e-4, kd=2.5e-11,  # FIXED: Higher affinity, consistent kinetics
            density=200.0, efficacy=0.6,
            coupling_strength=0.8,
            hill_coefficient=1.1,
            desensitization_rate=0.0015,  # FIXED: Reduced
            internalization_rate=0.00015  # FIXED: Reduced
        )
        self.receptor_populations[ReceptorSubtype.D3] = ReceptorPopulation(
            ReceptorSubtype.D3, 300, self.spatial_config, d3_params
        )
        
    def _compute_synthesis_rate(self, **kwargs) -> float:
        """Compute dopamine synthesis rate with FIXED bounds."""
        # Base synthesis rate
        base_rate = self.parameters.synthesis_rate
        
        # Activity-dependent synthesis
        activity_factor = 1.0 + 0.5 * self.tonic_activity
        
        # Feedback inhibition from dopamine levels
        concentration_ratio = self.concentration / (self.parameters.baseline_concentration + 1e-15)
        feedback_factor = 1.0 / (1.0 + concentration_ratio)
        
        # Stress and arousal modulation
        stress_factor = kwargs.get('stress_level', 0.0)
        arousal_factor = kwargs.get('arousal_level', 0.5)
        
        modulation = (1.0 + 0.3 * arousal_factor - 0.2 * stress_factor)
        modulation = np.clip(modulation, 0.1, 5.0)  # FIXED: Bound the modulation
        
        synthesis_rate = base_rate * activity_factor * feedback_factor * modulation
        return np.clip(synthesis_rate, 0.0, base_rate * 10.0)  # FIXED: Bound output
        
    def _compute_release_rate(self, **kwargs) -> float:
        """Compute dopamine release rate with FIXED scaling."""
        # Phasic release for positive RPE - FIXED: Scaled down
        phasic_release = max(0.0, self.reward_prediction_error) * 0.5  # FIXED: Reduced from 2.0
        
        # Tonic release baseline - FIXED: Scaled down
        tonic_release = self.tonic_activity * 0.02  # FIXED: Reduced from 0.1
        
        # Activity-dependent release - FIXED: Scaled down
        neural_activity = kwargs.get('neural_activity', 0.0)
        activity_release = neural_activity * 0.01  # FIXED: Reduced from 0.05
        
        total_release = tonic_release + phasic_release + activity_release
        return np.clip(total_release, 0.0, self.parameters.synthesis_rate * 5.0)  # FIXED: Bound output
        
    def _compute_modulation_effects(self) -> Dict[str, float]:
        """Compute dopamine's effects with FIXED bounds and error handling."""
        effects = {}
        
        try:
            # Get receptor occupancies with error handling
            d1_occupancy = 0.0
            d2_occupancy = 0.0
            d3_occupancy = 0.0
            
            if ReceptorSubtype.D1 in self.receptor_populations:
                d1_result = self.receptor_populations[ReceptorSubtype.D1].population_states
                d1_occupancy = d1_result.get('mean_occupancy', 0.0)
                
            if ReceptorSubtype.D2 in self.receptor_populations:
                d2_result = self.receptor_populations[ReceptorSubtype.D2].population_states
                d2_occupancy = d2_result.get('mean_occupancy', 0.0)
                
            if ReceptorSubtype.D3 in self.receptor_populations:
                d3_result = self.receptor_populations[ReceptorSubtype.D3].population_states
                d3_occupancy = d3_result.get('mean_occupancy', 0.0)
                
            # FIXED: Ensure occupancies are valid numbers
            d1_occupancy = float(np.clip(d1_occupancy, 0.0, 1.0))
            d2_occupancy = float(np.clip(d2_occupancy, 0.0, 1.0))
            d3_occupancy = float(np.clip(d3_occupancy, 0.0, 1.0))
            
            # FIXED: D1 effects (facilitatory) with bounds
            effects[ModulationTarget.SYNAPTIC_WEIGHT.value] = np.clip(1.0 + 0.4 * d1_occupancy, 0.5, 2.0)
            effects[ModulationTarget.PLASTICITY_RATE.value] = np.clip(1.0 + 1.5 * d1_occupancy, 0.5, 3.0)
            effects[ModulationTarget.FIRING_THRESHOLD.value] = np.clip(1.0 - 0.1 * d1_occupancy, 0.8, 1.2)
            
            # FIXED: D2 effects (inhibitory) with bounds
            effects[ModulationTarget.MEMBRANE_RESISTANCE.value] = np.clip(1.0 - 0.2 * d2_occupancy, 0.5, 1.5)
            effects[ModulationTarget.NOISE_LEVEL.value] = np.clip(1.0 - 0.3 * d2_occupancy, 0.3, 1.0)
            
            # FIXED: D3 effects (limbic modulation) with bounds
            effects['motivation_level'] = np.clip(1.0 + 0.8 * d3_occupancy, 0.5, 2.0)
            effects['reward_sensitivity'] = np.clip(1.0 + 0.6 * d3_occupancy, 0.5, 2.0)
            
            # FIXED: Working memory enhancement with inverted-U curve
            total_occupancy = d1_occupancy + d2_occupancy + d3_occupancy
            if total_occupancy <= 0.5:
                wm_gain = 1.0 + total_occupancy
            else:
                wm_gain = 1.5 - 0.5 * (total_occupancy - 0.5)
            effects['working_memory_gain'] = np.clip(wm_gain, 0.5, 2.0)
            
            # FIXED: Motor facilitation with balance
            motor_effect = d1_occupancy - 0.5 * d2_occupancy
            effects['motor_facilitation'] = np.clip(1.0 + 0.6 * motor_effect, 0.3, 2.0)
            
        except Exception as e:
            logger.warning(f"Error computing dopamine modulation effects: {e}")
            # FIXED: Return safe default values
            effects = {
                ModulationTarget.SYNAPTIC_WEIGHT.value: 1.0,
                ModulationTarget.PLASTICITY_RATE.value: 1.0,
                ModulationTarget.FIRING_THRESHOLD.value: 1.0,
                ModulationTarget.MEMBRANE_RESISTANCE.value: 1.0,
                ModulationTarget.NOISE_LEVEL.value: 1.0,
                'motivation_level': 1.0,
                'reward_sensitivity': 1.0,
                'working_memory_gain': 1.0,
                'motor_facilitation': 1.0
            }
            
        return effects
        
    def update_reward_prediction_error(self, predicted_reward: float, 
                                     actual_reward: float) -> float:
        """Update reward prediction error with FIXED bounds and precision."""
        # FIXED: Input validation
        predicted_reward = np.clip(float(predicted_reward), 0.0, 1.0)
        actual_reward = np.clip(float(actual_reward), 0.0, 1.0)
        
        self.expected_reward = predicted_reward
        self.actual_reward = actual_reward
        self.reward_prediction_error = actual_reward - predicted_reward
        
        # FIXED: Clamp RPE to reasonable bounds
        self.reward_prediction_error = np.clip(self.reward_prediction_error, -1.0, 1.0)
        
        # Update phasic activity based on RPE with FIXED bounds
        if self.reward_prediction_error > 0:
            # Positive RPE -> burst firing
            self.phasic_activity = np.clip(1.0 + 2.0 * self.reward_prediction_error, 1.0, 3.0)
        elif self.reward_prediction_error < 0:
            # Negative RPE -> pause in firing
            self.phasic_activity = np.clip(1.0 + self.reward_prediction_error, 0.1, 1.0)
        else:
            # No RPE -> baseline activity
            self.phasic_activity = 1.0
            
        logger.debug(f"RPE updated: {self.reward_prediction_error:.6f}, "
                    f"phasic activity: {self.phasic_activity:.6f}")
                    
        return float(self.reward_prediction_error)  # FIXED: Ensure float return
        
    def modulate_working_memory(self, memory_load: float, 
                              task_difficulty: float) -> Dict[str, float]:
        """Modulate working memory based on dopamine levels.
        
        Args:
            memory_load: Current working memory load (0-1)
            task_difficulty: Task difficulty level (0-1)
            
        Returns:
            Working memory modulation parameters
        """
        # Inverted-U curve for dopamine and working memory
        optimal_da = 0.5 * self.parameters.peak_concentration
        current_ratio = self.concentration / optimal_da
        
        # Performance follows inverted-U
        if current_ratio <= 1.0:
            performance = current_ratio
        else:
            performance = 2.0 - current_ratio
            
        performance = np.clip(performance, 0.0, 1.0)
        
        # Load and difficulty adjustments
        load_factor = 1.0 - 0.3 * memory_load
        difficulty_factor = 1.0 - 0.2 * task_difficulty
        
        working_memory_gain = performance * load_factor * difficulty_factor
        
        return {
            'working_memory_gain': working_memory_gain,
            'attention_focus': performance * 0.8,
            'cognitive_flexibility': (1.0 - abs(current_ratio - 1.0)) * 0.6,
            'response_inhibition': performance * 0.7
        }
        
    def get_pathway_activities(self) -> Dict[str, float]:
        """Get current activities in different dopamine pathways."""
        # Simplified pathway activity computation
        base_activity = self.concentration / self.parameters.baseline_concentration
        
        return {
            'mesolimbic': base_activity * (1.0 + 0.5 * self.reward_prediction_error),
            'mesocortical': base_activity * (1.0 + 0.3 * abs(self.reward_prediction_error)),
            'nigrostriatal': base_activity * self.tonic_activity,
            'tuberoinfundibular': base_activity * 0.5  # Lower activity in this pathway
        }


class SerotoninSystem(BaseNeuromodulator):
    """Comprehensive serotonin (5-HT) neuromodulation system with FIXED parameters."""
    
    def __init__(self, parameters: ModulatorParameters = None,
                 spatial_config: SpatialConfiguration = None):
        if parameters is None:
            parameters = ModulatorParameters(
                molecular_weight=176.22,  # g/mol
                diffusion_coefficient=5.5e-10,  # m²/s
                baseline_concentration=2e-10,  # FIXED: Reduced from 5e-9
                peak_concentration=2e-8,       # FIXED: Reduced from 5e-7
                tau_fast=500.0,  # ms
                tau_slow=30000.0,  # ms
                reuptake_rate=0.06,    # FIXED: Reduced from 0.12
                metabolism_rate=0.03,  # FIXED: Reduced from 0.06
                vesicle_content=25.0   # FIXED: Reduced from default
            )
            
        super().__init__(ModulatorType.SEROTONIN, parameters, spatial_config)
        
        # FIXED: Serotonin-specific state variables with proper bounds
        self.mood_state = 0.5  # 0 = depressed, 1 = elevated
        self.circadian_phase = 0.0  # 0-2π
        self.stress_level = 0.0
        self.social_context = 0.5
        self.impulse_control_strength = 1.0
        
        # Raphe nuclei activity
        self.dorsal_raphe_activity = 1.0
        self.median_raphe_activity = 1.0
        
        # FIXED: Add temporal tracking
        self.last_mood_update = 0.0
        self.circadian_amplitude = 0.3  # FIXED: Limit circadian effects
        
        logger.info("SerotoninSystem initialized")
        
    def _initialize_receptors(self):
        """Initialize serotonin receptor populations with FIXED parameters."""
        # FIXED: 5-HT1A receptors with better kinetics
        htr1a_params = ReceptorParameters(
            kon=2e7, koff=2e-3, kd=1e-10,  # FIXED: Consistent kinetics
            density=1200.0, efficacy=0.9,
            coupling_strength=1.1,
            hill_coefficient=1.0,
            desensitization_rate=0.001,   # FIXED: Reduced
            internalization_rate=0.0001  # FIXED: Reduced
        )
        self.receptor_populations[ReceptorSubtype.HTR1A] = ReceptorPopulation(
            ReceptorSubtype.HTR1A, 1200, self.spatial_config, htr1a_params
        )
        
        # FIXED: 5-HT2A receptors with better kinetics
        htr2a_params = ReceptorParameters(
            kon=1.5e7, koff=1.5e-3, kd=1e-10,  # FIXED: Consistent kinetics
            density=800.0, efficacy=1.0,
            coupling_strength=1.3,
            hill_coefficient=1.2,
            desensitization_rate=0.0015,  # FIXED: Reduced
            internalization_rate=0.00015  # FIXED: Reduced
        )
        self.receptor_populations[ReceptorSubtype.HTR2A] = ReceptorPopulation(
            ReceptorSubtype.HTR2A, 800, self.spatial_config, htr2a_params
        )
        
        # FIXED: 5-HT2C receptors with better kinetics
        htr2c_params = ReceptorParameters(
            kon=1.2e7, koff=1.2e-3, kd=1e-10,  # FIXED: Consistent kinetics
            density=600.0, efficacy=0.8,
            coupling_strength=1.0,
            hill_coefficient=1.1,
            desensitization_rate=0.0012,  # FIXED: Reduced
            internalization_rate=0.00012  # FIXED: Reduced
        )
        self.receptor_populations[ReceptorSubtype.HTR2C] = ReceptorPopulation(
            ReceptorSubtype.HTR2C, 600, self.spatial_config, htr2c_params
        )
        
        # FIXED: 5-HT3 receptors with better kinetics
        htr3_params = ReceptorParameters(
            kon=3e7, koff=3e-3, kd=1e-10,  # FIXED: Consistent kinetics
            density=300.0, efficacy=1.0,
            coupling_strength=2.0,  # Fast ionotropic response
            hill_coefficient=2.0,    # Cooperative binding
            desensitization_rate=0.005,  # FIXED: Reduced from original fast desensitization
            internalization_rate=0.0005  # FIXED: Reduced
        )
        self.receptor_populations[ReceptorSubtype.HTR3] = ReceptorPopulation(
            ReceptorSubtype.HTR3, 300, self.spatial_config, htr3_params
        )
        
    def _compute_synthesis_rate(self, **kwargs) -> float:
        """Compute serotonin synthesis rate with FIXED bounds."""
        base_rate = self.parameters.synthesis_rate
        
        # FIXED: Circadian modulation with bounds
        circadian_factor = 1.0 + self.circadian_amplitude * np.cos(self.circadian_phase)
        circadian_factor = np.clip(circadian_factor, 0.5, 1.5)
        
        # FIXED: Stress modulation with proper scaling
        if self.stress_level < 0.5:
            stress_factor = 1.0 + 0.1 * self.stress_level  # FIXED: Reduced from 0.2
        else:
            stress_factor = 1.05 - 0.15 * (self.stress_level - 0.5)  # FIXED: Reduced from 0.3
        stress_factor = np.clip(stress_factor, 0.5, 1.3)
            
        # FIXED: Social context modulation with bounds
        social_factor = 1.0 + 0.05 * self.social_context  # FIXED: Reduced from 0.1
        social_factor = np.clip(social_factor, 0.9, 1.1)
        
        # Tryptophan availability (simplified)
        tryptophan_factor = kwargs.get('tryptophan_level', 1.0)
        tryptophan_factor = np.clip(tryptophan_factor, 0.5, 2.0)
        
        # FIXED: Feedback inhibition with better regulation
        concentration_ratio = self.concentration / (self.parameters.baseline_concentration + 1e-15)
        feedback_factor = 1.0 / (1.0 + 0.8 * concentration_ratio)  # FIXED: Stronger feedback
        
        synthesis_rate = (base_rate * circadian_factor * stress_factor * 
                         social_factor * tryptophan_factor * feedback_factor)
        
        return np.clip(synthesis_rate, 0.0, base_rate * 3.0)  # FIXED: Bound output
                
    def _compute_release_rate(self, **kwargs) -> float:
        """Compute serotonin release rate with FIXED scaling."""
        # FIXED: Baseline release from raphe activity with scaling
        dorsal_release = self.dorsal_raphe_activity * 0.03  # FIXED: Reduced from 0.15
        median_release = self.median_raphe_activity * 0.02  # FIXED: Reduced from 0.10
        
        # FIXED: Stress-induced release with scaling
        stress_release = self.stress_level * 0.02  # FIXED: Reduced from 0.08
        
        # FIXED: Circadian modulation with scaling
        circadian_release = 0.01 * (1.0 + 0.2 * np.sin(self.circadian_phase))  # FIXED: Reduced from 0.05
        
        # FIXED: Activity-dependent release with scaling
        neural_activity = kwargs.get('neural_activity', 0.0)
        activity_release = neural_activity * 0.008  # FIXED: Reduced from 0.03
        
        total_release = dorsal_release + median_release + stress_release + circadian_release + activity_release
        return np.clip(total_release, 0.0, self.parameters.synthesis_rate * 2.0)  # FIXED: Bound output
        
    def _compute_modulation_effects(self) -> Dict[str, float]:
        """Compute serotonin's effects with FIXED bounds and error handling."""
        effects = {}
        
        try:
            # Get receptor occupancies with error handling
            htr1a_occupancy = 0.0
            htr2a_occupancy = 0.0
            htr2c_occupancy = 0.0
            htr3_occupancy = 0.0
            
            if ReceptorSubtype.HTR1A in self.receptor_populations:
                htr1a_occupancy = self.receptor_populations[ReceptorSubtype.HTR1A].population_states.get('mean_occupancy', 0.0)
            if ReceptorSubtype.HTR2A in self.receptor_populations:
                htr2a_occupancy = self.receptor_populations[ReceptorSubtype.HTR2A].population_states.get('mean_occupancy', 0.0)
            if ReceptorSubtype.HTR2C in self.receptor_populations:
                htr2c_occupancy = self.receptor_populations[ReceptorSubtype.HTR2C].population_states.get('mean_occupancy', 0.0)
            if ReceptorSubtype.HTR3 in self.receptor_populations:
                htr3_occupancy = self.receptor_populations[ReceptorSubtype.HTR3].population_states.get('mean_occupancy', 0.0)
                
            # FIXED: Ensure occupancies are valid numbers
            htr1a_occupancy = float(np.clip(htr1a_occupancy, 0.0, 1.0))
            htr2a_occupancy = float(np.clip(htr2a_occupancy, 0.0, 1.0))
            htr2c_occupancy = float(np.clip(htr2c_occupancy, 0.0, 1.0))
            htr3_occupancy = float(np.clip(htr3_occupancy, 0.0, 1.0))
            
            # FIXED: 5-HT1A effects (inhibitory, anxiolytic) with bounds
            effects[ModulationTarget.INHIBITION_STRENGTH.value] = np.clip(1.0 + 0.3 * htr1a_occupancy, 0.5, 1.8)
            effects[ModulationTarget.NOISE_LEVEL.value] = np.clip(1.0 - 0.2 * htr1a_occupancy, 0.5, 1.0)
            effects['anxiety_level'] = np.clip(1.0 - 0.4 * htr1a_occupancy, 0.3, 1.0)
            
            # FIXED: 5-HT2A effects (excitatory, perceptual) with bounds
            effects[ModulationTarget.SYNAPTIC_WEIGHT.value] = np.clip(1.0 + 0.2 * htr2a_occupancy, 0.7, 1.5)
            effects['sensory_gain'] = np.clip(1.0 + 0.5 * htr2a_occupancy, 0.8, 1.8)
            effects['perceptual_sensitivity'] = np.clip(1.0 + 0.4 * htr2a_occupancy, 0.8, 1.6)
            
            # FIXED: 5-HT2C effects (mood and appetite) with bounds
            effects['mood_regulation'] = np.clip(1.0 + 0.3 * htr2c_occupancy, 0.7, 1.5)
            effects['appetite_suppression'] = np.clip(1.0 + 0.2 * htr2c_occupancy, 0.8, 1.4)
            effects[ModulationTarget.PLASTICITY_RATE.value] = np.clip(1.0 + 0.15 * htr2c_occupancy, 0.8, 1.3)
            
            # FIXED: 5-HT3 effects (fast excitation, nausea) with bounds
            effects['fast_excitation'] = np.clip(1.0 + 0.8 * htr3_occupancy, 0.5, 2.0)
            effects['nausea_sensitivity'] = np.clip(1.0 + 0.5 * htr3_occupancy, 0.8, 1.8)
            
            # FIXED: Overall serotonin effects with bounds
            total_occupancy = htr1a_occupancy + htr2a_occupancy + htr2c_occupancy + htr3_occupancy
            effects['impulse_control'] = np.clip(1.0 + 0.25 * total_occupancy, 0.7, 1.5)
            effects['social_behavior'] = np.clip(1.0 + 0.2 * total_occupancy, 0.8, 1.4)
            effects['sleep_pressure'] = np.clip(1.0 + 0.3 * htr1a_occupancy, 0.7, 1.5)
            
        except Exception as e:
            logger.warning(f"Error computing serotonin modulation effects: {e}")
            # FIXED: Return safe default values
            effects = {
                ModulationTarget.INHIBITION_STRENGTH.value: 1.0,
                ModulationTarget.NOISE_LEVEL.value: 1.0,
                ModulationTarget.SYNAPTIC_WEIGHT.value: 1.0,
                ModulationTarget.PLASTICITY_RATE.value: 1.0,
                'anxiety_level': 1.0,
                'sensory_gain': 1.0,
                'perceptual_sensitivity': 1.0,
                'mood_regulation': 1.0,
                'appetite_suppression': 1.0,
                'fast_excitation': 1.0,
                'nausea_sensitivity': 1.0,
                'impulse_control': 1.0,
                'social_behavior': 1.0,
                'sleep_pressure': 1.0
            }
            
        return effects
        
    def update_circadian_cycle(self, time_of_day: float):
        """Update circadian phase with FIXED bounds."""
        # FIXED: Input validation
        time_of_day = np.clip(float(time_of_day), 0.0, 24.0)
        
        # Convert to radians (0 = midnight, π = noon)
        self.circadian_phase = 2 * np.pi * (time_of_day % 24) / 24
        
        # FIXED: Update raphe activity with bounds
        # Higher activity during wake period (reduced amplitude)
        wake_activity = 0.5 * (1.0 + np.cos(self.circadian_phase - np.pi))
        wake_activity = np.clip(wake_activity, 0.0, 1.0)
        
        self.dorsal_raphe_activity = np.clip(0.6 + 0.3 * wake_activity, 0.3, 1.0)  # FIXED: Reduced range
        self.median_raphe_activity = np.clip(0.7 + 0.2 * wake_activity, 0.5, 1.0)  # FIXED: Reduced range
        
    def modulate_mood(self, positive_events: float = 0.0, 
                     negative_events: float = 0.0,
                     social_interaction: float = 0.0) -> Dict[str, float]:
        """Modulate mood state with FIXED bounds and temporal dynamics."""
        # FIXED: Input validation
        positive_events = np.clip(float(positive_events), 0.0, 1.0)
        negative_events = np.clip(float(negative_events), 0.0, 1.0)
        social_interaction = np.clip(float(social_interaction), 0.0, 1.0)
        
        # FIXED: Update mood state with temporal smoothing
        current_time = time.time()
        dt = current_time - self.last_mood_update if self.last_mood_update > 0 else 0.1
        dt = np.clip(dt, 0.01, 10.0)  # Reasonable time bounds
        
        # FIXED: Mood change with reduced sensitivity
        mood_change = 0.05 * (positive_events - negative_events) + 0.025 * social_interaction
        mood_change = np.clip(mood_change, -0.2, 0.2)  # FIXED: Limit change rate
        
        # FIXED: Apply temporal dynamics
        mood_tau = 60.0  # Time constant in seconds
        alpha = dt / (mood_tau + dt)
        target_mood = self.mood_state + mood_change
        self.mood_state = self.mood_state + alpha * (target_mood - self.mood_state)
        self.mood_state = np.clip(self.mood_state, 0.0, 1.0)
        
        # Update social context with smoothing
        self.social_context = 0.95 * self.social_context + 0.05 * social_interaction
        self.social_context = np.clip(self.social_context, 0.0, 1.0)
        
        self.last_mood_update = current_time
        
        # FIXED: Compute mood-dependent effects with bounds
        mood_effects = {
            'emotional_reactivity': np.clip(0.6 + 0.4 * (1.0 - self.mood_state), 0.2, 1.0),
            'cognitive_bias': np.clip(-0.3 + 0.6 * self.mood_state, -0.5, 0.5),
            'social_approach': np.clip(self.mood_state * self.social_context, 0.0, 1.0),
            'reward_sensitivity': np.clip(0.4 + 0.5 * self.mood_state, 0.2, 1.0),
            'stress_vulnerability': np.clip(1.0 - 0.4 * self.mood_state, 0.3, 1.0),
            'mood_state': float(self.mood_state),
            'social_context': float(self.social_context)
        }
        
        return mood_effects
        
    def modulate_impulse_control(self, temptation_strength: float,
                               cognitive_load: float) -> Dict[str, float]:
        """Modulate impulse control with FIXED bounds."""
        # FIXED: Input validation
        temptation_strength = np.clip(float(temptation_strength), 0.0, 1.0)
        cognitive_load = np.clip(float(cognitive_load), 0.0, 1.0)
        
        # FIXED: Serotonin enhances impulse control with bounds
        serotonin_factor = self.concentration / (self.parameters.baseline_concentration + 1e-15)
        serotonin_factor = np.clip(serotonin_factor, 0.1, 10.0)
        
        base_control = 0.5 + 0.3 * np.tanh(serotonin_factor - 1.0)  # FIXED: Reduced sensitivity
        base_control = np.clip(base_control, 0.2, 0.9)
        
        # FIXED: Cognitive load reduces control with bounds
        load_penalty = 0.2 * cognitive_load  # FIXED: Reduced from 0.3
        
        # FIXED: Stronger temptations harder to resist with bounds
        temptation_challenge = 0.25 * temptation_strength  # FIXED: Reduced from 0.4
        
        # Final impulse control strength
        control_strength = base_control - load_penalty - temptation_challenge
        control_strength = np.clip(control_strength, 0.1, 1.0)
        self.impulse_control_strength = control_strength
        
        return {
            'impulse_control_strength': float(control_strength),
            'behavioral_inhibition': float(control_strength * 0.8),
            'delay_discounting': float(np.clip(1.0 - control_strength * 0.4, 0.3, 1.0)),
            'risk_assessment': float(control_strength * 0.6),
            'emotional_regulation': float(control_strength * 0.7),
            'temptation_resistance': float(np.clip(control_strength / (temptation_strength + 0.1), 0.1, 2.0))
        }
        
    def get_sleep_wake_regulation(self) -> Dict[str, float]:
        """Get sleep-wake regulation parameters with FIXED bounds."""
        # FIXED: Serotonin promotes wakefulness with bounds
        wake_promotion = self.concentration / (self.parameters.baseline_concentration + 1e-15)
        wake_promotion = np.clip(wake_promotion, 0.1, 5.0)
        
        # FIXED: Circadian influence with bounds
        circadian_wake = 0.5 * (1.0 + np.cos(self.circadian_phase))
        circadian_wake = np.clip(circadian_wake, 0.2, 0.8)
        
        # FIXED: Combined wake promotion with bounds
        combined_wake = wake_promotion * circadian_wake
        combined_wake = np.clip(combined_wake, 0.1, 2.0)
        
        return {
            'wake_promotion': float(combined_wake),
            'rem_sleep_regulation': float(np.clip(wake_promotion * 0.4, 0.2, 1.5)),
            'sleep_depth_modulation': float(np.clip(1.0 - combined_wake * 0.3, 0.3, 1.0)),
            'circadian_phase': float(self.circadian_phase),
            'sleep_pressure': float(np.clip(1.0 - combined_wake, 0.0, 1.0)),
            'raphe_activity': float((self.dorsal_raphe_activity + self.median_raphe_activity) / 2.0)
        }


class NorepinephrineSystem(BaseNeuromodulator):
    """Comprehensive norepinephrine system with FIXED temporal dynamics."""
    
    def __init__(self, parameters: ModulatorParameters = None,
                 spatial_config: SpatialConfiguration = None):
        if parameters is None:
            parameters = ModulatorParameters(
                molecular_weight=169.18,  # g/mol
                diffusion_coefficient=5.8e-10,  # m²/s
                baseline_concentration=2e-10,  # FIXED: Reduced from 2e-9
                peak_concentration=2e-8,       # FIXED: Reduced from 2e-7
                tau_fast=300.0,  # ms
                tau_slow=20000.0,  # ms
                reuptake_rate=0.09,    # FIXED: Reduced from 0.18
                metabolism_rate=0.05,  # FIXED: Reduced from 0.10
                vesicle_content=30.0   # FIXED: Reduced from default
            )
            
        super().__init__(ModulatorType.NOREPINEPHRINE, parameters, spatial_config)
        
        # FIXED: Norepinephrine-specific state variables with better initialization
        self.arousal_level = 0.5  # 0 = low arousal, 1 = high arousal
        self.stress_level = 0.0
        self.attention_focus = 0.5
        self.threat_detection = 0.0
        
        # Locus coeruleus activity (main NE source)
        self.locus_coeruleus_tonic = 1.0  # Baseline activity
        self.locus_coeruleus_phasic = 0.0  # Event-related bursts
        
        # Autonomic state
        self.sympathetic_tone = 0.5
        
        # FIXED: Add temporal dynamics tracking
        self.concentration_peak_time = 0.0
        self.last_stress_trigger = 0.0
        self.stress_response_active = False
        
        logger.info("NorepinephrineSystem initialized")
        
    def _initialize_receptors(self):
        """Initialize adrenergic receptor populations with FIXED parameters."""
        # FIXED: α1A receptors with better parameters
        alpha1a_params = ReceptorParameters(
            kon=2.5e7, koff=2.5e-3, kd=1e-10,  # FIXED: Consistent kinetics
            density=600.0, efficacy=1.0,
            coupling_strength=1.4,
            hill_coefficient=1.0,
            desensitization_rate=0.002,   # FIXED: Reduced
            internalization_rate=0.0002  # FIXED: Reduced
        )
        self.receptor_populations[ReceptorSubtype.ALPHA1A] = ReceptorPopulation(
            ReceptorSubtype.ALPHA1A, 600, self.spatial_config, alpha1a_params
        )
        
        # FIXED: α2A receptors with better parameters
        alpha2a_params = ReceptorParameters(
            kon=1.8e7, koff=1.8e-3, kd=1e-10,  # FIXED: Consistent kinetics
            density=800.0, efficacy=0.8,
            coupling_strength=1.0,
            hill_coefficient=1.0,
            desensitization_rate=0.001,   # FIXED: Reduced
            internalization_rate=0.0001  # FIXED: Reduced
        )
        self.receptor_populations[ReceptorSubtype.ALPHA2A] = ReceptorPopulation(
            ReceptorSubtype.ALPHA2A, 800, self.spatial_config, alpha2a_params
        )
        
        # FIXED: β1 receptors with better parameters
        beta1_params = ReceptorParameters(
            kon=3e7, koff=3e-3, kd=1e-10,  # FIXED: Consistent kinetics
            density=400.0, efficacy=1.0,
            coupling_strength=1.6,
            hill_coefficient=1.0,
            desensitization_rate=0.003,   # FIXED: Reduced
            internalization_rate=0.0003  # FIXED: Reduced
        )
        self.receptor_populations[ReceptorSubtype.BETA1] = ReceptorPopulation(
            ReceptorSubtype.BETA1, 400, self.spatial_config, beta1_params
        )
        
        # FIXED: β2 receptors with better parameters
        beta2_params = ReceptorParameters(
            kon=2.2e7, koff=2.2e-3, kd=1e-10,  # FIXED: Consistent kinetics
            density=500.0, efficacy=0.9,
            coupling_strength=1.2,
            hill_coefficient=1.1,
            desensitization_rate=0.0025,  # FIXED: Reduced
            internalization_rate=0.00025  # FIXED: Reduced
        )
        self.receptor_populations[ReceptorSubtype.BETA2] = ReceptorPopulation(
            ReceptorSubtype.BETA2, 500, self.spatial_config, beta2_params
        )
        
    def trigger_stress_response(self, stressor_magnitude: float,
                              stressor_duration: float,
                              coping_resources: float = 0.5) -> Dict[str, float]:
        """Trigger stress response with FIXED temporal dynamics."""
        # FIXED: Input validation
        stressor_magnitude = np.clip(float(stressor_magnitude), 0.0, 1.0)
        stressor_duration = np.clip(float(stressor_duration), 0.0, 3600.0)  # Max 1 hour
        coping_resources = np.clip(float(coping_resources), 0.0, 1.0)
        
        current_time = time.time()
        self.last_stress_trigger = current_time
        self.stress_response_active = True
        
        # FIXED: Acute vs chronic stress dynamics with proper scaling
        if stressor_duration < 300:  # Less than 5 minutes = acute
            stress_multiplier = 1.0 + 1.5 * stressor_magnitude  # FIXED: Reduced from 2.0
        else:  # Chronic stress
            decay_factor = max(0.3, 1.0 - 0.2 * np.log(stressor_duration / 300))
            stress_multiplier = 1.0 + 0.8 * stressor_magnitude * decay_factor  # FIXED: Reduced
            
        # Coping modulation
        effective_stress = stress_multiplier / (1.0 + coping_resources)
        
        # FIXED: Update stress level with bounds
        self.stress_level = np.clip(effective_stress * 0.5, 0.0, 1.0)  # FIXED: Scale down
        
        # FIXED: Update threat detection with bounds
        self.threat_detection = np.clip(0.6 * stressor_magnitude + 0.2 * self.stress_level, 0.0, 1.0)
        
        # FIXED: Update sympathetic tone with bounds
        self.sympathetic_tone = np.clip(0.3 + 0.4 * self.stress_level, 0.0, 1.0)  # FIXED: Better scaling
        
        # FIXED: LC phasic response with proper timing
        if stressor_duration < 60:  # Very acute stress
            self.locus_coeruleus_phasic = np.clip(1.0 + 1.5 * stressor_magnitude, 1.0, 2.5)
        else:
            self.locus_coeruleus_phasic = np.clip(1.0 + 0.8 * stressor_magnitude, 1.0, 2.0)
            
        # FIXED: Trigger concentration increase with proper scaling
        if len(self.release_sites) > 0:
            # Calculate release based on stress level
            release_intensity = min(0.8, self.stress_level * 1.2)
            for site_id in range(len(self.release_sites)):
                released = self.trigger_release(site_id, release_intensity)
                logger.debug(f"Stress-triggered release: {released:.2e} at site {site_id}")
            
        return {
            'stress_level': float(self.stress_level),
            'threat_detection': float(self.threat_detection),
            'sympathetic_tone': float(self.sympathetic_tone),
            'fight_or_flight': float(self.stress_level * self.sympathetic_tone),
            'stress_resilience': float(coping_resources / (self.stress_level + 0.1)),
            'recovery_time': float(stressor_duration * (1.0 - coping_resources) * 2.0),
            'lc_phasic_response': float(self.locus_coeruleus_phasic),
            'response_magnitude': float(effective_stress)
        }
        
    def _compute_synthesis_rate(self, **kwargs) -> float:
        """Compute norepinephrine synthesis with FIXED bounds."""
        base_rate = self.parameters.synthesis_rate
        
        # Activity-dependent synthesis
        lc_activity_factor = 1.0 + 0.4 * self.locus_coeruleus_tonic
        
        # Stress-induced synthesis - FIXED: Better modulation
        stress_factor = 1.0 + 0.5 * self.stress_level  # FIXED: Reduced from 0.6
        
        # Circadian modulation (higher during active period)
        circadian_phase = kwargs.get('circadian_phase', 0.0)
        circadian_factor = 1.0 + 0.2 * np.cos(circadian_phase - np.pi/2)
        
        # Feedback regulation - FIXED: Better feedback
        concentration_ratio = self.concentration / (self.parameters.baseline_concentration + 1e-15)
        feedback_factor = 1.0 / (1.0 + 1.5 * concentration_ratio)  # FIXED: Stronger feedback
        
        synthesis_rate = base_rate * lc_activity_factor * stress_factor * circadian_factor * feedback_factor
        return np.clip(synthesis_rate, 0.0, base_rate * 5.0)  # FIXED: Bound output
        
    def _compute_release_rate(self, **kwargs) -> float:
        """Compute norepinephrine release with FIXED scaling."""
        # FIXED: Tonic release from LC with proper scaling
        tonic_release = self.locus_coeruleus_tonic * 0.03  # FIXED: Reduced from 0.12
        
        # FIXED: Phasic release from LC bursts with proper scaling
        phasic_release = self.locus_coeruleus_phasic * 0.05  # FIXED: Reduced from 0.25
        
        # FIXED: Sympathetic terminal release with proper scaling
        sympathetic_release = self.sympathetic_tone * 0.02  # FIXED: Reduced from 0.08
        
        # FIXED: Stress-induced release with proper scaling
        stress_release = self.stress_level * 0.04  # FIXED: Reduced from 0.15
        
        # FIXED: Threat-induced release with proper scaling
        threat_release = self.threat_detection * 0.05  # FIXED: Reduced from 0.20
        
        total_release = tonic_release + phasic_release + sympathetic_release + stress_release + threat_release
        return np.clip(total_release, 0.0, self.parameters.synthesis_rate * 3.0)  # FIXED: Bound output
        
    def _update_temporal_dynamics(self, dt: float):
        """FIXED: Update temporal dynamics of NE system."""
        current_time = time.time()
        
        # FIXED: Decay phasic activity over time
        if self.locus_coeruleus_phasic > 1.0:
            decay_rate = 0.1  # Decay constant
            self.locus_coeruleus_phasic = max(1.0, self.locus_coeruleus_phasic - decay_rate * dt)
            
        # FIXED: Recovery from stress response
        if self.stress_response_active:
            time_since_trigger = current_time - self.last_stress_trigger
            
            # Stress level decays exponentially
            if time_since_trigger > 10.0:  # Start recovery after 10 seconds
                recovery_rate = 0.05  # Recovery time constant
                recovery_factor = np.exp(-recovery_rate * (time_since_trigger - 10.0))
                self.stress_level *= recovery_factor
                
                # End stress response when stress level is low
                if self.stress_level < 0.1:
                    self.stress_response_active = False
                    
        # FIXED: Update arousal based on current state
        target_arousal = 0.3 + 0.4 * self.stress_level + 0.2 * (self.locus_coeruleus_phasic - 1.0)
        arousal_tau = 5.0  # Time constant for arousal changes
        self.arousal_level += (target_arousal - self.arousal_level) * dt / arousal_tau
        self.arousal_level = np.clip(self.arousal_level, 0.0, 1.0)
        
    def update_concentration(self, dt: float, 
                           release_events: List[Tuple[int, float]] = None,
                           synthesis_modulation: float = 1.0,
                           uptake_modulation: float = 1.0) -> Dict[str, Any]:
        """Update concentration with FIXED temporal dynamics."""
        # FIXED: Update temporal dynamics first
        self._update_temporal_dynamics(dt)
        
        # Call parent update method
        result = super().update_concentration(dt, release_events, synthesis_modulation, uptake_modulation)
        
        # FIXED: Add temporal information to result
        result['temporal_dynamics'] = {
            'arousal_level': float(self.arousal_level),
            'stress_level': float(self.stress_level),
            'lc_tonic': float(self.locus_coeruleus_tonic),
            'lc_phasic': float(self.locus_coeruleus_phasic),
            'sympathetic_tone': float(self.sympathetic_tone),
            'stress_response_active': bool(self.stress_response_active),
            'time_since_last_stress': float(time.time() - self.last_stress_trigger) if self.last_stress_trigger > 0 else 0.0
        }
        
        return result
        
    def _compute_modulation_effects(self) -> Dict[str, float]:
        """Compute norepinephrine's effects on neural parameters."""
        effects = {}
        
        # Get receptor occupancies
        alpha1a_occupancy = 0.0
        alpha2a_occupancy = 0.0
        beta1_occupancy = 0.0
        beta2_occupancy = 0.0
        
        if ReceptorSubtype.ALPHA1A in self.receptor_populations:
            alpha1a_occupancy = self.receptor_populations[ReceptorSubtype.ALPHA1A].population_states.get('mean_occupancy', 0.0)
        if ReceptorSubtype.ALPHA2A in self.receptor_populations:
            alpha2a_occupancy = self.receptor_populations[ReceptorSubtype.ALPHA2A].population_states.get('mean_occupancy', 0.0)
        if ReceptorSubtype.BETA1 in self.receptor_populations:
            beta1_occupancy = self.receptor_populations[ReceptorSubtype.BETA1].population_states.get('mean_occupancy', 0.0)
        if ReceptorSubtype.BETA2 in self.receptor_populations:
            beta2_occupancy = self.receptor_populations[ReceptorSubtype.BETA2].population_states.get('mean_occupancy', 0.0)
            
        # α1 effects (excitatory, arousal)
        effects[ModulationTarget.SYNAPTIC_WEIGHT.value] = 1.0 + 0.5 * alpha1a_occupancy
        effects[ModulationTarget.FIRING_THRESHOLD.value] = 1.0 - 0.15 * alpha1a_occupancy
        effects['arousal_level'] = 1.0 + 0.8 * alpha1a_occupancy
        
        # α2 effects (inhibitory autoreceptors)
        effects[ModulationTarget.INHIBITION_STRENGTH.value] = 1.0 + 0.4 * alpha2a_occupancy
        effects['ne_release_inhibition'] = alpha2a_occupancy
        
        # β1 effects (cardiac and alertness)
        effects['cardiac_output'] = 1.0 + 0.6 * beta1_occupancy
        effects['alertness'] = 1.0 + 0.7 * beta1_occupancy
        
        # β2 effects (memory consolidation, metabolic)
        effects[ModulationTarget.PLASTICITY_RATE.value] = 1.0 + 0.8 * beta2_occupancy
        effects['memory_consolidation'] = 1.0 + 1.0 * beta2_occupancy
        effects['metabolic_rate'] = 1.0 + 0.4 * beta2_occupancy
        
        # Overall norepinephrine effects
        total_excitatory = alpha1a_occupancy + beta1_occupancy + beta2_occupancy
        effects['attention_focus'] = 1.0 + 0.6 * total_excitatory
        effects['vigilance'] = 1.0 + 0.8 * total_excitatory
        effects['stress_response'] = 1.0 + 0.5 * total_excitatory
        
        # Inverted-U curve for cognitive performance
        ne_ratio = self.concentration / self.parameters.baseline_concentration
        if ne_ratio <= 2.0:
            cognitive_performance = 0.5 + 0.25 * ne_ratio
        else:
            cognitive_performance = 1.0 - 0.2 * (ne_ratio - 2.0)
        effects['cognitive_performance'] = np.clip(cognitive_performance, 0.0, 1.0)
        
        return effects
        
    def update_arousal_state(self, stimulus_intensity: float,
                           stimulus_novelty: float,
                           task_demand: float) -> Dict[str, float]:
        """Update arousal state based on environmental demands.
        
        Args:
            stimulus_intensity: Intensity of current stimuli (0-1)
            stimulus_novelty: Novelty of stimuli (0-1)
            task_demand: Current task cognitive demand (0-1)
            
        Returns:
            Arousal state information
        """
        # Compute arousal from multiple sources
        stimulus_arousal = 0.4 * stimulus_intensity + 0.3 * stimulus_novelty
        task_arousal = 0.3 * task_demand
        
        # Update arousal level with temporal dynamics
        target_arousal = stimulus_arousal + task_arousal
        arousal_tau = 2.0  # seconds
        dt = 0.1  # assumed timestep
        
        self.arousal_level += (target_arousal - self.arousal_level) * dt / arousal_tau
        self.arousal_level = np.clip(self.arousal_level, 0.0, 1.0)
        
        # Update locus coeruleus activity
        self.locus_coeruleus_tonic = 0.5 + 0.5 * self.arousal_level
        
        # Phasic responses to novel/intense stimuli
        if stimulus_novelty > 0.7 or stimulus_intensity > 0.8:
            self.locus_coeruleus_phasic = min(2.0, 1.0 + stimulus_novelty + stimulus_intensity)
        else:
            self.locus_coeruleus_phasic *= 0.9  # Decay
            
        return {
            'arousal_level': self.arousal_level,
            'lc_tonic': self.locus_coeruleus_tonic,
            'lc_phasic': self.locus_coeruleus_phasic,
            'stimulus_arousal': stimulus_arousal,
            'task_arousal': task_arousal
        }
        
    def modulate_attention(self, target_salience: float,
                          distractor_strength: float,
                          cognitive_load: float) -> Dict[str, float]:
        """Modulate attention based on norepinephrine levels.
        
        Args:
            target_salience: Salience of attention target (0-1)
            distractor_strength: Strength of distractors (0-1)
            cognitive_load: Current cognitive load (0-1)
            
        Returns:
            Attention modulation effects
        """
        # NE enhances signal-to-noise ratio
        ne_factor = self.concentration / self.parameters.baseline_concentration
        
        # Signal enhancement
        signal_gain = 1.0 + 0.8 * ne_factor * target_salience
        
        # Noise suppression (including distractors)
        noise_suppression = 1.0 + 0.6 * ne_factor
        distractor_suppression = noise_suppression / (1.0 + distractor_strength)
        
        # Focus capacity (reduced by cognitive load)
        focus_capacity = (1.0 + 0.5 * ne_factor) / (1.0 + 0.3 * cognitive_load)
        
        # Attention focus strength
        self.attention_focus = signal_gain * distractor_suppression * focus_capacity
        self.attention_focus = np.clip(self.attention_focus, 0.0, 2.0)
        
        return {
            'attention_focus': self.attention_focus,
            'signal_gain': signal_gain,
            'noise_suppression': noise_suppression,
            'distractor_suppression': distractor_suppression,
            'focus_capacity': focus_capacity,
            'selective_attention': self.attention_focus * target_salience,
            'sustained_attention': self.attention_focus / (1.0 + 0.2 * cognitive_load)
        }
        
    def trigger_stress_response(self, stressor_magnitude: float,
                              stressor_duration: float,
                              coping_resources: float = 0.5) -> Dict[str, float]:
        """Trigger stress response with norepinephrine release.
        
        Args:
            stressor_magnitude: Magnitude of stressor (0-1)
            stressor_duration: Duration of stress exposure (seconds)
            coping_resources: Available coping resources (0-1)
            
        Returns:
            Stress response parameters
        """
        # Acute vs chronic stress dynamics
        if stressor_duration < 300:  # Less than 5 minutes = acute
            stress_multiplier = 1.0 + 2.0 * stressor_magnitude
        else:  # Chronic stress
            stress_multiplier = 1.0 + 1.0 * stressor_magnitude * (1.0 - 0.3 * np.log(stressor_duration / 300))
            
        # Coping modulation
        effective_stress = stress_multiplier / (1.0 + coping_resources)
        
        # Update stress level
        self.stress_level = min(1.0, effective_stress)
        
        # Update threat detection
        self.threat_detection = min(1.0, 0.8 * stressor_magnitude + 0.2 * self.stress_level)
        
        # Update sympathetic tone
        self.sympathetic_tone = min(1.0, 0.5 + 0.5 * self.stress_level)
        
        # LC phasic response to acute stress
        if stressor_duration < 60:  # Very acute stress
            self.locus_coeruleus_phasic = min(3.0, 1.0 + 2.0 * stressor_magnitude)
            
        return {
            'stress_level': self.stress_level,
            'threat_detection': self.threat_detection,
            'sympathetic_tone': self.sympathetic_tone,
            'fight_or_flight': self.stress_level * self.sympathetic_tone,
            'stress_resilience': coping_resources / (self.stress_level + 0.1),
            'recovery_time': stressor_duration * (1.0 - coping_resources) * 2.0
        }
        
    def modulate_memory_consolidation(self, memory_importance: float,
                                    emotional_valence: float,
                                    sleep_state: str = 'wake') -> Dict[str, float]:
        """Modulate memory consolidation via norepinephrine.
        
        Args:
            memory_importance: Importance/relevance of memory (0-1)
            emotional_valence: Emotional strength (-1 to 1)
            sleep_state: Current sleep state ('wake', 'nrem', 'rem')
            
        Returns:
            Memory consolidation parameters
        """
        # NE enhances consolidation of important/emotional memories
        base_enhancement = self.concentration / self.parameters.baseline_concentration
        
        # Importance weighting
        importance_factor = 1.0 + 0.8 * memory_importance
        
        # Emotional enhancement (stronger for negative emotions)
        emotion_factor = 1.0 + 0.6 * abs(emotional_valence)
        if emotional_valence < 0:  # Negative emotions get extra boost
            emotion_factor *= 1.2
            
        # Sleep state modulation
        sleep_factors = {
            'wake': 1.0,
            'nrem': 0.3,  # Low NE during NREM promotes consolidation
            'rem': 0.1    # Very low NE during REM
        }
        sleep_factor = sleep_factors.get(sleep_state, 1.0)
        
        # Consolidation strength
        consolidation_strength = (base_enhancement * importance_factor * 
                                emotion_factor * sleep_factor)
        
        return {
            'consolidation_strength': consolidation_strength,
            'memory_tagging': importance_factor * emotion_factor,
            'synaptic_plasticity': consolidation_strength * 0.8,
            'protein_synthesis': consolidation_strength * 0.6,
            'sleep_dependent_consolidation': (1.0 - sleep_factor) * consolidation_strength
        }


class AcetylcholineSystem(BaseNeuromodulator):
    """Comprehensive acetylcholine (ACh) neuromodulation system with FIXED parameters."""
    
    def __init__(self, parameters: ModulatorParameters = None,
                 spatial_config: SpatialConfiguration = None):
        if parameters is None:
            parameters = ModulatorParameters(
                molecular_weight=146.21,  # g/mol
                diffusion_coefficient=7.2e-10,  # m²/s
                baseline_concentration=5e-10,  # FIXED: Reduced from 1e-8
                peak_concentration=5e-8,       # FIXED: Reduced from 1e-6
                tau_fast=50.0,   # ms (fast hydrolysis by AChE)
                tau_slow=5000.0,  # ms
                reuptake_rate=0.01,    # FIXED: Reduced (minimal reuptake)
                metabolism_rate=0.12,  # FIXED: Reduced from 0.25 (high AChE activity)
                vesicle_content=35.0   # FIXED: Reduced from default
            )
            
        super().__init__(ModulatorType.ACETYLCHOLINE, parameters, spatial_config)
        
        # FIXED: ACh-specific state variables with proper bounds
        self.attention_state = 0.5  # 0 = relaxed, 1 = focused
        self.learning_context = 0.0  # Learning/novelty signal
        self.cholinergic_tone = 1.0  # Overall cholinergic activity
        self.sensory_gating = 0.5   # Sensory information filtering
        
        # Source nuclei activities
        self.basal_forebrain_activity = 1.0  # Basal forebrain (cortical ACh)
        self.brainstem_activity = 1.0        # Pedunculopontine/laterodorsal tegmental
        
        # Cognitive enhancement state
        self.cognitive_enhancement = 1.0
        
        # FIXED: Add temporal tracking
        self.last_attention_update = 0.0
        self.last_learning_update = 0.0
        self.attention_tau = 5.0  # Time constant for attention changes
        self.learning_tau = 10.0  # Time constant for learning context
        
        logger.info("AcetylcholineSystem initialized")
        
    def _initialize_receptors(self):
        """Initialize cholinergic receptor populations with FIXED parameters."""
        # FIXED: Nicotinic α4β2 receptors with better kinetics
        nicotinic_a4b2_params = ReceptorParameters(
            kon=5e7, koff=5e-3, kd=1e-10,  # FIXED: Consistent high affinity kinetics
            density=1000.0, efficacy=1.0,
            coupling_strength=2.5,  # Fast ionotropic
            hill_coefficient=2.0,   # Cooperative binding
            desensitization_rate=0.01,  # FIXED: Reduced from 0.2
            internalization_rate=0.001  # FIXED: Reduced
        )
        self.receptor_populations[ReceptorSubtype.NICOTINIC_ALPHA4BETA2] = ReceptorPopulation(
            ReceptorSubtype.NICOTINIC_ALPHA4BETA2, 1000, self.spatial_config, nicotinic_a4b2_params
        )
        
        # FIXED: Nicotinic α7 receptors with better kinetics
        nicotinic_a7_params = ReceptorParameters(
            kon=3e7, koff=6e-3, kd=2e-10,  # FIXED: Consistent lower affinity kinetics
            density=600.0, efficacy=1.0,
            coupling_strength=3.0,  # Very fast
            hill_coefficient=1.0,
            desensitization_rate=0.025,  # FIXED: Reduced from 0.5
            internalization_rate=0.0025  # FIXED: Reduced
        )
        self.receptor_populations[ReceptorSubtype.NICOTINIC_ALPHA7] = ReceptorPopulation(
            ReceptorSubtype.NICOTINIC_ALPHA7, 600, self.spatial_config, nicotinic_a7_params
        )
        
        # FIXED: Muscarinic M1 receptors with better kinetics
        muscarinic_m1_params = ReceptorParameters(
            kon=2e7, koff=2e-3, kd=1e-10,  # FIXED: Consistent kinetics
            density=800.0, efficacy=0.9,
            coupling_strength=1.1,
            hill_coefficient=1.0,
            desensitization_rate=0.0008,  # FIXED: Reduced
            internalization_rate=0.00008  # FIXED: Reduced
        )
        self.receptor_populations[ReceptorSubtype.MUSCARINIC_M1] = ReceptorPopulation(
            ReceptorSubtype.MUSCARINIC_M1, 800, self.spatial_config, muscarinic_m1_params
        )
        
        # FIXED: Muscarinic M2 receptors with better kinetics
        muscarinic_m2_params = ReceptorParameters(
            kon=1.5e7, koff=1.5e-3, kd=1e-10,  # FIXED: Consistent kinetics
            density=400.0, efficacy=0.8,
            coupling_strength=0.9,
            hill_coefficient=1.0,
            desensitization_rate=0.0006,  # FIXED: Reduced
            internalization_rate=0.00006  # FIXED: Reduced
        )
        self.receptor_populations[ReceptorSubtype.MUSCARINIC_M2] = ReceptorPopulation(
            ReceptorSubtype.MUSCARINIC_M2, 400, self.spatial_config, muscarinic_m2_params
        )
        
        # FIXED: Muscarinic M4 receptors with better kinetics
        muscarinic_m4_params = ReceptorParameters(
            kon=1.8e7, koff=1.8e-3, kd=1e-10,  # FIXED: Consistent kinetics
            density=300.0, efficacy=0.7,
            coupling_strength=0.8,
            hill_coefficient=1.1,
            desensitization_rate=0.0009,  # FIXED: Reduced
            internalization_rate=0.00009  # FIXED: Reduced
        )
        self.receptor_populations[ReceptorSubtype.MUSCARINIC_M4] = ReceptorPopulation(
            ReceptorSubtype.MUSCARINIC_M4, 300, self.spatial_config, muscarinic_m4_params
        )
        
    def _compute_synthesis_rate(self, **kwargs) -> float:
        """Compute acetylcholine synthesis with FIXED bounds."""
        base_rate = self.parameters.synthesis_rate
        
        # FIXED: Activity-dependent synthesis with bounds
        basal_forebrain_factor = 1.0 + 0.3 * self.basal_forebrain_activity  # FIXED: Reduced from 0.4
        brainstem_factor = 1.0 + 0.2 * self.brainstem_activity  # FIXED: Reduced from 0.3
        
        # FIXED: Choline availability with bounds
        choline_availability = kwargs.get('choline_level', 1.0)
        choline_availability = np.clip(choline_availability, 0.3, 3.0)
        choline_factor = choline_availability / (0.5 + choline_availability)  # Michaelis-Menten
        
        # FIXED: Attention-dependent synthesis with bounds
        attention_factor = 1.0 + 0.2 * self.attention_state  # FIXED: Reduced from 0.3
        
        # FIXED: Learning context enhancement with bounds
        learning_factor = 1.0 + 0.3 * self.learning_context  # FIXED: Reduced from 0.4
        
        # FIXED: Circadian modulation with bounds
        circadian_phase = kwargs.get('circadian_phase', 0.0)
        circadian_factor = 1.0 + 0.15 * np.cos(circadian_phase - np.pi/2)  # FIXED: Reduced from 0.25
        circadian_factor = np.clip(circadian_factor, 0.8, 1.2)
        
        synthesis_rate = (base_rate * basal_forebrain_factor * brainstem_factor * 
                         choline_factor * attention_factor * learning_factor * circadian_factor)
        
        return np.clip(synthesis_rate, 0.0, base_rate * 4.0)  # FIXED: Bound output
                
    def _compute_release_rate(self, **kwargs) -> float:
        """Compute acetylcholine release with FIXED scaling."""
        # FIXED: Basal forebrain release with scaling
        cortical_release = self.basal_forebrain_activity * 0.04  # FIXED: Reduced from 0.18
        
        # FIXED: Brainstem release with scaling
        brainstem_release = self.brainstem_activity * 0.025  # FIXED: Reduced from 0.12
        
        # FIXED: Attention-dependent release with scaling
        attention_release = self.attention_state * 0.03  # FIXED: Reduced from 0.15
        
        # FIXED: Learning-induced release with scaling
        learning_release = self.learning_context * 0.04  # FIXED: Reduced from 0.20
        
        # FIXED: Sensory-evoked release with scaling
        sensory_input = kwargs.get('sensory_activity', 0.0)
        sensory_release = sensory_input * 0.015  # FIXED: Reduced from 0.08
        
        # FIXED: Stress modulation with scaling
        stress_level = kwargs.get('stress_level', 0.0)
        stress_release = stress_level * 0.02  # FIXED: Reduced from 0.10
        
        total_release = (cortical_release + brainstem_release + attention_release + 
                        learning_release + sensory_release + stress_release)
        
        return np.clip(total_release, 0.0, self.parameters.synthesis_rate * 3.0)  # FIXED: Bound output
                
    def _compute_modulation_effects(self) -> Dict[str, float]:
        """Compute acetylcholine's effects with FIXED bounds and error handling."""
        effects = {}
        
        try:
            # Get receptor occupancies with error handling
            nicotinic_a4b2_occupancy = 0.0
            nicotinic_a7_occupancy = 0.0
            muscarinic_m1_occupancy = 0.0
            muscarinic_m2_occupancy = 0.0
            muscarinic_m4_occupancy = 0.0
            
            if ReceptorSubtype.NICOTINIC_ALPHA4BETA2 in self.receptor_populations:
                nicotinic_a4b2_occupancy = self.receptor_populations[ReceptorSubtype.NICOTINIC_ALPHA4BETA2].population_states.get('mean_occupancy', 0.0)
            if ReceptorSubtype.NICOTINIC_ALPHA7 in self.receptor_populations:
                nicotinic_a7_occupancy = self.receptor_populations[ReceptorSubtype.NICOTINIC_ALPHA7].population_states.get('mean_occupancy', 0.0)
            if ReceptorSubtype.MUSCARINIC_M1 in self.receptor_populations:
                muscarinic_m1_occupancy = self.receptor_populations[ReceptorSubtype.MUSCARINIC_M1].population_states.get('mean_occupancy', 0.0)
            if ReceptorSubtype.MUSCARINIC_M2 in self.receptor_populations:
                muscarinic_m2_occupancy = self.receptor_populations[ReceptorSubtype.MUSCARINIC_M2].population_states.get('mean_occupancy', 0.0)
            if ReceptorSubtype.MUSCARINIC_M4 in self.receptor_populations:
                muscarinic_m4_occupancy = self.receptor_populations[ReceptorSubtype.MUSCARINIC_M4].population_states.get('mean_occupancy', 0.0)
                
            # FIXED: Ensure occupancies are valid numbers
            nicotinic_a4b2_occupancy = float(np.clip(nicotinic_a4b2_occupancy, 0.0, 1.0))
            nicotinic_a7_occupancy = float(np.clip(nicotinic_a7_occupancy, 0.0, 1.0))
            muscarinic_m1_occupancy = float(np.clip(muscarinic_m1_occupancy, 0.0, 1.0))
            muscarinic_m2_occupancy = float(np.clip(muscarinic_m2_occupancy, 0.0, 1.0))
            muscarinic_m4_occupancy = float(np.clip(muscarinic_m4_occupancy, 0.0, 1.0))
            
            # FIXED: Nicotinic α4β2 effects with bounds
            effects[ModulationTarget.SYNAPTIC_WEIGHT.value] = np.clip(1.0 + 0.4 * nicotinic_a4b2_occupancy, 0.6, 1.8)
            effects[ModulationTarget.PLASTICITY_RATE.value] = np.clip(1.0 + 0.8 * nicotinic_a4b2_occupancy, 0.5, 2.2)
            effects['cognitive_enhancement'] = np.clip(1.0 + 0.6 * nicotinic_a4b2_occupancy, 0.6, 1.8)
            effects['attention_filtering'] = np.clip(1.0 + 0.5 * nicotinic_a4b2_occupancy, 0.7, 1.7)
            
            # FIXED: Nicotinic α7 effects with bounds
            effects['fast_excitation'] = np.clip(1.0 + 1.0 * nicotinic_a7_occupancy, 0.5, 2.5)
            effects['sensory_gating'] = np.clip(1.0 + 0.6 * nicotinic_a7_occupancy, 0.6, 1.8)
            effects['gamma_oscillations'] = np.clip(1.0 + 0.7 * nicotinic_a7_occupancy, 0.6, 2.0)
            
            # FIXED: Muscarinic M1 effects with bounds
            effects[ModulationTarget.MEMBRANE_RESISTANCE.value] = np.clip(1.0 + 0.2 * muscarinic_m1_occupancy, 0.8, 1.4)
            effects['memory_encoding'] = np.clip(1.0 + 0.5 * muscarinic_m1_occupancy, 0.7, 1.7)
            effects['theta_oscillations'] = np.clip(1.0 + 0.4 * muscarinic_m1_occupancy, 0.7, 1.6)
            
            # FIXED: Muscarinic M2 effects with bounds
            effects['ach_release_inhibition'] = np.clip(muscarinic_m2_occupancy, 0.0, 0.8)
            effects[ModulationTarget.INHIBITION_STRENGTH.value] = np.clip(1.0 + 0.2 * muscarinic_m2_occupancy, 0.8, 1.4)
            
            # FIXED: Muscarinic M4 effects with bounds
            effects['dopamine_modulation'] = np.clip(1.0 - 0.3 * muscarinic_m4_occupancy, 0.5, 1.0)
            effects['motor_control'] = np.clip(1.0 + 0.2 * muscarinic_m4_occupancy, 0.8, 1.4)
            
            # FIXED: Overall cholinergic effects with bounds
            total_nicotinic = nicotinic_a4b2_occupancy + nicotinic_a7_occupancy
            total_muscarinic = muscarinic_m1_occupancy + muscarinic_m2_occupancy + muscarinic_m4_occupancy
            
            effects['cholinergic_tone'] = np.clip(0.5 * total_nicotinic + 0.5 * total_muscarinic, 0.0, 2.0)
            effects['attention_enhancement'] = np.clip(1.0 + 0.5 * total_nicotinic, 0.6, 1.8)
            effects['learning_facilitation'] = np.clip(1.0 + 0.6 * (0.6 * total_nicotinic + 0.4 * total_muscarinic), 0.5, 2.0)
            effects['working_memory'] = np.clip(1.0 + 0.4 * total_nicotinic, 0.7, 1.6)
            
            # FIXED: Cognitive load optimization with inverted-U curve
            ach_ratio = self.concentration / (self.parameters.baseline_concentration + 1e-15)
            ach_ratio = np.clip(ach_ratio, 0.1, 10.0)
            
            if ach_ratio <= 1.5:
                cognitive_performance = 0.7 + 0.3 * (ach_ratio / 1.5)  # FIXED: Gentler curve
            else:
                cognitive_performance = 1.0 - 0.05 * (ach_ratio - 1.5)  # FIXED: Reduced penalty
                
            effects['cognitive_performance'] = np.clip(cognitive_performance, 0.3, 1.2)
            
        except Exception as e:
            logger.warning(f"Error computing acetylcholine modulation effects: {e}")
            # FIXED: Return safe default values
            effects = {
                ModulationTarget.SYNAPTIC_WEIGHT.value: 1.0,
                ModulationTarget.PLASTICITY_RATE.value: 1.0,
                ModulationTarget.MEMBRANE_RESISTANCE.value: 1.0,
                ModulationTarget.INHIBITION_STRENGTH.value: 1.0,
                'cognitive_enhancement': 1.0,
                'attention_filtering': 1.0,
                'fast_excitation': 1.0,
                'sensory_gating': 1.0,
                'gamma_oscillations': 1.0,
                'memory_encoding': 1.0,
                'theta_oscillations': 1.0,
                'ach_release_inhibition': 0.0,
                'dopamine_modulation': 1.0,
                'motor_control': 1.0,
                'cholinergic_tone': 1.0,
                'attention_enhancement': 1.0,
                'learning_facilitation': 1.0,
                'working_memory': 1.0,
                'cognitive_performance': 1.0
            }
            
        return effects
        
    def modulate_attention(self, task_demand: float,
                         stimulus_salience: float,
                         distractor_load: float,
                         top_down_control: float = 0.5) -> Dict[str, float]:
        """Modulate attention with FIXED temporal dynamics and bounds."""
        # FIXED: Input validation
        task_demand = np.clip(float(task_demand), 0.0, 1.0)
        stimulus_salience = np.clip(float(stimulus_salience), 0.0, 1.0)
        distractor_load = np.clip(float(distractor_load), 0.0, 1.0)
        top_down_control = np.clip(float(top_down_control), 0.0, 1.0)
        
        # FIXED: Update attention state with temporal dynamics
        current_time = time.time()
        dt = current_time - self.last_attention_update if self.last_attention_update > 0 else 0.1
        dt = np.clip(dt, 0.01, 10.0)
        
        target_attention = 0.2 * task_demand + 0.15 * stimulus_salience + 0.4 * top_down_control
        target_attention = np.clip(target_attention, 0.0, 1.0)
        
        # Apply temporal filtering
        alpha = dt / (self.attention_tau + dt)
        self.attention_state = self.attention_state + alpha * (target_attention - self.attention_state)
        self.attention_state = np.clip(self.attention_state, 0.0, 1.0)
        
        self.last_attention_update = current_time
        
        # FIXED: ACh enhances signal-to-noise ratio with bounds
        ach_factor = self.concentration / (self.parameters.baseline_concentration + 1e-15)
        ach_factor = np.clip(ach_factor, 0.1, 5.0)
        
        # FIXED: Enhanced effects with bounds
        salience_enhancement = np.clip(1.0 + 0.4 * ach_factor * stimulus_salience, 0.8, 1.8)
        control_enhancement = np.clip(1.0 + 0.5 * ach_factor * top_down_control, 0.8, 2.0)
        distractor_suppression = np.clip(1.0 + 0.3 * ach_factor / (1.0 + distractor_load), 0.7, 1.6)
        sustained_attention = np.clip(1.0 + 0.3 * ach_factor * self.attention_state, 0.8, 1.5)
        
        # Update sensory gating
        self.sensory_gating = np.clip((salience_enhancement * control_enhancement * distractor_suppression) / 3.0, 0.5, 2.0)
        
        return {
            'attention_state': float(self.attention_state),
            'salience_enhancement': float(salience_enhancement),
            'control_enhancement': float(control_enhancement),
            'distractor_suppression': float(distractor_suppression),
            'sustained_attention': float(sustained_attention),
            'sensory_gating': float(self.sensory_gating),
            'selective_attention': float(control_enhancement * distractor_suppression),
            'divided_attention': float(sustained_attention / (1.0 + 0.3 * task_demand)),
            'attention_focus_index': float(self.attention_state * control_enhancement)
        }
        
    def modulate_learning(self, novelty_signal: float,
                         prediction_error: float,
                         reward_context: float,
                         consolidation_phase: str = 'encoding') -> Dict[str, float]:
        """Modulate learning and memory based on acetylcholine.
        
        Args:
            novelty_signal: Novelty/surprise signal (0-1)
            prediction_error: Prediction error magnitude (0-1)
            reward_context: Reward/motivation context (0-1)
            consolidation_phase: Memory phase ('encoding', 'consolidation', 'retrieval')
            
        Returns:
            Learning modulation effects
        """
        # Update learning context
        self.learning_context = 0.4 * novelty_signal + 0.3 * prediction_error + 0.3 * reward_context
        
        # ACh effects on different learning phases
        ach_factor = self.concentration / self.parameters.baseline_concentration
        
        phase_modulations = {
            'encoding': {
                'plasticity_enhancement': 1.0 + 1.0 * ach_factor,
                'attention_to_detail': 1.0 + 0.8 * ach_factor,
                'interference_reduction': 1.0 + 0.6 * ach_factor
            },
            'consolidation': {
                'plasticity_enhancement': 1.0 + 0.4 * ach_factor,
                'memory_stabilization': 1.0 + 0.7 * ach_factor,
                'pattern_completion': 1.0 + 0.5 * ach_factor
            },
            'retrieval': {
                'plasticity_enhancement': 1.0 + 0.2 * ach_factor,
                'retrieval_precision': 1.0 + 0.6 * ach_factor,
                'context_binding': 1.0 + 0.4 * ach_factor
            }
        }
        
        current_modulation = phase_modulations.get(consolidation_phase, phase_modulations['encoding'])
        
        # Novelty-dependent modulation
        novelty_boost = 1.0 + 0.5 * novelty_signal * ach_factor
        
        # Apply novelty boost to all effects
        for key in current_modulation:
            current_modulation[key] *= novelty_boost
            
        # Additional learning effects
        current_modulation.update({
            'learning_context': self.learning_context,
            'novelty_detection': novelty_signal * ach_factor,
            'error_correction': prediction_error * ach_factor,
            'motivation_enhancement': reward_context * ach_factor,
            'cognitive_flexibility': (1.0 + 0.3 * ach_factor) / (1.0 + 0.2 * self.attention_state),
            'memory_capacity': 1.0 + 0.4 * ach_factor * (1.0 - 0.3 * novelty_signal)
        })
        
        return current_modulation
        
    def modulate_sensory_processing(self, sensory_inputs: Dict[str, float],
                                  attention_targets: Dict[str, float],
                                  adaptation_level: float = 0.0) -> Dict[str, float]:
        """Modulate sensory processing and gating.
        
        Args:
            sensory_inputs: Dictionary of sensory modality inputs
            attention_targets: Attention weights for each modality
            adaptation_level: Current adaptation level (0-1)
            
        Returns:
            Sensory processing modulation effects
        """
        ach_factor = self.concentration / self.parameters.baseline_concentration
        
        modulated_inputs = {}
        gating_effects = {}
        
        for modality, input_strength in sensory_inputs.items():
            attention_weight = attention_targets.get(modality, 0.5)
            
            # ACh enhances attended sensory channels
            enhancement = 1.0 + 0.6 * ach_factor * attention_weight
            
            # Gating of unattended inputs
            if attention_weight < 0.3:
                gating = 0.5 + 0.3 * ach_factor  # Suppress unattended
            else:
                gating = 1.0 + 0.4 * ach_factor  # Enhance attended
                
            # Adaptation effects
            adaptation_factor = 1.0 - 0.3 * adaptation_level
            
            modulated_inputs[modality] = (input_strength * enhancement * 
                                        gating * adaptation_factor)
            gating_effects[modality] = gating
            
        # Overall sensory gating
        mean_gating = np.mean(list(gating_effects.values()))
        self.sensory_gating = mean_gating
        
        return {
            'modulated_inputs': modulated_inputs,
            'gating_effects': gating_effects,
            'sensory_enhancement': 1.0 + 0.5 * ach_factor,
            'sensory_filtering': mean_gating,
            'cross_modal_integration': 1.0 + 0.3 * ach_factor,
            'habituation_reduction': 1.0 + 0.4 * ach_factor,
            'signal_to_noise_ratio': 1.0 + 0.7 * ach_factor
        }
        
    def get_cognitive_enhancement_profile(self) -> Dict[str, float]:
        """Get comprehensive cognitive enhancement profile with FIXED bounds."""
        try:
            ach_ratio = self.concentration / (self.parameters.baseline_concentration + 1e-15)
            ach_ratio = np.clip(ach_ratio, 0.1, 10.0)
            
            # FIXED: Optimal ACh levels for different functions
            attention_optimal = 1.5   # FIXED: Increased from 1.2
            memory_optimal = 2.0      # FIXED: Increased from 1.5  
            learning_optimal = 2.5    # FIXED: Increased from 1.8
            
            # FIXED: Performance curves with gentler slopes
            attention_performance = self._compute_performance_curve(ach_ratio, attention_optimal, steepness=0.3)
            memory_performance = self._compute_performance_curve(ach_ratio, memory_optimal, steepness=0.25)
            learning_performance = self._compute_performance_curve(ach_ratio, learning_optimal, steepness=0.2)
            
            # Overall cognitive enhancement
            self.cognitive_enhancement = np.clip((attention_performance + memory_performance + learning_performance) / 3.0, 0.3, 1.5)
            
            return {
                'attention_performance': float(attention_performance),
                'memory_performance': float(memory_performance),
                'learning_performance': float(learning_performance),
                'cognitive_enhancement': float(self.cognitive_enhancement),
                'processing_speed': float(np.clip(1.0 + 0.2 * min(ach_ratio, 2.0), 0.8, 1.4)),
                'cognitive_flexibility': float(np.clip(1.0 + 0.3 * attention_performance, 0.7, 1.5)),
                'working_memory_capacity': float(np.clip(0.8 + 0.2 * memory_performance, 0.6, 1.2)),
                'executive_control': float(np.clip(0.9 + 0.1 * attention_performance, 0.7, 1.1)),
                'perceptual_learning': float(learning_performance),
                'cholinergic_tone': float(self.cholinergic_tone),
                'attention_state': float(self.attention_state),
                'learning_context': float(self.learning_context)
            }
            
        except Exception as e:
            logger.warning(f"Error computing cognitive enhancement profile: {e}")
            return {
                'attention_performance': 1.0,
                'memory_performance': 1.0,
                'learning_performance': 1.0,
                'cognitive_enhancement': 1.0,
                'processing_speed': 1.0,
                'cognitive_flexibility': 1.0,
                'working_memory_capacity': 1.0,
                'executive_control': 1.0,
                'perceptual_learning': 1.0,
                'cholinergic_tone': 1.0,
                'attention_state': 0.5,
                'learning_context': 0.0,
                'error': str(e)
            }
        
    def _compute_performance_curve(self, current_level: float, optimal_level: float, steepness: float = 0.4) -> float:
        """Compute inverted-U performance curve with FIXED parameters."""
        try:
            if current_level <= optimal_level:
                # Rising phase - more gradual
                performance = 0.6 + 0.4 * (current_level / optimal_level)
            else:
                # Falling phase - less steep penalty
                excess = (current_level - optimal_level) / optimal_level
                performance = 1.0 - steepness * excess
                
            return float(np.clip(performance, 0.2, 1.2))
            
        except Exception as e:
            logger.debug(f"Performance curve calculation failed: {e}")
            return 1.0


# ===========================================================================================
# INTEGRATED NEUROMODULATION SYSTEM
# ===========================================================================================

class CrossModulatorInteractions:
    """Manages interactions between different neuromodulator systems with FIXED scaling."""
    
    def __init__(self):
        # FIXED: Interaction matrices with reduced magnitudes
        self.synthesis_interactions = self._initialize_synthesis_interactions()
        self.release_interactions = self._initialize_release_interactions()
        self.receptor_interactions = self._initialize_receptor_interactions()
        self.functional_interactions = self._initialize_functional_interactions()
        
        # FIXED: Add interaction bounds
        self.max_interaction_strength = 0.5  # Maximum interaction effect
        self.min_modulation_factor = 0.2    # Minimum modulation factor
        self.max_modulation_factor = 3.0    # Maximum modulation factor
        
        logger.info("CrossModulatorInteractions initialized")
        
    def _initialize_synthesis_interactions(self) -> Dict[str, Dict[str, float]]:
        """Initialize synthesis interaction matrix with FIXED scaling."""
        return {
            ModulatorType.DOPAMINE.value: {
                ModulatorType.SEROTONIN.value: -0.1,      # FIXED: Reduced from -0.2
                ModulatorType.NOREPINEPHRINE.value: 0.05,  # FIXED: Reduced from 0.1
                ModulatorType.ACETYLCHOLINE.value: 0.0    # Minimal direct effect
            },
            ModulatorType.SEROTONIN.value: {
                ModulatorType.DOPAMINE.value: -0.15,       # FIXED: Reduced from -0.3
                ModulatorType.NOREPINEPHRINE.value: -0.05, # FIXED: Reduced from -0.1
                ModulatorType.ACETYLCHOLINE.value: 0.05   # FIXED: Reduced from 0.1
            },
            ModulatorType.NOREPINEPHRINE.value: {
                ModulatorType.DOPAMINE.value: 0.1,        # FIXED: Reduced from 0.2
                ModulatorType.SEROTONIN.value: 0.0,       # Minimal effect
                ModulatorType.ACETYLCHOLINE.value: 0.15   # FIXED: Reduced from 0.3
            },
            ModulatorType.ACETYLCHOLINE.value: {
                ModulatorType.DOPAMINE.value: -0.05,       # FIXED: Reduced from -0.1
                ModulatorType.SEROTONIN.value: 0.1,       # FIXED: Reduced from 0.2
                ModulatorType.NOREPINEPHRINE.value: 0.05   # FIXED: Reduced from 0.1
            }
        }
        
    def _initialize_release_interactions(self) -> Dict[str, Dict[str, float]]:
        """Initialize release interaction matrix with FIXED scaling.""" 
        return {
            ModulatorType.DOPAMINE.value: {
                ModulatorType.SEROTONIN.value: -0.2,      # FIXED: Reduced from -0.4
                ModulatorType.NOREPINEPHRINE.value: 0.1,  # FIXED: Reduced from 0.2
                ModulatorType.ACETYLCHOLINE.value: -0.1   # FIXED: Reduced from -0.2
            },
            ModulatorType.SEROTONIN.value: {
                ModulatorType.DOPAMINE.value: -0.25,       # FIXED: Reduced from -0.5
                ModulatorType.NOREPINEPHRINE.value: -0.1, # FIXED: Reduced from -0.2
                ModulatorType.ACETYLCHOLINE.value: 0.15    # FIXED: Reduced from 0.3
            },
            ModulatorType.NOREPINEPHRINE.value: {
                ModulatorType.DOPAMINE.value: 0.15,        # FIXED: Reduced from 0.3
                ModulatorType.SEROTONIN.value: 0.05,       # FIXED: Reduced from 0.1
                ModulatorType.ACETYLCHOLINE.value: 0.2    # FIXED: Reduced from 0.4
            },
            ModulatorType.ACETYLCHOLINE.value: {
                ModulatorType.DOPAMINE.value: -0.15,       # FIXED: Reduced from -0.3
                ModulatorType.SEROTONIN.value: 0.05,       # FIXED: Reduced from 0.1
                ModulatorType.NOREPINEPHRINE.value: 0.1   # FIXED: Reduced from 0.2
            }
        }
        
    def _initialize_receptor_interactions(self) -> Dict[str, float]:
        """Initialize receptor cross-interactions with FIXED scaling."""
        return {
            # FIXED: Heteroreceptor interactions with reduced magnitudes
            'dopamine_serotonin': -0.15,    # FIXED: Reduced from -0.3
            'serotonin_dopamine': -0.2,     # FIXED: Reduced from -0.4
            'norepinephrine_acetylcholine': 0.15,  # FIXED: Reduced from 0.3
            'acetylcholine_dopamine': -0.1, # FIXED: Reduced from -0.2
            # FIXED: Receptor sensitization/desensitization interactions
            'cross_sensitization': 0.05,     # FIXED: Reduced from 0.1
            'cross_tolerance': -0.1         # FIXED: Reduced from -0.2
        }
        
    def _initialize_functional_interactions(self) -> Dict[str, Dict[str, float]]:
        """Initialize functional interaction matrix with FIXED scaling."""
        return {
            'attention': {
                ModulatorType.ACETYLCHOLINE.value: 0.6,   # FIXED: Reduced from 0.8
                ModulatorType.NOREPINEPHRINE.value: 0.4,  # FIXED: Reduced from 0.6
                ModulatorType.DOPAMINE.value: 0.2,        # FIXED: Reduced from 0.3
                ModulatorType.SEROTONIN.value: -0.1       # FIXED: Reduced from -0.2
            },
            'learning': {
                ModulatorType.ACETYLCHOLINE.value: 0.5,   # FIXED: Reduced from 0.7
                ModulatorType.DOPAMINE.value: 0.6,        # FIXED: Reduced from 0.8
                ModulatorType.NOREPINEPHRINE.value: 0.3,  # FIXED: Reduced from 0.4
                ModulatorType.SEROTONIN.value: 0.1        # FIXED: Reduced from 0.2
            },
            'memory': {
                ModulatorType.ACETYLCHOLINE.value: 0.7,   # FIXED: Reduced from 0.9
                ModulatorType.NOREPINEPHRINE.value: 0.4,  # FIXED: Reduced from 0.5
                ModulatorType.DOPAMINE.value: 0.2,        # FIXED: Reduced from 0.3
                ModulatorType.SEROTONIN.value: 0.05       # FIXED: Reduced from 0.1
            },
            'mood': {
                ModulatorType.SEROTONIN.value: 0.7,       # FIXED: Reduced from 0.9
                ModulatorType.DOPAMINE.value: 0.4,        # FIXED: Reduced from 0.6
                ModulatorType.NOREPINEPHRINE.value: 0.3,  # FIXED: Reduced from 0.4
                ModulatorType.ACETYLCHOLINE.value: 0.05   # FIXED: Reduced from 0.1
            },
            'arousal': {
                ModulatorType.NOREPINEPHRINE.value: 0.7,  # FIXED: Reduced from 0.9
                ModulatorType.ACETYLCHOLINE.value: 0.4,   # FIXED: Reduced from 0.5
                ModulatorType.DOPAMINE.value: 0.2,        # FIXED: Reduced from 0.3
                ModulatorType.SEROTONIN.value: -0.15      # FIXED: Reduced from -0.3
            }
        }
        
    def compute_cross_modulation(self, modulator_concentrations: Dict[str, float],
                               interaction_type: str = 'release') -> Dict[str, float]:
        """Compute cross-modulation effects with FIXED bounds and scaling."""
        if interaction_type == 'synthesis':
            interaction_matrix = self.synthesis_interactions
        elif interaction_type == 'release':
            interaction_matrix = self.release_interactions
        elif interaction_type == 'receptor':
            return self._compute_receptor_interactions(modulator_concentrations)
        else:
            raise ValueError(f"Unknown interaction type: {interaction_type}")
            
        cross_modulation = {}
        
        for target_modulator in modulator_concentrations:
            modulation_factor = 1.0
            
            for source_modulator, source_concentration in modulator_concentrations.items():
                if (source_modulator != target_modulator and 
                    source_modulator in interaction_matrix and
                    target_modulator in interaction_matrix[source_modulator]):
                    
                    interaction_strength = interaction_matrix[source_modulator][target_modulator]
                    
                    # FIXED: Normalize concentration with bounds
                    normalized_concentration = np.clip(source_concentration, 0.0, 10.0)
                    
                    # FIXED: Apply bounded sigmoidal interaction
                    interaction_effect = interaction_strength * np.tanh(normalized_concentration)
                    interaction_effect = np.clip(interaction_effect, -self.max_interaction_strength, 
                                               self.max_interaction_strength)
                    
                    # FIXED: Additive rather than multiplicative to prevent exponential growth
                    modulation_factor += interaction_effect
                    
            # FIXED: Apply strict bounds to prevent unrealistic values
            cross_modulation[target_modulator] = np.clip(modulation_factor, 
                                                       self.min_modulation_factor, 
                                                       self.max_modulation_factor)
            
        return cross_modulation
        
    def _compute_receptor_interactions(self, concentrations: Dict[str, float]) -> Dict[str, float]:
        """Compute receptor-level interactions with FIXED bounds."""
        interactions = {}
        
        try:
            # FIXED: Dopamine-Serotonin interaction with bounds
            da_conc = np.clip(concentrations.get(ModulatorType.DOPAMINE.value, 0.0), 0.0, 10.0)
            sht_conc = np.clip(concentrations.get(ModulatorType.SEROTONIN.value, 0.0), 0.0, 10.0)
            
            da_sht_interaction = self.receptor_interactions['dopamine_serotonin'] * da_conc * sht_conc
            interactions['da_sht_interaction'] = np.clip(da_sht_interaction, -1.0, 1.0)
            
            # FIXED: Norepinephrine-Acetylcholine interaction with bounds
            ne_conc = np.clip(concentrations.get(ModulatorType.NOREPINEPHRINE.value, 0.0), 0.0, 10.0)
            ach_conc = np.clip(concentrations.get(ModulatorType.ACETYLCHOLINE.value, 0.0), 0.0, 10.0)
            
            ne_ach_interaction = self.receptor_interactions['norepinephrine_acetylcholine'] * ne_conc * ach_conc
            interactions['ne_ach_interaction'] = np.clip(ne_ach_interaction, -1.0, 1.0)
            
            # FIXED: Cross-sensitization and tolerance with bounds
            total_activity = sum(concentrations.values())
            if total_activity > 0:
                interactions['cross_sensitization'] = np.clip(
                    self.receptor_interactions['cross_sensitization'] * total_activity, 0.0, 0.5)
                interactions['cross_tolerance'] = np.clip(
                    self.receptor_interactions['cross_tolerance'] * total_activity, -0.5, 0.0)
            else:
                interactions['cross_sensitization'] = 0.0
                interactions['cross_tolerance'] = 0.0
                    
        except Exception as e:
            logger.warning(f"Error computing receptor interactions: {e}")
            interactions = {
                'da_sht_interaction': 0.0,
                'ne_ach_interaction': 0.0,
                'cross_sensitization': 0.0,
                'cross_tolerance': 0.0
            }
        
        return interactions
        
    def compute_functional_synergy(self, modulator_concentrations: Dict[str, float],
                                 function: str) -> float:
        """Compute functional synergy with FIXED bounds and error handling."""
        if function not in self.functional_interactions:
            return 1.0
            
        function_weights = self.functional_interactions[function]
        
        try:
            # FIXED: Compute weighted sum with bounds
            total_contribution = 0.0
            total_weight = 0.0
            
            for modulator, weight in function_weights.items():
                if modulator in modulator_concentrations:
                    concentration = np.clip(modulator_concentrations[modulator], 0.0, 10.0)
                    contribution = weight * concentration
                    total_contribution += contribution
                    total_weight += abs(weight)
                    
            # FIXED: Normalize by total weight with safety check
            if total_weight > 0:
                normalized_contribution = total_contribution / total_weight
            else:
                normalized_contribution = 0.0
                
            # FIXED: Apply bounded synergy scaling
            if normalized_contribution >= 0:
                synergy_coefficient = 1.0 + 0.3 * np.tanh(normalized_contribution)
            else:
                synergy_coefficient = 1.0 + 0.2 * np.tanh(normalized_contribution)
                
            # FIXED: Apply strict bounds
            synergy_coefficient = np.clip(synergy_coefficient, 0.2, 2.0)
            
        except Exception as e:
            logger.warning(f"Error computing functional synergy for {function}: {e}")
            synergy_coefficient = 1.0
            
        return float(synergy_coefficient)


class NeuromodulationSystem:
    """Main neuromodulation system with FIXED concentration management and release scaling."""
    
    def __init__(self, config: Dict[str, Any] = None,
                 spatial_config: SpatialConfiguration = None):
        self.config = config or {}
        self.spatial_config = spatial_config or SpatialConfiguration()
        
        # Initialize modulator systems with FIXED parameters
        self.modulator_systems = self._initialize_modulator_systems()
        
        # Cross-modulator interactions
        self.cross_interactions = CrossModulatorInteractions()
        
        # System-wide state
        self.global_state = {
            'arousal_level': 0.5,
            'attention_focus': 0.5,
            'learning_context': 0.0,
            'mood_state': 0.5,
            'stress_level': 0.0,
            'cognitive_load': 0.0
        }
        
        # Environmental inputs
        self.environmental_inputs = {
            'reward_signal': 0.0,
            'stress_signal': 0.0,
            'novelty_signal': 0.0,
            'social_signal': 0.0,
            'circadian_phase': 0.0,
            'task_demand': 0.0
        }
        
        # FIXED: Integration parameters with bounds
        self.integration_weights = self.config.get('integration_weights', {
            'dopamine': 0.25,
            'serotonin': 0.25,
            'norepinephrine': 0.25,
            'acetylcholine': 0.25
        })
        
        # FIXED: Release scaling parameters
        self.release_scaling = {
            'dopamine': 1e-6,      # Scale factor for DA release
            'serotonin': 5e-7,     # Scale factor for 5-HT release
            'norepinephrine': 2e-7, # Scale factor for NE release
            'acetylcholine': 1e-6   # Scale factor for ACh release
        }
        
        # System monitoring
        self.system_history = deque(maxlen=10000)
        self.performance_metrics = {}
        
        # Neural network connections
        self.connected_core = None
        self.connected_plasticity = None
        self.connected_timing = None
        
        logger.info("NeuromodulationSystem initialized with comprehensive modulator integration")
        
    def _initialize_modulator_systems(self) -> Dict[str, BaseNeuromodulator]:
        """Initialize all modulator subsystems with FIXED parameters."""
        systems = {}
        
        # FIXED: Dopamine system with reduced concentrations
        da_params = ModulatorParameters(
            baseline_concentration=1e-9,   # FIXED: Reduced from 1e-8
            peak_concentration=1e-7,       # FIXED: Reduced from 1e-6
            tau_fast=200.0,
            tau_slow=15000.0,
            vesicle_content=30.0           # FIXED: Reduced from default
        )
        systems[ModulatorType.DOPAMINE.value] = DopamineSystem(da_params, self.spatial_config)
        
        # FIXED: Serotonin system with reduced concentrations
        sht_params = ModulatorParameters(
            baseline_concentration=2e-10,  # FIXED: Reduced from 5e-9
            peak_concentration=2e-8,       # FIXED: Reduced from 5e-7
            tau_fast=500.0,
            tau_slow=30000.0,
            vesicle_content=25.0           # FIXED: Reduced from default
        )
        systems[ModulatorType.SEROTONIN.value] = SerotoninSystem(sht_params, self.spatial_config)
        
        # FIXED: Norepinephrine system with reduced concentrations
        ne_params = ModulatorParameters(
            baseline_concentration=1e-10,  # FIXED: Reduced from 2e-9
            peak_concentration=1e-8,       # FIXED: Reduced from 2e-7
            tau_fast=300.0,
            tau_slow=20000.0,
            vesicle_content=20.0           # FIXED: Reduced from default
        )
        systems[ModulatorType.NOREPINEPHRINE.value] = NorepinephrineSystem(ne_params, self.spatial_config)
        
        # FIXED: Acetylcholine system with reduced concentrations
        ach_params = ModulatorParameters(
            baseline_concentration=5e-10,  # FIXED: Reduced from 1e-8
            peak_concentration=5e-8,       # FIXED: Reduced from 1e-6
            tau_fast=50.0,
            tau_slow=5000.0,
            vesicle_content=35.0           # FIXED: Reduced from default
        )
        systems[ModulatorType.ACETYLCHOLINE.value] = AcetylcholineSystem(ach_params, self.spatial_config)
        
        return systems
        
    def connect_to_core(self, neuromorphic_core):
        """Connect to neuromorphic core for bidirectional communication."""
        self.connected_core = neuromorphic_core
        logger.info("NeuromodulationSystem connected to NeuromorphicCore")
        
    def connect_to_plasticity(self, plasticity_engine):
        """Connect to plasticity engine for learning modulation."""
        self.connected_plasticity = plasticity_engine
        logger.info("NeuromodulationSystem connected to NeuroplasticityEngine")
        
    def connect_to_timing(self, timing_circuits):
        """Connect to biological timing circuits for rhythm modulation."""
        self.connected_timing = timing_circuits
        logger.info("NeuromodulationSystem connected to BiologicalTimingCircuits")
        
    def step(self, dt: float, 
             external_signals: Dict[str, float] = None,
             neural_activity: Dict[str, float] = None) -> Dict[str, Any]:
        """Execute one neuromodulation system update step.
        
        Args:
            dt: Time step (seconds)
            external_signals: External environmental signals
            neural_activity: Current neural activity levels
            
        Returns:
            Comprehensive system state information
        """
        if external_signals is None:
            external_signals = {}
        if neural_activity is None:
            neural_activity = {}
            
        # Update environmental inputs
        self._update_environmental_inputs(external_signals, neural_activity)
        
        # Get current concentrations for cross-modulation
        current_concentrations = self._get_current_concentrations()
        
        # Compute cross-modulation effects
        synthesis_modulation = self.cross_interactions.compute_cross_modulation(
            current_concentrations, 'synthesis'
        )
        release_modulation = self.cross_interactions.compute_cross_modulation(
            current_concentrations, 'release'
        )
        
        # Update individual modulator systems
        system_results = {}
        for modulator_name, system in self.modulator_systems.items():
            # Prepare system-specific inputs
            system_inputs = self._prepare_system_inputs(modulator_name, neural_activity)
            
            # Determine release events based on neural activity and environmental signals
            release_events = self._compute_release_events(modulator_name, neural_activity)
            
            # Apply cross-modulation
            synthesis_factor = synthesis_modulation.get(modulator_name, 1.0)
            release_factor = release_modulation.get(modulator_name, 1.0)
            
            # Update system
            result = system.update_concentration(
                dt, 
                release_events,
                synthesis_factor,
                1.0 / release_factor  # Inverse for uptake modulation
            )
            
            # Store results
            system_results[modulator_name] = result
            
        # Update global state
        self._update_global_state(system_results)
        
        # Compute functional synergies
        functional_synergies = self._compute_functional_synergies(current_concentrations)
        
        # Apply modulation to connected systems
        if self.connected_core:
            self._apply_core_modulation(dt)
        if self.connected_plasticity:
            self._apply_plasticity_modulation(dt)
        if self.connected_timing:
            self._apply_timing_modulation(dt)
            
        # Update performance metrics
        self._update_performance_metrics(system_results, functional_synergies)
        
        # Store system history
        system_state = {
            'time': time.time(),
            'concentrations': current_concentrations,
            'global_state': self.global_state.copy(),
            'environmental_inputs': self.environmental_inputs.copy(),
            'functional_synergies': functional_synergies,
            'cross_modulation': {
                'synthesis': synthesis_modulation,
                'release': release_modulation
            }
        }
        self.system_history.append(system_state)
        
        return {
            'system_results': system_results,
            'global_state': self.global_state.copy(),
            'concentrations': current_concentrations,
            'functional_synergies': functional_synergies,
            'cross_modulation_effects': {
                'synthesis_modulation': synthesis_modulation,
                'release_modulation': release_modulation
            },
            'performance_metrics': self.performance_metrics.copy(),
            'system_coherence': self._compute_system_coherence()
        }
        
    def _update_environmental_inputs(self, external_signals: Dict[str, float],
                                   neural_activity: Dict[str, float]):
        """Update environmental input signals."""
        # Update from external signals
        for signal_name, value in external_signals.items():
            if signal_name in self.environmental_inputs:
                self.environmental_inputs[signal_name] = value
                
        # Infer some signals from neural activity
        if 'population_rate' in neural_activity:
            # Use population rate to infer arousal
            pop_rate = neural_activity['population_rate']
            self.environmental_inputs['arousal_signal'] = min(1.0, pop_rate / 50.0)  # Normalize
            
        if 'synchrony' in neural_activity:
            # Use synchrony to infer attention
            synchrony = neural_activity['synchrony']
            self.environmental_inputs['attention_signal'] = synchrony
            
    def _get_current_concentrations(self) -> Dict[str, float]:
        """Get current concentrations from all modulator systems."""
        concentrations = {}
        for modulator_name, system in self.modulator_systems.items():
            concentrations[modulator_name] = system.concentration
        return concentrations
        
    def _prepare_system_inputs(self, modulator_name: str, 
                             neural_activity: Dict[str, float]) -> Dict[str, float]:
        """Prepare system-specific inputs for each modulator."""
        base_inputs = {
            'neural_activity': neural_activity.get('population_rate', 0.0),
            'circadian_phase': self.environmental_inputs['circadian_phase'],
            'stress_level': self.environmental_inputs['stress_signal']
        }
        
        # Modulator-specific inputs
        if modulator_name == ModulatorType.DOPAMINE.value:
            base_inputs.update({
                'reward_signal': self.environmental_inputs['reward_signal'],
                'arousal_level': self.environmental_inputs.get('arousal_signal', 0.5)
            })
        elif modulator_name == ModulatorType.SEROTONIN.value:
            base_inputs.update({
                'social_signal': self.environmental_inputs['social_signal'],
                'mood_context': self.global_state['mood_state']
            })
        elif modulator_name == ModulatorType.NOREPINEPHRINE.value:
            base_inputs.update({
                'novelty_signal': self.environmental_inputs['novelty_signal'],
                'attention_demand': self.environmental_inputs['task_demand']
            })
        elif modulator_name == ModulatorType.ACETYLCHOLINE.value:
            base_inputs.update({
                'task_demand': self.environmental_inputs['task_demand'],
                'sensory_activity': neural_activity.get('sensory_input', 0.0)
            })
            
        return base_inputs
        
    def _compute_release_events(self, modulator_name: str,
                              neural_activity: Dict[str, float]) -> List[Tuple[int, float]]:
        """Compute release events with FIXED scaling and realistic amounts."""
        release_events = []
        
        # Get the modulator system
        system = self.modulator_systems[modulator_name]
        
        # FIXED: Base release probability and scaling
        base_release_probability = 0.05  # FIXED: Reduced from 0.1
        scale_factor = self.release_scaling.get(modulator_name, 1e-6)
        
        # FIXED: Modulator-specific release triggers with proper scaling
        if modulator_name == ModulatorType.DOPAMINE.value:
            # Release on reward prediction error
            reward_signal = self.environmental_inputs['reward_signal']
            if reward_signal > 0.3:  # FIXED: Higher threshold
                release_probability = min(0.6, reward_signal * 0.8)  # FIXED: Reduced probability
                release_amount = reward_signal * 50.0 * scale_factor  # FIXED: Much smaller base amount
                
                for site_id in range(min(len(system.release_sites), 2)):  # FIXED: Limit sites
                    if np.random.random() < release_probability:
                        release_events.append((site_id, release_amount))
                        
        elif modulator_name == ModulatorType.SEROTONIN.value:
            # Release based on mood regulation needs
            mood_deviation = abs(self.global_state['mood_state'] - 0.5)
            if mood_deviation > 0.4:  # FIXED: Higher threshold
                release_probability = min(0.5, mood_deviation * 0.8)  # FIXED: Reduced probability
                release_amount = mood_deviation * 40.0 * scale_factor  # FIXED: Much smaller base amount
                
                for site_id in range(min(len(system.release_sites), 2)):  # FIXED: Limit sites
                    if np.random.random() < release_probability:
                        release_events.append((site_id, release_amount))
                        
        elif modulator_name == ModulatorType.NOREPINEPHRINE.value:
            # Release on stress or high attention demand
            stress_level = self.environmental_inputs['stress_signal']
            attention_demand = self.environmental_inputs['task_demand']
            
            trigger_strength = max(stress_level, attention_demand)
            if trigger_strength > 0.5:  # FIXED: Higher threshold
                release_probability = min(0.7, trigger_strength * 0.9)  # FIXED: Reduced probability
                release_amount = trigger_strength * 30.0 * scale_factor  # FIXED: Much smaller base amount
                
                for site_id in range(min(len(system.release_sites), 2)):  # FIXED: Limit sites
                    if np.random.random() < release_probability:
                        release_events.append((site_id, release_amount))
                        
        elif modulator_name == ModulatorType.ACETYLCHOLINE.value:
            # Release on attention and learning demands
            attention_level = self.global_state['attention_focus']
            learning_context = self.global_state['learning_context']
            
            trigger_strength = max(attention_level, learning_context)
            if trigger_strength > 0.4:  # FIXED: Higher threshold
                release_probability = min(0.6, trigger_strength * 0.8)  # FIXED: Reduced probability
                release_amount = trigger_strength * 45.0 * scale_factor  # FIXED: Much smaller base amount
                
                for site_id in range(min(len(system.release_sites), 2)):  # FIXED: Limit sites
                    if np.random.random() < release_probability:
                        release_events.append((site_id, release_amount))
                        
        return release_events
        
    def _update_global_state(self, system_results: Dict[str, Dict[str, Any]]):
        """Update global neuromodulation state."""
        # Extract key metrics from each system
        concentrations = {}
        for modulator_name, result in system_results.items():
            concentrations[modulator_name] = result['concentration']
            
        # Update arousal level (primarily NE, some DA)
        ne_contrib = concentrations.get(ModulatorType.NOREPINEPHRINE.value, 0.0)
        da_contrib = concentrations.get(ModulatorType.DOPAMINE.value, 0.0)
        
        # Normalize concentrations to baseline ratios
        ne_ratio = ne_contrib / (2e-9 + 1e-12)  # NE baseline
        da_ratio = da_contrib / (1e-8 + 1e-12)  # DA baseline
        
        self.global_state['arousal_level'] = np.clip(0.3 + 0.4 * ne_ratio + 0.1 * da_ratio, 0.0, 1.0)
        
        # Update attention focus (primarily ACh, some NE)
        ach_contrib = concentrations.get(ModulatorType.ACETYLCHOLINE.value, 0.0)
        ach_ratio = ach_contrib / (1e-8 + 1e-12)  # ACh baseline
        
        self.global_state['attention_focus'] = np.clip(0.2 + 0.5 * ach_ratio + 0.2 * ne_ratio, 0.0, 1.0)
        
        # Update mood state (primarily 5-HT, some DA)
        sht_contrib = concentrations.get(ModulatorType.SEROTONIN.value, 0.0)
        sht_ratio = sht_contrib / (5e-9 + 1e-12)  # 5-HT baseline
        
        self.global_state['mood_state'] = np.clip(0.2 + 0.5 * sht_ratio + 0.2 * da_ratio, 0.0, 1.0)
        
        # Update learning context (ACh and DA)
        learning_signal = 0.4 * ach_ratio + 0.4 * da_ratio
        self.global_state['learning_context'] = np.clip(learning_signal, 0.0, 1.0)
        
        # Update stress level (inverse of 5-HT, enhanced by excessive NE)
        stress_from_sht = max(0.0, 0.8 - sht_ratio)
        stress_from_ne = max(0.0, ne_ratio - 1.5) * 0.5
        self.global_state['stress_level'] = np.clip(stress_from_sht + stress_from_ne, 0.0, 1.0)
        
        # Update cognitive load (balance of all systems)
        cognitive_balance = abs(ach_ratio - 1.0) + abs(da_ratio - 1.0) + abs(ne_ratio - 1.0) + abs(sht_ratio - 1.0)
        self.global_state['cognitive_load'] = np.clip(cognitive_balance * 0.2, 0.0, 1.0)
        
    def _compute_functional_synergies(self, concentrations: Dict[str, float]) -> Dict[str, float]:
        """Compute functional synergies across cognitive domains."""
        synergies = {}
        
        cognitive_functions = ['attention', 'learning', 'memory', 'mood', 'arousal']
        
        for function in cognitive_functions:
            synergy = self.cross_interactions.compute_functional_synergy(concentrations, function)
            synergies[function] = synergy
            
        # Compute overall cognitive coherence
        synergy_values = list(synergies.values())
        synergies['cognitive_coherence'] = np.mean(synergy_values)
        synergies['cognitive_stability'] = 1.0 / (1.0 + np.std(synergy_values))
        
        return synergies
        
    def _apply_core_modulation(self, dt: float):
        """Apply neuromodulation effects to the neuromorphic core with FIXED error handling."""
        if not self.connected_core:
            return
            
        try:
            # Get modulation effects from each system
            all_effects = {}
            for modulator_name, system in self.modulator_systems.items():
                try:
                    effects = system.get_modulation_effects()
                    for effect_name, effect_value in effects.items():
                        if isinstance(effect_value, (int, float)) and np.isfinite(effect_value):
                            if effect_name not in all_effects:
                                all_effects[effect_name] = []
                            all_effects[effect_name].append(effect_value)
                except Exception as e:
                    logger.warning(f"Error getting effects from {modulator_name}: {e}")
                    
            # FIXED: Combine effects with bounds checking
            combined_effects = {}
            for effect_name, effect_values in all_effects.items():
                if len(effect_values) > 0:
                    # FIXED: Filter out invalid values
                    valid_values = [v for v in effect_values if np.isfinite(v) and v > 0]
                    if valid_values:
                        # FIXED: Use geometric mean but with bounds
                        log_values = np.log(np.maximum(valid_values, 1e-10))
                        combined_effect = np.exp(np.mean(log_values))
                        combined_effects[effect_name] = np.clip(combined_effect, 0.1, 5.0)
                    else:
                        combined_effects[effect_name] = 1.0
                else:
                    combined_effects[effect_name] = 1.0
                    
            # Apply to neurons and connections
            self._apply_neural_modulation(combined_effects)
            self._apply_synaptic_modulation(combined_effects)
            
        except Exception as e:
            logger.warning(f"Error applying core modulation: {e}")
        
    def _apply_neural_modulation(self, effects: Dict[str, float]):
        """Apply modulation effects to individual neurons with FIXED type checking."""
        try:
            for neuron_id, neuron in self.connected_core.neurons.items():
                # FIXED: Type checking and safe parameter access
                if not hasattr(neuron, 'parameters'):
                    continue
                    
                # FIXED: Modulate firing threshold with type checking
                if 'firing_threshold' in effects:
                    try:
                        if hasattr(neuron.parameters, 'v_threshold'):
                            base_threshold = getattr(neuron.parameters, 'base_v_threshold', 
                                                   neuron.parameters.v_threshold)
                            # FIXED: Ensure both values are numeric
                            if isinstance(base_threshold, (int, float)) and isinstance(effects['firing_threshold'], (int, float)):
                                new_threshold = float(base_threshold) * float(effects['firing_threshold'])
                                neuron.parameters.v_threshold = new_threshold
                    except (TypeError, AttributeError) as e:
                        logger.debug(f"Could not modulate firing threshold for neuron {neuron_id}: {e}")
                        
                # FIXED: Modulate membrane resistance with type checking
                if 'membrane_resistance' in effects:
                    try:
                        if hasattr(neuron.parameters, 'membrane_resistance'):
                            base_resistance = getattr(neuron.parameters, 'base_membrane_resistance', 
                                                    neuron.parameters.membrane_resistance)
                            if isinstance(base_resistance, (int, float)) and isinstance(effects['membrane_resistance'], (int, float)):
                                new_resistance = float(base_resistance) * float(effects['membrane_resistance'])
                                neuron.parameters.membrane_resistance = new_resistance
                    except (TypeError, AttributeError) as e:
                        logger.debug(f"Could not modulate membrane resistance for neuron {neuron_id}: {e}")
                        
                # FIXED: Store base values safely
                if not hasattr(neuron.parameters, 'base_v_threshold') and hasattr(neuron.parameters, 'v_threshold'):
                    if isinstance(neuron.parameters.v_threshold, (int, float)):
                        neuron.parameters.base_v_threshold = neuron.parameters.v_threshold
                        
                if not hasattr(neuron.parameters, 'base_membrane_resistance') and hasattr(neuron.parameters, 'membrane_resistance'):
                    if isinstance(neuron.parameters.membrane_resistance, (int, float)):
                        neuron.parameters.base_membrane_resistance = neuron.parameters.membrane_resistance
                        
        except Exception as e:
            logger.warning(f"Error in neural modulation: {e}")
                
    def _apply_synaptic_modulation(self, effects: Dict[str, float]):
        """Apply modulation effects to synaptic connections."""
        for connection_id, connection in self.connected_core.connections.items():
            # Modulate synaptic weight
            if 'synaptic_weight' in effects:
                base_weight = getattr(connection.parameters, 'base_weight', connection.parameters.weight)
                connection.parameters.weight = base_weight * effects['synaptic_weight']
                
            # Modulate synaptic conductance
            if 'synaptic_conductance' in effects:
                base_conductance = getattr(connection.parameters, 'base_max_conductance', connection.parameters.max_conductance)
                connection.parameters.max_conductance = base_conductance * effects['synaptic_conductance']
                
            # Store base values if not already stored
            if not hasattr(connection.parameters, 'base_weight'):
                connection.parameters.base_weight = connection.parameters.weight
            if not hasattr(connection.parameters, 'base_max_conductance'):
                connection.parameters.base_max_conductance = connection.parameters.max_conductance
                
    def _apply_plasticity_modulation(self, dt: float):
        """Apply neuromodulation effects to plasticity mechanisms."""
        if not self.connected_plasticity:
            return
            
        # Get plasticity-related effects
        plasticity_effects = {}
        for modulator_name, system in self.modulator_systems.items():
            effects = system.get_modulation_effects()
            if 'plasticity_rate' in effects:
                plasticity_effects[modulator_name] = effects['plasticity_rate']
                
        # Combine plasticity effects
        if plasticity_effects:
            combined_plasticity = np.exp(np.mean(np.log(list(plasticity_effects.values()))))
            
            # Modulate learning rates in plasticity engine
            if hasattr(self.connected_plasticity, 'default_params'):
                base_a_plus = getattr(self.connected_plasticity.default_params, 'base_a_plus', 
                                    self.connected_plasticity.default_params.a_plus)
                base_a_minus = getattr(self.connected_plasticity.default_params, 'base_a_minus',
                                     self.connected_plasticity.default_params.a_minus)
                
                self.connected_plasticity.default_params.a_plus = base_a_plus * combined_plasticity
                self.connected_plasticity.default_params.a_minus = base_a_minus * combined_plasticity
                
                # Store base values
                if not hasattr(self.connected_plasticity.default_params, 'base_a_plus'):
                    self.connected_plasticity.default_params.base_a_plus = base_a_plus
                if not hasattr(self.connected_plasticity.default_params, 'base_a_minus'):
                    self.connected_plasticity.default_params.base_a_minus = base_a_minus
                    
    def _apply_timing_modulation(self, dt: float):
        """Apply neuromodulation effects to biological timing circuits.""" 
        if not self.connected_timing:
            return
            
        # Get oscillation-related effects
        for modulator_name, system in self.modulator_systems.items():
            effects = system.get_modulation_effects()
            
            # Apply specific oscillation modulations
            if modulator_name == ModulatorType.ACETYLCHOLINE.value:
                # ACh enhances gamma oscillations
                if 'gamma_oscillations' in effects and 'gamma' in self.connected_timing.oscillators:
                    gamma_osc = self.connected_timing.oscillators['gamma']
                    base_amplitude = getattr(gamma_osc, 'base_amplitude', gamma_osc.amplitude)
                    gamma_osc.amplitude = base_amplitude * effects['gamma_oscillations']
                    if not hasattr(gamma_osc, 'base_amplitude'):
                        gamma_osc.base_amplitude = base_amplitude
                        
                # ACh modulates theta oscillations
                if 'theta_oscillations' in effects and 'theta' in self.connected_timing.oscillators:
                    theta_osc = self.connected_timing.oscillators['theta']
                    base_amplitude = getattr(theta_osc, 'base_amplitude', theta_osc.amplitude)
                    theta_osc.amplitude = base_amplitude * effects['theta_oscillations']
                    if not hasattr(theta_osc, 'base_amplitude'):
                        theta_osc.base_amplitude = base_amplitude
                        
    def _update_performance_metrics(self, system_results: Dict[str, Dict[str, Any]],
                                  functional_synergies: Dict[str, float]):
        """Update system performance metrics."""
        # Concentration stability
        concentrations = [result['concentration'] for result in system_results.values()]
        self.performance_metrics['concentration_stability'] = 1.0 / (1.0 + np.std(concentrations))
        
        # Functional coherence
        self.performance_metrics['functional_coherence'] = functional_synergies.get('cognitive_coherence', 0.0)
        
        # System responsiveness (how much concentrations change)
        if len(self.system_history) > 1:
            prev_concentrations = self.system_history[-2]['concentrations']
            current_concentrations = self.system_history[-1]['concentrations']
            
            changes = []
            for mod_name in prev_concentrations:
                if mod_name in current_concentrations:
                    change = abs(current_concentrations[mod_name] - prev_concentrations[mod_name])
                    changes.append(change)
                    
            self.performance_metrics['system_responsiveness'] = np.mean(changes) if changes else 0.0
            
        # Homeostatic balance (distance from baseline)
        baseline_deviations = []
        for modulator_name, system in self.modulator_systems.items():
            deviation = abs(system.concentration - system.parameters.baseline_concentration)
            normalized_deviation = deviation / system.parameters.baseline_concentration
            baseline_deviations.append(normalized_deviation)
            
        self.performance_metrics['homeostatic_balance'] = 1.0 / (1.0 + np.mean(baseline_deviations))
        
        # Overall system health
        health_components = [
            self.performance_metrics['concentration_stability'],
            self.performance_metrics['functional_coherence'],
            self.performance_metrics['homeostatic_balance']
        ]
        self.performance_metrics['system_health'] = np.mean(health_components)
        
    def _compute_system_coherence(self) -> float:
        """Compute overall system coherence measure."""
        if len(self.system_history) < 2:
            return 1.0
            
        # Coherence based on cross-correlations between modulator systems
        concentrations_history = []
        for state in list(self.system_history)[-100:]:  # Last 100 timepoints
            conc_vector = []
            for mod_name in [ModulatorType.DOPAMINE.value, ModulatorType.SEROTONIN.value,
                           ModulatorType.NOREPINEPHRINE.value, ModulatorType.ACETYLCHOLINE.value]:
                conc_vector.append(state['concentrations'].get(mod_name, 0.0))
            concentrations_history.append(conc_vector)
            
        if len(concentrations_history) < 2:
            return 1.0
            
        # Compute correlation matrix
        conc_array = np.array(concentrations_history)
        if conc_array.shape[0] < 2 or conc_array.shape[1] < 2:
            return 1.0
            
        try:
            correlation_matrix = np.corrcoef(conc_array.T)
            
            # System coherence as mean absolute correlation
            n_systems = correlation_matrix.shape[0]
            correlations = []
            for i in range(n_systems):
                for j in range(i+1, n_systems):
                    if not np.isnan(correlation_matrix[i, j]):
                        correlations.append(abs(correlation_matrix[i, j]))
                        
            coherence = np.mean(correlations) if correlations else 0.0
            return np.clip(coherence, 0.0, 1.0)
            
        except Exception as e:
            logger.warning(f"Error computing system coherence: {e}")
            return 1.0
            
    def set_environmental_signal(self, signal_type: str, value: float):
        """Set environmental signal value."""
        if signal_type in self.environmental_inputs:
            self.environmental_inputs[signal_type] = np.clip(value, 0.0, 1.0)
            logger.debug(f"Set {signal_type} = {value}")
        else:
            logger.warning(f"Unknown environmental signal: {signal_type}")
            
    def trigger_reward_learning(self, predicted_reward: float, actual_reward: float):
        """Trigger reward-based learning via dopamine system with FIXED precision."""
        try:
            da_system = self.modulator_systems.get(ModulatorType.DOPAMINE.value)
            if isinstance(da_system, DopamineSystem):
                rpe = da_system.update_reward_prediction_error(predicted_reward, actual_reward)
                self.environmental_inputs['reward_signal'] = max(0.0, float(rpe))
                logger.debug(f"Triggered reward learning: RPE = {rpe:.6f}")
        except Exception as e:
            logger.warning(f"Error in reward learning trigger: {e}")
            
    def modulate_cognitive_state(self, attention_demand: float = 0.5,
                               learning_context: float = 0.0,
                               stress_level: float = 0.0,
                               social_context: float = 0.5) -> Dict[str, float]:
        """Modulate cognitive state through coordinated neuromodulation."""
        # Update environmental signals
        self.set_environmental_signal('task_demand', attention_demand)
        self.set_environmental_signal('stress_signal', stress_level)
        self.set_environmental_signal('social_signal', social_context)
        
        # Get current system state
        concentrations = self._get_current_concentrations()
        
        # Compute cognitive state
        cognitive_state = {
            'attention_level': self.global_state['attention_focus'],
            'learning_facilitation': self.global_state['learning_context'],
            'arousal_level': self.global_state['arousal_level'],
            'mood_state': self.global_state['mood_state'],
            'stress_level': self.global_state['stress_level'],
            'cognitive_load': self.global_state['cognitive_load']
        }
        
        # Add modulator-specific cognitive effects
        for modulator_name, system in self.modulator_systems.items():
            if hasattr(system, 'get_cognitive_enhancement_profile'):
                profile = system.get_cognitive_enhancement_profile()
                cognitive_state[f'{modulator_name}_enhancement'] = profile.get('cognitive_enhancement', 1.0)
                
        return cognitive_state
        
    def get_system_state(self) -> Dict[str, Any]:
        """Get comprehensive system state with FIXED error handling."""
        try:
            return {
                'concentrations': self._get_current_concentrations(),
                'global_state': self.global_state.copy(),
                'environmental_inputs': self.environmental_inputs.copy(),
                'performance_metrics': self.performance_metrics.copy(),
                'modulator_effects': {
                    name: system.get_modulation_effects() if hasattr(system, 'get_modulation_effects') else {}
                    for name, system in self.modulator_systems.items()
                },
                'receptor_states': self._get_receptor_states(),
                'system_coherence': self._compute_system_coherence(),
                'functional_synergies': self._compute_functional_synergies(self._get_current_concentrations()),
                'system_health': self.performance_metrics.get('system_health', 1.0)
            }
        except Exception as e:
            logger.error(f"Error getting system state: {e}")
            return {
                'concentrations': {},
                'global_state': self.global_state.copy(),
                'error': str(e)
            }
        
    def _get_receptor_states(self) -> Dict[str, Dict[str, float]]:
        """Get receptor population states for all systems."""
        receptor_states = {}
        for modulator_name, system in self.modulator_systems.items():
            receptor_states[modulator_name] = {}
            for receptor_type, population in system.receptor_populations.items():
                receptor_states[modulator_name][receptor_type.value] = population.population_states
        return receptor_states
        
    def reset_system(self):
        """Reset entire neuromodulation system to baseline state with FIXED cleanup."""
        try:
            # Reset all modulator systems
            for system in self.modulator_systems.values():
                system.concentration = system.parameters.baseline_concentration
                system.concentration_history.clear()
                system.effect_history.clear()
                
                # FIXED: Reset system-specific states
                if hasattr(system, 'reward_prediction_error'):
                    system.reward_prediction_error = 0.0
                if hasattr(system, 'stress_level'):
                    system.stress_level = 0.0
                if hasattr(system, 'arousal_level'):
                    system.arousal_level = 0.5
                    
            # Reset global state
            self.global_state = {
                'arousal_level': 0.5,
                'attention_focus': 0.5,
                'learning_context': 0.0,
                'mood_state': 0.5,
                'stress_level': 0.0,
                'cognitive_load': 0.0
            }
            
            # Reset environmental inputs
            self.environmental_inputs = {key: 0.0 for key in self.environmental_inputs}
            self.environmental_inputs['circadian_phase'] = 0.0
            
            # Clear history
            self.system_history.clear()
            self.performance_metrics.clear()
            
            logger.info("NeuromodulationSystem reset to baseline state")
            
        except Exception as e:
            logger.error(f"Error resetting system: {e}")


# ===========================================================================================
# EXPORT DEFINITIONS AND MODULE INITIALIZATION
# ===========================================================================================

__all__ = [
    # Core enumerations and data structures
    'ModulatorType', 'ReceptorSubtype', 'ModulationTarget', 'TemporalScale',
    'ReceptorParameters', 'ModulatorParameters', 'SpatialConfiguration',
    
    # Receptor modeling
    'ReceptorKinetics', 'ReceptorPopulation',
    
    # Base neuromodulator system
    'BaseNeuromodulator',
    
    # Specific modulator systems
    'DopamineSystem', 'SerotoninSystem', 'NorepinephrineSystem', 'AcetylcholineSystem',
    
    # Cross-modulator interactions
    'CrossModulatorInteractions',
    
    # Main neuromodulation system
    'NeuromodulationSystem'
]

# Module version
__version__ = '1.0.0'

# Module metadata
__author__ = 'ULTRA Development Team'
__description__ = 'Comprehensive Neuromodulation System for ULTRA: Biologically-inspired modulator integration'
__license__ = 'MIT'

# Initialize logging for the module
logger.info(f"ULTRA Neuromodulation System v{__version__} loaded successfully")
logger.info(f"Available modulator systems: {', '.join([mt.value for mt in ModulatorType])}")

# Cleanup function for module shutdown
def _cleanup():
    """Cleanup function called on module shutdown."""
    logger.info("Cleaning up Neuromodulation System resources")
    gc.collect()

import atexit
atexit.register(_cleanup)

logger.info("ULTRA Neuromodulation System initialization complete")






