#!/usr/bin/env python3
"""
ULTRA: Synaptic Pruning Module

This module implements the Synaptic Pruning Module for the ULTRA architecture, which
systematically eliminates weak or unused synaptic connections to improve efficiency,
reduce overfitting, and enhance signal-to-noise ratio in neural processing.

The module implements biologically-inspired pruning mechanisms that mimic developmental
and experience-dependent synaptic pruning in the brain, allowing neural networks to
adaptively refine their connectivity based on utility, activity, and redundancy measures.

Classes:
    PruningStrategy: Base class for pruning strategies
    WeightBasedPruning: Prunes connections based on weight magnitude
    ActivityBasedPruning: Prunes connections based on activity level
    RedundancyPruning: Prunes connections that carry redundant information
    GradientBasedPruning: Prunes connections based on gradient magnitude
    CompositePruning: Combines multiple pruning strategies
    PruningSchedule: Schedules for when and how much to prune
    SynapticPruningModule: Main module that manages the pruning process
    PruningParameters: Configuration parameters for pruning
    SynapticUtility: Measures the usefulness of synaptic connections
    PruningEvent: Records information about pruning events
    PruningLogger: Logs pruning events and statistics

Functions:
    create_developmental_pruning: Create pruning configuration mimicking developmental pruning
    create_efficient_pruning: Create pruning configuration optimized for efficiency
    initialize_pruning: Initialize the pruning module
"""

import numpy as np
import scipy.sparse
import logging
import time
import matplotlib.pyplot as plt
from enum import Enum, auto
from typing import Dict, List, Tuple, Set, Any, Optional, Union, Callable, Type
from dataclasses import dataclass, field
from collections import deque, defaultdict
import pickle
import os
import math
from abc import ABC, abstractmethod
import warnings
import json

# Configure module-level logger
logger = logging.getLogger(__name__)

class PruningType(Enum):
    """Types of pruning strategies."""
    WEIGHT_BASED = auto()    # Based on weight magnitude
    ACTIVITY_BASED = auto()  # Based on activity/usage
    REDUNDANCY = auto()      # Based on information redundancy
    GRADIENT_BASED = auto()  # Based on gradient magnitude
    UNCERTAINTY = auto()     # Based on weight uncertainty
    RANDOM = auto()          # Random pruning (baseline)
    COMPOSITE = auto()       # Combination of multiple strategies
    
    def __str__(self):
        return self.name.lower()
    
    @classmethod
    def from_string(cls, name: str) -> 'PruningType':
        """Create PruningType from string name (case insensitive)."""
        try:
            return cls[name.upper()]
        except KeyError:
            raise ValueError(f"Unknown pruning type: {name}")

class PruningScheduleType(Enum):
    """Types of pruning schedules."""
    ONE_SHOT = auto()       # One-time pruning
    LINEAR = auto()         # Linear pruning rate
    EXPONENTIAL = auto()    # Exponential decay of connections
    POLYNOMIAL = auto()     # Polynomial decay of connections
    COSINE = auto()         # Cosine annealing schedule
    CYCLIC = auto()         # Cyclic pruning (prune and regrow)
    ADAPTIVE = auto()       # Adaptive based on performance
    
    def __str__(self):
        return self.name.lower()
    
    @classmethod
    def from_string(cls, name: str) -> 'PruningScheduleType':
        """Create PruningScheduleType from string name (case insensitive)."""
        try:
            return cls[name.upper()]
        except KeyError:
            raise ValueError(f"Unknown pruning schedule type: {name}")

@dataclass
class PruningParameters:
    """Parameters for the synaptic pruning process."""
    # General parameters
    target_sparsity: float = 0.5       # Target sparsity level (0.0-1.0)
    initial_sparsity: float = 0.0      # Initial sparsity level (0.0-1.0)
    pruning_frequency: int = 1000      # How often to prune (time steps)
    update_frequency: int = 100        # How often to update utility metrics (time steps)
    min_age: int = 1000                # Minimum connection age before pruning
    exempt_connections: List[Tuple[int, int]] = field(default_factory=list)  # Connections to exempt from pruning
    
    # Strategy parameters
    weight_threshold: float = 0.01     # Weight magnitude threshold for pruning
    activity_threshold: float = 0.05   # Activity level threshold for pruning
    redundancy_threshold: float = 0.8  # Correlation threshold for redundancy
    gradient_threshold: float = 0.001  # Gradient magnitude threshold for pruning
    weight_factor: float = 0.6         # Weight factor for composite pruning
    activity_factor: float = 0.3       # Activity factor for composite pruning
    redundancy_factor: float = 0.1     # Redundancy factor for composite pruning
    
    # Schedule parameters
    schedule_type: PruningScheduleType = PruningScheduleType.EXPONENTIAL
    schedule_start: int = 1000         # When to start pruning (time steps)
    schedule_end: int = 100000         # When to end pruning (time steps)
    schedule_frequency: int = 1000     # How often to apply pruning (time steps)
    schedule_rate: float = 0.7         # Rate parameter for the schedule
    
    # Activity tracking parameters
    activity_window: int = 10000       # Time window for activity tracking (time steps)
    activity_decay: float = 0.999      # Decay factor for activity tracking
    
    # Regrowth parameters
    enable_regrowth: bool = False      # Whether to enable connection regrowth
    regrowth_rate: float = 0.1         # Rate of connection regrowth
    regrowth_strategy: str = "random"  # Strategy for connection regrowth
    
    # Experimental parameters
    importance_criterion: str = "magnitude"  # Criterion for connection importance
    protect_ratio: float = 0.1         # Ratio of important connections to protect
    random_protect: float = 0.01       # Ratio of random connections to protect
    
    def validate(self) -> bool:
        """
        Validate parameter values are within acceptable ranges.
        
        Returns:
            True if all parameters are valid, False otherwise
        """
        valid = True
        
        if not 0.0 <= self.target_sparsity <= 1.0:
            logger.warning("target_sparsity must be between 0.0 and 1.0")
            valid = False
            
        if not 0.0 <= self.initial_sparsity <= 1.0:
            logger.warning("initial_sparsity must be between 0.0 and 1.0")
            valid = False
            
        if self.initial_sparsity > self.target_sparsity:
            logger.warning("initial_sparsity should not exceed target_sparsity")
            valid = False
            
        if self.pruning_frequency <= 0:
            logger.warning("pruning_frequency must be positive")
            valid = False
            
        if self.update_frequency <= 0:
            logger.warning("update_frequency must be positive")
            valid = False
            
        if self.min_age < 0:
            logger.warning("min_age must be non-negative")
            valid = False
            
        if not 0.0 <= self.weight_factor + self.activity_factor + self.redundancy_factor <= 1.0:
            logger.warning("Sum of weight_factor, activity_factor, and redundancy_factor should not exceed 1.0")
            valid = False
            
        if self.schedule_start >= self.schedule_end:
            logger.warning("schedule_start should be less than schedule_end")
            valid = False
            
        if self.schedule_frequency <= 0:
            logger.warning("schedule_frequency must be positive")
            valid = False
            
        if not 0.0 <= self.activity_decay <= 1.0:
            logger.warning("activity_decay must be between 0.0 and 1.0")
            valid = False
            
        if self.enable_regrowth and not 0.0 <= self.regrowth_rate <= 1.0:
            logger.warning("regrowth_rate must be between 0.0 and 1.0")
            valid = False
            
        return valid

class SynapticUtility:
    """
    Calculates and tracks the utility of synaptic connections.
    
    Utility is a composite measure of how useful a connection is, based on
    its weight magnitude, activity level, and contribution to network function.
    """
    
    def __init__(self, n_connections: int, params: PruningParameters = None):
        """
        Initialize synaptic utility calculator.
        
        Args:
            n_connections: Number of connections to track
            params: Pruning parameters (uses defaults if None)
        """
        self.n_connections = n_connections
        self.params = params or PruningParameters()
        
        # Utility metrics
        self.weight_utility = np.ones(n_connections)  # Based on weight magnitude
        self.activity_utility = np.zeros(n_connections)  # Based on activity level
        self.redundancy_utility = np.ones(n_connections)  # Based on non-redundancy
        self.gradient_utility = np.ones(n_connections)  # Based on gradient magnitude
        self.saliency_utility = np.zeros(n_connections)  # Based on impact on loss
        
        # Combined utility
        self.total_utility = np.ones(n_connections)
        
        # Activity tracking
        self.activation_history = np.zeros(n_connections)  # Recent activation sum
        self.activation_count = np.zeros(n_connections, dtype=int)  # Number of activations
        
        # Connection age
        self.connection_age = np.zeros(n_connections, dtype=int)  # Age in time steps
        
        # Importance score (for protecting connections)
        self.importance_score = np.zeros(n_connections)
        
        # Tracking recent updates
        self.last_update_time = 0
        
        # Connection indices
        self.connection_indices = np.arange(n_connections)
        
        # Logger
        self.logger = logging.getLogger(f"{__name__}.SynapticUtility")
    
    def update_weights(self, weights: np.ndarray) -> None:
        """
        Update utility based on weight magnitudes.
        
        Args:
            weights: Array of connection weights
        """
        if len(weights) != self.n_connections:
            raise ValueError(f"Expected {self.n_connections} weights, got {len(weights)}")
            
        # Calculate utility based on weight magnitude
        weight_magnitude = np.abs(weights)
        
        # Normalize to [0, 1] if not all weights are zero
        if np.max(weight_magnitude) > 0:
            self.weight_utility = weight_magnitude / np.max(weight_magnitude)
        else:
            self.weight_utility = np.zeros_like(weight_magnitude)
    
    def update_activity(self, activations: np.ndarray, time_step: int) -> None:
        """
        Update utility based on connection activity.
        
        Args:
            activations: Array of connection activations (0 or 1)
            time_step: Current time step
        """
        if len(activations) != self.n_connections:
            raise ValueError(f"Expected {self.n_connections} activations, got {len(activations)}")
            
        # Apply decay to historical activations
        self.activation_history *= self.params.activity_decay
        
        # Add new activations
        self.activation_history += activations
        
        # Update activation count
        self.activation_count += (activations > 0).astype(int)
        
        # Normalize activity utility
        if np.max(self.activation_history) > 0:
            self.activity_utility = self.activation_history / np.max(self.activation_history)
        else:
            self.activity_utility = np.zeros_like(self.activation_history)
            
        # Increment connection age
        self.connection_age += 1
    
    def update_redundancy(self, correlation_matrix: np.ndarray) -> None:
        """
        Update utility based on connection redundancy.
        
        Args:
            correlation_matrix: Matrix of correlations between connections
        """
        # Calculate redundancy as average correlation with other connections
        n = correlation_matrix.shape[0]
        redundancy = np.zeros(n)
        
        for i in range(n):
            # Calculate mean correlation with all other connections
            correlations = correlation_matrix[i, :]
            correlations[i] = 0  # Exclude self-correlation
            if n > 1:
                redundancy[i] = np.sum(np.abs(correlations)) / (n - 1)
            else:
                redundancy[i] = 0
                
        # Convert redundancy to utility (1 - redundancy)
        # Higher correlation means higher redundancy, which means lower utility
        self.redundancy_utility = 1.0 - redundancy
    
    def update_gradients(self, gradients: np.ndarray) -> None:
        """
        Update utility based on weight gradients.
        
        Args:
            gradients: Array of weight gradients
        """
        if len(gradients) != self.n_connections:
            raise ValueError(f"Expected {self.n_connections} gradients, got {len(gradients)}")
            
        # Calculate utility based on gradient magnitude
        gradient_magnitude = np.abs(gradients)
        
        # Normalize to [0, 1] if not all gradients are zero
        if np.max(gradient_magnitude) > 0:
            self.gradient_utility = gradient_magnitude / np.max(gradient_magnitude)
        else:
            self.gradient_utility = np.zeros_like(gradient_magnitude)
    
    def update_saliency(self, saliency: np.ndarray) -> None:
        """
        Update utility based on connection saliency (impact on loss).
        
        Args:
            saliency: Array of connection saliency scores
        """
        if len(saliency) != self.n_connections:
            raise ValueError(f"Expected {self.n_connections} saliency scores, got {len(saliency)}")
            
        # Normalize to [0, 1] if not all saliency scores are zero
        if np.max(saliency) > 0:
            self.saliency_utility = saliency / np.max(saliency)
        else:
            self.saliency_utility = np.zeros_like(saliency)
    
    def compute_importance(self, weights: np.ndarray = None) -> None:
        """
        Compute importance scores for connections.
        
        Args:
            weights: Array of connection weights (uses previously provided weights if None)
        """
        if weights is not None:
            self.update_weights(weights)
            
        # Calculate importance based on specified criterion
        if self.params.importance_criterion == "magnitude":
            # Importance based on weight magnitude
            self.importance_score = self.weight_utility
        elif self.params.importance_criterion == "activity":
            # Importance based on activity
            self.importance_score = self.activity_utility
        elif self.params.importance_criterion == "composite":
            # Importance based on combined utility
            self.importance_score = self.total_utility
        elif self.params.importance_criterion == "saliency":
            # Importance based on saliency
            self.importance_score = self.saliency_utility
        else:
            # Default to weight magnitude
            self.importance_score = self.weight_utility
    
    def compute_total_utility(self) -> np.ndarray:
        """
        Compute total utility as a weighted combination of individual utilities.
        
        Returns:
            Array of total utility scores
        """
        # Combine individual utilities using factors from parameters
        self.total_utility = (
            self.params.weight_factor * self.weight_utility +
            self.params.activity_factor * self.activity_utility +
            self.params.redundancy_factor * self.redundancy_utility
        )
        
        # Ensure total utility is between 0 and 1
        self.total_utility = np.clip(self.total_utility, 0.0, 1.0)
        
        return self.total_utility
    
    def get_pruning_candidates(self, exempt_mask: np.ndarray = None) -> np.ndarray:
        """
        Get candidate connections for pruning based on utility.
        
        Args:
            exempt_mask: Boolean mask of connections exempt from pruning
            
        Returns:
            Boolean mask of connections that are candidates for pruning
        """
        # Compute total utility if not already computed
        self.compute_total_utility()
        
        # Create mask of connections eligible for pruning
        # Exclude connections younger than min_age
        age_mask = self.connection_age >= self.params.min_age
        
        # Initialize base mask with age constraint
        candidates = age_mask
        
        # Apply exempt mask if provided
        if exempt_mask is not None:
            candidates = candidates & ~exempt_mask
            
        return candidates
    
    def select_connections_to_prune(self, target_count: int, exempt_mask: np.ndarray = None) -> np.ndarray:
        """
        Select connections to prune based on utility.
        
        Args:
            target_count: Number of connections to select for pruning
            exempt_mask: Boolean mask of connections exempt from pruning
            
        Returns:
            Boolean mask of connections selected for pruning
        """
        # Get pruning candidates
        candidates_mask = self.get_pruning_candidates(exempt_mask)
        candidates_indices = np.where(candidates_mask)[0]
        
        # If no candidates, return empty mask
        if len(candidates_indices) == 0:
            self.logger.warning("No pruning candidates found")
            return np.zeros(self.n_connections, dtype=bool)
            
        # If fewer candidates than target, prune all candidates
        if len(candidates_indices) <= target_count:
            self.logger.warning(f"Only {len(candidates_indices)} pruning candidates found, " +
                             f"but requested to prune {target_count} connections")
            prune_mask = candidates_mask
        else:
            # Get utility of candidates
            candidate_utility = self.total_utility[candidates_mask]
            
            # Sort candidates by increasing utility
            sorted_indices = candidates_indices[np.argsort(candidate_utility)]
            
            # Select the specified number of lowest-utility connections
            selected_indices = sorted_indices[:target_count]
            
            # Create pruning mask
            prune_mask = np.zeros(self.n_connections, dtype=bool)
            prune_mask[selected_indices] = True
        
        return prune_mask
    
    def protect_important_connections(self, prune_mask: np.ndarray) -> np.ndarray:
        """
        Protect a portion of connections from pruning based on importance.
        
        Args:
            prune_mask: Boolean mask of connections selected for pruning
            
        Returns:
            Modified pruning mask with important connections protected
        """
        # Compute importance if not already done
        if np.all(self.importance_score == 0):
            self.compute_importance()
            
        # Get indices of connections marked for pruning
        prune_indices = np.where(prune_mask)[0]
        
        # If no connections marked for pruning, return the original mask
        if len(prune_indices) == 0:
            return prune_mask
            
        # Calculate number of connections to protect
        n_protect = int(self.params.protect_ratio * len(prune_indices))
        n_random_protect = int(self.params.random_protect * len(prune_indices))
        
        # If no connections to protect, return the original mask
        if n_protect == 0 and n_random_protect == 0:
            return prune_mask
            
        # Get importance scores of connections marked for pruning
        prune_importance = self.importance_score[prune_indices]
        
        # Sort by importance (highest first)
        sorted_indices = prune_indices[np.argsort(-prune_importance)]
        
        # Select the most important connections to protect
        protect_indices = sorted_indices[:n_protect]
        
        # Select random connections to protect
        if n_random_protect > 0:
            remaining_indices = sorted_indices[n_protect:]
            if len(remaining_indices) > 0:
                random_protect_indices = np.random.choice(
                    remaining_indices, 
                    size=min(n_random_protect, len(remaining_indices)), 
                    replace=False
                )
                protect_indices = np.concatenate([protect_indices, random_protect_indices])
        
        # Modify the pruning mask to protect these connections
        protected_mask = prune_mask.copy()
        protected_mask[protect_indices] = False
        
        return protected_mask
    
    def reset(self) -> None:
        """Reset utility tracking."""
        self.weight_utility = np.ones(self.n_connections)
        self.activity_utility = np.zeros(self.n_connections)
        self.redundancy_utility = np.ones(self.n_connections)
        self.gradient_utility = np.ones(self.n_connections)
        self.saliency_utility = np.zeros(self.n_connections)
        self.total_utility = np.ones(self.n_connections)
        self.activation_history = np.zeros(self.n_connections)
        self.activation_count = np.zeros(self.n_connections, dtype=int)
        self.connection_age = np.zeros(self.n_connections, dtype=int)
        self.importance_score = np.zeros(self.n_connections)
        self.last_update_time = 0

class PruningEvent:
    """
    Records information about a pruning event.
    """
    
    def __init__(self, time_step: int, strategy: str, n_pruned: int, 
                sparsity_before: float, sparsity_after: float,
                pruned_connections: List[Tuple[int, int]] = None,
                utility_stats: Dict[str, float] = None,
                region_stats: Dict[str, Dict[str, int]] = None):
        """
        Initialize a pruning event record.
        
        Args:
            time_step: Time step when pruning occurred
            strategy: Pruning strategy used
            n_pruned: Number of connections pruned
            sparsity_before: Sparsity before pruning
            sparsity_after: Sparsity after pruning
            pruned_connections: List of pruned connections (i, j)
            utility_stats: Statistics on connection utility
            region_stats: Statistics on pruning by region
        """
        self.time_step = time_step
        self.strategy = strategy
        self.n_pruned = n_pruned
        self.sparsity_before = sparsity_before
        self.sparsity_after = sparsity_after
        self.pruned_connections = pruned_connections or []
        self.utility_stats = utility_stats or {}
        self.region_stats = region_stats or {}
        self.timestamp = time.time()
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the event to a dictionary.
        
        Returns:
            Dictionary representation of the event
        """
        return {
            'time_step': self.time_step,
            'strategy': self.strategy,
            'n_pruned': self.n_pruned,
            'sparsity_before': self.sparsity_before,
            'sparsity_after': self.sparsity_after,
            'utility_stats': self.utility_stats,
            'region_stats': self.region_stats,
            'timestamp': self.timestamp
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PruningEvent':
        """
        Create a pruning event from a dictionary.
        
        Args:
            data: Dictionary representation of the event
            
        Returns:
            PruningEvent instance
        """
        return cls(
            time_step=data['time_step'],
            strategy=data['strategy'],
            n_pruned=data['n_pruned'],
            sparsity_before=data['sparsity_before'],
            sparsity_after=data['sparsity_after'],
            utility_stats=data.get('utility_stats'),
            region_stats=data.get('region_stats')
        )
    
    def __str__(self) -> str:
        """
        String representation of the pruning event.
        
        Returns:
            String description of the event
        """
        return (f"PruningEvent(time_step={self.time_step}, strategy={self.strategy}, "
                f"n_pruned={self.n_pruned}, sparsity: {self.sparsity_before:.3f} -> {self.sparsity_after:.3f})")

class PruningLogger:
    """
    Logs and analyzes pruning events.
    """
    
    def __init__(self, log_file: str = None, max_events: int = 1000):
        """
        Initialize a pruning logger.
        
        Args:
            log_file: Path to log file (if None, no file logging)
            max_events: Maximum number of events to keep in memory
        """
        self.events = deque(maxlen=max_events)
        self.log_file = log_file
        self.statistics = {
            'total_pruned': 0,
            'pruning_events': 0,
            'by_strategy': defaultdict(int),
            'by_region': defaultdict(int)
        }
        
        # Initialize log file if specified
        if log_file:
            with open(log_file, 'w') as f:
                f.write("time_step,strategy,n_pruned,sparsity_before,sparsity_after,timestamp\n")
        
        # Logger
        self.logger = logging.getLogger(f"{__name__}.PruningLogger")
    
    def log_event(self, event: PruningEvent) -> None:
        """
        Log a pruning event.
        
        Args:
            event: PruningEvent to log
        """
        # Add event to the deque
        self.events.append(event)
        
        # Update statistics
        self.statistics['total_pruned'] += event.n_pruned
        self.statistics['pruning_events'] += 1
        self.statistics['by_strategy'][event.strategy] += event.n_pruned
        
        # Update region statistics
        for region, stats in event.region_stats.items():
            self.statistics['by_region'][region] += stats.get('n_pruned', 0)
        
        # Log to file if specified
        if self.log_file:
            with open(self.log_file, 'a') as f:
                f.write(f"{event.time_step},{event.strategy},{event.n_pruned},"
                      f"{event.sparsity_before:.6f},{event.sparsity_after:.6f},{event.timestamp}\n")
        
        # Log to logger
        self.logger.info(str(event))
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Get pruning statistics.
        
        Returns:
            Dictionary of pruning statistics
        """
        return self.statistics
    
    def get_events(self, n: int = None) -> List[PruningEvent]:
        """
        Get recent pruning events.
        
        Args:
            n: Number of events to return (None = all)
            
        Returns:
            List of recent pruning events
        """
        if n is None or n >= len(self.events):
            return list(self.events)
        else:
            return list(self.events)[-n:]
    
    def get_sparsity_history(self) -> Tuple[List[int], List[float]]:
        """
        Get history of sparsity levels.
        
        Returns:
            Tuple of (time_steps, sparsities)
        """
        events = list(self.events)
        time_steps = [event.time_step for event in events]
        sparsities = [event.sparsity_after for event in events]
        return time_steps, sparsities
    
    def save_to_file(self, file_path: str) -> None:
        """
        Save pruning history to a file.
        
        Args:
            file_path: Path to save the history
        """
        data = {
            'events': [event.to_dict() for event in self.events],
            'statistics': self.statistics
        }
        
        with open(file_path, 'w') as f:
            json.dump(data, f, indent=2)
            
        self.logger.info(f"Saved pruning history to {file_path}")
    
    def load_from_file(self, file_path: str) -> None:
        """
        Load pruning history from a file.
        
        Args:
            file_path: Path to load the history from
        """
        with open(file_path, 'r') as f:
            data = json.load(f)
            
        self.events = deque([PruningEvent.from_dict(event_data) for event_data in data['events']],
                           maxlen=self.events.maxlen)
        self.statistics = data['statistics']
        
        self.logger.info(f"Loaded pruning history from {file_path}")
    
    def plot_sparsity_history(self, figsize: Tuple[int, int] = (10, 6)) -> plt.Figure:
        """
        Plot the history of sparsity levels.
        
        Args:
            figsize: Figure size (width, height)
            
        Returns:
            Matplotlib Figure
        """
        time_steps, sparsities = self.get_sparsity_history()
        
        fig, ax = plt.subplots(figsize=figsize)
        ax.plot(time_steps, sparsities, marker='o', linestyle='-', markersize=4)
        ax.set_xlabel('Time Step')
        ax.set_ylabel('Sparsity')
        ax.set_title('Network Sparsity Over Time')
        ax.grid(True, alpha=0.3)
        
        # Add annotations for significant pruning events
        for i, (t, s) in enumerate(zip(time_steps, sparsities)):
            event = list(self.events)[i]
            if event.n_pruned > 0.01 * self.statistics['total_pruned']:
                ax.annotate(f"{event.n_pruned}", 
                          (t, s),
                          textcoords="offset points", 
                          xytext=(0, 10), 
                          ha='center')
        
        plt.tight_layout()
        return fig
    
    def plot_strategy_distribution(self, figsize: Tuple[int, int] = (10, 6)) -> plt.Figure:
        """
        Plot the distribution of pruning by strategy.
        
        Args:
            figsize: Figure size (width, height)
            
        Returns:
            Matplotlib Figure
        """
        strategies = list(self.statistics['by_strategy'].keys())
        counts = [self.statistics['by_strategy'][s] for s in strategies]
        
        fig, ax = plt.subplots(figsize=figsize)
        ax.bar(strategies, counts)
        ax.set_xlabel('Pruning Strategy')
        ax.set_ylabel('Connections Pruned')
        ax.set_title('Pruning by Strategy')
        
        # Add value labels
        for i, count in enumerate(counts):
            ax.annotate(f"{count}", 
                      (i, count),
                      textcoords="offset points", 
                      xytext=(0, 5), 
                      ha='center')
        
        plt.tight_layout()
        return fig
    
    def reset(self) -> None:
        """Reset the logger."""
        self.events.clear()
        self.statistics = {
            'total_pruned': 0,
            'pruning_events': 0,
            'by_strategy': defaultdict(int),
            'by_region': defaultdict(int)
        }
        
        # Reset log file if specified
        if self.log_file:
            with open(self.log_file, 'w') as f:
                f.write("time_step,strategy,n_pruned,sparsity_before,sparsity_after,timestamp\n")

class PruningStrategy(ABC):
    """
    Base class for pruning strategies.
    """
    
    def __init__(self, params: PruningParameters = None):
        """
        Initialize pruning strategy.
        
        Args:
            params: Pruning parameters (uses defaults if None)
        """
        self.params = params or PruningParameters()
        self.utility = None  # Will be set when connected to a network
    
    @abstractmethod
    def select_connections_to_prune(self, weights: np.ndarray, utility: SynapticUtility, 
                                   target_count: int) -> np.ndarray:
        """
        Select connections to prune.
        
        Args:
            weights: Array of connection weights
            utility: SynapticUtility instance
            target_count: Number of connections to select for pruning
            
        Returns:
            Boolean mask of connections selected for pruning
        """
        pass
    
    def connect_utility(self, utility: SynapticUtility) -> None:
        """
        Connect to a utility calculator.
        
        Args:
            utility: SynapticUtility instance
        """
        self.utility = utility
    
    def get_type(self) -> str:
        """
        Get the type of pruning strategy.
        
        Returns:
            Strategy type name
        """
        return self.__class__.__name__

class WeightBasedPruning(PruningStrategy):
    """
    Prunes connections based on weight magnitude.
    
    This strategy selects connections with the smallest absolute weight values
    for pruning, which is a simple but effective approach for reducing network size
    without significantly impacting performance.
    """
    
    def select_connections_to_prune(self, weights: np.ndarray, utility: SynapticUtility, 
                                  target_count: int) -> np.ndarray:
        """
        Select connections to prune based on weight magnitude.
        
        Args:
            weights: Array of connection weights
            utility: SynapticUtility instance
            target_count: Number of connections to select for pruning
            
        Returns:
            Boolean mask of connections selected for pruning
        """
        # Update weight utility
        utility.update_weights(weights)
        
        # Get exempt mask based on weight threshold
        exempt_mask = np.abs(weights) > self.params.weight_threshold
        
        # Select connections to prune
        prune_mask = utility.select_connections_to_prune(target_count, exempt_mask)
        
        # Apply importance protection
        prune_mask = utility.protect_important_connections(prune_mask)
        
        return prune_mask

class ActivityBasedPruning(PruningStrategy):
    """
    Prunes connections based on activity level.
    
    This strategy selects connections with low activity (rarely used)
    for pruning, which can improve efficiency by removing connections
    that don't contribute significantly to network function.
    """
    
    def select_connections_to_prune(self, weights: np.ndarray, utility: SynapticUtility, 
                                  target_count: int) -> np.ndarray:
        """
        Select connections to prune based on activity level.
        
        Args:
            weights: Array of connection weights
            utility: SynapticUtility instance
            target_count: Number of connections to select for pruning
            
        Returns:
            Boolean mask of connections selected for pruning
        """
        # Compute total utility with activity as the primary factor
        temp_params = utility.params
        utility.params = PruningParameters(
            weight_factor=0.2,
            activity_factor=0.7,
            redundancy_factor=0.1
        )
        utility.compute_total_utility()
        utility.params = temp_params
        
        # Get exempt mask based on activity threshold
        exempt_mask = utility.activity_utility > self.params.activity_threshold
        
        # Select connections to prune
        prune_mask = utility.select_connections_to_prune(target_count, exempt_mask)
        
        # Apply importance protection
        prune_mask = utility.protect_important_connections(prune_mask)
        
        return prune_mask

class RedundancyPruning(PruningStrategy):
    """
    Prunes connections that carry redundant information.
    
    This strategy identifies connections that have highly correlated activity
    with other connections and selects the redundant ones for pruning, which can
    reduce network size while preserving most of the information capacity.
    """
    
    def __init__(self, params: PruningParameters = None):
        """
        Initialize redundancy pruning strategy.
        
        Args:
            params: Pruning parameters (uses defaults if None)
        """
        super().__init__(params)
        self.correlation_matrix = None
    
    def update_correlation_matrix(self, activation_history: np.ndarray) -> None:
        """
        Update the correlation matrix based on activation history.
        
        Args:
            activation_history: Matrix of activation history (connections x time)
        """
        # Calculate correlation matrix
        n_connections = activation_history.shape[0]
        self.correlation_matrix = np.zeros((n_connections, n_connections))
        
        # Compute correlation only if there's enough history
        if activation_history.shape[1] > 1:
            for i in range(n_connections):
                for j in range(i+1, n_connections):
                    # Calculate correlation between connection activations
                    corr = np.corrcoef(activation_history[i], activation_history[j])[0, 1]
                    if np.isnan(corr):
                        corr = 0.0
                    self.correlation_matrix[i, j] = corr
                    self.correlation_matrix[j, i] = corr
    
    def select_connections_to_prune(self, weights: np.ndarray, utility: SynapticUtility, 
                                  target_count: int) -> np.ndarray:
        """
        Select connections to prune based on redundancy.
        
        Args:
            weights: Array of connection weights
            utility: SynapticUtility instance
            target_count: Number of connections to select for pruning
            
        Returns:
            Boolean mask of connections selected for pruning
        """
        # If correlation matrix is not computed, fall back to weight-based pruning
        if self.correlation_matrix is None:
            return WeightBasedPruning(self.params).select_connections_to_prune(
                weights, utility, target_count)
        
        # Update redundancy utility
        utility.update_redundancy(self.correlation_matrix)
        
        # Compute total utility with redundancy as the primary factor
        temp_params = utility.params
        utility.params = PruningParameters(
            weight_factor=0.2,
            activity_factor=0.2,
            redundancy_factor=0.6
        )
        utility.compute_total_utility()
        utility.params = temp_params
        
        # Get exempt mask based on redundancy threshold
        exempt_mask = utility.redundancy_utility > (1.0 - self.params.redundancy_threshold)
        
        # Select connections to prune
        prune_mask = utility.select_connections_to_prune(target_count, exempt_mask)
        
        # Apply importance protection
        prune_mask = utility.protect_important_connections(prune_mask)
        
        return prune_mask

class GradientBasedPruning(PruningStrategy):
    """
    Prunes connections based on gradient magnitude.
    
    This strategy selects connections with small gradient magnitudes
    for pruning, which can improve efficiency by removing connections
    that don't contribute significantly to learning.
    """
    
    def select_connections_to_prune(self, weights: np.ndarray, utility: SynapticUtility, 
                                  target_count: int) -> np.ndarray:
        """
        Select connections to prune based on gradient magnitude.
        
        Args:
            weights: Array of connection weights
            utility: SynapticUtility instance
            target_count: Number of connections to select for pruning
            
        Returns:
            Boolean mask of connections selected for pruning
        """
        # If gradient utility is not computed, fall back to weight-based pruning
        if np.all(utility.gradient_utility == 1.0):
            return WeightBasedPruning(self.params).select_connections_to_prune(
                weights, utility, target_count)
                
        # Compute total utility with gradient as the primary factor
        temp_params = utility.params
        utility.params = PruningParameters(
            weight_factor=0.3,
            activity_factor=0.2,
            redundancy_factor=0.1
        )
        # Manually include gradient in total utility calculation
        utility.total_utility = (
            0.3 * utility.weight_utility +
            0.2 * utility.activity_utility +
            0.1 * utility.redundancy_utility +
            0.4 * utility.gradient_utility
        )
        utility.params = temp_params
        
        # Get exempt mask based on gradient threshold
        exempt_mask = utility.gradient_utility > self.params.gradient_threshold
        
        # Select connections to prune
        prune_mask = utility.select_connections_to_prune(target_count, exempt_mask)
        
        # Apply importance protection
        prune_mask = utility.protect_important_connections(prune_mask)
        
        return prune_mask

class CompositePruning(PruningStrategy):
    """
    Combines multiple pruning strategies.
    
    This strategy uses a weighted combination of different pruning criteria,
    which can provide more balanced and effective pruning by considering
    multiple factors simultaneously.
    """
    
    def __init__(self, params: PruningParameters = None, strategies: List[PruningStrategy] = None):
        """
        Initialize composite pruning strategy.
        
        Args:
            params: Pruning parameters (uses defaults if None)
            strategies: List of pruning strategies to combine (uses defaults if None)
        """
        super().__init__(params)
        
        # Create default strategies if not provided
        if strategies is None:
            strategies = [
                WeightBasedPruning(params),
                ActivityBasedPruning(params),
                RedundancyPruning(params)
            ]
            
        self.strategies = strategies
        
        # Default weights for combining strategies
        self.strategy_weights = [
            self.params.weight_factor,
            self.params.activity_factor,
            self.params.redundancy_factor
        ]
        
        # Normalize weights if they don't sum to 1
        if sum(self.strategy_weights) > 0:
            self.strategy_weights = [w / sum(self.strategy_weights) for w in self.strategy_weights]
        else:
            # Equal weights if all are zero
            self.strategy_weights = [1.0 / len(self.strategies)] * len(self.strategies)
    
    def select_connections_to_prune(self, weights: np.ndarray, utility: SynapticUtility, 
                                  target_count: int) -> np.ndarray:
        """
        Select connections to prune using a combination of strategies.
        
        Args:
            weights: Array of connection weights
            utility: SynapticUtility instance
            target_count: Number of connections to select for pruning
            
        Returns:
            Boolean mask of connections selected for pruning
        """
        # Initialize pruning scores
        n_connections = len(weights)
        pruning_scores = np.zeros(n_connections)
        
        # Connect utility to all strategies
        for strategy in self.strategies:
            strategy.connect_utility(utility)
        
        # Get pruning masks from each strategy
        for i, strategy in enumerate(self.strategies):
            if i < len(self.strategy_weights) and self.strategy_weights[i] > 0:
                strategy_mask = strategy.select_connections_to_prune(
                    weights, utility, target_count)
                    
                # Add to scores
                pruning_scores[strategy_mask] += self.strategy_weights[i]
        
        # Select connections with highest combined pruning scores
        # Get pruning candidates
        candidates_mask = utility.get_pruning_candidates()
        candidates_indices = np.where(candidates_mask)[0]
        
        # If no candidates, return empty mask
        if len(candidates_indices) == 0:
            return np.zeros(n_connections, dtype=bool)
            
        # If fewer candidates than target, prune all candidates
        if len(candidates_indices) <= target_count:
            prune_mask = candidates_mask
        else:
            # Get scores of candidates
            candidate_scores = pruning_scores[candidates_indices]
            
            # Sort candidates by decreasing score
            sorted_indices = candidates_indices[np.argsort(-candidate_scores)]
            
            # Select the specified number of highest-score connections
            selected_indices = sorted_indices[:target_count]
            
            # Create pruning mask
            prune_mask = np.zeros(n_connections, dtype=bool)
            prune_mask[selected_indices] = True
        
        # Apply importance protection
        prune_mask = utility.protect_important_connections(prune_mask)
        
        return prune_mask

class PruningSchedule:
    """
    Defines a schedule for pruning over time.
    """
    
    def __init__(self, schedule_type: PruningScheduleType = PruningScheduleType.EXPONENTIAL, 
                params: PruningParameters = None):
        """
        Initialize pruning schedule.
        
        Args:
            schedule_type: Type of pruning schedule
            params: Pruning parameters (uses defaults if None)
        """
        self.schedule_type = schedule_type
        self.params = params or PruningParameters()
        
        # Track schedule state
        self.current_sparsity = self.params.initial_sparsity
        self.target_sparsity = self.params.target_sparsity
        self.last_pruning_step = 0
        
        # Logger
        self.logger = logging.getLogger(f"{__name__}.PruningSchedule")
    
    def should_prune(self, time_step: int) -> bool:
        """
        Determine if pruning should be performed at the current time step.
        
        Args:
            time_step: Current time step
            
        Returns:
            True if pruning should be performed, False otherwise
        """
        # Don't prune before start time
        if time_step < self.params.schedule_start:
            return False
            
        # Don't prune after end time
        if time_step > self.params.schedule_end:
            return False
            
        # Check if enough time has passed since last pruning
        if time_step - self.last_pruning_step < self.params.schedule_frequency:
            return False
            
        # Check if target sparsity already reached
        if self.current_sparsity >= self.target_sparsity:
            return False
            
        return True
    
    def get_target_sparsity(self, time_step: int) -> float:
        """
        Get the target sparsity for the current time step.
        
        Args:
            time_step: Current time step
            
        Returns:
            Target sparsity level (0.0-1.0)
        """
        # Don't exceed final target sparsity
        if time_step >= self.params.schedule_end:
            return self.params.target_sparsity
            
        # Don't prune before start time
        if time_step < self.params.schedule_start:
            return self.params.initial_sparsity
            
        # Calculate progress through schedule
        schedule_progress = (time_step - self.params.schedule_start) / (
            self.params.schedule_end - self.params.schedule_start)
        
        # Clamp progress to [0, 1]
        schedule_progress = max(0.0, min(1.0, schedule_progress))
        
        # Calculate target sparsity based on schedule type
        if self.schedule_type == PruningScheduleType.ONE_SHOT:
            # One-shot pruning at the beginning of the schedule
            if schedule_progress > 0:
                return self.params.target_sparsity
            else:
                return self.params.initial_sparsity
                
        elif self.schedule_type == PruningScheduleType.LINEAR:
            # Linear increase in sparsity
            return self.params.initial_sparsity + schedule_progress * (
                self.params.target_sparsity - self.params.initial_sparsity)
                
        elif self.schedule_type == PruningScheduleType.EXPONENTIAL:
            # Exponential increase in sparsity
            return self.params.initial_sparsity + (
                1.0 - np.exp(-self.params.schedule_rate * schedule_progress)) * (
                self.params.target_sparsity - self.params.initial_sparsity)
                
        elif self.schedule_type == PruningScheduleType.POLYNOMIAL:
            # Polynomial increase in sparsity
            return self.params.initial_sparsity + (schedule_progress ** self.params.schedule_rate) * (
                self.params.target_sparsity - self.params.initial_sparsity)
                
        elif self.schedule_type == PruningScheduleType.COSINE:
            # Cosine annealing
            return self.params.initial_sparsity + (
                0.5 - 0.5 * np.cos(np.pi * schedule_progress)) * (
                self.params.target_sparsity - self.params.initial_sparsity)
                
        elif self.schedule_type == PruningScheduleType.CYCLIC:
            # Cyclic pruning with regrowth
            cycle = (time_step - self.params.schedule_start) // self.params.schedule_frequency
            cycle_progress = ((time_step - self.params.schedule_start) % self.params.schedule_frequency
                            ) / self.params.schedule_frequency
            
            if cycle % 2 == 0:
                # Pruning phase
                return self.current_sparsity + cycle_progress * self.params.schedule_rate * (
                    self.params.target_sparsity - self.current_sparsity)
            else:
                # Regrowth phase
                return self.current_sparsity - cycle_progress * self.params.regrowth_rate * (
                    self.current_sparsity - self.params.initial_sparsity)
                
        elif self.schedule_type == PruningScheduleType.ADAPTIVE:
            # Adaptive based on performance (placeholder - actual implementation would use performance metrics)
            return self.current_sparsity + self.params.schedule_rate * (
                self.params.target_sparsity - self.current_sparsity)
        
        # Default fallback
        return self.params.initial_sparsity
    
    def update(self, time_step: int, current_sparsity: float) -> None:
        """
        Update schedule state after pruning.
        
        Args:
            time_step: Current time step
            current_sparsity: Current network sparsity
        """
        self.last_pruning_step = time_step
        self.current_sparsity = current_sparsity
    
    def calculate_pruning_amount(self, current_connections: int, time_step: int) -> int:
        """
        Calculate how many connections to prune at the current time step.
        
        Args:
            current_connections: Current number of connections
            time_step: Current time step
            
        Returns:
            Number of connections to prune
        """
        # Calculate current and target sparsity
        current_sparsity = self.current_sparsity
        target_sparsity = self.get_target_sparsity(time_step)
        
        # Current and target numbers of connections
        total_possible_connections = current_connections / (1.0 - current_sparsity)
        current_pruned = current_sparsity * total_possible_connections
        target_pruned = target_sparsity * total_possible_connections
        
        # Calculate number of connections to prune
        prune_count = int(target_pruned - current_pruned)
        
        # Ensure non-negative
        prune_count = max(0, prune_count)
        
        # Ensure we don't prune too many connections
        prune_count = min(prune_count, current_connections - 1)
        
        return prune_count
    
    def reset(self) -> None:
        """Reset schedule state."""
        self.current_sparsity = self.params.initial_sparsity
        self.last_pruning_step = 0

class SynapticPruningModule:
    """
    Main module responsible for managing the synaptic pruning process.
    
    This module integrates pruning strategies, schedules, and utility measures
    to systematically remove unnecessary connections from neural networks.
    """
    
    def __init__(self, params: PruningParameters = None):
        """
        Initialize synaptic pruning module.
        
        Args:
            params: Pruning parameters (uses defaults if None)
        """
        self.params = params or PruningParameters()
        
        # Validate parameters
        if not self.params.validate():
            logger.warning("Some pruning parameters are invalid")
        
        # Create default strategy
        self.strategy = CompositePruning(self.params)
        
        # Create default schedule
        self.schedule = PruningSchedule(self.params.schedule_type, self.params)
        
        # Create logger
        self.logger = PruningLogger()
        
        # Network state
        self.connected_to_core = False
        self.core = None
        self.utility = None
        self.current_time = 0
        self.n_connections = 0
        self.current_sparsity = self.params.initial_sparsity
        
        # Mapping from flat connection indices to (i,j) pairs
        self.connection_map = []
        
        # Create module logger
        self.module_logger = logging.getLogger(f"{__name__}.SynapticPruningModule")
    
    def connect_to_core(self, core) -> None:
        """
        Connect the pruning module to a neuromorphic core.
        
        Args:
            core: NeuromorphicCore instance
        """
        self.core = core
        self.connected_to_core = True
        
        # Get number of connections
        self.connection_map = core.get_connection_pairs()
        self.n_connections = len(self.connection_map)
        
        # Create utility calculator
        self.utility = SynapticUtility(self.n_connections, self.params)
        
        # Set utility in strategy
        self.strategy.connect_utility(self.utility)
        
        self.module_logger.info(f"Connected to Neuromorphic Core with {self.n_connections} connections")
    
    def set_strategy(self, strategy: PruningStrategy) -> None:
        """
        Set the pruning strategy.
        
        Args:
            strategy: PruningStrategy instance
        """
        self.strategy = strategy
        
        # Connect utility if available
        if self.utility is not None:
            self.strategy.connect_utility(self.utility)
            
        self.module_logger.info(f"Set pruning strategy to {strategy.get_type()}")
    
    def set_schedule(self, schedule: PruningSchedule) -> None:
        """
        Set the pruning schedule.
        
        Args:
            schedule: PruningSchedule instance
        """
        self.schedule = schedule
        self.module_logger.info(f"Set pruning schedule to {schedule.schedule_type}")
    
    def step(self, dt: float) -> None:
        """
        Update the pruning module for a time step.
        
        Args:
            dt: Time step size (ms)
        """
        if not self.connected_to_core:
            return
            
        # Update current time
        self.current_time += dt
        
        # Only execute at integer time steps
        if int(self.current_time) <= int(self.current_time - dt):
            return
            
        # Convert to integer time step
        time_step = int(self.current_time)
        
        # Update utility at the specified frequency
        if time_step % self.params.update_frequency == 0:
            self._update_utility(time_step)
        
        # Check if pruning should be performed
        if self.schedule.should_prune(time_step):
            self._prune_connections(time_step)
    
    def _update_utility(self, time_step: int) -> None:
        """
        Update utility measures.
        
        Args:
            time_step: Current time step
        """
        if not self.connected_to_core:
            return
            
        # Get current weights
        weights = self.core.get_connection_weights()
        
        # Get current activations
        activations = self.core.get_connection_activations()
        
        # Update utility measures
        self.utility.update_weights(weights)
        self.utility.update_activity(activations, time_step)
        
        # If redundancy pruning is used, update correlation matrix
        for strategy in [self.strategy] if not isinstance(self.strategy, CompositePruning) else self.strategy.strategies:
            if isinstance(strategy, RedundancyPruning):
                # Get activation history from core if available
                if hasattr(self.core, 'get_activation_history'):
                    activation_history = self.core.get_activation_history()
                    strategy.update_correlation_matrix(activation_history)
        
        # If gradient-based pruning is used, update gradients
        for strategy in [self.strategy] if not isinstance(self.strategy, CompositePruning) else self.strategy.strategies:
            if isinstance(strategy, GradientBasedPruning):
                # Get weight gradients from core if available
                if hasattr(self.core, 'get_weight_gradients'):
                    gradients = self.core.get_weight_gradients()
                    self.utility.update_gradients(gradients)
    
    def _prune_connections(self, time_step: int) -> None:
        """
        Prune connections based on the current strategy and schedule.
        
        Args:
            time_step: Current time step
        """
        if not self.connected_to_core:
            return
            
        # Get current weights
        weights = self.core.get_connection_weights()
        
        # Calculate how many connections to prune
        prune_count = self.schedule.calculate_pruning_amount(self.n_connections, time_step)
        
        # Skip if no pruning needed
        if prune_count <= 0:
            return
            
        # Get pruning mask
        prune_mask = self.strategy.select_connections_to_prune(weights, self.utility, prune_count)
        
        # Get indices to prune
        prune_indices = np.where(prune_mask)[0]
        actual_prune_count = len(prune_indices)
        
        # Convert to connection pairs
        prune_connections = [self.connection_map[i] for i in prune_indices]
        
        # Log before pruning
        before_sparsity = self.current_sparsity
        
        # Prune connections in core
        if hasattr(self.core, 'prune_connections'):
            self.core.prune_connections(prune_connections)
        
        # Update current sparsity
        total_possible = self.n_connections / (1.0 - before_sparsity)
        pruned_count = before_sparsity * total_possible + actual_prune_count
        self.current_sparsity = pruned_count / total_possible
        
        # Update schedule
        self.schedule.update(time_step, self.current_sparsity)
        
        # Update connection count
        self.n_connections -= actual_prune_count
        
        # Log pruning event
        event = PruningEvent(
            time_step=time_step,
            strategy=self.strategy.get_type(),
            n_pruned=actual_prune_count,
            sparsity_before=before_sparsity,
            sparsity_after=self.current_sparsity,
            pruned_connections=prune_connections,
            utility_stats={
                'weight_utility_mean': float(np.mean(self.utility.weight_utility)),
                'activity_utility_mean': float(np.mean(self.utility.activity_utility)),
                'redundancy_utility_mean': float(np.mean(self.utility.redundancy_utility)),
                'total_utility_mean': float(np.mean(self.utility.total_utility))
            }
        )
        self.logger.log_event(event)
        
        self.module_logger.info(f"Pruned {actual_prune_count} connections at time step {time_step}, "
                             f"sparsity: {before_sparsity:.3f} -> {self.current_sparsity:.3f}")
    
    def prune_manually(self, count: int = None, target_sparsity: float = None) -> int:
        """
        Manually trigger pruning.
        
        Args:
            count: Number of connections to prune (overrides target_sparsity)
            target_sparsity: Target sparsity level (used if count is None)
            
        Returns:
            Number of connections pruned
        """
        if not self.connected_to_core:
            self.module_logger.warning("Cannot prune: not connected to core")
            return 0
            
        # Calculate pruning count if not specified
        if count is None:
            if target_sparsity is None:
                target_sparsity = self.params.target_sparsity
                
            # Calculate number of connections to prune
            total_possible = self.n_connections / (1.0 - self.current_sparsity)
            current_pruned = self.current_sparsity * total_possible
            target_pruned = target_sparsity * total_possible
            count = int(target_pruned - current_pruned)
            
            # Ensure non-negative
            count = max(0, count)
        
        # Skip if no pruning needed
        if count <= 0:
            self.module_logger.info("No pruning needed")
            return 0
            
        # Get current weights
        weights = self.core.get_connection_weights()
        
        # Get pruning mask
        prune_mask = self.strategy.select_connections_to_prune(weights, self.utility, count)
        
        # Get indices to prune
        prune_indices = np.where(prune_mask)[0]
        actual_prune_count = len(prune_indices)
        
        # Convert to connection pairs
        prune_connections = [self.connection_map[i] for i in prune_indices]
        
        # Log before pruning
        before_sparsity = self.current_sparsity
        
        # Prune connections in core
        if hasattr(self.core, 'prune_connections'):
            self.core.prune_connections(prune_connections)
        
        # Update current sparsity
        total_possible = self.n_connections / (1.0 - before_sparsity)
        pruned_count = before_sparsity * total_possible + actual_prune_count
        self.current_sparsity = pruned_count / total_possible
        
        # Update connection count
        self.n_connections -= actual_prune_count
        
        # Log pruning event
        event = PruningEvent(
            time_step=int(self.current_time),
            strategy=self.strategy.get_type(),
            n_pruned=actual_prune_count,
            sparsity_before=before_sparsity,
            sparsity_after=self.current_sparsity,
            pruned_connections=prune_connections,
            utility_stats={
                'weight_utility_mean': float(np.mean(self.utility.weight_utility)),
                'activity_utility_mean': float(np.mean(self.utility.activity_utility)),
                'redundancy_utility_mean': float(np.mean(self.utility.redundancy_utility)),
                'total_utility_mean': float(np.mean(self.utility.total_utility))
            }
        )
        self.logger.log_event(event)
        
        self.module_logger.info(f"Manually pruned {actual_prune_count} connections, "
                             f"sparsity: {before_sparsity:.3f} -> {self.current_sparsity:.3f}")
        
        return actual_prune_count
    
    def get_current_sparsity(self) -> float:
        """
        Get the current network sparsity.
        
        Returns:
            Current sparsity level (0.0-1.0)
        """
        return self.current_sparsity
    
    def get_pruning_schedule(self) -> List[Tuple[int, float]]:
        """
        Get the pruning schedule as a list of (time_step, target_sparsity) tuples.
        
        Returns:
            List of (time_step, target_sparsity) tuples
        """
        schedule = []
        
        # Start at schedule start and sample at schedule frequency
        for time_step in range(self.params.schedule_start, 
                             self.params.schedule_end + 1, 
                             self.params.schedule_frequency):
            target_sparsity = self.schedule.get_target_sparsity(time_step)
            schedule.append((time_step, target_sparsity))
            
        return schedule
    
    def get_pruning_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about the pruning process.
        
        Returns:
            Dictionary of pruning statistics
        """
        return self.logger.get_statistics()
    
    def plot_sparsity_history(self) -> plt.Figure:
        """
        Plot the history of sparsity levels.
        
        Returns:
            Matplotlib Figure
        """
        return self.logger.plot_sparsity_history()
    
    def visualize_utility(self, figsize: Tuple[int, int] = (12, 8)) -> plt.Figure:
        """
        Visualize utility measures.
        
        Args:
            figsize: Figure size (width, height)
            
        Returns:
            Matplotlib Figure
        """
        if self.utility is None:
            fig, ax = plt.subplots(figsize=figsize)
            ax.text(0.5, 0.5, "No utility data available", 
                   horizontalalignment='center', verticalalignment='center')
            ax.set_axis_off()
            return fig
        
        # Compute total utility
        self.utility.compute_total_utility()
        
        # Create figure with subplots
        fig, axes = plt.subplots(2, 2, figsize=figsize)
        
        # Flatten axes for easier iteration
        axes = axes.flatten()
        
        # Plot weight utility
        axes[0].hist(self.utility.weight_utility, bins=20, alpha=0.7)
        axes[0].set_title("Weight Utility")
        axes[0].set_xlabel("Utility")
        axes[0].set_ylabel("Count")
        axes[0].grid(True, alpha=0.3)
        
        # Plot activity utility
        axes[1].hist(self.utility.activity_utility, bins=20, alpha=0.7)
        axes[1].set_title("Activity Utility")
        axes[1].set_xlabel("Utility")
        axes[1].set_ylabel("Count")
        axes[1].grid(True, alpha=0.3)
        
        # Plot redundancy utility
        axes[2].hist(self.utility.redundancy_utility, bins=20, alpha=0.7)
        axes[2].set_title("Redundancy Utility")
        axes[2].set_xlabel("Utility")
        axes[2].set_ylabel("Count")
        axes[2].grid(True, alpha=0.3)
        
        # Plot total utility
        axes[3].hist(self.utility.total_utility, bins=20, alpha=0.7)
        axes[3].set_title("Total Utility")
        axes[3].set_xlabel("Utility")
        axes[3].set_ylabel("Count")
        axes[3].grid(True, alpha=0.3)
        
        plt.tight_layout()
        return fig
    
    def reset(self) -> None:
        """Reset the pruning module."""
        self.current_time = 0
        self.current_sparsity = self.params.initial_sparsity
        
        if self.utility is not None:
            self.utility.reset()
            
        self.schedule.reset()
        self.logger.reset()
        
        self.module_logger.info("Reset SynapticPruningModule")
    
    def save_state(self, file_path: str) -> None:
        """
        Save the state of the pruning module.
        
        Args:
            file_path: Path to save the state
        """
        state = {
            'current_time': self.current_time,
            'current_sparsity': self.current_sparsity,
            'n_connections': self.n_connections,
            'params': self.params.__dict__,
            'schedule_state': {
                'current_sparsity': self.schedule.current_sparsity,
                'last_pruning_step': self.schedule.last_pruning_step
            }
        }
        
        with open(file_path, 'wb') as f:
            pickle.dump(state, f)
            
        # Save pruning history separately
        history_path = os.path.splitext(file_path)[0] + '_history.json'
        self.logger.save_to_file(history_path)
        
        self.module_logger.info(f"Saved pruning state to {file_path}")
    
    def load_state(self, file_path: str) -> None:
        """
        Load the state of the pruning module.
        
        Args:
            file_path: Path to load the state from
        """
        with open(file_path, 'rb') as f:
            state = pickle.load(f)
            
        self.current_time = state['current_time']
        self.current_sparsity = state['current_sparsity']
        self.n_connections = state['n_connections']
        
        # Load parameters
        for key, value in state['params'].items():
            setattr(self.params, key, value)
            
        # Load schedule state
        self.schedule.current_sparsity = state['schedule_state']['current_sparsity']
        self.schedule.last_pruning_step = state['schedule_state']['last_pruning_step']
        
        # Load pruning history if available
        history_path = os.path.splitext(file_path)[0] + '_history.json'
        if os.path.exists(history_path):
            self.logger.load_from_file(history_path)
            
        self.module_logger.info(f"Loaded pruning state from {file_path}")

def create_developmental_pruning(pruning_module: SynapticPruningModule, 
                               network, **params) -> SynapticPruningModule:
    """
    Create a pruning configuration that mimics developmental pruning in the brain.
    
    Developmental pruning starts with high connectivity (overproduction) and
    gradually reduces connections based primarily on activity, with rapid pruning
    early in development followed by more selective pruning later.
    
    Args:
        pruning_module: SynapticPruningModule instance
        network: Neural network to apply pruning to
        **params: Additional parameters
        
    Returns:
        Configured SynapticPruningModule
    """
    # Default parameters for developmental pruning
    dev_params = PruningParameters(
        target_sparsity=0.7,          # High target sparsity (70% of connections removed)
        initial_sparsity=0.0,         # Start with full connectivity
        pruning_frequency=1000,       # Frequent pruning
        update_frequency=100,         # Frequent utility updates
        min_age=500,                  # Shorter minimum age
        weight_threshold=0.01,        # Lower weight threshold
        activity_threshold=0.03,      # Lower activity threshold
        weight_factor=0.3,            # Less weight on weight magnitude
        activity_factor=0.6,          # More weight on activity
        redundancy_factor=0.1,        # Some weight on redundancy
        schedule_type=PruningScheduleType.EXPONENTIAL,  # Exponential schedule
        schedule_start=1000,          # Start pruning early
        schedule_end=50000,           # End pruning at middle development
        schedule_frequency=1000,      # Frequent pruning events
        schedule_rate=2.0,            # Faster pruning rate
        activity_window=5000,         # Shorter activity window
        activity_decay=0.999,         # Faster activity decay
        importance_criterion="activity",  # Activity-based importance
        protect_ratio=0.05            # Protect fewer connections
    )
    
    # Override defaults with provided params
    for key, value in params.items():
        setattr(dev_params, key, value)
    
    # Configure pruning module
    pruning_module.params = dev_params
    
    # Create activity-focused composite strategy
    activity_strategy = ActivityBasedPruning(dev_params)
    weight_strategy = WeightBasedPruning(dev_params)
    redundancy_strategy = RedundancyPruning(dev_params)
    
    composite_strategy = CompositePruning(
        dev_params,
        strategies=[activity_strategy, weight_strategy, redundancy_strategy],
    )
    
    pruning_module.set_strategy(composite_strategy)
    
    # Create exponential schedule
    schedule = PruningSchedule(PruningScheduleType.EXPONENTIAL, dev_params)
    pruning_module.set_schedule(schedule)
    
    # Connect to network
    pruning_module.connect_to_core(network)
    
    return pruning_module

def create_efficient_pruning(pruning_module: SynapticPruningModule, 
                           network, **params) -> SynapticPruningModule:
    """
    Create a pruning configuration optimized for efficiency.
    
    Efficient pruning aims to maximize performance-to-parameter ratio by
    carefully balancing weight magnitude, activity, and redundancy criteria.
    
    Args:
        pruning_module: SynapticPruningModule instance
        network: Neural network to apply pruning to
        **params: Additional parameters
        
    Returns:
        Configured SynapticPruningModule
    """
    # Default parameters for efficient pruning
    eff_params = PruningParameters(
        target_sparsity=0.5,          # Moderate target sparsity (50% of connections removed)
        initial_sparsity=0.0,         # Start with full connectivity
        pruning_frequency=2000,       # Less frequent pruning
        update_frequency=200,         # Less frequent utility updates
        min_age=2000,                 # Longer minimum age
        weight_threshold=0.005,       # Lower weight threshold
        activity_threshold=0.01,      # Lower activity threshold
        redundancy_threshold=0.9,     # Higher redundancy threshold
        weight_factor=0.4,            # Balanced weight on weight magnitude
        activity_factor=0.3,          # Balanced weight on activity
        redundancy_factor=0.3,        # Balanced weight on redundancy
        schedule_type=PruningScheduleType.POLYNOMIAL,  # Polynomial schedule
        schedule_start=5000,          # Start pruning later
        schedule_end=100000,          # End pruning later
        schedule_frequency=2000,      # Less frequent pruning events
        schedule_rate=3.0,            # Slower initial pruning, faster later
        activity_window=20000,        # Longer activity window
        activity_decay=0.9995,        # Slower activity decay
        importance_criterion="composite",  # Composite importance
        protect_ratio=0.1,            # Protect more connections
        random_protect=0.03           # Some random protection
    )
    
    # Override defaults with provided params
    for key, value in params.items():
        setattr(eff_params, key, value)
    
    # Configure pruning module
    pruning_module.params = eff_params
    
    # Create balanced composite strategy
    weight_strategy = WeightBasedPruning(eff_params)
    activity_strategy = ActivityBasedPruning(eff_params)
    redundancy_strategy = RedundancyPruning(eff_params)
    
    composite_strategy = CompositePruning(
        eff_params,
        strategies=[weight_strategy, activity_strategy, redundancy_strategy],
    )
    
    pruning_module.set_strategy(composite_strategy)
    
    # Create polynomial schedule
    schedule = PruningSchedule(PruningScheduleType.POLYNOMIAL, eff_params)
    pruning_module.set_schedule(schedule)
    
    # Connect to network
    pruning_module.connect_to_core(network)
    
    return pruning_module

def initialize_pruning(config: Dict[str, Any] = None) -> SynapticPruningModule:
    """
    Initialize the pruning module with default configuration.
    
    Args:
        config: Configuration dictionary (optional)
        
    Returns:
        Initialized SynapticPruningModule
    """
    # Create default configuration if none provided
    if config is None:
        config = {}
    
    # Create parameters with defaults from config
    params = PruningParameters(
        target_sparsity=config.get('target_sparsity', 0.5),
        initial_sparsity=config.get('initial_sparsity', 0.0),
        pruning_frequency=config.get('pruning_frequency', 1000),
        update_frequency=config.get('update_frequency', 100),
        min_age=config.get('min_age', 1000),
        weight_threshold=config.get('weight_threshold', 0.01),
        activity_threshold=config.get('activity_threshold', 0.05),
        redundancy_threshold=config.get('redundancy_threshold', 0.8),
        weight_factor=config.get('weight_factor', 0.6),
        activity_factor=config.get('activity_factor', 0.3),
        redundancy_factor=config.get('redundancy_factor', 0.1),
        schedule_type=PruningScheduleType.from_string(config.get('schedule_type', 'EXPONENTIAL')),
        schedule_start=config.get('schedule_start', 1000),
        schedule_end=config.get('schedule_end', 100000),
        schedule_frequency=config.get('schedule_frequency', 1000),
        schedule_rate=config.get('schedule_rate', 0.7),
        activity_window=config.get('activity_window', 10000),
        activity_decay=config.get('activity_decay', 0.999),
        enable_regrowth=config.get('enable_regrowth', False),
        regrowth_rate=config.get('regrowth_rate', 0.1)
    )
    
    # Create pruning module
    pruning_module = SynapticPruningModule(params)
    
    # Create strategy based on config
    strategy_type = config.get('strategy_type', 'composite')
    if strategy_type.lower() == 'weight':
        strategy = WeightBasedPruning(params)
    elif strategy_type.lower() == 'activity':
        strategy = ActivityBasedPruning(params)
    elif strategy_type.lower() == 'redundancy':
        strategy = RedundancyPruning(params)
    elif strategy_type.lower() == 'gradient':
        strategy = GradientBasedPruning(params)
    else:  # Default to composite
        strategy = CompositePruning(params)
        
    pruning_module.set_strategy(strategy)
    
    # Log initialization
    logger.info(f"Initialized SynapticPruningModule with {strategy_type} strategy "
              f"and {params.schedule_type} schedule")
    
    return pruning_module

# Utility function to make it easier to create a pruning module
def create_pruning_module(target_sparsity: float = 0.5, 
                         strategy_type: str = 'composite',
                         schedule_type: str = 'exponential',
                         log_file: str = None) -> SynapticPruningModule:
    """
    Create a pruning module with the specified configuration.
    
    Args:
        target_sparsity: Target sparsity level (0.0-1.0)
        strategy_type: Type of pruning strategy ('weight', 'activity', 'redundancy', 'gradient', 'composite')
        schedule_type: Type of pruning schedule ('one_shot', 'linear', 'exponential', 'polynomial', 'cosine', 'cyclic')
        log_file: Path to log file (optional)
        
    Returns:
        Configured SynapticPruningModule
    """
    # Create config dictionary
    config = {
        'target_sparsity': target_sparsity,
        'strategy_type': strategy_type,
        'schedule_type': schedule_type.upper()
    }
    
    # Initialize pruning module
    pruning_module = initialize_pruning(config)
    
    # Set up logger with log file if specified
    if log_file:
        pruning_module.logger = PruningLogger(log_file)
        
    return pruning_module

# === Examples of usage === 

def example_pruning():
    """Example of setting up and using synaptic pruning."""
    # Create a simple mock network for demonstration
    class MockNetwork:
        def __init__(self, n_neurons=100):
            self.n_neurons = n_neurons
            # Create random connectivity
            self.connections = []
            for i in range(n_neurons):
                for j in range(n_neurons):
                    if i != j and np.random.random() < 0.1:
                        self.connections.append((i, j))
            
            # Create random weights
            self.weights = np.random.normal(0, 0.1, len(self.connections))
            
            # Create random activations
            self.activations = np.random.binomial(1, 0.2, len(self.connections))
            
            print(f"Created mock network with {n_neurons} neurons and {len(self.connections)} connections")
        
        def get_connection_pairs(self):
            return self.connections
            
        def get_connection_weights(self):
            return self.weights
            
        def get_connection_activations(self):
            # Update activations randomly
            self.activations = np.random.binomial(1, 0.2, len(self.connections))
            return self.activations
            
        def prune_connections(self, connections_to_prune):
            # Get indices of connections to prune
            indices_to_remove = []
            for conn in connections_to_prune:
                try:
                    idx = self.connections.index(conn)
                    indices_to_remove.append(idx)
                except ValueError:
                    pass
            
            # Remove connections in reverse order to avoid index issues
            for idx in sorted(indices_to_remove, reverse=True):
                del self.connections[idx]
                self.weights = np.delete(self.weights, idx)
                self.activations = np.delete(self.activations, idx)
                
            print(f"Pruned {len(indices_to_remove)} connections, {len(self.connections)} remaining")
    
    # Create mock network
    network = MockNetwork(n_neurons=50)
    
    # Create pruning module
    pruning_module = create_pruning_module(
        target_sparsity=0.5,
        strategy_type='composite',
        schedule_type='exponential'
    )
    
    # Connect to network
    pruning_module.connect_to_core(network)
    
    # Run simulation for 20,000 time steps
    for t in range(1, 20001):
        pruning_module.step(1.0)
        
        # Print status every 5,000 steps
        if t % 5000 == 0:
            sparsity = pruning_module.get_current_sparsity()
            stats = pruning_module.get_pruning_statistics()
            print(f"Time step {t}, sparsity: {sparsity:.3f}, pruned so far: {stats['total_pruned']}")
    
    # Plot results
    fig1 = pruning_module.plot_sparsity_history()
    fig1.savefig("pruning_sparsity.png")
    
    fig2 = pruning_module.visualize_utility()
    fig2.savefig("pruning_utility.png")
    
    print("Pruning example complete, check the generated images for results.")

if __name__ == "__main__":
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Run example if executed directly
    example_pruning()