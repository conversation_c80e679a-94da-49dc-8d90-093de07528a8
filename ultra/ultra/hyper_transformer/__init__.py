#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hyper-Dimensional Transformer for the ULTRA system.

This module implements the advanced transformer components of ULTRA, including:
- Self-evolving dynamic attention mechanisms
- Contextual bias matrices for enhanced attention
- Recursive transformer layers with adaptive depth
- Temporal-causal transformers for explicit causality modeling
- Multi-scale knowledge embeddings across abstraction levels
- Cross-modal dimension mapping for multi-modal integration

The Hyper-Dimensional Transformer extends traditional transformer architectures with
novel mechanisms that allow for dynamic adaptation, recursive processing, and
enhanced representations of temporal and causal relationships.

Key mathematical formulations:
- Self-Evolving Attention: Attention(Q, K, V) = softmax((QK^T/√d_k) · M) V
  with mask M evolving as M_t = f_φ(M_{t-1}, C_t, P_t)
- Contextual Bias: B_t = ∑α_i·B_i^task + ∑β_j·B_j^history + ∑γ_k·B_k^knowledge
- Recursive Processing: h_i = F_i(h_{i-1}, d) with adaptive halting
- Multi-Scale Embeddings: MS-Attention(Q,K,V) = ∑w_l·Attention(Q^l,K^l,V^l)

References:
- ULTRA Technical Paper v1.0
- "Attention is All You Need" (Vaswani et al., 2017)
- "Transformers are RNNs" (Katharopoulos et al., 2020)
"""

import logging
import math
import sys
import os
import warnings
import importlib
from typing import Dict, List, Tuple, Optional, Union, Callable, Any, Type

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.nn import TransformerEncoder, TransformerEncoderLayer
from torch.utils.tensorboard import SummaryWriter

# Configure logging
logger = logging.getLogger(__name__)

# Import submodules
from ultra.hyper_transformer.dynamic_attention import SelfEvolvingDynamicAttention
from ultra.hyper_transformer.contextual_bias import ContextualBiasMatrix
from ultra.hyper_transformer.recursive_transformer import RecursiveTransformer
from ultra.hyper_transformer.temporal_causal import TemporalCausalTransformer
from ultra.hyper_transformer.multiscale_embedding import MultiScaleKnowledgeEmbedding
from ultra.hyper_transformer.cross_modal_mapper import CrossModalDimensionMapper

# Create alias for backward compatibility
DynamicAttention = SelfEvolvingDynamicAttention
CrossModalMapper = CrossModalDimensionMapper

# Define public API
__all__ = [
    'SelfEvolvingDynamicAttention',
    'DynamicAttention',  # Alias for backward compatibility
    'ContextualBiasMatrix',
    'RecursiveTransformer',
    'TemporalCausalTransformer',
    'MultiScaleKnowledgeEmbedding',
    'CrossModalDimensionMapper',
    'CrossModalMapper',  # Alias for backward compatibility
    'HyperDimensionalTransformerConfig',
    'HyperDimensionalTransformer',
]

# Check for optional dependencies
_has_flash_attention = importlib.util.find_spec("flash_attn") is not None
if _has_flash_attention:
    from flash_attn import flash_attn_func
    logger.info("Flash Attention is available and will be used for optimized attention computation")
else:
    logger.info("Flash Attention not found. Using standard attention implementation")

# Default configuration values
DEFAULT_CONFIG = {
    "d_model": 1024,           # Model dimension
    "nhead": 16,               # Number of attention heads
    "d_ff": 4096,              # Feedforward dimension
    "dropout": 0.1,            # Dropout rate
    "num_layers": 24,          # Number of layers
    "max_recursion": 3,        # Maximum recursion depth
    "num_scales": 4,           # Number of scales for multi-scale embeddings (changed from 3 to 4)
    "activation": "gelu",      # Activation function
    "position_embedding": "rotary",  # Position embedding type
    "layer_norm_eps": 1e-5,    # Layer normalization epsilon
    "precision": "bf16",       # Numerical precision (fp32, fp16, bf16)
    "causal_mask": True,       # Whether to use causal masking
    "use_dynamic_attention": True,  # Whether to use dynamic attention
    "use_contextual_bias": True,  # Whether to use contextual bias
    "self_evolution_rate": 0.01,  # Rate of self-evolution
    "temporal_modeling": True,  # Whether to model temporal relationships
    "max_context_length": 8192,  # Maximum context length
    "vocab_size": 50000,       # Vocabulary size for embeddings
}


class HyperDimensionalTransformerConfig:
    """Configuration class for HyperDimensionalTransformer.
    
    This class defines all hyperparameters and configuration options for the
    Hyper-Dimensional Transformer architecture.
    """
    
    def __init__(self, **kwargs):
        """Initialize configuration with default values and overrides.
        
        Args:
            **kwargs: Overrides for default configuration values
        """
        # Start with default configuration
        for key, value in DEFAULT_CONFIG.items():
            setattr(self, key, value)
        
        # Override with any provided kwargs
        for key, value in kwargs.items():
            if key not in DEFAULT_CONFIG:
                warnings.warn(f"Unknown configuration parameter: {key}")
            setattr(self, key, value)
        
        # Validate configuration
        self._validate()
        
        # Log configuration
        logger.info(f"Initialized HyperDimensionalTransformerConfig: {self.__dict__}")
    
    def _validate(self):
        """Validate configuration parameters."""
        if self.d_model % self.nhead != 0:
            raise ValueError(f"d_model ({self.d_model}) must be divisible by nhead ({self.nhead})")
        
        if self.max_recursion < 1:
            raise ValueError(f"max_recursion ({self.max_recursion}) must be at least 1")
        
        if self.num_scales < 1:
            raise ValueError(f"num_scales ({self.num_scales}) must be at least 1")
        
        valid_activations = ["relu", "gelu", "swish", "silu", "mish"]
        if self.activation not in valid_activations:
            raise ValueError(f"activation must be one of {valid_activations}")
        
        valid_precisions = ["fp32", "fp16", "bf16"]
        if self.precision not in valid_precisions:
            raise ValueError(f"precision must be one of {valid_precisions}")
        
        valid_pos_embeddings = ["learned", "sinusoidal", "rotary", "alibi", "none"]
        if self.position_embedding not in valid_pos_embeddings:
            raise ValueError(f"position_embedding must be one of {valid_pos_embeddings}")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary.
        
        Returns:
            Dict[str, Any]: Configuration as dictionary
        """
        return {k: v for k, v in self.__dict__.items() if not k.startswith('_')}
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'HyperDimensionalTransformerConfig':
        """Create configuration from dictionary.
        
        Args:
            config_dict: Dictionary containing configuration parameters
            
        Returns:
            HyperDimensionalTransformerConfig: Configuration object
        """
        return cls(**config_dict)
    
    def save(self, file_path: str) -> None:
        """Save configuration to JSON file.
        
        Args:
            file_path: Path to save the configuration file
        """
        import json
        with open(file_path, 'w') as f:
            json.dump(self.to_dict(), f, indent=2)
    
    @classmethod
    def load(cls, file_path: str) -> 'HyperDimensionalTransformerConfig':
        """Load configuration from JSON file.
        
        Args:
            file_path: Path to the configuration file
            
        Returns:
            HyperDimensionalTransformerConfig: Configuration object
        """
        import json
        with open(file_path, 'r') as f:
            config_dict = json.load(f)
        return cls.from_dict(config_dict)


class HyperDimensionalTransformer(nn.Module):
    """Hyper-Dimensional Transformer model.
    
    This is the main transformer model that integrates all the components of the
    Hyper-Dimensional Transformer architecture, including self-evolving attention,
    contextual bias, recursive processing, temporal-causal modeling, and
    multi-scale embeddings.
    """
    
    def __init__(self, config: HyperDimensionalTransformerConfig):
        """Initialize the Hyper-Dimensional Transformer.
        
        Args:
            config: Configuration object
        """
        super().__init__()
        self.config = config
        
        # Initialize embeddings
        self.token_embedding = nn.Embedding(config.vocab_size, config.d_model)
        
        # Initialize positional encoding based on config
        self.pos_encoding = self._init_positional_encoding(
            config.position_embedding, config.d_model, config.max_context_length
        )
        
        # Initialize Multi-Scale Knowledge Embedding
        self.multiscale_embedding = MultiScaleKnowledgeEmbedding(
            embedding_dim=config.d_model,
            num_scales=config.num_scales,
            max_sequence_length=config.max_context_length
        )
        
        # Initialize Cross-Modal Dimension Mapper if needed
        if hasattr(config, 'modalities') and config.modalities:
            modality_dims = {m: d for m, d in config.modalities}
            self.cross_modal_mapper = CrossModalDimensionMapper(
                modality_dims=modality_dims,
                joint_dim=config.d_model
            )
        
        # Initialize transformer layers
        self.layers = nn.ModuleList([
            self._create_layer(i) for i in range(config.num_layers)
        ])
        
        # Output normalization
        self.norm = nn.LayerNorm(config.d_model, eps=config.layer_norm_eps)
        
        # Initialize weights
        self._init_weights()
        
        # Setup for self-evolution if enabled
        if config.self_evolution_rate > 0:
            self.register_buffer('evolution_steps', torch.tensor(0, dtype=torch.long))
            self.register_buffer('performance_history', torch.zeros(100))  # Track last 100 performance metrics
        
        logger.info(f"Initialized HyperDimensionalTransformer with {config.num_layers} layers")
    
    def _init_positional_encoding(self, pos_type, d_model, max_length):
        """Initialize positional encoding based on type.
        
        Args:
            pos_type: Type of positional encoding
            d_model: Model dimension
            max_length: Maximum sequence length
            
        Returns:
            nn.Module: Positional encoding module
        """
        if pos_type == "learned":
            return nn.Embedding(max_length, d_model)
        elif pos_type == "sinusoidal":
            pe = torch.zeros(max_length, d_model)
            position = torch.arange(0, max_length, dtype=torch.float).unsqueeze(1)
            div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))
            pe[:, 0::2] = torch.sin(position * div_term)
            pe[:, 1::2] = torch.cos(position * div_term)
            pe = pe.unsqueeze(0)
            self.register_buffer('pe', pe)
            return lambda x, pos: x + self.pe[:, pos]
        elif pos_type == "rotary":
            from ultra.hyper_transformer.utils import RotaryPositionEncoding
            return RotaryPositionEncoding(d_model // self.config.nhead)
        elif pos_type == "alibi":
            from ultra.hyper_transformer.utils import ALiBiPositionalBias
            return ALiBiPositionalBias(self.config.nhead)
        elif pos_type == "none":
            return lambda x, pos: x
        else:
            raise ValueError(f"Unknown positional encoding type: {pos_type}")
    
    def _create_layer(self, layer_idx):
        """Create a single transformer layer.
        
        Args:
            layer_idx: Index of the layer
            
        Returns:
            nn.Module: Transformer layer
        """
        config = self.config
        
        # Initialize different layer types based on configuration
        if config.use_dynamic_attention:
            attention_mechanism = SelfEvolvingDynamicAttention(
                d_model=config.d_model,
                nhead=config.nhead,
                dropout=config.dropout,
                evolution_rate=config.self_evolution_rate,
                use_flash_attn=_has_flash_attention
            )
        else:
            attention_mechanism = nn.MultiheadAttention(
                embed_dim=config.d_model,
                num_heads=config.nhead,
                dropout=config.dropout,
                batch_first=True
            )
        
        # Add contextual bias if enabled
        if config.use_contextual_bias:
            contextual_bias = ContextualBiasMatrix(
                d_model=config.d_model,
                nhead=config.nhead,
                dropout=config.dropout
            )
        else:
            contextual_bias = None
        
        # Choose between recursive and temporal-causal transformer
        if config.temporal_modeling and layer_idx % 2 == 0:
            return TemporalCausalTransformer(
                d_model=config.d_model,
                nhead=config.nhead,
                dim_feedforward=config.d_ff,
                dropout=config.dropout,
                activation=config.activation,
                contextual_bias=contextual_bias,
                attention_mechanism=attention_mechanism,
                layer_norm_eps=config.layer_norm_eps
            )
        else:
            return RecursiveTransformer(
                d_model=config.d_model,
                nhead=config.nhead,
                dim_feedforward=config.d_ff,
                dropout=config.dropout,
                activation=config.activation,
                max_recursion=config.max_recursion,
                contextual_bias=contextual_bias,
                attention_mechanism=attention_mechanism,
                layer_norm_eps=config.layer_norm_eps
            )
    
    def _init_weights(self):
        """Initialize model weights."""
        for p in self.parameters():
            if p.dim() > 1:
                nn.init.xavier_uniform_(p)
    
    def forward(
        self,
        input_ids: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        position_ids: Optional[torch.Tensor] = None,
        token_type_ids: Optional[torch.Tensor] = None,
        modality_inputs: Optional[Dict[str, torch.Tensor]] = None,
        context_embeddings: Optional[torch.Tensor] = None,
        temporal_info: Optional[Dict[str, torch.Tensor]] = None,
        return_attention_weights: bool = False,
        return_hidden_states: bool = False
    ) -> Union[torch.Tensor, Tuple[torch.Tensor, ...]]:
        """Forward pass through the transformer.
        
        Args:
            input_ids: Input token IDs [batch_size, seq_len]
            attention_mask: Attention mask [batch_size, seq_len]
            position_ids: Position IDs [batch_size, seq_len]
            token_type_ids: Token type IDs [batch_size, seq_len]
            modality_inputs: Inputs from different modalities
            context_embeddings: Context embeddings for bias generation
            temporal_info: Temporal information for causal modeling
            return_attention_weights: Whether to return attention weights
            return_hidden_states: Whether to return all hidden states
            
        Returns:
            torch.Tensor or tuple: Output or tuple of (output, attention, hidden_states)
        """
        # Get batch size and sequence length
        batch_size, seq_length = input_ids.shape
        device = input_ids.device
        
        # Create position IDs if not provided
        if position_ids is None:
            position_ids = torch.arange(seq_length, dtype=torch.long, device=device).unsqueeze(0).expand(batch_size, -1)
        
        # Create attention mask if not provided
        if attention_mask is None:
            attention_mask = torch.ones(batch_size, seq_length, device=device)
        
        # Convert attention mask to appropriate format for transformer
        # [batch_size, seq_length] -> [batch_size, 1, seq_length, seq_length]
        if self.config.causal_mask:
            # Create causal mask
            causal_mask = self._generate_causal_mask(seq_length, device)
            # Combined with explicit attention mask
            extended_attention_mask = attention_mask.unsqueeze(1).unsqueeze(2)
            extended_attention_mask = extended_attention_mask * causal_mask
        else:
            extended_attention_mask = attention_mask.unsqueeze(1).unsqueeze(2)
        
        # Mask value: -10000.0 for masked positions, 0 for unmasked
        extended_attention_mask = (1.0 - extended_attention_mask) * -10000.0
        
        # Get token embeddings
        hidden_states = self.token_embedding(input_ids)
        
        # Apply positional encoding
        if isinstance(self.pos_encoding, nn.Embedding):
            hidden_states = hidden_states + self.pos_encoding(position_ids)
        else:
            hidden_states = self.pos_encoding(hidden_states, position_ids)
        
        # Apply token type embeddings if provided
        if token_type_ids is not None and hasattr(self, 'token_type_embedding'):
            hidden_states = hidden_states + self.token_type_embedding(token_type_ids)
        
        # Handle multi-modal inputs if provided
        if modality_inputs is not None and hasattr(self, 'cross_modal_mapper'):
            modal_embeddings = self.cross_modal_mapper(modality_inputs)
            # Combine with text embeddings - strategy depends on architecture
            hidden_states = self._fuse_modalities(hidden_states, modal_embeddings)
        
        # Apply multi-scale knowledge embedding
        hidden_states = self.multiscale_embedding(hidden_states)
        
        # Store attention weights and hidden states if requested
        all_attention_weights = [] if return_attention_weights else None
        all_hidden_states = [hidden_states] if return_hidden_states else None
        
        # Forward pass through layers
        layer_outputs = hidden_states
        for i, layer in enumerate(self.layers):
            # Get contextual bias if available
            contextual_bias = None
            if context_embeddings is not None and hasattr(layer, 'get_contextual_bias'):
                contextual_bias = layer.get_contextual_bias(context_embeddings)
            
            # Get temporal information if available
            layer_temporal_info = None
            if temporal_info is not None and hasattr(layer, 'use_temporal_info'):
                layer_temporal_info = temporal_info
            
            # Forward through layer
            if hasattr(layer, 'forward_with_context'):
                # For layers that support contextual and temporal information
                layer_outputs, attention_weights = layer.forward_with_context(
                    layer_outputs,
                    extended_attention_mask,
                    contextual_bias=contextual_bias,
                    temporal_info=layer_temporal_info,
                    return_attention=return_attention_weights
                )
            else:
                # Standard forward
                layer_outputs, attention_weights = layer(
                    layer_outputs,
                    extended_attention_mask,
                    return_attention=return_attention_weights
                )
            
            # Store attention weights and hidden states if requested
            if return_attention_weights:
                all_attention_weights.append(attention_weights)
            
            if return_hidden_states:
                all_hidden_states.append(layer_outputs)
        
        # Apply final normalization
        output = self.norm(layer_outputs)
        
        # Return appropriate outputs
        if return_attention_weights and return_hidden_states:
            return output, all_attention_weights, all_hidden_states
        elif return_attention_weights:
            return output, all_attention_weights
        elif return_hidden_states:
            return output, all_hidden_states
        else:
            return output
    
    def _generate_causal_mask(self, seq_length, device):
        """Generate a causal mask for self-attention.
        
        Args:
            seq_length: Sequence length
            device: Device for the mask
            
        Returns:
            torch.Tensor: Causal mask [1, 1, seq_length, seq_length]
        """
        # Create lower triangular mask (1s in lower triangle, 0s elsewhere)
        mask = torch.tril(torch.ones(seq_length, seq_length, device=device))
        # Add batch and head dimensions
        return mask.unsqueeze(0).unsqueeze(0)
    
    def _fuse_modalities(self, text_embeddings, modal_embeddings):
        """Fuse text embeddings with other modality embeddings.
        
        Args:
            text_embeddings: Text embeddings [batch_size, seq_len, d_model]
            modal_embeddings: Dict of modal embeddings
            
        Returns:
            torch.Tensor: Fused embeddings
        """
        # Different fusion strategies based on architectural choices
        fusion_strategy = getattr(self.config, 'modal_fusion_strategy', 'attention')
        
        if fusion_strategy == 'concatenate':
            # Create special tokens to represent modal embeddings
            batch_size, seq_len, d_model = text_embeddings.shape
            modal_tokens = []
            
            for modality, embeddings in modal_embeddings.items():
                if embeddings.dim() == 2:  # [batch_size, d_model]
                    modal_tokens.append(embeddings.unsqueeze(1))  # [batch_size, 1, d_model]
                else:  # embeddings is already [batch_size, seq_len, d_model]
                    modal_tokens.append(embeddings)
            
            # Concatenate all modal tokens with text embeddings
            return torch.cat([text_embeddings] + modal_tokens, dim=1)
        
        elif fusion_strategy == 'attention':
            # Use cross-attention to fuse modalities
            fused_embedding = text_embeddings
            
            for modality, embeddings in modal_embeddings.items():
                # Cross-attention between text and modality
                if embeddings.dim() == 2:  # [batch_size, d_model]
                    modal_context = embeddings.unsqueeze(1)  # [batch_size, 1, d_model]
                else:
                    modal_context = embeddings
                
                # Simple cross-attention implementation
                query = fused_embedding
                key = value = modal_context
                
                # Compute attention scores
                scores = torch.matmul(query, key.transpose(-2, -1)) / math.sqrt(d_model)
                attention_weights = F.softmax(scores, dim=-1)
                attended_values = torch.matmul(attention_weights, value)
                
                # Fuse with original embeddings (residual connection)
                fused_embedding = fused_embedding + attended_values
            
            return fused_embedding
        
        elif fusion_strategy == 'feature_wise':
            # Element-wise operations to combine features
            fused_embedding = text_embeddings
            
            for modality, embeddings in modal_embeddings.items():
                if embeddings.dim() == 2:  # [batch_size, d_model]
                    # Broadcast to match text embedding shape
                    modal_feature = embeddings.unsqueeze(1).expand(-1, text_embeddings.size(1), -1)
                else:
                    # Ensure modal feature has the same sequence length
                    seq_len = min(embeddings.size(1), text_embeddings.size(1))
                    modal_feature = embeddings[:, :seq_len, :]
                    fused_embedding = fused_embedding[:, :seq_len, :]
                
                # Feature-wise modulation (FiLM-like)
                gamma = self.film_gamma[modality](modal_feature)
                beta = self.film_beta[modality](modal_feature)
                
                fused_embedding = gamma * fused_embedding + beta
            
            return fused_embedding
        
        else:
            raise ValueError(f"Unknown modality fusion strategy: {fusion_strategy}")
    
    def update_evolution(self, performance_metric):
        """Update self-evolution based on performance metric.
        
        Args:
            performance_metric: Performance metric to guide evolution
        """
        if self.config.self_evolution_rate <= 0:
            return
        
        # Update performance history
        idx = self.evolution_steps % 100
        self.performance_history[idx] = performance_metric
        self.evolution_steps += 1
        
        # Only evolve after gathering enough data
        if self.evolution_steps < 10:
            return
        
        # Compute moving average of performance
        window_size = min(self.evolution_steps, 100)
        performance_avg = self.performance_history[:window_size].mean().item()
        
        # Evolve each layer that supports evolution
        for layer in self.layers:
            if hasattr(layer, 'evolve'):
                layer.evolve(performance_avg)
    
    def save_pretrained(self, save_directory):
        """Save model and configuration to directory.
        
        Args:
            save_directory: Directory to save model
        """
        os.makedirs(save_directory, exist_ok=True)
        
        # Save configuration
        config_path = os.path.join(save_directory, "config.json")
        self.config.save(config_path)
        
        # Save model weights
        model_path = os.path.join(save_directory, "pytorch_model.bin")
        torch.save(self.state_dict(), model_path)
        
        logger.info(f"Model saved to {save_directory}")
    
    @classmethod
    def from_pretrained(cls, load_directory):
        """Load model and configuration from directory.
        
        Args:
            load_directory: Directory containing saved model
            
        Returns:
            HyperDimensionalTransformer: Loaded model
        """
        # Load configuration
        config_path = os.path.join(load_directory, "config.json")
        config = HyperDimensionalTransformerConfig.load(config_path)
        
        # Create model with loaded configuration
        model = cls(config)
        
        # Load model weights
        model_path = os.path.join(load_directory, "pytorch_model.bin")
        model.load_state_dict(torch.load(model_path, map_location="cpu"))
        
        logger.info(f"Model loaded from {load_directory}")
        return model
    
    def freeze_embeddings(self):
        """Freeze embedding parameters."""
        for param in self.token_embedding.parameters():
            param.requires_grad = False
        
        if isinstance(self.pos_encoding, nn.Module):
            for param in self.pos_encoding.parameters():
                param.requires_grad = False
    
    def unfreeze_embeddings(self):
        """Unfreeze embedding parameters."""
        for param in self.token_embedding.parameters():
            param.requires_grad = True
        
        if isinstance(self.pos_encoding, nn.Module):
            for param in self.pos_encoding.parameters():
                param.requires_grad = True
    
    def freeze_layers(self, num_layers):
        """Freeze the first n layers of the transformer.
        
        Args:
            num_layers: Number of layers to freeze
        """
        if num_layers <= 0:
            return
        
        for i in range(min(num_layers, len(self.layers))):
            for param in self.layers[i].parameters():
                param.requires_grad = False
    
    def unfreeze_layers(self):
        """Unfreeze all transformer layers."""
        for layer in self.layers:
            for param in layer.parameters():
                param.requires_grad = True

# Version information
__version__ = '0.1.0'