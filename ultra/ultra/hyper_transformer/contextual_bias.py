#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Contextual Bias Matrix Module for ULTRA Hyper-Dimensional Transformer

This module implements the Contextual Bias Matrix (CBM) as described in the ULTRA paper.
The CBM generates context-specific biases to guide the attention mechanism based on:
    1. Task-specific information (task constraints, goals)
    2. Interaction history (prior attention patterns)
    3. External knowledge (domain-specific insights)

Mathematical formulation:
    B_t = ∑_{i=1}^{n} α_i · B_i^{task} + ∑_{j=1}^{m} β_j · B_j^{history} + ∑_{k=1}^{p} γ_k · B_k^{knowledge}

References:
    - ULTRA Technical Paper v1.0, Section 5.2
"""

import logging
import math
from typing import Dict, List, Tuple, Optional, Union, Any

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F

# Configure logging
logger = logging.getLogger(__name__)


class ContextualBiasMatrix(nn.Module):
    """
    Contextual Bias Matrix that guides attention based on context.
    
    This module generates bias matrices to incorporate prior knowledge, interaction history,
    and task-specific information into attention mechanisms, enabling more focused attention
    and enhanced performance on complex tasks.
    
    The bias matrix acts as an additive term in the attention computation:
        Attention(Q, K, V) = softmax((QK^T/√d_k) + B_t) V
    
    where B_t is the contextual bias matrix at time t.
    """
    
    def __init__(
        self,
        d_model: int,
        nhead: int,
        bias_types: Optional[List[str]] = None,
        bias_scale: float = 1.0,
        max_seq_len: int = 8192,
        max_tasks: int = 64,
        dropout: float = 0.1,
        layer_norm_eps: float = 1e-5,
        device: Optional[torch.device] = None,
        dtype: Optional[torch.dtype] = None,
    ):
        """
        Initialize the Contextual Bias Matrix.
        
        Args:
            d_model: Model dimension
            nhead: Number of attention heads
            bias_types: Types of biases to include (e.g., ['task', 'history', 'knowledge'])
            bias_scale: Scale factor for bias values
            max_seq_len: Maximum sequence length for position encodings
            max_tasks: Maximum number of task embeddings
            dropout: Dropout probability
            layer_norm_eps: Layer normalization epsilon
            device: Device to use (CPU/GPU)
            dtype: Data type to use
        """
        factory_kwargs = {'device': device, 'dtype': dtype}
        super().__init__()
        
        self.d_model = d_model
        self.nhead = nhead
        self.bias_scale = bias_scale
        self.max_seq_len = max_seq_len
        self.max_tasks = max_tasks
        self.head_dim = d_model // nhead
        self.dropout = dropout
        self.layer_norm_eps = layer_norm_eps
        
        if self.d_model % self.nhead != 0:
            raise ValueError(
                f"d_model ({d_model}) must be divisible by nhead ({nhead})"
            )
        
        # Default bias types if not provided
        if bias_types is None:
            self.bias_types = ['task', 'history', 'knowledge']
        else:
            self.bias_types = bias_types
        
        # Initialize bias projectors for each bias type
        self.bias_projectors = nn.ModuleDict()
        for bias_type in self.bias_types:
            self.bias_projectors[bias_type] = self._create_bias_projector(bias_type, **factory_kwargs)
        
        # Initialize weights for combining different bias types
        # Implementing the α_i, β_j, γ_k coefficients from the mathematical formulation
        self.bias_weights = nn.Parameter(
            torch.ones(len(self.bias_types), **factory_kwargs) / len(self.bias_types)
        )
        
        # Position encoding for sequence-aware bias
        self.position_encodings = self._create_position_encodings(
            self.max_seq_len, self.d_model, **factory_kwargs
        )
        
        # Learnable task embeddings for task-specific bias
        self.task_embeddings = nn.Parameter(
            torch.randn(self.max_tasks, self.d_model, **factory_kwargs)
        )
        
        # Context transformation layers
        self.context_norm = nn.LayerNorm(self.d_model, eps=layer_norm_eps, **factory_kwargs)
        self.context_transform = nn.Sequential(
            nn.Linear(self.d_model, self.d_model * 2, **factory_kwargs),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(self.d_model * 2, self.d_model, **factory_kwargs),
            nn.Dropout(dropout)
        )
        
        # Bias generation layers
        self._init_bias_generators(**factory_kwargs)
        
        # Layer for final bias scaling and adjustment
        self.bias_adjustment = nn.Sequential(
            nn.LayerNorm(self.nhead, eps=layer_norm_eps, **factory_kwargs),
            nn.Sigmoid()
        )
        
        # Initialize weights
        self._reset_parameters()
        
        logger.info(f"Initialized ContextualBiasMatrix with bias types: {self.bias_types}")
    
    def _create_bias_projector(self, bias_type, **factory_kwargs):
        """
        Create a bias projector for a specific bias type.
        
        Args:
            bias_type: Type of bias ('task', 'history', or 'knowledge')
            factory_kwargs: Additional keyword arguments for layer creation
            
        Returns:
            nn.Module: Bias projector module
        """
        if bias_type == 'task':
            return nn.Sequential(
                nn.Linear(self.d_model, self.d_model, **factory_kwargs),
                nn.GELU(),
                nn.Dropout(self.dropout),
                nn.Linear(self.d_model, self.nhead, **factory_kwargs)
            )
        elif bias_type == 'history':
            return nn.Sequential(
                nn.Linear(self.d_model, self.d_model, **factory_kwargs),
                nn.GELU(),
                nn.Dropout(self.dropout),
                nn.Linear(self.d_model, self.nhead, **factory_kwargs)
            )
        elif bias_type == 'knowledge':
            return nn.Sequential(
                nn.Linear(self.d_model, self.d_model * 2, **factory_kwargs),
                nn.GELU(),
                nn.Dropout(self.dropout),
                nn.Linear(self.d_model * 2, self.d_model, **factory_kwargs),
                nn.GELU(),
                nn.Dropout(self.dropout),
                nn.Linear(self.d_model, self.nhead, **factory_kwargs)
            )
        else:
            return nn.Sequential(
                nn.Linear(self.d_model, self.d_model, **factory_kwargs),
                nn.GELU(),
                nn.Dropout(self.dropout),
                nn.Linear(self.d_model, self.nhead, **factory_kwargs)
            )
    
    def _init_bias_generators(self, **factory_kwargs):
        """
        Initialize the bias generator components for different bias types.
        
        Args:
            factory_kwargs: Additional keyword arguments for layer creation
        """
        # Task-specific bias generator
        self.task_bias_generator = nn.Bilinear(
            self.d_model, self.d_model, self.nhead, **factory_kwargs
        )
        
        # History-based bias generator
        self.history_bias_temporal_proj = nn.Linear(
            self.d_model, self.nhead * 2, **factory_kwargs
        )
        self.history_bias_decay = nn.Parameter(
            torch.ones(self.nhead, **factory_kwargs) * 0.9
        )
        
        # Knowledge-based bias generator (more complex for external knowledge integration)
        self.knowledge_bias_key_proj = nn.Linear(
            self.d_model, self.nhead * self.head_dim, **factory_kwargs
        )
        self.knowledge_bias_query_proj = nn.Linear(
            self.d_model, self.nhead * self.head_dim, **factory_kwargs
        )
        self.knowledge_bias_gate = nn.Linear(
            self.d_model, self.nhead, **factory_kwargs
        )
        
        # Cross-bias interaction
        self.cross_bias_gate = nn.Sequential(
            nn.Linear(len(self.bias_types) * self.nhead, self.nhead, **factory_kwargs),
            nn.Sigmoid()
        )
    
    def _reset_parameters(self):
        """
        Reset parameters using appropriate initialization methods.
        """
        # Initialize all Linear layers
        for module in self.modules():
            if isinstance(module, nn.Linear):
                # Use Xavier initialization for linear layers
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
        
        # Initialize bias weights with uniform values
        nn.init.constant_(self.bias_weights, 1.0 / len(self.bias_types))
        
        # Initialize task embeddings
        nn.init.normal_(self.task_embeddings, mean=0.0, std=0.02)
        
        # Initialize special parameters
        nn.init.constant_(self.history_bias_decay, 0.9)  # Start with 0.9 decay factor
    
    def _create_position_encodings(self, max_len, d_model, **factory_kwargs):
        """
        Create position encodings for incorporating positional information.
        
        Uses sinusoidal position encodings similar to the original Transformer paper.
        
        Args:
            max_len: Maximum sequence length
            d_model: Model dimension
            factory_kwargs: Additional keyword arguments for parameter creation
            
        Returns:
            nn.Parameter: Position encodings of shape (max_len, d_model)
        """
        position = torch.arange(0, max_len, dtype=torch.float, **factory_kwargs).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2, **factory_kwargs).float() * (-math.log(10000.0) / d_model))
        
        pe = torch.zeros(max_len, d_model, **factory_kwargs)
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        
        return nn.Parameter(pe, requires_grad=False)
    
    def compute_task_bias(self, enhanced_context, seq_len, position_encodings):
        """
        Compute task-specific bias based on context and positions.
        
        Implements the B_i^{task} term from the mathematical formulation.
        
        Args:
            enhanced_context: Enhanced context embedding [batch_size, d_model]
            seq_len: Sequence length
            position_encodings: Position encodings [seq_len, d_model]
            
        Returns:
            torch.Tensor: Task-specific bias [batch_size, nhead, seq_len, seq_len]
        """
        batch_size = enhanced_context.size(0)
        device = enhanced_context.device
        
        # Project enhanced context for task bias
        task_proj = self.bias_projectors['task'](enhanced_context)  # [batch_size, nhead]
        
        # Create position vectors for all sequence positions
        pos_encodings = position_encodings[:seq_len]  # [seq_len, d_model]
        
        # Prepare context for each position
        # [batch_size, 1, d_model] -> [batch_size, seq_len, d_model]
        seq_context = enhanced_context.unsqueeze(1).expand(-1, seq_len, -1)
        
        # Combine position and context for each position
        pos_ctx = seq_context + pos_encodings.unsqueeze(0)  # [batch_size, seq_len, d_model]
        
        # Create task bias using outer product of position-aware contexts
        # This is a more efficient implementation than explicitly computing all pairs
        
        # [batch_size, seq_len, d_model] -> [batch_size * seq_len, d_model]
        flat_pos_ctx = pos_ctx.reshape(batch_size * seq_len, self.d_model)
        
        # Create a task bias projection for each position
        # [batch_size * seq_len, d_model] -> [batch_size * seq_len, nhead]
        pos_task_proj = self.task_bias_generator(
            flat_pos_ctx, 
            enhanced_context.repeat_interleave(seq_len, dim=0)
        )
        
        # Reshape to [batch_size, seq_len, nhead]
        pos_task_proj = pos_task_proj.view(batch_size, seq_len, self.nhead)
        
        # Use outer product to create attention bias matrix
        # [batch_size, seq_len, nhead] -> [batch_size, nhead, seq_len, seq_len]
        task_bias = torch.einsum('bsn,btn->bnst', pos_task_proj, pos_task_proj)
        
        # Apply sigmoid and scale
        task_bias = torch.sigmoid(task_bias) * self.bias_scale
        
        return task_bias
    
    def compute_history_bias(self, enhanced_context, seq_len):
        """
        Compute history-based bias using temporal decay patterns.
        
        Implements the B_j^{history} term from the mathematical formulation.
        
        Args:
            enhanced_context: Enhanced context embedding [batch_size, d_model]
            seq_len: Sequence length
            
        Returns:
            torch.Tensor: History-based bias [batch_size, nhead, seq_len, seq_len]
        """
        batch_size = enhanced_context.size(0)
        device = enhanced_context.device
        
        # Project context to get history parameters
        # [batch_size, d_model] -> [batch_size, nhead * 2]
        history_params = self.history_bias_temporal_proj(enhanced_context)
        
        # Split into focus and decay modifiers
        history_focus = history_params[:, :self.nhead].view(batch_size, self.nhead, 1, 1)
        history_decay_mod = history_params[:, self.nhead:].view(batch_size, self.nhead, 1, 1)
        
        # Create base decay matrix for recency bias
        history_bias = torch.zeros(batch_size, self.nhead, seq_len, seq_len, device=device)
        
        # Get per-head decay rates (modified by context)
        decay_rates = torch.sigmoid(self.history_bias_decay.view(1, self.nhead, 1, 1) + history_decay_mod)
        
        # Efficiently create recency bias using broadcasting
        positions = torch.arange(seq_len, device=device)
        
        # Create distance matrix: distance_ij = |i - j|
        # For causal attention, we only want previous tokens (j < i)
        distances = positions.unsqueeze(1) - positions.unsqueeze(0)  # [seq_len, seq_len]
        
        # Only keep distances for previous tokens (set others to a large negative value)
        causal_mask = torch.tril(torch.ones(seq_len, seq_len, device=device), diagonal=0)
        masked_distances = torch.where(
            causal_mask > 0, 
            distances.float(), 
            torch.tensor(float('-inf'), device=device)
        )
        
        # Create decay matrix: decay_ij = decay_rate^distance_ij for valid positions
        # Shape: [1, nhead, seq_len, seq_len]
        decay_matrix = torch.pow(
            decay_rates,  # [batch_size, nhead, 1, 1]
            torch.abs(masked_distances.view(1, 1, seq_len, seq_len))
        )
        
        # Mask invalid positions
        decay_matrix = torch.where(
            masked_distances.view(1, 1, seq_len, seq_len) != float('-inf'),
            decay_matrix,
            torch.zeros_like(decay_matrix)
        )
        
        # Apply focus modifier
        history_bias = decay_matrix * torch.sigmoid(history_focus)
        
        return history_bias
    
    def compute_knowledge_bias(self, enhanced_context, seq_len, position_encodings):
        """
        Compute knowledge-based bias using external knowledge representation.
        
        Implements the B_k^{knowledge} term from the mathematical formulation.
        
        Args:
            enhanced_context: Enhanced context embedding [batch_size, d_model]
            seq_len: Sequence length
            position_encodings: Position encodings [seq_len, d_model]
            
        Returns:
            torch.Tensor: Knowledge-based bias [batch_size, nhead, seq_len, seq_len]
        """
        batch_size = enhanced_context.size(0)
        device = enhanced_context.device
        
        # Project context to get knowledge representation
        knowledge_proj = self.bias_projectors['knowledge'](enhanced_context)  # [batch_size, nhead]
        
        # Get position encodings for this sequence
        pos_encodings = position_encodings[:seq_len]  # [seq_len, d_model]
        
        # Create knowledge-enhanced queries and keys
        # [batch_size, d_model] -> [batch_size, nhead * head_dim]
        knowledge_queries = self.knowledge_bias_query_proj(enhanced_context)
        knowledge_keys = self.knowledge_bias_key_proj(enhanced_context)
        
        # Reshape to [batch_size, nhead, head_dim]
        knowledge_queries = knowledge_queries.view(batch_size, self.nhead, self.head_dim)
        knowledge_keys = knowledge_keys.view(batch_size, self.nhead, self.head_dim)
        
        # Compute position-aware knowledge matrix
        # For each position, compute a position-specific knowledge vector
        
        # First, create position-specific context
        # [seq_len, d_model] + [batch_size, 1, d_model] -> [batch_size, seq_len, d_model]
        pos_context = enhanced_context.unsqueeze(1) + pos_encodings.unsqueeze(0)
        
        # Project to knowledge space [batch_size, seq_len, d_model] -> [batch_size, seq_len, nhead, head_dim]
        pos_queries = self.knowledge_bias_query_proj(pos_context.view(-1, self.d_model)).view(
            batch_size, seq_len, self.nhead, self.head_dim
        )
        pos_keys = self.knowledge_bias_key_proj(pos_context.view(-1, self.d_model)).view(
            batch_size, seq_len, self.nhead, self.head_dim
        )
        
        # Compute knowledge attention scores
        # Using Einstein summation for efficient computation
        # [batch_size, nhead, seq_len, head_dim] @ [batch_size, nhead, head_dim, seq_len] 
        # -> [batch_size, nhead, seq_len, seq_len]
        knowledge_bias = torch.einsum(
            'bnsh,bnht->bnst',
            pos_queries.permute(0, 2, 1, 3),  # [batch_size, nhead, seq_len, head_dim]
            pos_keys.permute(0, 2, 3, 1)  # [batch_size, nhead, head_dim, seq_len]
        )
        
        # Scale by sqrt(head_dim)
        knowledge_bias = knowledge_bias / math.sqrt(self.head_dim)
        
        # Apply knowledge gating
        knowledge_gate = torch.sigmoid(self.knowledge_bias_gate(enhanced_context))  # [batch_size, nhead]
        knowledge_bias = knowledge_bias * knowledge_gate.view(batch_size, self.nhead, 1, 1)
        
        return knowledge_bias
    
    def compute_contextual_bias(self, context_embedding, seq_len, task_id=None, attention_history=None):
        """
        Compute contextual bias matrix based on context embedding.
        
        Implements the full bias matrix formula:
            B_t = ∑_{i=1}^{n} α_i · B_i^{task} + ∑_{j=1}^{m} β_j · B_j^{history} + ∑_{k=1}^{p} γ_k · B_k^{knowledge}
        
        Args:
            context_embedding: Context embedding [batch_size, d_model]
            seq_len: Sequence length
            task_id: Task identifier(s) for task-specific bias
            attention_history: Optional history of attention patterns
            
        Returns:
            torch.Tensor: Contextual bias matrix [batch_size, nhead, seq_len, seq_len]
        """
        batch_size = context_embedding.size(0)
        device = context_embedding.device
        
        # Get position encodings for this sequence length
        position_encodings = self.position_encodings[:seq_len]
        
        # Get task embedding if task_id is provided
        if task_id is not None:
            if isinstance(task_id, int):
                task_embedding = self.task_embeddings[task_id].unsqueeze(0).expand(batch_size, -1)
            elif isinstance(task_id, torch.Tensor):
                if task_id.dim() == 1:  # [batch_size]
                    task_embedding = self.task_embeddings[task_id]
                else:  # One-hot or soft weights [batch_size, max_tasks]
                    task_embedding = torch.matmul(task_id, self.task_embeddings)
            else:
                task_embedding = self.task_embeddings[0].unsqueeze(0).expand(batch_size, -1)
        else:
            # Default task embedding
            task_embedding = self.task_embeddings[0].unsqueeze(0).expand(batch_size, -1)
        
        # Enhance context with task information
        context_with_task = context_embedding + task_embedding
        enhanced_context = self.context_norm(context_with_task)
        enhanced_context = enhanced_context + self.context_transform(enhanced_context)
        
        # Initialize bias components dictionary
        bias_components = {}
        
        # Compute task-specific bias
        if 'task' in self.bias_types:
            bias_components['task'] = self.compute_task_bias(
                enhanced_context, seq_len, position_encodings
            )
        
        # Compute history-based bias
        if 'history' in self.bias_types:
            bias_components['history'] = self.compute_history_bias(
                enhanced_context, seq_len
            )
            
            # If attention history is provided, incorporate it
            if attention_history is not None:
                # Assuming attention_history is a list of previous attention matrices
                # We use exponential decay to weigh recent attention patterns more heavily
                history_bias = bias_components['history']
                
                for i, prev_attn in enumerate(reversed(attention_history[-5:])):  # Use last 5 attention patterns
                    # Apply exponential decay based on recency
                    decay = 0.7 ** (i + 1)  # More recent = less decay
                    
                    # Ensure previous attention has the right shape
                    if prev_attn.dim() == 4:  # [batch_size, nhead, seq_len_prev, seq_len_prev]
                        prev_seq_len = min(prev_attn.size(2), seq_len)
                        
                        # Add to history bias, padding if necessary
                        if prev_seq_len == seq_len:
                            history_bias[:, :, :prev_seq_len, :prev_seq_len] += decay * prev_attn
                        else:
                            history_bias[:, :, :prev_seq_len, :prev_seq_len] += decay * prev_attn[:, :, :prev_seq_len, :prev_seq_len]
                
                bias_components['history'] = history_bias
        
        # Compute knowledge-based bias
        if 'knowledge' in self.bias_types:
            bias_components['knowledge'] = self.compute_knowledge_bias(
                enhanced_context, seq_len, position_encodings
            )
        
        # Combine bias components using learned weights
        # Implement the weighted sum from the mathematical formulation
        normalized_weights = F.softmax(self.bias_weights, dim=0)
        
        # Initialize combined bias
        combined_bias = torch.zeros(
            batch_size, self.nhead, seq_len, seq_len, device=device
        )
        
        # Add each component with its weight
        for i, bias_type in enumerate(self.bias_types):
            if bias_type in bias_components:
                combined_bias += normalized_weights[i] * bias_components[bias_type]
        
        # Add cross-bias interactions (optional enhancement)
        if len(bias_components) > 1:
            # Concatenate all bias components and compute a cross-bias gate
            stacked_biases = torch.cat(
                [bias_components[bias_type].mean(dim=[2, 3]) for bias_type in self.bias_types if bias_type in bias_components],
                dim=1  # Concatenate along feature dimension
            )  # [batch_size, len(bias_types) * nhead]
            
            cross_gate = self.cross_bias_gate(stacked_biases)  # [batch_size, nhead]
            combined_bias = combined_bias * cross_gate.view(batch_size, self.nhead, 1, 1)
        
        # Apply final bias adjustment
        combined_bias = combined_bias * self.bias_scale
        
        return combined_bias
    
    def forward(
        self,
        query: torch.Tensor,
        key: torch.Tensor,
        value: torch.Tensor,
        context_embedding: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        task_id: Optional[Union[int, torch.Tensor]] = None,
        attention_history: Optional[List[torch.Tensor]] = None,
        return_bias: bool = False
    ) -> Union[torch.Tensor, Tuple[torch.Tensor, torch.Tensor]]:
        """
        Forward pass for Contextual Bias Matrix.
        
        Args:
            query: Query tensor [batch_size, seq_len, d_model]
            key: Key tensor [batch_size, seq_len, d_model]
            value: Value tensor [batch_size, seq_len, d_model]
            context_embedding: Context embedding [batch_size, d_model]
            attention_mask: Attention mask [batch_size, 1, seq_len, seq_len]
            task_id: Task identifier(s) for task-specific bias
            attention_history: Optional history of attention patterns
            return_bias: Whether to return the computed bias matrix
            
        Returns:
            torch.Tensor or tuple: Contextual bias matrix or tuple with additional info
        """
        batch_size, seq_len = query.size(0), query.size(1)
        
        # Compute contextual bias matrix
        contextual_bias = self.compute_contextual_bias(
            context_embedding, seq_len, task_id, attention_history
        )
        
        if return_bias:
            return contextual_bias
        else:
            return contextual_bias
    
    def combine_with_attention(
        self,
        attention_scores: torch.Tensor,
        context_embedding: torch.Tensor,
        task_id: Optional[Union[int, torch.Tensor]] = None,
        attention_history: Optional[List[torch.Tensor]] = None
    ) -> torch.Tensor:
        """
        Combine attention scores with contextual bias.
        
        Args:
            attention_scores: Raw attention scores [batch_size, nhead, seq_len, seq_len]
            context_embedding: Context embedding [batch_size, d_model]
            task_id: Task identifier(s) for task-specific bias
            attention_history: Optional history of attention patterns
            
        Returns:
            torch.Tensor: Attention scores with contextual bias applied
        """
        seq_len = attention_scores.size(2)
        
        # Compute contextual bias
        contextual_bias = self.compute_contextual_bias(
            context_embedding, seq_len, task_id, attention_history
        )
        
        # Add bias to attention scores
        biased_scores = attention_scores + contextual_bias
        
        return biased_scores
    
    def update_bias_weights(self, performance_gradient, learning_rate=0.01):
        """
        Update bias weights based on performance gradient.
        
        This method implements a form of self-evolution, allowing the system to adjust
        the relative importance of different bias types based on their contribution
        to overall performance.
        
        Args:
            performance_gradient: Gradient of performance metric
            learning_rate: Learning rate for update
        """
        with torch.no_grad():
            # Convert gradient to tensor if it's a scalar
            if isinstance(performance_gradient, float):
                performance_gradient = torch.tensor(
                    performance_gradient, device=self.bias_weights.device
                )
            
            # Update weights based on gradient (uses a form of policy gradient)
            gradient = performance_gradient * F.softmax(self.bias_weights, dim=0)
            self.bias_weights.data += learning_rate * gradient
            
            # Ensure weights don't get too extreme
            self.bias_weights.data = torch.clamp(self.bias_weights.data, min=-10.0, max=10.0)
    
    def get_bias_weights(self) -> Dict[str, float]:
        """
        Get the current weights for different bias types.
        
        Returns:
            dict: Mapping from bias types to their normalized weights
        """
        weights = F.softmax(self.bias_weights, dim=0)
        return {bias_type: weights[i].item() for i, bias_type in enumerate(self.bias_types)}
    
    def reset_evolution(self):
        """Reset self-evolution state."""
        nn.init.constant_(self.bias_weights, 1.0 / len(self.bias_types))
    
    def extra_repr(self) -> str:
        """
        Return extra representation string.
        """
        return (f'bias_types={self.bias_types}, '
                f'bias_scale={self.bias_scale}, '
                f'd_model={self.d_model}, '
                f'nhead={self.nhead}')