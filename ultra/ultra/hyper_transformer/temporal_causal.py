#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Temporal-Causal Transformer Module for ULTRA

This module implements the Temporal-Causal Transformer, which explicitly models temporal
relationships and causality. It extends traditional transformer architectures by:

1. Implementing temporal encoding that captures both absolute and relative timing
2. Defining causal attention masks based on inferred causal relationships
3. Enabling explicit causal modeling between events with temporal constraints

Key mathematical formulations:
- Temporal Encoding: TE(t_i, t_j) = f_abs(t_i) + f_rel(t_i - t_j)
- Causal Attention Masks: M_ij = 0 if event j causally influences event i, -∞ otherwise
- Explicit Causal Modeling: P(E_i causes E_j) = σ(f_causal(h_i, h_j, Δt_ij))

References:
- ULTRA Technical Paper v1.0, Section 5.4
"""

import logging
import math
from typing import Dict, List, Tuple, Optional, Union, Any

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

# Configure logging
logger = logging.getLogger(__name__)

class TemporalEncoding(nn.Module):
    """
    Temporal Encoding that captures both absolute and relative temporal information.
    
    The encoding consists of two components:
    - Absolute encoding (f_abs): Standard positional encoding for absolute positions
    - Relative encoding (f_rel): Specialized encoding for temporal distances
    
    These are combined as: TE(t_i, t_j) = f_abs(t_i) + f_rel(t_i - t_j)
    """
    
    def __init__(
        self, 
        d_model: int, 
        max_len: int = 10000, 
        dropout: float = 0.1,
        use_learnable_projections: bool = True,
        device: Optional[torch.device] = None
    ):
        """
        Initialize the Temporal Encoding module.
        
        Args:
            d_model: Model dimension (must be even)
            max_len: Maximum sequence length
            dropout: Dropout probability for encodings
            use_learnable_projections: Whether to use learnable projections for encodings
            device: Device to use (CPU/GPU)
        """
        super().__init__()
        
        if d_model % 2 != 0:
            raise ValueError(f"d_model must be even, got {d_model}")
            
        # Set device
        self.device = device if device is not None else torch.device(
            'cuda' if torch.cuda.is_available() else 'cpu'
        )
        
        self.d_model = d_model
        self.max_len = max_len
        self.dropout = nn.Dropout(p=dropout)
        self.use_learnable_projections = use_learnable_projections
        
        half_d_model = d_model // 2
        
        # Create absolute position encodings
        position = torch.arange(0, max_len, dtype=torch.float, device=self.device).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, half_d_model, 2, device=self.device).float() * 
                           (-math.log(10000.0) / half_d_model))
        
        # Initialize absolute position encodings
        pe_abs = torch.zeros(max_len, half_d_model, device=self.device)
        pe_abs[:, 0::2] = torch.sin(position * div_term)
        pe_abs[:, 1::2] = torch.cos(position * div_term)
        self.register_buffer('pe_abs', pe_abs)
        
        # Create relative distance encodings
        # We'll create encodings for distances from -max_len+1 to max_len-1
        total_rel_positions = 2 * max_len - 1
        rel_distances = torch.arange(-max_len+1, max_len, dtype=torch.float, device=self.device).unsqueeze(1)
        div_term_rel = torch.exp(torch.arange(0, half_d_model, 2, device=self.device).float() * 
                               (-math.log(10000.0) / half_d_model))
        
        # Initialize relative distance encodings
        pe_rel = torch.zeros(total_rel_positions, half_d_model, device=self.device)
        pe_rel[:, 0::2] = torch.sin(rel_distances * div_term_rel)
        pe_rel[:, 1::2] = torch.cos(rel_distances * div_term_rel)
        self.register_buffer('pe_rel', pe_rel)
        
        # Learnable projections for absolute and relative encodings
        if use_learnable_projections:
            self.abs_projection = nn.Linear(half_d_model, half_d_model)
            self.rel_projection = nn.Linear(half_d_model, half_d_model)
            # Initialize with small values for stability
            nn.init.xavier_normal_(self.abs_projection.weight, gain=0.02)
            nn.init.xavier_normal_(self.rel_projection.weight, gain=0.02)
        
        logger.info(f"Temporal Encoding initialized with d_model={d_model}, max_len={max_len}")
    
    def forward(
        self, 
        positions: torch.Tensor, 
        rel_distances: Optional[torch.Tensor] = None
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Generate temporal encodings for given positions and relative distances.
        
        Args:
            positions: Absolute positions of shape (batch_size, seq_len)
            rel_distances: Relative distances of shape (batch_size, seq_len, seq_len)
                          If None, computed from positions
        
        Returns:
            tuple: (absolute_encodings, relative_encodings)
                absolute_encodings: Tensor of shape (batch_size, seq_len, d_model//2)
                relative_encodings: Tensor of shape (batch_size, seq_len, seq_len, d_model//2)
        """
        batch_size, seq_len = positions.shape
        
        # Get absolute position encodings
        abs_encodings = self.pe_abs[positions]  # (batch_size, seq_len, d_model//2)
        
        if self.use_learnable_projections:
            abs_encodings = self.abs_projection(abs_encodings)
        
        # Apply dropout
        abs_encodings = self.dropout(abs_encodings)
        
        # Compute relative distances if not provided
        if rel_distances is None:
            # Create pairs of positions
            pos_i = positions.unsqueeze(2).expand(-1, -1, seq_len)  # (batch_size, seq_len, seq_len)
            pos_j = positions.unsqueeze(1).expand(-1, seq_len, -1)  # (batch_size, seq_len, seq_len)
            
            # Compute distances: pos_j - pos_i
            rel_distances = pos_j - pos_i  # (batch_size, seq_len, seq_len)
        
        # Shift distances to be in the range [0, 2*max_len-1)
        rel_indices = rel_distances + (self.max_len - 1)
        
        # Clip indices to valid range
        rel_indices = torch.clamp(rel_indices, 0, 2 * self.max_len - 2).long()
        
        # Get relative distance encodings (batch_size, seq_len, seq_len, d_model//2)
        rel_encodings = self.pe_rel[rel_indices]
        
        if self.use_learnable_projections:
            # Reshape for efficient computation
            batch_seq_seq = batch_size * seq_len * seq_len
            rel_encodings_flat = rel_encodings.view(batch_seq_seq, -1)
            rel_encodings_flat = self.rel_projection(rel_encodings_flat)
            rel_encodings = rel_encodings_flat.view(batch_size, seq_len, seq_len, -1)
        
        # Apply dropout
        rel_encodings = self.dropout(rel_encodings)
        
        return abs_encodings, rel_encodings


class CausalAttention(nn.Module):
    """
    Causal Attention mechanism that enforces causality constraints.
    
    This extends standard attention by:
    1. Incorporating explicit causal relations between tokens
    2. Using temporal information to inform attention weights
    3. Enforcing causality constraints (causes must precede effects)
    
    The causal attention computes: 
        Attention(Q, K, V) = softmax((QK^T/√d_k + M_causal) V
    with M_causal incorporating both temporal and causal information.
    """
    
    def __init__(
        self, 
        d_model: int, 
        nhead: int, 
        dropout: float = 0.1, 
        causal_bias_alpha: float = 0.5,
        use_flash_attn: bool = False,
        device: Optional[torch.device] = None
    ):
        """
        Initialize the Causal Attention module.
        
        Args:
            d_model: Model dimension
            nhead: Number of attention heads
            dropout: Dropout probability
            causal_bias_alpha: Scaling factor for causal bias
            use_flash_attn: Whether to use FlashAttention if available
            device: Device to use (CPU/GPU)
        """
        super().__init__()
        
        # Set device
        self.device = device if device is not None else torch.device(
            'cuda' if torch.cuda.is_available() else 'cpu'
        )
        
        self.d_model = d_model
        self.nhead = nhead
        self.dropout_p = dropout
        self.d_head = d_model // nhead
        self.causal_bias_alpha = causal_bias_alpha
        self.use_flash_attn = use_flash_attn and _has_flash_attention
        
        # Check that d_model is divisible by nhead
        if d_model % nhead != 0:
            raise ValueError(f"d_model ({d_model}) must be divisible by nhead ({nhead})")
        
        # Linear projections
        self.q_projection = nn.Linear(d_model, d_model)
        self.k_projection = nn.Linear(d_model, d_model)
        self.v_projection = nn.Linear(d_model, d_model)
        self.output_projection = nn.Linear(d_model, d_model)
        
        # Initialize weights
        for projection in [self.q_projection, self.k_projection, self.v_projection, self.output_projection]:
            nn.init.xavier_uniform_(projection.weight)
            nn.init.zeros_(projection.bias)
        
        # Causal bias function (maps relative temporal encodings to attention biases)
        self.causal_bias_function = nn.Sequential(
            nn.Linear(d_model // 2, d_model // 4),
            nn.LayerNorm(d_model // 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 4, nhead)
        )
        
        # Causal strength estimator
        self.causal_estimator = nn.Bilinear(d_model, d_model, 1)
        
        # Learnable scale for attention 
        self.scale = nn.Parameter(torch.tensor(1.0 / math.sqrt(self.d_head)))
        
        # Dropout
        self.dropout = nn.Dropout(dropout)
        
        logger.info(f"Causal Attention initialized with {nhead} heads, d_model={d_model}, "
                   f"causal_bias_alpha={causal_bias_alpha}")
    
    def estimate_causal_strength(
        self, 
        src: torch.Tensor, 
        temporal_diffs: torch.Tensor
    ) -> torch.Tensor:
        """
        Estimate causal strength between events.
        
        Implements P(E_i causes E_j) = σ(f_causal(h_i, h_j, Δt_ij))
        
        Args:
            src: Source tensor of shape (batch_size, seq_len, d_model)
            temporal_diffs: Temporal differences of shape (batch_size, seq_len, seq_len)
        
        Returns:
            torch.Tensor: Causal strength matrix of shape (batch_size, seq_len, seq_len)
        """
        batch_size, seq_len, _ = src.shape
        
        # Create pairs of representations
        h_i = src.unsqueeze(2).expand(-1, -1, seq_len, -1)  # (batch_size, seq_len, seq_len, d_model)
        h_j = src.unsqueeze(1).expand(-1, seq_len, -1, -1)  # (batch_size, seq_len, seq_len, d_model)
        
        # Flatten for efficient computation
        h_i_flat = h_i.reshape(batch_size * seq_len * seq_len, -1)
        h_j_flat = h_j.reshape(batch_size * seq_len * seq_len, -1)
        
        # Compute causal strength
        causal_strength = self.causal_estimator(h_i_flat, h_j_flat).view(batch_size, seq_len, seq_len)
        
        # Apply sigmoid to get probabilities
        causal_strength = torch.sigmoid(causal_strength)
        
        # Apply temporal constraint: cause must precede effect
        # Set causal strength to 0 where the temporal difference is negative or zero
        causal_mask = (temporal_diffs > 0).float()
        causal_strength = causal_strength * causal_mask
        
        return causal_strength
    
    def forward(
        self, 
        query: torch.Tensor, 
        key: torch.Tensor, 
        value: torch.Tensor,
        positions: Optional[torch.Tensor] = None,
        temporal_distances: Optional[torch.Tensor] = None,
        causal_mask: Optional[torch.Tensor] = None,
        attn_mask: Optional[torch.Tensor] = None,
        bias_matrix: Optional[torch.Tensor] = None,
        need_weights: bool = False
    ) -> Tuple[torch.Tensor, Optional[torch.Tensor]]:
        """
        Forward pass for Causal Attention.
        
        Args:
            query: Query tensor (batch_size, seq_len, d_model)
            key: Key tensor (batch_size, seq_len, d_model)
            value: Value tensor (batch_size, seq_len, d_model)
            positions: Position indices (batch_size, seq_len)
            temporal_distances: Temporal distance matrix (batch_size, seq_len, seq_len)
            causal_mask: Explicit causal mask (batch_size, seq_len, seq_len)
            attn_mask: Attention mask (batch_size, seq_len, seq_len)
            bias_matrix: Additional bias matrix from external source
            need_weights: Whether to return attention weights
        
        Returns:
            Tuple[torch.Tensor, Optional[torch.Tensor]]: 
                output tensor (batch_size, seq_len, d_model)
                attention weights if need_weights=True
        """
        batch_size, seq_len, _ = query.shape
        
        # Project query, key, value
        q = self.q_projection(query)  # (batch_size, seq_len, d_model)
        k = self.k_projection(key)    # (batch_size, seq_len, d_model)
        v = self.v_projection(value)  # (batch_size, seq_len, d_model)
        
        # Reshape for multi-head attention
        q = q.view(batch_size, seq_len, self.nhead, self.d_head).transpose(1, 2)  # (batch_size, nhead, seq_len, d_head)
        k = k.view(batch_size, seq_len, self.nhead, self.d_head).transpose(1, 2)  # (batch_size, nhead, seq_len, d_head)
        v = v.view(batch_size, seq_len, self.nhead, self.d_head).transpose(1, 2)  # (batch_size, nhead, seq_len, d_head)
        
        # Check if we can use FlashAttention
        if self.use_flash_attn and temporal_distances is None and causal_mask is None and bias_matrix is None:
            try:
                from flash_attn import flash_attn_func
                
                # Move to contiguous memory format required by FlashAttention
                q = q.contiguous()
                k = k.contiguous()
                v = v.contiguous()
                
                # Apply FlashAttention (which uses scaled dot-product attention internally)
                # FlashAttention expects inputs in shape (batch, seq_len, nhead, d_head)
                q_fa = q.transpose(1, 2)
                k_fa = k.transpose(1, 2)
                v_fa = v.transpose(1, 2)
                
                # Create attention mask for FlashAttention
                fa_mask = None
                if attn_mask is not None:
                    # Convert boolean mask to float mask where 0 = keep, -inf = mask
                    fa_mask = attn_mask.float().masked_fill(~attn_mask, -float('inf'))
                
                # Apply FlashAttention
                output = flash_attn_func(q_fa, k_fa, v_fa, attn_mask=fa_mask, dropout_p=self.dropout_p)
                
                # Reshape back
                output = output.transpose(1, 2).reshape(batch_size, seq_len, self.d_model)
                
                # Final projection
                output = self.output_projection(output)
                
                # Return output and optionally dummy attention weights (since FlashAttention doesn't return them)
                if need_weights:
                    # Create dummy attention weights (uniform distribution)
                    attn_weights = torch.ones(batch_size, self.nhead, seq_len, seq_len, device=output.device)
                    attn_weights = attn_weights / seq_len
                    return output, attn_weights
                
                return output, None
                
            except (ImportError, RuntimeError) as e:
                # Fall back to standard attention if FlashAttention fails
                logger.warning(f"FlashAttention failed, falling back to standard attention: {e}")
                self.use_flash_attn = False
        
        # Compute attention scores: (batch_size, nhead, seq_len, seq_len)
        attn_scores = torch.matmul(q, k.transpose(-2, -1)) * self.scale
        
        # Add relative position bias if temporal distances are provided
        if temporal_distances is not None:
            # Get relative encodings using the TemporalEncoding module
            # This would typically be done at a higher level, but we'll compute a simplified version here
            rel_indices = temporal_distances + seq_len - 1
            rel_indices = torch.clamp(rel_indices, 0, 2 * seq_len - 2).long()
            
            # Create a simplified relative position encoding
            position = torch.arange(0, 2 * seq_len - 1, dtype=torch.float, device=self.device).unsqueeze(1)
            div_term = torch.exp(torch.arange(0, self.d_head // 2, 2, device=self.device).float() * 
                              (-math.log(10000.0) / (self.d_head // 2)))
            
            pe_rel = torch.zeros(2 * seq_len - 1, self.d_head // 2, device=self.device)
            pe_rel[:, 0::2] = torch.sin(position * div_term)
            pe_rel[:, 1::2] = torch.cos(position * div_term)
            
            # Get relative encodings
            rel_encodings = pe_rel[rel_indices]  # (batch_size, seq_len, seq_len, d_head//2)
            
            # Project to attention bias
            rel_encodings_flat = rel_encodings.view(batch_size * seq_len * seq_len, -1)
            rel_bias = self.causal_bias_function(rel_encodings_flat)
            rel_bias = rel_bias.view(batch_size, seq_len, seq_len, self.nhead)
            rel_bias = rel_bias.permute(0, 3, 1, 2)  # (batch_size, nhead, seq_len, seq_len)
            
            # Add to attention scores
            attn_scores = attn_scores + rel_bias
        
        # Apply causal bias if available
        if causal_mask is not None:
            # Expand causal mask for all heads
            causal_bias = causal_mask.unsqueeze(1)  # (batch_size, 1, seq_len, seq_len)
            
            # Scale by alpha and apply
            causal_bias = causal_bias * self.causal_bias_alpha
            attn_scores = attn_scores + causal_bias
        
        # Apply additional bias matrix if provided (e.g., from contextual bias module)
        if bias_matrix is not None:
            attn_scores = attn_scores + bias_matrix
        
        # Apply attention mask if provided
        if attn_mask is not None:
            # Expand mask for all heads if needed
            if attn_mask.dim() == 3:
                attn_mask = attn_mask.unsqueeze(1)
            
            # Apply mask
            attn_scores = attn_scores.masked_fill(attn_mask == 0, -1e9)
        
        # Apply standard causal mask (lower triangular) if needed
        if temporal_distances is not None and causal_mask is None:
            # Create causal mask based on temporal ordering
            standard_causal_mask = (temporal_distances <= 0).unsqueeze(1)
            attn_scores = attn_scores.masked_fill(standard_causal_mask, -1e9)
        
        # Apply softmax and dropout
        attn_probs = F.softmax(attn_scores, dim=-1)
        attn_probs = self.dropout(attn_probs)
        
        # Apply attention to values
        output = torch.matmul(attn_probs, v)  # (batch_size, nhead, seq_len, d_head)
        
        # Reshape back
        output = output.transpose(1, 2).contiguous().view(batch_size, seq_len, self.d_model)
        
        # Final projection
        output = self.output_projection(output)
        
        if need_weights:
            # Average attention weights over heads
            attn_weights = attn_probs.mean(dim=1) if self.nhead > 1 else attn_probs.squeeze(1)
            return output, attn_weights
        
        return output, None


class TemporalCausalTransformer(nn.Module):
    """
    Temporal-Causal Transformer explicitly models temporal relationships and causality.
    
    Key innovations:
    1. Temporal Encoding: TE(t_i, t_j) = f_abs(t_i) + f_rel(t_i - t_j)
    2. Causal Attention Masks: M_ij = 0 if event j causally influences event i, -∞ otherwise
    3. Explicit Causal Modeling: P(E_i causes E_j) = σ(f_causal(h_i, h_j, Δt_ij))
    
    This enables the model to distinguish between correlation and causation,
    and to capture temporal relationships at multiple timescales.
    """
    
    def __init__(
        self, 
        d_model: int, 
        nhead: int, 
        dim_feedforward: int = 2048, 
        dropout: float = 0.1,
        activation: str = "relu",
        contextual_bias: Optional[nn.Module] = None,
        attention_mechanism: Optional[nn.Module] = None,
        layer_norm_eps: float = 1e-5,
        causal_bias_alpha: float = 0.5,
        use_flash_attn: bool = False,
        device: Optional[torch.device] = None
    ):
        """
        Initialize the Temporal-Causal Transformer.
        
        Args:
            d_model: Model dimension
            nhead: Number of attention heads
            dim_feedforward: Dimension of the feedforward network
            dropout: Dropout probability
            activation: Activation function ("relu", "gelu", "silu")
            contextual_bias: Optional contextual bias module
            attention_mechanism: Optional attention mechanism (defaults to CausalAttention)
            layer_norm_eps: Layer normalization epsilon
            causal_bias_alpha: Scaling factor for causal bias
            use_flash_attn: Whether to use FlashAttention if available
            device: Device to use (CPU/GPU)
        """
        super().__init__()
        
        # Set device
        self.device = device if device is not None else torch.device(
            'cuda' if torch.cuda.is_available() else 'cpu'
        )
        
        self.d_model = d_model
        self.nhead = nhead
        self.dim_feedforward = dim_feedforward
        self.dropout = dropout
        self.contextual_bias = contextual_bias
        self.layer_norm_eps = layer_norm_eps
        
        # Set up attention mechanism
        if attention_mechanism is None:
            self.self_attn = CausalAttention(
                d_model=d_model,
                nhead=nhead,
                dropout=dropout,
                causal_bias_alpha=causal_bias_alpha,
                use_flash_attn=use_flash_attn,
                device=device
            )
        else:
            self.self_attn = attention_mechanism
        
        # Feedforward network
        self.feed_forward = nn.Sequential(
            nn.Linear(d_model, dim_feedforward),
            self._get_activation_fn(activation),
            nn.Dropout(dropout),
            nn.Linear(dim_feedforward, d_model)
        )
        
        # Layer normalization
        self.norm1 = nn.LayerNorm(d_model, eps=layer_norm_eps)
        self.norm2 = nn.LayerNorm(d_model, eps=layer_norm_eps)
        
        # Dropout
        self.dropout1 = nn.Dropout(dropout)
        self.dropout2 = nn.Dropout(dropout)
        
        # Temporal modeling components
        self.temporal_modeling = True
        
        # Causal relation estimator
        self.causal_estimator = nn.Sequential(
            nn.Linear(2 * d_model + 1, d_model),  # +1 for temporal distance
            nn.LayerNorm(d_model, eps=layer_norm_eps),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model // 2),
            nn.LayerNorm(d_model // 2, eps=layer_norm_eps),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, 1),
            nn.Sigmoid()
        )
        
        # Initialize weights
        self._init_weights()
        
        logger.info(f"Temporal-Causal Transformer initialized with d_model={d_model}, nhead={nhead}")
    
    def _init_weights(self):
        """Initialize the weights for the feed-forward network."""
        for name, p in self.named_parameters():
            if 'feed_forward' in name and p.dim() > 1:
                nn.init.xavier_uniform_(p)
    
    def _get_activation_fn(self, activation):
        """Get activation function by name."""
        if activation == "relu":
            return nn.ReLU()
        elif activation == "gelu":
            return nn.GELU()
        elif activation == "silu" or activation == "swish":
            return nn.SiLU()
        else:
            raise ValueError(f"Unknown activation function: {activation}")
    
    def estimate_causal_relations(
        self, 
        src: torch.Tensor, 
        temporal_distances: torch.Tensor
    ) -> torch.Tensor:
        """
        Estimate causal relations between events.
        
        Implements P(E_i causes E_j) = σ(f_causal(h_i, h_j, Δt_ij))
        
        Args:
            src: Source tensor of shape (seq_len, batch_size, d_model)
            temporal_distances: Temporal distances of shape (batch_size, seq_len, seq_len)
        
        Returns:
            torch.Tensor: Causal relation matrix of shape (batch_size, seq_len, seq_len)
        """
        # Transpose src to (batch_size, seq_len, d_model) if necessary
        if src.dim() == 3 and src.size(1) != temporal_distances.size(1):
            src = src.transpose(0, 1)
        
        batch_size, seq_len, _ = src.shape
        
        # Create pairs of representations
        h_i = src.unsqueeze(2).expand(-1, -1, seq_len, -1)  # (batch_size, seq_len, seq_len, d_model)
        h_j = src.unsqueeze(1).expand(-1, seq_len, -1, -1)  # (batch_size, seq_len, seq_len, d_model)
        
        # Normalize temporal distances for better training stability
        max_dist = temporal_distances.abs().max().item()
        if max_dist > 0:
            norm_temp_diffs = temporal_distances.float() / max_dist
        else:
            norm_temp_diffs = temporal_distances.float()
        
        # Reshape and concatenate for efficient computation
        temp_diffs_flat = norm_temp_diffs.view(batch_size * seq_len * seq_len, 1)
        h_i_flat = h_i.view(batch_size * seq_len * seq_len, -1)
        h_j_flat = h_j.view(batch_size * seq_len * seq_len, -1)
        
        # Concat features
        causal_features = torch.cat([h_i_flat, h_j_flat, temp_diffs_flat], dim=-1)
        
        # Estimate causal relations
        causal_probs = self.causal_estimator(causal_features).view(batch_size, seq_len, seq_len)
        
        # Apply temporal constraint: cause must precede effect
        causal_mask = (temporal_distances > 0).float()
        causal_probs = causal_probs * causal_mask
        
        return causal_probs
    
    def forward(
        self, 
        src: torch.Tensor,
        mask: Optional[torch.Tensor] = None,
        return_attention: bool = False,
        temporal_info: Optional[Dict[str, torch.Tensor]] = None
    ) -> Union[torch.Tensor, Tuple[torch.Tensor, torch.Tensor]]:
        """
        Forward pass for Temporal-Causal Transformer.
        
        Args:
            src: Input tensor of shape (seq_len, batch_size, d_model)
            mask: Attention mask
            return_attention: Whether to return attention weights
            temporal_info: Dictionary with temporal information, which may include:
                - positions: Position indices of shape (batch_size, seq_len)
                - temporal_distances: Temporal distance matrix (batch_size, seq_len, seq_len)
                - causal_mask: Explicit causal mask (batch_size, seq_len, seq_len)
        
        Returns:
            torch.Tensor or Tuple[torch.Tensor, torch.Tensor]: 
                Output tensor (seq_len, batch_size, d_model)
                Attention weights if return_attention=True
        """
        # Extract sequence and batch dimensions
        seq_len, batch_size, _ = src.shape
        
        # Get positions, temporal distances, and causal mask from temporal_info if provided
        positions = None
        temporal_distances = None
        causal_mask = None
        if temporal_info is not None:
            positions = temporal_info.get('positions')
            temporal_distances = temporal_info.get('temporal_distances')
            causal_mask = temporal_info.get('causal_mask')
        
        # If temporal distances not provided, but positions are,
        # compute temporal distances from positions
        if temporal_distances is None and positions is not None:
            # Compute temporal distances
            pos_i = positions.unsqueeze(2).expand(-1, -1, positions.size(1))
            pos_j = positions.unsqueeze(1).expand(-1, positions.size(1), -1)
            temporal_distances = pos_j - pos_i  # (batch_size, seq_len, seq_len)
        
        # If no causal mask provided but temporal information available,
        # try to infer causal relations
        if causal_mask is None and temporal_distances is not None:
            causal_mask = self.estimate_causal_relations(src.transpose(0, 1), temporal_distances)
        
        # Get contextual bias if available
        bias_matrix = None
        if self.contextual_bias is not None:
            if hasattr(self.contextual_bias, 'get_bias_matrix'):
                bias_matrix = self.contextual_bias.get_bias_matrix(src)
            elif hasattr(self.contextual_bias, 'compute_contextual_bias'):
                bias_matrix = self.contextual_bias.compute_contextual_bias(src)
        
        # Self-attention with normalization (pre-norm architecture)
        src2, attn_weights = self.self_attn(
            query=self.norm1(src.transpose(0, 1)),
            key=self.norm1(src.transpose(0, 1)),
            value=self.norm1(src.transpose(0, 1)),
            positions=positions,
            temporal_distances=temporal_distances,
            causal_mask=causal_mask,
            attn_mask=mask,
            bias_matrix=bias_matrix,
            need_weights=return_attention
        )
        
        # Transpose back to (seq_len, batch_size, d_model)
        src2 = src2.transpose(0, 1)
        
        # Add & dropout (first residual connection)
        src = src + self.dropout1(src2)
        
        # Feed-forward with normalization (pre-norm architecture)
        src2 = self.feed_forward(self.norm2(src))
        
        # Add & dropout (second residual connection)
        output = src + self.dropout2(src2)
        
        if return_attention:
            return output, attn_weights
        
        return output
    
    def forward_with_context(
        self,
        src: torch.Tensor,
        mask: Optional[torch.Tensor] = None,
        contextual_bias: Optional[torch.Tensor] = None,
        temporal_info: Optional[Dict[str, torch.Tensor]] = None,
        return_attention: bool = False
    ) -> Union[torch.Tensor, Tuple[torch.Tensor, torch.Tensor]]:
        """
        Forward pass with explicit contextual information and temporal data.
        Designed for integration with higher-level transformers that provide context.
        
        Args:
            src: Input tensor (seq_len, batch_size, d_model)
            mask: Attention mask
            contextual_bias: External contextual bias matrix
            temporal_info: Temporal information for causal modeling
            return_attention: Whether to return attention weights
            
        Returns:
            Tuple[torch.Tensor, Optional[torch.Tensor]]: (output, attention_weights)
        """
        # Store external contextual bias temporarily
        original_bias = None
        if hasattr(self, 'contextual_bias') and self.contextual_bias is not None:
            original_bias = self.contextual_bias
        
        # Create temporary contextual bias if provided
        if contextual_bias is not None:
            class TempContextualBias:
                def __init__(self, bias_matrix):
                    self.bias_matrix = bias_matrix
                def get_bias_matrix(self, x):
                    return self.bias_matrix
            
            self.contextual_bias = TempContextualBias(contextual_bias)
        
        # Process with current settings
        result = self.forward(src, mask, return_attention, temporal_info)
        
        # Restore original contextual bias
        if original_bias is not None:
            self.contextual_bias = original_bias
        
        return result
    
    def use_temporal_info(self) -> bool:
        """
        Whether this layer can use temporal information.
        
        Returns:
            bool: True if the layer can use temporal information
        """
        return self.temporal_modeling
    
    def get_contextual_bias(
        self, 
        context_embeddings: torch.Tensor
    ) -> Optional[torch.Tensor]:
        """
        Generate contextual bias from context embeddings.
        
        Args:
            context_embeddings: Context embeddings for bias generation
            
        Returns:
            Optional[torch.Tensor]: Contextual bias matrix or None
        """
        if self.contextual_bias is not None:
            if hasattr(self.contextual_bias, 'compute_contextual_bias'):
                return self.contextual_bias.compute_contextual_bias(context_embeddings)
            elif hasattr(self.contextual_bias, 'get_bias_matrix'):
                return self.contextual_bias.get_bias_matrix(context_embeddings)
        return None
    
    def evolve(self, performance_metric: float) -> None:
        """
        Evolve the transformer based on performance metric.
        
        Args:
            performance_metric: Performance metric to guide evolution
        """
        # Example evolution logic:
        # - Adjust causal bias alpha based on performance
        if hasattr(self.self_attn, 'causal_bias_alpha'):
            old_alpha = self.self_attn.causal_bias_alpha
            # Adjust alpha based on performance (increase if performing well)
            new_alpha = old_alpha + 0.01 * (performance_metric - 0.5)
            # Keep within reasonable bounds
            new_alpha = max(0.1, min(1.0, new_alpha))
            self.self_attn.causal_bias_alpha = new_alpha
            
            if abs(new_alpha - old_alpha) > 0.01:
                logger.debug(f"Evolved causal_bias_alpha from {old_alpha:.4f} to {new_alpha:.4f}")


# Check for optional dependencies
_has_flash_attention = False
try:
    import flash_attn
    _has_flash_attention = True
    logger.info("FlashAttention found and will be used for optimized attention computation")
except ImportError:
    logger.info("FlashAttention not found, using standard attention implementation")