#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Recursive Transformer Module for ULTRA

This module implements the Recursive Transformer, which extends traditional transformer
architecture by allowing layers to recursively call themselves, creating a form of
"deep thinking" when complex problems require it. The implementation uses a learned
halting mechanism to dynamically determine the appropriate recursion depth for each input.

Key mathematical formulations:
- Recursive layer: h_i = F_i(h_{i-1}, d)
- Halting probability: p_i^t = σ(W_h · h_i^t + b_h)
- Halting condition: N_i = min{t : ∑_{j=1}^{t} p_i^j > 1 - ε or t = T_{max}}
- Final output: h_i = ∑_{t=1}^{N_i-1} p_i^t · h_i^t + (1 - ∑_{t=1}^{N_i-1} p_i^t) · h_i^{N_i}

References:
- ULTRA Technical Paper v1.0, Section 5.3
- "Universal Transformers" (<PERSON><PERSON><PERSON><PERSON> et al., 2019)
- "Adaptive Computation Time for Recurrent Neural Networks" (<PERSON>, 2016)
"""

import logging
import math
from typing import Dict, List, Tuple, Optional, Union, Any

import torch
import torch.nn as nn
import torch.nn.functional as F

# Configure logging
logger = logging.getLogger(__name__)

class RecursiveTransformer(nn.Module):
    """
    Recursive Transformer extends the standard transformer by allowing layers to
    recursively call themselves, creating a form of "deep thinking" when complex
    problems require it.
    
    The key innovation is a learned halting mechanism that dynamically determines
    the appropriate recursion depth for each input, allocating more computation 
    to complex inputs and less to simpler ones.
    """
    
    def __init__(
        self,
        d_model: int,
        nhead: int,
        dim_feedforward: int = 2048,
        dropout: float = 0.1,
        activation: str = "relu",
        max_recursion: int = 5,
        contextual_bias: Optional[nn.Module] = None,
        attention_mechanism: Optional[nn.Module] = None,
        layer_norm_eps: float = 1e-5,
        halting_threshold: float = 0.9,
        use_act: bool = True,
    ):
        """
        Initialize the Recursive Transformer.
        
        Args:
            d_model: Model dimension
            nhead: Number of attention heads
            dim_feedforward: Dimension of the feedforward network
            dropout: Dropout probability
            activation: Activation function ("relu", "gelu", "silu")
            max_recursion: Maximum recursion depth
            contextual_bias: Optional contextual bias module
            attention_mechanism: Optional attention mechanism (defaults to standard MultiheadAttention)
            layer_norm_eps: Layer normalization epsilon
            halting_threshold: Threshold for halting probability
            use_act: Whether to use Adaptive Computation Time (ACT) mechanism
        """
        super().__init__()
        self.d_model = d_model
        self.nhead = nhead
        self.dim_feedforward = dim_feedforward
        self.dropout = dropout
        self.max_recursion = max_recursion
        self.halting_threshold = halting_threshold
        self.use_act = use_act
        self.layer_norm_eps = layer_norm_eps
        
        # Set up attention mechanism
        if attention_mechanism is None:
            self.self_attn = nn.MultiheadAttention(
                d_model, nhead, dropout=dropout, batch_first=False
            )
        else:
            self.self_attn = attention_mechanism
        
        # Store contextual bias module if provided
        self.contextual_bias = contextual_bias
        
        # Feed-forward network
        self.linear1 = nn.Linear(d_model, dim_feedforward)
        self.dropout1 = nn.Dropout(dropout)
        self.linear2 = nn.Linear(dim_feedforward, d_model)
        
        # Layer normalization
        self.norm1 = nn.LayerNorm(d_model, eps=layer_norm_eps)
        self.norm2 = nn.LayerNorm(d_model, eps=layer_norm_eps)
        
        # Dropout
        self.dropout2 = nn.Dropout(dropout)
        self.dropout3 = nn.Dropout(dropout)
        
        # Activation function
        if activation == "relu":
            self.activation = F.relu
        elif activation == "gelu":
            self.activation = F.gelu
        elif activation == "silu" or activation == "swish":
            self.activation = F.silu
        else:
            raise ValueError(f"Unknown activation function: {activation}")
        
        # Halting network to determine recursion depth
        # Get activation function properly
        if activation.lower() == 'relu':
            activation_fn = nn.ReLU()
        elif activation.lower() == 'gelu':
            activation_fn = nn.GELU()
        elif activation.lower() in ['silu', 'swish']:
            activation_fn = nn.SiLU()
        else:
            activation_fn = nn.ReLU()  # Default fallback

        self.halting_network = nn.Sequential(
            nn.Linear(d_model, d_model // 4),
            nn.LayerNorm(d_model // 4, eps=layer_norm_eps),
            activation_fn,
            nn.Dropout(dropout),
            nn.Linear(d_model // 4, 1),
            nn.Sigmoid()
        )
        
        # Initialize ACT mechanism if used
        if use_act:
            self.act = ACT(d_model, threshold=halting_threshold)
        
        # Counter for tracking recursion statistics
        self.register_buffer('step_counter', torch.tensor(0, dtype=torch.long))
        self.register_buffer('total_steps', torch.tensor(0, dtype=torch.long))
        self.register_buffer('total_samples', torch.tensor(0, dtype=torch.long))
        
        logger.info(f"Initialized RecursiveTransformer with max_recursion={max_recursion}")
    
    def forward(
        self,
        src: torch.Tensor,
        mask: Optional[torch.Tensor] = None,
        return_attention: bool = False,
        return_halting_probs: bool = False
    ) -> Union[torch.Tensor, Tuple[torch.Tensor, ...]]:
        """
        Forward pass for the Recursive Transformer layer.
        
        Args:
            src: Input tensor of shape [seq_len, batch_size, d_model]
            mask: Optional attention mask
            return_attention: Whether to return attention weights
            return_halting_probs: Whether to return halting probabilities
            
        Returns:
            Tensor or tuple of tensors with processed outputs
        """
        if self.use_act:
            return self._forward_act(src, mask, return_attention, return_halting_probs)
        else:
            return self._forward_recursive(src, mask, return_attention, return_halting_probs)
    
    def _forward_recursive(
        self,
        src: torch.Tensor,
        mask: Optional[torch.Tensor] = None,
        return_attention: bool = False,
        return_halting_probs: bool = False
    ) -> Union[torch.Tensor, Tuple[torch.Tensor, ...]]:
        """
        Forward pass using explicit recursion with halting mechanism.
        
        Args:
            src: Input tensor of shape [seq_len, batch_size, d_model]
            mask: Optional attention mask
            return_attention: Whether to return attention weights
            return_halting_probs: Whether to return halting probabilities
            
        Returns:
            Tensor or tuple of tensors with processed outputs
        """
        # Initialize tracking
        self.step_counter = torch.tensor(0, dtype=torch.long, device=src.device)
        seq_len, batch_size, _ = src.shape
        attention_weights = None
        
        # Initialize states and halting mechanisms
        states = [src]
        halt_probs = []
        
        # Recursive processing
        for step in range(self.max_recursion):
            self.step_counter += 1
            
            # Get current state
            current_state = states[-1]
            
            # Compute halting probability: p_i^t = σ(W_h · h_i^t + b_h)
            # Aggregate over sequence dimension for global halting decision
            halt_logit = self.halting_network(current_state.mean(dim=0))
            halt_prob = halt_logit.squeeze(-1)  # [batch_size]
            halt_probs.append(halt_prob)
            
            # Process current state
            next_state, attn_weights = self._process_state(current_state, mask)
            states.append(next_state)
            
            # Store attention weights from last processing step
            if return_attention and step == 0:
                attention_weights = attn_weights
            
            # Check if we should halt based on accumulated probability
            if step == self.max_recursion - 1 or torch.all(torch.stack(halt_probs).sum(dim=0) >= self.halting_threshold):
                break
        
        # Compute weighted sum of states based on halting probabilities
        halt_probs = torch.stack(halt_probs)  # [steps, batch_size]
        
        # Normalized halting probabilities: Ensure they sum to exactly 1
        # This implements the remainder handling from the paper
        cumulative_halt = torch.cumsum(halt_probs, dim=0)
        under_threshold = cumulative_halt < self.halting_threshold
        halt_probs = halt_probs * under_threshold.float()
        
        # Add remainders for the final step
        remainders = torch.clamp(self.halting_threshold - cumulative_halt, min=0.0)
        final_step_indices = under_threshold.sum(dim=0)
        
        # Create a mask for the final step of each sample
        final_step_mask = torch.zeros_like(halt_probs)
        for b in range(batch_size):
            if final_step_indices[b] < halt_probs.size(0):
                final_step_mask[final_step_indices[b], b] = remainders[final_step_indices[b], b]
        
        # Combine original halting probs with remainders
        halt_probs = halt_probs + final_step_mask
        
        # Skip initial state, only use processed states
        states = torch.stack(states[1:])  # [steps, seq_len, batch_size, d_model]
        
        # Apply halting probabilities and sum across steps
        weighted_states = states * halt_probs.unsqueeze(1).unsqueeze(-1)  # Broadcasting
        output = weighted_states.sum(dim=0)  # [seq_len, batch_size, d_model]
        
        # Update recursion statistics
        self.total_steps += self.step_counter
        self.total_samples += batch_size
        
        # Return appropriate combination of outputs
        if return_attention and return_halting_probs:
            return output, attention_weights, halt_probs
        elif return_attention:
            return output, attention_weights
        elif return_halting_probs:
            return output, halt_probs
        else:
            return output
    
    def _forward_act(
        self,
        src: torch.Tensor,
        mask: Optional[torch.Tensor] = None,
        return_attention: bool = False,
        return_halting_probs: bool = False
    ) -> Union[torch.Tensor, Tuple[torch.Tensor, ...]]:
        """
        Forward pass using Adaptive Computation Time (ACT) mechanism.
        
        Args:
            src: Input tensor of shape [seq_len, batch_size, d_model]
            mask: Optional attention mask
            return_attention: Whether to return attention weights
            return_halting_probs: Whether to return halting probabilities
            
        Returns:
            Tensor or tuple of tensors with processed outputs
        """
        # Define the processing function for ACT
        def act_fn(x, mask=None):
            return self._process_state(x, mask)[0]  # Only return state, not attention
        
        # Apply ACT
        output, remainders, n_updates = self.act(
            src, act_fn, fn_args=(mask,)
        )
        
        # Update recursion statistics
        self.total_steps += n_updates.sum().long()
        self.total_samples += src.shape[1]  # batch_size
        
        # For attention weights, we need to do a separate forward pass
        # since ACT doesn't preserve them
        if return_attention:
            _, attention_weights = self._process_state(src, mask)
        
        # Return appropriate combination of outputs
        if return_attention and return_halting_probs:
            return output, attention_weights, n_updates
        elif return_attention:
            return output, attention_weights
        elif return_halting_probs:
            return output, n_updates
        else:
            return output
    
    def _process_state(
        self,
        src: torch.Tensor,
        mask: Optional[torch.Tensor] = None
    ) -> Tuple[torch.Tensor, Optional[torch.Tensor]]:
        """
        Process a single state with self-attention and feed-forward layers.
        
        Args:
            src: Input tensor of shape [seq_len, batch_size, d_model]
            mask: Optional attention mask
            
        Returns:
            Tuple of (processed state, attention weights)
        """
        # Apply contextual bias if available
        if self.contextual_bias is not None and hasattr(self.contextual_bias, "get_bias_matrix"):
            bias_matrix = self.contextual_bias.get_bias_matrix(src)
        else:
            bias_matrix = None
        
        # Self-attention block with bias
        if bias_matrix is not None:
            # Apply attention with contextual bias
            src2, attn_weights = self.self_attn(
                src, src, src, attn_mask=mask, bias_matrix=bias_matrix, need_weights=True
            )
        else:
            # Standard attention
            src2, attn_weights = self.self_attn(
                src, src, src, attn_mask=mask, need_weights=True
            )
        
        # Apply residual connection and normalization
        src = src + self.dropout2(src2)
        src = self.norm1(src)
        
        # Feed-forward block
        src2 = self.linear2(self.dropout1(self.activation(self.linear1(src))))
        src = src + self.dropout3(src2)
        src = self.norm2(src)
        
        return src, attn_weights
    
    def forward_with_context(
        self,
        src: torch.Tensor,
        mask: Optional[torch.Tensor] = None,
        contextual_bias: Optional[torch.Tensor] = None,
        temporal_info: Optional[Dict[str, torch.Tensor]] = None,
        return_attention: bool = False
    ) -> Tuple[torch.Tensor, Optional[torch.Tensor]]:
        """
        Forward pass with explicit contextual information and temporal data.
        Designed for integration with higher-level transformers that provide context.
        
        Args:
            src: Input tensor
            mask: Attention mask
            contextual_bias: External contextual bias matrix
            temporal_info: Temporal information for causal modeling
            return_attention: Whether to return attention weights
            
        Returns:
            Tuple of (output, attention_weights)
        """
        # Store external contextual bias temporarily
        original_bias = None
        if hasattr(self, 'contextual_bias') and self.contextual_bias is not None:
            original_bias = self.contextual_bias
        
        # Create temporary contextual bias if provided
        if contextual_bias is not None:
            class TempContextualBias:
                def __init__(self, bias_matrix):
                    self.bias_matrix = bias_matrix
                def get_bias_matrix(self, x):
                    return self.bias_matrix
            
            self.contextual_bias = TempContextualBias(contextual_bias)
        
        # Process with current settings
        result = self.forward(src, mask, return_attention=return_attention)
        
        # Restore original contextual bias
        if original_bias is not None:
            self.contextual_bias = original_bias
        
        return result
    
    def get_recursion_stats(self) -> Tuple[float, int, int]:
        """
        Get statistics about recursion depth.
        
        Returns:
            Tuple of (average recursion depth, total steps, total samples)
        """
        if self.total_samples > 0:
            avg_depth = self.total_steps.float() / self.total_samples.float()
        else:
            avg_depth = torch.tensor(0.0)
        
        return avg_depth.item(), self.total_steps.item(), self.total_samples.item()
    
    def reset_recursion_stats(self) -> None:
        """Reset recursion statistics counters."""
        self.total_steps.zero_()
        self.total_samples.zero_()
    
    def get_contextual_bias(self, context_embeddings: torch.Tensor) -> torch.Tensor:
        """
        Generate contextual bias from context embeddings.
        
        Args:
            context_embeddings: Context embeddings for bias generation
            
        Returns:
            torch.Tensor: Contextual bias matrix
        """
        if self.contextual_bias is not None and hasattr(self.contextual_bias, "compute_contextual_bias"):
            return self.contextual_bias.compute_contextual_bias(context_embeddings)
        return None
    
    def evolve(self, performance_metric: float) -> None:
        """
        Evolve the recursive transformer based on performance metric.
        
        Args:
            performance_metric: Performance metric to guide evolution
        """
        # Example evolution strategy: Adjust halting threshold based on performance
        # Higher performance means we can be more aggressive with halting
        if hasattr(self, 'halting_threshold'):
            # Adjust threshold within reasonable bounds (0.7 to 0.95)
            self.halting_threshold = min(0.95, max(0.7, 
                self.halting_threshold + 0.01 * (performance_metric - 0.5)))
        
        # Could also evolve other hyperparameters
        logger.debug(f"Evolved RecursiveTransformer: halting_threshold={self.halting_threshold:.4f}")


class ACT(nn.Module):
    """
    Adaptive Computation Time (ACT) mechanism as described in:
    "Adaptive Computation Time for Recurrent Neural Networks" (Graves, 2016),
    adapted for Transformers.
    
    ACT enables dynamic computation by allowing the model to decide how many 
    computational steps to take based on the input.
    """
    
    def __init__(
        self,
        hidden_size: int,
        threshold: float = 0.9,
        time_penalty: float = 0.01,
        max_steps: int = 20
    ):
        """
        Initialize the ACT mechanism.
        
        Args:
            hidden_size: Hidden dimension size
            threshold: Halting threshold
            time_penalty: Penalty for additional computation steps
            max_steps: Maximum number of steps allowed
        """
        super().__init__()
        self.hidden_size = hidden_size
        self.threshold = threshold
        self.time_penalty = time_penalty
        self.max_steps = max_steps
        
        # Halting predictor - produces halting probabilities
        self.halting_predictor = nn.Sequential(
            nn.Linear(hidden_size, hidden_size // 2),
            nn.LayerNorm(hidden_size // 2),
            nn.ReLU(),
            nn.Linear(hidden_size // 2, 1),
            nn.Sigmoid()
        )
        
        logger.info(f"Initialized ACT with threshold={threshold}, max_steps={max_steps}")
    
    def forward(
        self,
        hidden_states: torch.Tensor,
        fn: callable,
        fn_args: tuple = (),
        fn_kwargs: Optional[dict] = None
    ) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Forward pass for ACT.
        
        Args:
            hidden_states: Initial hidden states [seq_len, batch_size, hidden_size]
            fn: Function to apply repeatedly
            fn_args: Arguments for fn
            fn_kwargs: Keyword arguments for fn
            
        Returns:
            Tuple of (final_state, remainders, n_updates)
        """
        if fn_kwargs is None:
            fn_kwargs = {}
        
        # Get sequence length and batch size
        seq_len, batch_size, _ = hidden_states.size()
        device = hidden_states.device
        
        # Initialize tracking tensors
        halting_probability = torch.zeros(batch_size, device=device)
        remainders = torch.zeros(batch_size, device=device)
        n_updates = torch.zeros(batch_size, device=device)
        
        # Initialize accumulated outputs and halting state
        accumulated_state = torch.zeros_like(hidden_states)
        prev_state = hidden_states
        still_running = torch.ones(batch_size, dtype=torch.bool, device=device)
        
        # Ensure at least one update
        step = 0
        while still_running.any() and step < self.max_steps:
            # Apply function to get new state
            state = fn(prev_state, *fn_args, **fn_kwargs)
            
            # Calculate halting probability
            # Aggregate over sequence dimension
            state_mean = state.mean(dim=0)  # [batch_size, hidden_size]
            p = self.halting_predictor(state_mean).squeeze(-1)  # [batch_size]
            
            # Mask for samples that are still running
            p = p * still_running.float()
            
            # Update halting probability
            halting_probability += p
            
            # Mask for samples that finished at this step
            # These are samples where adding p pushes them over threshold
            halted = (halting_probability >= self.threshold) & still_running
            
            # Update remainders for samples that halted at this step
            remainders = remainders + halted.float() * (1 - remainders)
            
            # Add weighted state to accumulator
            update_weights = p.unsqueeze(0).unsqueeze(-1)  # [1, batch_size, 1]
            accumulated_state = accumulated_state + state * update_weights
            
            # Update count of samples that are still running
            still_running = still_running & ~halted
            
            # Update n_updates for running samples
            n_updates = n_updates + still_running.float()
            
            # Update previous state
            prev_state = state
            
            step += 1
        
        # If any samples are still running after max_steps, force halting
        if still_running.any():
            # Force halting probability to threshold for remaining samples
            remainders = remainders + still_running.float() * (self.threshold - halting_probability)
            
            # Add weighted final state to accumulator
            final_update_weights = (self.threshold - halting_probability).unsqueeze(0).unsqueeze(-1)
            final_update_weights = final_update_weights * still_running.float().unsqueeze(0).unsqueeze(-1)
            accumulated_state = accumulated_state + prev_state * final_update_weights
        
        # Normalize accumulated state
        accumulated_state = accumulated_state / self.threshold
        
        # Increment n_updates to account for final forced step
        n_updates = n_updates + 1.0
        
        return accumulated_state, remainders, n_updates
    
    def compute_ponder_loss(self, n_updates: torch.Tensor) -> torch.Tensor:
        """
        Compute ponder loss to penalize excessive computation.
        
        Args:
            n_updates: Number of updates tensor
            
        Returns:
            torch.Tensor: Ponder loss
        """
        # Ponder loss as described in the paper
        # Loss = ρ × (N-1+R) where N is n_updates and R is remainder
        return self.time_penalty * n_updates.mean()