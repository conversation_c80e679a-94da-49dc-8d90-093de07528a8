#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Self-Evolving Dynamic Attention Module for ULTRA Hyper-Dimensional Transformer.

This module implements the Self-Evolving Dynamic Attention (SEDA) mechanism described
in the ULTRA paper. SEDA extends traditional transformer attention by allowing the
attention parameters to evolve based on context, task demands, and performance feedback.

Key mathematical formulations:
1. Dynamic Attention: Attention(Q, K, V) = softmax((QK^T)/√d_k * M(ctx)) * V
2. Context-dependent mask: M_t = f_φ(M_{t-1}, C_t, P_t)
3. Temperature adaptation: τ_t = g_ψ(τ_{t-1}, U_t)
4. Parameter evolution: φ_{t+1} = φ_t - α∇_φL(φ_t, D_t)

References:
    - ULTRA Technical Paper v1.0, Section 5.1
"""

import logging
import math
from typing import Dict, List, Tuple, Optional, Union, Any, Set

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.distributions import Categorical

# Configure logging
logger = logging.getLogger(__name__)


class SelfEvolvingDynamicAttention(nn.Module):
    """
    Self-Evolving Dynamic Attention that adapts attention parameters based on context.
    
    This module extends traditional transformer attention by implementing mechanisms that
    allow attention patterns to evolve based on input context, task requirements, and
    performance feedback. It supports dynamic pruning and addition of attention heads,
    adaptive temperature scaling, and context-dependent attention masks.
    
    The key difference from standard attention is the introduction of evolving parameters
    and context-dependent masks:
    
    Attention(Q, K, V) = softmax((QK^T)/√d_k * M(ctx) / τ) * V
    
    where M(ctx) is a context-dependent mask and τ is an adaptive temperature parameter.
    """
    
    def __init__(
        self,
        d_model: int,
        nhead: int, 
        d_head: Optional[int] = None,
        dropout: float = 0.1,
        evolution_rate: float = 0.01,
        temperature_init: float = 1.0,
        utility_threshold: float = 0.1,
        utility_decay: float = 0.99,
        use_flash_attn: bool = False,
        use_evolving_heads: bool = True,
        max_head_growth: float = 0.5,
        layer_norm_eps: float = 1e-5,
        device: Optional[torch.device] = None,
        dtype: Optional[torch.dtype] = None,
    ):
        """
        Initialize the Self-Evolving Dynamic Attention.
        
        Args:
            d_model: Model dimension
            nhead: Number of attention heads
            d_head: Dimension of each attention head (if None, computed as d_model/nhead)
            dropout: Dropout probability
            evolution_rate: Rate of parameter evolution (learning rate for self-evolution)
            temperature_init: Initial attention temperature
            utility_threshold: Threshold for head pruning based on utility
            utility_decay: Decay factor for head utility EMA
            use_flash_attn: Whether to use FlashAttention for faster computation if available
            use_evolving_heads: Whether to allow attention heads to evolve
            max_head_growth: Maximum proportion of additional heads that can be added
            layer_norm_eps: Epsilon for layer normalization
            device: Device to use (CPU/GPU)
            dtype: Data type to use
        """
        factory_kwargs = {'device': device, 'dtype': dtype}
        super().__init__()
        
        self.d_model = d_model
        self.nhead = nhead
        self.d_head = d_head if d_head is not None else d_model // nhead
        self.dropout = dropout
        self.evolution_rate = evolution_rate
        self.utility_threshold = utility_threshold
        self.utility_decay = utility_decay
        self.use_flash_attn = use_flash_attn
        self.use_evolving_heads = use_evolving_heads
        self.max_head_growth = max_head_growth
        self.layer_norm_eps = layer_norm_eps
        
        # Set up scaling factor for attention
        self.scaling = self.d_head ** -0.5
        
        # Maximum number of heads (includes potential growth)
        self.max_heads = nhead + int(nhead * max_head_growth)
        
        # Check if d_model is divisible by nhead
        if self.d_head * nhead != d_model:
            raise ValueError(
                f"d_model ({d_model}) must be divisible by nhead ({nhead})"
            )
        
        # Calculate initial and maximum QKV projection dimensions
        init_qkv_dim = nhead * self.d_head
        max_qkv_dim = self.max_heads * self.d_head
        
        # Initialize query, key, value projections
        # We initialize with the initial number of heads but allocate space for max heads
        self.q_proj = nn.Linear(d_model, max_qkv_dim, **factory_kwargs)
        self.k_proj = nn.Linear(d_model, max_qkv_dim, **factory_kwargs)
        self.v_proj = nn.Linear(d_model, max_qkv_dim, **factory_kwargs)
        self.out_proj = nn.Linear(max_qkv_dim, d_model, **factory_kwargs)
        
        # Initialize only the initial portion of the weight matrices
        with torch.no_grad():
            # Zero-initialize the space reserved for new heads
            self.q_proj.weight.data[:, init_qkv_dim:] = 0.0
            self.k_proj.weight.data[:, init_qkv_dim:] = 0.0
            self.v_proj.weight.data[:, init_qkv_dim:] = 0.0
            self.out_proj.weight.data[init_qkv_dim:, :] = 0.0
            
            if self.q_proj.bias is not None:
                self.q_proj.bias.data[init_qkv_dim:] = 0.0
            if self.k_proj.bias is not None:
                self.k_proj.bias.data[init_qkv_dim:] = 0.0
            if self.v_proj.bias is not None:
                self.v_proj.bias.data[init_qkv_dim:] = 0.0
            if self.out_proj.bias is not None:
                self.out_proj.bias.data[init_qkv_dim:] = 0.0
        
        # Initialize context-dependent mask generator
        self.context_projection = nn.Sequential(
            nn.Linear(d_model, d_model, **factory_kwargs),
            nn.Tanh(),
            nn.Linear(d_model, self.max_heads, **factory_kwargs)
        )
        
        # Layer norms for normalization before attention
        self.q_norm = nn.LayerNorm(d_model, eps=layer_norm_eps, **factory_kwargs)
        self.k_norm = nn.LayerNorm(d_model, eps=layer_norm_eps, **factory_kwargs)
        self.v_norm = nn.LayerNorm(d_model, eps=layer_norm_eps, **factory_kwargs)
        
        # Initialize adaptive temperature parameter
        self.temperature = nn.Parameter(
            torch.ones(self.max_heads, **factory_kwargs) * temperature_init
        )
        
        # Uncertainty estimator for temperature adaptation
        self.uncertainty_estimator = nn.Sequential(
            nn.Linear(d_model, d_model // 2, **factory_kwargs),
            nn.ReLU(),
            nn.Linear(d_model // 2, self.max_heads, **factory_kwargs),
            nn.Sigmoid()
        )
        
        # Context-dependent attention modulation
        self.contextual_bias_gen = nn.Sequential(
            nn.Linear(d_model, d_model, **factory_kwargs),
            nn.Tanh(),
            nn.Linear(d_model, self.max_heads, **factory_kwargs)
        )
        
        # Initialize additional parameters for context-dependent attention
        self.key_bias = nn.Parameter(torch.zeros(self.max_heads, self.d_head, **factory_kwargs))
        self.query_bias = nn.Parameter(torch.zeros(self.max_heads, self.d_head, **factory_kwargs))
        self.value_bias = nn.Parameter(torch.zeros(self.max_heads, self.d_head, **factory_kwargs))
        
        # Dimension-wise scaling vectors
        self.key_scaling = nn.Parameter(torch.ones(self.max_heads, self.d_head, **factory_kwargs))
        self.query_scaling = nn.Parameter(torch.ones(self.max_heads, self.d_head, **factory_kwargs))
        
        # Head embeddings for head differentiation
        self.head_embeddings = nn.Parameter(
            torch.randn(self.max_heads, self.d_head, **factory_kwargs) * 0.02
        )
        
        # Register buffers for tracking head utility and status
        self.register_buffer('head_utility', torch.ones(self.max_heads, **factory_kwargs))
        self.register_buffer('head_utility_ema', torch.ones(self.max_heads, **factory_kwargs))
        self.register_buffer('head_masks', torch.zeros(self.max_heads, **factory_kwargs))
        self.register_buffer('active_heads', torch.tensor(nhead, dtype=torch.long, **factory_kwargs))
        
        # Initialize head masks (1 for active, 0 for inactive)
        self.head_masks[:nhead] = 1.0
        
        # Performance tracking
        self.register_buffer('performance_history', torch.zeros(100, **factory_kwargs))
        self.register_buffer('performance_index', torch.tensor(0, dtype=torch.long, **factory_kwargs))
        
        # Initialize attention tracking
        self.register_buffer('attention_entropy', torch.zeros(self.max_heads, **factory_kwargs))
        
        # Check for FlashAttention
        self._has_flash_attn = False
        if use_flash_attn:
            try:
                from flash_attn import flash_attn_func
                self._has_flash_attn = True
                logger.info("FlashAttention is available and will be used")
            except ImportError:
                logger.warning("FlashAttention requested but not available, falling back to standard attention")
        
        # Reset parameters with proper initialization
        self._reset_parameters()
        
        logger.info(
            f"Self-Evolving Dynamic Attention initialized with {nhead} heads of dimension {self.d_head}, "
            f"evolution rate: {evolution_rate}"
        )
    
    def _reset_parameters(self):
        """Reset parameters using Xavier/Glorot initialization."""
        # Initialize all Linear layers
        for module in [self.q_proj, self.k_proj, self.v_proj, self.out_proj]:
            # Use Xavier uniform initialization with gain=1/sqrt(2)
            # This helps with learning stability for evolving attention
            nn.init.xavier_uniform_(module.weight, gain=1/math.sqrt(2))
            if module.bias is not None:
                nn.init.zeros_(module.bias)
        
        # Initialize context projection with small weights
        for name, param in self.context_projection.named_parameters():
            if 'weight' in name:
                nn.init.normal_(param, mean=0.0, std=0.02)
            else:
                nn.init.zeros_(param)
        
        # Initialize bias generators with small weights
        for name, param in self.contextual_bias_gen.named_parameters():
            if 'weight' in name:
                nn.init.normal_(param, mean=0.0, std=0.02)
            else:
                nn.init.zeros_(param)
        
        # Initialize uncertainty estimator
        for name, param in self.uncertainty_estimator.named_parameters():
            if 'weight' in name:
                nn.init.normal_(param, mean=0.0, std=0.02)
            else:
                nn.init.zeros_(param)
        
        # Initialize LayerNorm
        for module in [self.q_norm, self.k_norm, self.v_norm]:
            nn.init.ones_(module.weight)
            nn.init.zeros_(module.bias)
        
        # Initialize QKV biases and scaling with identity-like values
        nn.init.zeros_(self.key_bias)
        nn.init.zeros_(self.query_bias)
        nn.init.zeros_(self.value_bias)
        nn.init.ones_(self.key_scaling)
        nn.init.ones_(self.query_scaling)
        
        # Initialize temperature with reasonable default
        nn.init.constant_(self.temperature, 1.0)
        
        # Initialize head embeddings with small random values
        nn.init.normal_(self.head_embeddings, mean=0.0, std=0.02)
    
    def _split_heads(
        self, 
        x: torch.Tensor, 
        n_active: int
    ) -> torch.Tensor:
        """
        Split tensor into attention heads.
        
        Args:
            x: Input tensor [batch_size, seq_len, n_active * d_head]
            n_active: Number of active heads
            
        Returns:
            torch.Tensor: Reshaped tensor [batch_size, n_active, seq_len, d_head]
        """
        batch_size, seq_len = x.shape[:2]
        x = x.view(batch_size, seq_len, n_active, self.d_head)
        return x.transpose(1, 2)
    
    def _merge_heads(
        self, 
        x: torch.Tensor, 
        n_active: int
    ) -> torch.Tensor:
        """
        Merge attention heads back to original shape.
        
        Args:
            x: Input tensor [batch_size, n_active, seq_len, d_head]
            n_active: Number of active heads
            
        Returns:
            torch.Tensor: Reshaped tensor [batch_size, seq_len, n_active * d_head]
        """
        batch_size, _, seq_len = x.shape[:3]
        x = x.transpose(1, 2)
        return x.contiguous().view(batch_size, seq_len, n_active * self.d_head)
    
    def _compute_qkv(
        self, 
        query: torch.Tensor, 
        key: torch.Tensor, 
        value: torch.Tensor,
        context_embedding: Optional[torch.Tensor] = None
    ) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Compute query, key, and value with dynamic parameters.
        
        Args:
            query: Query tensor [batch_size, seq_len, d_model]
            key: Key tensor [batch_size, seq_len, d_model]
            value: Value tensor [batch_size, seq_len, d_model]
            context_embedding: Context embedding [batch_size, d_model]
            
        Returns:
            Tuple[torch.Tensor, torch.Tensor, torch.Tensor]: q, k, v tensors
        """
        # Get number of active heads
        n_active = int(torch.sum(self.head_masks).item())
        
        # Apply layer normalization
        query_normed = self.q_norm(query)
        key_normed = self.k_norm(key)
        value_normed = self.v_norm(value)
        
        # Project normalized inputs to q, k, v
        q_projected = self.q_proj(query_normed)
        k_projected = self.k_proj(key_normed)
        v_projected = self.v_proj(value_normed)
        
        # Get only the active portions of the projections
        q_active = q_projected[:, :, :n_active * self.d_head]
        k_active = k_projected[:, :, :n_active * self.d_head]
        v_active = v_projected[:, :, :n_active * self.d_head]
        
        # Split into heads
        q = self._split_heads(q_active, n_active)  # [batch, n_active, seq_len, d_head]
        k = self._split_heads(k_active, n_active)  # [batch, n_active, seq_len, d_head]
        v = self._split_heads(v_active, n_active)  # [batch, n_active, seq_len, d_head]
        
        # Apply context-dependent biases and scaling if context embedding is provided
        if context_embedding is not None:
            # Extract active portions of the parameters
            key_bias_active = self.key_bias[:n_active]
            query_bias_active = self.query_bias[:n_active]
            value_bias_active = self.value_bias[:n_active]
            key_scaling_active = self.key_scaling[:n_active]
            query_scaling_active = self.query_scaling[:n_active]
            
            # Apply biases and scaling
            q = q + query_bias_active.unsqueeze(0).unsqueeze(2)
            k = k + key_bias_active.unsqueeze(0).unsqueeze(2)
            v = v + value_bias_active.unsqueeze(0).unsqueeze(2)
            
            q = q * query_scaling_active.unsqueeze(0).unsqueeze(2)
            k = k * key_scaling_active.unsqueeze(0).unsqueeze(2)
        
        # Scale query by d_head
        q = q * self.scaling
        
        return q, k, v, n_active
    
    def _apply_attention(
        self,
        q: torch.Tensor,
        k: torch.Tensor,
        v: torch.Tensor,
        mask: Optional[torch.Tensor] = None,
        temperature: Optional[torch.Tensor] = None,
        contextual_bias: Optional[torch.Tensor] = None,
        n_active: int = None
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Apply scaled dot-product attention with dynamic parameters.
        
        Args:
            q: Query tensor [batch_size, n_active, seq_len_q, d_head]
            k: Key tensor [batch_size, n_active, seq_len_k, d_head]
            v: Value tensor [batch_size, n_active, seq_len_v, d_head]
            mask: Attention mask [batch_size, 1, seq_len_q, seq_len_k]
            temperature: Temperature parameter [n_active]
            contextual_bias: Contextual bias [batch_size, n_active, seq_len_q, seq_len_k]
            n_active: Number of active heads
            
        Returns:
            Tuple[torch.Tensor, torch.Tensor]: Output tensor and attention weights
        """
        # Get batch size and sequence lengths
        batch_size, n_heads = q.shape[:2]
        seq_len_q, seq_len_k, seq_len_v = q.size(2), k.size(2), v.size(2)
        
        # Use FlashAttention if available and requested
        if self._has_flash_attn and self.use_flash_attn and mask is None:
            try:
                from flash_attn import flash_attn_func
                # FlashAttention expects [batch, seq_len, n_heads, head_dim]
                q_flash = q.transpose(1, 2)  # [batch, seq_len_q, n_heads, head_dim]
                k_flash = k.transpose(1, 2)  # [batch, seq_len_k, n_heads, head_dim]
                v_flash = v.transpose(1, 2)  # [batch, seq_len_v, n_heads, head_dim]
                
                # Apply FlashAttention
                output = flash_attn_func(
                    q_flash, k_flash, v_flash,
                    dropout_p=self.dropout if self.training else 0.0,
                    softmax_scale=1.0 if temperature is None else 1.0/temperature.mean().item()
                )
                
                # Reshape output to [batch, n_heads, seq_len, head_dim]
                output = output.transpose(1, 2)
                
                # Since FlashAttention doesn't return attention weights, we compute an approximation
                # for utility tracking when training
                if self.training:
                    with torch.no_grad():
                        # Sample a small batch to compute attention weights for tracking
                        sample_size = min(2, batch_size)
                        attn_weights = torch.matmul(q[:sample_size], k[:sample_size].transpose(-2, -1))
                        
                        # Apply temperature if provided
                        if temperature is not None:
                            attn_weights = attn_weights / temperature.view(1, -1, 1, 1)
                        
                        # Apply contextual bias if provided
                        if contextual_bias is not None:
                            attn_weights = attn_weights + contextual_bias[:sample_size]
                        
                        # Apply attention mask if provided
                        if mask is not None:
                            attn_weights = attn_weights.masked_fill(mask[:sample_size] == 0, -1e9)
                        
                        # Apply softmax
                        attn_weights = F.softmax(attn_weights, dim=-1)
                else:
                    # No attention weights when not training
                    attn_weights = None
                
                return output, attn_weights
            
            except (ImportError, Exception) as e:
                # Fall back to standard attention if FlashAttention fails
                logger.warning(f"FlashAttention failed, falling back to standard attention: {e}")
        
        # Standard scaled dot-product attention
        # attn_weights: [batch, n_heads, seq_len_q, seq_len_k]
        attn_weights = torch.matmul(q, k.transpose(-2, -1))
        
        # Apply temperature if provided
        if temperature is not None:
            attn_weights = attn_weights / temperature.view(1, -1, 1, 1)
        
        # Apply contextual bias if provided
        if contextual_bias is not None:
            attn_weights = attn_weights + contextual_bias
        
        # Apply attention mask if provided
        if mask is not None:
            # Expand mask to handle multi-head attention
            if mask.dim() == 3:  # [batch_size, seq_len_q, seq_len_k]
                mask = mask.unsqueeze(1)  # [batch_size, 1, seq_len_q, seq_len_k]
                
            # Apply mask (set masked positions to large negative value)
            attn_weights = attn_weights.masked_fill(mask == 0, -1e9)
        
        # Apply softmax to get attention probabilities
        attn_probs = F.softmax(attn_weights, dim=-1)
        
        # Store attention entropy for utility computation
        if self.training and n_active is not None:
            with torch.no_grad():
                # Calculate entropy of attention distributions for each head
                # We use a sample to save computation
                sample_size = min(2, batch_size)
                
                # -sum(p * log(p))
                entropy = -torch.sum(
                    attn_probs[:sample_size] * torch.log(attn_probs[:sample_size] + 1e-10),
                    dim=-1
                )
                
                # Average entropy across batch and sequence length
                entropy = entropy.mean(dim=[0, 2])
                
                # Store in buffer (first n_active heads)
                self.attention_entropy[:n_active] = entropy
        
        # Apply dropout during training
        if self.training and self.dropout > 0.0:
            attn_probs = F.dropout(attn_probs, p=self.dropout, training=True)
        
        # Apply attention to values: [batch, n_heads, seq_len_q, seq_len_v]
        output = torch.matmul(attn_probs, v)
        
        return output, attn_probs
    
    def generate_contextual_bias(
        self, 
        context_embedding: torch.Tensor,
        n_active: int,
        seq_len_q: int,
        seq_len_k: int
    ) -> torch.Tensor:
        """
        Generate contextual bias based on the context embedding.
        
        This implements the M(ctx) function from the mathematical formulation.
        
        Args:
            context_embedding: Context embedding [batch_size, d_model]
            n_active: Number of active heads
            seq_len_q: Query sequence length
            seq_len_k: Key sequence length
            
        Returns:
            torch.Tensor: Contextual bias for attention [batch_size, n_active, seq_len_q, seq_len_k]
        """
        batch_size = context_embedding.size(0)
        
        # Generate context-dependent bias factors for each head
        # [batch_size, max_heads] -> [batch_size, n_active]
        bias_factors = self.contextual_bias_gen(context_embedding)[:, :n_active]
        
        # Reshape for broadcasting: [batch_size, n_active, 1, 1]
        bias_factors = bias_factors.unsqueeze(-1).unsqueeze(-1)
        
        # Create a base attention bias pattern
        # This could be extended to support more complex patterns
        
        # Option 1: Global bias (same for all positions)
        # bias_pattern = bias_factors.expand(batch_size, n_active, seq_len_q, seq_len_k)
        
        # Option 2: Distance-based bias (favor closer positions)
        positions_q = torch.arange(seq_len_q, device=context_embedding.device).unsqueeze(1)
        positions_k = torch.arange(seq_len_k, device=context_embedding.device).unsqueeze(0)
        distance = torch.abs(positions_q - positions_k).float()  # [seq_len_q, seq_len_k]
        
        # Normalize distances to [0, 1] range
        if seq_len_q > 1 or seq_len_k > 1:
            max_distance = max(seq_len_q, seq_len_k) - 1
            if max_distance > 0:
                distance = distance / max_distance
        
        # Convert distance to similarity (1 = identical, 0 = maximally distant)
        similarity = 1.0 - distance
        
        # Create attention bias from bias factors and similarity
        # This creates a bias that favors attending to nearby positions,
        # with the strength determined by the context-dependent bias factors
        contextual_bias = bias_factors * similarity.unsqueeze(0).unsqueeze(0)
        
        return contextual_bias
    
    def update_head_utility(self):
        """
        Update head utility based on attention entropy.
        
        This tracks how much each attention head contributes to the overall attention,
        which is used for pruning and adding heads.
        """
        if not self.training:
            return
        
        # Get number of active heads
        n_active = int(torch.sum(self.head_masks).item())
        
        # Calculate utility based on entropy (lower entropy = higher utility)
        # We already computed entropy in _apply_attention
        entropy = self.attention_entropy[:n_active]
        
        # Normalize to [0, 1] range (lower entropy = higher utility)
        # Maximum entropy for a distribution over n elements is log(n)
        batch_size = self.head_utility.size(0)
        seq_len = 1  # Just a placeholder, will be determined dynamically in forward pass
        
        # Since we can't know the exact sequence length, use a reasonable default
        # for maximum entropy estimation (e.g., max of 1024 tokens)
        max_entropy = math.log(1024)
        normalized_utility = 1.0 - (entropy / max_entropy).clamp(0, 1)
        
        # Update utility with exponential moving average
        # Only update for active heads
        self.head_utility_ema[:n_active] = (
            self.utility_decay * self.head_utility_ema[:n_active] + 
            (1 - self.utility_decay) * normalized_utility
        )
    
    def forward(
        self,
        query: torch.Tensor,
        key: torch.Tensor,
        value: torch.Tensor,
        mask: Optional[torch.Tensor] = None,
        context_embedding: Optional[torch.Tensor] = None,
        return_attention: bool = False
    ) -> Union[torch.Tensor, Tuple[torch.Tensor, torch.Tensor]]:
        """
        Forward pass for Self-Evolving Dynamic Attention.
        
        This implements the full attention mechanism with context-dependent parameters:
        Attention(Q, K, V) = softmax((QK^T)/√d_k * M(ctx) / τ) * V
        
        Args:
            query: Query tensor [batch_size, seq_len, d_model]
            key: Key tensor [batch_size, seq_len, d_model]
            value: Value tensor [batch_size, seq_len, d_model]
            mask: Attention mask [batch_size, seq_len, seq_len] or [batch_size, 1, seq_len, seq_len]
            context_embedding: Context embedding for dynamic attention [batch_size, d_model]
            return_attention: Whether to return attention weights
            
        Returns:
            torch.Tensor or Tuple[torch.Tensor, torch.Tensor]: 
                Output tensor [batch_size, seq_len, d_model] and optionally attention weights
        """
        batch_size, seq_len_q = query.size(0), query.size(1)
        seq_len_k, seq_len_v = key.size(1), value.size(1)
        
        # Generate dynamic parameters based on context
        if context_embedding is not None:
            # Compute context once for the entire sequence
            if context_embedding.dim() == 3:  # [batch_size, seq_len, d_model]
                # Average over sequence dimension
                context_embedding = context_embedding.mean(dim=1)
            
            # Should be [batch_size, d_model]
            if context_embedding.dim() != 2:
                raise ValueError(
                    f"Context embedding must have shape [batch_size, d_model] or "
                    f"[batch_size, seq_len, d_model], got {context_embedding.shape}"
                )
            
            # Adapt temperature based on uncertainty
            # [batch_size, max_heads] -> [batch_size, n_active]
            n_active = int(torch.sum(self.head_masks).item())
            uncertainty = self.uncertainty_estimator(context_embedding)[:, :n_active]
            
            # Apply uncertainty modulation to temperature
            # [max_heads] -> [n_active]
            base_temperature = self.temperature[:n_active]
            
            # Reshape uncertainty for broadcasting: [batch_size, n_active]
            # and take mean across batch dimension
            uncertainty_factor = (1.0 + 0.5 * uncertainty).mean(dim=0)
            
            # Apply to temperature: [n_active]
            temperature = base_temperature * uncertainty_factor
            
            # Generate contextual bias [batch_size, n_active, seq_len_q, seq_len_k]
            contextual_bias = self.generate_contextual_bias(
                context_embedding, n_active, seq_len_q, seq_len_k
            )
        else:
            # Use base temperature without adaptation
            n_active = int(torch.sum(self.head_masks).item())
            temperature = self.temperature[:n_active]
            contextual_bias = None
        
        # Compute q, k, v tensors
        q, k, v, n_active = self._compute_qkv(query, key, value, context_embedding)
        
        # Apply attention
        attn_output, attn_weights = self._apply_attention(
            q, k, v, mask, temperature, contextual_bias, n_active
        )
        
        # Merge heads back to original shape
        output = self._merge_heads(attn_output, n_active)
        
        # Final projection
        output = self.out_proj(output)
        
        # Update head utility if in training mode
        if self.training:
            self.update_head_utility()
        
        if return_attention:
            return output, attn_weights
        else:
            return output
    
    def evolve_attention_heads(
        self, 
        utility_threshold: Optional[float] = None,
        performance_metric: Optional[float] = None
    ) -> bool:
        """
        Evolve attention heads based on their utility.
        
        This method prunes low-utility heads and optionally adds new ones.
        
        Args:
            utility_threshold: Threshold for head pruning
            performance_metric: Optional performance metric for evolution guidance
            
        Returns:
            bool: True if any changes were made
        """
        if not self.use_evolving_heads:
            return False
        
        # Use instance threshold if not specified
        if utility_threshold is None:
            utility_threshold = self.utility_threshold
        
        # Get number of active heads
        n_active = int(torch.sum(self.head_masks).item())
        
        # Get current head utility
        utility = self.head_utility_ema[:n_active].detach()
        
        # Record performance if provided
        if performance_metric is not None:
            idx = self.performance_index.item() % self.performance_history.size(0)
            self.performance_history[idx] = performance_metric
            self.performance_index += 1
        
        # Identify heads to prune (low utility)
        heads_to_prune = (utility < utility_threshold).nonzero().squeeze(dim=-1)
        
        # Convert to list (handle empty tensor and single-item tensor cases)
        if heads_to_prune.dim() == 0 and heads_to_prune.numel() == 1:
            heads_to_prune = [heads_to_prune.item()]
        elif heads_to_prune.numel() > 0:
            heads_to_prune = heads_to_prune.tolist()
        else:
            heads_to_prune = []
        
        # Don't prune all heads
        if len(heads_to_prune) == n_active:
            # Keep the highest utility head
            _, keep_idx = torch.max(utility, dim=0)
            keep_idx = keep_idx.item()
            heads_to_prune = [i for i in range(n_active) if i != keep_idx]
        
        # Ensure we always have at least one head
        if n_active - len(heads_to_prune) < 1:
            # Sort by utility and keep the top one
            _, indices = torch.sort(utility, descending=True)
            heads_to_prune = [i for i in range(n_active) if i not in indices[:1].tolist()]
        
        # Update head masks (0 = pruned, 1 = active)
        made_changes = False
        if len(heads_to_prune) > 0:
            for idx in heads_to_prune:
                # Only prune if it's currently active
                if self.head_masks[idx] > 0:
                    self.head_masks[idx] = 0
                    made_changes = True
            
            if made_changes:
                # Rearrange active heads to maintain contiguity
                self._rearrange_active_heads()
                logger.info(f"Pruned {len(heads_to_prune)} attention heads")
        
        # Check if we should add heads based on performance trends
        if self.performance_index > 10 and n_active < self.max_heads:
            should_add = self._check_performance_for_head_addition()
            if should_add:
                made_changes = self.add_attention_head() or made_changes
        
        return made_changes
    
    def _check_performance_for_head_addition(self) -> bool:
        """
        Check if performance trends suggest adding a new head.
        
        Returns:
            bool: True if a new head should be added
        """
        # Get the available performance history
        history_size = min(self.performance_index.item(), self.performance_history.size(0))
        if history_size < 10:
            return False
        
        # Get recent and older performance metrics
        recent_window = 5
        recent = self.performance_history[max(0, history_size - recent_window):history_size]
        older = self.performance_history[max(0, history_size - 2*recent_window):history_size - recent_window]
        
        # Calculate means
        recent_mean = recent.mean().item()
        older_mean = older.mean().item()
        
        # Check if performance has plateaued or declined
        # Lower values are better for loss-based metrics, higher for accuracy-based metrics
        # We assume lower is better (loss-based) as a default
        performance_stalled = recent_mean >= older_mean
        
        # Only add head if performance has stalled and we're not already at max heads
        n_active = int(torch.sum(self.head_masks).item())
        return performance_stalled and n_active < self.max_heads
    
    def _rearrange_active_heads(self):
        """
        Rearrange active heads to maintain contiguity.
        
        This is important for efficient computation, as it ensures that all active
        heads are at the beginning of the parameter tensors.
        """
        # Get current head status
        head_status = self.head_masks.bool()
        n_active = head_status.sum().item()
        
        # If all heads are already active and contiguous, no need to rearrange
        if head_status[:n_active].all() and (n_active == len(head_status) or not head_status[n_active:].any()):
            return
        
        # Find active and inactive heads
        active_indices = torch.where(head_status)[0]
        inactive_indices = torch.where(~head_status)[0]
        
        # Create a mapping from old to new positions
        new_positions = torch.zeros_like(self.head_masks, dtype=torch.long)
        new_positions[active_indices] = torch.arange(len(active_indices), device=new_positions.device)
        new_positions[inactive_indices] = torch.arange(
            len(active_indices), len(self.head_masks), device=new_positions.device
        )
        
        # Rearrange head-related parameters
        with torch.no_grad():
            # Update head masks
            new_masks = torch.zeros_like(self.head_masks)
            new_masks[:n_active] = 1.0
            self.head_masks.copy_(new_masks)
            
            # Rearrange head-specific parameters
            self._rearrange_parameter(self.temperature, new_positions)
            self._rearrange_parameter(self.head_utility, new_positions)
            self._rearrange_parameter(self.head_utility_ema, new_positions)
            self._rearrange_parameter(self.key_bias, new_positions)
            self._rearrange_parameter(self.query_bias, new_positions)
            self._rearrange_parameter(self.value_bias, new_positions)
            self._rearrange_parameter(self.key_scaling, new_positions)
            self._rearrange_parameter(self.query_scaling, new_positions)
            self._rearrange_parameter(self.head_embeddings, new_positions)
            self._rearrange_parameter(self.attention_entropy, new_positions)
            
            # Rearrange projection weights
            self._rearrange_projection_weights(self.q_proj, new_positions, output_dim=False)
            self._rearrange_projection_weights(self.k_proj, new_positions, output_dim=False)
            self._rearrange_projection_weights(self.v_proj, new_positions, output_dim=False)
            self._rearrange_projection_weights(self.out_proj, new_positions, output_dim=True)
            
            if hasattr(self.q_proj, 'bias') and self.q_proj.bias is not None:
                self._rearrange_projection_bias(self.q_proj, new_positions)
                self._rearrange_projection_bias(self.k_proj, new_positions)
                self._rearrange_projection_bias(self.v_proj, new_positions)
    
    def _rearrange_parameter(self, param, new_positions):
        """
        Rearrange a head-specific parameter based on new positions.
        
        Args:
            param: Parameter to rearrange
            new_positions: Mapping from old to new positions
        """
        # Skip if parameter doesn't exist
        if param is None:
            return
        
        # Create a new tensor with rearranged values
        shape = param.shape
        new_param = torch.zeros_like(param)
        
        # Handle parameters with different shapes
        if param.dim() == 1:  # [max_heads]
            new_param = param[new_positions]
        elif param.dim() == 2:  # [max_heads, d_head]
            new_param = param[new_positions]
        else:
            # Unsupported parameter shape
            logger.warning(f"Unsupported parameter shape for rearrangement: {shape}")
            return
        
        # Copy the rearranged values back to the parameter
        param.copy_(new_param)
    
    def _rearrange_projection_weights(self, layer, new_positions, output_dim=False):
        """
        Rearrange projection layer weights based on new head positions.
        
        Args:
            layer: Linear layer to rearrange
            new_positions: Mapping from old to new positions
            output_dim: Whether the heads are in the output dimension
        """
        weight = layer.weight
        if output_dim:
            # For output projection, heads are in the rows
            # [d_model, max_heads * d_head]
            new_weight = torch.zeros_like(weight)
            for old_pos, new_pos in enumerate(new_positions):
                old_start = old_pos * self.d_head
                old_end = (old_pos + 1) * self.d_head
                new_start = new_pos * self.d_head
                new_end = (new_pos + 1) * self.d_head
                new_weight[:, new_start:new_end] = weight[:, old_start:old_end]
            weight.copy_(new_weight)
        else:
            # For QKV projections, heads are in the columns
            # [max_heads * d_head, d_model]
            new_weight = torch.zeros_like(weight)
            for old_pos, new_pos in enumerate(new_positions):
                old_start = old_pos * self.d_head
                old_end = (old_pos + 1) * self.d_head
                new_start = new_pos * self.d_head
                new_end = (new_pos + 1) * self.d_head
                new_weight[new_start:new_end] = weight[old_start:old_end]
            weight.copy_(new_weight)
    
    def _rearrange_projection_bias(self, layer, new_positions):
        """
        Rearrange projection layer bias based on new head positions.
        
        Args:
            layer: Linear layer to rearrange
            new_positions: Mapping from old to new positions
        """
        bias = layer.bias
        if bias is None:
            return
        
        # [max_heads * d_head]
        new_bias = torch.zeros_like(bias)
        for old_pos, new_pos in enumerate(new_positions):
            old_start = old_pos * self.d_head
            old_end = (old_pos + 1) * self.d_head
            new_start = new_pos * self.d_head
            new_end = (new_pos + 1) * self.d_head
            new_bias[new_start:new_end] = bias[old_start:old_end]
        bias.copy_(new_bias)
    
    def add_attention_head(self, prototype_idx=None) -> bool:
        """
        Add a new attention head, optionally based on a prototype.
        
        Args:
            prototype_idx: Index of head to use as prototype
            
        Returns:
            bool: True if head was added
        """
        if not self.use_evolving_heads:
            return False
        
        # Get number of active heads
        n_active = int(torch.sum(self.head_masks).item())
        
        # If already at maximum, we cannot add more
        if n_active >= self.max_heads:
            return False
        
        # Activate the next head
        new_head_idx = n_active
        
        # If prototype is provided, initialize from it
        if prototype_idx is not None and prototype_idx < n_active:
            # Copy parameters from prototype
            with torch.no_grad():
                # Add small random noise to create variation
                noise_scale = 0.1
                
                # Copy head-specific parameters with noise
                self.temperature[new_head_idx] = (
                    self.temperature[prototype_idx] + 
                    torch.randn_like(self.temperature[prototype_idx]) * noise_scale
                )
                
                self.key_bias[new_head_idx] = (
                    self.key_bias[prototype_idx] + 
                    torch.randn_like(self.key_bias[prototype_idx]) * noise_scale
                )
                
                self.query_bias[new_head_idx] = (
                    self.query_bias[prototype_idx] + 
                    torch.randn_like(self.query_bias[prototype_idx]) * noise_scale
                )
                
                self.value_bias[new_head_idx] = (
                    self.value_bias[prototype_idx] + 
                    torch.randn_like(self.value_bias[prototype_idx]) * noise_scale
                )
                
                self.key_scaling[new_head_idx] = (
                    self.key_scaling[prototype_idx] + 
                    torch.randn_like(self.key_scaling[prototype_idx]) * noise_scale
                )
                
                self.query_scaling[new_head_idx] = (
                    self.query_scaling[prototype_idx] + 
                    torch.randn_like(self.query_scaling[prototype_idx]) * noise_scale
                )
                
                # Copy projection weights with noise - this is already handled
                # since the inactive weights are still being trained
        else:
            # Initialize the new head with default values
            with torch.no_grad():
                self.temperature[new_head_idx] = 1.0
                nn.init.zeros_(self.key_bias[new_head_idx])
                nn.init.zeros_(self.query_bias[new_head_idx])
                nn.init.zeros_(self.value_bias[new_head_idx])
                nn.init.ones_(self.key_scaling[new_head_idx])
                nn.init.ones_(self.query_scaling[new_head_idx])
        
        # Activate the head
        self.head_masks[new_head_idx] = 1.0
        
        # Reset utility for new head
        self.head_utility_ema[new_head_idx] = 0.5  # Neutral initial utility
        
        logger.info(f"Added new attention head at index {new_head_idx}")
        return True
    
    def update_evolution(self, performance_metric: float, learning_rate: Optional[float] = None):
        """
        Update self-evolution based on performance metric.
        
        This implements the parameter evolution from the paper:
        φ_{t+1} = φ_t - α∇_φL(φ_t, D_t)
        
        Args:
            performance_metric: Performance metric to guide evolution
            learning_rate: Learning rate for evolution, if None use self.evolution_rate
        """
        if not self.training or self.evolution_rate <= 0:
            return
        
        # Use instance rate if not specified
        if learning_rate is None:
            learning_rate = self.evolution_rate
        
        # Record performance for head evolution
        idx = self.performance_index.item() % self.performance_history.size(0)
        self.performance_history[idx] = performance_metric
        self.performance_index += 1
        
        # Get number of active heads
        n_active = int(torch.sum(self.head_masks).item())
        
        # Compute gradient direction based on performance
        # For loss metrics, lower is better, so we want negative gradient
        # For accuracy metrics, higher is better, so we want positive gradient
        # We assume performance_metric is a loss value
        
        # Check if we have enough history to compute trend
        if self.performance_index > 5:
            # Get recent performance metrics
            history_size = min(self.performance_index.item(), self.performance_history.size(0))
            recent = self.performance_history[max(0, history_size - 5):history_size]
            
            # Compute trend (positive = getting worse, negative = getting better)
            if recent.size(0) > 1:
                # Fit a line to recent performance
                x = torch.arange(recent.size(0), device=recent.device).float()
                x_mean = x.mean()
                y_mean = recent.mean()
                
                # Compute slope of the trend line
                numer = ((x - x_mean) * (recent - y_mean)).sum()
                denom = ((x - x_mean) ** 2).sum()
                slope = numer / (denom + 1e-8)
                
                # If performance is getting worse, apply changes
                if slope > 0:
                    with torch.no_grad():
                        # Update temperature based on performance
                        # If performance is getting worse, randomize a bit to explore
                        self.temperature[:n_active] += (
                            torch.randn_like(self.temperature[:n_active]) * 
                            learning_rate * 0.1
                        )
                        
                        # Ensure temperature stays positive and reasonable
                        self.temperature[:n_active] = torch.clamp(
                            self.temperature[:n_active], min=0.1, max=10.0
                        )
        
        # Decide whether to evolve attention heads
        if self.use_evolving_heads and self.performance_index % 10 == 0:
            self.evolve_attention_heads(performance_metric=performance_metric)
    
    def get_attention_patterns(self) -> Optional[torch.Tensor]:
        """
        Get the last attention patterns for visualization or analysis.
        
        Returns:
            torch.Tensor or None: Last attention patterns or None if not available
        """
        # This information is not stored by default to save memory
        # Implement this if needed for specific applications
        return None
    
    def get_head_utility(self) -> torch.Tensor:
        """
        Get the current head utility values.
        
        Returns:
            torch.Tensor: Head utility values for active heads
        """
        n_active = int(torch.sum(self.head_masks).item())
        return self.head_utility_ema[:n_active]
    
    def get_active_heads(self) -> int:
        """
        Get the current number of active attention heads.
        
        Returns:
            int: Number of active heads
        """
        return int(torch.sum(self.head_masks).item())
    
    def extra_repr(self) -> str:
        """Return extra information string."""
        n_active = int(torch.sum(self.head_masks).item())
        return (f"d_model={self.d_model}, n_active={n_active}/{self.max_heads}, "
                f"d_head={self.d_head}, evolution_rate={self.evolution_rate}")