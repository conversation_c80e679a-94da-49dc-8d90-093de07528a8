#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cross-Modal Dimension Mapper Module for ULTRA Hyper-Dimensional Transformer.

This module enables the transformer to integrate information across different modalities
(text, vision, audio, tabular data, etc.) by mapping them to a shared dimensional space
while preserving modality-specific characteristics. It implements the Cross-Modal Dimension
Mapper component described in the ULTRA paper.

Key mathematical operations:
1. Modality projection: x_m^{joint} = P_m(x_m)
2. Cross-modal attention: y_i→j = Attention(Q_i, K_j, V_j)
3. Integration: z_i = f_integrate([y_i→1, y_i→2, ..., y_i→M])

References:
    - ULTRA Technical Paper v1.0, Section 5.5
"""

import logging
import math
from typing import Dict, List, Tuple, Optional, Union, Any, Set

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.nn.utils.rnn import pad_sequence

# Configure logging
logger = logging.getLogger(__name__)


class CrossModalDimensionMapper(nn.Module):
    """
    Maps different modalities to a joint embedding space and facilitates cross-modal attention.
    
    This component allows the transformer to process and integrate information from different
    modalities by projecting them into a common representation space while preserving
    modality-specific features. It implements the cross-modal mapping functionality as 
    described in the ULTRA paper.
    
    Mathematical formulation:
    1. Project each modality: x_m^{joint} = P_m(x_m)
    2. Cross-attention: y_i→j = Attention(Q_i, K_j, V_j)
    3. Integration: z_i = f_integrate([y_i→1, y_i→2, ..., y_i→M])
    """
    
    def __init__(
        self,
        modality_dims: Dict[str, int],
        joint_dim: int = 1024,
        num_heads: int = 8,
        dropout: float = 0.1,
        activation: str = 'gelu',
        layer_norm_eps: float = 1e-5,
        use_modality_embedding: bool = True,
        cross_modal_ffn_ratio: int = 4,
        use_modal_alignment_loss: bool = True,
        device: Optional[torch.device] = None,
        dtype: Optional[torch.dtype] = None,
    ):
        """
        Initialize the Cross-Modal Dimension Mapper.
        
        Args:
            modality_dims: Dictionary mapping modality names to their dimensions
                          (e.g., {'text': 768, 'image': 1024, 'audio': 512})
            joint_dim: Dimension of the joint embedding space
            num_heads: Number of attention heads for cross-modal attention
            dropout: Dropout rate
            activation: Activation function to use ('relu', 'gelu', 'swish')
            layer_norm_eps: Epsilon for layer normalization
            use_modality_embedding: Whether to use learnable modality type embeddings
            cross_modal_ffn_ratio: Ratio of FFN inner dim to joint dim
            use_modal_alignment_loss: Whether to use modal alignment loss during training
            device: Device to use
            dtype: Data type to use
        """
        factory_kwargs = {'device': device, 'dtype': dtype}
        super().__init__()
        
        self.modality_dims = modality_dims
        self.joint_dim = joint_dim
        self.num_heads = num_heads
        self.dropout = dropout
        self.layer_norm_eps = layer_norm_eps
        self.use_modality_embedding = use_modality_embedding
        self.cross_modal_ffn_ratio = cross_modal_ffn_ratio
        self.use_modal_alignment_loss = use_modal_alignment_loss
        
        # Activation function
        if activation == 'relu':
            self.activation = nn.ReLU()
        elif activation == 'gelu':
            self.activation = nn.GELU()
        elif activation == 'swish' or activation == 'silu':
            self.activation = nn.SiLU()
        else:
            raise ValueError(f"Unknown activation: {activation}")
        
        # Create projection layers for each modality
        self.projections = nn.ModuleDict({
            modality: self._create_projection_layer(dim, **factory_kwargs)
            for modality, dim in modality_dims.items()
        })
        
        # Optional modality type embeddings
        if self.use_modality_embedding:
            self.modality_embeddings = nn.ParameterDict({
                modality: nn.Parameter(torch.randn(1, 1, joint_dim, device=device, dtype=dtype) * 0.02)
                for modality in modality_dims
            })
        
        # Cross-modal attention for each modality pair
        self.cross_attentions = nn.ModuleDict()
        for source_mod in modality_dims:
            for target_mod in modality_dims:
                if source_mod != target_mod:
                    attn_name = f"{source_mod}_to_{target_mod}"
                    self.cross_attentions[attn_name] = nn.MultiheadAttention(
                        embed_dim=joint_dim,
                        num_heads=num_heads,
                        dropout=dropout,
                        batch_first=True,
                        **factory_kwargs
                    )
        
        # Normalization and feed-forward networks for each cross-modal interaction
        self.cross_norms1 = nn.ModuleDict({
            f"{source_mod}_to_{target_mod}": nn.LayerNorm(joint_dim, eps=layer_norm_eps, **factory_kwargs)
            for source_mod in modality_dims
            for target_mod in modality_dims
            if source_mod != target_mod
        })
        
        self.cross_norms2 = nn.ModuleDict({
            f"{source_mod}_to_{target_mod}": nn.LayerNorm(joint_dim, eps=layer_norm_eps, **factory_kwargs)
            for source_mod in modality_dims
            for target_mod in modality_dims
            if source_mod != target_mod
        })
        
        self.cross_ffns = nn.ModuleDict({
            f"{source_mod}_to_{target_mod}": self._create_ffn_layer(**factory_kwargs)
            for source_mod in modality_dims
            for target_mod in modality_dims
            if source_mod != target_mod
        })
        
        # Integration layers for combining cross-modal information
        self.integration_layers = nn.ModuleDict({
            modality: nn.Sequential(
                nn.Linear(joint_dim * len(modality_dims), joint_dim * 2, **factory_kwargs),
                self.activation,
                nn.Dropout(dropout),
                nn.Linear(joint_dim * 2, joint_dim, **factory_kwargs),
                nn.LayerNorm(joint_dim, eps=layer_norm_eps, **factory_kwargs)
            )
            for modality in modality_dims
        })
        
        # Gating mechanism for modality integration
        self.integration_gates = nn.ModuleDict({
            modality: nn.Sequential(
                nn.Linear(joint_dim * len(modality_dims), joint_dim, **factory_kwargs),
                nn.Sigmoid()
            )
            for modality in modality_dims
        })
        
        # Modal alignment loss components (if enabled)
        if self.use_modal_alignment_loss:
            # Projection for calculating alignment between modalities
            self.alignment_projector = nn.Linear(joint_dim, joint_dim, **factory_kwargs)
            
            # Temperature parameter for alignment loss
            self.alignment_temperature = nn.Parameter(torch.ones(1, device=device, dtype=dtype) * 0.07)
        
        # Initialize parameters
        self._reset_parameters()
        
        logger.info(
            f"Initialized CrossModalDimensionMapper with {len(modality_dims)} modalities, "
            f"joint dimension: {joint_dim}, attention heads: {num_heads}"
        )
    
    def _create_projection_layer(self, input_dim, **factory_kwargs):
        """
        Create a projection layer for a specific modality.
        
        Args:
            input_dim: Input dimension for the modality
            factory_kwargs: Additional keyword arguments for layer creation
            
        Returns:
            nn.Module: Projection layer
        """
        return nn.Sequential(
            nn.Linear(input_dim, self.joint_dim, **factory_kwargs),
            nn.LayerNorm(self.joint_dim, eps=self.layer_norm_eps, **factory_kwargs),
            nn.Dropout(self.dropout)
        )
    
    def _create_ffn_layer(self, **factory_kwargs):
        """
        Create a feed-forward network layer for cross-modal processing.
        
        Args:
            factory_kwargs: Additional keyword arguments for layer creation
            
        Returns:
            nn.Module: Feed-forward network
        """
        return nn.Sequential(
            nn.Linear(self.joint_dim, self.joint_dim * self.cross_modal_ffn_ratio, **factory_kwargs),
            self.activation,
            nn.Dropout(self.dropout),
            nn.Linear(self.joint_dim * self.cross_modal_ffn_ratio, self.joint_dim, **factory_kwargs),
            nn.Dropout(self.dropout)
        )
    
    def _reset_parameters(self):
        """Reset parameters using appropriate initialization methods."""
        # Initialize linear layers
        for module in self.modules():
            if isinstance(module, nn.Linear):
                # Xavier uniform for linear layers
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
        
        # Initialize layer norm
        for module in self.modules():
            if isinstance(module, nn.LayerNorm):
                nn.init.ones_(module.weight)
                nn.init.zeros_(module.bias)
        
        # Initialize modality embeddings with small random values
        if self.use_modality_embedding:
            for emb in self.modality_embeddings.values():
                nn.init.normal_(emb, mean=0.0, std=0.02)
    
    def _check_tensor_dimensions(self, inputs: Dict[str, torch.Tensor]):
        """
        Verify input tensors have correct dimensionality for each modality.
        
        Args:
            inputs: Dictionary mapping modality names to tensors
            
        Raises:
            ValueError: If modality is unknown or tensor has incorrect dimension
        """
        for modality, tensor in inputs.items():
            if modality not in self.modality_dims:
                raise ValueError(
                    f"Unknown modality: {modality}. "
                    f"Available modalities: {list(self.modality_dims.keys())}"
                )
            
            if tensor.dim() < 2:
                raise ValueError(
                    f"Input tensor for modality {modality} must have at least 2 dimensions. "
                    f"Got shape: {tensor.shape}"
                )
            
            if tensor.size(-1) != self.modality_dims[modality]:
                raise ValueError(
                    f"Tensor for modality {modality} has incorrect dimension. "
                    f"Expected: {self.modality_dims[modality]}, got: {tensor.size(-1)}"
                )
    
    def _get_attention_mask(
        self, 
        inputs: Dict[str, torch.Tensor], 
        attention_mask: Optional[Dict[str, torch.Tensor]] = None
    ) -> Dict[str, Optional[torch.Tensor]]:
        """
        Get attention masks for each modality.
        
        Args:
            inputs: Dictionary mapping modality names to tensors
            attention_mask: Dictionary of explicit attention masks for each modality
            
        Returns:
            Dict[str, Optional[torch.Tensor]]: Dictionary of attention masks
        """
        masks = {}
        
        for modality, tensor in inputs.items():
            if attention_mask is not None and modality in attention_mask:
                # Use provided explicit mask
                mask = attention_mask[modality]
                
                # Convert boolean mask to padding mask if needed
                if mask.dtype == torch.bool:
                    # For MultiheadAttention, we need:
                    # True = position is NOT allowed to attend, False = position can attend
                    # So we need to invert the boolean mask
                    mask = ~mask
                
                masks[modality] = mask
            else:
                # No explicit mask provided, default to None
                masks[modality] = None
        
        return masks
    
    def _project_modalities(
        self, 
        inputs: Dict[str, torch.Tensor]
    ) -> Dict[str, torch.Tensor]:
        """
        Project each modality to the joint dimension.
        
        Implements the x_m^{joint} = P_m(x_m) equation from the formulation.
        
        Args:
            inputs: Dictionary mapping modality names to tensors
            
        Returns:
            Dict[str, torch.Tensor]: Dictionary of projected modality tensors
        """
        projected = {}
        
        for modality, tensor in inputs.items():
            # Project to joint dimension
            proj = self.projections[modality](tensor)
            
            # Add modality embedding if enabled
            if self.use_modality_embedding:
                # Broadcast modality embedding to the right shape
                batch_size, seq_len = proj.shape[:2]
                mod_emb = self.modality_embeddings[modality].expand(batch_size, seq_len, -1)
                
                # Add to projection
                proj = proj + mod_emb
            
            projected[modality] = proj
        
        return projected
    
    def _apply_cross_attention(
        self,
        projected: Dict[str, torch.Tensor],
        masks: Dict[str, Optional[torch.Tensor]]
    ) -> Dict[str, Dict[str, torch.Tensor]]:
        """
        Apply cross-modal attention between all modality pairs.
        
        Implements the y_i→j = Attention(Q_i, K_j, V_j) equation from the formulation.
        
        Args:
            projected: Dictionary of projected modality tensors
            masks: Dictionary of attention masks
            
        Returns:
            Dict[str, Dict[str, torch.Tensor]]: Dictionary mapping source modality to 
                a dictionary of attended features from each target modality
        """
        cross_attended = {}
        
        for source_mod in projected:
            cross_attended[source_mod] = {}
            
            # Self-attention (identity)
            cross_attended[source_mod][source_mod] = projected[source_mod]
            
            # Cross-attention with other modalities
            for target_mod in projected:
                if source_mod != target_mod:
                    attn_key = f"{source_mod}_to_{target_mod}"
                    
                    # Get query, key, value
                    query = projected[source_mod]
                    key = value = projected[target_mod]
                    
                    # Get key padding mask for target modality
                    key_padding_mask = masks[target_mod]
                    
                    # Apply attention norm before attention
                    normed_query = self.cross_norms1[attn_key](query)
                    
                    # Apply cross-modal attention
                    attended, _ = self.cross_attentions[attn_key](
                        query=normed_query,
                        key=key,
                        value=value,
                        key_padding_mask=key_padding_mask,
                        need_weights=False
                    )
                    
                    # Apply residual connection
                    attended = query + attended
                    
                    # Apply feed-forward norm
                    normed_attended = self.cross_norms2[attn_key](attended)
                    
                    # Apply feed-forward network
                    ffn_output = self.cross_ffns[attn_key](normed_attended)
                    
                    # Apply residual connection
                    final_output = attended + ffn_output
                    
                    cross_attended[source_mod][target_mod] = final_output
        
        return cross_attended
    
    def _integrate_modalities(
        self,
        cross_attended: Dict[str, Dict[str, torch.Tensor]]
    ) -> Dict[str, torch.Tensor]:
        """
        Integrate cross-modal information for each modality.
        
        Implements the z_i = f_integrate([y_i→1, y_i→2, ..., y_i→M]) equation from the formulation.
        
        Args:
            cross_attended: Dictionary mapping source modality to a dictionary of 
                attended features from each target modality
            
        Returns:
            Dict[str, torch.Tensor]: Dictionary of integrated modality features
        """
        integrated = {}
        
        for source_mod in cross_attended:
            # Collect attended features from all modalities
            attended_features = []
            for target_mod in self.modality_dims:
                attended_features.append(cross_attended[source_mod][target_mod])
            
            # Concatenate features along the last dimension
            combined = torch.cat(attended_features, dim=-1)
            
            # Calculate integration gate
            gate = self.integration_gates[source_mod](combined)
            
            # Apply integration layer
            integrated_features = self.integration_layers[source_mod](combined)
            
            # Apply gating mechanism
            gated_output = gate * integrated_features + (1 - gate) * cross_attended[source_mod][source_mod]
            
            integrated[source_mod] = gated_output
        
        return integrated
    
    def forward(
        self,
        inputs: Dict[str, torch.Tensor],
        attention_mask: Optional[Dict[str, torch.Tensor]] = None,
        return_all_representations: bool = False
    ) -> Union[Dict[str, torch.Tensor], Tuple[Dict[str, torch.Tensor], Dict[str, Dict[str, torch.Tensor]]]]:
        """
        Process multi-modal inputs by projecting and performing cross-modal attention.
        
        Args:
            inputs: Dictionary mapping modality names to tensors
                  (e.g., {'text': text_tensor, 'image': image_tensor})
            attention_mask: Dictionary of attention masks for each modality
            return_all_representations: Whether to return all intermediate representations
            
        Returns:
            Dict[str, torch.Tensor] or Tuple[Dict[str, torch.Tensor], Dict[str, Dict[str, torch.Tensor]]]:
                Dictionary mapping modality names to enhanced representations, and
                optionally a dictionary of all cross-attended representations
        """
        # Check input dimensions
        self._check_tensor_dimensions(inputs)
        
        # Get attention masks
        masks = self._get_attention_mask(inputs, attention_mask)
        
        # Project each modality to joint dimension
        projected = self._project_modalities(inputs)
        
        # Perform cross-modal attention
        cross_attended = self._apply_cross_attention(projected, masks)
        
        # Integrate cross-modal information
        integrated = self._integrate_modalities(cross_attended)
        
        if return_all_representations:
            return integrated, cross_attended
        else:
            return integrated
    
    def process_modality(
        self,
        modality: str,
        tensor: torch.Tensor,
        context_dict: Optional[Dict[str, torch.Tensor]] = None,
        attention_mask: Optional[Dict[str, torch.Tensor]] = None
    ) -> torch.Tensor:
        """
        Process a single modality with optional context from other modalities.
        
        Args:
            modality: Name of the modality to process
            tensor: Input tensor for the specified modality
            context_dict: Dictionary of context tensors from other modalities
            attention_mask: Dictionary of attention masks
            
        Returns:
            torch.Tensor: Enhanced representation of the input modality
        """
        if modality not in self.modality_dims:
            raise ValueError(
                f"Unknown modality: {modality}. "
                f"Available modalities: {list(self.modality_dims.keys())}"
            )
            
        # Create inputs dictionary
        inputs = {modality: tensor}
        if context_dict:
            inputs.update(context_dict)
            
        # Process through the full forward pass
        results = self.forward(inputs, attention_mask)
        
        # Return the enhanced representation for the requested modality
        return results[modality]
    
    def compute_alignment_loss(
        self,
        projected: Dict[str, torch.Tensor],
        masks: Optional[Dict[str, torch.Tensor]] = None
    ) -> torch.Tensor:
        """
        Compute alignment loss to encourage consistent representations across modalities.
        
        Implements a contrastive loss that pulls together representations of the same
        instance across different modalities while pushing apart representations of
        different instances.
        
        Args:
            projected: Dictionary of projected modality tensors
            masks: Dictionary of attention masks to identify valid tokens
            
        Returns:
            torch.Tensor: Alignment loss
        """
        if not self.use_modal_alignment_loss:
            return torch.tensor(0.0, device=next(iter(projected.values())).device)
        
        # Need at least 2 modalities for alignment
        if len(projected) < 2:
            return torch.tensor(0.0, device=next(iter(projected.values())).device)
        
        # Get all modality names
        modalities = list(projected.keys())
        
        # Compute aggregate features for each modality
        # We'll use mean pooling across sequence dimension
        pooled_features = {}
        
        for modality, tensor in projected.items():
            # Apply pooling based on mask if available
            if masks is not None and masks.get(modality) is not None:
                # Create an actual mask (1 where valid, 0 where padding)
                valid_mask = (~masks[modality]).float().unsqueeze(-1)
                # Apply mask and compute mean
                masked_tensor = tensor * valid_mask
                # Sum and divide by number of valid tokens (avoid div by zero)
                sum_mask = valid_mask.sum(dim=1, keepdim=True).clamp(min=1.0)
                pooled = masked_tensor.sum(dim=1) / sum_mask.squeeze(1)
            else:
                # Simple mean pooling if no mask
                pooled = tensor.mean(dim=1)
            
            # Project for alignment
            pooled_features[modality] = self.alignment_projector(pooled)
        
        # Compute pairwise alignment loss across modalities
        total_loss = 0.0
        num_pairs = 0
        
        for i, mod_i in enumerate(modalities):
            for j in range(i+1, len(modalities)):
                mod_j = modalities[j]
                
                # Get features for both modalities
                feat_i = pooled_features[mod_i]
                feat_j = pooled_features[mod_j]
                
                # Normalize features
                feat_i = F.normalize(feat_i, dim=1)
                feat_j = F.normalize(feat_j, dim=1)
                
                # Compute similarity matrix
                similarity = torch.matmul(feat_i, feat_j.t()) / self.alignment_temperature
                
                # The ground truth is the diagonal (matching pairs)
                targets = torch.arange(similarity.size(0), device=similarity.device)
                
                # Compute bidirectional cross-entropy loss
                loss_i_to_j = F.cross_entropy(similarity, targets)
                loss_j_to_i = F.cross_entropy(similarity.t(), targets)
                
                total_loss += (loss_i_to_j + loss_j_to_i) / 2
                num_pairs += 1
        
        # Average loss across all modality pairs
        if num_pairs > 0:
            avg_loss = total_loss / num_pairs
        else:
            avg_loss = torch.tensor(0.0, device=next(iter(projected.values())).device)
        
        return avg_loss
    
    def extra_repr(self) -> str:
        """Return extra information string."""
        return f'modalities={list(self.modality_dims.keys())}, joint_dim={self.joint_dim}'


class ModalityFusion(nn.Module):
    """
    Fuses representations from multiple modalities into a single unified representation.
    
    This component implements various strategies for combining information from different
    modalities after they have been processed by the CrossModalDimensionMapper.
    
    Available fusion methods:
    1. 'attention': Use attention mechanism to weight different modalities
    2. 'concat': Concatenate and project representations
    3. 'mean': Simple averaging of representations
    4. 'sum': Simple addition of representations
    5. 'gated': Gated combination of representations
    6. 'bilinear': Bilinear fusion of modalities
    7. 'film': Feature-wise linear modulation
    """
    
    def __init__(
        self,
        joint_dim: int,
        modalities: Optional[List[str]] = None,
        output_dim: Optional[int] = None,
        fusion_method: str = 'attention',
        attention_heads: int = 4,
        dropout: float = 0.1,
        activation: str = 'gelu',
        layer_norm_eps: float = 1e-5,
        device: Optional[torch.device] = None,
        dtype: Optional[torch.dtype] = None,
    ):
        """
        Initialize the Modality Fusion module.
        
        Args:
            joint_dim: Dimension of the joint embedding space
            modalities: List of modalities to fuse
            output_dim: Dimension of the output (if None, uses joint_dim)
            fusion_method: Method for fusion ('attention', 'concat', 'mean', 'sum', 'gated', etc.)
            attention_heads: Number of attention heads for attention fusion
            dropout: Dropout rate
            activation: Activation function to use ('relu', 'gelu', 'swish')
            layer_norm_eps: Epsilon for layer normalization
            device: Device to use
            dtype: Data type to use
        """
        factory_kwargs = {'device': device, 'dtype': dtype}
        super().__init__()
        
        self.joint_dim = joint_dim
        self.modalities = modalities
        self.output_dim = output_dim if output_dim is not None else joint_dim
        self.fusion_method = fusion_method
        self.attention_heads = attention_heads
        self.dropout = dropout
        self.layer_norm_eps = layer_norm_eps
        
        # Activation function
        if activation == 'relu':
            self.activation = nn.ReLU()
        elif activation == 'gelu':
            self.activation = nn.GELU()
        elif activation == 'swish' or activation == 'silu':
            self.activation = nn.SiLU()
        else:
            raise ValueError(f"Unknown activation: {activation}")
        
        # Initialize fusion components based on method
        if fusion_method == 'attention':
            self.attention = nn.MultiheadAttention(
                embed_dim=joint_dim,
                num_heads=attention_heads,
                dropout=dropout,
                batch_first=True,
                **factory_kwargs
            )
            self.modality_encoding = None
            if modalities is not None:
                # Create learnable modality encodings
                self.modality_encoding = nn.Parameter(
                    torch.randn(len(modalities), 1, joint_dim, device=device, dtype=dtype) * 0.02
                )
        
        elif fusion_method == 'concat':
            # Projection to combine concatenated features
            self.projection = nn.Sequential(
                nn.Linear(joint_dim, joint_dim, **factory_kwargs),
                self.activation,
                nn.Dropout(dropout)
            )
            self.final_projection = nn.Linear(joint_dim, self.output_dim, **factory_kwargs)
        
        elif fusion_method == 'gated':
            # Gating mechanism for each modality
            self.gate_generator = nn.Sequential(
                nn.Linear(joint_dim, 1, **factory_kwargs),
                nn.Sigmoid()
            )
        
        elif fusion_method == 'bilinear':
            # Bilinear fusion (only works with 2 modalities)
            if modalities is not None and len(modalities) != 2:
                raise ValueError(f"Bilinear fusion requires exactly 2 modalities, got {len(modalities)}")
            
            self.bilinear = nn.Bilinear(joint_dim, joint_dim, joint_dim, **factory_kwargs)
            self.bilinear_norm = nn.LayerNorm(joint_dim, eps=layer_norm_eps, **factory_kwargs)
        
        elif fusion_method == 'film':
            # FiLM fusion (modality1 modulates modality2)
            if modalities is not None and len(modalities) != 2:
                raise ValueError(f"FiLM fusion requires exactly 2 modalities, got {len(modalities)}")
            
            self.film_gamma = nn.Linear(joint_dim, joint_dim, **factory_kwargs)
            self.film_beta = nn.Linear(joint_dim, joint_dim, **factory_kwargs)
        
        # For all methods, have a final transformation
        self.output_transform = nn.Sequential(
            nn.Linear(joint_dim, self.output_dim, **factory_kwargs),
            nn.LayerNorm(self.output_dim, eps=layer_norm_eps, **factory_kwargs),
            nn.Dropout(dropout)
        )
        
        # Initialize weights
        self._reset_parameters()
        
        logger.info(f"Initialized ModalityFusion with method: {fusion_method}")
    
    def _reset_parameters(self):
        """Reset parameters using appropriate initialization methods."""
        # Initialize linear layers
        for module in self.modules():
            if isinstance(module, nn.Linear):
                # Xavier uniform for linear layers
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
        
        # Initialize layer norm
        for module in self.modules():
            if isinstance(module, nn.LayerNorm):
                nn.init.ones_(module.weight)
                nn.init.zeros_(module.bias)
        
        # Initialize modality encoding with small random values
        if self.modality_encoding is not None:
            nn.init.normal_(self.modality_encoding, mean=0.0, std=0.02)
    
    def _attention_fusion(self, features_stack, modality_names=None):
        """
        Fusion using cross-attention mechanism.
        
        Args:
            features_stack: Stacked features [batch, num_modalities, feature_dim]
            modality_names: Optional list of modality names (for modality encoding)
            
        Returns:
            torch.Tensor: Fused representation [batch, feature_dim]
        """
        batch_size, num_modalities, feature_dim = features_stack.shape
        
        # If using modality encoding and names are provided, apply it
        if self.modality_encoding is not None and modality_names is not None and self.modalities is not None:
            # Map modality names to indices
            modality_indices = [self.modalities.index(m) for m in modality_names]
            
            # Get corresponding encodings
            encodings = self.modality_encoding[modality_indices]
            
            # Add to features
            features_stack = features_stack + encodings
        
        # Use the first modality as the query
        query = features_stack[:, 0:1, :]  # [batch, 1, feature_dim]
        
        # Use all modalities as keys and values
        key = value = features_stack  # [batch, num_modalities, feature_dim]
        
        # Apply cross-attention
        attended, _ = self.attention(query, key, value)  # [batch, 1, feature_dim]
        
        # Squeeze out the single-token dimension
        fused = attended.squeeze(1)  # [batch, feature_dim]
        
        return fused
    
    def _concat_fusion(self, features_stack):
        """
        Fusion by projection of concatenated modalities.
        
        Args:
            features_stack: Stacked features [batch, num_modalities, feature_dim]
            
        Returns:
            torch.Tensor: Fused representation [batch, feature_dim]
        """
        batch_size, num_modalities, feature_dim = features_stack.shape
        
        # Project each modality separately
        projected_features = torch.stack([
            self.projection(features_stack[:, i, :])
            for i in range(num_modalities)
        ], dim=1)
        
        # Sum up the projections
        fused = torch.sum(projected_features, dim=1)
        fused = self.final_projection(fused)
        
        return fused
    
    def _gated_fusion(self, features_stack):
        """
        Fusion with learnable gates for each modality.
        
        Args:
            features_stack: Stacked features [batch, num_modalities, feature_dim]
            
        Returns:
            torch.Tensor: Fused representation [batch, feature_dim]
        """
        batch_size, num_modalities, feature_dim = features_stack.shape
        
        # Generate gates for each modality
        gates = torch.cat([
            self.gate_generator(features_stack[:, i, :])
            for i in range(num_modalities)
        ], dim=1)  # [batch, num_modalities]
        
        # Normalize gates to sum to 1
        gates = F.softmax(gates, dim=1)
        
        # Apply gates to features
        gated_features = features_stack * gates.unsqueeze(-1)
        
        # Sum up the gated features
        fused = torch.sum(gated_features, dim=1)
        
        return fused
    
    def _bilinear_fusion(self, features_stack):
        """
        Bilinear fusion for exactly 2 modalities.
        
        Args:
            features_stack: Stacked features [batch, 2, feature_dim]
            
        Returns:
            torch.Tensor: Fused representation [batch, feature_dim]
        """
        batch_size, num_modalities, feature_dim = features_stack.shape
        
        if num_modalities != 2:
            raise ValueError(f"Bilinear fusion requires exactly 2 modalities, got {num_modalities}")
        
        # Get the two modalities
        mod1 = features_stack[:, 0, :]
        mod2 = features_stack[:, 1, :]
        
        # Apply bilinear fusion
        fused = self.bilinear(mod1, mod2)
        fused = self.bilinear_norm(fused)
        
        return fused
    
    def _film_fusion(self, features_stack):
        """
        Feature-wise Linear Modulation (FiLM) fusion.
        
        Args:
            features_stack: Stacked features [batch, 2, feature_dim]
            
        Returns:
            torch.Tensor: Fused representation [batch, feature_dim]
        """
        batch_size, num_modalities, feature_dim = features_stack.shape
        
        if num_modalities != 2:
            raise ValueError(f"FiLM fusion requires exactly 2 modalities, got {num_modalities}")
        
        # Get the two modalities (conditioning and target)
        cond = features_stack[:, 0, :]  # Conditioning modality
        target = features_stack[:, 1, :]  # Target modality to be modulated
        
        # Generate gamma and beta
        gamma = self.film_gamma(cond)
        beta = self.film_beta(cond)
        
        # Apply FiLM: gamma * target + beta
        fused = gamma * target + beta
        
        return fused
    
    def forward(
        self,
        modality_features: Dict[str, torch.Tensor],
        modality_weights: Optional[Dict[str, float]] = None
    ) -> torch.Tensor:
        """
        Fuse features from multiple modalities.
        
        Args:
            modality_features: Dictionary mapping modality names to feature tensors
            modality_weights: Optional dictionary of explicit weights for each modality
            
        Returns:
            torch.Tensor: Fused representation
        """
        if not modality_features:
            raise ValueError("No modality features provided for fusion")
        
        # If only one modality, just apply output transform
        if len(modality_features) == 1:
            single_feature = next(iter(modality_features.values()))
            return self.output_transform(single_feature)
        
        # Get modality names and feature tensors
        modality_names = list(modality_features.keys())
        features_list = list(modality_features.values())
        
        # Stack as batch dimension x num_modalities x feature_dim
        features_stack = torch.stack(features_list, dim=1)
        
        # Apply fusion method
        if self.fusion_method == 'attention':
            fused = self._attention_fusion(features_stack, modality_names)
            fused = self.output_transform(fused)
            
        elif self.fusion_method == 'mean':
            # Apply weights if provided
            if modality_weights is not None:
                weights = torch.tensor(
                    [modality_weights.get(mod, 1.0) for mod in modality_names],
                    device=features_stack.device
                )
                # Normalize weights
                weights = weights / weights.sum()
                # Apply weights
                fused = torch.sum(features_stack * weights.view(1, -1, 1), dim=1)
            else:
                fused = torch.mean(features_stack, dim=1)
            
            fused = self.output_transform(fused)
            
        elif self.fusion_method == 'sum':
            # Apply weights if provided
            if modality_weights is not None:
                weights = torch.tensor(
                    [modality_weights.get(mod, 1.0) for mod in modality_names],
                    device=features_stack.device
                )
                # Apply weights
                fused = torch.sum(features_stack * weights.view(1, -1, 1), dim=1)
            else:
                fused = torch.sum(features_stack, dim=1)
            
            fused = self.output_transform(fused)
            
        elif self.fusion_method == 'concat':
            fused = self._concat_fusion(features_stack)
            
        elif self.fusion_method == 'gated':
            fused = self._gated_fusion(features_stack)
            fused = self.output_transform(fused)
            
        elif self.fusion_method == 'bilinear':
            fused = self._bilinear_fusion(features_stack)
            fused = self.output_transform(fused)
            
        elif self.fusion_method == 'film':
            fused = self._film_fusion(features_stack)
            fused = self.output_transform(fused)
            
        else:
            raise ValueError(f"Unknown fusion method: {self.fusion_method}")
        
        return fused
    
    def extra_repr(self) -> str:
        """Return extra information string."""
        return f'method={self.fusion_method}, joint_dim={self.joint_dim}, output_dim={self.output_dim}'


# Alias for backwards compatibility
CrossModalMapper = CrossModalDimensionMapper