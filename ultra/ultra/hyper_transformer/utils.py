#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hyper-Dimensional Transformer Utilities

This module provides utility functions and classes for the ULTRA hyper-dimensional 
transformer system, including position encodings, attention optimizations, and 
helper functions for transformer operations.
"""

import math
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Tuple, Optional, Union


class RotaryPositionEncoding(nn.Module):
    """Rotary Position Encoding (RoPE) for enhanced positional awareness.
    
    This implements the rotary position encoding from "RoFormer: Enhanced 
    Transformer with Rotary Position Embedding" which applies rotation to 
    the embedding dimensions based on position.
    """
    
    def __init__(self, dim: int, max_position_embeddings: int = 2048, base: float = 10000):
        """Initialize rotary position encoding.
        
        Args:
            dim: Embedding dimension
            max_position_embeddings: Maximum sequence length
            base: Base for computing rotation frequencies
        """
        super().__init__()
        self.dim = dim
        self.max_position_embeddings = max_position_embeddings
        self.base = base
        
        # Compute rotation frequencies
        inv_freq = 1. / (base ** (torch.arange(0, dim, 2).float() / dim))
        self.register_buffer('inv_freq', inv_freq)
        
        # Cache for efficiency
        self._cache = {}
    
    def forward(self, x: torch.Tensor, seq_len: int = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """Apply rotary position encoding.
        
        Args:
            x: Input tensor of shape (batch_size, seq_len, dim)
            seq_len: Sequence length (optional, inferred from x if not provided)
        
        Returns:
            Tuple of (cos_pos, sin_pos) tensors for rotation
        """
        if seq_len is None:
            seq_len = x.shape[1]
        
        # Use cached values if available
        if seq_len in self._cache:
            return self._cache[seq_len]
        
        # Compute position indices
        t = torch.arange(seq_len, device=x.device).type_as(self.inv_freq)
        
        # Compute frequencies
        freqs = torch.einsum('i,j->ij', t, self.inv_freq)
        emb = torch.cat((freqs, freqs), dim=-1)
        
        cos_pos = emb.cos()[None, :, :]  # (1, seq_len, dim)
        sin_pos = emb.sin()[None, :, :]  # (1, seq_len, dim)
        
        # Cache for future use
        self._cache[seq_len] = (cos_pos, sin_pos)
        
        return cos_pos, sin_pos


class ALiBiPositionalBias(nn.Module):
    """Attention with Linear Biases (ALiBi) for length extrapolation.
    
    This implements ALiBi from "Train Short, Test Long: Attention with Linear 
    Biases Enables Input Length Extrapolation" which adds linear biases to 
    attention scores based on relative distances.
    """
    
    def __init__(self, num_heads: int):
        """Initialize ALiBi positional bias.
        
        Args:
            num_heads: Number of attention heads
        """
        super().__init__()
        self.num_heads = num_heads
        
        # Compute slopes for each head
        slopes = self._get_slopes(num_heads)
        self.register_buffer('slopes', slopes)
    
    def _get_slopes(self, num_heads: int) -> torch.Tensor:
        """Get slopes for ALiBi bias computation.
        
        Args:
            num_heads: Number of attention heads
        
        Returns:
            Tensor of slopes for each head
        """
        def get_slopes_power_of_2(n):
            start = (2**(-2**-(math.log2(n)-3)))
            ratio = start
            return [start*ratio**i for i in range(n)]
        
        if math.log2(num_heads).is_integer():
            return torch.tensor(get_slopes_power_of_2(num_heads))
        else:
            closest_power_of_2 = 2**math.floor(math.log2(num_heads))
            slopes_1 = get_slopes_power_of_2(closest_power_of_2)
            slopes_2 = self._get_slopes(2*closest_power_of_2)
            slopes_2 = slopes_2[0::2][:num_heads-closest_power_of_2]
            return torch.tensor(slopes_1 + slopes_2)
    
    def forward(self, seq_len: int) -> torch.Tensor:
        """Compute ALiBi bias matrix.
        
        Args:
            seq_len: Sequence length
        
        Returns:
            Bias matrix of shape (num_heads, seq_len, seq_len)
        """
        # Create distance matrix
        context_position = torch.arange(seq_len)[:, None]
        memory_position = torch.arange(seq_len)[None, :]
        relative_position = memory_position - context_position
        
        # Apply slopes to get bias
        bias = self.slopes[:, None, None] * relative_position.abs()
        return -bias


def apply_rotary_pos_emb(x: torch.Tensor, cos: torch.Tensor, sin: torch.Tensor) -> torch.Tensor:
    """Apply rotary positional embedding to input tensor.
    
    Args:
        x: Input tensor of shape (..., seq_len, dim)
        cos: Cosine component of rotary encoding
        sin: Sine component of rotary encoding
    
    Returns:
        Tensor with rotary position encoding applied
    """
    # Split into two halves for rotation
    x1, x2 = x[..., :x.shape[-1]//2], x[..., x.shape[-1]//2:]
    
    # Apply rotation
    return torch.cat([
        x1 * cos[..., :x1.shape[-1]] - x2 * sin[..., :x2.shape[-1]],
        x2 * cos[..., :x2.shape[-1]] + x1 * sin[..., :x1.shape[-1]]
    ], dim=-1)


def scaled_dot_product_attention(
    query: torch.Tensor, 
    key: torch.Tensor, 
    value: torch.Tensor,
    attn_mask: Optional[torch.Tensor] = None,
    dropout_p: float = 0.0,
    is_causal: bool = False,
    scale: Optional[float] = None
) -> Tuple[torch.Tensor, torch.Tensor]:
    """Compute scaled dot-product attention.
    
    Args:
        query: Query tensor of shape (batch_size, num_heads, seq_len, head_dim)
        key: Key tensor of shape (batch_size, num_heads, seq_len, head_dim)
        value: Value tensor of shape (batch_size, num_heads, seq_len, head_dim)
        attn_mask: Optional attention mask
        dropout_p: Dropout probability
        is_causal: Whether to apply causal masking
        scale: Optional scaling factor (defaults to 1/sqrt(head_dim))
    
    Returns:
        Tuple of (output, attention_weights)
    """
    if scale is None:
        scale = 1.0 / math.sqrt(query.size(-1))
    
    # Compute attention scores
    attn_weights = torch.matmul(query, key.transpose(-2, -1)) * scale
    
    # Apply causal mask if requested
    if is_causal:
        seq_len = query.size(-2)
        causal_mask = torch.triu(torch.ones(seq_len, seq_len), diagonal=1).bool()
        attn_weights.masked_fill_(causal_mask, float('-inf'))
    
    # Apply attention mask
    if attn_mask is not None:
        attn_weights += attn_mask
    
    # Apply softmax
    attn_weights = F.softmax(attn_weights, dim=-1)
    
    # Apply dropout
    if dropout_p > 0.0 and query.training:
        attn_weights = F.dropout(attn_weights, p=dropout_p)
    
    # Compute output
    output = torch.matmul(attn_weights, value)
    
    return output, attn_weights


def get_activation_function(name: str) -> nn.Module:
    """Get activation function by name.
    
    Args:
        name: Name of activation function
    
    Returns:
        Activation function module
    """
    activations = {
        'relu': nn.ReLU(),
        'gelu': nn.GELU(),
        'swish': nn.SiLU(),
        'mish': nn.Mish(),
        'tanh': nn.Tanh(),
        'sigmoid': nn.Sigmoid(),
    }
    
    if name.lower() not in activations:
        raise ValueError(f"Unknown activation function: {name}")
    
    return activations[name.lower()]


def compute_model_size(model: nn.Module) -> Tuple[int, int]:
    """Compute model size in parameters and memory.
    
    Args:
        model: PyTorch model
    
    Returns:
        Tuple of (total_params, trainable_params)
    """
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    return total_params, trainable_params


def create_padding_mask(input_ids: torch.Tensor, pad_token_id: int = 0) -> torch.Tensor:
    """Create padding mask for attention computation.
    
    Args:
        input_ids: Input token IDs
        pad_token_id: ID of padding token
    
    Returns:
        Boolean mask where True indicates padding positions
    """
    return input_ids == pad_token_id


def apply_gradient_clipping(model: nn.Module, max_norm: float = 1.0) -> float:
    """Apply gradient clipping to model parameters.
    
    Args:
        model: PyTorch model
        max_norm: Maximum gradient norm
    
    Returns:
        Actual gradient norm before clipping
    """
    return torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm)


class LayerNorm(nn.Module):
    """Layer normalization with optional centering and scaling.
    
    This is a more flexible version of standard LayerNorm that allows
    disabling centering and scaling independently.
    """
    
    def __init__(self, normalized_shape: int, eps: float = 1e-5, 
                 elementwise_affine: bool = True, center: bool = True):
        """Initialize layer normalization.
        
        Args:
            normalized_shape: Size of the feature dimension
            eps: Small constant for numerical stability
            elementwise_affine: Whether to learn affine parameters
            center: Whether to center the features (subtract mean)
        """
        super().__init__()
        self.normalized_shape = normalized_shape
        self.eps = eps
        self.elementwise_affine = elementwise_affine
        self.center = center
        
        if self.elementwise_affine:
            self.weight = nn.Parameter(torch.ones(normalized_shape))
            self.bias = nn.Parameter(torch.zeros(normalized_shape))
        else:
            self.register_parameter('weight', None)
            self.register_parameter('bias', None)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Apply layer normalization.
        
        Args:
            x: Input tensor
        
        Returns:
            Normalized tensor
        """
        if self.center:
            mean = x.mean(-1, keepdim=True)
            x = x - mean
        
        variance = x.var(-1, keepdim=True, unbiased=False)
        x = x / torch.sqrt(variance + self.eps)
        
        if self.elementwise_affine:
            x = x * self.weight + self.bias
        
        return x


# Export all utilities
__all__ = [
    'RotaryPositionEncoding',
    'ALiBiPositionalBias', 
    'apply_rotary_pos_emb',
    'scaled_dot_product_attention',
    'get_activation_function',
    'compute_model_size',
    'create_padding_mask',
    'apply_gradient_clipping',
    'LayerNorm'
]
