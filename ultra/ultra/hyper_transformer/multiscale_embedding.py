#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Multi-Scale Knowledge Embedding Module for ULTRA Hyper-Dimensional Transformer.

This module implements the Multi-Scale Knowledge Embedding (MSKE) component as described
in the ULTRA paper. MSKE represents information simultaneously at multiple levels of
abstraction, from detailed token-level information to broader conceptual representations.

Key mathematical formulations:
1. Multi-scale representation: E = {E^1, E^2, ..., E^L}
2. Scale projections: E^{l+1} = P_up(E^l), E^{l-1} = P_down(E^l)
3. Multi-scale attention: MS-Attention(Q, K, V) = ∑_{l=1}^{L} w_l · Attention(Q^l, K^l, V^l)

References:
    - ULTRA Technical Paper v1.0, Section 5.5
"""

import logging
import math
from typing import Dict, List, Tuple, Optional, Union, Any

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F

# Configure logging
logger = logging.getLogger(__name__)


class MultiScaleKnowledgeEmbedding(nn.Module):
    """
    Multi-Scale Knowledge Embedding that represents information at multiple abstraction levels.
    
    This module enables the representation of knowledge at different scales of abstraction,
    from detailed token-level information to broader conceptual and semantic levels. It
    implements bidirectional flow of information between scales and weighted combination
    for the final representation.
    
    The mathematical formulation includes:
    1. Scale-specific projections: E^l = P_l(E)
    2. Bidirectional integrations: E^{l+1} += P_up(E^l), E^{l-1} += P_down(E^l)
    3. Weighted combination: E_final = ∑_{l=1}^{L} w_l · E^l
    """
    
    def __init__(
        self,
        embedding_dim: int,
        num_scales: int = 3,
        vocab_size: Optional[int] = None,
        max_sequence_length: int = 8192,
        scale_factor: int = 2,
        use_context_integration: bool = True,
        dropout: float = 0.1,
        layer_norm_eps: float = 1e-5,
        device: Optional[torch.device] = None,
        dtype: Optional[torch.dtype] = None,
    ):
        """
        Initialize the Multi-Scale Knowledge Embedding.
        
        Args:
            embedding_dim: Model dimension (must be divisible by num_scales)
            num_scales: Number of abstraction scales
            vocab_size: Vocabulary size for token embeddings (optional)
            max_sequence_length: Maximum sequence length
            scale_factor: Factor by which to reduce sequence length at each scale
            use_context_integration: Whether to use context integration via attention
            dropout: Dropout probability
            layer_norm_eps: Epsilon for layer normalization
            device: Device to use
            dtype: Data type to use
        """
        factory_kwargs = {'device': device, 'dtype': dtype}
        super().__init__()
        
        # Ensure embedding_dim is divisible by num_scales
        if embedding_dim % num_scales != 0:
            raise ValueError(f"embedding_dim ({embedding_dim}) must be divisible by num_scales ({num_scales})")
        
        self.embedding_dim = embedding_dim
        self.num_scales = num_scales
        self.scale_dim = embedding_dim // num_scales  # Dimension per scale
        self.vocab_size = vocab_size
        self.max_sequence_length = max_sequence_length
        self.scale_factor = scale_factor
        self.use_context_integration = use_context_integration
        self.dropout = dropout
        
        # Create token embeddings if vocab_size is provided
        if vocab_size is not None:
            self.token_embedding = nn.Embedding(
                vocab_size, embedding_dim, **factory_kwargs
            )
        else:
            self.token_embedding = None
        
        # Scale-specific projections for each level of abstraction
        # Implements E^l = P_l(E) from the mathematical formulation
        self.scale_projections = nn.ModuleList([
            nn.Linear(embedding_dim, self.scale_dim, **factory_kwargs)
            for _ in range(num_scales)
        ])
        
        # Layer normalization for each scale
        self.scale_norms = nn.ModuleList([
            nn.LayerNorm(self.scale_dim, eps=layer_norm_eps, **factory_kwargs)
            for _ in range(num_scales)
        ])
        
        # Positional encoding
        self.register_buffer(
            'positional_encoding',
            self._create_positional_encoding(
                max_sequence_length, 
                embedding_dim, 
                device=factory_kwargs.get('device'),
                dtype=factory_kwargs.get('dtype', torch.float32)
            )
        )
        
        # Scale weights (for combining scales)
        self.scale_weights = nn.Parameter(
            torch.ones(num_scales, device=factory_kwargs.get('device'), dtype=factory_kwargs.get('dtype', torch.float32)) / num_scales
        )
        
        # Scale-specific transformations (feedforward networks)
        self.scale_transforms = nn.ModuleList([
            nn.Sequential(
                nn.Linear(self.scale_dim, self.scale_dim * 4, **factory_kwargs),
                nn.GELU(),
                nn.Dropout(dropout),
                nn.Linear(self.scale_dim * 4, self.scale_dim, **factory_kwargs),
                nn.Dropout(dropout)
            ) for _ in range(num_scales)
        ])
        
        # Upward projections between scales (from detailed to abstract)
        # Implements E^{l+1} = P_up(E^l) from the mathematical formulation
        self.upward_projections = nn.ModuleList([
            nn.Linear(self.scale_dim, self.scale_dim, **factory_kwargs)
            for _ in range(num_scales - 1)
        ])
        
        # Downward projections between scales (from abstract to detailed)
        # Implements E^{l-1} = P_down(E^l) from the mathematical formulation
        self.downward_projections = nn.ModuleList([
            nn.Linear(self.scale_dim, self.scale_dim, **factory_kwargs)
            for _ in range(num_scales - 1)
        ])
        
        # Pooling layers for different scales
        self.pooling_factors = [scale_factor ** i for i in range(num_scales)]
        
        # Context integration with attention (optional)
        if use_context_integration:
            # Implements MS-Attention from the mathematical formulation
            self.context_integration = nn.ModuleList([
                nn.MultiheadAttention(
                    embed_dim=self.scale_dim,
                    num_heads=max(1, self.scale_dim // 64),  # Scale heads with dimension
                    dropout=dropout,
                    batch_first=True,
                    **factory_kwargs
                ) for _ in range(num_scales)
            ])
            
            # Pre-layer normalization for attention
            self.attention_norms = nn.ModuleList([
                nn.LayerNorm(self.scale_dim, eps=layer_norm_eps, **factory_kwargs)
                for _ in range(num_scales)
            ])
        
        # Final output projection and normalization
        self.output_projection = nn.Linear(embedding_dim, embedding_dim, **factory_kwargs)
        self.output_norm = nn.LayerNorm(embedding_dim, eps=layer_norm_eps, **factory_kwargs)
        self.output_dropout = nn.Dropout(dropout)
        
        # Gates for scale integration (for more controlled information flow)
        self.upward_gates = nn.ModuleList([
            nn.Sequential(
                nn.Linear(self.scale_dim * 2, self.scale_dim, **factory_kwargs),
                nn.Sigmoid()
            ) for _ in range(num_scales - 1)
        ])
        
        self.downward_gates = nn.ModuleList([
            nn.Sequential(
                nn.Linear(self.scale_dim * 2, self.scale_dim, **factory_kwargs),
                nn.Sigmoid()
            ) for _ in range(num_scales - 1)
        ])
        
        # Initialize parameters
        self._reset_parameters()
        
        logger.info(
            f"Multi-Scale Knowledge Embedding initialized with {num_scales} scales, "
            f"embedding dimension: {embedding_dim}, scale dimension: {self.scale_dim}"
        )
    
    def _reset_parameters(self):
        """Reset parameters using appropriate initialization methods."""
        # Initialize linear layers
        for module in self.modules():
            if isinstance(module, nn.Linear):
                # Use Xavier uniform initialization for linear layers
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
        
        # Initialize embeddings if present
        if self.token_embedding is not None:
            nn.init.normal_(self.token_embedding.weight, mean=0.0, std=0.02)
        
        # Initialize layer norm
        for module in self.modules():
            if isinstance(module, nn.LayerNorm):
                nn.init.ones_(module.weight)
                nn.init.zeros_(module.bias)
        
        # Initialize scale weights with equal values
        nn.init.constant_(self.scale_weights, 1.0 / self.num_scales)
    
    def _create_positional_encoding(
        self, 
        max_len: int, 
        d_model: int,
        device=None,
        dtype=torch.float32
    ) -> torch.Tensor:
        """
        Create positional encodings for sequence positions.
        
        Uses the sinusoidal position encoding from "Attention Is All You Need".
        
        Args:
            max_len: Maximum sequence length
            d_model: Model dimension
            device: Device to create tensors on
            dtype: Data type for tensors
            
        Returns:
            torch.Tensor: Positional encodings of shape (1, max_len, d_model)
        """
        
        pe = torch.zeros(max_len, d_model, device=device, dtype=dtype)
        position = torch.arange(0, max_len, device=device, dtype=dtype).unsqueeze(1)
        div_term = torch.exp(
            torch.arange(0, d_model, 2, device=device, dtype=dtype).float() * 
            (-math.log(10000.0) / d_model)
        )
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        
        return pe.unsqueeze(0)  # Add batch dimension
    
    def pool_sequence(
        self, 
        x: torch.Tensor, 
        pool_factor: int,
        attention_mask: Optional[torch.Tensor] = None
    ) -> Tuple[torch.Tensor, Optional[torch.Tensor]]:
        """
        Pool sequence by a specified factor, with optional attention mask handling.
        
        Args:
            x: Input tensor of shape (batch_size, seq_len, dim)
            pool_factor: Pooling factor
            attention_mask: Optional attention mask of shape (batch_size, seq_len)
            
        Returns:
            Tuple[torch.Tensor, Optional[torch.Tensor]]: 
                Pooled tensor and updated attention mask (if provided)
        """
        batch_size, seq_len, dim = x.shape
        
        # If pool_factor is 1, return as is
        if pool_factor == 1:
            return x, attention_mask
        
        # Calculate pooled sequence length
        pooled_len = math.ceil(seq_len / pool_factor)
        padded_len = pooled_len * pool_factor
        
        # Pad if necessary
        if padded_len > seq_len:
            padding = torch.zeros(
                batch_size, padded_len - seq_len, dim, 
                device=x.device, dtype=x.dtype
            )
            x = torch.cat([x, padding], dim=1)
            
            # Also pad attention mask if provided
            if attention_mask is not None:
                mask_padding = torch.zeros(
                    batch_size, padded_len - seq_len,
                    device=attention_mask.device, dtype=attention_mask.dtype
                )
                attention_mask = torch.cat([attention_mask, mask_padding], dim=1)
        
        # Reshape for pooling
        x = x.view(batch_size, pooled_len, pool_factor, dim)
        
        # Handle attention mask for pooling
        pooled_mask = None
        if attention_mask is not None:
            # Reshape mask to match x before pooling
            mask = attention_mask.view(batch_size, pooled_len, pool_factor)
            
            # Pool mask (consider a position valid if any corresponding position was valid)
            pooled_mask = (mask.sum(dim=2) > 0).float()
            
            # Use mask for weighted pooling of x
            # Add a small epsilon to avoid division by zero
            mask_expanded = mask.unsqueeze(-1).expand_as(x)
            safe_mask_sum = mask.sum(dim=2, keepdim=True).unsqueeze(-1).clamp(min=1e-9)
            x = (x * mask_expanded).sum(dim=2) / safe_mask_sum
        else:
            # Standard pooling without mask
            x = x.mean(dim=2)
        
        return x, pooled_mask
    
    def integrate_scales(
        self, 
        embeddings: List[torch.Tensor],
        masks: Optional[List[torch.Tensor]] = None
    ) -> List[torch.Tensor]:
        """
        Integrate information across scales (bidirectional).
        
        This implements the bidirectional flow in the mathematical formulation:
        E^{l+1} += P_up(E^l), E^{l-1} += P_down(E^l)
        
        Args:
            embeddings: List of embeddings at each scale
            masks: Optional list of attention masks for each scale
            
        Returns:
            List[torch.Tensor]: Integrated embeddings at each scale
        """
        batch_size = embeddings[0].shape[0]
        device = embeddings[0].device
        
        # Upward pass (from detailed to abstract)
        for i in range(self.num_scales - 1):
            # Project current scale
            upward_info = self.upward_projections[i](embeddings[i])
            
            # Get target scale info
            target_dim = embeddings[i+1].shape[1]
            
            # Handle sequence length difference (usually downsampling)
            if upward_info.shape[1] != target_dim:
                upward_info = self._adjust_sequence_length(upward_info, target_dim)
            
            # Compute gate for controlled information flow
            if hasattr(self, 'upward_gates'):
                # Concatenate source and target for gate computation
                gate_input = torch.cat([upward_info, embeddings[i+1]], dim=-1)
                gate = self.upward_gates[i](gate_input)
                
                # Apply gate to control information flow
                upward_contribution = gate * upward_info
            else:
                upward_contribution = upward_info
            
            # Add to the next scale
            embeddings[i+1] = embeddings[i+1] + upward_contribution
        
        # Downward pass (from abstract to detailed)
        for i in range(self.num_scales - 1, 0, -1):
            # Project current scale
            downward_info = self.downward_projections[i-1](embeddings[i])
            
            # Get target scale info
            target_dim = embeddings[i-1].shape[1]
            
            # Handle sequence length difference (usually upsampling)
            if downward_info.shape[1] != target_dim:
                downward_info = self._adjust_sequence_length(downward_info, target_dim)
            
            # Compute gate for controlled information flow
            if hasattr(self, 'downward_gates'):
                # Concatenate source and target for gate computation
                gate_input = torch.cat([downward_info, embeddings[i-1]], dim=-1)
                gate = self.downward_gates[i-1](gate_input)
                
                # Apply gate to control information flow
                downward_contribution = gate * downward_info
            else:
                downward_contribution = downward_info
            
            # Add to the previous scale
            embeddings[i-1] = embeddings[i-1] + downward_contribution
        
        return embeddings
    
    def _adjust_sequence_length(
        self, 
        x: torch.Tensor, 
        target_len: int
    ) -> torch.Tensor:
        """
        Adjust sequence length by upsampling or downsampling.
        
        Args:
            x: Input tensor of shape (batch_size, seq_len, dim)
            target_len: Target sequence length
            
        Returns:
            torch.Tensor: Adjusted tensor of shape (batch_size, target_len, dim)
        """
        current_len = x.shape[1]
        
        if current_len == target_len:
            return x
        elif current_len < target_len:
            return self._upsample(x, target_len)
        else:
            return self._downsample(x, target_len)
    
    def _upsample(
        self, 
        x: torch.Tensor, 
        target_len: int
    ) -> torch.Tensor:
        """
        Upsample a sequence to a target length.
        
        Args:
            x: Input tensor of shape (batch_size, seq_len, dim)
            target_len: Target sequence length
            
        Returns:
            torch.Tensor: Upsampled tensor of shape (batch_size, target_len, dim)
        """
        batch_size, seq_len, dim = x.shape
        
        # Faster path for integer scaling factors
        scale_factor = target_len / seq_len
        if scale_factor.is_integer() and scale_factor <= 16:
            # Use repeat for integer scaling factors (more efficient for small factors)
            scale_factor = int(scale_factor)
            x_expanded = x.unsqueeze(2).expand(batch_size, seq_len, scale_factor, dim)
            return x_expanded.reshape(batch_size, seq_len * scale_factor, dim)
        
        # Use interpolation for non-integer scaling factors or large factors
        x_reshaped = x.permute(0, 2, 1)  # (batch_size, dim, seq_len)
        x_upsampled = F.interpolate(
            x_reshaped, size=target_len, mode='linear', 
            align_corners=False if seq_len > 1 else None
        )
        return x_upsampled.permute(0, 2, 1)  # (batch_size, target_len, dim)
    
    def _downsample(
        self, 
        x: torch.Tensor, 
        target_len: int
    ) -> torch.Tensor:
        """
        Downsample a sequence to a target length.
        
        Args:
            x: Input tensor of shape (batch_size, seq_len, dim)
            target_len: Target sequence length
            
        Returns:
            torch.Tensor: Downsampled tensor of shape (batch_size, target_len, dim)
        """
        batch_size, seq_len, dim = x.shape
        
        # For integer factors, use strided pooling (more efficient)
        if seq_len % target_len == 0:
            stride = seq_len // target_len
            x_reshaped = x.reshape(batch_size, target_len, stride, dim)
            return x_reshaped.mean(dim=2)
        
        # For non-integer factors, use interpolation
        x_reshaped = x.permute(0, 2, 1)  # (batch_size, dim, seq_len)
        x_downsampled = F.interpolate(
            x_reshaped, size=target_len, mode='linear', 
            align_corners=False if seq_len > 1 else None
        )
        return x_downsampled.permute(0, 2, 1)  # (batch_size, target_len, dim)
    
    def apply_context_integration(
        self,
        embeddings: List[torch.Tensor],
        masks: Optional[List[torch.Tensor]] = None
    ) -> List[torch.Tensor]:
        """
        Apply context integration for each scale using self-attention.
        
        Implements the context integration component of the MS-Attention mechanism.
        
        Args:
            embeddings: List of embeddings at each scale
            masks: Optional list of attention masks for each scale
            
        Returns:
            List[torch.Tensor]: Context-integrated embeddings
        """
        if not self.use_context_integration:
            return embeddings
        
        # Process each scale with its own self-attention
        for i in range(self.num_scales):
            # Get the current scale embedding
            scale_emb = embeddings[i]
            
            # Apply pre-layer normalization
            normed_emb = self.attention_norms[i](scale_emb)
            
            # Prepare attention mask if provided
            attn_mask = None
            if masks is not None and i < len(masks) and masks[i] is not None:
                # Convert from [batch_size, seq_len] to attention mask format
                # 0 = masked position (don't attend), 1 = can attend
                attn_mask = masks[i]
                
                # Create the right format for MultiheadAttention
                # For key_padding_mask: True = position is masked (don't attend)
                attn_mask = (1 - attn_mask).bool()
            
            # Apply self-attention
            attended, _ = self.context_integration[i](
                query=normed_emb,
                key=normed_emb,
                value=normed_emb,
                key_padding_mask=attn_mask,
                need_weights=False
            )
            
            # Add residual connection
            embeddings[i] = scale_emb + attended
        
        return embeddings
    
    def forward(
        self,
        input_ids: Optional[torch.Tensor] = None,
        input_embeddings: Optional[torch.Tensor] = None,
        attention_mask: Optional[torch.Tensor] = None,
        position_ids: Optional[torch.Tensor] = None,
        return_all_scales: bool = False
    ) -> Union[torch.Tensor, Tuple[torch.Tensor, List[torch.Tensor]]]:
        """
        Forward pass for Multi-Scale Knowledge Embedding.
        
        This implements the full multi-scale knowledge embedding process:
        1. Project input to multiple scales
        2. Apply scale-specific transformations
        3. Integrate information across scales
        4. Apply context integration
        5. Combine scales with learned weights
        
        Args:
            input_ids: Token IDs [batch_size, seq_len]
            input_embeddings: Pre-computed embeddings [batch_size, seq_len, embedding_dim]
            attention_mask: Attention mask [batch_size, seq_len]
            position_ids: Position indices [batch_size, seq_len]
            return_all_scales: Whether to return embeddings for all scales
            
        Returns:
            torch.Tensor or Tuple[torch.Tensor, List[torch.Tensor]]:
                Combined embedding [batch_size, seq_len, embedding_dim]
                and optionally list of embeddings at each scale
        """
        # Get base embeddings
        if input_embeddings is not None:
            embeddings = input_embeddings
        elif input_ids is not None and self.token_embedding is not None:
            embeddings = self.token_embedding(input_ids)
        else:
            raise ValueError("Either input_ids or input_embeddings must be provided")
        
        batch_size, seq_len, _ = embeddings.shape
        device = embeddings.device
        
        # Add positional encoding
        if position_ids is None:
            positions = torch.arange(seq_len, device=device).expand(batch_size, seq_len)
        else:
            positions = position_ids
        
        # Get positional encodings (using pre-computed buffer)
        pos_enc = self.positional_encoding[:, :seq_len]
        
        # Add positional encoding to embeddings
        embeddings = embeddings + pos_enc
        
        # Create multi-scale embeddings
        scale_embeddings = []
        scale_masks = []
        
        # Project to different scales
        for i in range(self.num_scales):
            # Project to scale-specific space
            scale_embedding = self.scale_projections[i](embeddings)
            
            # Apply normalization
            scale_embedding = self.scale_norms[i](scale_embedding)
            
            # Apply pooling for this scale
            pool_factor = self.pooling_factors[i]
            
            # Pool the sequence and handle the attention mask
            scale_mask = None
            if attention_mask is not None:
                # Pool embedding and mask together
                pooled, pooled_mask = self.pool_sequence(
                    scale_embedding, pool_factor, attention_mask
                )
                scale_mask = pooled_mask
            else:
                pooled, _ = self.pool_sequence(scale_embedding, pool_factor)
            
            # Apply scale-specific transformation
            transformed = self.scale_transforms[i](pooled)
            
            # Store scale embedding and mask
            scale_embeddings.append(transformed)
            if scale_mask is not None:
                scale_masks.append(scale_mask)
        
        # Handle case where we have no masks
        if not scale_masks:
            scale_masks = None
        
        # Integrate information across scales
        integrated_embeddings = self.integrate_scales(scale_embeddings, scale_masks)
        
        # Apply context integration for each scale using self-attention
        if self.use_context_integration:
            integrated_embeddings = self.apply_context_integration(
                integrated_embeddings, scale_masks
            )
        
        # Combine scales with learned weights
        # First, normalize weights for stable combination
        scale_weights = F.softmax(self.scale_weights, dim=0)
        
        # Initialize combined embedding
        combined_embedding = torch.zeros(
            batch_size, seq_len, self.embedding_dim, 
            device=device, dtype=embeddings.dtype
        )
        
        # Add each scale's contribution
        for i in range(self.num_scales):
            # Get this scale's embedding
            scale_emb = integrated_embeddings[i]
            
            # Adjust to match original sequence length
            if scale_emb.shape[1] != seq_len:
                scale_emb = self._adjust_sequence_length(scale_emb, seq_len)
            
            # Add to combined embedding, in the appropriate slice of embedding_dim
            start_idx = i * self.scale_dim
            end_idx = (i + 1) * self.scale_dim
            combined_embedding[:, :, start_idx:end_idx] = scale_emb * scale_weights[i]
        
        # Apply final projection and normalization
        output = self.output_projection(combined_embedding)
        output = self.output_norm(output)
        output = self.output_dropout(output)
        
        if return_all_scales:
            return output, integrated_embeddings
        else:
            return output
    
    def process_text(
        self,
        input_ids: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        position_ids: Optional[torch.Tensor] = None,
        return_all_scales: bool = False
    ) -> Union[torch.Tensor, Tuple[torch.Tensor, List[torch.Tensor]]]:
        """
        Process text input and return multi-scale embeddings.
        
        Convenience wrapper around forward() for text input.
        
        Args:
            input_ids: Token IDs [batch_size, seq_len]
            attention_mask: Attention mask [batch_size, seq_len]
            position_ids: Position indices [batch_size, seq_len]
            return_all_scales: Whether to return embeddings for all scales
            
        Returns:
            torch.Tensor or Tuple[torch.Tensor, List[torch.Tensor]]:
                Combined embedding and optionally embeddings at each scale
        """
        return self.forward(
            input_ids=input_ids,
            attention_mask=attention_mask,
            position_ids=position_ids,
            return_all_scales=return_all_scales
        )
    
    def process_embeddings(
        self,
        embeddings: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        position_ids: Optional[torch.Tensor] = None,
        return_all_scales: bool = False
    ) -> Union[torch.Tensor, Tuple[torch.Tensor, List[torch.Tensor]]]:
        """
        Process pre-computed embeddings.
        
        Convenience wrapper around forward() for pre-computed embeddings.
        
        Args:
            embeddings: Pre-computed embeddings [batch_size, seq_len, embedding_dim]
            attention_mask: Attention mask [batch_size, seq_len]
            position_ids: Position indices [batch_size, seq_len]
            return_all_scales: Whether to return embeddings for all scales
            
        Returns:
            torch.Tensor or Tuple[torch.Tensor, List[torch.Tensor]]:
                Combined embedding and optionally embeddings at each scale
        """
        return self.forward(
            input_embeddings=embeddings,
            attention_mask=attention_mask,
            position_ids=position_ids,
            return_all_scales=return_all_scales
        )
    
    def get_scale_weights(self) -> torch.Tensor:
        """
        Get the normalized weights for each scale.
        
        Returns:
            torch.Tensor: Normalized scale weights
        """
        return F.softmax(self.scale_weights, dim=0)
    
    def set_scale_weights(
        self, 
        weights: torch.Tensor,
        normalize: bool = True
    ) -> None:
        """
        Set custom weights for each scale.
        
        Args:
            weights: Weights for each scale [num_scales]
            normalize: Whether to normalize weights
        """
        if weights.shape != self.scale_weights.shape:
            raise ValueError(
                f"Weights shape {weights.shape} doesn't match "
                f"scale_weights shape {self.scale_weights.shape}"
            )
        
        if normalize:
            # Apply softmax to ensure weights sum to 1
            weights = F.softmax(weights, dim=0)
            
            # Convert back to logits for the parameter
            # Add a small epsilon to avoid numerical issues
            weights = torch.log(weights + 1e-10)
        
        self.scale_weights.data.copy_(weights)
    
    def freeze_scales(
        self, 
        scales_to_freeze: Optional[List[int]] = None
    ) -> None:
        """
        Freeze parameters for specific scales.
        
        Args:
            scales_to_freeze: List of scale indices to freeze (if None, freeze all)
        """
        if scales_to_freeze is None:
            scales_to_freeze = list(range(self.num_scales))
        
        for i in scales_to_freeze:
            # Freeze scale-specific projections
            for param in self.scale_projections[i].parameters():
                param.requires_grad = False
            
            # Freeze scale-specific transformations
            for param in self.scale_transforms[i].parameters():
                param.requires_grad = False
            
            # Freeze scale-specific normalization
            for param in self.scale_norms[i].parameters():
                param.requires_grad = False
            
            # Freeze context integration if used
            if self.use_context_integration:
                for param in self.context_integration[i].parameters():
                    param.requires_grad = False
                for param in self.attention_norms[i].parameters():
                    param.requires_grad = False
            
            # Freeze upward and downward projections
            if i < self.num_scales - 1:
                for param in self.upward_projections[i].parameters():
                    param.requires_grad = False
                if hasattr(self, 'upward_gates'):
                    for param in self.upward_gates[i].parameters():
                        param.requires_grad = False
            
            if i > 0:
                for param in self.downward_projections[i-1].parameters():
                    param.requires_grad = False
                if hasattr(self, 'downward_gates'):
                    for param in self.downward_gates[i-1].parameters():
                        param.requires_grad = False
    
    def unfreeze_scales(
        self, 
        scales_to_unfreeze: Optional[List[int]] = None
    ) -> None:
        """
        Unfreeze parameters for specific scales.
        
        Args:
            scales_to_unfreeze: List of scale indices to unfreeze (if None, unfreeze all)
        """
        if scales_to_unfreeze is None:
            scales_to_unfreeze = list(range(self.num_scales))
        
        for i in scales_to_unfreeze:
            # Unfreeze scale-specific projections
            for param in self.scale_projections[i].parameters():
                param.requires_grad = True
            
            # Unfreeze scale-specific transformations
            for param in self.scale_transforms[i].parameters():
                param.requires_grad = True
            
            # Unfreeze scale-specific normalization
            for param in self.scale_norms[i].parameters():
                param.requires_grad = True
            
            # Unfreeze context integration if used
            if self.use_context_integration:
                for param in self.context_integration[i].parameters():
                    param.requires_grad = True
                for param in self.attention_norms[i].parameters():
                    param.requires_grad = True
            
            # Unfreeze upward and downward projections
            if i < self.num_scales - 1:
                for param in self.upward_projections[i].parameters():
                    param.requires_grad = True
                if hasattr(self, 'upward_gates'):
                    for param in self.upward_gates[i].parameters():
                        param.requires_grad = True
            
            if i > 0:
                for param in self.downward_projections[i-1].parameters():
                    param.requires_grad = True
                if hasattr(self, 'downward_gates'):
                    for param in self.downward_gates[i-1].parameters():
                        param.requires_grad = True
    
    def extra_repr(self) -> str:
        """Return extra information string."""
        return (f"embedding_dim={self.embedding_dim}, num_scales={self.num_scales}, "
                f"scale_dim={self.scale_dim}, scale_factor={self.scale_factor}")