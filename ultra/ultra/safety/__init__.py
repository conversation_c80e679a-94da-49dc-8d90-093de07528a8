#!/usr/bin/env python3
"""
ULTRA Safety Module: Implements safety monitoring, constraints, and ethical guidelines for ULTRA.

The safety module is responsible for ensuring that ULTRA operates within safe parameters,
adheres to ethical guidelines, and protects against potential harmful actions or states.
It integrates system monitoring, constraint enforcement, and ethical frameworks.
"""

import os
import sys
import time
import logging
import numpy as np
import threading
import inspect
from typing import Dict, List, Set, Tuple, Any, Callable, Optional, Union
from dataclasses import dataclass, field
from concurrent.futures import ThreadPoolExecutor
from functools import wraps
import json
import re

# Set up logging
logger = logging.getLogger("ultra.safety")
logger.setLevel(logging.INFO)
if not logger.handlers:
    handler = logging.StreamHandler(sys.stdout)
    handler.setFormatter(logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    ))
    logger.addHandler(handler)

# Constants for safety thresholds
DEFAULT_SAFETY_THRESHOLDS = {
    "confidence_threshold": 0.95,       # Minimum confidence for critical decisions
    "uncertainty_threshold": 0.1,       # Maximum uncertainty for critical decisions
    "max_modification_impact": 0.25,    # Maximum allowed impact of self-modifications
    "min_safety_score": 0.8,            # Minimum safety score for actions
    "max_resource_usage": 0.9,          # Maximum resource usage as fraction of available
    "bias_detection_threshold": 0.3,    # Threshold for bias detection alerts
    "privacy_violation_threshold": 0.2, # Threshold for privacy violation alerts
    "max_divergence": 0.5,              # Maximum allowed divergence from expected behavior
}

# Define Safety Constraint Levels
class ConstraintLevel:
    ADVISORY = "ADVISORY"        # Suggestions but no enforced restrictions
    WARNING = "WARNING"          # Triggers alerts but allows continuation
    RESTRICTIVE = "RESTRICTIVE"  # Enforces constraints but allows overrides
    CRITICAL = "CRITICAL"        # Enforces hard constraints with no overrides

# Import required components
try:
    from ultra.monitoring import (
        SystemMonitor, ResourceMonitor, BehaviorMonitor, 
        ActivityTracker, AnomalyDetector
    )
    from ultra.constraints import (
        ConstraintManager, SafetyConstraint, EthicalConstraint,
        ResourceConstraint, SecurityConstraint, PrivacyConstraint
    )
    from ultra.ethical_framework import (
        EthicalFramework, EthicalRules, TransparencyManager,
        FairnessMetrics, BiasDetector, ExplainabilityEngine
    )
    _SAFETY_IMPORTS_SUCCESSFUL = True
except ImportError as e:
    logger.warning(f"Some safety components could not be imported: {e}")
    _SAFETY_IMPORTS_SUCCESSFUL = False


@dataclass
class SafetyViolation:
    """Represents a safety violation detected by the system."""
    timestamp: float
    component: str
    severity: str
    description: str
    constraint_violated: Optional[str] = None
    context: Dict[str, Any] = field(default_factory=dict)
    mitigations: List[str] = field(default_factory=list)


class SafetyMetrics:
    """Collects and manages safety-related metrics."""
    
    def __init__(self):
        self.metrics = {
            "violations_by_severity": {"CRITICAL": 0, "HIGH": 0, "MEDIUM": 0, "LOW": 0},
            "violations_by_component": {},
            "constraints_triggered": {},
            "safety_scores": [],
            "timestamp_last_violation": 0,
            "total_checks": 0,
            "passed_checks": 0,
        }
        self.violations_history = []
        self._lock = threading.RLock()
    
    def record_violation(self, violation: SafetyViolation) -> None:
        """Record a safety violation and update metrics."""
        with self._lock:
            self.violations_history.append(violation)
            self.metrics["violations_by_severity"][violation.severity] = (
                self.metrics["violations_by_severity"].get(violation.severity, 0) + 1
            )
            self.metrics["violations_by_component"][violation.component] = (
                self.metrics["violations_by_component"].get(violation.component, 0) + 1
            )
            if violation.constraint_violated:
                self.metrics["constraints_triggered"][violation.constraint_violated] = (
                    self.metrics["constraints_triggered"].get(violation.constraint_violated, 0) + 1
                )
            self.metrics["timestamp_last_violation"] = violation.timestamp
    
    def record_safety_check(self, passed: bool, safety_score: float = None) -> None:
        """Record the result of a safety check."""
        with self._lock:
            self.metrics["total_checks"] += 1
            if passed:
                self.metrics["passed_checks"] += 1
            if safety_score is not None:
                self.metrics["safety_scores"].append(safety_score)
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get the current safety metrics."""
        with self._lock:
            metrics_copy = self.metrics.copy()
            if self.metrics["safety_scores"]:
                metrics_copy["avg_safety_score"] = np.mean(self.metrics["safety_scores"])
                metrics_copy["min_safety_score"] = min(self.metrics["safety_scores"])
                metrics_copy["max_safety_score"] = max(self.metrics["safety_scores"])
            metrics_copy["check_pass_rate"] = (
                self.metrics["passed_checks"] / max(1, self.metrics["total_checks"])
            )
            return metrics_copy
    
    def get_recent_violations(self, n: int = 10) -> List[SafetyViolation]:
        """Get the n most recent safety violations."""
        with self._lock:
            return sorted(
                self.violations_history,
                key=lambda v: v.timestamp,
                reverse=True
            )[:n]


class SafetyContext:
    """Maintains safety-related context during system execution."""
    
    def __init__(self):
        self.current_operation = None
        self.active_constraints = set()
        self.operation_stack = []
        self.allowed_overrides = set()
        self.risk_level = "NORMAL"
        self.safety_checks_enabled = True
        self.context_data = {}
        self._lock = threading.RLock()
    
    def enter_operation(self, operation_name: str, 
                       risk_level: str = None,
                       active_constraints: Set[str] = None) -> None:
        """Enter a new operation context."""
        with self._lock:
            self.operation_stack.append(self.current_operation)
            self.current_operation = operation_name
            
            if risk_level:
                self.risk_level = risk_level
            
            if active_constraints:
                self.active_constraints.update(active_constraints)
    
    def exit_operation(self) -> None:
        """Exit the current operation context."""
        with self._lock:
            if self.operation_stack:
                self.current_operation = self.operation_stack.pop()
            else:
                self.current_operation = None
    
    def add_context_data(self, key: str, value: Any) -> None:
        """Add data to the current context."""
        with self._lock:
            self.context_data[key] = value
    
    def get_context_data(self, key: str, default: Any = None) -> Any:
        """Get data from the current context."""
        with self._lock:
            return self.context_data.get(key, default)
    
    def allow_override(self, constraint_name: str) -> None:
        """Allow an override for a specific constraint."""
        with self._lock:
            self.allowed_overrides.add(constraint_name)
    
    def is_override_allowed(self, constraint_name: str) -> bool:
        """Check if an override is allowed for a specific constraint."""
        with self._lock:
            return constraint_name in self.allowed_overrides
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.exit_operation()
        return False  # Don't suppress exceptions


class SafetyVerifier:
    """Verifies system safety according to defined constraints and ethical guidelines."""
    
    def __init__(self, constraints=None, thresholds=None):
        self.constraints = constraints or {}
        self.thresholds = thresholds or DEFAULT_SAFETY_THRESHOLDS.copy()
        self.metrics = SafetyMetrics()
        self.context = SafetyContext()
        self._lock = threading.RLock()
        
        # Initialize component references that will be set later
        self.system_monitor = None
        self.constraint_manager = None
        self.ethical_framework = None
    
    def verify_modification(self, current_state: Any, proposed_modification: Any, 
                          constraint_set: Optional[List[str]] = None) -> Tuple[bool, float, List[str]]:
        """
        Verify if a proposed modification to the system state satisfies safety constraints.
        
        Using the formula from the ULTRA spec:
        Safe(m_i) = ⋀(j=1 to k) C_j(S⊕m_i)
        
        Args:
            current_state: The current system state
            proposed_modification: The proposed modification
            constraint_set: Optional set of specific constraints to check against
            
        Returns:
            Tuple containing:
            - Boolean indicating if the modification is safe
            - Safety score between 0 and 1
            - List of violated constraints
        """
        if not self.constraint_manager:
            logger.warning("Constraint manager not initialized in SafetyVerifier")
            return False, 0.0, ["Constraint manager not initialized"]
        
        # Select constraints to evaluate
        constraints_to_check = []
        if constraint_set:
            constraints_to_check = [
                c for c in self.constraint_manager.get_all_constraints()
                if c.name in constraint_set
            ]
        else:
            constraints_to_check = self.constraint_manager.get_all_constraints()
        
        if not constraints_to_check:
            logger.warning("No constraints available for verification")
            return False, 0.0, ["No constraints available"]
        
        # Compute the modified state
        try:
            # This is a simplified representation; in practice, this would depend
            # on the actual structure of states and modifications
            modified_state = self._apply_modification(current_state, proposed_modification)
        except Exception as e:
            logger.error(f"Error applying modification: {e}")
            return False, 0.0, [f"Error applying modification: {e}"]
        
        # Evaluate each constraint
        violated_constraints = []
        constraint_scores = []
        
        for constraint in constraints_to_check:
            try:
                # Check if constraint is satisfied
                is_satisfied, score = constraint.check(modified_state)
                constraint_scores.append(score)
                
                if not is_satisfied:
                    violated_constraints.append(constraint.name)
                    
                    # Critical constraints can immediately fail the verification
                    if constraint.level == ConstraintLevel.CRITICAL:
                        logger.warning(f"Critical constraint violated: {constraint.name}")
                        self.metrics.record_safety_check(
                            passed=False, 
                            safety_score=min(constraint_scores) if constraint_scores else 0.0
                        )
                        return False, min(constraint_scores) if constraint_scores else 0.0, violated_constraints
            except Exception as e:
                logger.error(f"Error evaluating constraint {constraint.name}: {e}")
                violated_constraints.append(f"{constraint.name} (evaluation error)")
        
        # Compute overall safety score
        safety_score = np.mean(constraint_scores) if constraint_scores else 0.0
        
        # Check if any non-critical constraints were violated
        is_safe = not violated_constraints
        
        # Record the safety check result
        self.metrics.record_safety_check(passed=is_safe, safety_score=safety_score)
        
        return is_safe, safety_score, violated_constraints
    
    def _apply_modification(self, current_state: Any, modification: Any) -> Any:
        """
        Apply a modification to the current state to produce a new state.
        This is a simplified implementation and would need to be adapted to the actual state representation.
        """
        # This is a placeholder implementation
        # In a real system, this would depend on the structure of the state and modification
        if hasattr(current_state, 'copy'):
            new_state = current_state.copy()
        elif isinstance(current_state, dict):
            new_state = current_state.copy()
            if isinstance(modification, dict):
                new_state.update(modification)
        elif isinstance(current_state, np.ndarray):
            new_state = current_state.copy()
            if isinstance(modification, np.ndarray) and modification.shape == current_state.shape:
                new_state += modification
        else:
            # Fallback for simple types or custom objects without copy methods
            # This is oversimplified and would need to be replaced with proper implementation
            new_state = modification
        
        return new_state
    
    def verify_action(self, action: Any, context: Dict[str, Any] = None) -> Tuple[bool, float, List[str]]:
        """
        Verify if a proposed action is safe according to constraints and ethical guidelines.
        
        Args:
            action: The action to be verified
            context: Additional context information for verification
            
        Returns:
            Tuple containing:
            - Boolean indicating if the action is safe
            - Safety score between 0 and 1
            - List of violated constraints or guidelines
        """
        if not self.constraint_manager or not self.ethical_framework:
            logger.warning("Required components not initialized in SafetyVerifier")
            return False, 0.0, ["Required components not initialized"]
        
        context = context or {}
        violations = []
        scores = []
        
        # Check against safety constraints
        constraints = self.constraint_manager.get_relevant_constraints(action, context)
        for constraint in constraints:
            try:
                is_satisfied, score = constraint.check_action(action, context)
                scores.append(score)
                if not is_satisfied:
                    violations.append(f"Constraint: {constraint.name}")
                    
                    # Critical constraints can immediately fail the verification
                    if constraint.level == ConstraintLevel.CRITICAL:
                        self.metrics.record_safety_check(passed=False, safety_score=score)
                        return False, score, violations
            except Exception as e:
                logger.error(f"Error checking constraint {constraint.name}: {e}")
                violations.append(f"Constraint: {constraint.name} (evaluation error)")
        
        # Check against ethical guidelines
        ethical_rules = self.ethical_framework.get_relevant_rules(action, context)
        for rule in ethical_rules:
            try:
                is_satisfied, score = rule.evaluate(action, context)
                scores.append(score)
                if not is_satisfied:
                    violations.append(f"Ethical rule: {rule.name}")
            except Exception as e:
                logger.error(f"Error evaluating ethical rule {rule.name}: {e}")
                violations.append(f"Ethical rule: {rule.name} (evaluation error)")
        
        # Check for bias
        if hasattr(self.ethical_framework, 'bias_detector'):
            try:
                bias_detected, bias_score = self.ethical_framework.bias_detector.detect_bias(action, context)
                if bias_detected:
                    bias_level = 1.0 - bias_score  # Convert bias measure to safety score
                    scores.append(bias_level)
                    if bias_level < self.thresholds.get("bias_detection_threshold", 0.3):
                        violations.append("Potential bias detected")
            except Exception as e:
                logger.error(f"Error checking for bias: {e}")
        
        # Calculate overall safety score
        safety_score = np.mean(scores) if scores else 0.0
        
        # Determine if action is safe
        is_safe = len(violations) == 0
        
        # Record safety check
        self.metrics.record_safety_check(passed=is_safe, safety_score=safety_score)
        
        return is_safe, safety_score, violations
    
    def verify_state(self, state: Any, context: Dict[str, Any] = None) -> Tuple[bool, float, List[str]]:
        """
        Verify if a system state is safe according to constraints.
        
        Args:
            state: The system state to be verified
            context: Additional context information for verification
            
        Returns:
            Tuple containing:
            - Boolean indicating if the state is safe
            - Safety score between 0 and 1
            - List of violated constraints
        """
        if not self.constraint_manager:
            logger.warning("Constraint manager not initialized in SafetyVerifier")
            return False, 0.0, ["Constraint manager not initialized"]
        
        context = context or {}
        violations = []
        scores = []
        
        # Check against safety constraints
        constraints = self.constraint_manager.get_all_constraints()
        for constraint in constraints:
            try:
                if hasattr(constraint, 'check_state'):
                    is_satisfied, score = constraint.check_state(state, context)
                    scores.append(score)
                    if not is_satisfied:
                        violations.append(f"Constraint: {constraint.name}")
                        
                        # Critical constraints can immediately fail the verification
                        if constraint.level == ConstraintLevel.CRITICAL:
                            self.metrics.record_safety_check(passed=False, safety_score=score)
                            return False, score, violations
            except Exception as e:
                logger.error(f"Error checking state against constraint {constraint.name}: {e}")
                violations.append(f"Constraint: {constraint.name} (evaluation error)")
        
        # Calculate overall safety score
        safety_score = np.mean(scores) if scores else 0.0
        
        # Determine if state is safe
        is_safe = len(violations) == 0
        
        # Record safety check
        self.metrics.record_safety_check(passed=is_safe, safety_score=safety_score)
        
        return is_safe, safety_score, violations
    
    def safe_execute(self, func: Callable, *args, **kwargs) -> Any:
        """
        Execute a function with safety verification before and after.
        
        Args:
            func: The function to execute
            *args, **kwargs: Arguments to pass to the function
            
        Returns:
            The result of the function execution if it's safe
            
        Raises:
            SafetyViolationError: If the execution would violate safety constraints
        """
        # Get function name and context
        func_name = getattr(func, "__name__", str(func))
        context = {
            "function": func_name,
            "args": args,
            "kwargs": kwargs,
            "caller": inspect.stack()[1].function if len(inspect.stack()) > 1 else "unknown",
            "timestamp": time.time()
        }
        
        # Enter operation context
        self.context.enter_operation(func_name)
        
        try:
            # Verify pre-execution
            if not self.context.safety_checks_enabled:
                logger.info(f"Safety checks disabled for {func_name}, proceeding without verification")
            else:
                # This is a simplified check - in practice, we'd need to predict the effect
                # of the function call, which might be non-trivial
                is_safe, score, violations = self.verify_action(
                    {"function": func_name, "args": args, "kwargs": kwargs},
                    context
                )
                
                if not is_safe:
                    violation = SafetyViolation(
                        timestamp=time.time(),
                        component=func_name,
                        severity="HIGH",
                        description=f"Safety verification failed for function {func_name}",
                        constraint_violated=violations[0] if violations else None,
                        context=context,
                        mitigations=["Function execution prevented"]
                    )
                    self.metrics.record_violation(violation)
                    raise SafetyViolationError(f"Safety verification failed: {violations}")
            
            # Execute function
            result = func(*args, **kwargs)
            
            # Verify post-execution if checks are enabled
            if self.context.safety_checks_enabled and self.system_monitor:
                # Check system state after execution
                try:
                    state = self.system_monitor.get_current_state()
                    is_safe_post, score_post, violations_post = self.verify_state(state, context)
                    
                    if not is_safe_post:
                        # Log violation but don't raise an exception since the function has already executed
                        violation = SafetyViolation(
                            timestamp=time.time(),
                            component=func_name,
                            severity="MEDIUM",
                            description=f"Post-execution state verification failed for {func_name}",
                            constraint_violated=violations_post[0] if violations_post else None,
                            context=context,
                            mitigations=["Logged for review", "Potential mitigation actions"]
                        )
                        self.metrics.record_violation(violation)
                        logger.warning(f"Post-execution state invalid after {func_name}: {violations_post}")
                except Exception as e:
                    logger.error(f"Error in post-execution verification: {e}")
            
            return result
            
        finally:
            # Exit operation context
            self.context.exit_operation()
    
    def check_constraint(self, constraint_name: str, subject: Any, context: Dict[str, Any] = None) -> Tuple[bool, float]:
        """
        Check a specific constraint against a subject.
        
        Args:
            constraint_name: Name of the constraint to check
            subject: The subject to check the constraint against
            context: Additional context for the check
            
        Returns:
            Tuple of (is_satisfied, confidence_score)
        """
        if not self.constraint_manager:
            logger.warning("Constraint manager not initialized in SafetyVerifier")
            return False, 0.0
        
        constraint = self.constraint_manager.get_constraint(constraint_name)
        if not constraint:
            logger.warning(f"Constraint {constraint_name} not found")
            return False, 0.0
        
        try:
            is_satisfied, score = constraint.check(subject, context or {})
            return is_satisfied, score
        except Exception as e:
            logger.error(f"Error checking constraint {constraint_name}: {e}")
            return False, 0.0


class SafetyViolationError(Exception):
    """Exception raised when a safety constraint is violated."""
    pass


class SafetyManager:
    """
    Central manager for all safety-related functionality in ULTRA.
    
    This class coordinates the different safety components:
    - SystemMonitor for monitoring system state and behavior
    - ConstraintManager for enforcing safety constraints
    - EthicalFramework for applying ethical guidelines
    - SafetyVerifier for verifying safety of actions and modifications
    """
    
    def __init__(self, config_path=None):
        """
        Initialize the SafetyManager.
        
        Args:
            config_path: Optional path to a safety configuration file
        """
        self.config = self._load_config(config_path)
        self.thresholds = self.config.get("thresholds", DEFAULT_SAFETY_THRESHOLDS.copy())
        self.active = True
        self.initialized = False
        self._lock = threading.RLock()
        
        # Initialize metrics
        self.metrics = SafetyMetrics()
        
        # Create context for safety operations
        self.context = SafetyContext()
        
        # Initialize safety verifier
        self.verifier = SafetyVerifier(thresholds=self.thresholds)
        
        # Initialize monitor, constraint manager, and ethical framework 
        # (will be properly set up in initialize())
        self.system_monitor = None
        self.constraint_manager = None
        self.ethical_framework = None
        
        # Initialize violation handlers
        self.violation_handlers = {}
        
        # Thread pool for asynchronous safety checks
        self.thread_pool = ThreadPoolExecutor(
            max_workers=self.config.get("max_check_workers", 4),
            thread_name_prefix="safety_check_"
        )
    
    def _load_config(self, config_path=None) -> Dict[str, Any]:
        """
        Load safety configuration from a file or use defaults.
        
        Args:
            config_path: Path to a JSON or YAML configuration file
            
        Returns:
            Dictionary containing safety configuration
        """
        config = {"thresholds": DEFAULT_SAFETY_THRESHOLDS.copy()}
        
        if config_path and os.path.exists(config_path):
            try:
                # Determine file type by extension
                if config_path.endswith('.json'):
                    with open(config_path, 'r') as f:
                        loaded_config = json.load(f)
                elif config_path.endswith(('.yaml', '.yml')):
                    try:
                        import yaml
                        with open(config_path, 'r') as f:
                            loaded_config = yaml.safe_load(f)
                    except ImportError:
                        logger.warning("PyYAML not installed, falling back to default config")
                        return config
                else:
                    logger.warning(f"Unsupported config file format: {config_path}")
                    return config
                
                # Update default config with loaded values
                if isinstance(loaded_config, dict):
                    # Merge thresholds
                    if "thresholds" in loaded_config and isinstance(loaded_config["thresholds"], dict):
                        config["thresholds"].update(loaded_config["thresholds"])
                    
                    # Copy other config sections
                    for key, value in loaded_config.items():
                        if key != "thresholds":
                            config[key] = value
                
            except Exception as e:
                logger.error(f"Error loading safety config from {config_path}: {e}")
        
        return config
    
    def initialize(self) -> bool:
        """
        Initialize all safety components.
        
        Returns:
            True if initialization was successful, False otherwise
        """
        with self._lock:
            if self.initialized:
                logger.info("Safety Manager already initialized")
                return True
            
            try:
                # Initialize system monitoring
                monitor_config = self.config.get("monitoring", {})
                self.system_monitor = SystemMonitor(monitor_config)
                
                # Initialize resource monitoring
                resource_monitor = ResourceMonitor(
                    check_interval=monitor_config.get("resource_check_interval", 10)
                )
                self.system_monitor.add_monitor(resource_monitor)
                
                # Initialize behavior monitoring
                behavior_monitor = BehaviorMonitor(
                    expected_behavior_model=monitor_config.get("expected_behavior_model")
                )
                self.system_monitor.add_monitor(behavior_monitor)
                
                # Initialize anomaly detection
                anomaly_detector = AnomalyDetector(
                    sensitivity=monitor_config.get("anomaly_sensitivity", 0.8)
                )
                self.system_monitor.add_monitor(anomaly_detector)
                
                # Initialize constraint manager
                constraint_config = self.config.get("constraints", {})
                self.constraint_manager = ConstraintManager(
                    default_constraints_path=constraint_config.get("default_constraints_path")
                )
                
                # Load default constraints
                self._load_default_constraints()
                
                # Initialize ethical framework
                ethics_config = self.config.get("ethics", {})
                self.ethical_framework = EthicalFramework(
                    rules_path=ethics_config.get("rules_path")
                )
                
                # Set up explainability engine
                explainability_engine = ExplainabilityEngine(
                    explanation_templates=ethics_config.get("explanation_templates", {})
                )
                self.ethical_framework.set_explainability_engine(explainability_engine)
                
                # Set up bias detector
                bias_detector = BiasDetector(
                    bias_detection_models=ethics_config.get("bias_detection_models", {})
                )
                self.ethical_framework.set_bias_detector(bias_detector)
                
                # Connect components to the verifier
                self.verifier.system_monitor = self.system_monitor
                self.verifier.constraint_manager = self.constraint_manager
                self.verifier.ethical_framework = self.ethical_framework
                
                # Set up violation handlers
                self._setup_violation_handlers()
                
                # Start monitoring
                self.system_monitor.start()
                
                self.initialized = True
                logger.info("Safety Manager successfully initialized")
                return True
                
            except Exception as e:
                logger.error(f"Error initializing Safety Manager: {e}")
                return False
    
    def _load_default_constraints(self) -> None:
        """Load default safety constraints into the constraint manager."""
        try:
            # System resource constraints
            self.constraint_manager.add_constraint(
                ResourceConstraint(
                    name="memory_usage_limit",
                    description="Limit system memory usage",
                    level=ConstraintLevel.RESTRICTIVE,
                    resource_type="memory",
                    threshold=self.thresholds.get("max_resource_usage", 0.9),
                    constraint_func=lambda state, ctx: (
                        state.get("memory_usage", 1.0) <= self.thresholds.get("max_resource_usage", 0.9),
                        1.0 - state.get("memory_usage", 0.0)
                    )
                )
            )
            
            self.constraint_manager.add_constraint(
                ResourceConstraint(
                    name="cpu_usage_limit",
                    description="Limit system CPU usage",
                    level=ConstraintLevel.WARNING,
                    resource_type="cpu",
                    threshold=self.thresholds.get("max_resource_usage", 0.9),
                    constraint_func=lambda state, ctx: (
                        state.get("cpu_usage", 1.0) <= self.thresholds.get("max_resource_usage", 0.9),
                        1.0 - state.get("cpu_usage", 0.0)
                    )
                )
            )
            
            # Safety constraints for self-modification
            self.constraint_manager.add_constraint(
                SafetyConstraint(
                    name="self_modification_impact",
                    description="Limit the impact of self-modifications",
                    level=ConstraintLevel.CRITICAL,
                    constraint_func=lambda state, ctx: (
                        state.get("modification_impact", 1.0) <= self.thresholds.get("max_modification_impact", 0.25),
                        1.0 - (state.get("modification_impact", 0.0) / self.thresholds.get("max_modification_impact", 0.25))
                    )
                )
            )
            
            self.constraint_manager.add_constraint(
                SafetyConstraint(
                    name="uncertainty_threshold",
                    description="Ensure uncertainty is below threshold for critical decisions",
                    level=ConstraintLevel.CRITICAL,
                    constraint_func=lambda state, ctx: (
                        state.get("uncertainty", 1.0) <= self.thresholds.get("uncertainty_threshold", 0.1),
                        1.0 - (state.get("uncertainty", 0.0) / self.thresholds.get("uncertainty_threshold", 0.1))
                    )
                )
            )
            
            # Ethical constraints
            self.constraint_manager.add_constraint(
                EthicalConstraint(
                    name="prevent_harmful_actions",
                    description="Prevent actions that could cause harm",
                    level=ConstraintLevel.CRITICAL,
                    constraint_func=lambda state, ctx: (
                        state.get("harm_potential", 1.0) <= 0.2,
                        1.0 - state.get("harm_potential", 0.0)
                    )
                )
            )
            
            # Privacy constraints
            self.constraint_manager.add_constraint(
                PrivacyConstraint(
                    name="data_privacy_protection",
                    description="Protect privacy of user data",
                    level=ConstraintLevel.RESTRICTIVE,
                    constraint_func=lambda state, ctx: (
                        state.get("privacy_risk", 1.0) <= self.thresholds.get("privacy_violation_threshold", 0.2),
                        1.0 - (state.get("privacy_risk", 0.0) / self.thresholds.get("privacy_violation_threshold", 0.2))
                    )
                )
            )
            
            # Security constraints
            self.constraint_manager.add_constraint(
                SecurityConstraint(
                    name="prevent_unauthorized_access",
                    description="Prevent unauthorized access to system resources",
                    level=ConstraintLevel.CRITICAL,
                    constraint_func=lambda state, ctx: (
                        state.get("unauthorized_access_risk", 1.0) <= 0.1,
                        1.0 - state.get("unauthorized_access_risk", 0.0)
                    )
                )
            )
            
            logger.info(f"Loaded {self.constraint_manager.count_constraints()} default constraints")
            
        except Exception as e:
            logger.error(f"Error loading default constraints: {e}")
    
    def _setup_violation_handlers(self) -> None:
        """Set up handlers for different types of safety violations."""
        # Handler for resource constraint violations
        self.register_violation_handler(
            constraint_type="ResourceConstraint",
            handler=self._handle_resource_violation
        )
        
        # Handler for safety constraint violations
        self.register_violation_handler(
            constraint_type="SafetyConstraint",
            handler=self._handle_safety_violation
        )
        
        # Handler for ethical constraint violations
        self.register_violation_handler(
            constraint_type="EthicalConstraint",
            handler=self._handle_ethical_violation
        )
        
        # Handler for privacy constraint violations
        self.register_violation_handler(
            constraint_type="PrivacyConstraint",
            handler=self._handle_privacy_violation
        )
        
        # Handler for security constraint violations
        self.register_violation_handler(
            constraint_type="SecurityConstraint",
            handler=self._handle_security_violation
        )
    
    def _handle_resource_violation(self, violation: SafetyViolation) -> None:
        """Handle a resource constraint violation."""
        logger.warning(f"Resource constraint violated: {violation.description}")
        
        # Add resource management mitigations
        violation.mitigations.append("Reduce resource usage")
        violation.mitigations.append("Scale up resources if available")
        
        # Attempt to free resources automatically
        if violation.component == "memory_usage_limit":
            # Signal for garbage collection
            import gc
            gc.collect()
            violation.mitigations.append("Triggered garbage collection")
    
    def _handle_safety_violation(self, violation: SafetyViolation) -> None:
        """Handle a safety constraint violation."""
        logger.error(f"Safety constraint violated: {violation.description}")
        
        # Add safety mitigations
        violation.mitigations.append("Revert to safe state")
        violation.mitigations.append("Log full context for review")
        
        # Trigger safety fallback if available
        if hasattr(self, "trigger_safety_fallback"):
            try:
                self.trigger_safety_fallback(violation)
                violation.mitigations.append("Safety fallback triggered")
            except Exception as e:
                logger.error(f"Error triggering safety fallback: {e}")
    
    def _handle_ethical_violation(self, violation: SafetyViolation) -> None:
        """Handle an ethical constraint violation."""
        logger.error(f"Ethical constraint violated: {violation.description}")
        
        # Add ethical mitigations
        violation.mitigations.append("Review decision process")
        violation.mitigations.append("Apply ethical framework corrections")
        
        # Generate explanation for the violation
        if self.ethical_framework and hasattr(self.ethical_framework, "explainability_engine"):
            try:
                explanation = self.ethical_framework.explainability_engine.generate_explanation(
                    violation.context, violation.constraint_violated
                )
                violation.context["explanation"] = explanation
            except Exception as e:
                logger.error(f"Error generating explanation for ethical violation: {e}")
    
    def _handle_privacy_violation(self, violation: SafetyViolation) -> None:
        """Handle a privacy constraint violation."""
        logger.error(f"Privacy constraint violated: {violation.description}")
        
        # Add privacy mitigations
        violation.mitigations.append("Restrict data access")
        violation.mitigations.append("Apply additional privacy protections")
        
        # Log for compliance purposes
        try:
            # This would be implemented to log to a secure compliance log
            violation.mitigations.append("Logged to compliance record")
        except Exception as e:
            logger.error(f"Error logging privacy violation to compliance record: {e}")
    
    def _handle_security_violation(self, violation: SafetyViolation) -> None:
        """Handle a security constraint violation."""
        logger.error(f"Security constraint violated: {violation.description}")
        
        # Add security mitigations
        violation.mitigations.append("Block potentially malicious operation")
        violation.mitigations.append("Increase security monitoring")
        
        # Trigger security alert
        try:
            # This would be implemented to signal a security alert
            violation.mitigations.append("Security alert triggered")
        except Exception as e:
            logger.error(f"Error triggering security alert: {e}")
    
    def register_violation_handler(self, constraint_type: str, handler: Callable[[SafetyViolation], None]) -> None:
        """
        Register a handler for a specific type of constraint violation.
        
        Args:
            constraint_type: The type of constraint to handle violations for
            handler: A function that handles the violation
        """
        with self._lock:
            self.violation_handlers[constraint_type] = handler
    
    def handle_violation(self, violation: SafetyViolation) -> None:
        """
        Handle a safety violation by calling the appropriate handler.
        
        Args:
            violation: The safety violation to handle
        """
        # Record the violation in metrics
        self.metrics.record_violation(violation)
        
        # Determine the constraint type
        constraint_type = "unknown"
        if violation.constraint_violated:
            constraint = self.constraint_manager.get_constraint(violation.constraint_violated)
            if constraint:
                constraint_type = constraint.__class__.__name__
        
        # Call the appropriate handler
        handler = self.violation_handlers.get(constraint_type)
        if handler:
            try:
                handler(violation)
            except Exception as e:
                logger.error(f"Error in violation handler for {constraint_type}: {e}")
        else:
            # Default handling
            logger.warning(f"No handler for constraint type {constraint_type}, using default handling")
            logger.error(f"Safety violation: {violation.description} ({violation.severity})")
    
    def verify(self, subject: Any, constraint_set: List[str] = None, context: Dict[str, Any] = None) -> Tuple[bool, float, List[str]]:
        """
        Verify a subject against safety constraints.
        
        Args:
            subject: The subject to verify (can be an action, state, or modification)
            constraint_set: Optional list of specific constraint names to check
            context: Additional context for verification
            
        Returns:
            Tuple of (is_safe, safety_score, violated_constraints)
        """
        if not self.initialized:
            if not self.initialize():
                logger.error("Failed to initialize SafetyManager for verification")
                return False, 0.0, ["SafetyManager not initialized"]
        
        if not self.active:
            logger.warning("Safety verification bypassed because SafetyManager is inactive")
            return True, 1.0, []
        
        # Determine the type of subject and use the appropriate verification method
        if isinstance(subject, dict) and "action" in subject:
            # This is an action
            is_safe, score, violations = self.verifier.verify_action(subject, context)
        elif isinstance(subject, dict) and "modification" in subject and "current_state" in subject:
            # This is a modification
            is_safe, score, violations = self.verifier.verify_modification(
                subject["current_state"], subject["modification"], constraint_set
            )
        else:
            # Assume this is a state
            is_safe, score, violations = self.verifier.verify_state(subject, context)
        
        # Handle any violations
        if not is_safe and violations:
            violation = SafetyViolation(
                timestamp=time.time(),
                component=context.get("component", "unknown") if context else "unknown",
                severity="HIGH" if score < 0.3 else "MEDIUM",
                description=f"Safety verification failed: {violations[0]}",
                constraint_violated=violations[0] if violations else None,
                context=context or {},
                mitigations=[]
            )
            self.handle_violation(violation)
        
        return is_safe, score, violations
    
    def async_verify(self, subject: Any, constraint_set: List[str] = None, context: Dict[str, Any] = None, 
                   callback: Callable[[bool, float, List[str]], None] = None) -> None:
        """
        Asynchronously verify a subject against safety constraints.
        
        Args:
            subject: The subject to verify
            constraint_set: Optional list of specific constraint names to check
            context: Additional context for verification
            callback: Optional callback to be called with the result
        """
        def _verify_and_callback():
            try:
                result = self.verify(subject, constraint_set, context)
                if callback:
                    callback(*result)
            except Exception as e:
                logger.error(f"Error in async verification: {e}")
                if callback:
                    callback(False, 0.0, [f"Verification error: {e}"])
        
        # Submit the verification task to the thread pool
        self.thread_pool.submit(_verify_and_callback)
    
    def monitor(self, component_name: str = None) -> Any:
        """
        Create a decorator that monitors function calls for safety.
        
        Args:
            component_name: Optional name of the component being monitored
            
        Returns:
            A decorator that can be applied to functions
        """
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                # Create context for this function call
                context = {
                    "function": func.__name__,
                    "component": component_name or func.__module__,
                    "args": args,
                    "kwargs": kwargs,
                    "timestamp": time.time()
                }
                
                # Verify the function call is safe
                is_safe, score, violations = self.verify(
                    {"action": "function_call", "function": func.__name__},
                    context=context
                )
                
                if not is_safe:
                    # Handle unsafe function call
                    if self.config.get("block_unsafe_calls", True):
                        raise SafetyViolationError(f"Safety check failed for {func.__name__}: {violations}")
                    else:
                        logger.warning(f"Safety check failed for {func.__name__}, but proceeding: {violations}")
                
                # Execute the function
                result = func(*args, **kwargs)
                
                # Verify the result is safe
                try:
                    is_result_safe, result_score, result_violations = self.verify(
                        {"action": "function_result", "function": func.__name__, "result": result},
                        context=context
                    )
                    
                    if not is_result_safe:
                        logger.warning(f"Function {func.__name__} returned unsafe result: {result_violations}")
                except Exception as e:
                    logger.error(f"Error verifying result of {func.__name__}: {e}")
                
                return result
            
            return wrapper
        
        return decorator
    
    def check_constraint(self, constraint_name: str, subject: Any, context: Dict[str, Any] = None) -> Tuple[bool, float]:
        """
        Check a specific constraint against a subject.
        
        Args:
            constraint_name: Name of the constraint to check
            subject: The subject to check against
            context: Optional context for the check
            
        Returns:
            Tuple of (is_satisfied, confidence_score)
        """
        if not self.initialized:
            if not self.initialize():
                logger.error("Failed to initialize SafetyManager for constraint check")
                return False, 0.0
        
        return self.verifier.check_constraint(constraint_name, subject, context)
    
    def safe_execute(self, func: Callable, *args, **kwargs) -> Any:
        """
        Execute a function with safety verification before and after.
        
        Args:
            func: The function to execute
            *args, **kwargs: Arguments to pass to the function
            
        Returns:
            The result of the function execution if it's safe
            
        Raises:
            SafetyViolationError: If the execution would violate safety constraints
        """
        if not self.initialized:
            if not self.initialize():
                logger.error("Failed to initialize SafetyManager for safe execution")
                raise SafetyViolationError("SafetyManager not initialized")
        
        return self.verifier.safe_execute(func, *args, **kwargs)
    
    def get_safety_status(self) -> Dict[str, Any]:
        """
        Get the current safety status of the system.
        
        Returns:
            Dictionary containing safety metrics and status information
        """
        status = {
            "active": self.active,
            "initialized": self.initialized,
            "metrics": self.metrics.get_metrics(),
            "recent_violations": [
                {
                    "timestamp": v.timestamp,
                    "component": v.component,
                    "severity": v.severity,
                    "description": v.description,
                    "constraint": v.constraint_violated,
                    "mitigations": v.mitigations
                } 
                for v in self.metrics.get_recent_violations(5)
            ]
        }
        
        # Add system monitor status if available
        if self.system_monitor and hasattr(self.system_monitor, "get_status"):
            try:
                status["monitor_status"] = self.system_monitor.get_status()
            except Exception as e:
                logger.error(f"Error getting system monitor status: {e}")
                status["monitor_status"] = {"error": str(e)}
        
        return status
    
    def shutdown(self) -> None:
        """Shutdown the safety manager and its components."""
        with self._lock:
            if not self.initialized:
                return
            
            logger.info("Shutting down SafetyManager")
            
            # Stop the system monitor
            if self.system_monitor:
                try:
                    self.system_monitor.stop()
                except Exception as e:
                    logger.error(f"Error stopping system monitor: {e}")
            
            # Shutdown the thread pool
            try:
                self.thread_pool.shutdown(wait=True)
            except Exception as e:
                logger.error(f"Error shutting down thread pool: {e}")
            
            self.initialized = False
            logger.info("SafetyManager shutdown complete")


# Default global safety manager instance
safety_manager = SafetyManager()

# Safe decorator for functions
def safe(component_name=None):
    """
    Decorator to ensure functions are safe to execute.
    
    Args:
        component_name: Optional name of the component the function belongs to
        
    Returns:
        A decorator that wraps the function with safety checks
    """
    return safety_manager.monitor(component_name)

# Function to initialize safety system
def initialize_safety(config_path=None):
    """
    Initialize the safety system.
    
    Args:
        config_path: Optional path to a safety configuration file
        
    Returns:
        True if initialization was successful, False otherwise
    """
    # Create a new manager if the default one has been shut down
    global safety_manager
    if not safety_manager or not safety_manager.active:
        safety_manager = SafetyManager(config_path)
    elif config_path:
        # Use existing manager but update config
        safety_manager.config = safety_manager._load_config(config_path)
        safety_manager.thresholds = safety_manager.config.get("thresholds", DEFAULT_SAFETY_THRESHOLDS.copy())
    
    return safety_manager.initialize()

# Function to verify safety of an action, state, or modification
def verify_safety(subject, constraint_set=None, context=None):
    """
    Verify the safety of a subject.
    
    Args:
        subject: The subject to verify (action, state, or modification)
        constraint_set: Optional list of specific constraint names to check
        context: Additional context for verification
        
    Returns:
        Tuple of (is_safe, safety_score, violated_constraints)
    """
    return safety_manager.verify(subject, constraint_set, context)

# Function to handle a safety violation
def handle_safety_violation(component, description, severity="MEDIUM", constraint=None, context=None, mitigations=None):
    """
    Handle a safety violation.
    
    Args:
        component: Component where the violation occurred
        description: Description of the violation
        severity: Severity level (LOW, MEDIUM, HIGH, CRITICAL)
        constraint: Optional constraint that was violated
        context: Additional context for the violation
        mitigations: List of mitigation steps already taken
        
    Returns:
        SafetyViolation object representing the handled violation
    """
    violation = SafetyViolation(
        timestamp=time.time(),
        component=component,
        severity=severity,
        description=description,
        constraint_violated=constraint,
        context=context or {},
        mitigations=mitigations or []
    )
    
    safety_manager.handle_violation(violation)
    return violation

# Function to safely execute a function
def safe_execute(func, *args, **kwargs):
    """
    Execute a function with safety verification.
    
    Args:
        func: Function to execute
        *args, **kwargs: Arguments to pass to the function
        
    Returns:
        Result of the function if it's safe to execute
        
    Raises:
        SafetyViolationError: If execution would violate safety constraints
    """
    return safety_manager.safe_execute(func, *args, **kwargs)

# Function to get the current safety status
def get_safety_status():
    """
    Get the current safety status of the system.
    
    Returns:
        Dictionary containing safety metrics and status information
    """
    return safety_manager.get_safety_status()