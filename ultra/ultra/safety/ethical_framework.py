#!/usr/bin/env python3
"""
ULTRA Safety Ethical Framework Module: Implements the ethical framework for ULTRA.

This module defines the ethical framework, principles, rules, and evaluation methods
that guide the ULTRA system's ethical behavior. It includes components for bias detection,
fairness metrics, value alignment, explainability, and transparency. The ethical framework
ensures the system acts in accordance with established ethical principles and societal values.

The framework is based on a hybrid consequentialist-deontological model that combines 
outcome assessment with principle-based constraints, formalized using multi-dimensional
utility functions and rule-based reasoning.
"""

import os
import sys
import json
import yaml
import time
import logging
import inspect
import hashlib
import threading
import numpy as np
import warnings
import re
import traceback
import datetime
from enum import Enum
from typing import Dict, List, Set, Tuple, Any, Callable, Optional, Union, Type, TypeVar, Generic, NamedTuple
from dataclasses import dataclass, field, asdict
from concurrent.futures import ThreadPoolExecutor
from functools import wraps, lru_cache, partial
from abc import ABC, abstractmethod
from collections import defaultdict, Counter

# Import third-party libraries for specific capabilities
try:
    import sklearn
    from sklearn.metrics import confusion_matrix, classification_report
    from sklearn.metrics.pairwise import cosine_similarity
    from sklearn.preprocessing import StandardScaler
    _HAS_SKLEARN = True
except ImportError:
    _HAS_SKLEARN = False
    warnings.warn("scikit-learn not available; some fairness metrics will be limited")

try:
    import nltk
    from nltk.tokenize import word_tokenize
    from nltk.corpus import stopwords
    from nltk.stem import WordNetLemmatizer
    _HAS_NLTK = True
except ImportError:
    _HAS_NLTK = False
    warnings.warn("NLTK not available; some text analysis capabilities will be limited")
    
try:
    import networkx as nx
    _HAS_NETWORKX = True
except ImportError:
    _HAS_NETWORKX = False
    warnings.warn("NetworkX not available; some graph-based explainability features will be limited")

# Set up logging
logger = logging.getLogger("ultra.safety.ethical_framework")
logger.setLevel(logging.INFO)
if not logger.handlers:
    handler = logging.StreamHandler(sys.stdout)
    handler.setFormatter(logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    ))
    logger.addHandler(handler)


# Define ethical principle types as an Enum for type safety
class EthicalPrincipleType(str, Enum):
    """Defines the types of ethical principles in the framework."""
    BENEFICENCE = "BENEFICENCE"               # Promote wellbeing and benefit
    NON_MALEFICENCE = "NON_MALEFICENCE"       # Avoid harm
    AUTONOMY = "AUTONOMY"                     # Respect individual autonomy and agency
    JUSTICE = "JUSTICE"                       # Ensure fairness, equality, and non-discrimination
    TRANSPARENCY = "TRANSPARENCY"             # Provide clear explanations and visibility
    ACCOUNTABILITY = "ACCOUNTABILITY"         # Be answerable for actions and decisions
    PRIVACY = "PRIVACY"                       # Protect personal information and data
    RELIABILITY = "RELIABILITY"               # Ensure consistent, dependable operation
    HUMAN_OVERSIGHT = "HUMAN_OVERSIGHT"       # Maintain appropriate human control
    ROBUSTNESS = "ROBUSTNESS"                 # Be resilient against attacks and manipulation


class ValueAlignmentType(str, Enum):
    """Defines the types of value alignment considerations."""
    HUMAN_WELFARE = "HUMAN_WELFARE"            # Promote human wellbeing
    FREEDOM = "FREEDOM"                        # Protect human freedom and autonomy
    DIGNITY = "DIGNITY"                        # Respect human dignity
    FAIRNESS = "FAIRNESS"                      # Ensure fair and equitable treatment
    TRUST = "TRUST"                            # Build and maintain trustworthiness
    SUSTAINABILITY = "SUSTAINABILITY"          # Ensure long-term sustainability
    DIVERSITY = "DIVERSITY"                    # Value and respect diversity
    INCLUSIVITY = "INCLUSIVITY"                # Ensure inclusion of all stakeholders
    COOPERATION = "COOPERATION"                # Promote cooperation and collaboration
    PROGRESS = "PROGRESS"                      # Enable beneficial progress and innovation


@dataclass
class EthicalRuleMetadata:
    """Metadata for an ethical rule, used for organization and filtering."""
    principle: EthicalPrincipleType
    values: List[ValueAlignmentType] = field(default_factory=list)
    created_at: float = field(default_factory=time.time)
    modified_at: float = field(default_factory=time.time)
    author: str = field(default="system")
    version: str = field(default="1.0.0")
    source: str = field(default="internal")
    priority: int = field(default=0)  # Higher number means higher priority
    dependencies: List[str] = field(default_factory=list)  # Names of rules this one depends on
    references: List[str] = field(default_factory=list)  # References to supporting documentation, research, etc.
    legal_compliance: Dict[str, str] = field(default_factory=dict)  # Regulations/laws this rule helps comply with
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert metadata to a dictionary."""
        return {
            "principle": self.principle.value if isinstance(self.principle, EthicalPrincipleType) else self.principle,
            "values": [v.value if isinstance(v, ValueAlignmentType) else v for v in self.values],
            "created_at": self.created_at,
            "modified_at": self.modified_at,
            "author": self.author,
            "version": self.version,
            "source": self.source,
            "priority": self.priority,
            "dependencies": self.dependencies,
            "references": self.references,
            "legal_compliance": self.legal_compliance
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'EthicalRuleMetadata':
        """Create metadata from a dictionary."""
        # Convert principle string to enum if needed
        principle = data.get("principle", "NON_MALEFICENCE")
        if isinstance(principle, str):
            try:
                principle = EthicalPrincipleType(principle)
            except ValueError:
                principle = EthicalPrincipleType.NON_MALEFICENCE
                logger.warning(f"Invalid ethical principle '{principle}', defaulting to NON_MALEFICENCE")
        
        # Convert values strings to enums if needed
        values = data.get("values", [])
        processed_values = []
        for value in values:
            if isinstance(value, str):
                try:
                    processed_values.append(ValueAlignmentType(value))
                except ValueError:
                    logger.warning(f"Invalid value alignment type '{value}', skipping")
            else:
                processed_values.append(value)
        
        return cls(
            principle=principle,
            values=processed_values,
            created_at=data.get("created_at", time.time()),
            modified_at=data.get("modified_at", time.time()),
            author=data.get("author", "system"),
            version=data.get("version", "1.0.0"),
            source=data.get("source", "internal"),
            priority=data.get("priority", 0),
            dependencies=data.get("dependencies", []),
            references=data.get("references", []),
            legal_compliance=data.get("legal_compliance", {})
        )


class EthicalRule(ABC):
    """
    Abstract base class for all ethical rules in the ULTRA system.
    
    Ethical rules define the specific ethical guidelines that the system must follow.
    Each rule relates to a specific ethical principle and implements an evaluation
    method to determine if the rule is being followed.
    """
    
    def __init__(self, 
                name: str, 
                description: str,
                evaluation_func: Optional[Callable[[Any, Dict[str, Any]], Tuple[bool, float]]] = None,
                metadata: Optional[EthicalRuleMetadata] = None):
        """
        Initialize an ethical rule.
        
        Args:
            name: Unique identifier for the rule
            description: Human-readable description of the rule
            evaluation_func: Function that evaluates if the rule is satisfied
            metadata: Additional metadata for categorization and management
        """
        self.name = name
        self.description = description
        self.evaluation_func = evaluation_func
        self.metadata = metadata or EthicalRuleMetadata(
            principle=EthicalPrincipleType.NON_MALEFICENCE
        )
        self.last_evaluation_time = 0.0
        self.last_evaluation_result = (False, 0.0)
        self.evaluation_count = 0
        self.violation_count = 0
        self._lock = threading.RLock()  # For thread safety
    
    def evaluate(self, subject: Any, context: Dict[str, Any] = None) -> Tuple[bool, float]:
        """
        Evaluate if the ethical rule is satisfied by the subject.
        
        Args:
            subject: The subject to evaluate against the rule (can be action, decision, etc.)
            context: Additional context information for the evaluation
            
        Returns:
            Tuple of (is_satisfied, confidence_score)
            - is_satisfied: Boolean indicating if the rule is satisfied
            - confidence_score: Float between 0 and 1 indicating confidence in the evaluation
        """
        with self._lock:
            start_time = time.time()
            
            try:
                # If an evaluation function is provided in the constructor, it will be used.
                # Otherwise, use the evaluate_rule method that subclasses should implement.
                if self.evaluation_func is not None:
                    result = self.evaluation_func(subject, context or {})
                else:
                    result = self.evaluate_rule(subject, context or {})
                
                # Ensure result is properly formatted
                if isinstance(result, bool):
                    # If just a boolean is returned, assume high confidence if satisfied
                    is_satisfied = result
                    confidence = 1.0 if is_satisfied else 0.0
                elif isinstance(result, tuple) and len(result) == 2:
                    is_satisfied, confidence = result
                else:
                    logger.error(f"Ethical rule {self.name} returned invalid result format: {result}")
                    is_satisfied, confidence = False, 0.0
                
                # Update statistics
                self.evaluation_count += 1
                if not is_satisfied:
                    self.violation_count += 1
                
                self.last_evaluation_time = start_time
                self.last_evaluation_result = (is_satisfied, confidence)
                
                return is_satisfied, confidence
                
            except Exception as e:
                logger.error(f"Error evaluating ethical rule {self.name}: {e}")
                logger.debug(traceback.format_exc())
                
                # Rule evaluation errors default to violation with zero confidence
                self.evaluation_count += 1
                self.violation_count += 1
                self.last_evaluation_time = start_time
                self.last_evaluation_result = (False, 0.0)
                
                return False, 0.0
    
    @abstractmethod
    def evaluate_rule(self, subject: Any, context: Dict[str, Any]) -> Tuple[bool, float]:
        """
        Evaluate if the ethical rule is satisfied by the subject.
        
        This method should be implemented by subclasses to provide
        rule-specific evaluation logic.
        
        Args:
            subject: The subject to evaluate against the rule
            context: Additional context information for the evaluation
            
        Returns:
            Tuple of (is_satisfied, confidence_score)
        """
        raise NotImplementedError("Subclasses must implement evaluate_rule method")
    
    def get_principle(self) -> EthicalPrincipleType:
        """Get the ethical principle this rule is based on."""
        return self.metadata.principle
    
    def get_values(self) -> List[ValueAlignmentType]:
        """Get the values this rule aligns with."""
        return self.metadata.values.copy()
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the rule's usage and violations.
        
        Returns:
            Dictionary with rule statistics
        """
        with self._lock:
            return {
                "name": self.name,
                "principle": self.get_principle().value,
                "evaluation_count": self.evaluation_count,
                "violation_count": self.violation_count,
                "violation_rate": self.violation_count / max(1, self.evaluation_count),
                "last_evaluation_time": self.last_evaluation_time,
                "last_evaluation_result": self.last_evaluation_result
            }
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the ethical rule to a dictionary for serialization.
        
        Returns:
            Dictionary representation of the ethical rule
        """
        return {
            "name": self.name,
            "description": self.description,
            "type": self.__class__.__name__,
            "metadata": self.metadata.to_dict(),
            "stats": {
                "evaluation_count": self.evaluation_count,
                "violation_count": self.violation_count,
                "last_evaluation_time": self.last_evaluation_time
            }
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'EthicalRule':
        """
        Create an ethical rule from a dictionary.
        
        This method should be implemented by subclasses to handle
        rule-specific deserialization.
        
        Args:
            data: Dictionary representation of an ethical rule
            
        Returns:
            Instantiated ethical rule object
        """
        raise NotImplementedError(f"{cls.__name__}.from_dict() must be implemented by subclasses")


class FairnessRule(EthicalRule):
    """
    Ethical rule for fairness and non-discrimination.
    
    Fairness rules ensure that the system treats all individuals fairly and
    does not discriminate based on protected attributes like race, gender,
    religion, etc.
    """
    
    def __init__(self, 
                name: str, 
                description: str,
                evaluation_func: Optional[Callable[[Any, Dict[str, Any]], Tuple[bool, float]]] = None,
                protected_attributes: List[str] = None,
                fairness_metric: str = "demographic_parity",
                threshold: float = 0.8,
                metadata: Optional[EthicalRuleMetadata] = None):
        """
        Initialize a fairness rule.
        
        Args:
            name: Unique identifier for the rule
            description: Human-readable description of the rule
            evaluation_func: Function that evaluates if the rule is satisfied
            protected_attributes: List of protected attributes to check for fairness
            fairness_metric: Metric to use for fairness evaluation
            threshold: Minimum acceptable fairness score
            metadata: Additional metadata for categorization and management
        """
        # Create default metadata if none provided
        if metadata is None:
            metadata = EthicalRuleMetadata(
                principle=EthicalPrincipleType.JUSTICE,
                values=[ValueAlignmentType.FAIRNESS, ValueAlignmentType.DIVERSITY]
            )
        
        super().__init__(name, description, evaluation_func, metadata)
        self.protected_attributes = protected_attributes or ["race", "gender", "age", "religion", "disability"]
        self.fairness_metric = fairness_metric
        self.threshold = threshold
        
        # Initialize fairness metric functions
        self._fairness_metric_functions = {
            "demographic_parity": self._demographic_parity,
            "equal_opportunity": self._equal_opportunity,
            "equalized_odds": self._equalized_odds,
            "disparate_impact": self._disparate_impact,
            "statistical_parity": self._statistical_parity,
            "predictive_parity": self._predictive_parity,
            "counterfactual_fairness": self._counterfactual_fairness
        }
    
    def evaluate_rule(self, subject: Any, context: Dict[str, Any]) -> Tuple[bool, float]:
        """
        Evaluate if the fairness rule is satisfied.
        
        Args:
            subject: The subject to evaluate against the rule
            context: Additional context information for the evaluation
            
        Returns:
            Tuple of (is_satisfied, confidence_score)
        """
        # Extract fairness metrics from subject or context
        fairness_score = self._extract_fairness_metrics(subject, context)
        
        # If score is None, we couldn't evaluate fairness directly, try to extract data for evaluation
        if fairness_score is None:
            # Try to extract data needed for fairness evaluation
            data = self._extract_evaluation_data(subject, context)
            
            if data:
                # Use appropriate fairness metric
                metric_func = self._fairness_metric_functions.get(self.fairness_metric)
                if metric_func:
                    fairness_score = metric_func(data)
                else:
                    # Default to statistical parity if metric not found
                    fairness_score = self._statistical_parity(data)
            else:
                # No data available for evaluation
                logger.debug(f"No data available for fairness evaluation in rule {self.name}")
                fairness_score = None
        
        # Check if fairness score meets the threshold
        if fairness_score is None:
            # If we couldn't evaluate fairness, assume non-compliant with low confidence
            return False, 0.2
        
        is_satisfied = fairness_score >= self.threshold
        
        # Calculate confidence based on distance from threshold
        if is_satisfied:
            # If satisfied, confidence increases as score exceeds threshold
            confidence = min(1.0, 0.5 + (fairness_score - self.threshold) / (1.0 - self.threshold) * 0.5)
        else:
            # If not satisfied, confidence decreases as score falls below threshold
            confidence = max(0.0, 0.5 * fairness_score / self.threshold)
        
        return is_satisfied, confidence
    
    def _extract_fairness_metrics(self, subject: Any, context: Dict[str, Any]) -> Optional[float]:
        """
        Extract fairness metrics from subject or context.
        
        Args:
            subject: The subject to evaluate
            context: Additional context information
            
        Returns:
            Float representing fairness score, or None if not available
        """
        # Try to find direct fairness metrics in subject
        if isinstance(subject, dict):
            # Try to find the specific fairness metric
            if self.fairness_metric in subject:
                return float(subject[self.fairness_metric])
            
            # Try common variations of the metric name
            for key in [
                f"fairness_{self.fairness_metric}",
                f"{self.fairness_metric}_score",
                f"fairness.{self.fairness_metric}"
            ]:
                if key in subject:
                    return float(subject[key])
            
            # Check for general fairness metrics
            for key in ["fairness", "fairness_score", "justice_score"]:
                if key in subject:
                    return float(subject[key])
            
            # Check in fairness metrics section
            if "fairness_metrics" in subject and isinstance(subject["fairness_metrics"], dict):
                metrics = subject["fairness_metrics"]
                if self.fairness_metric in metrics:
                    return float(metrics[self.fairness_metric])
                # Fall back to general fairness
                if "overall" in metrics:
                    return float(metrics["overall"])
        
        # Check context for fairness metrics
        if context:
            # Try to find the specific fairness metric
            if self.fairness_metric in context:
                return float(context[self.fairness_metric])
            
            # Try common variations of the metric name
            for key in [
                f"fairness_{self.fairness_metric}",
                f"{self.fairness_metric}_score",
                f"fairness.{self.fairness_metric}"
            ]:
                if key in context:
                    return float(context[key])
            
            # Check for general fairness metrics
            for key in ["fairness", "fairness_score", "justice_score"]:
                if key in context:
                    return float(context[key])
            
            # Check in fairness metrics section
            if "fairness_metrics" in context and isinstance(context["fairness_metrics"], dict):
                metrics = context["fairness_metrics"]
                if self.fairness_metric in metrics:
                    return float(metrics[self.fairness_metric])
                # Fall back to general fairness
                if "overall" in metrics:
                    return float(metrics["overall"])
        
        # No direct fairness metrics found
        return None
    
    def _extract_evaluation_data(self, subject: Any, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Extract data needed for fairness evaluation.
        
        Args:
            subject: The subject to evaluate
            context: Additional context information
            
        Returns:
            Dictionary with evaluation data, or None if not available
        """
        data = {}
        
        # Try to find predictions and protected attributes
        predictions = None
        protected_values = {}
        ground_truth = None
        
        # Check subject for data
        if isinstance(subject, dict):
            # Look for predictions
            if "predictions" in subject:
                predictions = subject["predictions"]
            elif "outputs" in subject:
                predictions = subject["outputs"]
            elif "scores" in subject:
                predictions = subject["scores"]
            
            # Look for ground truth
            if "ground_truth" in subject:
                ground_truth = subject["ground_truth"]
            elif "labels" in subject:
                ground_truth = subject["labels"]
            elif "actual" in subject:
                ground_truth = subject["actual"]
            
            # Look for protected attributes
            for attr in self.protected_attributes:
                if attr in subject:
                    protected_values[attr] = subject[attr]
            
            # Check structured data
            if "data" in subject and isinstance(subject["data"], dict):
                data_dict = subject["data"]
                
                # Try to find predictions in data
                if predictions is None:
                    for key in ["predictions", "outputs", "scores"]:
                        if key in data_dict:
                            predictions = data_dict[key]
                            break
                
                # Try to find ground truth in data
                if ground_truth is None:
                    for key in ["ground_truth", "labels", "actual"]:
                        if key in data_dict:
                            ground_truth = data_dict[key]
                            break
                
                # Look for protected attributes in data
                for attr in self.protected_attributes:
                    if attr in data_dict and attr not in protected_values:
                        protected_values[attr] = data_dict[attr]
        
        # Check context for data if not found in subject
        if context:
            # Look for predictions
            if predictions is None:
                if "predictions" in context:
                    predictions = context["predictions"]
                elif "outputs" in context:
                    predictions = context["outputs"]
                elif "scores" in context:
                    predictions = context["scores"]
            
            # Look for ground truth
            if ground_truth is None:
                if "ground_truth" in context:
                    ground_truth = context["ground_truth"]
                elif "labels" in context:
                    ground_truth = context["labels"]
                elif "actual" in context:
                    ground_truth = context["actual"]
            
            # Look for protected attributes
            for attr in self.protected_attributes:
                if attr in context and attr not in protected_values:
                    protected_values[attr] = context[attr]
            
            # Check for dataset in context
            if "dataset" in context and isinstance(context["dataset"], dict):
                dataset = context["dataset"]
                
                # Try to find predictions in dataset
                if predictions is None:
                    for key in ["predictions", "outputs", "scores"]:
                        if key in dataset:
                            predictions = dataset[key]
                            break
                
                # Try to find ground truth in dataset
                if ground_truth is None:
                    for key in ["ground_truth", "labels", "actual"]:
                        if key in dataset:
                            ground_truth = dataset[key]
                            break
                
                # Look for protected attributes in dataset
                for attr in self.protected_attributes:
                    if attr in dataset and attr not in protected_values:
                        protected_values[attr] = dataset[attr]
        
        # Check if we have enough data for evaluation
        if predictions is not None and any(protected_values):
            data = {
                "predictions": predictions,
                "protected_attributes": protected_values
            }
            
            if ground_truth is not None:
                data["ground_truth"] = ground_truth
            
            return data
        
        return None
    
    def _demographic_parity(self, data: Dict[str, Any]) -> float:
        """
        Calculate demographic parity (statistical parity).
        
        Demographic parity is satisfied when the probability of a positive outcome
        is the same across all protected attribute groups.
        
        Args:
            data: Dictionary with evaluation data
            
        Returns:
            Fairness score between 0 and 1
        """
        predictions = np.array(data["predictions"])
        protected_attributes = data["protected_attributes"]
        
        if not protected_attributes:
            return 1.0  # No protected attributes to check
        
        # Convert predictions to binary if not already
        if not isinstance(predictions[0], (bool, int)) or not all(p in [0, 1] for p in predictions):
            # Assume probabilistic predictions, threshold at 0.5
            binary_predictions = (predictions > 0.5).astype(int)
        else:
            binary_predictions = predictions
        
        # Calculate acceptance rates for each protected attribute
        parities = []
        for attr, attr_values in protected_attributes.items():
            attr_values = np.array(attr_values)
            
            # Get unique attribute values
            unique_values = np.unique(attr_values)
            if len(unique_values) < 2:
                continue  # Skip if there's only one group
            
            # Calculate acceptance rate for each group
            acceptance_rates = []
            for value in unique_values:
                group_mask = (attr_values == value)
                group_predictions = binary_predictions[group_mask]
                
                if len(group_predictions) > 0:
                    acceptance_rate = np.mean(group_predictions)
                    acceptance_rates.append(acceptance_rate)
            
            # Calculate min-max ratio or difference
            if acceptance_rates:
                min_rate = min(acceptance_rates)
                max_rate = max(acceptance_rates)
                
                if max_rate > 0:
                    # Use ratio (min/max) as parity measure
                    parity = min_rate / max_rate
                else:
                    # If max_rate is 0, check if min_rate is also 0
                    parity = 1.0 if min_rate == 0 else 0.0
                
                parities.append(parity)
        
        # Final score is the minimum parity across all attributes
        return min(parities) if parities else 1.0
    
    def _equal_opportunity(self, data: Dict[str, Any]) -> float:
        """
        Calculate equal opportunity.
        
        Equal opportunity is satisfied when the true positive rates are
        equal across all protected attribute groups.
        
        Args:
            data: Dictionary with evaluation data
            
        Returns:
            Fairness score between 0 and 1
        """
        predictions = np.array(data["predictions"])
        protected_attributes = data["protected_attributes"]
        
        # Need ground truth for equal opportunity
        if "ground_truth" not in data:
            return self._demographic_parity(data)  # Fall back to demographic parity
        
        ground_truth = np.array(data["ground_truth"])
        
        if not protected_attributes:
            return 1.0  # No protected attributes to check
        
        # Convert predictions to binary if not already
        if not isinstance(predictions[0], (bool, int)) or not all(p in [0, 1] for p in predictions):
            # Assume probabilistic predictions, threshold at 0.5
            binary_predictions = (predictions > 0.5).astype(int)
        else:
            binary_predictions = predictions
        
        # Calculate true positive rates for each protected attribute
        tpr_parities = []
        for attr, attr_values in protected_attributes.items():
            attr_values = np.array(attr_values)
            
            # Get unique attribute values
            unique_values = np.unique(attr_values)
            if len(unique_values) < 2:
                continue  # Skip if there's only one group
            
            # Calculate TPR for each group
            tprs = []
            for value in unique_values:
                group_mask = (attr_values == value)
                group_predictions = binary_predictions[group_mask]
                group_ground_truth = ground_truth[group_mask]
                
                # Calculate TPR: TP / (TP + FN)
                positive_mask = (group_ground_truth == 1)
                if np.sum(positive_mask) > 0:
                    tp = np.sum(group_predictions[positive_mask] == 1)
                    tpr = tp / np.sum(positive_mask)
                    tprs.append(tpr)
            
            # Calculate min-max ratio
            if tprs:
                min_tpr = min(tprs)
                max_tpr = max(tprs)
                
                if max_tpr > 0:
                    tpr_parity = min_tpr / max_tpr
                else:
                    tpr_parity = 1.0 if min_tpr == 0 else 0.0
                
                tpr_parities.append(tpr_parity)
        
        # Final score is the minimum TPR parity across all attributes
        return min(tpr_parities) if tpr_parities else 1.0
    
    def _equalized_odds(self, data: Dict[str, Any]) -> float:
        """
        Calculate equalized odds.
        
        Equalized odds is satisfied when both the true positive rates and
        false positive rates are equal across all protected attribute groups.
        
        Args:
            data: Dictionary with evaluation data
            
        Returns:
            Fairness score between 0 and 1
        """
        predictions = np.array(data["predictions"])
        protected_attributes = data["protected_attributes"]
        
        # Need ground truth for equalized odds
        if "ground_truth" not in data:
            return self._demographic_parity(data)  # Fall back to demographic parity
        
        ground_truth = np.array(data["ground_truth"])
        
        if not protected_attributes:
            return 1.0  # No protected attributes to check
        
        # Convert predictions to binary if not already
        if not isinstance(predictions[0], (bool, int)) or not all(p in [0, 1] for p in predictions):
            # Assume probabilistic predictions, threshold at 0.5
            binary_predictions = (predictions > 0.5).astype(int)
        else:
            binary_predictions = predictions
        
        # Calculate TPR and FPR for each protected attribute
        odds_parities = []
        for attr, attr_values in protected_attributes.items():
            attr_values = np.array(attr_values)
            
            # Get unique attribute values
            unique_values = np.unique(attr_values)
            if len(unique_values) < 2:
                continue  # Skip if there's only one group
            
            # Calculate TPR and FPR for each group
            tprs = []
            fprs = []
            for value in unique_values:
                group_mask = (attr_values == value)
                group_predictions = binary_predictions[group_mask]
                group_ground_truth = ground_truth[group_mask]
                
                # Calculate TPR: TP / (TP + FN)
                positive_mask = (group_ground_truth == 1)
                if np.sum(positive_mask) > 0:
                    tp = np.sum(group_predictions[positive_mask] == 1)
                    tpr = tp / np.sum(positive_mask)
                    tprs.append(tpr)
                
                # Calculate FPR: FP / (FP + TN)
                negative_mask = (group_ground_truth == 0)
                if np.sum(negative_mask) > 0:
                    fp = np.sum(group_predictions[negative_mask] == 1)
                    fpr = fp / np.sum(negative_mask)
                    fprs.append(fpr)
            
            # Calculate min-max ratio for TPR and FPR
            tpr_parity = 1.0
            if tprs:
                min_tpr = min(tprs)
                max_tpr = max(tprs)
                
                if max_tpr > 0:
                    tpr_parity = min_tpr / max_tpr
                else:
                    tpr_parity = 1.0 if min_tpr == 0 else 0.0
            
            fpr_parity = 1.0
            if fprs:
                min_fpr = min(fprs)
                max_fpr = max(fprs)
                
                if max_fpr > 0:
                    fpr_parity = min_fpr / max_fpr
                else:
                    fpr_parity = 1.0 if min_fpr == 0 else 0.0
            
            # Equalized odds is the minimum of TPR parity and FPR parity
            odds_parity = min(tpr_parity, fpr_parity)
            odds_parities.append(odds_parity)
        
        # Final score is the minimum odds parity across all attributes
        return min(odds_parities) if odds_parities else 1.0
    
    def _disparate_impact(self, data: Dict[str, Any]) -> float:
        """
        Calculate disparate impact.
        
        Disparate impact is a measure of the ratio of positive outcomes
        between different protected attribute groups.
        
        Args:
            data: Dictionary with evaluation data
            
        Returns:
            Fairness score between 0 and 1
        """
        # Disparate impact is essentially the same as demographic parity
        # But often uses the 80% rule from US law
        parity = self._demographic_parity(data)
        
        # Scale to 80% rule if below 0.8
        if parity < 0.8:
            # Scale linearly from 0 to 1 in the range [0, 0.8]
            return parity / 0.8
        else:
            return 1.0
    
    def _statistical_parity(self, data: Dict[str, Any]) -> float:
        """
        Calculate statistical parity.
        
        Statistical parity is satisfied when the probability of a positive outcome
        is the same across all protected attribute groups.
        
        Args:
            data: Dictionary with evaluation data
            
        Returns:
            Fairness score between 0 and 1
        """
        # Statistical parity is the same as demographic parity
        return self._demographic_parity(data)
    
    def _predictive_parity(self, data: Dict[str, Any]) -> float:
        """
        Calculate predictive parity.
        
        Predictive parity is satisfied when the precision (positive predictive value)
        is the same across all protected attribute groups.
        
        Args:
            data: Dictionary with evaluation data
            
        Returns:
            Fairness score between 0 and 1
        """
        predictions = np.array(data["predictions"])
        protected_attributes = data["protected_attributes"]
        
        # Need ground truth for predictive parity
        if "ground_truth" not in data:
            return self._demographic_parity(data)  # Fall back to demographic parity
        
        ground_truth = np.array(data["ground_truth"])
        
        if not protected_attributes:
            return 1.0  # No protected attributes to check
        
        # Convert predictions to binary if not already
        if not isinstance(predictions[0], (bool, int)) or not all(p in [0, 1] for p in predictions):
            # Assume probabilistic predictions, threshold at 0.5
            binary_predictions = (predictions > 0.5).astype(int)
        else:
            binary_predictions = predictions
        
        # Calculate precision for each protected attribute
        precision_parities = []
        for attr, attr_values in protected_attributes.items():
            attr_values = np.array(attr_values)
            
            # Get unique attribute values
            unique_values = np.unique(attr_values)
            if len(unique_values) < 2:
                continue  # Skip if there's only one group
            
            # Calculate precision for each group
            precisions = []
            for value in unique_values:
                group_mask = (attr_values == value)
                group_predictions = binary_predictions[group_mask]
                group_ground_truth = ground_truth[group_mask]
                
                # Calculate precision: TP / (TP + FP)
                predicted_positive_mask = (group_predictions == 1)
                if np.sum(predicted_positive_mask) > 0:
                    tp = np.sum(group_ground_truth[predicted_positive_mask] == 1)
                    precision = tp / np.sum(predicted_positive_mask)
                    precisions.append(precision)
            
            # Calculate min-max ratio
            if precisions:
                min_precision = min(precisions)
                max_precision = max(precisions)
                
                if max_precision > 0:
                    precision_parity = min_precision / max_precision
                else:
                    precision_parity = 1.0 if min_precision == 0 else 0.0
                
                precision_parities.append(precision_parity)
        
        # Final score is the minimum precision parity across all attributes
        return min(precision_parities) if precision_parities else 1.0
    
    def _counterfactual_fairness(self, data: Dict[str, Any]) -> float:
        """
        Approximate counterfactual fairness.
        
        Counterfactual fairness is satisfied when the outcome remains the same
        if the protected attribute is changed, all else being equal.
        This is an approximation since true counterfactual fairness requires a causal model.
        
        Args:
            data: Dictionary with evaluation data
            
        Returns:
            Fairness score between 0 and 1
        """
        # This is a simplified approximation of counterfactual fairness
        # True counterfactual fairness requires a causal model
        
        # Fall back to demographic parity as a proxy
        return self._demographic_parity(data)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FairnessRule':
        """
        Create a fairness rule from a dictionary.
        
        Args:
            data: Dictionary representation of a fairness rule
            
        Returns:
            FairnessRule instance
        """
        # Extract required fields with defaults
        name = data.get("name", "unnamed_fairness_rule")
        description = data.get("description", "")
        protected_attributes = data.get("protected_attributes", ["race", "gender", "age"])
        fairness_metric = data.get("fairness_metric", "demographic_parity")
        threshold = data.get("threshold", 0.8)
        
        # Create metadata if present
        metadata = None
        if "metadata" in data:
            metadata = EthicalRuleMetadata.from_dict(data["metadata"])
        
        # Create the rule (without evaluation_func for now)
        rule = cls(
            name=name,
            description=description,
            protected_attributes=protected_attributes,
            fairness_metric=fairness_metric,
            threshold=threshold,
            metadata=metadata
        )
        
        # If stats are provided, update them
        if "stats" in data and isinstance(data["stats"], dict):
            stats = data["stats"]
            rule.evaluation_count = stats.get("evaluation_count", 0)
            rule.violation_count = stats.get("violation_count", 0)
            rule.last_evaluation_time = stats.get("last_evaluation_time", 0.0)
        
        return rule
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the fairness rule to a dictionary for serialization.
        
        Returns:
            Dictionary representation of the fairness rule
        """
        data = super().to_dict()
        data.update({
            "protected_attributes": self.protected_attributes,
            "fairness_metric": self.fairness_metric,
            "threshold": self.threshold
        })
        return data


class BeneficenceRule(EthicalRule):
    """
    Ethical rule for beneficence (promoting well-being).
    
    Beneficence rules ensure that the system's actions and decisions
    promote human well-being and positive outcomes.
    """
    
    def __init__(self, 
                name: str, 
                description: str,
                evaluation_func: Optional[Callable[[Any, Dict[str, Any]], Tuple[bool, float]]] = None,
                benefit_dimensions: List[str] = None,
                min_benefit_score: float = 0.6,
                metadata: Optional[EthicalRuleMetadata] = None):
        """
        Initialize a beneficence rule.
        
        Args:
            name: Unique identifier for the rule
            description: Human-readable description of the rule
            evaluation_func: Function that evaluates if the rule is satisfied
            benefit_dimensions: List of dimensions to evaluate for benefit
            min_benefit_score: Minimum acceptable benefit score
            metadata: Additional metadata for categorization and management
        """
        # Create default metadata if none provided
        if metadata is None:
            metadata = EthicalRuleMetadata(
                principle=EthicalPrincipleType.BENEFICENCE,
                values=[ValueAlignmentType.HUMAN_WELFARE, ValueAlignmentType.PROGRESS]
            )
        
        super().__init__(name, description, evaluation_func, metadata)
        self.benefit_dimensions = benefit_dimensions or [
            "physical_wellbeing", "mental_wellbeing", "social_wellbeing",
            "economic_benefit", "knowledge", "capabilities", "satisfaction"
        ]
        self.min_benefit_score = min_benefit_score
        
        # Dimension weights (can be customized)
        self.dimension_weights = {dim: 1.0 for dim in self.benefit_dimensions}
    
    def evaluate_rule(self, subject: Any, context: Dict[str, Any]) -> Tuple[bool, float]:
        """
        Evaluate if the beneficence rule is satisfied.
        
        Args:
            subject: The subject to evaluate against the rule
            context: Additional context information for the evaluation
            
        Returns:
            Tuple of (is_satisfied, confidence_score)
        """
        # Extract benefit scores from subject or context
        benefit_scores = self._extract_benefit_scores(subject, context)
        
        # If no benefit scores were found, try to calculate them
        if not benefit_scores:
            benefit_scores = self._calculate_benefit_scores(subject, context)
        
        # If still no benefit scores, check for overall benefit score
        if not benefit_scores:
            overall_score = self._extract_overall_benefit(subject, context)
            if overall_score is not None:
                # Check if overall score meets minimum requirement
                is_satisfied = overall_score >= self.min_benefit_score
                
                # Calculate confidence based on distance from threshold
                if is_satisfied:
                    confidence = min(1.0, 0.5 + (overall_score - self.min_benefit_score) / (1.0 - self.min_benefit_score) * 0.5)
                else:
                    confidence = max(0.0, 0.5 * overall_score / self.min_benefit_score)
                
                return is_satisfied, confidence
            else:
                # No benefit information available
                logger.debug(f"No benefit information available for rule {self.name}")
                return False, 0.2
        
        # Calculate weighted average of benefit scores
        total_weight = sum(self.dimension_weights.get(dim, 1.0) for dim in benefit_scores)
        if total_weight <= 0:
            return False, 0.0
        
        weighted_score = sum(
            score * self.dimension_weights.get(dim, 1.0)
            for dim, score in benefit_scores.items()
        ) / total_weight
        
        # Check if weighted score meets minimum requirement
        is_satisfied = weighted_score >= self.min_benefit_score
        
        # Calculate confidence based on distance from threshold and number of dimensions
        dimension_coverage = len(benefit_scores) / len(self.benefit_dimensions)
        
        if is_satisfied:
            # Higher confidence when further above threshold and more dimensions covered
            score_factor = min(1.0, 0.5 + (weighted_score - self.min_benefit_score) / (1.0 - self.min_benefit_score) * 0.5)
            coverage_factor = 0.5 + dimension_coverage * 0.5
            confidence = score_factor * coverage_factor
        else:
            # Lower confidence when further below threshold
            confidence = max(0.0, 0.5 * weighted_score / self.min_benefit_score)
        
        return is_satisfied, confidence
    
    def _extract_benefit_scores(self, subject: Any, context: Dict[str, Any]) -> Dict[str, float]:
        """
        Extract benefit scores for different dimensions from subject or context.
        
        Args:
            subject: The subject to evaluate
            context: Additional context information
            
        Returns:
            Dictionary mapping benefit dimensions to scores
        """
        benefit_scores = {}
        
        # Check subject for benefit scores
        if isinstance(subject, dict):
            # Look for benefit scores in a nested structure
            if "benefits" in subject and isinstance(subject["benefits"], dict):
                benefits = subject["benefits"]
                for dim in self.benefit_dimensions:
                    if dim in benefits:
                        try:
                            benefit_scores[dim] = float(benefits[dim])
                        except (ValueError, TypeError):
                            pass
            
            # Look for benefit scores directly in subject
            for dim in self.benefit_dimensions:
                if dim in subject and dim not in benefit_scores:
                    try:
                        benefit_scores[dim] = float(subject[dim])
                    except (ValueError, TypeError):
                        pass
                
                # Try variations of dimension name
                for variant in [f"benefit_{dim}", f"{dim}_benefit", f"{dim}_score"]:
                    if variant in subject and dim not in benefit_scores:
                        try:
                            benefit_scores[dim] = float(subject[variant])
                        except (ValueError, TypeError):
                            pass
        
        # Check context for benefit scores
        if context:
            # Look for benefit scores in a nested structure
            if "benefits" in context and isinstance(context["benefits"], dict):
                benefits = context["benefits"]
                for dim in self.benefit_dimensions:
                    if dim in benefits and dim not in benefit_scores:
                        try:
                            benefit_scores[dim] = float(benefits[dim])
                        except (ValueError, TypeError):
                            pass
            
            # Look for benefit scores directly in context
            for dim in self.benefit_dimensions:
                if dim in context and dim not in benefit_scores:
                    try:
                        benefit_scores[dim] = float(context[dim])
                    except (ValueError, TypeError):
                        pass
                
                # Try variations of dimension name
                for variant in [f"benefit_{dim}", f"{dim}_benefit", f"{dim}_score"]:
                    if variant in context and dim not in benefit_scores:
                        try:
                            benefit_scores[dim] = float(context[variant])
                        except (ValueError, TypeError):
                            pass
        
        # Normalize scores to [0, 1] range
        for dim in list(benefit_scores.keys()):
            score = benefit_scores[dim]
            if score < 0 or score > 1:
                # Attempt to normalize
                if isinstance(score, (int, float)):
                    if score < 0:
                        benefit_scores[dim] = 0.0
                    elif score > 1:
                        benefit_scores[dim] = 1.0
                else:
                    # Remove non-numeric scores
                    del benefit_scores[dim]
        
        return benefit_scores
    
    def _calculate_benefit_scores(self, subject: Any, context: Dict[str, Any]) -> Dict[str, float]:
        """
        Calculate benefit scores based on available information.
        
        Args:
            subject: The subject to evaluate
            context: Additional context information
            
        Returns:
            Dictionary mapping benefit dimensions to scores
        """
        benefit_scores = {}
        
        # Try to extract impact information
        impacts = {}
        
        # Check subject for impact information
        if isinstance(subject, dict):
            if "impacts" in subject and isinstance(subject["impacts"], dict):
                impacts.update(subject["impacts"])
            elif "effects" in subject and isinstance(subject["effects"], dict):
                impacts.update(subject["effects"])
            elif "outcomes" in subject and isinstance(subject["outcomes"], dict):
                impacts.update(subject["outcomes"])
        
        # Check context for impact information
        if context:
            if "impacts" in context and isinstance(context["impacts"], dict):
                for k, v in context["impacts"].items():
                    if k not in impacts:
                        impacts[k] = v
            elif "effects" in context and isinstance(context["effects"], dict):
                for k, v in context["effects"].items():
                    if k not in impacts:
                        impacts[k] = v
            elif "outcomes" in context and isinstance(context["outcomes"], dict):
                for k, v in context["outcomes"].items():
                    if k not in impacts:
                        impacts[k] = v
        
        # Map impacts to benefit dimensions
        if impacts:
            # Define dimension keywords to look for in impact keys
            dimension_keywords = {
                "physical_wellbeing": ["physical", "health", "safety", "medical", "bodily"],
                "mental_wellbeing": ["mental", "psychological", "emotional", "stress", "anxiety", "happiness"],
                "social_wellbeing": ["social", "community", "relationship", "connection", "belonging"],
                "economic_benefit": ["economic", "financial", "money", "income", "wealth", "cost"],
                "knowledge": ["knowledge", "learning", "education", "understanding", "intellectual"],
                "capabilities": ["capability", "ability", "skill", "competence", "capacity", "function"],
                "satisfaction": ["satisfaction", "preference", "enjoyment", "pleasure", "approval"]
            }
            
            # Match impact keys to dimensions
            for dim, keywords in dimension_keywords.items():
                if dim not in benefit_scores:
                    matched_impacts = []
                    for impact_key, impact_value in impacts.items():
                        # Check if any keyword matches the impact key
                        if any(keyword in impact_key.lower() for keyword in keywords):
                            try:
                                matched_impacts.append(float(impact_value))
                            except (ValueError, TypeError):
                                # Try to interpret boolean or string values
                                if isinstance(impact_value, bool):
                                    matched_impacts.append(1.0 if impact_value else 0.0)
                                elif isinstance(impact_value, str) and impact_value.lower() in [
                                    "positive", "good", "beneficial", "yes", "true", "high"
                                ]:
                                    matched_impacts.append(1.0)
                                elif isinstance(impact_value, str) and impact_value.lower() in [
                                    "negative", "bad", "harmful", "no", "false", "low"
                                ]:
                                    matched_impacts.append(0.0)
                                elif isinstance(impact_value, str) and impact_value.lower() in [
                                    "neutral", "medium", "moderate", "average"
                                ]:
                                    matched_impacts.append(0.5)
                    
                    # If any impacts were matched, take their average
                    if matched_impacts:
                        benefit_scores[dim] = sum(matched_impacts) / len(matched_impacts)
        
        return benefit_scores
    
    def _extract_overall_benefit(self, subject: Any, context: Dict[str, Any]) -> Optional[float]:
        """
        Extract overall benefit score from subject or context.
        
        Args:
            subject: The subject to evaluate
            context: Additional context information
            
        Returns:
            Float representing overall benefit score, or None if not available
        """
        # Check subject for overall benefit
        if isinstance(subject, dict):
            for key in ["benefit", "benefit_score", "wellbeing", "wellbeing_score", "utility", "welfare"]:
                if key in subject:
                    try:
                        return float(subject[key])
                    except (ValueError, TypeError):
                        pass
        
        # Check context for overall benefit
        if context:
            for key in ["benefit", "benefit_score", "wellbeing", "wellbeing_score", "utility", "welfare"]:
                if key in context:
                    try:
                        return float(context[key])
                    except (ValueError, TypeError):
                        pass
        
        return None
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BeneficenceRule':
        """
        Create a beneficence rule from a dictionary.
        
        Args:
            data: Dictionary representation of a beneficence rule
            
        Returns:
            BeneficenceRule instance
        """
        # Extract required fields with defaults
        name = data.get("name", "unnamed_beneficence_rule")
        description = data.get("description", "")
        benefit_dimensions = data.get("benefit_dimensions")
        min_benefit_score = data.get("min_benefit_score", 0.6)
        
        # Create metadata if present
        metadata = None
        if "metadata" in data:
            metadata = EthicalRuleMetadata.from_dict(data["metadata"])
        
        # Create the rule (without evaluation_func for now)
        rule = cls(
            name=name,
            description=description,
            benefit_dimensions=benefit_dimensions,
            min_benefit_score=min_benefit_score,
            metadata=metadata
        )
        
        # If dimension weights are provided, update them
        if "dimension_weights" in data and isinstance(data["dimension_weights"], dict):
            for dim, weight in data["dimension_weights"].items():
                rule.dimension_weights[dim] = weight
        
        # If stats are provided, update them
        if "stats" in data and isinstance(data["stats"], dict):
            stats = data["stats"]
            rule.evaluation_count = stats.get("evaluation_count", 0)
            rule.violation_count = stats.get("violation_count", 0)
            rule.last_evaluation_time = stats.get("last_evaluation_time", 0.0)
        
        return rule
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the beneficence rule to a dictionary for serialization.
        
        Returns:
            Dictionary representation of the beneficence rule
        """
        data = super().to_dict()
        data.update({
            "benefit_dimensions": self.benefit_dimensions,
            "min_benefit_score": self.min_benefit_score,
            "dimension_weights": self.dimension_weights
        })
        return data


class NonMaleficenceRule(EthicalRule):
    """
    Ethical rule for non-maleficence (avoiding harm).
    
    Non-maleficence rules ensure that the system's actions and decisions
    do not cause harm to humans or other entities.
    """
    
    def __init__(self, 
                name: str, 
                description: str,
                evaluation_func: Optional[Callable[[Any, Dict[str, Any]], Tuple[bool, float]]] = None,
                harm_categories: List[str] = None,
                max_acceptable_harm: float = 0.2,
                harm_detection_keywords: Dict[str, List[str]] = None,
                metadata: Optional[EthicalRuleMetadata] = None):
        """
        Initialize a non-maleficence rule.
        
        Args:
            name: Unique identifier for the rule
            description: Human-readable description of the rule
            evaluation_func: Function that evaluates if the rule is satisfied
            harm_categories: List of harm categories to check
            max_acceptable_harm: Maximum acceptable harm score
            harm_detection_keywords: Keywords for detecting harm in textual content
            metadata: Additional metadata for categorization and management
        """
        # Create default metadata if none provided
        if metadata is None:
            metadata = EthicalRuleMetadata(
                principle=EthicalPrincipleType.NON_MALEFICENCE,
                values=[ValueAlignmentType.HUMAN_WELFARE, ValueAlignmentType.DIGNITY]
            )
        
        super().__init__(name, description, evaluation_func, metadata)
        self.harm_categories = harm_categories or [
            "physical_harm", "psychological_harm", "social_harm", 
            "economic_harm", "environmental_harm", "security_harm", 
            "privacy_harm", "autonomy_harm", "dignity_harm"
        ]
        self.max_acceptable_harm = max_acceptable_harm
        
        # Default keywords for harm detection if not provided
        if harm_detection_keywords is None:
            self.harm_detection_keywords = {
                "physical_harm": ["injury", "pain", "death", "damage", "hurt", "unsafe", "dangerous"],
                "psychological_harm": ["distress", "trauma", "anxiety", "depression", "fear", "suffering"],
                "social_harm": ["discrimination", "isolation", "exclusion", "stigma", "prejudice"],
                "economic_harm": ["loss", "cost", "damage", "poverty", "expense", "debt", "financial"],
                "environmental_harm": ["pollution", "destruction", "waste", "contamination", "depletion"],
                "security_harm": ["vulnerability", "exploit", "breach", "attack", "threat", "insecure"],
                "privacy_harm": ["surveillance", "exposure", "intrusion", "disclosure", "tracking"],
                "autonomy_harm": ["coercion", "manipulation", "deception", "control", "override"],
                "dignity_harm": ["humiliation", "degradation", "disrespect", "dehumanization"]
            }
        else:
            self.harm_detection_keywords = harm_detection_keywords
        
        # Category weights (can be customized)
        self.category_weights = {cat: 1.0 for cat in self.harm_categories}
    
    def evaluate_rule(self, subject: Any, context: Dict[str, Any]) -> Tuple[bool, float]:
        """
        Evaluate if the non-maleficence rule is satisfied.
        
        Args:
            subject: The subject to evaluate against the rule
            context: Additional context information for the evaluation
            
        Returns:
            Tuple of (is_satisfied, confidence_score)
        """
        # Extract harm scores from subject or context
        harm_scores = self._extract_harm_scores(subject, context)
        
        # If no harm scores were found, try to calculate them
        if not harm_scores:
            harm_scores = self._calculate_harm_scores(subject, context)
        
        # If still no harm scores, check for overall harm score
        if not harm_scores:
            overall_harm = self._extract_overall_harm(subject, context)
            if overall_harm is not None:
                # Check if overall harm is below maximum acceptable harm
                is_satisfied = overall_harm <= self.max_acceptable_harm
                
                # Calculate confidence based on distance from threshold
                if is_satisfied:
                    confidence = min(1.0, 0.5 + (self.max_acceptable_harm - overall_harm) / self.max_acceptable_harm * 0.5)
                else:
                    excess_harm = (overall_harm - self.max_acceptable_harm) / (1.0 - self.max_acceptable_harm)
                    confidence = max(0.0, 0.5 - excess_harm * 0.5)
                
                return is_satisfied, confidence
            else:
                # No harm information available
                text_content = self._extract_text_content(subject, context)
                if text_content:
                    # Try to detect harm in text content
                    harm_level, detected_harms = self._detect_harm_in_text(text_content)
                    
                    # Check if detected harm is below maximum acceptable harm
                    is_satisfied = harm_level <= self.max_acceptable_harm
                    
                    # Adjust confidence based on detection method
                    base_confidence = 0.6  # Lower confidence for text-based detection
                    if is_satisfied:
                        confidence = base_confidence * (1.0 - harm_level / self.max_acceptable_harm)
                    else:
                        confidence = base_confidence * (harm_level - self.max_acceptable_harm) / (1.0 - self.max_acceptable_harm)
                    
                    return is_satisfied, confidence
                else:
                    # No information available to evaluate
                    logger.debug(f"No harm information available for rule {self.name}")
                    # Default to safe (non-harmful) with low confidence
                    return True, 0.3
        
        # Calculate weighted average of harm scores
        total_weight = sum(self.category_weights.get(cat, 1.0) for cat in harm_scores)
        if total_weight <= 0:
            return True, 0.0  # No valid harm categories found
        
        weighted_harm = sum(
            score * self.category_weights.get(cat, 1.0)
            for cat, score in harm_scores.items()
        ) / total_weight
        
        # Check if weighted harm is below maximum acceptable harm
        is_satisfied = weighted_harm <= self.max_acceptable_harm
        
        # Calculate confidence based on distance from threshold and number of categories
        category_coverage = len(harm_scores) / len(self.harm_categories)
        
        if is_satisfied:
            # Higher confidence when further below threshold and more categories covered
            score_factor = min(1.0, 0.5 + (self.max_acceptable_harm - weighted_harm) / self.max_acceptable_harm * 0.5)
            coverage_factor = 0.5 + category_coverage * 0.5
            confidence = score_factor * coverage_factor
        else:
            # Lower confidence when further above threshold
            excess_harm = (weighted_harm - self.max_acceptable_harm) / (1.0 - self.max_acceptable_harm)
            confidence = max(0.0, 0.5 - excess_harm * 0.5)
        
        return is_satisfied, confidence
    
    def _extract_harm_scores(self, subject: Any, context: Dict[str, Any]) -> Dict[str, float]:
        """
        Extract harm scores for different categories from subject or context.
        
        Args:
            subject: The subject to evaluate
            context: Additional context information
            
        Returns:
            Dictionary mapping harm categories to scores
        """
        harm_scores = {}
        
        # Check subject for harm scores
        if isinstance(subject, dict):
            # Look for harm scores in a nested structure
            if "harms" in subject and isinstance(subject["harms"], dict):
                harms = subject["harms"]
                for cat in self.harm_categories:
                    if cat in harms:
                        try:
                            harm_scores[cat] = float(harms[cat])
                        except (ValueError, TypeError):
                            pass
            
            # Look for harm scores directly in subject
            for cat in self.harm_categories:
                if cat in subject and cat not in harm_scores:
                    try:
                        harm_scores[cat] = float(subject[cat])
                    except (ValueError, TypeError):
                        pass
                
                # Try variations of category name
                for variant in [f"harm_{cat}", f"{cat}_harm", f"{cat}_risk"]:
                    if variant in subject and cat not in harm_scores:
                        try:
                            harm_scores[cat] = float(subject[variant])
                        except (ValueError, TypeError):
                            pass
        
        # Check context for harm scores
        if context:
            # Look for harm scores in a nested structure
            if "harms" in context and isinstance(context["harms"], dict):
                harms = context["harms"]
                for cat in self.harm_categories:
                    if cat in harms and cat not in harm_scores:
                        try:
                            harm_scores[cat] = float(harms[cat])
                        except (ValueError, TypeError):
                            pass
            
            # Look for harm scores directly in context
            for cat in self.harm_categories:
                if cat in context and cat not in harm_scores:
                    try:
                        harm_scores[cat] = float(context[cat])
                    except (ValueError, TypeError):
                        pass
                
                # Try variations of category name
                for variant in [f"harm_{cat}", f"{cat}_harm", f"{cat}_risk"]:
                    if variant in context and cat not in harm_scores:
                        try:
                            harm_scores[cat] = float(context[variant])
                        except (ValueError, TypeError):
                            pass
            
            # Check in risk assessment if available
            if "risk_assessment" in context and isinstance(context["risk_assessment"], dict):
                risk_assessment = context["risk_assessment"]
                for cat in self.harm_categories:
                    if cat in risk_assessment and cat not in harm_scores:
                        try:
                            harm_scores[cat] = float(risk_assessment[cat])
                        except (ValueError, TypeError):
                            pass
        
        # Normalize scores to [0, 1] range
        for cat in list(harm_scores.keys()):
            score = harm_scores[cat]
            if score < 0 or score > 1:
                # Attempt to normalize
                if isinstance(score, (int, float)):
                    if score < 0:
                        harm_scores[cat] = 0.0
                    elif score > 1:
                        harm_scores[cat] = 1.0
                else:
                    # Remove non-numeric scores
                    del harm_scores[cat]
        
        return harm_scores
    
    def _calculate_harm_scores(self, subject: Any, context: Dict[str, Any]) -> Dict[str, float]:
        """
        Calculate harm scores based on available information.
        
        Args:
            subject: The subject to evaluate
            context: Additional context information
            
        Returns:
            Dictionary mapping harm categories to scores
        """
        harm_scores = {}
        
        # Try to extract risk information
        risks = {}
        
        # Check subject for risk information
        if isinstance(subject, dict):
            if "risks" in subject and isinstance(subject["risks"], dict):
                risks.update(subject["risks"])
            elif "hazards" in subject and isinstance(subject["hazards"], dict):
                risks.update(subject["hazards"])
            elif "dangers" in subject and isinstance(subject["dangers"], dict):
                risks.update(subject["dangers"])
        
        # Check context for risk information
        if context:
            if "risks" in context and isinstance(context["risks"], dict):
                for k, v in context["risks"].items():
                    if k not in risks:
                        risks[k] = v
            elif "hazards" in context and isinstance(context["hazards"], dict):
                for k, v in context["hazards"].items():
                    if k not in risks:
                        risks[k] = v
            elif "dangers" in context and isinstance(context["dangers"], dict):
                for k, v in context["dangers"].items():
                    if k not in risks:
                        risks[k] = v
        
        # Map risks to harm categories
        if risks:
            # Define category keywords to look for in risk keys
            category_keywords = {
                "physical_harm": ["physical", "injury", "pain", "death", "bodily", "safety"],
                "psychological_harm": ["psychological", "mental", "emotional", "distress", "anxiety"],
                "social_harm": ["social", "community", "discrimination", "stigma", "relationship"],
                "economic_harm": ["economic", "financial", "money", "cost", "expense", "loss"],
                "environmental_harm": ["environmental", "ecology", "nature", "pollution", "climate"],
                "security_harm": ["security", "breach", "vulnerability", "attack", "threat"],
                "privacy_harm": ["privacy", "surveillance", "confidentiality", "exposure", "data"],
                "autonomy_harm": ["autonomy", "agency", "control", "manipulation", "override"],
                "dignity_harm": ["dignity", "respect", "humiliation", "degradation", "disrespect"]
            }
            
            # Match risk keys to categories
            for cat, keywords in category_keywords.items():
                if cat not in harm_scores:
                    matched_risks = []
                    for risk_key, risk_value in risks.items():
                        # Check if any keyword matches the risk key
                        if any(keyword in risk_key.lower() for keyword in keywords):
                            try:
                                matched_risks.append(float(risk_value))
                            except (ValueError, TypeError):
                                # Try to interpret boolean or string values
                                if isinstance(risk_value, bool):
                                    matched_risks.append(1.0 if risk_value else 0.0)
                                elif isinstance(risk_value, str) and risk_value.lower() in [
                                    "high", "severe", "critical", "dangerous", "harmful"
                                ]:
                                    matched_risks.append(0.8)
                                elif isinstance(risk_value, str) and risk_value.lower() in [
                                    "medium", "moderate", "significant"
                                ]:
                                    matched_risks.append(0.5)
                                elif isinstance(risk_value, str) and risk_value.lower() in [
                                    "low", "minor", "minimal", "slight"
                                ]:
                                    matched_risks.append(0.2)
                                elif isinstance(risk_value, str) and risk_value.lower() in [
                                    "none", "safe", "negligible"
                                ]:
                                    matched_risks.append(0.0)
                    
                    # If any risks were matched, take their average
                    if matched_risks:
                        harm_scores[cat] = sum(matched_risks) / len(matched_risks)
        
        # If no harm scores were found via risk mapping, try text content analysis
        if not harm_scores:
            text_content = self._extract_text_content(subject, context)
            if text_content:
                # Detect harm in text content
                harm_level, detected_harms = self._detect_harm_in_text(text_content)
                harm_scores.update(detected_harms)
        
        return harm_scores
    
    def _extract_overall_harm(self, subject: Any, context: Dict[str, Any]) -> Optional[float]:
        """
        Extract overall harm score from subject or context.
        
        Args:
            subject: The subject to evaluate
            context: Additional context information
            
        Returns:
            Float representing overall harm score, or None if not available
        """
        # Check subject for overall harm
        if isinstance(subject, dict):
            for key in ["harm", "harm_score", "risk", "risk_score", "danger", "hazard"]:
                if key in subject:
                    try:
                        return float(subject[key])
                    except (ValueError, TypeError):
                        pass
            
            # Check for safe/unsafe indicators
            if "is_safe" in subject:
                try:
                    if isinstance(subject["is_safe"], bool):
                        return 0.0 if subject["is_safe"] else 0.8
                    elif isinstance(subject["is_safe"], (int, float)):
                        return 1.0 - float(subject["is_safe"])
                except (ValueError, TypeError):
                    pass
            
            if "is_harmful" in subject:
                try:
                    if isinstance(subject["is_harmful"], bool):
                        return 0.8 if subject["is_harmful"] else 0.0
                    elif isinstance(subject["is_harmful"], (int, float)):
                        return float(subject["is_harmful"])
                except (ValueError, TypeError):
                    pass
        
        # Check context for overall harm
        if context:
            for key in ["harm", "harm_score", "risk", "risk_score", "danger", "hazard"]:
                if key in context:
                    try:
                        return float(context[key])
                    except (ValueError, TypeError):
                        pass
            
            # Check for safe/unsafe indicators
            if "is_safe" in context:
                try:
                    if isinstance(context["is_safe"], bool):
                        return 0.0 if context["is_safe"] else 0.8
                    elif isinstance(context["is_safe"], (int, float)):
                        return 1.0 - float(context["is_safe"])
                except (ValueError, TypeError):
                    pass
            
            if "is_harmful" in context:
                try:
                    if isinstance(context["is_harmful"], bool):
                        return 0.8 if context["is_harmful"] else 0.0
                    elif isinstance(context["is_harmful"], (int, float)):
                        return float(context["is_harmful"])
                except (ValueError, TypeError):
                    pass
            
            # Check risk assessment if available
            if "risk_assessment" in context and isinstance(context["risk_assessment"], dict):
                assessment = context["risk_assessment"]
                if "overall_risk" in assessment:
                    try:
                        return float(assessment["overall_risk"])
                    except (ValueError, TypeError):
                        pass
                elif "harm_level" in assessment:
                    try:
                        return float(assessment["harm_level"])
                    except (ValueError, TypeError):
                        pass
        
        return None
    
    def _extract_text_content(self, subject: Any, context: Dict[str, Any]) -> str:
        """
        Extract textual content from subject or context for harm detection.
        
        Args:
            subject: The subject to evaluate
            context: Additional context information
            
        Returns:
            String containing relevant text content
        """
        texts = []
        
        # Extract text from subject
        if isinstance(subject, str):
            texts.append(subject)
        elif isinstance(subject, dict):
            # Check common text fields
            for key in ["text", "content", "message", "description", "narrative", "output"]:
                if key in subject and isinstance(subject[key], str):
                    texts.append(subject[key])
            
            # Check for nested content
            if "data" in subject and isinstance(subject["data"], dict):
                for key in ["text", "content", "message", "description", "narrative", "output"]:
                    if key in subject["data"] and isinstance(subject["data"][key], str):
                        texts.append(subject["data"][key])
        
        # Extract text from context
        if context:
            if "text" in context and isinstance(context["text"], str):
                texts.append(context["text"])
            elif "content" in context and isinstance(context["content"], str):
                texts.append(context["content"])
            elif "message" in context and isinstance(context["message"], str):
                texts.append(context["message"])
            elif "description" in context and isinstance(context["description"], str):
                texts.append(context["description"])
        
        # Combine all extracted texts
        return " ".join(texts)
    
    def _detect_harm_in_text(self, text: str) -> Tuple[float, Dict[str, float]]:
        """
        Detect harmful content in text using keyword analysis.
        
        Args:
            text: Text to analyze for harmful content
            
        Returns:
            Tuple of (overall_harm_level, harm_categories)
        """
        if not text:
            return 0.0, {}
        
        # Normalize text
        text = text.lower()
        
        # Initialize harm scores for each category
        category_scores = {}
        
        # Check for keywords in each harm category
        for category, keywords in self.harm_detection_keywords.items():
            # Count occurrences of keywords
            keyword_counts = sum(text.count(keyword) for keyword in keywords)
            
            # Normalize by text length and keyword count
            text_length_factor = min(1.0, len(text) / 1000)  # Normalize for text length
            keyword_factor = min(1.0, len(keywords) / 10)    # Normalize for number of keywords
            
            # Calculate normalized score
            if keyword_counts > 0:
                category_score = min(1.0, keyword_counts / (10 * text_length_factor * keyword_factor))
                category_scores[category] = category_score
        
        # Calculate overall harm level as weighted average of category scores
        if category_scores:
            total_weight = sum(self.category_weights.get(cat, 1.0) for cat in category_scores)
            if total_weight > 0:
                overall_harm = sum(
                    score * self.category_weights.get(cat, 1.0)
                    for cat, score in category_scores.items()
                ) / total_weight
            else:
                overall_harm = sum(category_scores.values()) / len(category_scores)
        else:
            overall_harm = 0.0
        
        return overall_harm, category_scores
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'NonMaleficenceRule':
        """
        Create a non-maleficence rule from a dictionary.
        
        Args:
            data: Dictionary representation of a non-maleficence rule
            
        Returns:
            NonMaleficenceRule instance
        """
        # Extract required fields with defaults
        name = data.get("name", "unnamed_non_maleficence_rule")
        description = data.get("description", "")
        harm_categories = data.get("harm_categories")
        max_acceptable_harm = data.get("max_acceptable_harm", 0.2)
        harm_detection_keywords = data.get("harm_detection_keywords")
        
        # Create metadata if present
        metadata = None
        if "metadata" in data:
            metadata = EthicalRuleMetadata.from_dict(data["metadata"])
        
        # Create the rule (without evaluation_func for now)
        rule = cls(
            name=name,
            description=description,
            harm_categories=harm_categories,
            max_acceptable_harm=max_acceptable_harm,
            harm_detection_keywords=harm_detection_keywords,
            metadata=metadata
        )
        
        # If category weights are provided, update them
        if "category_weights" in data and isinstance(data["category_weights"], dict):
            for cat, weight in data["category_weights"].items():
                rule.category_weights[cat] = weight
        
        # If stats are provided, update them
        if "stats" in data and isinstance(data["stats"], dict):
            stats = data["stats"]
            rule.evaluation_count = stats.get("evaluation_count", 0)
            rule.violation_count = stats.get("violation_count", 0)
            rule.last_evaluation_time = stats.get("last_evaluation_time", 0.0)
        
        return rule
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the non-maleficence rule to a dictionary for serialization.
        
        Returns:
            Dictionary representation of the non-maleficence rule
        """
        data = super().to_dict()
        data.update({
            "harm_categories": self.harm_categories,
            "max_acceptable_harm": self.max_acceptable_harm,
            "category_weights": self.category_weights
        })
        
        # Only include harm_detection_keywords if it's not the default
        if self.harm_detection_keywords:
            data["harm_detection_keywords"] = self.harm_detection_keywords
        
        return data


class AutonomyRule(EthicalRule):
    """
    Ethical rule for respecting autonomy.
    
    Autonomy rules ensure that the system respects human agency and choice,
    avoiding coercion, manipulation, or undue influence.
    """
    
    def __init__(self, 
                name: str, 
                description: str,
                evaluation_func: Optional[Callable[[Any, Dict[str, Any]], Tuple[bool, float]]] = None,
                autonomy_dimensions: List[str] = None,
                min_autonomy_score: float = 0.7,
                consent_required: bool = True,
                manipulation_detection_threshold: float = 0.3,
                metadata: Optional[EthicalRuleMetadata] = None):
        """
        Initialize an autonomy rule.
        
        Args:
            name: Unique identifier for the rule
            description: Human-readable description of the rule
            evaluation_func: Function that evaluates if the rule is satisfied
            autonomy_dimensions: List of dimensions to evaluate for autonomy
            min_autonomy_score: Minimum acceptable autonomy score
            consent_required: Whether explicit consent is required
            manipulation_detection_threshold: Threshold for detecting manipulation
            metadata: Additional metadata for categorization and management
        """
        # Create default metadata if none provided
        if metadata is None:
            metadata = EthicalRuleMetadata(
                principle=EthicalPrincipleType.AUTONOMY,
                values=[ValueAlignmentType.FREEDOM, ValueAlignmentType.DIGNITY]
            )
        
        super().__init__(name, description, evaluation_func, metadata)
        self.autonomy_dimensions = autonomy_dimensions or [
            "informed_consent", "freedom_of_choice", "understanding",
            "voluntariness", "absence_of_coercion", "absence_of_manipulation",
            "capacity", "authenticity"
        ]
        self.min_autonomy_score = min_autonomy_score
        self.consent_required = consent_required
        self.manipulation_detection_threshold = manipulation_detection_threshold
        
        # Dimension weights (can be customized)
        self.dimension_weights = {dim: 1.0 for dim in self.autonomy_dimensions}
        
        # Give higher weight to critical dimensions
        for dim in ["informed_consent", "absence_of_coercion", "absence_of_manipulation"]:
            if dim in self.dimension_weights:
                self.dimension_weights[dim] = 1.5
    
    def evaluate_rule(self, subject: Any, context: Dict[str, Any]) -> Tuple[bool, float]:
        """
        Evaluate if the autonomy rule is satisfied.
        
        Args:
            subject: The subject to evaluate against the rule
            context: Additional context information for the evaluation
            
        Returns:
            Tuple of (is_satisfied, confidence_score)
        """
        # Explicitly check for consent first if required
        if self.consent_required:
            consent_given = self._check_consent(subject, context)
            if consent_given is False:  # Only fail if explicitly not given
                return False, 0.9  # High confidence when consent is explicitly denied
        
        # Extract autonomy scores from subject or context
        autonomy_scores = self._extract_autonomy_scores(subject, context)
        
        # Check for manipulation
        manipulation_detected, manipulation_score = self._detect_manipulation(subject, context)
        if manipulation_detected:
            # Add manipulation score to autonomy scores (inverted since higher manipulation = lower autonomy)
            autonomy_scores["absence_of_manipulation"] = 1.0 - manipulation_score
        
        # If no autonomy scores were found, try to calculate them
        if not autonomy_scores:
            autonomy_scores = self._calculate_autonomy_scores(subject, context)
        
        # If still no autonomy scores, check for overall autonomy score
        if not autonomy_scores:
            overall_score = self._extract_overall_autonomy(subject, context)
            if overall_score is not None:
                # Check if overall score meets minimum requirement
                is_satisfied = overall_score >= self.min_autonomy_score
                
                # Calculate confidence based on distance from threshold
                if is_satisfied:
                    confidence = min(1.0, 0.5 + (overall_score - self.min_autonomy_score) / (1.0 - self.min_autonomy_score) * 0.5)
                else:
                    confidence = max(0.0, 0.5 * overall_score / self.min_autonomy_score)
                
                return is_satisfied, confidence
            else:
                # No autonomy information available
                logger.debug(f"No autonomy information available for rule {self.name}")
                return True, 0.3  # Default to assuming autonomy is respected with low confidence
        
        # Calculate weighted average of autonomy scores
        total_weight = sum(self.dimension_weights.get(dim, 1.0) for dim in autonomy_scores)
        if total_weight <= 0:
            return True, 0.0  # No valid autonomy dimensions found
        
        weighted_score = sum(
            score * self.dimension_weights.get(dim, 1.0)
            for dim, score in autonomy_scores.items()
        ) / total_weight
        
        # Check if weighted score meets minimum requirement
        is_satisfied = weighted_score >= self.min_autonomy_score
        
        # Calculate confidence based on distance from threshold and number of dimensions
        dimension_coverage = len(autonomy_scores) / len(self.autonomy_dimensions)
        
        if is_satisfied:
            # Higher confidence when further above threshold and more dimensions covered
            score_factor = min(1.0, 0.5 + (weighted_score - self.min_autonomy_score) / (1.0 - self.min_autonomy_score) * 0.5)
            coverage_factor = 0.5 + dimension_coverage * 0.5
            confidence = score_factor * coverage_factor
        else:
            # Lower confidence when further below threshold
            confidence = max(0.0, 0.5 * weighted_score / self.min_autonomy_score)
        
        return is_satisfied, confidence
    
    def _check_consent(self, subject: Any, context: Dict[str, Any]) -> Optional[bool]:
        """
        Check if explicit consent is given.
        
        Args:
            subject: The subject to evaluate
            context: Additional context information
            
        Returns:
            Boolean indicating if consent is given, or None if unknown
        """
        # Check subject for consent information
        if isinstance(subject, dict):
            if "consent" in subject:
                consent = subject["consent"]
                if isinstance(consent, bool):
                    return consent
                elif isinstance(consent, str):
                    return consent.lower() in ["yes", "true", "given", "granted", "accepted"]
            
            if "consent_given" in subject:
                consent = subject["consent_given"]
                if isinstance(consent, bool):
                    return consent
                elif isinstance(consent, str):
                    return consent.lower() in ["yes", "true", "given", "granted", "accepted"]
            
            if "has_consent" in subject:
                consent = subject["has_consent"]
                if isinstance(consent, bool):
                    return consent
                elif isinstance(consent, str):
                    return consent.lower() in ["yes", "true", "given", "granted", "accepted"]
        
        # Check context for consent information
        if context:
            if "consent" in context:
                consent = context["consent"]
                if isinstance(consent, bool):
                    return consent
                elif isinstance(consent, str):
                    return consent.lower() in ["yes", "true", "given", "granted", "accepted"]
            
            if "consent_given" in context:
                consent = context["consent_given"]
                if isinstance(consent, bool):
                    return consent
                elif isinstance(consent, str):
                    return consent.lower() in ["yes", "true", "given", "granted", "accepted"]
            
            if "has_consent" in context:
                consent = context["has_consent"]
                if isinstance(consent, bool):
                    return consent
                elif isinstance(consent, str):
                    return consent.lower() in ["yes", "true", "given", "granted", "accepted"]
        
        # Consent information not found
        return None
    
    def _extract_autonomy_scores(self, subject: Any, context: Dict[str, Any]) -> Dict[str, float]:
        """
        Extract autonomy scores for different dimensions from subject or context.
        
        Args:
            subject: The subject to evaluate
            context: Additional context information
            
        Returns:
            Dictionary mapping autonomy dimensions to scores
        """
        autonomy_scores = {}
        
        # Check subject for autonomy scores
        if isinstance(subject, dict):
            # Look for autonomy scores in a nested structure
            if "autonomy" in subject and isinstance(subject["autonomy"], dict):
                autonomy = subject["autonomy"]
                for dim in self.autonomy_dimensions:
                    if dim in autonomy:
                        try:
                            autonomy_scores[dim] = float(autonomy[dim])
                        except (ValueError, TypeError):
                            pass
            
            # Look for autonomy scores directly in subject
            for dim in self.autonomy_dimensions:
                if dim in subject and dim not in autonomy_scores:
                    try:
                        autonomy_scores[dim] = float(subject[dim])
                    except (ValueError, TypeError):
                        pass
                
                # Try variations of dimension name
                for variant in [f"autonomy_{dim}", f"{dim}_score", f"{dim}_level"]:
                    if variant in subject and dim not in autonomy_scores:
                        try:
                            autonomy_scores[dim] = float(subject[variant])
                        except (ValueError, TypeError):
                            pass
            
            # Check for binary consent
            if "consent" in subject and "informed_consent" not in autonomy_scores:
                if isinstance(subject["consent"], bool):
                    autonomy_scores["informed_consent"] = 1.0 if subject["consent"] else 0.0
                elif isinstance(subject["consent"], str):
                    autonomy_scores["informed_consent"] = 1.0 if subject["consent"].lower() in ["yes", "true", "given"] else 0.0
        
        # Check context for autonomy scores
        if context:
            # Look for autonomy scores in a nested structure
            if "autonomy" in context and isinstance(context["autonomy"], dict):
                autonomy = context["autonomy"]
                for dim in self.autonomy_dimensions:
                    if dim in autonomy and dim not in autonomy_scores:
                        try:
                            autonomy_scores[dim] = float(autonomy[dim])
                        except (ValueError, TypeError):
                            pass
            
            # Look for autonomy scores directly in context
            for dim in self.autonomy_dimensions:
                if dim in context and dim not in autonomy_scores:
                    try:
                        autonomy_scores[dim] = float(context[dim])
                    except (ValueError, TypeError):
                        pass
                
                # Try variations of dimension name
                for variant in [f"autonomy_{dim}", f"{dim}_score", f"{dim}_level"]:
                    if variant in context and dim not in autonomy_scores:
                        try:
                            autonomy_scores[dim] = float(context[variant])
                        except (ValueError, TypeError):
                            pass
            
            # Check for binary consent
            if "consent" in context and "informed_consent" not in autonomy_scores:
                if isinstance(context["consent"], bool):
                    autonomy_scores["informed_consent"] = 1.0 if context["consent"] else 0.0
                elif isinstance(context["consent"], str):
                    autonomy_scores["informed_consent"] = 1.0 if context["consent"].lower() in ["yes", "true", "given"] else 0.0
        
        # Normalize scores to [0, 1] range
        for dim in list(autonomy_scores.keys()):
            score = autonomy_scores[dim]
            if score < 0 or score > 1:
                # Attempt to normalize
                if isinstance(score, (int, float)):
                    if score < 0:
                        autonomy_scores[dim] = 0.0
                    elif score > 1:
                        autonomy_scores[dim] = 1.0
                else:
                    # Remove non-numeric scores
                    del autonomy_scores[dim]
        
        return autonomy_scores
    
    def _detect_manipulation(self, subject: Any, context: Dict[str, Any]) -> Tuple[bool, float]:
        """
        Detect potential manipulation in subject or context.
        
        Args:
            subject: The subject to evaluate
            context: Additional context information
            
        Returns:
            Tuple of (manipulation_detected, manipulation_score)
        """
        # Check for explicit manipulation indicators
        if isinstance(subject, dict):
            if "manipulation" in subject:
                try:
                    manipulation = float(subject["manipulation"])
                    return manipulation > self.manipulation_detection_threshold, manipulation
                except (ValueError, TypeError):
                    if isinstance(subject["manipulation"], bool):
                        return subject["manipulation"], 0.8 if subject["manipulation"] else 0.0
                    
                    if "manipulation_score" in subject:
                        try:
                            manipulation = float(subject["manipulation_score"])
                            return manipulation > self.manipulation_detection_threshold, manipulation
                        except (ValueError, TypeError):
                            pass
                    
                    if "is_manipulative" in subject:
                        if isinstance(subject["is_manipulative"], bool):
                            return subject["is_manipulative"], 0.8 if subject["is_manipulative"] else 0.0
        
        # Check context for manipulation indicators
        if context:
            if "manipulation" in context:
                try:
                    manipulation = float(context["manipulation"])
                    return manipulation > self.manipulation_detection_threshold, manipulation
                except (ValueError, TypeError):
                    if isinstance(context["manipulation"], bool):
                        return context["manipulation"], 0.8 if context["manipulation"] else 0.0
            
            if "manipulation_score" in context:
                try:
                    manipulation = float(context["manipulation_score"])
                    return manipulation > self.manipulation_detection_threshold, manipulation
                except (ValueError, TypeError):
                    pass
            
            if "is_manipulative" in context:
                if isinstance(context["is_manipulative"], bool):
                    return context["is_manipulative"], 0.8 if context["is_manipulative"] else 0.0
        
        # Check textual content for manipulative language patterns
        text_content = self._extract_text_content(subject, context)
        if text_content:
            manipulation_score = self._detect_manipulation_in_text(text_content)
            return manipulation_score > self.manipulation_detection_threshold, manipulation_score
        
        # No manipulation detected or insufficient information
        return False, 0.0
    
    def _extract_text_content(self, subject: Any, context: Dict[str, Any]) -> str:
        """
        Extract textual content from subject or context for analysis.
        
        Args:
            subject: The subject to evaluate
            context: Additional context information
            
        Returns:
            String containing relevant text content
        """
        texts = []
        
        # Extract text from subject
        if isinstance(subject, str):
            texts.append(subject)
        elif isinstance(subject, dict):
            # Check common text fields
            for key in ["text", "content", "message", "description", "narrative", "output"]:
                if key in subject and isinstance(subject[key], str):
                    texts.append(subject[key])
            
            # Check for nested content
            if "data" in subject and isinstance(subject["data"], dict):
                for key in ["text", "content", "message", "description", "narrative", "output"]:
                    if key in subject["data"] and isinstance(subject["data"][key], str):
                        texts.append(subject["data"][key])
        
        # Extract text from context
        if context:
            if isinstance(context, str):
                texts.append(context)
            elif isinstance(context, dict):
                for key in ["text", "content", "message", "description", "narrative", "output"]:
                    if key in context and isinstance(context[key], str):
                        texts.append(context[key])
        
        # Combine all extracted texts
        return " ".join(texts)
    
    def _detect_manipulation_in_text(self, text: str) -> float:
        """
        Detect manipulative content in text.
        
        Args:
            text: Text to analyze for manipulative content
            
        Returns:
            Float indicating manipulation score between 0 and 1
        """
        if not text:
            return 0.0
        
        # Normalize text
        text = text.lower()
        
        # Define manipulation indicators (keyword-based approach)
        manipulation_indicators = {
            "urgency": ["limited time", "act now", "urgent", "hurry", "running out", "last chance", "deadline", "immediately"],
            "scarcity": ["limited availability", "exclusive", "rare opportunity", "only a few", "while supplies last"],
            "social_pressure": ["everyone is", "other people", "popular choice", "most people", "join others", "don't miss out"],
            "authority_claims": ["expert", "scientific", "proven", "guaranteed", "certified", "official", "research shows"],
            "emotional_manipulation": ["you owe", "you should feel", "guilt", "shame", "fear", "regret"],
            "false_causality": ["this means", "this proves", "clearly shows", "obviously", "without a doubt"],
            "forced_choices": ["only choice", "no alternative", "must choose", "either or", "no other way"],
            "hidden_information": ["fine print", "terms apply", "restrictions may", "not disclosed"]
        }
        
        # Calculate manipulation score based on indicators
        category_scores = {}
        for category, keywords in manipulation_indicators.items():
            # Count occurrences of keywords
            category_count = sum(text.count(keyword) for keyword in keywords)
            text_length = max(100, len(text))  # Normalize by text length
            
            # Calculate normalized score
            if category_count > 0:
                category_score = min(1.0, category_count / (text_length / 100))
                category_scores[category] = category_score
        
        # Calculate overall manipulation score
        if category_scores:
            # Weight certain categories higher
            category_weights = {
                "urgency": 1.2,
                "scarcity": 1.2,
                "social_pressure": 1.0,
                "authority_claims": 0.8,
                "emotional_manipulation": 1.5,
                "false_causality": 1.0,
                "forced_choices": 1.3,
                "hidden_information": 1.4
            }
            
            weighted_sum = sum(
                score * category_weights.get(category, 1.0)
                for category, score in category_scores.items()
            )
            total_weight = sum(
                category_weights.get(category, 1.0)
                for category in category_scores
            )
            
            if total_weight > 0:
                return min(1.0, weighted_sum / total_weight)
        
        return 0.0
    
    def _calculate_autonomy_scores(self, subject: Any, context: Dict[str, Any]) -> Dict[str, float]:
        """
        Calculate autonomy scores based on available information.
        
        Args:
            subject: The subject to evaluate
            context: Additional context information
            
        Returns:
            Dictionary mapping autonomy dimensions to scores
        """
        autonomy_scores = {}
        
        # Check for consent-related indicators
        consent_status = self._check_consent(subject, context)
        if consent_status is not None:
            autonomy_scores["informed_consent"] = 1.0 if consent_status else 0.0
        
        # Check for coercion or manipulation
        manipulation_detected, manipulation_score = self._detect_manipulation(subject, context)
        if manipulation_detected:
            autonomy_scores["absence_of_manipulation"] = 1.0 - manipulation_score
        
        # Check for voluntariness
        if isinstance(subject, dict) and "voluntary" in subject:
            if isinstance(subject["voluntary"], bool):
                autonomy_scores["voluntariness"] = 1.0 if subject["voluntary"] else 0.0
            elif isinstance(subject["voluntary"], (int, float)):
                autonomy_scores["voluntariness"] = min(1.0, max(0.0, float(subject["voluntary"])))
        elif context and "voluntary" in context:
            if isinstance(context["voluntary"], bool):
                autonomy_scores["voluntariness"] = 1.0 if context["voluntary"] else 0.0
            elif isinstance(context["voluntary"], (int, float)):
                autonomy_scores["voluntariness"] = min(1.0, max(0.0, float(context["voluntary"])))
        
        # Check for understanding
        if isinstance(subject, dict) and "understanding" in subject:
            if isinstance(subject["understanding"], (int, float)):
                autonomy_scores["understanding"] = min(1.0, max(0.0, float(subject["understanding"])))
        elif context and "understanding" in context:
            if isinstance(context["understanding"], (int, float)):
                autonomy_scores["understanding"] = min(1.0, max(0.0, float(context["understanding"])))
        
        # Check for freedom of choice
        if isinstance(subject, dict) and "freedom_of_choice" in subject:
            if isinstance(subject["freedom_of_choice"], (int, float)):
                autonomy_scores["freedom_of_choice"] = min(1.0, max(0.0, float(subject["freedom_of_choice"])))
        elif context and "freedom_of_choice" in context:
            if isinstance(context["freedom_of_choice"], (int, float)):
                autonomy_scores["freedom_of_choice"] = min(1.0, max(0.0, float(context["freedom_of_choice"])))
        
        return autonomy_scores
    
    def _extract_overall_autonomy(self, subject: Any, context: Dict[str, Any]) -> Optional[float]:
        """
        Extract overall autonomy score from subject or context.
        
        Args:
            subject: The subject to evaluate
            context: Additional context information
            
        Returns:
            Float representing overall autonomy score, or None if not available
        """
        # Check subject for overall autonomy
        if isinstance(subject, dict):
            for key in ["autonomy", "autonomy_score", "autonomy_level", "respect_for_autonomy"]:
                if key in subject:
                    try:
                        return float(subject[key])
                    except (ValueError, TypeError):
                        pass
        
        # Check context for overall autonomy
        if context:
            for key in ["autonomy", "autonomy_score", "autonomy_level", "respect_for_autonomy"]:
                if key in context:
                    try:
                        return float(context[key])
                    except (ValueError, TypeError):
                        pass
        
        return None
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AutonomyRule':
        """
        Create an autonomy rule from a dictionary.
        
        Args:
            data: Dictionary representation of an autonomy rule
            
        Returns:
            AutonomyRule instance
        """
        # Extract required fields with defaults
        name = data.get("name", "unnamed_autonomy_rule")
        description = data.get("description", "")
        autonomy_dimensions = data.get("autonomy_dimensions")
        min_autonomy_score = data.get("min_autonomy_score", 0.7)
        consent_required = data.get("consent_required", True)
        manipulation_detection_threshold = data.get("manipulation_detection_threshold", 0.3)
        
        # Create metadata if present
        metadata = None
        if "metadata" in data:
            metadata = EthicalRuleMetadata.from_dict(data["metadata"])
        
        # Create the rule (without evaluation_func for now)
        rule = cls(
            name=name,
            description=description,
            autonomy_dimensions=autonomy_dimensions,
            min_autonomy_score=min_autonomy_score,
            consent_required=consent_required,
            manipulation_detection_threshold=manipulation_detection_threshold,
            metadata=metadata
        )
        
        # If dimension weights are provided, update them
        if "dimension_weights" in data and isinstance(data["dimension_weights"], dict):
            for dim, weight in data["dimension_weights"].items():
                rule.dimension_weights[dim] = weight
        
        # If stats are provided, update them
        if "stats" in data and isinstance(data["stats"], dict):
            stats = data["stats"]
            rule.evaluation_count = stats.get("evaluation_count", 0)
            rule.violation_count = stats.get("violation_count", 0)
            rule.last_evaluation_time = stats.get("last_evaluation_time", 0.0)
        
        return rule
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the autonomy rule to a dictionary for serialization.
        
        Returns:
            Dictionary representation of the autonomy rule
        """
        data = super().to_dict()
        data.update({
            "autonomy_dimensions": self.autonomy_dimensions,
            "min_autonomy_score": self.min_autonomy_score,
            "consent_required": self.consent_required,
            "manipulation_detection_threshold": self.manipulation_detection_threshold,
            "dimension_weights": self.dimension_weights
        })
        return data


class TransparencyRule(EthicalRule):
    """
    Ethical rule for transparency.
    
    Transparency rules ensure that the system's operations, decisions, and
    limitations are clear and understandable to users and stakeholders.
    """
    
    def __init__(self, 
                name: str, 
                description: str,
                evaluation_func: Optional[Callable[[Any, Dict[str, Any]], Tuple[bool, float]]] = None,
                transparency_aspects: List[str] = None,
                min_transparency_score: float = 0.6,
                explainability_required: bool = True,
                metadata: Optional[EthicalRuleMetadata] = None):
        """
        Initialize a transparency rule.
        
        Args:
            name: Unique identifier for the rule
            description: Human-readable description of the rule
            evaluation_func: Function that evaluates if the rule is satisfied
            transparency_aspects: List of aspects to evaluate for transparency
            min_transparency_score: Minimum acceptable transparency score
            explainability_required: Whether explanations are required
            metadata: Additional metadata for categorization and management
        """
        # Create default metadata if none provided
        if metadata is None:
            metadata = EthicalRuleMetadata(
                principle=EthicalPrincipleType.TRANSPARENCY,
                values=[ValueAlignmentType.TRUST, ValueAlignmentType.ACCOUNTABILITY]
            )
        
        super().__init__(name, description, evaluation_func, metadata)
        self.transparency_aspects = transparency_aspects or [
            "explainability", "interpretability", "disclosure_of_limitations",
            "clarity_of_purpose", "visibility_of_process", "accessibility_of_information",
            "traceability", "verifiability"
        ]
        self.min_transparency_score = min_transparency_score
        self.explainability_required = explainability_required
        
        # Aspect weights (can be customized)
        self.aspect_weights = {aspect: 1.0 for aspect in self.transparency_aspects}
        
        # Give higher weight to critical aspects
        for aspect in ["explainability", "disclosure_of_limitations"]:
            if aspect in self.aspect_weights:
                self.aspect_weights[aspect] = 1.5
    
    def evaluate_rule(self, subject: Any, context: Dict[str, Any]) -> Tuple[bool, float]:
        """
        Evaluate if the transparency rule is satisfied.
        
        Args:
            subject: The subject to evaluate against the rule
            context: Additional context information for the evaluation
            
        Returns:
            Tuple of (is_satisfied, confidence_score)
        """
        # Explicitly check for explainability if required
        if self.explainability_required:
            explanation_provided = self._check_explanation(subject, context)
            if explanation_provided is False:  # Only fail if explicitly not provided
                return False, 0.8  # High confidence when explanation is explicitly missing
        
        # Extract transparency scores from subject or context
        transparency_scores = self._extract_transparency_scores(subject, context)
        
        # If no transparency scores were found, try to calculate them
        if not transparency_scores:
            transparency_scores = self._calculate_transparency_scores(subject, context)
        
        # If still no transparency scores, check for overall transparency score
        if not transparency_scores:
            overall_score = self._extract_overall_transparency(subject, context)
            if overall_score is not None:
                # Check if overall score meets minimum requirement
                is_satisfied = overall_score >= self.min_transparency_score
                
                # Calculate confidence based on distance from threshold
                if is_satisfied:
                    confidence = min(1.0, 0.5 + (overall_score - self.min_transparency_score) / (1.0 - self.min_transparency_score) * 0.5)
                else:
                    confidence = max(0.0, 0.5 * overall_score / self.min_transparency_score)
                
                return is_satisfied, confidence
            else:
                # No transparency information available
                logger.debug(f"No transparency information available for rule {self.name}")
                return False, 0.3  # Default to assuming transparency is lacking with low confidence
        
        # Calculate weighted average of transparency scores
        total_weight = sum(self.aspect_weights.get(aspect, 1.0) for aspect in transparency_scores)
        if total_weight <= 0:
            return False, 0.0  # No valid transparency aspects found
        
        weighted_score = sum(
            score * self.aspect_weights.get(aspect, 1.0)
            for aspect, score in transparency_scores.items()
        ) / total_weight
        
        # Check if weighted score meets minimum requirement
        is_satisfied = weighted_score >= self.min_transparency_score
        
        # Calculate confidence based on distance from threshold and number of aspects
        aspect_coverage = len(transparency_scores) / len(self.transparency_aspects)
        
        if is_satisfied:
            # Higher confidence when further above threshold and more aspects covered
            score_factor = min(1.0, 0.5 + (weighted_score - self.min_transparency_score) / (1.0 - self.min_transparency_score) * 0.5)
            coverage_factor = 0.5 + aspect_coverage * 0.5
            confidence = score_factor * coverage_factor
        else:
            # Lower confidence when further below threshold
            confidence = max(0.0, 0.5 * weighted_score / self.min_transparency_score)
        
        return is_satisfied, confidence
    
    def _check_explanation(self, subject: Any, context: Dict[str, Any]) -> Optional[bool]:
        """
        Check if an explanation is provided.
        
        Args:
            subject: The subject to evaluate
            context: Additional context information
            
        Returns:
            Boolean indicating if explanation is provided, or None if unknown
        """
        # Check subject for explanation
        if isinstance(subject, dict):
            if "explanation" in subject:
                explanation = subject["explanation"]
                if isinstance(explanation, str):
                    # Check if explanation is non-empty
                    return bool(explanation.strip())
                elif isinstance(explanation, bool):
                    return explanation
            
            # Check for explanation in nested data
            if "data" in subject and isinstance(subject["data"], dict) and "explanation" in subject["data"]:
                explanation = subject["data"]["explanation"]
                if isinstance(explanation, str):
                    return bool(explanation.strip())
                elif isinstance(explanation, bool):
                    return explanation
        
        # Check context for explanation
        if context:
            if "explanation" in context:
                explanation = context["explanation"]
                if isinstance(explanation, str):
                    return bool(explanation.strip())
                elif isinstance(explanation, bool):
                    return explanation
            
            # Check for explanation engine output
            if "explanation_engine" in context:
                explanation_engine = context["explanation_engine"]
                if isinstance(explanation_engine, dict) and "output" in explanation_engine:
                    return bool(explanation_engine["output"])
        
        # Explanation information not found
        return None
    
    def _extract_transparency_scores(self, subject: Any, context: Dict[str, Any]) -> Dict[str, float]:
        """
        Extract transparency scores for different aspects from subject or context.
        
        Args:
            subject: The subject to evaluate
            context: Additional context information
            
        Returns:
            Dictionary mapping transparency aspects to scores
        """
        transparency_scores = {}
        
        # Check subject for transparency scores
        if isinstance(subject, dict):
            # Look for transparency scores in a nested structure
            if "transparency" in subject and isinstance(subject["transparency"], dict):
                transparency = subject["transparency"]
                for aspect in self.transparency_aspects:
                    if aspect in transparency:
                        try:
                            transparency_scores[aspect] = float(transparency[aspect])
                        except (ValueError, TypeError):
                            pass
            
            # Look for transparency scores directly in subject
            for aspect in self.transparency_aspects:
                if aspect in subject and aspect not in transparency_scores:
                    try:
                        transparency_scores[aspect] = float(subject[aspect])
                    except (ValueError, TypeError):
                        pass
                
                # Try variations of aspect name
                for variant in [f"transparency_{aspect}", f"{aspect}_score", f"{aspect}_level"]:
                    if variant in subject and aspect not in transparency_scores:
                        try:
                            transparency_scores[aspect] = float(subject[variant])
                        except (ValueError, TypeError):
                            pass
            
            # Check specifically for explainability
            if "explainable" in subject and "explainability" not in transparency_scores:
                if isinstance(subject["explainable"], bool):
                    transparency_scores["explainability"] = 1.0 if subject["explainable"] else 0.0
                elif isinstance(subject["explainable"], (int, float)):
                    transparency_scores["explainability"] = min(1.0, max(0.0, float(subject["explainable"])))
        
        # Check context for transparency scores
        if context:
            # Look for transparency scores in a nested structure
            if "transparency" in context and isinstance(context["transparency"], dict):
                transparency = context["transparency"]
                for aspect in self.transparency_aspects:
                    if aspect in transparency and aspect not in transparency_scores:
                        try:
                            transparency_scores[aspect] = float(transparency[aspect])
                        except (ValueError, TypeError):
                            pass
            
            # Look for transparency scores directly in context
            for aspect in self.transparency_aspects:
                if aspect in context and aspect not in transparency_scores:
                    try:
                        transparency_scores[aspect] = float(context[aspect])
                    except (ValueError, TypeError):
                        pass
                
                # Try variations of aspect name
                for variant in [f"transparency_{aspect}", f"{aspect}_score", f"{aspect}_level"]:
                    if variant in context and aspect not in transparency_scores:
                        try:
                            transparency_scores[aspect] = float(context[variant])
                        except (ValueError, TypeError):
                            pass
            
            # Check specifically for explainability
            if "explainable" in context and "explainability" not in transparency_scores:
                if isinstance(context["explainable"], bool):
                    transparency_scores["explainability"] = 1.0 if context["explainable"] else 0.0
                elif isinstance(context["explainable"], (int, float)):
                    transparency_scores["explainability"] = min(1.0, max(0.0, float(context["explainable"])))
        
        # Normalize scores to [0, 1] range
        for aspect in list(transparency_scores.keys()):
            score = transparency_scores[aspect]
            if score < 0 or score > 1:
                # Attempt to normalize
                if isinstance(score, (int, float)):
                    if score < 0:
                        transparency_scores[aspect] = 0.0
                    elif score > 1:
                        transparency_scores[aspect] = 1.0
                else:
                    # Remove non-numeric scores
                    del transparency_scores[aspect]
        
        return transparency_scores
    
    def _calculate_transparency_scores(self, subject: Any, context: Dict[str, Any]) -> Dict[str, float]:
        """
        Calculate transparency scores based on available information.
        
        Args:
            subject: The subject to evaluate
            context: Additional context information
            
        Returns:
            Dictionary mapping transparency aspects to scores
        """
        transparency_scores = {}
        
        # Check for explanation
        explanation_provided = self._check_explanation(subject, context)
        if explanation_provided is not None:
            transparency_scores["explainability"] = 1.0 if explanation_provided else 0.0
        
        # Check for disclosure of limitations
        limitations_disclosed = self._check_limitations_disclosure(subject, context)
        if limitations_disclosed is not None:
            transparency_scores["disclosure_of_limitations"] = 1.0 if limitations_disclosed else 0.0
        
        # Calculate interpretability
        interpretability_score = self._calculate_interpretability(subject, context)
        if interpretability_score is not None:
            transparency_scores["interpretability"] = interpretability_score
        
        # Check for clarity of purpose
        purpose_clarity = self._check_purpose_clarity(subject, context)
        if purpose_clarity is not None:
            transparency_scores["clarity_of_purpose"] = purpose_clarity
        
        # Check for process visibility
        process_visibility = self._check_process_visibility(subject, context)
        if process_visibility is not None:
            transparency_scores["visibility_of_process"] = process_visibility
        
        return transparency_scores
    
    def _check_limitations_disclosure(self, subject: Any, context: Dict[str, Any]) -> Optional[bool]:
        """
        Check if limitations are disclosed.
        
        Args:
            subject: The subject to evaluate
            context: Additional context information
            
        Returns:
            Boolean indicating if limitations are disclosed, or None if unknown
        """
        # Check subject for limitations disclosure
        if isinstance(subject, dict):
            if "limitations" in subject:
                limitations = subject["limitations"]
                if isinstance(limitations, list):
                    return len(limitations) > 0
                elif isinstance(limitations, str):
                    return bool(limitations.strip())
                elif isinstance(limitations, bool):
                    return limitations
            
            if "limitations_disclosed" in subject:
                return bool(subject["limitations_disclosed"])
            
            # Check for nested limitations
            if "data" in subject and isinstance(subject["data"], dict) and "limitations" in subject["data"]:
                limitations = subject["data"]["limitations"]
                if isinstance(limitations, list):
                    return len(limitations) > 0
                elif isinstance(limitations, str):
                    return bool(limitations.strip())
                elif isinstance(limitations, bool):
                    return limitations
        
        # Check context for limitations disclosure
        if context:
            if "limitations" in context:
                limitations = context["limitations"]
                if isinstance(limitations, list):
                    return len(limitations) > 0
                elif isinstance(limitations, str):
                    return bool(limitations.strip())
                elif isinstance(limitations, bool):
                    return limitations
            
            if "limitations_disclosed" in context:
                return bool(context["limitations_disclosed"])
        
        # Limitations disclosure information not found
        return None
    
    def _calculate_interpretability(self, subject: Any, context: Dict[str, Any]) -> Optional[float]:
        """
        Calculate interpretability score.
        
        Args:
            subject: The subject to evaluate
            context: Additional context information
            
        Returns:
            Float representing interpretability score, or None if not calculable
        """
        # Check for direct interpretability scores
        if isinstance(subject, dict):
            if "interpretability" in subject:
                try:
                    return float(subject["interpretability"])
                except (ValueError, TypeError):
                    pass
            
            if "interpretable" in subject:
                if isinstance(subject["interpretable"], bool):
                    return 1.0 if subject["interpretable"] else 0.0
                try:
                    return float(subject["interpretable"])
                except (ValueError, TypeError):
                    pass
        
        if context:
            if "interpretability" in context:
                try:
                    return float(context["interpretability"])
                except (ValueError, TypeError):
                    pass
            
            if "interpretable" in context:
                if isinstance(context["interpretable"], bool):
                    return 1.0 if context["interpretable"] else 0.0
                try:
                    return float(context["interpretable"])
                except (ValueError, TypeError):
                    pass
        
        # Check for model complexity information
        model_complexity = None
        
        if isinstance(subject, dict) and "model_complexity" in subject:
            try:
                model_complexity = float(subject["model_complexity"])
            except (ValueError, TypeError):
                pass
        
        if model_complexity is None and context and "model_complexity" in context:
            try:
                model_complexity = float(context["model_complexity"])
            except (ValueError, TypeError):
                pass
        
        # If model complexity is available, convert to interpretability score
        if model_complexity is not None:
            # Assuming model_complexity is between 0 and 1, where 0 is simple and 1 is complex
            return 1.0 - model_complexity
        
        # Check for model type information
        model_type = None
        
        if isinstance(subject, dict) and "model_type" in subject:
            model_type = subject["model_type"]
        
        if model_type is None and context and "model_type" in context:
            model_type = context["model_type"]
        
        # If model type is available, estimate interpretability based on model type
        if model_type is not None:
            if isinstance(model_type, str):
                model_type = model_type.lower()
                # Assign interpretability scores based on model type
                if model_type in ["linear", "logistic", "decision_tree", "rule_based"]:
                    return 0.9  # Highly interpretable
                elif model_type in ["random_forest", "xgboost", "lightgbm", "svm"]:
                    return 0.6  # Moderately interpretable
                elif model_type in ["neural_network", "deep_learning", "transformer", "diffusion"]:
                    return 0.3  # Less interpretable
                else:
                    return 0.5  # Unknown model type
        
        # Cannot calculate interpretability
        return None
    
    def _check_purpose_clarity(self, subject: Any, context: Dict[str, Any]) -> Optional[float]:
        """
        Check clarity of purpose.
        
        Args:
            subject: The subject to evaluate
            context: Additional context information
            
        Returns:
            Float representing clarity of purpose score, or None if not available
        """
        # Check for direct purpose clarity scores
        if isinstance(subject, dict):
            if "clarity_of_purpose" in subject:
                try:
                    return float(subject["clarity_of_purpose"])
                except (ValueError, TypeError):
                    pass
            
            if "purpose_clarity" in subject:
                try:
                    return float(subject["purpose_clarity"])
                except (ValueError, TypeError):
                    pass
        
        if context:
            if "clarity_of_purpose" in context:
                try:
                    return float(context["clarity_of_purpose"])
                except (ValueError, TypeError):
                    pass
            
            if "purpose_clarity" in context:
                try:
                    return float(context["purpose_clarity"])
                except (ValueError, TypeError):
                    pass
        
        # Check if purpose is explicitly stated
        purpose_stated = False
        purpose_text = ""
        
        if isinstance(subject, dict):
            if "purpose" in subject:
                purpose = subject["purpose"]
                if isinstance(purpose, str):
                    purpose_stated = bool(purpose.strip())
                    purpose_text = purpose
                else:
                    purpose_stated = True
        
        if not purpose_stated and context:
            if "purpose" in context:
                purpose = context["purpose"]
                if isinstance(purpose, str):
                    purpose_stated = bool(purpose.strip())
                    purpose_text = purpose
                else:
                    purpose_stated = True
        
        # If purpose is stated, evaluate its clarity
        if purpose_stated:
            if not purpose_text:
                return 0.8  # Purpose is stated but not available as text
            
            # Basic heuristic for purpose clarity based on text length and complexity
            purpose_length = len(purpose_text.split())
            if purpose_length < 5:
                return 0.4  # Too short to be clear
            elif purpose_length > 100:
                return 0.6  # Too long, might be unclear
            else:
                return 0.9  # Reasonable length for clear purpose
        
        # Cannot determine purpose clarity
        return None
    
    def _check_process_visibility(self, subject: Any, context: Dict[str, Any]) -> Optional[float]:
        """
        Check visibility of process.
        
        Args:
            subject: The subject to evaluate
            context: Additional context information
            
        Returns:
            Float representing process visibility score, or None if not available
        """
        # Check for direct process visibility scores
        if isinstance(subject, dict):
            if "visibility_of_process" in subject:
                try:
                    return float(subject["visibility_of_process"])
                except (ValueError, TypeError):
                    pass
            
            if "process_visibility" in subject:
                try:
                    return float(subject["process_visibility"])
                except (ValueError, TypeError):
                    pass
        
        if context:
            if "visibility_of_process" in context:
                try:
                    return float(context["visibility_of_process"])
                except (ValueError, TypeError):
                    pass
            
            if "process_visibility" in context:
                try:
                    return float(context["process_visibility"])
                except (ValueError, TypeError):
                    pass
        
        # Check if process description is available
        process_described = False
        process_steps = []
        
        if isinstance(subject, dict):
            if "process" in subject:
                process = subject["process"]
                if isinstance(process, str):
                    process_described = bool(process.strip())
                elif isinstance(process, list):
                    process_described = len(process) > 0
                    process_steps = process
                else:
                    process_described = True
            
            # Check for steps or methodology
            if "steps" in subject:
                steps = subject["steps"]
                if isinstance(steps, list):
                    process_described = True
                    process_steps = steps
            
            if "methodology" in subject:
                process_described = True
        
        if not process_described and context:
            if "process" in context:
                process = context["process"]
                if isinstance(process, str):
                    process_described = bool(process.strip())
                elif isinstance(process, list):
                    process_described = len(process) > 0
                    process_steps = process
                else:
                    process_described = True
            
            # Check for steps or methodology
            if "steps" in context:
                steps = context["steps"]
                if isinstance(steps, list):
                    process_described = True
                    process_steps = steps
            
            if "methodology" in context:
                process_described = True
        
        # If process is described, evaluate its visibility
        if process_described:
            if process_steps:
                return min(1.0, 0.5 + len(process_steps) * 0.1)  # More steps = more visibility
            else:
                return 0.7  # Process is described but not as explicit steps
        
        # Cannot determine process visibility
        return None
    
    def _extract_overall_transparency(self, subject: Any, context: Dict[str, Any]) -> Optional[float]:
        """
        Extract overall transparency score from subject or context.
        
        Args:
            subject: The subject to evaluate
            context: Additional context information
            
        Returns:
            Float representing overall transparency score, or None if not available
        """
        # Check subject for overall transparency
        if isinstance(subject, dict):
            for key in ["transparency", "transparency_score", "transparency_level"]:
                if key in subject:
                    try:
                        return float(subject[key])
                    except (ValueError, TypeError):
                        pass
        
        # Check context for overall transparency
        if context:
            for key in ["transparency", "transparency_score", "transparency_level"]:
                if key in context:
                    try:
                        return float(context[key])
                    except (ValueError, TypeError):
                        pass
        
        return None
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TransparencyRule':
        """
        Create a transparency rule from a dictionary.
        
        Args:
            data: Dictionary representation of a transparency rule
            
        Returns:
            TransparencyRule instance
        """
        # Extract required fields with defaults
        name = data.get("name", "unnamed_transparency_rule")
        description = data.get("description", "")
        transparency_aspects = data.get("transparency_aspects")
        min_transparency_score = data.get("min_transparency_score", 0.6)
        explainability_required = data.get("explainability_required", True)
        
        # Create metadata if present
        metadata = None
        if "metadata" in data:
            metadata = EthicalRuleMetadata.from_dict(data["metadata"])
        
        # Create the rule (without evaluation_func for now)
        rule = cls(
            name=name,
            description=description,
            transparency_aspects=transparency_aspects,
            min_transparency_score=min_transparency_score,
            explainability_required=explainability_required,
            metadata=metadata
        )
        
        # If aspect weights are provided, update them
        if "aspect_weights" in data and isinstance(data["aspect_weights"], dict):
            for aspect, weight in data["aspect_weights"].items():
                rule.aspect_weights[aspect] = weight
        
        # If stats are provided, update them
        if "stats" in data and isinstance(data["stats"], dict):
            stats = data["stats"]
            rule.evaluation_count = stats.get("evaluation_count", 0)
            rule.violation_count = stats.get("violation_count", 0)
            rule.last_evaluation_time = stats.get("last_evaluation_time", 0.0)
        
        return rule
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the transparency rule to a dictionary for serialization.
        
        Returns:
            Dictionary representation of the transparency rule
        """
        data = super().to_dict()
        data.update({
            "transparency_aspects": self.transparency_aspects,
            "min_transparency_score": self.min_transparency_score,
            "explainability_required": self.explainability_required,
            "aspect_weights": self.aspect_weights
        })
        return data


class EthicalRules:
    """
    Manages a collection of ethical rules and provides access to them.
    
    The EthicalRules class is responsible for:
    - Storing and organizing ethical rules
    - Providing access to rules by name, principle, or other criteria
    - Loading rules from configuration
    - Evaluating rules against subjects (actions, decisions, etc.)
    - Tracking rule statistics
    """
    
    def __init__(self, rules_path: Optional[str] = None):
        """
        Initialize an ethical rules manager.
        
        Args:
            rules_path: Optional path to load ethical rules from
        """
        self.rules = {}  # Dict of name -> rule
        self.rule_types = {
            "FairnessRule": FairnessRule,
            "BeneficenceRule": BeneficenceRule,
            "NonMaleficenceRule": NonMaleficenceRule,
            "AutonomyRule": AutonomyRule,
            "TransparencyRule": TransparencyRule
        }
        self._lock = threading.RLock()  # For thread safety
        
        # Load rules if path is provided
        if rules_path and os.path.exists(rules_path):
            self.load_rules(rules_path)
    
    def add_rule(self, rule: EthicalRule) -> bool:
        """
        Add an ethical rule to the manager.
        
        Args:
            rule: The ethical rule to add
            
        Returns:
            True if the rule was successfully added, False otherwise
        """
        with self._lock:
            if rule.name in self.rules:
                logger.warning(f"Rule with name '{rule.name}' already exists, replacing")
            
            self.rules[rule.name] = rule
            return True
    
    def remove_rule(self, name: str) -> bool:
        """
        Remove an ethical rule from the manager.
        
        Args:
            name: The name of the rule to remove
            
        Returns:
            True if the rule was successfully removed, False otherwise
        """
        with self._lock:
            if name in self.rules:
                del self.rules[name]
                return True
            else:
                return False
    
    def get_rule(self, name: str) -> Optional[EthicalRule]:
        """
        Get an ethical rule by name.
        
        Args:
            name: The name of the rule to get
            
        Returns:
            The ethical rule with the given name, or None if not found
        """
        with self._lock:
            return self.rules.get(name)
    
    def get_all_rules(self) -> List[EthicalRule]:
        """
        Get all ethical rules managed by this manager.
        
        Returns:
            List of all ethical rules
        """
        with self._lock:
            return list(self.rules.values())
    
    def count_rules(self) -> int:
        """
        Get the number of ethical rules managed by this manager.
        
        Returns:
            The number of ethical rules
        """
        with self._lock:
            return len(self.rules)
    
    def get_rules_by_principle(self, principle: Union[EthicalPrincipleType, str]) -> List[EthicalRule]:
        """
        Get ethical rules for the specified principle.
        
        Args:
            principle: The ethical principle to filter by
            
        Returns:
            List of rules for the specified principle
        """
        # Convert string to EthicalPrincipleType enum if needed
        if isinstance(principle, str):
            try:
                principle = EthicalPrincipleType(principle)
            except ValueError:
                logger.warning(f"Invalid ethical principle '{principle}'")
                return []
        
        with self._lock:
            return [r for r in self.rules.values() if r.metadata.principle == principle]
    
    def get_rules_by_value(self, value: Union[ValueAlignmentType, str]) -> List[EthicalRule]:
        """
        Get ethical rules aligned with the specified value.
        
        Args:
            value: The value alignment to filter by
            
        Returns:
            List of rules aligned with the specified value
        """
        # Convert string to ValueAlignmentType enum if needed
        if isinstance(value, str):
            try:
                value = ValueAlignmentType(value)
            except ValueError:
                logger.warning(f"Invalid value alignment type '{value}'")
                return []
        
        with self._lock:
            return [r for r in self.rules.values() if value in r.metadata.values]
    
    def get_rules_by_type(self, rule_type: str) -> List[EthicalRule]:
        """
        Get ethical rules of the specified type.
        
        Args:
            rule_type: The rule type to filter by
            
        Returns:
            List of rules of the specified type
        """
        with self._lock:
            return [r for r in self.rules.values() if r.__class__.__name__ == rule_type]
    
    def get_relevant_rules(self, subject: Any, context: Dict[str, Any] = None) -> List[EthicalRule]:
        """
        Get rules that are relevant to the given subject and context.
        
        This method uses heuristics to determine which rules
        are applicable to the current subject and context.
        
        Args:
            subject: The subject being evaluated
            context: Additional context information
            
        Returns:
            List of relevant rules
        """
        with self._lock:
            # Start with all rules
            all_rules = list(self.rules.values())
            
            # Filter rules based on subject and context
            relevant_rules = []
            
            # If subject is a dictionary, use its keys to determine relevant rules
            if isinstance(subject, dict):
                # Look for principle or value indicators in the subject
                principles_mentioned = set()
                values_mentioned = set()
                
                for key in subject:
                    # Check if key matches a principle name
                    for principle in EthicalPrincipleType:
                        if principle.name.lower() in key.lower():
                            principles_mentioned.add(principle)
                    
                    # Check if key matches a value name
                    for value in ValueAlignmentType:
                        if value.name.lower() in key.lower():
                            values_mentioned.add(value)
                
                # Include rules for mentioned principles and values
                for rule in all_rules:
                    if rule.metadata.principle in principles_mentioned:
                        relevant_rules.append(rule)
                    elif any(value in rule.metadata.values for value in values_mentioned):
                        relevant_rules.append(rule)
                
                # Include rules based on action type
                if "action_type" in subject:
                    action_type = subject["action_type"]
                    if isinstance(action_type, str):
                        # Map action types to relevant principles
                        action_principle_map = {
                            "decision": [EthicalPrincipleType.JUSTICE, EthicalPrincipleType.TRANSPARENCY],
                            "recommendation": [EthicalPrincipleType.BENEFICENCE, EthicalPrincipleType.TRANSPARENCY],
                            "classification": [EthicalPrincipleType.JUSTICE, EthicalPrincipleType.ACCOUNTABILITY],
                            "prediction": [EthicalPrincipleType.TRANSPARENCY, EthicalPrincipleType.ACCOUNTABILITY],
                            "generation": [EthicalPrincipleType.NON_MALEFICENCE, EthicalPrincipleType.AUTONOMY]
                        }
                        
                        relevant_principles = action_principle_map.get(action_type.lower(), [])
                        for rule in all_rules:
                            if rule.metadata.principle in relevant_principles and rule not in relevant_rules:
                                relevant_rules.append(rule)
            
            # If context has specific ethical requirements, include those rules
            if context and isinstance(context, dict):
                if "ethical_requirements" in context:
                    req = context["ethical_requirements"]
                    if isinstance(req, list):
                        for rule_name in req:
                            rule = self.get_rule(rule_name)
                            if rule and rule not in relevant_rules:
                                relevant_rules.append(rule)
                    elif isinstance(req, dict):
                        for principle_name, importance in req.items():
                            try:
                                principle = EthicalPrincipleType(principle_name.upper())
                                for rule in self.get_rules_by_principle(principle):
                                    if rule not in relevant_rules:
                                        relevant_rules.append(rule)
                            except ValueError:
                                pass
            
            # If no relevant rules were found, include core rules
            if not relevant_rules:
                # Include one rule for each core principle
                core_principles = [EthicalPrincipleType.NON_MALEFICENCE, EthicalPrincipleType.JUSTICE]
                for principle in core_principles:
                    principle_rules = self.get_rules_by_principle(principle)
                    if principle_rules:
                        relevant_rules.append(principle_rules[0])
            
            # Remove duplicates while preserving order
            seen = set()
            relevant_rules = [r for r in relevant_rules 
                             if not (r.name in seen or seen.add(r.name))]
            
            return relevant_rules
    
    def evaluate_all_rules(self, subject: Any, context: Dict[str, Any] = None) -> Tuple[bool, float, List[str]]:
        """
        Evaluate if a subject satisfies all ethical rules.
        
        Args:
            subject: The subject to evaluate
            context: Additional context information
            
        Returns:
            Tuple of (all_satisfied, average_score, violated_rules)
        """
        with self._lock:
            if not self.rules:
                logger.warning("No ethical rules defined in EthicalRules")
                return True, 1.0, []
            
            # Get relevant rules
            relevant_rules = self.get_relevant_rules(subject, context)
            
            if not relevant_rules:
                logger.debug("No relevant ethical rules found for subject")
                return True, 1.0, []
            
            # Evaluate each rule
            violated_rules = []
            scores = []
            
            for rule in relevant_rules:
                try:
                    is_satisfied, score = rule.evaluate(subject, context)
                    scores.append(score)
                    
                    if not is_satisfied:
                        violated_rules.append(rule.name)
                except Exception as e:
                    logger.error(f"Error evaluating ethical rule {rule.name}: {e}")
                    violated_rules.append(f"{rule.name} (error)")
            
            # Calculate overall results
            all_satisfied = len(violated_rules) == 0
            average_score = sum(scores) / len(scores) if scores else 0.0
            
            return all_satisfied, average_score, violated_rules
    
    def load_rules(self, file_path: str) -> int:
        """
        Load ethical rules from a file.
        
        Args:
            file_path: Path to a JSON or YAML file containing rule definitions
            
        Returns:
            Number of rules successfully loaded
        """
        if not os.path.exists(file_path):
            logger.error(f"Ethical rules file not found: {file_path}")
            return 0
        
        try:
            # Determine file type by extension
            if file_path.endswith('.json'):
                with open(file_path, 'r') as f:
                    data = json.load(f)
            elif file_path.endswith(('.yaml', '.yml')):
                with open(file_path, 'r') as f:
                    data = yaml.safe_load(f)
            else:
                logger.error(f"Unsupported file format: {file_path}")
                return 0
            
            # Ensure we have a list of rules
            if isinstance(data, dict):
                if "rules" in data and isinstance(data["rules"], list):
                    rules_data = data["rules"]
                else:
                    # Single rule definition
                    rules_data = [data]
            elif isinstance(data, list):
                rules_data = data
            else:
                logger.error(f"Invalid rule data format in {file_path}")
                return 0
            
            # Load each rule
            loaded_count = 0
            for rule_data in rules_data:
                try:
                    # Check rule type
                    rule_type = rule_data.get("type")
                    if not rule_type or rule_type not in self.rule_types:
                        logger.warning(f"Unknown rule type: {rule_type}")
                        continue
                    
                    # Create rule instance
                    rule_class = self.rule_types[rule_type]
                    rule = rule_class.from_dict(rule_data)
                    
                    # Add rule to manager
                    if self.add_rule(rule):
                        loaded_count += 1
                except Exception as e:
                    logger.error(f"Error loading ethical rule: {e}")
            
            logger.info(f"Loaded {loaded_count} ethical rules from {file_path}")
            return loaded_count
            
        except Exception as e:
            logger.error(f"Error loading ethical rules from {file_path}: {e}")
            return 0
    
    def save_rules(self, file_path: str, rules: List[str] = None) -> bool:
        """
        Save ethical rules to a file.
        
        Args:
            file_path: Path to save the rules to
            rules: Optional list of rule names to save, or None for all
            
        Returns:
            True if the rules were successfully saved, False otherwise
        """
        try:
            # Determine which rules to save
            with self._lock:
                if rules is None:
                    # Save all rules
                    rules_to_save = list(self.rules.values())
                else:
                    # Save only the specified rules
                    rules_to_save = [
                        self.rules[name] for name in rules
                        if name in self.rules
                    ]
            
            if not rules_to_save:
                logger.warning("No ethical rules to save")
                return False
            
            # Serialize rules
            serialized_rules = [rule.to_dict() for rule in rules_to_save]
            
            # Write to file based on extension
            if file_path.endswith('.json'):
                with open(file_path, 'w') as f:
                    json.dump({"rules": serialized_rules}, f, indent=2)
            elif file_path.endswith(('.yaml', '.yml')):
                with open(file_path, 'w') as f:
                    yaml.dump({"rules": serialized_rules}, f)
            else:
                logger.error(f"Unsupported file format: {file_path}")
                return False
            
            logger.info(f"Saved {len(rules_to_save)} ethical rules to {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving ethical rules to {file_path}: {e}")
            return False
    
    def get_rule_stats(self) -> Dict[str, Any]:
        """
        Get statistics about all ethical rules.
        
        Returns:
            Dictionary with rule statistics
        """
        with self._lock:
            stats = {
                "total_rules": len(self.rules),
                "rules_by_principle": {
                    principle.value: len(self.get_rules_by_principle(principle))
                    for principle in EthicalPrincipleType
                },
                "rules_by_type": {
                    rule_type: len(self.get_rules_by_type(rule_type))
                    for rule_type in self.rule_types
                },
                "total_evaluations": sum(r.evaluation_count for r in self.rules.values()),
                "total_violations": sum(r.violation_count for r in self.rules.values()),
                "rules": [r.get_stats() for r in self.rules.values()]
            }
            
            # Calculate violation rates
            total_evaluations = stats["total_evaluations"]
            if total_evaluations > 0:
                stats["overall_violation_rate"] = stats["total_violations"] / total_evaluations
            else:
                stats["overall_violation_rate"] = 0.0
            
            return stats
    
    def register_rule_type(self, type_name: str, rule_class: Type[EthicalRule]) -> None:
        """
        Register a new ethical rule type.
        
        Args:
            type_name: Name of the rule type
            rule_class: Rule class for this type
        """
        with self._lock:
            self.rule_types[type_name] = rule_class
            logger.info(f"Registered ethical rule type: {type_name}")
    
    def create_rule(self, rule_type: str, **kwargs) -> Optional[EthicalRule]:
        """
        Create an ethical rule of the specified type with the given parameters.
        
        Args:
            rule_type: Type of rule to create
            **kwargs: Parameters to pass to the rule constructor
            
        Returns:
            Created ethical rule, or None if the type is not recognized
        """
        with self._lock:
            if rule_type not in self.rule_types:
                logger.error(f"Unknown ethical rule type: {rule_type}")
                return None
            
            rule_class = self.rule_types[rule_type]
            try:
                rule = rule_class(**kwargs)
                return rule
            except Exception as e:
                logger.error(f"Error creating ethical rule of type {rule_type}: {e}")
                return None


class BiasMetric:
    """
    Base class for bias metrics. Each bias metric calculates a specific
    measure of bias in data or algorithms.
    """
    
    def __init__(self, name: str, description: str):
        """
        Initialize a bias metric.
        
        Args:
            name: Name of the bias metric
            description: Description of what the metric measures
        """
        self.name = name
        self.description = description
    
    def calculate(self, data: Any) -> Tuple[float, Dict[str, Any]]:
        """
        Calculate the bias metric for the given data.
        
        Args:
            data: Data to analyze for bias
            
        Returns:
            Tuple of (bias_score, details)
            - bias_score: Float between 0 and 1, where higher means more bias
            - details: Dictionary with detailed results of the calculation
        """
        raise NotImplementedError("Subclasses must implement calculate method")
    
    def interpret(self, score: float) -> str:
        """
        Interpret a bias score, providing a human-readable explanation.
        
        Args:
            score: The bias score to interpret
            
        Returns:
            String interpretation of the score
        """
        if score <= 0.2:
            return f"Low {self.name} bias: The data or model shows minimal bias according to this metric."
        elif score <= 0.5:
            return f"Moderate {self.name} bias: Some bias is present, which may require attention."
        else:
            return f"High {self.name} bias: Significant bias detected, requiring immediate attention and mitigation."


class StatisticalParityBiasMetric(BiasMetric):
    """
    Statistical parity bias metric. Measures whether outcomes are
    independent of protected attributes.
    """
    
    def __init__(self):
        super().__init__(
            name="Statistical Parity",
            description="Measures whether outcomes are independent of protected attributes"
        )
    
    def calculate(self, data: Dict[str, Any]) -> Tuple[float, Dict[str, Any]]:
        """
        Calculate statistical parity for the given data.
        
        Args:
            data: Dictionary containing:
                - predictions: Model predictions or outcomes
                - protected_attributes: Dictionary mapping attribute names to values
                
        Returns:
            Tuple of (bias_score, details)
        """
        if "predictions" not in data or "protected_attributes" not in data:
            raise ValueError("Data must contain 'predictions' and 'protected_attributes'")
        
        predictions = np.array(data["predictions"])
        protected_attributes = data["protected_attributes"]
        
        if not protected_attributes:
            return 0.0, {"message": "No protected attributes provided"}
        
        # Convert predictions to binary if not already
        if not isinstance(predictions[0], (bool, int)) or not all(p in [0, 1] for p in predictions):
            # Assume probabilistic predictions, threshold at 0.5
            binary_predictions = (predictions > 0.5).astype(int)
        else:
            binary_predictions = predictions
        
        # Calculate acceptance rates for each protected attribute
        details = {}
        disparities = []
        
        for attr, attr_values in protected_attributes.items():
            attr_values = np.array(attr_values)
            
            # Get unique attribute values
            unique_values = np.unique(attr_values)
            if len(unique_values) < 2:
                continue  # Skip if there's only one group
            
            # Calculate acceptance rate for each group
            acceptance_rates = {}
            for value in unique_values:
                group_mask = (attr_values == value)
                group_predictions = binary_predictions[group_mask]
                
                if len(group_predictions) > 0:
                    acceptance_rate = np.mean(group_predictions)
                    acceptance_rates[str(value)] = acceptance_rate
            
            # Calculate disparity
            if acceptance_rates:
                max_rate = max(acceptance_rates.values())
                min_rate = min(acceptance_rates.values())
                
                if max_rate > 0:
                    disparity = 1.0 - (min_rate / max_rate)
                else:
                    disparity = 0.0 if min_rate == 0 else 1.0
                
                disparities.append(disparity)
                details[attr] = {
                    "acceptance_rates": acceptance_rates,
                    "disparity": disparity
                }
        
        # Final score is the maximum disparity across all attributes
        bias_score = max(disparities) if disparities else 0.0
        
        return bias_score, details
    
    def interpret(self, score: float) -> str:
        """
        Interpret a statistical parity bias score.
        
        Args:
            score: The bias score to interpret
            
        Returns:
            String interpretation of the score
        """
        if score <= 0.1:
            return "Low statistical parity bias: The outcomes are approximately independent of protected attributes."
        elif score <= 0.2:
            return "Moderate statistical parity bias: There is some disparity in outcomes across protected groups."
        else:
            return "High statistical parity bias: Significant disparities exist in outcomes across protected groups."


class EqualOpportunityBiasMetric(BiasMetric):
    """
    Equal opportunity bias metric. Measures whether true positive rates
    are equal across protected attributes.
    """
    
    def __init__(self):
        super().__init__(
            name="Equal Opportunity",
            description="Measures whether true positive rates are equal across protected attributes"
        )
    
    def calculate(self, data: Dict[str, Any]) -> Tuple[float, Dict[str, Any]]:
        """
        Calculate equal opportunity metric for the given data.
        
        Args:
            data: Dictionary containing:
                - predictions: Model predictions or outcomes
                - ground_truth: True labels
                - protected_attributes: Dictionary mapping attribute names to values
                
        Returns:
            Tuple of (bias_score, details)
        """
        if "predictions" not in data or "protected_attributes" not in data:
            raise ValueError("Data must contain 'predictions' and 'protected_attributes'")
        
        if "ground_truth" not in data:
            raise ValueError("Equal opportunity metric requires ground truth labels")
        
        predictions = np.array(data["predictions"])
        ground_truth = np.array(data["ground_truth"])
        protected_attributes = data["protected_attributes"]
        
        if not protected_attributes:
            return 0.0, {"message": "No protected attributes provided"}
        
        # Convert predictions to binary if not already
        if not isinstance(predictions[0], (bool, int)) or not all(p in [0, 1] for p in predictions):
            # Assume probabilistic predictions, threshold at 0.5
            binary_predictions = (predictions > 0.5).astype(int)
        else:
            binary_predictions = predictions
        
        # Convert ground truth to binary if not already
        if not isinstance(ground_truth[0], (bool, int)) or not all(g in [0, 1] for g in ground_truth):
            # Assume probabilistic, threshold at 0.5
            binary_ground_truth = (ground_truth > 0.5).astype(int)
        else:
            binary_ground_truth = ground_truth
        
        # Calculate true positive rates for each protected attribute
        details = {}
        disparities = []
        
        for attr, attr_values in protected_attributes.items():
            attr_values = np.array(attr_values)
            
            # Get unique attribute values
            unique_values = np.unique(attr_values)
            if len(unique_values) < 2:
                continue  # Skip if there's only one group
            
            # Calculate TPR for each group
            tpr_rates = {}
            for value in unique_values:
                group_mask = (attr_values == value)
                group_predictions = binary_predictions[group_mask]
                group_ground_truth = binary_ground_truth[group_mask]
                
                # Calculate TPR: TP / (TP + FN)
                positive_mask = (group_ground_truth == 1)
                if np.sum(positive_mask) > 0:
                    tp = np.sum(group_predictions[positive_mask] == 1)
                    tpr = tp / np.sum(positive_mask)
                    tpr_rates[str(value)] = tpr
            
            # Calculate disparity
            if tpr_rates:
                max_rate = max(tpr_rates.values())
                min_rate = min(tpr_rates.values())
                
                if max_rate > 0:
                    disparity = 1.0 - (min_rate / max_rate)
                else:
                    disparity = 0.0 if min_rate == 0 else 1.0
                
                disparities.append(disparity)
                details[attr] = {
                    "tpr_rates": tpr_rates,
                    "disparity": disparity
                }
        
        # Final score is the maximum disparity across all attributes
        bias_score = max(disparities) if disparities else 0.0
        
        return bias_score, details


class BiasDetector:
    """
    Detects bias in data, models, or outputs using various bias metrics.
    
    The BiasDetector identifies potential biases related to protected 
    attributes like race, gender, age, religion, etc., and provides
    scores and explanations for the detected biases.
    """
    
    def __init__(self, 
                bias_detection_models: Dict[str, Any] = None,
                protected_attributes: List[str] = None,
                bias_threshold: float = 0.3):
        """
        Initialize a bias detector.
        
        Args:
            bias_detection_models: Dictionary of bias detection models
            protected_attributes: List of protected attributes to check for bias
            bias_threshold: Threshold above which bias is considered significant
        """
        self.bias_metrics = self._initialize_bias_metrics()
        self.bias_detection_models = bias_detection_models or {}
        self.protected_attributes = protected_attributes or [
            "race", "gender", "age", "religion", "nationality", 
            "disability", "sexual_orientation", "socioeconomic_status"
        ]
        self.bias_threshold = bias_threshold
        self._lock = threading.RLock()  # For thread safety
    
    def _initialize_bias_metrics(self) -> Dict[str, BiasMetric]:
        """
        Initialize the built-in bias metrics.
        
        Returns:
            Dictionary of bias metric name to metric instance
        """
        metrics = {
            "statistical_parity": StatisticalParityBiasMetric(),
            "equal_opportunity": EqualOpportunityBiasMetric()
        }
        
        return metrics
    
    def detect_bias(self, subject: Any, context: Dict[str, Any] = None) -> Tuple[bool, float]:
        """
        Detect bias in the subject.
        
        Args:
            subject: The subject to analyze for bias
            context: Additional context information
            
        Returns:
            Tuple of (bias_detected, bias_score)
        """
        with self._lock:
            # Check for explicit bias indicators
            explicit_bias = self._check_explicit_bias(subject, context)
            if explicit_bias is not None:
                return explicit_bias
            
            # Extract data for bias analysis
            data = self._extract_bias_data(subject, context)
            if not data:
                # Check for text content to analyze for bias
                text = self._extract_text_content(subject, context)
                if text:
                    return self._detect_bias_in_text(text)
                
                # No data or text to analyze
                logger.debug("No data available for bias detection")
                return False, 0.0
            
            # Calculate bias metrics
            bias_scores = []
            for metric_name, metric in self.bias_metrics.items():
                try:
                    if self._can_apply_metric(metric, data):
                        bias_score, details = metric.calculate(data)
                        bias_scores.append(bias_score)
                except Exception as e:
                    logger.error(f"Error calculating bias metric {metric_name}: {e}")
            
            # Check custom bias detection models
            for model_name, model in self.bias_detection_models.items():
                try:
                    if callable(model):
                        result = model(data)
                        if isinstance(result, (int, float)):
                            bias_scores.append(float(result))
                        elif isinstance(result, tuple) and len(result) >= 1:
                            bias_scores.append(float(result[0]))
                except Exception as e:
                    logger.error(f"Error using bias detection model {model_name}: {e}")
            
            # Calculate overall bias score
            if bias_scores:
                overall_bias = max(bias_scores)  # Use maximum bias score across metrics
                bias_detected = overall_bias > self.bias_threshold
                return bias_detected, overall_bias
            else:
                # No bias scores available
                return False, 0.0
    
    def _check_explicit_bias(self, subject: Any, context: Dict[str, Any]) -> Optional[Tuple[bool, float]]:
        """
        Check for explicit bias indicators in subject or context.
        
        Args:
            subject: The subject to check
            context: Additional context information
            
        Returns:
            Tuple of (bias_detected, bias_score), or None if not found
        """
        # Check subject for bias indicators
        if isinstance(subject, dict):
            if "bias" in subject:
                try:
                    bias = float(subject["bias"])
                    return bias > self.bias_threshold, bias
                except (ValueError, TypeError):
                    if isinstance(subject["bias"], bool):
                        return subject["bias"], 0.8 if subject["bias"] else 0.0
            
            if "bias_score" in subject:
                try:
                    bias = float(subject["bias_score"])
                    return bias > self.bias_threshold, bias
                except (ValueError, TypeError):
                    pass
            
            if "is_biased" in subject:
                if isinstance(subject["is_biased"], bool):
                    return subject["is_biased"], 0.8 if subject["is_biased"] else 0.0
        
        # Check context for bias indicators
        if context:
            if "bias" in context:
                try:
                    bias = float(context["bias"])
                    return bias > self.bias_threshold, bias
                except (ValueError, TypeError):
                    if isinstance(context["bias"], bool):
                        return context["bias"], 0.8 if context["bias"] else 0.0
            
            if "bias_score" in context:
                try:
                    bias = float(context["bias_score"])
                    return bias > self.bias_threshold, bias
                except (ValueError, TypeError):
                    pass
            
            if "is_biased" in context:
                if isinstance(context["is_biased"], bool):
                    return context["is_biased"], 0.8 if context["is_biased"] else 0.0
        
        # No explicit bias indicators found
        return None
    
    def _extract_bias_data(self, subject: Any, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Extract data needed for bias analysis.
        
        Args:
            subject: The subject to analyze
            context: Additional context information
            
        Returns:
            Dictionary with evaluation data, or None if not available
        """
        data = {}
        
        # Try to find predictions and protected attributes
        predictions = None
        protected_values = {}
        ground_truth = None
        
        # Check subject for data
        if isinstance(subject, dict):
            # Look for predictions
            if "predictions" in subject:
                predictions = subject["predictions"]
            elif "outputs" in subject:
                predictions = subject["outputs"]
            elif "scores" in subject:
                predictions = subject["scores"]
            
            # Look for ground truth
            if "ground_truth" in subject:
                ground_truth = subject["ground_truth"]
            elif "labels" in subject:
                ground_truth = subject["labels"]
            elif "actual" in subject:
                ground_truth = subject["actual"]
            
            # Look for protected attributes
            for attr in self.protected_attributes:
                if attr in subject:
                    protected_values[attr] = subject[attr]
            
            # Check for nested bias data
            if "bias_data" in subject and isinstance(subject["bias_data"], dict):
                bias_data = subject["bias_data"]
                
                # Try to find predictions in bias_data
                if predictions is None:
                    for key in ["predictions", "outputs", "scores"]:
                        if key in bias_data:
                            predictions = bias_data[key]
                            break
                
                # Try to find ground truth in bias_data
                if ground_truth is None:
                    for key in ["ground_truth", "labels", "actual"]:
                        if key in bias_data:
                            ground_truth = bias_data[key]
                            break
                
                # Look for protected attributes in bias_data
                for attr in self.protected_attributes:
                    if attr in bias_data and attr not in protected_values:
                        protected_values[attr] = bias_data[attr]
            
            # Check structured data
            if "data" in subject and isinstance(subject["data"], dict):
                data_dict = subject["data"]
                
                # Try to find predictions in data
                if predictions is None:
                    for key in ["predictions", "outputs", "scores"]:
                        if key in data_dict:
                            predictions = data_dict[key]
                            break
                
                # Try to find ground truth in data
                if ground_truth is None:
                    for key in ["ground_truth", "labels", "actual"]:
                        if key in data_dict:
                            ground_truth = data_dict[key]
                            break
                
                # Look for protected attributes in data
                for attr in self.protected_attributes:
                    if attr in data_dict and attr not in protected_values:
                        protected_values[attr] = data_dict[attr]
        
        # Check context for data if not found in subject
        if context:
            # Look for predictions
            if predictions is None:
                if "predictions" in context:
                    predictions = context["predictions"]
                elif "outputs" in context:
                    predictions = context["outputs"]
                elif "scores" in context:
                    predictions = context["scores"]
            
            # Look for ground truth
            if ground_truth is None:
                if "ground_truth" in context:
                    ground_truth = context["ground_truth"]
                elif "labels" in context:
                    ground_truth = context["labels"]
                elif "actual" in context:
                    ground_truth = context["actual"]
            
            # Look for protected attributes
            for attr in self.protected_attributes:
                if attr in context and attr not in protected_values:
                    protected_values[attr] = context[attr]
            
            # Check for dataset in context
            if "dataset" in context and isinstance(context["dataset"], dict):
                dataset = context["dataset"]
                
                # Try to find predictions in dataset
                if predictions is None:
                    for key in ["predictions", "outputs", "scores"]:
                        if key in dataset:
                            predictions = dataset[key]
                            break
                
                # Try to find ground truth in dataset
                if ground_truth is None:
                    for key in ["ground_truth", "labels", "actual"]:
                        if key in dataset:
                            ground_truth = dataset[key]
                            break
                
                # Look for protected attributes in dataset
                for attr in self.protected_attributes:
                    if attr in dataset and attr not in protected_values:
                        protected_values[attr] = dataset[attr]
            
            # Check if context has bias_data
            if "bias_data" in context and isinstance(context["bias_data"], dict):
                bias_data = context["bias_data"]
                
                # Try to find predictions in bias_data
                if predictions is None:
                    for key in ["predictions", "outputs", "scores"]:
                        if key in bias_data:
                            predictions = bias_data[key]
                            break
                
                # Try to find ground truth in bias_data
                if ground_truth is None:
                    for key in ["ground_truth", "labels", "actual"]:
                        if key in bias_data:
                            ground_truth = bias_data[key]
                            break
                
                # Look for protected attributes in bias_data
                for attr in self.protected_attributes:
                    if attr in bias_data and attr not in protected_values:
                        protected_values[attr] = bias_data[attr]
        
        # Check if we have enough data for evaluation
        if predictions is not None and len(protected_values) > 0:
            data = {
                "predictions": predictions,
                "protected_attributes": protected_values
            }
            
            if ground_truth is not None:
                data["ground_truth"] = ground_truth
            
            return data
        
        return None
    
    def _extract_text_content(self, subject: Any, context: Dict[str, Any]) -> str:
        """
        Extract textual content from subject or context for bias analysis.
        
        Args:
            subject: The subject to analyze
            context: Additional context information
            
        Returns:
            String containing relevant text content
        """
        texts = []
        
        # Extract text from subject
        if isinstance(subject, str):
            texts.append(subject)
        elif isinstance(subject, dict):
            # Check common text fields
            for key in ["text", "content", "message", "description", "narrative", "output"]:
                if key in subject and isinstance(subject[key], str):
                    texts.append(subject[key])
            
            # Check for nested content
            if "data" in subject and isinstance(subject["data"], dict):
                for key in ["text", "content", "message", "description", "narrative", "output"]:
                    if key in subject["data"] and isinstance(subject["data"][key], str):
                        texts.append(subject["data"][key])
        
        # Extract text from context
        if context:
            if isinstance(context, str):
                texts.append(context)
            elif isinstance(context, dict):
                for key in ["text", "content", "message", "description", "narrative", "output"]:
                    if key in context and isinstance(context[key], str):
                        texts.append(context[key])
        
        # Combine all extracted texts
        return " ".join(texts)
    
    def _detect_bias_in_text(self, text: str) -> Tuple[bool, float]:
        """
        Detect bias in text content.
        
        Args:
            text: Text to analyze for bias
            
        Returns:
            Tuple of (bias_detected, bias_score)
        """
        if not text:
            return False, 0.0
        
        # Normalize text
        text = text.lower()
        
        # Define bias indicators (keyword-based approach)
        # This is a simplified approach; in production, use more sophisticated NLP models
        bias_indicators = {
            "gender_bias": [
                "women are", "men are", "boys will be boys", "girls are", 
                "like a man", "like a woman", "manly", "womanly", "feminine", "masculine",
                "all men", "all women", "typical woman", "typical man"
            ],
            "racial_bias": [
                "black people", "white people", "asian people", "hispanic people",
                "people of color", "ethnic", "race", "racial", "minority groups"
            ],
            "age_bias": [
                "old people", "young people", "millennials are", "boomers are", 
                "generation z", "elderly", "seniors", "too young", "too old"
            ],
            "religious_bias": [
                "christians are", "muslims are", "jews are", "hindus are", "atheists are",
                "religious people", "non-religious"
            ],
            "socioeconomic_bias": [
                "poor people", "rich people", "wealthy", "poverty", "low-income",
                "elite", "privileged", "underprivileged", "welfare"
            ]
        }
        
        # Calculate bias score based on indicators
        category_scores = {}
        for category, keywords in bias_indicators.items():
            # Count occurrences of keywords
            category_count = sum(text.count(keyword) for keyword in keywords)
            text_length = max(100, len(text))  # Normalize by text length
            
            # Calculate normalized score
            if category_count > 0:
                category_score = min(1.0, category_count / (text_length / 200))
                category_scores[category] = category_score
        
        # Calculate overall bias score
        if category_scores:
            overall_bias = max(category_scores.values())
            bias_detected = overall_bias > self.bias_threshold
            return bias_detected, overall_bias
        
        return False, 0.0
    
    def _can_apply_metric(self, metric: BiasMetric, data: Dict[str, Any]) -> bool:
        """
        Check if a bias metric can be applied to the given data.
        
        Args:
            metric: The bias metric to check
            data: The data to analyze
            
        Returns:
            Boolean indicating if the metric can be applied
        """
        if isinstance(metric, EqualOpportunityBiasMetric):
            # Equal opportunity requires ground truth
            return "ground_truth" in data
        
        # Default assumption is that the metric can be applied
        return True
    
    def add_bias_metric(self, name: str, metric: BiasMetric) -> None:
        """
        Add a custom bias metric.
        
        Args:
            name: Name of the bias metric
            metric: BiasMetric instance
        """
        with self._lock:
            self.bias_metrics[name] = metric
            logger.info(f"Added bias metric: {name}")
    
    def remove_bias_metric(self, name: str) -> bool:
        """
        Remove a bias metric.
        
        Args:
            name: Name of the bias metric to remove
            
        Returns:
            True if the metric was successfully removed, False otherwise
        """
        with self._lock:
            if name in self.bias_metrics:
                del self.bias_metrics[name]
                logger.info(f"Removed bias metric: {name}")
                return True
            return False
    
    def add_protected_attribute(self, attribute: str) -> None:
        """
        Add a protected attribute to check for bias.
        
        Args:
            attribute: Name of the protected attribute
        """
        with self._lock:
            if attribute not in self.protected_attributes:
                self.protected_attributes.append(attribute)
                logger.info(f"Added protected attribute: {attribute}")
    
    def remove_protected_attribute(self, attribute: str) -> bool:
        """
        Remove a protected attribute.
        
        Args:
            attribute: Name of the protected attribute to remove
            
        Returns:
            True if the attribute was successfully removed, False otherwise
        """
        with self._lock:
            if attribute in self.protected_attributes:
                self.protected_attributes.remove(attribute)
                logger.info(f"Removed protected attribute: {attribute}")
                return True
            return False
    
    def set_bias_threshold(self, threshold: float) -> None:
        """
        Set the bias detection threshold.
        
        Args:
            threshold: New threshold value between 0 and 1
        """
        if threshold < 0 or threshold > 1:
            raise ValueError("Bias threshold must be between 0 and 1")
        
        with self._lock:
            self.bias_threshold = threshold
            logger.info(f"Bias threshold set to {threshold}")
    
    def get_bias_report(self, subject: Any, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Generate a comprehensive bias report for the subject.
        
        Args:
            subject: The subject to analyze for bias
            context: Additional context information
            
        Returns:
            Dictionary containing detailed bias analysis
        """
        with self._lock:
            # Start with basic bias detection
            bias_detected, overall_bias = self.detect_bias(subject, context)
            
            report = {
                "bias_detected": bias_detected,
                "overall_bias_score": overall_bias,
                "threshold": self.bias_threshold,
                "timestamp": time.time(),
                "metrics": {},
                "protected_attributes": {},
                "recommendations": []
            }
            
            # Extract data for detailed analysis
            data = self._extract_bias_data(subject, context)
            if data:
                # Calculate individual metrics
                for metric_name, metric in self.bias_metrics.items():
                    try:
                        if self._can_apply_metric(metric, data):
                            bias_score, details = metric.calculate(data)
                            report["metrics"][metric_name] = {
                                "score": bias_score,
                                "interpretation": metric.interpret(bias_score),
                                "details": details
                            }
                            
                            # Add recommendations if bias is detected
                            if bias_score > self.bias_threshold:
                                report["recommendations"].append(
                                    f"Address {metric_name} bias, which shows a score of {bias_score:.2f}"
                                )
                    except Exception as e:
                        logger.error(f"Error calculating bias metric {metric_name}: {e}")
                
                # Analyze bias by protected attribute
                if "protected_attributes" in data:
                    for attr, values in data["protected_attributes"].items():
                        # Check if this attribute shows bias in any metric
                        attr_bias = False
                        for metric_details in report["metrics"].values():
                            if "details" in metric_details and attr in metric_details["details"]:
                                if metric_details["details"][attr].get("disparity", 0) > self.bias_threshold:
                                    attr_bias = True
                                    break
                        
                        report["protected_attributes"][attr] = {
                            "bias_detected": attr_bias,
                            "unique_values": len(np.unique(values)) if isinstance(values, (list, np.ndarray)) else "unknown"
                        }
                        
                        if attr_bias:
                            report["recommendations"].append(
                                f"Review data distribution for attribute '{attr}' to address potential bias"
                            )
            else:
                # Text-based bias detection
                text = self._extract_text_content(subject, context)
                if text:
                    bias_detected, bias_score = self._detect_bias_in_text(text)
                    report["text_analysis"] = {
                        "bias_detected": bias_detected,
                        "bias_score": bias_score,
                        "method": "keyword-based"
                    }
                    
                    if bias_detected:
                        report["recommendations"].append(
                            "Review text content for potentially biased language or framing"
                        )
            
            # Add general recommendations if bias is detected
            if bias_detected and not report["recommendations"]:
                report["recommendations"].append(
                    "Consider reviewing data collection and model training processes for potential sources of bias"
                )
                report["recommendations"].append(
                    "Evaluate whether additional diversity in training data could help mitigate detected bias"
                )
            
            return report


class FairnessMetrics:
    """
    Calculates and manages fairness metrics for evaluating decision systems.
    
    Fairness metrics measure how well a decision system treats different groups
    defined by protected attributes like race, gender, etc.
    """
    
    def __init__(self, protected_attributes: List[str] = None):
        """
        Initialize fairness metrics calculator.
        
        Args:
            protected_attributes: List of protected attributes to consider
        """
        self.protected_attributes = protected_attributes or [
            "race", "gender", "age", "religion", "nationality", 
            "disability", "sexual_orientation", "socioeconomic_status"
        ]
        self.metrics = self._initialize_metrics()
        self._lock = threading.RLock()  # For thread safety
        
        # Utility property to check for sklearn
        self.has_sklearn = _HAS_SKLEARN
    
    def _initialize_metrics(self) -> Dict[str, Callable]:
        """
        Initialize fairness metrics functions.
        
        Returns:
            Dictionary of metric name to calculation function
        """
        metrics = {
            "demographic_parity": self._demographic_parity,
            "equal_opportunity": self._equal_opportunity,
            "equalized_odds": self._equalized_odds,
            "predictive_parity": self._predictive_parity,
            "disparate_impact": self._disparate_impact,
            "statistical_parity": self._demographic_parity,  # Alias
            "treatment_equality": self._treatment_equality
        }
        
        return metrics
    
    def calculate_metric(self, metric_name: str, data: Dict[str, Any]) -> Tuple[float, Dict[str, Any]]:
        """
        Calculate a specific fairness metric.
        
        Args:
            metric_name: Name of the metric to calculate
            data: Dictionary containing:
                - predictions: Model predictions or outcomes
                - ground_truth: (Optional) True labels
                - protected_attributes: Dictionary mapping attribute names to values
                
        Returns:
            Tuple of (fairness_score, details)
        """
        with self._lock:
            if metric_name not in self.metrics:
                raise ValueError(f"Unknown fairness metric: {metric_name}")
            
            metric_func = self.metrics[metric_name]
            try:
                return metric_func(data)
            except Exception as e:
                logger.error(f"Error calculating {metric_name}: {e}")
                return 0.0, {"error": str(e)}
    
    def calculate_all_metrics(self, data: Dict[str, Any]) -> Dict[str, Tuple[float, Dict[str, Any]]]:
        """
        Calculate all applicable fairness metrics.
        
        Args:
            data: Dictionary containing model predictions, optional ground truth, and protected attributes
                
        Returns:
            Dictionary mapping metric names to (fairness_score, details) tuples
        """
        with self._lock:
            results = {}
            
            for metric_name, metric_func in self.metrics.items():
                try:
                    # Check if metric requires ground truth
                    requires_ground_truth = (
                        metric_name in ["equal_opportunity", "equalized_odds", "predictive_parity", "treatment_equality"]
                    )
                    
                    if requires_ground_truth and "ground_truth" not in data:
                        logger.debug(f"Skipping {metric_name} as it requires ground truth")
                        continue
                    
                    # Calculate metric
                    fairness_score, details = metric_func(data)
                    results[metric_name] = (fairness_score, details)
                except Exception as e:
                    logger.error(f"Error calculating {metric_name}: {e}")
            
            return results
    
    def _demographic_parity(self, data: Dict[str, Any]) -> Tuple[float, Dict[str, Any]]:
        """
        Calculate demographic parity (statistical parity).
        
        Demographic parity measures whether outcomes are independent of protected attributes.
        Score of 1.0 means perfect parity, 0.0 means maximum disparity.
        
        Args:
            data: Dictionary with predictions and protected attributes
                
        Returns:
            Tuple of (fairness_score, details)
        """
        if "predictions" not in data or "protected_attributes" not in data:
            raise ValueError("Data must contain 'predictions' and 'protected_attributes'")
        
        predictions = np.array(data["predictions"])
        protected_attributes = data["protected_attributes"]
        
        if not protected_attributes:
            return 1.0, {"message": "No protected attributes provided"}
        
        # Convert predictions to binary if not already
        if not isinstance(predictions[0], (bool, int)) or not all(p in [0, 1] for p in predictions):
            # Assume probabilistic predictions, threshold at 0.5
            binary_predictions = (predictions > 0.5).astype(int)
        else:
            binary_predictions = predictions
        
        # Calculate acceptance rates for each protected attribute
        details = {}
        parities = []
        
        for attr, attr_values in protected_attributes.items():
            attr_values = np.array(attr_values)
            
            # Get unique attribute values
            unique_values = np.unique(attr_values)
            if len(unique_values) < 2:
                continue  # Skip if there's only one group
            
            # Calculate acceptance rate for each group
            acceptance_rates = {}
            for value in unique_values:
                group_mask = (attr_values == value)
                group_predictions = binary_predictions[group_mask]
                
                if len(group_predictions) > 0:
                    acceptance_rate = np.mean(group_predictions)
                    acceptance_rates[str(value)] = acceptance_rate
            
            # Calculate min-max ratio
            if acceptance_rates:
                max_rate = max(acceptance_rates.values())
                min_rate = min(acceptance_rates.values())
                
                if max_rate > 0:
                    # Use ratio (min/max) as parity measure
                    parity = min_rate / max_rate
                else:
                    # If max_rate is 0, check if min_rate is also 0
                    parity = 1.0 if min_rate == 0 else 0.0
                
                parities.append(parity)
                details[attr] = {
                    "acceptance_rates": acceptance_rates,
                    "parity": parity
                }
        
        # Final score is the minimum parity across all attributes
        fairness_score = min(parities) if parities else 1.0
        
        return fairness_score, details
    
    def _equal_opportunity(self, data: Dict[str, Any]) -> Tuple[float, Dict[str, Any]]:
        """
        Calculate equal opportunity.
        
        Equal opportunity measures whether true positive rates are equal across protected attributes.
        Score of 1.0 means perfect equality, 0.0 means maximum inequality.
        
        Args:
            data: Dictionary with predictions, ground truth, and protected attributes
                
        Returns:
            Tuple of (fairness_score, details)
        """
        if "predictions" not in data or "protected_attributes" not in data or "ground_truth" not in data:
            raise ValueError("Data must contain 'predictions', 'ground_truth', and 'protected_attributes'")
        
        predictions = np.array(data["predictions"])
        ground_truth = np.array(data["ground_truth"])
        protected_attributes = data["protected_attributes"]
        
        # Convert predictions to binary if not already
        if not isinstance(predictions[0], (bool, int)) or not all(p in [0, 1] for p in predictions):
            # Assume probabilistic predictions, threshold at 0.5
            binary_predictions = (predictions > 0.5).astype(int)
        else:
            binary_predictions = predictions
        
        # Convert ground truth to binary if not already
        if not isinstance(ground_truth[0], (bool, int)) or not all(g in [0, 1] for g in ground_truth):
            # Assume probabilistic ground truth, threshold at 0.5
            binary_ground_truth = (ground_truth > 0.5).astype(int)
        else:
            binary_ground_truth = ground_truth
        
        # Calculate true positive rates for each protected attribute
        details = {}
        parities = []
        
        for attr, attr_values in protected_attributes.items():
            attr_values = np.array(attr_values)
            
            # Get unique attribute values
            unique_values = np.unique(attr_values)
            if len(unique_values) < 2:
                continue  # Skip if there's only one group
            
            # Calculate TPR for each group
            tpr_rates = {}
            for value in unique_values:
                group_mask = (attr_values == value)
                group_predictions = binary_predictions[group_mask]
                group_ground_truth = binary_ground_truth[group_mask]
                
                # Calculate TPR: TP / (TP + FN)
                positive_mask = (group_ground_truth == 1)
                if np.sum(positive_mask) > 0:
                    tp = np.sum(group_predictions[positive_mask] == 1)
                    tpr = tp / np.sum(positive_mask)
                    tpr_rates[str(value)] = tpr
            
            # Calculate min-max ratio
            if tpr_rates:
                max_rate = max(tpr_rates.values())
                min_rate = min(tpr_rates.values())
                
                if max_rate > 0:
                    # Use ratio (min/max) as parity measure
                    parity = min_rate / max_rate
                else:
                    # If max_rate is 0, check if min_rate is also 0
                    parity = 1.0 if min_rate == 0 else 0.0
                
                parities.append(parity)
                details[attr] = {
                    "true_positive_rates": tpr_rates,
                    "parity": parity
                }
        
        # Final score is the minimum parity across all attributes
        fairness_score = min(parities) if parities else 1.0
        
        return fairness_score, details
    
    def _equalized_odds(self, data: Dict[str, Any]) -> Tuple[float, Dict[str, Any]]:
        """
        Calculate equalized odds.
        
        Equalized odds measures whether both true positive rates and false positive rates
        are equal across protected attributes.
        Score of 1.0 means perfect equality, 0.0 means maximum inequality.
        
        Args:
            data: Dictionary with predictions, ground truth, and protected attributes
                
        Returns:
            Tuple of (fairness_score, details)
        """
        if "predictions" not in data or "protected_attributes" not in data or "ground_truth" not in data:
            raise ValueError("Data must contain 'predictions', 'ground_truth', and 'protected_attributes'")
        
        predictions = np.array(data["predictions"])
        ground_truth = np.array(data["ground_truth"])
        protected_attributes = data["protected_attributes"]
        
        # Convert predictions to binary if not already
        if not isinstance(predictions[0], (bool, int)) or not all(p in [0, 1] for p in predictions):
            # Assume probabilistic predictions, threshold at 0.5
            binary_predictions = (predictions > 0.5).astype(int)
        else:
            binary_predictions = predictions
        
        # Convert ground truth to binary if not already
        if not isinstance(ground_truth[0], (bool, int)) or not all(g in [0, 1] for g in ground_truth):
            # Assume probabilistic ground truth, threshold at 0.5
            binary_ground_truth = (ground_truth > 0.5).astype(int)
        else:
            binary_ground_truth = ground_truth
        
        # Calculate TPR and FPR for each protected attribute
        details = {}
        parities = []
        
        for attr, attr_values in protected_attributes.items():
            attr_values = np.array(attr_values)
            
            # Get unique attribute values
            unique_values = np.unique(attr_values)
            if len(unique_values) < 2:
                continue  # Skip if there's only one group
            
            # Calculate TPR and FPR for each group
            tpr_rates = {}
            fpr_rates = {}
            for value in unique_values:
                group_mask = (attr_values == value)
                group_predictions = binary_predictions[group_mask]
                group_ground_truth = binary_ground_truth[group_mask]
                
                # Calculate TPR: TP / (TP + FN)
                positive_mask = (group_ground_truth == 1)
                if np.sum(positive_mask) > 0:
                    tp = np.sum(group_predictions[positive_mask] == 1)
                    tpr = tp / np.sum(positive_mask)
                    tpr_rates[str(value)] = tpr
                
                # Calculate FPR: FP / (FP + TN)
                negative_mask = (group_ground_truth == 0)
                if np.sum(negative_mask) > 0:
                    fp = np.sum(group_predictions[negative_mask] == 1)
                    fpr = fp / np.sum(negative_mask)
                    fpr_rates[str(value)] = fpr
            
            # Calculate min-max ratio for TPR
            tpr_parity = 1.0
            if tpr_rates:
                max_rate = max(tpr_rates.values())
                min_rate = min(tpr_rates.values())
                
                if max_rate > 0:
                    tpr_parity = min_rate / max_rate
                else:
                    tpr_parity = 1.0 if min_rate == 0 else 0.0
            
            # Calculate min-max ratio for FPR
            fpr_parity = 1.0
            if fpr_rates:
                # For FPR, lower is better, so we need to invert the ratio
                max_rate = max(fpr_rates.values())
                min_rate = min(fpr_rates.values())
                
                if max_rate > 0:
                    # Use ratio (min/max) as parity measure
                    fpr_parity = min_rate / max_rate
                else:
                    # If max_rate is 0, perfect parity
                    fpr_parity = 1.0
            
            # Equalized odds is the minimum of TPR parity and FPR parity
            parity = min(tpr_parity, fpr_parity)
            parities.append(parity)
            details[attr] = {
                "true_positive_rates": tpr_rates,
                "false_positive_rates": fpr_rates,
                "tpr_parity": tpr_parity,
                "fpr_parity": fpr_parity,
                "parity": parity
            }
        
        # Final score is the minimum parity across all attributes
        fairness_score = min(parities) if parities else 1.0
        
        return fairness_score, details
    
    def _predictive_parity(self, data: Dict[str, Any]) -> Tuple[float, Dict[str, Any]]:
        """
        Calculate predictive parity.
        
        Predictive parity measures whether precision (positive predictive value)
        is equal across protected attributes.
        Score of 1.0 means perfect parity, 0.0 means maximum disparity.
        
        Args:
            data: Dictionary with predictions, ground truth, and protected attributes
                
        Returns:
            Tuple of (fairness_score, details)
        """
        if "predictions" not in data or "protected_attributes" not in data or "ground_truth" not in data:
            raise ValueError("Data must contain 'predictions', 'ground_truth', and 'protected_attributes'")
        
        predictions = np.array(data["predictions"])
        ground_truth = np.array(data["ground_truth"])
        protected_attributes = data["protected_attributes"]
        
        # Convert predictions to binary if not already
        if not isinstance(predictions[0], (bool, int)) or not all(p in [0, 1] for p in predictions):
            # Assume probabilistic predictions, threshold at 0.5
            binary_predictions = (predictions > 0.5).astype(int)
        else:
            binary_predictions = predictions
        
        # Convert ground truth to binary if not already
        if not isinstance(ground_truth[0], (bool, int)) or not all(g in [0, 1] for g in ground_truth):
            # Assume probabilistic ground truth, threshold at 0.5
            binary_ground_truth = (ground_truth > 0.5).astype(int)
        else:
            binary_ground_truth = ground_truth
        
        # Calculate precision for each protected attribute
        details = {}
        parities = []
        
        for attr, attr_values in protected_attributes.items():
            attr_values = np.array(attr_values)
            
            # Get unique attribute values
            unique_values = np.unique(attr_values)
            if len(unique_values) < 2:
                continue  # Skip if there's only one group
            
            # Calculate precision for each group
            precision_rates = {}
            for value in unique_values:
                group_mask = (attr_values == value)
                group_predictions = binary_predictions[group_mask]
                group_ground_truth = binary_ground_truth[group_mask]
                
                # Calculate precision: TP / (TP + FP)
                predicted_positive_mask = (group_predictions == 1)
                if np.sum(predicted_positive_mask) > 0:
                    tp = np.sum(group_ground_truth[predicted_positive_mask] == 1)
                    precision = tp / np.sum(predicted_positive_mask)
                    precision_rates[str(value)] = precision
            
            # Calculate min-max ratio
            if precision_rates:
                max_rate = max(precision_rates.values())
                min_rate = min(precision_rates.values())
                
                if max_rate > 0:
                    # Use ratio (min/max) as parity measure
                    parity = min_rate / max_rate
                else:
                    # If max_rate is 0, check if min_rate is also 0
                    parity = 1.0 if min_rate == 0 else 0.0
                
                parities.append(parity)
                details[attr] = {
                    "precision_rates": precision_rates,
                    "parity": parity
                }
        
        # Final score is the minimum parity across all attributes
        fairness_score = min(parities) if parities else 1.0
        
        return fairness_score, details
    
    def _disparate_impact(self, data: Dict[str, Any]) -> Tuple[float, Dict[str, Any]]:
        """
        Calculate disparate impact.
        
        Disparate impact measures the ratio between the favorable outcome rates
        for different groups.
        Score of 1.0 means no disparate impact, 0.0 means maximum disparity.
        
        Args:
            data: Dictionary with predictions and protected attributes
                
        Returns:
            Tuple of (fairness_score, details)
        """
        # Calculate demographic parity
        parity, details = self._demographic_parity(data)
        
        # For disparate impact, we typically use the 80% rule (0.8 threshold)
        # Scale the parity score to reflect this
        if parity < 0.8:
            # Scale to reflect the 80% rule: if parity is below 0.8, scale to 0-0.8 range
            disparate_impact_score = parity / 0.8
            for attr in details:
                if "parity" in details[attr]:
                    if details[attr]["parity"] < 0.8:
                        details[attr]["meets_80_percent_rule"] = False
                    else:
                        details[attr]["meets_80_percent_rule"] = True
        else:
            # If parity is above 0.8, it meets the 80% rule
            disparate_impact_score = 1.0
            for attr in details:
                if "parity" in details[attr]:
                    details[attr]["meets_80_percent_rule"] = True
        
        # Add disparate impact specific details
        for attr in details:
            details[attr]["disparate_impact_score"] = disparate_impact_score
        
        return disparate_impact_score, details
    
    def _treatment_equality(self, data: Dict[str, Any]) -> Tuple[float, Dict[str, Any]]:
        """
        Calculate treatment equality.
        
        Treatment equality measures whether the ratio of false negatives to false positives
        is the same across protected attributes.
        Score of 1.0 means perfect equality, 0.0 means maximum inequality.
        
        Args:
            data: Dictionary with predictions, ground truth, and protected attributes
                
        Returns:
            Tuple of (fairness_score, details)
        """
        if "predictions" not in data or "protected_attributes" not in data or "ground_truth" not in data:
            raise ValueError("Data must contain 'predictions', 'ground_truth', and 'protected_attributes'")
        
        predictions = np.array(data["predictions"])
        ground_truth = np.array(data["ground_truth"])
        protected_attributes = data["protected_attributes"]
        
        # Convert predictions to binary if not already
        if not isinstance(predictions[0], (bool, int)) or not all(p in [0, 1] for p in predictions):
            # Assume probabilistic predictions, threshold at 0.5
            binary_predictions = (predictions > 0.5).astype(int)
        else:
            binary_predictions = predictions
        
        # Convert ground truth to binary if not already
        if not isinstance(ground_truth[0], (bool, int)) or not all(g in [0, 1] for g in ground_truth):
            # Assume probabilistic ground truth, threshold at 0.5
            binary_ground_truth = (ground_truth > 0.5).astype(int)
        else:
            binary_ground_truth = ground_truth
        
        # Calculate error rates for each protected attribute
        details = {}
        parities = []
        
        for attr, attr_values in protected_attributes.items():
            attr_values = np.array(attr_values)
            
            # Get unique attribute values
            unique_values = np.unique(attr_values)
            if len(unique_values) < 2:
                continue  # Skip if there's only one group
            
            # Calculate error rates for each group
            fn_rates = {}
            fp_rates = {}
            fnr_to_fpr_ratios = {}
            for value in unique_values:
                group_mask = (attr_values == value)
                group_predictions = binary_predictions[group_mask]
                group_ground_truth = binary_ground_truth[group_mask]
                
                # Calculate FN: actual positive but predicted negative
                positive_mask = (group_ground_truth == 1)
                if np.sum(positive_mask) > 0:
                    fn = np.sum(group_predictions[positive_mask] == 0)
                    fn_rate = fn / np.sum(positive_mask)
                    fn_rates[str(value)] = fn_rate
                
                # Calculate FP: actual negative but predicted positive
                negative_mask = (group_ground_truth == 0)
                if np.sum(negative_mask) > 0:
                    fp = np.sum(group_predictions[negative_mask] == 1)
                    fp_rate = fp / np.sum(negative_mask)
                    fp_rates[str(value)] = fp_rate
                
                # Calculate FN to FP ratio
                if str(value) in fn_rates and str(value) in fp_rates:
                    if fp_rates[str(value)] > 0:
                        fnr_to_fpr_ratio = fn_rates[str(value)] / fp_rates[str(value)]
                    else:
                        # If FP rate is 0, check FN rate
                        fnr_to_fpr_ratio = float('inf') if fn_rates[str(value)] > 0 else 1.0
                    fnr_to_fpr_ratios[str(value)] = fnr_to_fpr_ratio
            
            # Calculate ratio parity
            if fnr_to_fpr_ratios:
                # Filter out infinity values for comparison
                finite_ratios = [r for r in fnr_to_fpr_ratios.values() if r != float('inf')]
                
                if finite_ratios:
                    max_ratio = max(finite_ratios)
                    min_ratio = min(finite_ratios)
                    
                    if max_ratio > 0 and min_ratio > 0:
                        # Use ratio of min/max ratios as parity measure
                        parity = min_ratio / max_ratio
                    else:
                        parity = 0.0
                else:
                    # All ratios are infinity, check if they're all infinity
                    if all(r == float('inf') for r in fnr_to_fpr_ratios.values()):
                        parity = 1.0  # All groups have the same (infinity) ratio
                    else:
                        parity = 0.0  # Some groups have infinity, others don't
                
                parities.append(parity)
                details[attr] = {
                    "false_negative_rates": fn_rates,
                    "false_positive_rates": fp_rates,
                    "fnr_to_fpr_ratios": {k: v for k, v in fnr_to_fpr_ratios.items() if v != float('inf')},
                    "parity": parity
                }
        
        # Final score is the minimum parity across all attributes
        fairness_score = min(parities) if parities else 1.0
        
        return fairness_score, details
    
    def get_fairness_report(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate a comprehensive fairness report.
        
        Args:
            data: Dictionary containing model predictions, optional ground truth, and protected attributes
                
        Returns:
            Dictionary containing detailed fairness analysis
        """
        with self._lock:
            report = {
                "timestamp": time.time(),
                "overall_fairness": None,
                "metrics": {},
                "protected_attributes": {},
                "recommendations": []
            }
            
            # Check if data is valid
            if "predictions" not in data or "protected_attributes" not in data:
                logger.error("Missing required data for fairness report")
                report["error"] = "Missing required data: 'predictions' and 'protected_attributes'"
                return report
            
            # Calculate all applicable metrics
            metric_results = self.calculate_all_metrics(data)
            
            if not metric_results:
                logger.warning("No fairness metrics could be calculated")
                report["error"] = "No fairness metrics could be calculated with the provided data"
                return report
            
            # Process metric results
            fairness_scores = []
            for metric_name, (score, details) in metric_results.items():
                report["metrics"][metric_name] = {
                    "score": score,
                    "details": details
                }
                fairness_scores.append(score)
                
                # Add interpretation based on score
                if score >= 0.9:
                    report["metrics"][metric_name]["interpretation"] = f"High fairness according to {metric_name}"
                elif score >= 0.8:
                    report["metrics"][metric_name]["interpretation"] = f"Good fairness according to {metric_name}"
                elif score >= 0.6:
                    report["metrics"][metric_name]["interpretation"] = f"Moderate fairness according to {metric_name}"
                else:
                    report["metrics"][metric_name]["interpretation"] = f"Low fairness according to {metric_name}"
                    report["recommendations"].append(f"Address fairness issues related to {metric_name}")
            
            # Calculate overall fairness score
            if fairness_scores:
                report["overall_fairness"] = sum(fairness_scores) / len(fairness_scores)
            
            # Analyze protected attributes
            protected_attributes = data["protected_attributes"]
            for attr, values in protected_attributes.items():
                attr_values = np.array(values)
                unique_values = np.unique(attr_values)
                
                report["protected_attributes"][attr] = {
                    "unique_values": len(unique_values),
                    "distribution": {str(value): np.sum(attr_values == value) / len(attr_values) for value in unique_values}
                }
                
                # Check if this attribute has fairness issues in any metric
                attr_issues = False
                for metric_name, (score, details) in metric_results.items():
                    if attr in details and details[attr].get("parity", 1.0) < 0.8:
                        attr_issues = True
                        break
                
                report["protected_attributes"][attr]["has_fairness_issues"] = attr_issues
                
                if attr_issues:
                    report["recommendations"].append(f"Review distribution and model behavior for attribute '{attr}'")
            
            # Add generic recommendations if none specific
            if not report["recommendations"] and report["overall_fairness"] < 0.8:
                report["recommendations"].append("Consider techniques like pre-processing, in-processing, or post-processing to improve fairness")
                report["recommendations"].append("Review data collection practices to ensure representative sampling")
            
            return report


class ExplainabilityEngine:
    """
    Provides explanations for decisions and actions of the system.
    
    The ExplainabilityEngine generates human-understandable explanations
    for the system's operations, focusing on transparency and accountability.
    """
    
    def __init__(self, 
                explanation_templates: Dict[str, str] = None,
                model_interpretability: Dict[str, float] = None,
                use_templates: bool = True):
        """
        Initialize an explainability engine.
        
        Args:
            explanation_templates: Dictionary of templates for different explanation types
            model_interpretability: Dictionary mapping model types to interpretability scores
            use_templates: Whether to use templates for explanations
        """
        self.explanation_templates = explanation_templates or self._default_templates()
        self.model_interpretability = model_interpretability or self._default_interpretability()
        self.use_templates = use_templates
        self._lock = threading.RLock()  # For thread safety
        
        # Set up interpretability strategies
        self.interpretability_strategies = {
            "decision_tree": self._explain_decision_tree,
            "linear_model": self._explain_linear_model,
            "rule_based": self._explain_rule_based,
            "neural_network": self._explain_neural_network,
            "ensemble": self._explain_ensemble,
            "transformer": self._explain_transformer,
            "diffusion": self._explain_diffusion,
            "general": self._explain_general
        }
    
    def _default_templates(self) -> Dict[str, str]:
        """
        Define default explanation templates.
        
        Returns:
            Dictionary of template strings
        """
        return {
            "decision": "This decision was made based on {factors}. The most important factors were {top_factors}.",
            "classification": "This item was classified as {class_name} with {confidence}% confidence, based primarily on {top_features}.",
            "recommendation": "This recommendation was made because {reasons}. Similar items that influenced this recommendation include {similar_items}.",
            "action": "This action was taken because {reasons}. The expected outcome is {outcome}.",
            "error": "An error occurred because {reasons}. Alternative approaches to consider are {alternatives}.",
            "fairness": "The fairness of this decision was evaluated using {metrics}. The results show {fairness_result}.",
            "privacy": "Your data was processed according to {privacy_policy}. Specifically, {data_usage}.",
            "general": "This outcome was produced by {model_type}. The process involved {process_description}."
        }
    
    def _default_interpretability(self) -> Dict[str, float]:
        """
        Define default interpretability scores for different model types.
        
        Returns:
            Dictionary mapping model types to interpretability scores (0-1)
        """
        return {
            "decision_tree": 0.9,
            "rule_based": 0.9,
            "linear_model": 0.8,
            "logistic_regression": 0.8,
            "naive_bayes": 0.7,
            "random_forest": 0.6,
            "gradient_boosting": 0.5,
            "svm": 0.5,
            "neural_network": 0.3,
            "deep_learning": 0.2,
            "transformer": 0.2,
            "diffusion": 0.1
        }
    
    def generate_explanation(self, subject: Any, context: Dict[str, Any] = None) -> str:
        """
        Generate an explanation for the subject.
        
        Args:
            subject: The subject to explain (decision, action, model, etc.)
            context: Additional context information
            
        Returns:
            String containing the explanation
        """
        with self._lock:
            # Extract explanation type and model type
            explanation_type = self._determine_explanation_type(subject, context)
            model_type = self._determine_model_type(subject, context)
            
            # Check if an explanation is already available
            explicit_explanation = self._check_explicit_explanation(subject, context)
            if explicit_explanation:
                return explicit_explanation
            
            # Generate explanation based on model type
            strategy = self.interpretability_strategies.get(model_type, self.interpretability_strategies["general"])
            interpretation = strategy(subject, context)
            
            # Format using template if available and enabled
            if self.use_templates and explanation_type in self.explanation_templates:
                template = self.explanation_templates[explanation_type]
                
                # Extract variables from interpretation to fill template
                template_vars = self._extract_template_variables(interpretation, subject, context)
                
                try:
                    formatted_explanation = template.format(**template_vars)
                    return formatted_explanation
                except KeyError as e:
                    logger.warning(f"Missing template variable: {e}")
                    return interpretation  # Fall back to raw interpretation
            
            return interpretation
    
    def _determine_explanation_type(self, subject: Any, context: Dict[str, Any] = None) -> str:
        """
        Determine the type of explanation needed.
        
        Args:
            subject: The subject to explain
            context: Additional context information
            
        Returns:
            String indicating explanation type
        """
        # Check explicit type in subject or context
        if isinstance(subject, dict) and "explanation_type" in subject:
            return subject["explanation_type"]
        
        if context and "explanation_type" in context:
            return context["explanation_type"]
        
        # Try to infer from subject
        if isinstance(subject, dict):
            if "class" in subject or "classification" in subject or "predicted_class" in subject:
                return "classification"
            elif "decision" in subject or "outcome" in subject:
                return "decision"
            elif "recommendation" in subject or "suggestions" in subject:
                return "recommendation"
            elif "action" in subject or "operation" in subject:
                return "action"
            elif "error" in subject or "exception" in subject:
                return "error"
            elif "fairness" in subject or "bias" in subject:
                return "fairness"
            elif "privacy" in subject or "data_protection" in subject:
                return "privacy"
        
        # Default to general explanation
        return "general"
    
    def _determine_model_type(self, subject: Any, context: Dict[str, Any] = None) -> str:
        """
        Determine the model type for generating appropriate explanations.
        
        Args:
            subject: The subject to explain
            context: Additional context information
            
        Returns:
            String indicating model type
        """
        # Check explicit model type in subject or context
        if isinstance(subject, dict) and "model_type" in subject:
            model_type = subject["model_type"].lower()
            
            # Map to one of our strategy types
            if "tree" in model_type or "forest" in model_type or "xgboost" in model_type:
                return "decision_tree"
            elif "linear" in model_type or "regression" in model_type or "logistic" in model_type:
                return "linear_model"
            elif "rule" in model_type or "symbolic" in model_type:
                return "rule_based"
            elif "neural" in model_type or "deep" in model_type or "cnn" in model_type or "rnn" in model_type:
                return "neural_network"
            elif "ensemble" in model_type or "boost" in model_type or "bag" in model_type:
                return "ensemble"
            elif "transformer" in model_type or "attention" in model_type or "bert" in model_type or "gpt" in model_type:
                return "transformer"
            elif "diffusion" in model_type:
                return "diffusion"
        
        if context and "model_type" in context:
            model_type = context["model_type"].lower()
            
            # Same mapping logic as above
            if "tree" in model_type or "forest" in model_type or "xgboost" in model_type:
                return "decision_tree"
            elif "linear" in model_type or "regression" in model_type or "logistic" in model_type:
                return "linear_model"
            elif "rule" in model_type or "symbolic" in model_type:
                return "rule_based"
            elif "neural" in model_type or "deep" in model_type or "cnn" in model_type or "rnn" in model_type:
                return "neural_network"
            elif "ensemble" in model_type or "boost" in model_type or "bag" in model_type:
                return "ensemble"
            elif "transformer" in model_type or "attention" in model_type or "bert" in model_type or "gpt" in model_type:
                return "transformer"
            elif "diffusion" in model_type:
                return "diffusion"
        
        # Try to infer from features or other hints
        if isinstance(subject, dict):
            if "feature_importances" in subject or "coefficients" in subject:
                return "linear_model"
            elif "rules" in subject or "conditions" in subject:
                return "rule_based"
            elif "attention_weights" in subject or "transformer" in str(subject).lower():
                return "transformer"
            elif "layers" in subject or "weights" in subject or "neural" in str(subject).lower():
                return "neural_network"
            elif "trees" in subject or "decision_path" in subject:
                return "decision_tree"
        
        # Default to general
        return "general"
    
    def _check_explicit_explanation(self, subject: Any, context: Dict[str, Any] = None) -> Optional[str]:
        """
        Check if an explicit explanation is already available.
        
        Args:
            subject: The subject to explain
            context: Additional context information
            
        Returns:
            String explanation if available, None otherwise
        """
        # Check subject for explanation
        if isinstance(subject, dict):
            if "explanation" in subject:
                explanation = subject["explanation"]
                if isinstance(explanation, str) and explanation.strip():
                    return explanation
            
            if "reason" in subject:
                reason = subject["reason"]
                if isinstance(reason, str) and reason.strip():
                    return reason
        
        # Check context for explanation
        if context:
            if "explanation" in context:
                explanation = context["explanation"]
                if isinstance(explanation, str) and explanation.strip():
                    return explanation
            
            if "reason" in context:
                reason = context["reason"]
                if isinstance(reason, str) and reason.strip():
                    return reason
        
        return None
    
    def _extract_template_variables(self, interpretation: str, subject: Any, context: Dict[str, Any] = None) -> Dict[str, str]:
        """
        Extract variables from interpretation and context to fill explanation templates.
        
        Args:
            interpretation: The raw interpretation text
            subject: The subject being explained
            context: Additional context information
            
        Returns:
            Dictionary of template variables
        """
        template_vars = {}
        
        # Extract variables from subject
        if isinstance(subject, dict):
            # Common template variables
            if "factors" in subject:
                if isinstance(subject["factors"], list):
                    template_vars["factors"] = ", ".join(str(f) for f in subject["factors"])
                else:
                    template_vars["factors"] = str(subject["factors"])
            
            if "top_factors" in subject:
                if isinstance(subject["top_factors"], list):
                    template_vars["top_factors"] = ", ".join(str(f) for f in subject["top_factors"])
                else:
                    template_vars["top_factors"] = str(subject["top_factors"])
            
            # Classification-specific
            if "class_name" in subject:
                template_vars["class_name"] = str(subject["class_name"])
            
            if "confidence" in subject:
                try:
                    confidence = float(subject["confidence"]) * 100
                    template_vars["confidence"] = f"{confidence:.1f}"
                except (ValueError, TypeError):
                    template_vars["confidence"] = str(subject["confidence"])
            
            if "top_features" in subject:
                if isinstance(subject["top_features"], list):
                    template_vars["top_features"] = ", ".join(str(f) for f in subject["top_features"])
                else:
                    template_vars["top_features"] = str(subject["top_features"])
            
            # Recommendation-specific
            if "reasons" in subject:
                if isinstance(subject["reasons"], list):
                    template_vars["reasons"] = ", ".join(str(r) for r in subject["reasons"])
                else:
                    template_vars["reasons"] = str(subject["reasons"])
            
            if "similar_items" in subject:
                if isinstance(subject["similar_items"], list):
                    template_vars["similar_items"] = ", ".join(str(i) for i in subject["similar_items"])
                else:
                    template_vars["similar_items"] = str(subject["similar_items"])
            
            # Action-specific
            if "outcome" in subject:
                template_vars["outcome"] = str(subject["outcome"])
            
            # Error-specific
            if "alternatives" in subject:
                if isinstance(subject["alternatives"], list):
                    template_vars["alternatives"] = ", ".join(str(a) for a in subject["alternatives"])
                else:
                    template_vars["alternatives"] = str(subject["alternatives"])
            
            # Fairness-specific
            if "metrics" in subject:
                if isinstance(subject["metrics"], list):
                    template_vars["metrics"] = ", ".join(str(m) for m in subject["metrics"])
                else:
                    template_vars["metrics"] = str(subject["metrics"])
            
            if "fairness_result" in subject:
                template_vars["fairness_result"] = str(subject["fairness_result"])
            
            # Privacy-specific
            if "privacy_policy" in subject:
                template_vars["privacy_policy"] = str(subject["privacy_policy"])
            
            if "data_usage" in subject:
                template_vars["data_usage"] = str(subject["data_usage"])
            
            # General
            if "model_type" in subject:
                template_vars["model_type"] = str(subject["model_type"])
            
            if "process_description" in subject:
                template_vars["process_description"] = str(subject["process_description"])
        
        # Extract variables from context
        if context:
            for key in [
                "factors", "top_factors", "class_name", "confidence", "top_features",
                "reasons", "similar_items", "outcome", "alternatives", "metrics",
                "fairness_result", "privacy_policy", "data_usage", "model_type",
                "process_description"
            ]:
                if key in context and key not in template_vars:
                    if isinstance(context[key], list):
                        template_vars[key] = ", ".join(str(item) for item in context[key])
                    else:
                        template_vars[key] = str(context[key])
        
        # Fill in missing variables with defaults
        for key in [
            "factors", "top_factors", "class_name", "confidence", "top_features",
            "reasons", "similar_items", "outcome", "alternatives", "metrics",
            "fairness_result", "privacy_policy", "data_usage", "model_type",
            "process_description"
        ]:
            if key not in template_vars:
                template_vars[key] = "relevant information"
        
        return template_vars
    
    def _explain_decision_tree(self, subject: Any, context: Dict[str, Any] = None) -> str:
        """
        Generate explanation for decision tree type models.
        
        Args:
            subject: The subject to explain
            context: Additional context information
            
        Returns:
            Explanation string
        """
        # Look for decision path or feature importances
        decision_path = None
        feature_importances = None
        features = None
        
        if isinstance(subject, dict):
            if "decision_path" in subject:
                decision_path = subject["decision_path"]
            elif "conditions" in subject:
                decision_path = subject["conditions"]
            
            if "feature_importances" in subject:
                feature_importances = subject["feature_importances"]
            
            if "features" in subject:
                features = subject["features"]
        
        if context:
            if not decision_path and "decision_path" in context:
                decision_path = context["decision_path"]
            elif not decision_path and "conditions" in context:
                decision_path = context["conditions"]
            
            if not feature_importances and "feature_importances" in context:
                feature_importances = context["feature_importances"]
            
            if not features and "features" in context:
                features = context["features"]
        
        # Generate explanation based on available information
        explanation_parts = []
        
        if decision_path:
            if isinstance(decision_path, list):
                path_str = " → ".join(str(step) for step in decision_path)
                explanation_parts.append(f"This decision followed a path through the decision tree: {path_str}")
            else:
                explanation_parts.append(f"This decision was based on the following conditions: {decision_path}")
        
        if feature_importances:
            if isinstance(feature_importances, dict):
                # Sort features by importance
                sorted_features = sorted(feature_importances.items(), key=lambda x: x[1], reverse=True)
                top_features = sorted_features[:3]  # Take top 3
                
                # Format feature names and importances
                if features and isinstance(features, list):
                    # Map feature indices to names if available
                    top_features_str = ", ".join(f"{features[int(idx)] if idx.isdigit() else idx} ({importance:.2f})" 
                                              for idx, importance in top_features)
                else:
                    top_features_str = ", ".join(f"{feature} ({importance:.2f})" 
                                              for feature, importance in top_features)
                
                explanation_parts.append(f"The most important features in this decision were: {top_features_str}")
            elif isinstance(feature_importances, list):
                if features and isinstance(features, list) and len(features) == len(feature_importances):
                    # Create feature-importance pairs
                    pairs = [(features[i], importance) for i, importance in enumerate(feature_importances)]
                    # Sort by importance
                    sorted_pairs = sorted(pairs, key=lambda x: x[1], reverse=True)
                    top_features = sorted_pairs[:3]  # Take top 3
                    top_features_str = ", ".join(f"{feature} ({importance:.2f})" 
                                              for feature, importance in top_features)
                    explanation_parts.append(f"The most important features in this decision were: {top_features_str}")
                else:
                    # Just list importances without feature names
                    sorted_importances = sorted(enumerate(feature_importances), key=lambda x: x[1], reverse=True)
                    top_features = sorted_importances[:3]  # Take top 3
                    top_features_str = ", ".join(f"Feature {idx} ({importance:.2f})" 
                                              for idx, importance in top_features)
                    explanation_parts.append(f"The most important features in this decision were: {top_features_str}")
        
        # If no specific information was found, provide a generic explanation
        if not explanation_parts:
            explanation_parts.append("This decision was made using a decision tree model, which made a series of yes/no decisions based on your data to reach a conclusion.")
            explanation_parts.append("Decision trees are highly interpretable models that split data based on feature values.")
        
        return " ".join(explanation_parts)
    
    def _explain_linear_model(self, subject: Any, context: Dict[str, Any] = None) -> str:
        """
        Generate explanation for linear models.
        
        Args:
            subject: The subject to explain
            context: Additional context information
            
        Returns:
            Explanation string
        """
        # Look for coefficients
        coefficients = None
        features = None
        intercept = None
        
        if isinstance(subject, dict):
            if "coefficients" in subject:
                coefficients = subject["coefficients"]
            
            if "features" in subject:
                features = subject["features"]
            
            if "intercept" in subject:
                intercept = subject["intercept"]
        
        if context:
            if not coefficients and "coefficients" in context:
                coefficients = context["coefficients"]
            
            if not features and "features" in context:
                features = context["features"]
            
            if intercept is None and "intercept" in context:
                intercept = context["intercept"]
        
        # Generate explanation based on available information
        explanation_parts = []
        
        if coefficients:
            if isinstance(coefficients, dict):
                # Sort features by absolute coefficient value
                sorted_features = sorted(coefficients.items(), key=lambda x: abs(x[1]), reverse=True)
                top_features = sorted_features[:3]  # Take top 3
                
                # Format coefficient direction
                top_features_str = ", ".join(f"{feature} ({'positively' if coef > 0 else 'negatively'}, weight: {abs(coef):.2f})" 
                                          for feature, coef in top_features)
                
                explanation_parts.append(f"This decision was influenced most by: {top_features_str}")
            elif isinstance(coefficients, list):
                if features and isinstance(features, list) and len(features) == len(coefficients):
                    # Create feature-coefficient pairs
                    pairs = [(features[i], coef) for i, coef in enumerate(coefficients)]
                    # Sort by absolute coefficient value
                    sorted_pairs = sorted(pairs, key=lambda x: abs(x[1]), reverse=True)
                    top_features = sorted_pairs[:3]  # Take top 3
                    top_features_str = ", ".join(f"{feature} ({'positively' if coef > 0 else 'negatively'}, weight: {abs(coef):.2f})" 
                                              for feature, coef in top_features)
                    explanation_parts.append(f"This decision was influenced most by: {top_features_str}")
                else:
                    # Just list coefficients without feature names
                    sorted_coefficients = sorted(enumerate(coefficients), key=lambda x: abs(x[1]), reverse=True)
                    top_features = sorted_coefficients[:3]  # Take top 3
                    top_features_str = ", ".join(f"Feature {idx} ({'positively' if coef > 0 else 'negatively'}, weight: {abs(coef):.2f})" 
                                              for idx, coef in top_features)
                    explanation_parts.append(f"This decision was influenced most by: {top_features_str}")
        
        if intercept is not None:
            explanation_parts.append(f"The baseline value (intercept) for this model is {intercept:.2f}.")
        
        # If no specific information was found, provide a generic explanation
        if not explanation_parts:
            explanation_parts.append("This decision was made using a linear model, which weighs various factors to produce an outcome.")
            explanation_parts.append("Linear models are interpretable because each factor contributes directly to the final result with a specific weight.")
        
        return " ".join(explanation_parts)
    
    def _explain_rule_based(self, subject: Any, context: Dict[str, Any] = None) -> str:
        """
        Generate explanation for rule-based systems.
        
        Args:
            subject: The subject to explain
            context: Additional context information
            
        Returns:
            Explanation string
        """
        # Look for rules or conditions
        rules = None
        matched_rules = None
        
        if isinstance(subject, dict):
            if "rules" in subject:
                rules = subject["rules"]
            
            if "matched_rules" in subject:
                matched_rules = subject["matched_rules"]
            elif "triggered_rules" in subject:
                matched_rules = subject["triggered_rules"]
            elif "applied_rules" in subject:
                matched_rules = subject["applied_rules"]
        
        if context:
            if not rules and "rules" in context:
                rules = context["rules"]
            
            if not matched_rules:
                for key in ["matched_rules", "triggered_rules", "applied_rules"]:
                    if key in context:
                        matched_rules = context[key]
                        break
        
        # Generate explanation based on available information
        explanation_parts = []
        
        if matched_rules:
            if isinstance(matched_rules, list):
                if len(matched_rules) > 0:
                    rules_str = "; ".join(str(rule) for rule in matched_rules[:3])  # Show up to 3 rules
                    if len(matched_rules) > 3:
                        rules_str += f"; and {len(matched_rules) - 3} more"
                    explanation_parts.append(f"This decision was made because the following rules were triggered: {rules_str}")
                else:
                    explanation_parts.append("No specific rules were triggered for this decision.")
            else:
                explanation_parts.append(f"This decision was made because the following rules were triggered: {matched_rules}")
        elif rules:
            explanation_parts.append("This decision was made based on a set of predefined rules.")
            if isinstance(rules, list) and len(rules) > 0:
                rules_str = "; ".join(str(rule) for rule in rules[:3])  # Show up to 3 rules
                if len(rules) > 3:
                    rules_str += f"; and {len(rules) - 3} more"
                explanation_parts.append(f"The system uses rules such as: {rules_str}")
        
        # If no specific information was found, provide a generic explanation
        if not explanation_parts:
            explanation_parts.append("This decision was made using a rule-based system, which applies logical rules to your data.")
            explanation_parts.append("Rule-based systems are highly interpretable because they follow explicit if-then conditions.")
        
        return " ".join(explanation_parts)
    
    def _explain_neural_network(self, subject: Any, context: Dict[str, Any] = None) -> str:
        """
        Generate explanation for neural network models.
        
        Args:
            subject: The subject to explain
            context: Additional context information
            
        Returns:
            Explanation string
        """
        # Look for feature importance approximations or attention weights
        feature_importances = None
        features = None
        architecture = None
        
        if isinstance(subject, dict):
            if "feature_importances" in subject:
                feature_importances = subject["feature_importances"]
            elif "feature_attributions" in subject:
                feature_importances = subject["feature_attributions"]
            elif "saliency" in subject:
                feature_importances = subject["saliency"]
            
            if "features" in subject:
                features = subject["features"]
            
            if "architecture" in subject:
                architecture = subject["architecture"]
            elif "layers" in subject:
                architecture = subject["layers"]
        
        if context:
            if not feature_importances:
                for key in ["feature_importances", "feature_attributions", "saliency"]:
                    if key in context:
                        feature_importances = context[key]
                        break
            
            if not features and "features" in context:
                features = context["features"]
            
            if not architecture:
                for key in ["architecture", "layers"]:
                    if key in context:
                        architecture = context[key]
                        break
        
        # Generate explanation based on available information
        explanation_parts = []
        
        if feature_importances:
            if isinstance(feature_importances, dict):
                # Sort features by importance
                sorted_features = sorted(feature_importances.items(), key=lambda x: x[1], reverse=True)
                top_features = sorted_features[:3]  # Take top 3
                
                # Format feature names and importances
                top_features_str = ", ".join(f"{feature} ({importance:.2f})" 
                                          for feature, importance in top_features)
                
                explanation_parts.append(f"The most influential features for this decision were: {top_features_str}")
            elif isinstance(feature_importances, list):
                if features and isinstance(features, list) and len(features) == len(feature_importances):
                    # Create feature-importance pairs
                    pairs = [(features[i], importance) for i, importance in enumerate(feature_importances)]
                    # Sort by importance
                    sorted_pairs = sorted(pairs, key=lambda x: x[1], reverse=True)
                    top_features = sorted_pairs[:3]  # Take top 3
                    top_features_str = ", ".join(f"{feature} ({importance:.2f})" 
                                              for feature, importance in top_features)
                    explanation_parts.append(f"The most influential features for this decision were: {top_features_str}")
                else:
                    # Just list importances without feature names
                    sorted_importances = sorted(enumerate(feature_importances), key=lambda x: x[1], reverse=True)
                    top_features = sorted_importances[:3]  # Take top 3
                    top_features_str = ", ".join(f"Feature {idx} ({importance:.2f})" 
                                              for idx, importance in top_features)
                    explanation_parts.append(f"The most influential features for this decision were: {top_features_str}")
        
        if architecture:
            if isinstance(architecture, list):
                layers_description = f"{len(architecture)} layers"
                explanation_parts.append(f"This decision was made by a neural network with {layers_description}.")
            else:
                explanation_parts.append(f"This decision was made by a neural network with architecture: {architecture}")
        
        # If no specific information was found, provide a generic explanation
        if not explanation_parts:
            explanation_parts.append("This decision was made using a neural network model, which learns complex patterns from data.")
            explanation_parts.append("Neural networks are powerful but can be less interpretable than simpler models. This explanation is based on an analysis of how the model responds to the input data.")
        
        return " ".join(explanation_parts)
    
    def _explain_ensemble(self, subject: Any, context: Dict[str, Any] = None) -> str:
        """
        Generate explanation for ensemble models.
        
        Args:
            subject: The subject to explain
            context: Additional context information
            
        Returns:
            Explanation string
        """
        # Look for feature importances or model votes
        feature_importances = None
        features = None
        model_votes = None
        ensemble_type = None
        
        if isinstance(subject, dict):
            if "feature_importances" in subject:
                feature_importances = subject["feature_importances"]
            
            if "features" in subject:
                features = subject["features"]
            
            if "model_votes" in subject:
                model_votes = subject["model_votes"]
            elif "votes" in subject:
                model_votes = subject["votes"]
            
            if "ensemble_type" in subject:
                ensemble_type = subject["ensemble_type"]
            elif "type" in subject:
                ensemble_type = subject["type"]
        
        if context:
            if not feature_importances and "feature_importances" in context:
                feature_importances = context["feature_importances"]
            
            if not features and "features" in context:
                features = context["features"]
            
            if not model_votes:
                for key in ["model_votes", "votes"]:
                    if key in context:
                        model_votes = context[key]
                        break
            
            if not ensemble_type:
                for key in ["ensemble_type", "type"]:
                    if key in context:
                        ensemble_type = context[key]
                        break
        
        # Generate explanation based on available information
        explanation_parts = []
        
        # Describe ensemble type if available
        if ensemble_type:
            if "boost" in str(ensemble_type).lower():
                explanation_parts.append("This decision was made using a boosting ensemble, which combines multiple models sequentially, with each model correcting the errors of previous models.")
            elif "bag" in str(ensemble_type).lower() or "forest" in str(ensemble_type).lower():
                explanation_parts.append("This decision was made using a bagging ensemble, which combines multiple independent models to reduce variance and avoid overfitting.")
            elif "stack" in str(ensemble_type).lower():
                explanation_parts.append("This decision was made using a stacking ensemble, which uses a meta-model to combine predictions from multiple base models.")
            else:
                explanation_parts.append(f"This decision was made using an ensemble of type: {ensemble_type}")
        else:
            explanation_parts.append("This decision was made using an ensemble of multiple models, which combines their predictions to improve accuracy and robustness.")
        
        # Include feature importances if available
        if feature_importances:
            if isinstance(feature_importances, dict):
                # Sort features by importance
                sorted_features = sorted(feature_importances.items(), key=lambda x: x[1], reverse=True)
                top_features = sorted_features[:3]  # Take top 3
                
                # Format feature names and importances
                top_features_str = ", ".join(f"{feature} ({importance:.2f})" 
                                          for feature, importance in top_features)
                
                explanation_parts.append(f"The most important features across the ensemble were: {top_features_str}")
            elif isinstance(feature_importances, list):
                if features and isinstance(features, list) and len(features) == len(feature_importances):
                    # Create feature-importance pairs
                    pairs = [(features[i], importance) for i, importance in enumerate(feature_importances)]
                    # Sort by importance
                    sorted_pairs = sorted(pairs, key=lambda x: x[1], reverse=True)
                    top_features = sorted_pairs[:3]  # Take top 3
                    top_features_str = ", ".join(f"{feature} ({importance:.2f})" 
                                              for feature, importance in top_features)
                    explanation_parts.append(f"The most important features across the ensemble were: {top_features_str}")
                else:
                    # Just list importances without feature names
                    sorted_importances = sorted(enumerate(feature_importances), key=lambda x: x[1], reverse=True)
                    top_features = sorted_importances[:3]  # Take top 3
                    top_features_str = ", ".join(f"Feature {idx} ({importance:.2f})" 
                                              for idx, importance in top_features)
                    explanation_parts.append(f"The most important features across the ensemble were: {top_features_str}")
        
        # Include model votes if available
        if model_votes:
            if isinstance(model_votes, dict):
                votes_str = ", ".join(f"{outcome}: {votes}" for outcome, votes in model_votes.items())
                explanation_parts.append(f"The models in the ensemble voted as follows: {votes_str}")
            elif isinstance(model_votes, list):
                vote_count = {}
                for vote in model_votes:
                    vote_count[vote] = vote_count.get(vote, 0) + 1
                votes_str = ", ".join(f"{outcome}: {votes}" for outcome, votes in vote_count.items())
                explanation_parts.append(f"The models in the ensemble voted as follows: {votes_str}")
        
        return " ".join(explanation_parts)
    
    def _explain_transformer(self, subject: Any, context: Dict[str, Any] = None) -> str:
        """
        Generate explanation for transformer models.
        
        Args:
            subject: The subject to explain
            context: Additional context information
            
        Returns:
            Explanation string
        """
        # Look for attention weights or token attributions
        attention_weights = None
        tokens = None
        token_attributions = None
        
        if isinstance(subject, dict):
            if "attention_weights" in subject:
                attention_weights = subject["attention_weights"]
            elif "attention" in subject:
                attention_weights = subject["attention"]
            
            if "tokens" in subject:
                tokens = subject["tokens"]
            
            if "token_attributions" in subject:
                token_attributions = subject["token_attributions"]
            elif "attributions" in subject:
                token_attributions = subject["attributions"]
        
        if context:
            if not attention_weights:
                for key in ["attention_weights", "attention"]:
                    if key in context:
                        attention_weights = context[key]
                        break
            
            if not tokens and "tokens" in context:
                tokens = context["tokens"]
            
            if not token_attributions:
                for key in ["token_attributions", "attributions"]:
                    if key in context:
                        token_attributions = context[key]
                        break
        
        # Generate explanation based on available information
        explanation_parts = []
        
        explanation_parts.append("This decision was made using a transformer model, which processes text or data by paying attention to different parts of the input with varying intensity.")
        
        # Include token attributions if available
        if token_attributions:
            if isinstance(token_attributions, dict) and tokens:
                # Sort tokens by attribution
                sorted_tokens = sorted(token_attributions.items(), key=lambda x: x[1], reverse=True)
                top_tokens = sorted_tokens[:5]  # Take top 5
                
                # Format tokens and attributions
                top_tokens_str = ", ".join(f"'{tokens[int(idx)]}' ({attr:.2f})" if idx.isdigit() and int(idx) < len(tokens) 
                                       else f"'{idx}' ({attr:.2f})" for idx, attr in top_tokens)
                
                explanation_parts.append(f"The model paid most attention to: {top_tokens_str}")
            elif isinstance(token_attributions, list) and tokens and len(token_attributions) == len(tokens):
                # Create token-attribution pairs
                pairs = [(tokens[i], attr) for i, attr in enumerate(token_attributions)]
                # Sort by attribution
                sorted_pairs = sorted(pairs, key=lambda x: x[1], reverse=True)
                top_tokens = sorted_pairs[:5]  # Take top 5
                top_tokens_str = ", ".join(f"'{token}' ({attr:.2f})" for token, attr in top_tokens)
                explanation_parts.append(f"The model paid most attention to: {top_tokens_str}")
            elif isinstance(token_attributions, list):
                # Just list attributions without token names
                sorted_attributions = sorted(enumerate(token_attributions), key=lambda x: x[1], reverse=True)
                top_tokens = sorted_attributions[:5]  # Take top 5
                top_tokens_str = ", ".join(f"Token {idx} ({attr:.2f})" for idx, attr in top_tokens)
                explanation_parts.append(f"The model paid most attention to: {top_tokens_str}")
        elif attention_weights and tokens:
            # This is a simplified handling of attention weights, which in reality are multi-dimensional
            if isinstance(attention_weights, list) and len(attention_weights) == len(tokens):
                # Assume weights correspond to tokens
                pairs = [(tokens[i], weight) for i, weight in enumerate(attention_weights)]
                # Sort by weight
                sorted_pairs = sorted(pairs, key=lambda x: x[1], reverse=True)
                top_tokens = sorted_pairs[:5]  # Take top 5
                top_tokens_str = ", ".join(f"'{token}' ({weight:.2f})" for token, weight in top_tokens)
                explanation_parts.append(f"The model paid most attention to: {top_tokens_str}")
        
        # If no specific information was found, provide a generic explanation
        if len(explanation_parts) <= 1:
            explanation_parts.append("Transformer models are complex and process information through multiple layers of attention mechanisms. This explanation is an approximation of how the model arrived at its decision.")
        
        return " ".join(explanation_parts)
    
    def _explain_diffusion(self, subject: Any, context: Dict[str, Any] = None) -> str:
        """
        Generate explanation for diffusion models.
        
        Args:
            subject: The subject to explain
            context: Additional context information
            
        Returns:
            Explanation string
        """
        # Diffusion models are among the hardest to explain
        # Look for any guidance information or conditioning
        guidance_scale = None
        conditioning = None
        steps = None
        
        if isinstance(subject, dict):
            if "guidance_scale" in subject:
                guidance_scale = subject["guidance_scale"]
            
            if "conditioning" in subject:
                conditioning = subject["conditioning"]
            elif "condition" in subject:
                conditioning = subject["condition"]
            
            if "steps" in subject:
                steps = subject["steps"]
            elif "num_steps" in subject:
                steps = subject["num_steps"]
        
        if context:
            if guidance_scale is None:
                if "guidance_scale" in context:
                    guidance_scale = context["guidance_scale"]
            
            if not conditioning:
                for key in ["conditioning", "condition"]:
                    if key in context:
                        conditioning = context[key]
                        break
            
            if steps is None:
                for key in ["steps", "num_steps"]:
                    if key in context:
                        steps = context[key]
                        break
        
        # Generate explanation based on available information
        explanation_parts = []
        
        explanation_parts.append("This was created using a diffusion model, which generates outputs by gradually removing noise from a random starting point, guided by the input specifications.")
        
        if conditioning:
            explanation_parts.append(f"The generation was conditioned on: {conditioning}")
        
        if guidance_scale is not None:
            if float(guidance_scale) > 7.0:
                explanation_parts.append(f"The generation used a high guidance scale ({guidance_scale}), resulting in output that closely follows the conditioning but may be less diverse.")
            elif float(guidance_scale) < 3.0:
                explanation_parts.append(f"The generation used a low guidance scale ({guidance_scale}), resulting in more diverse output that may deviate from the conditioning.")
            else:
                explanation_parts.append(f"The generation used a moderate guidance scale ({guidance_scale}), balancing adherence to conditioning with diversity.")
        
        if steps is not None:
            if int(steps) > 50:
                explanation_parts.append(f"The process used {steps} denoising steps, which typically results in higher quality but takes longer.")
            elif int(steps) < 20:
                explanation_parts.append(f"The process used {steps} denoising steps, which is faster but may result in lower quality.")
            else:
                explanation_parts.append(f"The process used {steps} denoising steps.")
        
        # If minimal information available, provide generic explanation
        if len(explanation_parts) <= 1:
            explanation_parts.append("Diffusion models work by learning to reverse a gradual noise-adding process, allowing them to generate highly structured data from random noise.")
            explanation_parts.append("These models are powerful but often less interpretable than other approaches.")
        
        return " ".join(explanation_parts)
    
    def _explain_general(self, subject: Any, context: Dict[str, Any] = None) -> str:
        """
        Generate general explanation when specific model type is unknown.
        
        Args:
            subject: The subject to explain
            context: Additional context information
            
        Returns:
            Explanation string
        """
        # Look for any available information about the decision/action
        factors = None
        confidence = None
        process = None
        
        if isinstance(subject, dict):
            if "factors" in subject:
                factors = subject["factors"]
            elif "features" in subject:
                factors = subject["features"]
            
            if "confidence" in subject:
                confidence = subject["confidence"]
            elif "probability" in subject:
                confidence = subject["probability"]
            
            if "process" in subject:
                process = subject["process"]
            elif "steps" in subject:
                process = subject["steps"]
        
        if context:
            if not factors:
                for key in ["factors", "features"]:
                    if key in context:
                        factors = context[key]
                        break
            
            if confidence is None:
                for key in ["confidence", "probability"]:
                    if key in context:
                        confidence = context[key]
                        break
            
            if not process:
                for key in ["process", "steps"]:
                    if key in context:
                        process = context[key]
                        break
        
        # Generate explanation based on available information
        explanation_parts = []
        
        explanation_parts.append("This decision was made through an automated process that considers multiple factors in your data.")
        
        if factors:
            if isinstance(factors, list):
                factors_str = ", ".join(str(f) for f in factors[:5])  # Show up to 5 factors
                if len(factors) > 5:
                    factors_str += f", and {len(factors) - 5} others"
                explanation_parts.append(f"The factors considered include: {factors_str}")
            else:
                explanation_parts.append(f"The factors considered include: {factors}")
        
        if confidence is not None:
            try:
                conf_value = float(confidence)
                if conf_value > 0.9:
                    explanation_parts.append(f"The system is highly confident in this decision ({conf_value:.2f}).")
                elif conf_value > 0.7:
                    explanation_parts.append(f"The system is moderately confident in this decision ({conf_value:.2f}).")
                else:
                    explanation_parts.append(f"The system has limited confidence in this decision ({conf_value:.2f}).")
            except (ValueError, TypeError):
                explanation_parts.append(f"The confidence in this decision is: {confidence}")
        
        if process:
            if isinstance(process, list):
                explanation_parts.append("The process involved the following steps:")
                for i, step in enumerate(process[:3], 1):  # Show up to 3 steps
                    explanation_parts.append(f"{i}. {step}")
                if len(process) > 3:
                    explanation_parts.append(f"... and {len(process) - 3} additional steps")
            else:
                explanation_parts.append(f"The process involved: {process}")
        
        return " ".join(explanation_parts)
    
    def add_explanation_template(self, type_name: str, template: str) -> None:
        """
        Add a new explanation template.
        
        Args:
            type_name: Name of the explanation type
            template: Template string
        """
        with self._lock:
            self.explanation_templates[type_name] = template
            logger.info(f"Added explanation template for {type_name}")
    
    def set_model_interpretability(self, model_type: str, score: float) -> None:
        """
        Set interpretability score for a model type.
        
        Args:
            model_type: Name of the model type
            score: Interpretability score between 0 and 1
        """
        if score < 0 or score > 1:
            raise ValueError("Interpretability score must be between 0 and 1")
        
        with self._lock:
            self.model_interpretability[model_type] = score
            logger.info(f"Set interpretability score for {model_type} to {score}")
    
    def add_interpretability_strategy(self, model_type: str, strategy: Callable[[Any, Dict[str, Any]], str]) -> None:
        """
        Add a custom interpretability strategy for a model type.
        
        Args:
            model_type: Name of the model type
            strategy: Function that generates explanations for this model type
        """
        with self._lock:
            self.interpretability_strategies[model_type] = strategy
            logger.info(f"Added interpretability strategy for {model_type}")


class TransparencyManager:
    """
    Manages transparency-related information and disclosures.
    
    The TransparencyManager ensures that appropriate information about
    the system's operations, capabilities, and limitations is provided
    to users and stakeholders.
    """
    
    def __init__(self, 
                system_info: Dict[str, Any] = None,
                capability_levels: Dict[str, float] = None,
                limitation_disclosures: Dict[str, str] = None):
        """
        Initialize a transparency manager.
        
        Args:
            system_info: Dictionary of system information
            capability_levels: Dictionary mapping capabilities to competence levels
            limitation_disclosures: Dictionary mapping limitation types to disclosure texts
        """
        self.system_info = system_info or {}
        self.capability_levels = capability_levels or {}
        self.limitation_disclosures = limitation_disclosures or {}
        self.transparency_records = []
        self._lock = threading.RLock()  # For thread safety
    
    def get_system_info(self) -> Dict[str, Any]:
        """
        Get information about the system for transparency purposes.
        
        Returns:
            Dictionary of system information
        """
        with self._lock:
            return self.system_info.copy()
    
    def update_system_info(self, key: str, value: Any) -> None:
        """
        Update system information.
        
        Args:
            key: Information key
            value: Information value
        """
        with self._lock:
            self.system_info[key] = value
            
            # Record the update
            self.transparency_records.append({
                "timestamp": time.time(),
                "action": "system_info_update",
                "key": key,
                "previous_value": self.system_info.get(key, None),
                "new_value": value
            })
    
    def get_capability_level(self, capability: str) -> float:
        """
        Get the competence level for a specific capability.
        
        Args:
            capability: Name of the capability
            
        Returns:
            Competence level between 0 and 1, or 0 if not found
        """
        with self._lock:
            return self.capability_levels.get(capability, 0.0)
    
    def set_capability_level(self, capability: str, level: float) -> None:
        """
        Set the competence level for a specific capability.
        
        Args:
            capability: Name of the capability
            level: Competence level between 0 and 1
        """
        if level < 0 or level > 1:
            raise ValueError("Capability level must be between 0 and 1")
        
        with self._lock:
            previous_level = self.capability_levels.get(capability, 0.0)
            self.capability_levels[capability] = level
            
            # Record the update
            self.transparency_records.append({
                "timestamp": time.time(),
                "action": "capability_level_update",
                "capability": capability,
                "previous_level": previous_level,
                "new_level": level
            })
    
    def get_limitation_disclosure(self, limitation_type: str) -> str:
        """
        Get the disclosure text for a specific limitation.
        
        Args:
            limitation_type: Type of limitation
            
        Returns:
            Disclosure text, or empty string if not found
        """
        with self._lock:
            return self.limitation_disclosures.get(limitation_type, "")
    
    def set_limitation_disclosure(self, limitation_type: str, disclosure: str) -> None:
        """
        Set the disclosure text for a specific limitation.
        
        Args:
            limitation_type: Type of limitation
            disclosure: Disclosure text
        """
        with self._lock:
            previous_disclosure = self.limitation_disclosures.get(limitation_type, "")
            self.limitation_disclosures[limitation_type] = disclosure
            
            # Record the update
            self.transparency_records.append({
                "timestamp": time.time(),
                "action": "limitation_disclosure_update",
                "limitation_type": limitation_type,
                "previous_disclosure": previous_disclosure,
                "new_disclosure": disclosure
            })
    
    def record_transparency_action(self, action_type: str, details: Dict[str, Any]) -> None:
        """
        Record a transparency-related action.
        
        Args:
            action_type: Type of action
            details: Details of the action
        """
        with self._lock:
            record = {
                "timestamp": time.time(),
                "action": action_type,
                **details
            }
            self.transparency_records.append(record)
    
    def get_transparency_records(self, 
                                start_time: Optional[float] = None, 
                                end_time: Optional[float] = None, 
                                action_types: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """
        Get transparency records filtered by time and action type.
        
        Args:
            start_time: Optional start time for filtering records
            end_time: Optional end time for filtering records
            action_types: Optional list of action types to include
            
        Returns:
            List of transparency records
        """
        with self._lock:
            filtered_records = self.transparency_records.copy()
            
            # Filter by time
            if start_time is not None:
                filtered_records = [r for r in filtered_records if r["timestamp"] >= start_time]
            
            if end_time is not None:
                filtered_records = [r for r in filtered_records if r["timestamp"] <= end_time]
            
            # Filter by action type
            if action_types is not None:
                filtered_records = [r for r in filtered_records if r["action"] in action_types]
            
            return filtered_records
    
    def generate_transparency_report(self) -> Dict[str, Any]:
        """
        Generate a comprehensive transparency report.
        
        Returns:
            Dictionary containing transparency information
        """
        with self._lock:
            report = {
                "timestamp": time.time(),
                "system_info": self.get_system_info(),
                "capabilities": {k: self._describe_capability_level(v) for k, v in self.capability_levels.items()},
                "limitations": {k: v for k, v in self.limitation_disclosures.items()},
                "activity_summary": self._generate_activity_summary()
            }
            
            return report
    
    def _describe_capability_level(self, level: float) -> Dict[str, Any]:
        """
        Convert a capability level to a descriptive object.
        
        Args:
            level: Capability level between 0 and 1
            
        Returns:
            Dictionary with descriptive information
        """
        description = ""
        if level >= 0.9:
            description = "Advanced capability with high reliability"
        elif level >= 0.7:
            description = "Good capability with occasional limitations"
        elif level >= 0.5:
            description = "Moderate capability with significant limitations"
        elif level >= 0.3:
            description = "Limited capability that should be used with caution"
        else:
            description = "Minimal capability that is not recommended for use"
        
        return {
            "level": level,
            "description": description,
            "score": f"{level * 100:.1f}%"
        }
    
    def _generate_activity_summary(self) -> Dict[str, Any]:
        """
        Generate a summary of transparency-related activity.
        
        Returns:
            Dictionary with activity summary
        """
        summary = {
            "total_records": len(self.transparency_records),
            "action_types": {},
            "recent_updates": []
        }
        
        # Count action types
        for record in self.transparency_records:
            action = record["action"]
            summary["action_types"][action] = summary["action_types"].get(action, 0) + 1
        
        # Get recent updates
        if self.transparency_records:
            sorted_records = sorted(self.transparency_records, key=lambda r: r["timestamp"], reverse=True)
            summary["recent_updates"] = sorted_records[:10]  # Take 10 most recent
        
        return summary
    
    def disclose_limitations(self, context: Dict[str, Any] = None) -> List[str]:
        """
        Generate appropriate limitation disclosures for a given context.
        
        Args:
            context: Context information for determining relevant limitations
            
        Returns:
            List of limitation disclosure texts
        """
        with self._lock:
            relevant_limitations = []
            
            # If no context, return all limitations
            if not context:
                return list(self.limitation_disclosures.values())
            
            # Check for explicit capability requests
            if "requested_capability" in context:
                requested = context["requested_capability"]
                if requested in self.capability_levels:
                    level = self.capability_levels[requested]
                    
                    # If capability level is low, disclose limitation
                    if level < 0.5:
                        limitation_type = f"limited_{requested}"
                        if limitation_type in self.limitation_disclosures:
                            relevant_limitations.append(self.limitation_disclosures[limitation_type])
                        else:
                            # Generate generic limitation disclosure
                            relevant_limitations.append(
                                f"Please note that the system has limited capability in {requested} "
                                f"(current capability level: {level:.1f}). Results may not be optimal."
                            )
            
            # Check for domain-specific limitations
            if "domain" in context:
                domain = context["domain"]
                domain_limitation = f"{domain}_limitation"
                
                if domain_limitation in self.limitation_disclosures:
                    relevant_limitations.append(self.limitation_disclosures[domain_limitation])
            
            # Add general limitations if appropriate
            if "general_limitation" in self.limitation_disclosures and (
                not context.get("hide_general_limitations", False)
            ):
                relevant_limitations.append(self.limitation_disclosures["general_limitation"])
            
            # Record this disclosure
            self.record_transparency_action(
                "limitation_disclosure",
                {
                    "context": str(context),
                    "limitations_disclosed": len(relevant_limitations),
                    "disclosures": relevant_limitations
                }
            )
            
            return relevant_limitations
    
    def get_model_cards(self) -> Dict[str, Dict[str, Any]]:
        """
        Get model cards for transparency and documentation.
        
        Returns:
            Dictionary mapping model names to model card information
        """
        with self._lock:
            model_cards = {}
            
            # Extract model cards from system info
            if "models" in self.system_info and isinstance(self.system_info["models"], dict):
                for model_name, model_info in self.system_info["models"].items():
                    # Create a standardized model card
                    model_card = {
                        "name": model_name,
                        "version": model_info.get("version", "unknown"),
                        "description": model_info.get("description", "No description available"),
                        "capabilities": model_info.get("capabilities", []),
                        "limitations": model_info.get("limitations", []),
                        "performance_metrics": model_info.get("performance_metrics", {}),
                        "training_data": model_info.get("training_data", "Not specified"),
                        "ethical_considerations": model_info.get("ethical_considerations", []),
                        "recommended_uses": model_info.get("recommended_uses", []),
                        "not_recommended_uses": model_info.get("not_recommended_uses", [])
                    }
                    
                    model_cards[model_name] = model_card
            
            return model_cards
    
    def load_transparency_data(self, file_path: str) -> bool:
        """
        Load transparency data from a file.
        
        Args:
            file_path: Path to the data file
            
        Returns:
            True if loading was successful, False otherwise
        """
        try:
            if not os.path.exists(file_path):
                logger.error(f"Transparency data file not found: {file_path}")
                return False
            
            # Determine file type by extension
            if file_path.endswith('.json'):
                with open(file_path, 'r') as f:
                    data = json.load(f)
            elif file_path.endswith(('.yaml', '.yml')):
                with open(file_path, 'r') as f:
                    data = yaml.safe_load(f)
            else:
                logger.error(f"Unsupported file format: {file_path}")
                return False
            
            with self._lock:
                # Load system info
                if "system_info" in data and isinstance(data["system_info"], dict):
                    self.system_info = data["system_info"]
                
                # Load capability levels
                if "capability_levels" in data and isinstance(data["capability_levels"], dict):
                    self.capability_levels = {k: float(v) for k, v in data["capability_levels"].items()}
                
                # Load limitation disclosures
                if "limitation_disclosures" in data and isinstance(data["limitation_disclosures"], dict):
                    self.limitation_disclosures = data["limitation_disclosures"]
                
                # Load transparency records if available
                if "transparency_records" in data and isinstance(data["transparency_records"], list):
                    self.transparency_records = data["transparency_records"]
                
                logger.info(f"Successfully loaded transparency data from {file_path}")
                return True
                
        except Exception as e:
            logger.error(f"Error loading transparency data from {file_path}: {e}")
            return False
    
    def save_transparency_data(self, file_path: str) -> bool:
        """
        Save transparency data to a file.
        
        Args:
            file_path: Path to save the data to
            
        Returns:
            True if saving was successful, False otherwise
        """
        try:
            with self._lock:
                data = {
                    "system_info": self.system_info,
                    "capability_levels": self.capability_levels,
                    "limitation_disclosures": self.limitation_disclosures,
                    "transparency_records": self.transparency_records
                }
            
            # Write to file based on extension
            if file_path.endswith('.json'):
                with open(file_path, 'w') as f:
                    json.dump(data, f, indent=2)
            elif file_path.endswith(('.yaml', '.yml')):
                with open(file_path, 'w') as f:
                    yaml.dump(data, f)
            else:
                logger.error(f"Unsupported file format: {file_path}")
                return False
            
            logger.info(f"Successfully saved transparency data to {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving transparency data to {file_path}: {e}")
            return False


class EthicalFramework:
    """
    Top-level ethical framework for the ULTRA system.
    
    The EthicalFramework integrates ethical rules, bias detection,
    fairness metrics, and explainability to ensure the system operates
    in accordance with ethical principles and values.
    """
    
    def __init__(self, rules_path: Optional[str] = None):
        """
        Initialize the ethical framework.
        
        Args:
            rules_path: Optional path to load ethical rules from
        """
        # Initialize components
        self.ethical_rules = EthicalRules(rules_path)
        self.bias_detector = BiasDetector()
        self.fairness_metrics = FairnessMetrics()
        self.explainability_engine = ExplainabilityEngine()
        self.transparency_manager = TransparencyManager()
        
        # Initialize basic values if there are no rules yet
        if self.ethical_rules.count_rules() == 0:
            self._initialize_default_rules()
        
        self._lock = threading.RLock()  # For thread safety
    
    def _initialize_default_rules(self) -> None:
        """Initialize default ethical rules if none were loaded."""
        # Create and add core ethical rules
        
        # Non-maleficence (Do No Harm)
        non_maleficence_rule = NonMaleficenceRule(
            name="prevent_harm",
            description="Prevent actions that could cause harm to users or others",
            harm_categories=["physical_harm", "psychological_harm", "social_harm", "economic_harm"],
            max_acceptable_harm=0.2,
            metadata=EthicalRuleMetadata(
                principle=EthicalPrincipleType.NON_MALEFICENCE,
                values=[ValueAlignmentType.HUMAN_WELFARE, ValueAlignmentType.DIGNITY]
            )
        )
        self.ethical_rules.add_rule(non_maleficence_rule)
        
        # Fairness rule
        fairness_rule = FairnessRule(
            name="ensure_fairness",
            description="Ensure fair treatment across different groups",
            protected_attributes=["race", "gender", "age", "religion", "disability"],
            fairness_metric="demographic_parity",
            threshold=0.8,
            metadata=EthicalRuleMetadata(
                principle=EthicalPrincipleType.JUSTICE,
                values=[ValueAlignmentType.FAIRNESS, ValueAlignmentType.DIVERSITY]
            )
        )
        self.ethical_rules.add_rule(fairness_rule)
        
        # Beneficence rule
        beneficence_rule = BeneficenceRule(
            name="maximize_benefit",
            description="Maximize positive outcomes and benefits for users",
            benefit_dimensions=["physical_wellbeing", "mental_wellbeing", "knowledge", "capabilities"],
            min_benefit_score=0.6,
            metadata=EthicalRuleMetadata(
                principle=EthicalPrincipleType.BENEFICENCE,
                values=[ValueAlignmentType.HUMAN_WELFARE, ValueAlignmentType.PROGRESS]
            )
        )
        self.ethical_rules.add_rule(beneficence_rule)
        
        # Autonomy rule
        autonomy_rule = AutonomyRule(
            name="respect_autonomy",
            description="Respect user autonomy and promote informed decision-making",
            autonomy_dimensions=["informed_consent", "freedom_of_choice", "understanding"],
            min_autonomy_score=0.7,
            consent_required=True,
            metadata=EthicalRuleMetadata(
                principle=EthicalPrincipleType.AUTONOMY,
                values=[ValueAlignmentType.FREEDOM, ValueAlignmentType.DIGNITY]
            )
        )
        self.ethical_rules.add_rule(autonomy_rule)
        
        # Transparency rule
        transparency_rule = TransparencyRule(
            name="provide_transparency",
            description="Ensure transparency in system operations and decision-making",
            transparency_aspects=["explainability", "disclosure_of_limitations", "clarity_of_purpose"],
            min_transparency_score=0.6,
            explainability_required=True,
            metadata=EthicalRuleMetadata(
                principle=EthicalPrincipleType.TRANSPARENCY,
                values=[ValueAlignmentType.TRUST, ValueAlignmentType.ACCOUNTABILITY]
            )
        )
        self.ethical_rules.add_rule(transparency_rule)
        
        logger.info(f"Initialized {self.ethical_rules.count_rules()} default ethical rules")
    
    def evaluate_ethics(self, subject: Any, context: Dict[str, Any] = None) -> Tuple[bool, float, List[str]]:
        """
        Evaluate if a subject satisfies ethical requirements.
        
        Args:
            subject: The subject to evaluate (action, decision, etc.)
            context: Additional context information
            
        Returns:
            Tuple of (is_ethical, ethics_score, violated_rules)
        """
        with self._lock:
            # Get the ethical evaluation
            is_ethical, ethics_score, violated_rules = self.ethical_rules.evaluate_all_rules(subject, context)
            
            # Check for bias
            bias_detected, bias_score = self.bias_detector.detect_bias(subject, context)
            
            # If bias is detected, add it to the evaluation results
            if bias_detected:
                is_ethical = False
                ethics_score = (ethics_score * 0.7) + (1.0 - bias_score) * 0.3  # Weight bias in the overall score
                violated_rules.append("bias_detected")
            
            return is_ethical, ethics_score, violated_rules
    
    def get_ethical_analysis(self, subject: Any, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Get a comprehensive ethical analysis of the subject.
        
        Args:
            subject: The subject to analyze
            context: Additional context information
            
        Returns:
            Dictionary containing detailed ethical analysis
        """
        with self._lock:
            # Basic ethical evaluation
            is_ethical, ethics_score, violated_rules = self.evaluate_ethics(subject, context)
            
            analysis = {
                "timestamp": time.time(),
                "is_ethical": is_ethical,
                "ethics_score": ethics_score,
                "violated_rules": violated_rules
            }
            
            # Add bias detection
            bias_detected, bias_score = self.bias_detector.detect_bias(subject, context)
            analysis["bias"] = {
                "bias_detected": bias_detected,
                "bias_score": bias_score
            }
            
            # If we have data for fairness metrics, add them
            if isinstance(subject, dict) and "predictions" in subject and "protected_attributes" in subject:
                fairness_data = {}
                if "ground_truth" in subject:
                    fairness_data = subject
                else:
                    # Try to extract fairness data from context
                    fairness_data = {
                        "predictions": subject["predictions"],
                        "protected_attributes": subject["protected_attributes"]
                    }
                    if context and "ground_truth" in context:
                        fairness_data["ground_truth"] = context["ground_truth"]
                
                # Calculate fairness metrics
                fairness_metrics = {}
                for metric_name in ["demographic_parity", "disparate_impact"]:
                    try:
                        score, details = self.fairness_metrics.calculate_metric(metric_name, fairness_data)
                        fairness_metrics[metric_name] = {
                            "score": score,
                            "details": details
                        }
                    except Exception as e:
                        logger.error(f"Error calculating fairness metric {metric_name}: {e}")
                
                # Add fairness metrics if any were calculated
                if fairness_metrics:
                    analysis["fairness_metrics"] = fairness_metrics
            
            # Generate explanation
            try:
                explanation = self.explainability_engine.generate_explanation(subject, context)
                analysis["explanation"] = explanation
            except Exception as e:
                logger.error(f"Error generating explanation: {e}")
                analysis["explanation_error"] = str(e)
            
            # Add transparency disclosures if appropriate
            try:
                disclosures = self.transparency_manager.disclose_limitations(context)
                if disclosures:
                    analysis["transparency_disclosures"] = disclosures
            except Exception as e:
                logger.error(f"Error generating transparency disclosures: {e}")
                analysis["transparency_disclosure_error"] = str(e)
            return analysis
# Add ethical recommendations
            analysis["recommendations"] = self._generate_ethical_recommendations(
                is_ethical, ethics_score, violated_rules, bias_detected
            )
            
            return analysis
    
    def _generate_ethical_recommendations(self, is_ethical: bool, ethics_score: float, 
                                        violated_rules: List[str], bias_detected: bool) -> List[str]:
        """
        Generate recommendations to improve ethical compliance.
        
        Args:
            is_ethical: Whether the subject satisfies ethical requirements
            ethics_score: The ethical score
            violated_rules: List of violated rules
            bias_detected: Whether bias was detected
            
        Returns:
            List of recommendation strings
        """
        recommendations = []
        
        if not is_ethical:
            recommendations.append("Review and address the identified ethical concerns before proceeding.")
            
            # Add specific recommendations based on violated rules
            for rule in violated_rules:
                if "harm" in rule.lower() or rule.lower().startswith("prevent_"):
                    recommendations.append("Conduct a harm assessment to identify and mitigate potential negative impacts.")
                elif "fair" in rule.lower() or rule == "ensure_fairness":
                    recommendations.append("Review for fairness across different demographic groups and consider applying fairness constraints.")
                elif "benefit" in rule.lower() or rule == "maximize_benefit":
                    recommendations.append("Consider how to enhance positive outcomes and benefits for all stakeholders.")
                elif "autonom" in rule.lower() or rule == "respect_autonomy":
                    recommendations.append("Ensure informed consent and user control are properly implemented.")
                elif "transparen" in rule.lower() or rule == "provide_transparency":
                    recommendations.append("Improve explanations and disclosures to enhance transparency.")
        
        if bias_detected:
            recommendations.append("Address potential bias in the system by reviewing training data and algorithmic fairness.")
            recommendations.append("Consider applying bias mitigation techniques such as preprocessing, in-processing, or postprocessing methods.")
        
        # Add general recommendations based on ethics score
        if ethics_score < 0.5:
            recommendations.append("Consider a comprehensive ethical redesign of this component.")
        elif ethics_score < 0.7:
            recommendations.append("Implement additional ethical safeguards to improve overall ethical compliance.")
        elif ethics_score < 0.9 and not recommendations:
            recommendations.append("While acceptable, consider further ethical improvements for optimal performance.")
        
        # If all is well and no recommendations yet, add a positive note
        if is_ethical and not bias_detected and not recommendations:
            recommendations.append("Current ethical compliance is satisfactory. Continue monitoring for emergent ethical issues.")
        
        return recommendations
    
    def set_bias_detector(self, bias_detector: BiasDetector) -> None:
        """
        Set the bias detector component.
        
        Args:
            bias_detector: BiasDetector instance
        """
        with self._lock:
            self.bias_detector = bias_detector
            logger.info("Bias detector updated in ethical framework")
    
    def set_fairness_metrics(self, fairness_metrics: FairnessMetrics) -> None:
        """
        Set the fairness metrics component.
        
        Args:
            fairness_metrics: FairnessMetrics instance
        """
        with self._lock:
            self.fairness_metrics = fairness_metrics
            logger.info("Fairness metrics updated in ethical framework")
    
    def set_explainability_engine(self, explainability_engine: ExplainabilityEngine) -> None:
        """
        Set the explainability engine component.
        
        Args:
            explainability_engine: ExplainabilityEngine instance
        """
        with self._lock:
            self.explainability_engine = explainability_engine
            logger.info("Explainability engine updated in ethical framework")
    
    def set_transparency_manager(self, transparency_manager: TransparencyManager) -> None:
        """
        Set the transparency manager component.
        
        Args:
            transparency_manager: TransparencyManager instance
        """
        with self._lock:
            self.transparency_manager = transparency_manager
            logger.info("Transparency manager updated in ethical framework")
    
    def add_ethical_rule(self, rule: EthicalRule) -> bool:
        """
        Add an ethical rule to the framework.
        
        Args:
            rule: The ethical rule to add
            
        Returns:
            True if the rule was successfully added, False otherwise
        """
        return self.ethical_rules.add_rule(rule)
    
    def remove_ethical_rule(self, rule_name: str) -> bool:
        """
        Remove an ethical rule from the framework.
        
        Args:
            rule_name: The name of the rule to remove
            
        Returns:
            True if the rule was successfully removed, False otherwise
        """
        return self.ethical_rules.remove_rule(rule_name)
    
    def get_ethical_rule(self, rule_name: str) -> Optional[EthicalRule]:
        """
        Get an ethical rule by name.
        
        Args:
            rule_name: The name of the rule to get
            
        Returns:
            The ethical rule with the given name, or None if not found
        """
        return self.ethical_rules.get_rule(rule_name)
    
    def get_relevant_rules(self, subject: Any, context: Dict[str, Any] = None) -> List[EthicalRule]:
        """
        Get ethical rules that are relevant to the given subject and context.
        
        Args:
            subject: The subject being evaluated
            context: Additional context information
            
        Returns:
            List of relevant ethical rules
        """
        return self.ethical_rules.get_relevant_rules(subject, context)
    
    def load_ethical_rules(self, file_path: str) -> int:
        """
        Load ethical rules from a file.
        
        Args:
            file_path: Path to a JSON or YAML file containing rule definitions
            
        Returns:
            Number of rules successfully loaded
        """
        return self.ethical_rules.load_rules(file_path)
    
    def save_ethical_rules(self, file_path: str, rules: List[str] = None) -> bool:
        """
        Save ethical rules to a file.
        
        Args:
            file_path: Path to save the rules to
            rules: Optional list of rule names to save, or None for all
            
        Returns:
            True if the rules were successfully saved, False otherwise
        """
        return self.ethical_rules.save_rules(file_path, rules)
    
    def get_ethical_rule_stats(self) -> Dict[str, Any]:
        """
        Get statistics about ethical rules.
        
        Returns:
            Dictionary with rule statistics
        """
        return self.ethical_rules.get_rule_stats()
    
    def generate_explanation(self, subject: Any, context: Dict[str, Any] = None) -> str:
        """
        Generate an explanation for the subject.
        
        Args:
            subject: The subject to explain
            context: Additional context information
            
        Returns:
            Explanation string
        """
        return self.explainability_engine.generate_explanation(subject, context)
    
    def detect_bias(self, subject: Any, context: Dict[str, Any] = None) -> Tuple[bool, float]:
        """
        Detect bias in the subject.
        
        Args:
            subject: The subject to check for bias
            context: Additional context information
            
        Returns:
            Tuple of (bias_detected, bias_score)
        """
        return self.bias_detector.detect_bias(subject, context)
    
    def get_bias_report(self, subject: Any, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Generate a comprehensive bias report.
        
        Args:
            subject: The subject to analyze for bias
            context: Additional context information
            
        Returns:
            Dictionary containing detailed bias analysis
        """
        return self.bias_detector.get_bias_report(subject, context)
    
    def calculate_fairness_metric(self, metric_name: str, data: Dict[str, Any]) -> Tuple[float, Dict[str, Any]]:
        """
        Calculate a specific fairness metric.
        
        Args:
            metric_name: Name of the metric to calculate
            data: Dictionary containing model predictions, optional ground truth, and protected attributes
            
        Returns:
            Tuple of (fairness_score, details)
        """
        return self.fairness_metrics.calculate_metric(metric_name, data)
    
    def get_fairness_report(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate a comprehensive fairness report.
        
        Args:
            data: Dictionary containing model predictions, optional ground truth, and protected attributes
            
        Returns:
            Dictionary containing detailed fairness analysis
        """
        return self.fairness_metrics.get_fairness_report(data)
    
    def disclose_limitations(self, context: Dict[str, Any] = None) -> List[str]:
        """
        Generate appropriate limitation disclosures for a given context.
        
        Args:
            context: Context information for determining relevant limitations
            
        Returns:
            List of limitation disclosure texts
        """
        return self.transparency_manager.disclose_limitations(context)
    
    def get_transparency_report(self) -> Dict[str, Any]:
        """
        Generate a comprehensive transparency report.
        
        Returns:
            Dictionary containing transparency information
        """
        return self.transparency_manager.generate_transparency_report()
    
    def create_fairness_rule(self, name: str, description: str, protected_attributes: List[str],
                          fairness_metric: str = "demographic_parity", threshold: float = 0.8) -> FairnessRule:
        """
        Create a fairness rule with the specified parameters.
        
        Args:
            name: Unique identifier for the rule
            description: Human-readable description of the rule
            protected_attributes: List of protected attributes to check for fairness
            fairness_metric: Metric to use for fairness evaluation
            threshold: Minimum acceptable fairness score
            
        Returns:
            Created FairnessRule instance
        """
        rule = FairnessRule(
            name=name,
            description=description,
            protected_attributes=protected_attributes,
            fairness_metric=fairness_metric,
            threshold=threshold,
            metadata=EthicalRuleMetadata(
                principle=EthicalPrincipleType.JUSTICE,
                values=[ValueAlignmentType.FAIRNESS, ValueAlignmentType.DIVERSITY]
            )
        )
        
        self.add_ethical_rule(rule)
        return rule
    
    def create_non_maleficence_rule(self, name: str, description: str, harm_categories: List[str],
                                 max_acceptable_harm: float = 0.2) -> NonMaleficenceRule:
        """
        Create a non-maleficence rule with the specified parameters.
        
        Args:
            name: Unique identifier for the rule
            description: Human-readable description of the rule
            harm_categories: List of harm categories to check
            max_acceptable_harm: Maximum acceptable harm score
            
        Returns:
            Created NonMaleficenceRule instance
        """
        rule = NonMaleficenceRule(
            name=name,
            description=description,
            harm_categories=harm_categories,
            max_acceptable_harm=max_acceptable_harm,
            metadata=EthicalRuleMetadata(
                principle=EthicalPrincipleType.NON_MALEFICENCE,
                values=[ValueAlignmentType.HUMAN_WELFARE, ValueAlignmentType.DIGNITY]
            )
        )
        
        self.add_ethical_rule(rule)
        return rule
    
    def create_beneficence_rule(self, name: str, description: str, benefit_dimensions: List[str],
                             min_benefit_score: float = 0.6) -> BeneficenceRule:
        """
        Create a beneficence rule with the specified parameters.
        
        Args:
            name: Unique identifier for the rule
            description: Human-readable description of the rule
            benefit_dimensions: List of dimensions to evaluate for benefit
            min_benefit_score: Minimum acceptable benefit score
            
        Returns:
            Created BeneficenceRule instance
        """
        rule = BeneficenceRule(
            name=name,
            description=description,
            benefit_dimensions=benefit_dimensions,
            min_benefit_score=min_benefit_score,
            metadata=EthicalRuleMetadata(
                principle=EthicalPrincipleType.BENEFICENCE,
                values=[ValueAlignmentType.HUMAN_WELFARE, ValueAlignmentType.PROGRESS]
            )
        )
        
        self.add_ethical_rule(rule)
        return rule
    
    def create_autonomy_rule(self, name: str, description: str, autonomy_dimensions: List[str],
                          min_autonomy_score: float = 0.7, consent_required: bool = True) -> AutonomyRule:
        """
        Create an autonomy rule with the specified parameters.
        
        Args:
            name: Unique identifier for the rule
            description: Human-readable description of the rule
            autonomy_dimensions: List of dimensions to evaluate for autonomy
            min_autonomy_score: Minimum acceptable autonomy score
            consent_required: Whether explicit consent is required
            
        Returns:
            Created AutonomyRule instance
        """
        rule = AutonomyRule(
            name=name,
            description=description,
            autonomy_dimensions=autonomy_dimensions,
            min_autonomy_score=min_autonomy_score,
            consent_required=consent_required,
            metadata=EthicalRuleMetadata(
                principle=EthicalPrincipleType.AUTONOMY,
                values=[ValueAlignmentType.FREEDOM, ValueAlignmentType.DIGNITY]
            )
        )
        
        self.add_ethical_rule(rule)
        return rule
    
    def create_transparency_rule(self, name: str, description: str, transparency_aspects: List[str],
                              min_transparency_score: float = 0.6, explainability_required: bool = True) -> TransparencyRule:
        """
        Create a transparency rule with the specified parameters.
        
        Args:
            name: Unique identifier for the rule
            description: Human-readable description of the rule
            transparency_aspects: List of aspects to evaluate for transparency
            min_transparency_score: Minimum acceptable transparency score
            explainability_required: Whether explanations are required
            
        Returns:
            Created TransparencyRule instance
        """
        rule = TransparencyRule(
            name=name,
            description=description,
            transparency_aspects=transparency_aspects,
            min_transparency_score=min_transparency_score,
            explainability_required=explainability_required,
            metadata=EthicalRuleMetadata(
                principle=EthicalPrincipleType.TRANSPARENCY,
                values=[ValueAlignmentType.TRUST, ValueAlignmentType.ACCOUNTABILITY]
            )
        )
        
        self.add_ethical_rule(rule)
        return rule
    
    def save_ethical_framework_state(self, directory: str) -> bool:
        """
        Save the entire ethical framework state to a directory.
        
        Args:
            directory: Directory path to save the state to
            
        Returns:
            True if saving was successful, False otherwise
        """
        try:
            # Create directory if it doesn't exist
            os.makedirs(directory, exist_ok=True)
            
            # Save ethical rules
            rules_path = os.path.join(directory, "ethical_rules.json")
            self.save_ethical_rules(rules_path)
            
            # Save transparency data
            transparency_path = os.path.join(directory, "transparency_data.json")
            self.transparency_manager.save_transparency_data(transparency_path)
            
            # Save bias detector configuration
            bias_config = {
                "protected_attributes": self.bias_detector.protected_attributes,
                "bias_threshold": self.bias_detector.bias_threshold
            }
            bias_path = os.path.join(directory, "bias_detector_config.json")
            with open(bias_path, 'w') as f:
                json.dump(bias_config, f, indent=2)
            
            # Save explainability engine templates
            explanation_path = os.path.join(directory, "explanation_templates.json")
            with open(explanation_path, 'w') as f:
                json.dump({
                    "templates": self.explainability_engine.explanation_templates,
                    "model_interpretability": self.explainability_engine.model_interpretability
                }, f, indent=2)
            
            logger.info(f"Ethical framework state saved to {directory}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving ethical framework state: {e}")
            return False
    
    def load_ethical_framework_state(self, directory: str) -> bool:
        """
        Load the entire ethical framework state from a directory.
        
        Args:
            directory: Directory path to load the state from
            
        Returns:
            True if loading was successful, False otherwise
        """
        try:
            if not os.path.isdir(directory):
                logger.error(f"Directory not found: {directory}")
                return False
            
            # Load ethical rules
            rules_path = os.path.join(directory, "ethical_rules.json")
            if os.path.exists(rules_path):
                loaded_rules = self.load_ethical_rules(rules_path)
                logger.info(f"Loaded {loaded_rules} ethical rules")
            
            # Load transparency data
            transparency_path = os.path.join(directory, "transparency_data.json")
            if os.path.exists(transparency_path):
                self.transparency_manager.load_transparency_data(transparency_path)
            
            # Load bias detector configuration
            bias_path = os.path.join(directory, "bias_detector_config.json")
            if os.path.exists(bias_path):
                with open(bias_path, 'r') as f:
                    bias_config = json.load(f)
                
                if "protected_attributes" in bias_config:
                    self.bias_detector.protected_attributes = bias_config["protected_attributes"]
                
                if "bias_threshold" in bias_config:
                    self.bias_detector.set_bias_threshold(bias_config["bias_threshold"])
            
            # Load explainability engine templates
            explanation_path = os.path.join(directory, "explanation_templates.json")
            if os.path.exists(explanation_path):
                with open(explanation_path, 'r') as f:
                    explanation_data = json.load(f)
                
                if "templates" in explanation_data:
                    self.explainability_engine.explanation_templates = explanation_data["templates"]
                
                if "model_interpretability" in explanation_data:
                    self.explainability_engine.model_interpretability = explanation_data["model_interpretability"]
            
            logger.info(f"Ethical framework state loaded from {directory}")
            return True
            
        except Exception as e:
            logger.error(f"Error loading ethical framework state: {e}")
            return False


# Utility functions for working with the ethical framework

def create_ethical_framework(rules_path: Optional[str] = None) -> EthicalFramework:
    """
    Create an ethical framework with optional rules.
    
    Args:
        rules_path: Optional path to load ethical rules from
        
    Returns:
        EthicalFramework instance
    """
    return EthicalFramework(rules_path)

def evaluate_ethics(framework: EthicalFramework, subject: Any, context: Dict[str, Any] = None) -> Tuple[bool, float, List[str]]:
    """
    Evaluate if a subject satisfies ethical requirements.
    
    Args:
        framework: EthicalFramework instance
        subject: The subject to evaluate
        context: Additional context information
        
    Returns:
        Tuple of (is_ethical, ethics_score, violated_rules)
    """
    return framework.evaluate_ethics(subject, context)

def get_ethical_analysis(framework: EthicalFramework, subject: Any, context: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Get a comprehensive ethical analysis of the subject.
    
    Args:
        framework: EthicalFramework instance
        subject: The subject to analyze
        context: Additional context information
        
    Returns:
        Dictionary containing detailed ethical analysis
    """
    return framework.get_ethical_analysis(subject, context)

def detect_bias(framework: EthicalFramework, subject: Any, context: Dict[str, Any] = None) -> Tuple[bool, float]:
    """
    Detect bias in the subject.
    
    Args:
        framework: EthicalFramework instance
        subject: The subject to check for bias
        context: Additional context information
        
    Returns:
        Tuple of (bias_detected, bias_score)
    """
    return framework.detect_bias(subject, context)

def generate_explanation(framework: EthicalFramework, subject: Any, context: Dict[str, Any] = None) -> str:
    """
    Generate an explanation for the subject.
    
    Args:
        framework: EthicalFramework instance
        subject: The subject to explain
        context: Additional context information
        
    Returns:
        Explanation string
    """
    return framework.generate_explanation(subject, context)

def calculate_fairness(framework: EthicalFramework, metric_name: str, data: Dict[str, Any]) -> Tuple[float, Dict[str, Any]]:
    """
    Calculate a fairness metric.
    
    Args:
        framework: EthicalFramework instance
        metric_name: Name of the metric to calculate
        data: Dictionary containing model predictions, optional ground truth, and protected attributes
        
    Returns:
        Tuple of (fairness_score, details)
    """
    return framework.calculate_fairness_metric(metric_name, data)

def create_fairness_data(predictions: List[Any], protected_attributes: Dict[str, List[Any]],
                       ground_truth: Optional[List[Any]] = None) -> Dict[str, Any]:
    """
    Create a data dictionary for fairness calculations.
    
    Args:
        predictions: List of model predictions
        protected_attributes: Dictionary mapping attribute names to values
        ground_truth: Optional list of true labels
        
    Returns:
        Dictionary that can be used for fairness calculations
    """
    data = {
        "predictions": predictions,
        "protected_attributes": protected_attributes
    }
    
    if ground_truth is not None:
        data["ground_truth"] = ground_truth
    
    return data

def load_ethical_rule(file_path: str, rule_name: Optional[str] = None) -> Optional[EthicalRule]:
    """
    Load a single ethical rule from a file.
    
    Args:
        file_path: Path to a JSON or YAML file containing rule definitions
        rule_name: Optional name of the specific rule to load
        
    Returns:
        Loaded rule, or None if not found
    """
    rules = EthicalRules()
    loaded_count = rules.load_rules(file_path)
    
    if loaded_count == 0:
        return None
    
    if rule_name:
        return rules.get_rule(rule_name)
    else:
        all_rules = rules.get_all_rules()
        return all_rules[0] if all_rules else None                