#!/usr/bin/env python3
"""
ULTRA Safety Constraints Module: Implements the constraint management system for ULTRA.

This module defines the constraint infrastructure, constraint types, and constraint management
for the ULTRA system. Constraints represent safety bounds, ethical guidelines, resource limitations,
security policies, and privacy requirements that the system must adhere to during operation.

The constraint system is mathematically grounded in formal verification principles, using
a constraint satisfaction framework to ensure system operations remain within safe bounds.
"""

import os
import sys
import json
import yaml
import time
import logging
import inspect
import hashlib
import threading
import numpy as np
import warnings
import re
import traceback
from enum import Enum
from typing import Dict, List, Set, Tuple, Any, Callable, Optional, Union, Type, TypeVar, Generic
from dataclasses import dataclass, field, asdict
from concurrent.futures import Thread<PERSON>oolExecutor
from functools import wraps, lru_cache
from abc import ABC, abstractmethod
from datetime import datetime
import importlib.util

# Set up logging
logger = logging.getLogger("ultra.safety.constraints")
logger.setLevel(logging.INFO)
if not logger.handlers:
    handler = logging.StreamHandler(sys.stdout)
    handler.setFormatter(logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    ))
    logger.addHandler(handler)


# Define constraint levels as an Enum for type safety
class ConstraintLevel(str, Enum):
    """Defines the enforcement levels for constraints."""
    ADVISORY = "ADVISORY"        # Suggestions but no enforced restrictions
    WARNING = "WARNING"          # Triggers alerts but allows continuation
    RESTRICTIVE = "RESTRICTIVE"  # Enforces constraints but allows overrides
    CRITICAL = "CRITICAL"        # Enforces hard constraints with no overrides


# Type for constraint functions
ConstraintFunc = Callable[[Any, Dict[str, Any]], Tuple[bool, float]]
# Type variable for constraint classes
T = TypeVar('T', bound='BaseConstraint')


@dataclass
class ConstraintMetadata:
    """Metadata for a constraint, used for organization and filtering."""
    category: str
    tags: List[str] = field(default_factory=list)
    created_at: float = field(default_factory=time.time)
    modified_at: float = field(default_factory=time.time)
    author: str = field(default="system")
    version: str = field(default="1.0.0")
    source: str = field(default="internal")
    priority: int = field(default=0)  # Higher number means higher priority
    dependencies: List[str] = field(default_factory=list)  # Names of constraints this one depends on
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert metadata to a dictionary."""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ConstraintMetadata':
        """Create metadata from a dictionary."""
        # Filter out keys that aren't in the ConstraintMetadata class
        valid_keys = {f.name for f in fields(cls)}
        filtered_data = {k: v for k, v in data.items() if k in valid_keys}
        return cls(**filtered_data)


class BaseConstraint(ABC):
    """
    Abstract base class for all constraints in the ULTRA system.
    
    Constraints define boundaries of safe operation for the system. Each constraint
    has a level indicating how strictly it should be enforced, and a constraint
    function that evaluates whether the constraint is satisfied.
    """
    
    def __init__(self, 
                name: str, 
                description: str, 
                level: Union[ConstraintLevel, str],
                constraint_func: Optional[ConstraintFunc] = None,
                metadata: Optional[ConstraintMetadata] = None):
        """
        Initialize a constraint.
        
        Args:
            name: Unique identifier for the constraint
            description: Human-readable description of the constraint
            level: Enforcement level (ADVISORY, WARNING, RESTRICTIVE, CRITICAL)
            constraint_func: Function that evaluates if the constraint is satisfied
            metadata: Additional metadata for categorization and management
        """
        self.name = name
        self.description = description
        
        # Ensure level is a ConstraintLevel
        if isinstance(level, str):
            try:
                self.level = ConstraintLevel(level)
            except ValueError:
                logger.warning(f"Invalid constraint level '{level}', defaulting to ADVISORY")
                self.level = ConstraintLevel.ADVISORY
        else:
            self.level = level
            
        self.constraint_func = constraint_func
        self.metadata = metadata or ConstraintMetadata(category="general")
        self.last_check_time = 0.0
        self.last_check_result = (False, 0.0)
        self.check_count = 0
        self.violation_count = 0
        self._lock = threading.RLock()  # For thread safety
    
    def check(self, subject: Any, context: Dict[str, Any] = None) -> Tuple[bool, float]:
        """
        Check if the constraint is satisfied by the subject.
        
        Args:
            subject: The subject to check against the constraint
            context: Additional context information for the check
            
        Returns:
            Tuple of (is_satisfied, confidence_score)
            - is_satisfied: Boolean indicating if the constraint is satisfied
            - confidence_score: Float between 0 and 1 indicating confidence
        """
        with self._lock:
            start_time = time.time()
            
            try:
                # If no constraint function is set, use the evaluate method
                if self.constraint_func is None:
                    result = self.evaluate(subject, context or {})
                else:
                    result = self.constraint_func(subject, context or {})
                
                # Ensure result is properly formatted
                if isinstance(result, bool):
                    # If just a boolean is returned, assume high confidence if satisfied
                    is_satisfied = result
                    confidence = 1.0 if is_satisfied else 0.0
                elif isinstance(result, tuple) and len(result) == 2:
                    is_satisfied, confidence = result
                else:
                    logger.error(f"Constraint {self.name} returned invalid result format: {result}")
                    is_satisfied, confidence = False, 0.0
                
                # Update statistics
                self.check_count += 1
                if not is_satisfied:
                    self.violation_count += 1
                
                self.last_check_time = start_time
                self.last_check_result = (is_satisfied, confidence)
                
                return is_satisfied, confidence
                
            except Exception as e:
                logger.error(f"Error checking constraint {self.name}: {e}")
                logger.debug(traceback.format_exc())
                
                # Constraint evaluation errors default to violation with zero confidence
                self.check_count += 1
                self.violation_count += 1
                self.last_check_time = start_time
                self.last_check_result = (False, 0.0)
                
                return False, 0.0
    
    def check_action(self, action: Any, context: Dict[str, Any] = None) -> Tuple[bool, float]:
        """
        Check if an action satisfies the constraint.
        
        Args:
            action: The action to check against the constraint
            context: Additional context information for the check
            
        Returns:
            Tuple of (is_satisfied, confidence_score)
        """
        return self.check(action, context)
    
    def check_state(self, state: Any, context: Dict[str, Any] = None) -> Tuple[bool, float]:
        """
        Check if a state satisfies the constraint.
        
        Args:
            state: The state to check against the constraint
            context: Additional context information for the check
            
        Returns:
            Tuple of (is_satisfied, confidence_score)
        """
        return self.check(state, context)
    
    @abstractmethod
    def evaluate(self, subject: Any, context: Dict[str, Any]) -> Tuple[bool, float]:
        """
        Evaluate if the constraint is satisfied by the subject.
        
        This method should be implemented by subclasses to provide
        constraint-specific evaluation logic.
        
        Args:
            subject: The subject to check against the constraint
            context: Additional context information for the check
            
        Returns:
            Tuple of (is_satisfied, confidence_score)
        """
        pass
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the constraint's usage and violations.
        
        Returns:
            Dictionary with constraint statistics
        """
        with self._lock:
            return {
                "name": self.name,
                "level": self.level.value,
                "check_count": self.check_count,
                "violation_count": self.violation_count,
                "violation_rate": self.violation_count / max(1, self.check_count),
                "last_check_time": self.last_check_time,
                "last_check_result": self.last_check_result
            }
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the constraint to a dictionary for serialization.
        
        Returns:
            Dictionary representation of the constraint
        """
        return {
            "name": self.name,
            "description": self.description,
            "level": self.level.value,
            "type": self.__class__.__name__,
            "metadata": self.metadata.to_dict(),
            "stats": {
                "check_count": self.check_count,
                "violation_count": self.violation_count,
                "last_check_time": self.last_check_time
            }
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BaseConstraint':
        """
        Create a constraint from a dictionary.
        
        This method should be implemented by subclasses to handle
        constraint-specific deserialization.
        
        Args:
            data: Dictionary representation of a constraint
            
        Returns:
            Instantiated constraint object
        """
        raise NotImplementedError(f"{cls.__name__}.from_dict() must be implemented by subclasses")


class SafetyConstraint(BaseConstraint):
    """
    Constraint for general system safety.
    
    Safety constraints define boundaries for system behavior to prevent
    dangerous operations, states, or modifications. These are the most
    fundamental constraints in the system.
    """
    
    def __init__(self, 
                name: str, 
                description: str, 
                level: Union[ConstraintLevel, str],
                constraint_func: Optional[ConstraintFunc] = None,
                threshold: float = 0.5,
                safety_domain: str = "general",
                metadata: Optional[ConstraintMetadata] = None):
        """
        Initialize a safety constraint.
        
        Args:
            name: Unique identifier for the constraint
            description: Human-readable description of the constraint
            level: Enforcement level (ADVISORY, WARNING, RESTRICTIVE, CRITICAL)
            constraint_func: Function that evaluates if the constraint is satisfied
            threshold: Threshold value for the safety constraint
            safety_domain: Domain this safety constraint applies to
            metadata: Additional metadata for categorization and management
        """
        super().__init__(name, description, level, constraint_func, metadata)
        self.threshold = threshold
        self.safety_domain = safety_domain
        
        # Set default metadata category if not provided
        if metadata is None or metadata.category == "general":
            self.metadata.category = f"safety.{safety_domain}"
    
    def evaluate(self, subject: Any, context: Dict[str, Any]) -> Tuple[bool, float]:
        """
        Evaluate if the safety constraint is satisfied.
        
        If a constraint_func is provided in the constructor, it will be used.
        Otherwise, this implementation extracts safety metrics from the subject
        based on the safety domain and checks them against the threshold.
        
        Args:
            subject: The subject to check (can be state, action, etc.)
            context: Additional context information
            
        Returns:
            Tuple of (is_satisfied, confidence_score)
        """
        # If a constraint function was provided, use it
        if self.constraint_func is not None:
            return self.constraint_func(subject, context)
        
        # Extract safety metric based on domain
        safety_metric = self._extract_safety_metric(subject, context)
        
        # Check if the safety metric meets the threshold
        is_satisfied = safety_metric >= self.threshold
        
        # Calculate confidence based on distance from threshold
        if is_satisfied:
            # If satisfied, confidence increases as metric exceeds threshold
            confidence = min(1.0, (safety_metric - self.threshold) / (1.0 - self.threshold) + 0.5)
        else:
            # If not satisfied, confidence decreases as metric falls below threshold
            confidence = max(0.0, safety_metric / self.threshold * 0.5)
        
        return is_satisfied, confidence
    
    def _extract_safety_metric(self, subject: Any, context: Dict[str, Any]) -> float:
        """
        Extract the relevant safety metric from the subject based on the safety domain.
        
        Args:
            subject: The subject to extract metrics from
            context: Additional context information
            
        Returns:
            Float value representing the safety metric
        """
        # Check if the subject has the safety metric directly
        if isinstance(subject, dict):
            # Try domain-specific keys
            if self.safety_domain in subject:
                return float(subject[self.safety_domain])
            
            # Try general safety keys
            for key in ["safety", "safety_score", "safety_metric", self.name.lower()]:
                if key in subject:
                    return float(subject[key])
                
            # If safety domain has dots (e.g., "resource.memory"), try nested lookup
            if "." in self.safety_domain:
                path = self.safety_domain.split(".")
                current = subject
                try:
                    for part in path:
                        current = current[part]
                    return float(current)
                except (KeyError, TypeError):
                    pass
        
        # Check context for safety information
        if context is not None:
            # Try domain-specific context keys
            if self.safety_domain in context:
                return float(context[self.safety_domain])
            
            # Try general safety context keys
            for key in ["safety", "safety_score", "safety_metric", self.name.lower()]:
                if key in context:
                    return float(context[key])
        
        # If we can't extract a meaningful value, return a conservative estimate
        logger.debug(f"Could not extract safety metric for {self.name} from subject or context")
        return 0.0
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SafetyConstraint':
        """
        Create a safety constraint from a dictionary.
        
        Args:
            data: Dictionary representation of a safety constraint
            
        Returns:
            SafetyConstraint instance
        """
        # Extract required fields with defaults
        name = data.get("name", "unnamed_safety_constraint")
        description = data.get("description", "")
        level_str = data.get("level", "ADVISORY")
        threshold = data.get("threshold", 0.5)
        safety_domain = data.get("safety_domain", "general")
        
        # Create metadata if present
        metadata = None
        if "metadata" in data:
            metadata = ConstraintMetadata.from_dict(data["metadata"])
        
        # Create the constraint (without constraint_func for now)
        constraint = cls(
            name=name,
            description=description,
            level=level_str,
            threshold=threshold,
            safety_domain=safety_domain,
            metadata=metadata
        )
        
        # If stats are provided, update them
        if "stats" in data and isinstance(data["stats"], dict):
            stats = data["stats"]
            constraint.check_count = stats.get("check_count", 0)
            constraint.violation_count = stats.get("violation_count", 0)
            constraint.last_check_time = stats.get("last_check_time", 0.0)
        
        return constraint
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the safety constraint to a dictionary for serialization.
        
        Returns:
            Dictionary representation of the safety constraint
        """
        data = super().to_dict()
        data.update({
            "threshold": self.threshold,
            "safety_domain": self.safety_domain
        })
        return data


class EthicalConstraint(BaseConstraint):
    """
    Constraint for ethical considerations.
    
    Ethical constraints ensure the system's behavior aligns with
    ethical principles and values. These constraints guide decision-making
    to prevent harmful, biased, or unethical behaviors.
    """
    
    def __init__(self, 
                name: str, 
                description: str, 
                level: Union[ConstraintLevel, str],
                constraint_func: Optional[ConstraintFunc] = None,
                ethical_principle: str = "general",
                normative_weight: float = 1.0,
                value_alignment: List[str] = None,
                metadata: Optional[ConstraintMetadata] = None):
        """
        Initialize an ethical constraint.
        
        Args:
            name: Unique identifier for the constraint
            description: Human-readable description of the constraint
            level: Enforcement level (ADVISORY, WARNING, RESTRICTIVE, CRITICAL)
            constraint_func: Function that evaluates if the constraint is satisfied
            ethical_principle: The ethical principle this constraint upholds
            normative_weight: Weight of this constraint in ethical calculations
            value_alignment: List of human values this constraint aligns with
            metadata: Additional metadata for categorization and management
        """
        super().__init__(name, description, level, constraint_func, metadata)
        self.ethical_principle = ethical_principle
        self.normative_weight = normative_weight
        self.value_alignment = value_alignment or ["fairness", "transparency"]
        
        # Set default metadata category if not provided
        if metadata is None or metadata.category == "general":
            self.metadata.category = f"ethics.{ethical_principle}"
    
    def evaluate(self, subject: Any, context: Dict[str, Any]) -> Tuple[bool, float]:
        """
        Evaluate if the ethical constraint is satisfied.
        
        Args:
            subject: The subject to check (can be action, state, etc.)
            context: Additional context information
            
        Returns:
            Tuple of (is_satisfied, confidence_score)
        """
        # If a constraint function was provided, use it
        if self.constraint_func is not None:
            return self.constraint_func(subject, context)
        
        # Extract relevant ethical metrics from subject and context
        ethical_metrics = self._extract_ethical_metrics(subject, context)
        
        # Calculate weighted ethical score using the extracted metrics
        total_weight = sum(self.ETHICAL_WEIGHTS.get(metric, 0.5) for metric in ethical_metrics)
        if total_weight == 0:
            # No relevant metrics found, default to conservative values
            return False, 0.0
        
        ethical_score = sum(
            self.ETHICAL_WEIGHTS.get(metric, 0.5) * ethical_metrics[metric]
            for metric in ethical_metrics
        ) / total_weight
        
        # Determine if constraint is satisfied based on ethical score
        # Using a sigmoidal threshold to model ethical reasoning
        threshold = 0.7  # Default ethical threshold
        if "ethical_threshold" in context:
            threshold = float(context["ethical_threshold"])
        
        is_satisfied = ethical_score >= threshold
        
        # Calculate confidence
        if is_satisfied:
            confidence = min(1.0, 0.5 + (ethical_score - threshold) / (1.0 - threshold) * 0.5)
        else:
            confidence = max(0.0, (ethical_score / threshold) * 0.5)
        
        return is_satisfied, confidence
    
    # Weights for different ethical metrics
    ETHICAL_WEIGHTS = {
        "fairness": 1.0,
        "transparency": 0.8,
        "privacy": 0.9,
        "autonomy": 0.8,
        "beneficence": 0.7,
        "non_maleficence": 1.0,
        "justice": 0.9,
        "human_control": 0.8,
        "dignity": 0.9,
        "informed_consent": 0.8,
        "accountability": 0.7,
        "explainability": 0.6,
        "equity": 0.9,
        "sustainability": 0.6,
        "inclusivity": 0.7
    }
    
    def _extract_ethical_metrics(self, subject: Any, context: Dict[str, Any]) -> Dict[str, float]:
        """
        Extract ethical metrics from the subject and context.
        
        Args:
            subject: The subject to check
            context: Additional context information
            
        Returns:
            Dictionary of ethical metric names to values
        """
        metrics = {}
        
        # Check if subject has ethical metrics directly
        if isinstance(subject, dict):
            # Check for ethical scores section
            if "ethical_scores" in subject and isinstance(subject["ethical_scores"], dict):
                metrics.update(subject["ethical_scores"])
            
            # Check for individual ethical metrics
            for metric in self.ETHICAL_WEIGHTS:
                if metric in subject:
                    metrics[metric] = float(subject[metric])
        
        # Check context for ethical information
        if context is not None:
            # Check for ethical scores section
            if "ethical_scores" in context and isinstance(context["ethical_scores"], dict):
                metrics.update(context["ethical_scores"])
            
            # Check for individual ethical metrics
            for metric in self.ETHICAL_WEIGHTS:
                if metric in context:
                    metrics[metric] = float(context[metric])
        
        # If we have no metrics but ethical_principle is specified,
        # check if there's a matching entry
        if not metrics and self.ethical_principle != "general":
            principle_key = f"ethics.{self.ethical_principle}"
            
            if isinstance(subject, dict) and principle_key in subject:
                metrics[self.ethical_principle] = float(subject[principle_key])
            elif context is not None and principle_key in context:
                metrics[self.ethical_principle] = float(context[principle_key])
        
        # Ensure all metrics are in range [0, 1]
        for key in list(metrics.keys()):
            try:
                value = float(metrics[key])
                metrics[key] = max(0.0, min(1.0, value))
            except (TypeError, ValueError):
                # Remove non-numeric metrics
                del metrics[key]
        
        return metrics
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'EthicalConstraint':
        """
        Create an ethical constraint from a dictionary.
        
        Args:
            data: Dictionary representation of an ethical constraint
            
        Returns:
            EthicalConstraint instance
        """
        # Extract required fields with defaults
        name = data.get("name", "unnamed_ethical_constraint")
        description = data.get("description", "")
        level_str = data.get("level", "ADVISORY")
        ethical_principle = data.get("ethical_principle", "general")
        normative_weight = data.get("normative_weight", 1.0)
        value_alignment = data.get("value_alignment", ["fairness", "transparency"])
        
        # Create metadata if present
        metadata = None
        if "metadata" in data:
            metadata = ConstraintMetadata.from_dict(data["metadata"])
        
        # Create the constraint (without constraint_func for now)
        constraint = cls(
            name=name,
            description=description,
            level=level_str,
            ethical_principle=ethical_principle,
            normative_weight=normative_weight,
            value_alignment=value_alignment,
            metadata=metadata
        )
        
        # If stats are provided, update them
        if "stats" in data and isinstance(data["stats"], dict):
            stats = data["stats"]
            constraint.check_count = stats.get("check_count", 0)
            constraint.violation_count = stats.get("violation_count", 0)
            constraint.last_check_time = stats.get("last_check_time", 0.0)
        
        return constraint
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the ethical constraint to a dictionary for serialization.
        
        Returns:
            Dictionary representation of the ethical constraint
        """
        data = super().to_dict()
        data.update({
            "ethical_principle": self.ethical_principle,
            "normative_weight": self.normative_weight,
            "value_alignment": self.value_alignment
        })
        return data


class ResourceConstraint(BaseConstraint):
    """
    Constraint for system resource usage.
    
    Resource constraints limit the usage of computational resources such as
    memory, CPU, storage, network bandwidth, or other finite system resources.
    These constraints prevent resource exhaustion and ensure efficient operation.
    """
    
    def __init__(self, 
                name: str, 
                description: str, 
                level: Union[ConstraintLevel, str],
                constraint_func: Optional[ConstraintFunc] = None,
                resource_type: str = "general",
                threshold: float = 0.9,  # Default to 90% utilization limit
                threshold_unit: str = "relative",  # "relative" as percentage or "absolute" for direct values
                time_window: Optional[float] = None,  # Time window for resource averaging, if applicable
                degradation_function: Optional[Callable[[float], float]] = None,  # Function to model degraded performance
                metadata: Optional[ConstraintMetadata] = None):
        """
        Initialize a resource constraint.
        
        Args:
            name: Unique identifier for the constraint
            description: Human-readable description of the constraint
            level: Enforcement level (ADVISORY, WARNING, RESTRICTIVE, CRITICAL)
            constraint_func: Function that evaluates if the constraint is satisfied
            resource_type: Type of resource being constrained (memory, cpu, disk, etc.)
            threshold: Resource usage threshold for the constraint
            threshold_unit: Unit for threshold ("relative" for percentage, "absolute" for direct values)
            time_window: Optional time window for resource averaging
            degradation_function: Optional function that models performance degradation vs. resource usage
            metadata: Additional metadata for categorization and management
        """
        super().__init__(name, description, level, constraint_func, metadata)
        self.resource_type = resource_type
        self.threshold = threshold
        self.threshold_unit = threshold_unit
        self.time_window = time_window
        self.degradation_function = degradation_function
        
        # Resource usage history for time window calculations
        self.usage_history = []
        
        # Set default metadata category if not provided
        if metadata is None or metadata.category == "general":
            self.metadata.category = f"resource.{resource_type}"
    
    def evaluate(self, subject: Any, context: Dict[str, Any]) -> Tuple[bool, float]:
        """
        Evaluate if the resource constraint is satisfied.
        
        Args:
            subject: The subject to check (can be state, action, etc.)
            context: Additional context information
            
        Returns:
            Tuple of (is_satisfied, confidence_score)
        """
        # If a constraint function was provided, use it
        if self.constraint_func is not None:
            return self.constraint_func(subject, context)
        
        # Extract resource usage information
        current_usage = self._extract_resource_usage(subject, context)
        
        # Update usage history if time window is specified
        if self.time_window is not None:
            now = time.time()
            self.usage_history.append((now, current_usage))
            
            # Remove entries outside the time window
            self.usage_history = [(t, u) for t, u in self.usage_history 
                                 if now - t <= self.time_window]
            
            # Calculate average usage over the time window
            if self.usage_history:
                current_usage = sum(u for _, u in self.usage_history) / len(self.usage_history)
        
        # Check if resource usage is below threshold
        is_satisfied = current_usage <= self.threshold
        
        # Calculate confidence score based on how far from threshold
        if is_satisfied:
            # If usage is below threshold, confidence increases as usage decreases from threshold
            if self.threshold > 0:
                confidence = min(1.0, (self.threshold - current_usage) / self.threshold + 0.5)
            else:
                confidence = 1.0  # If threshold is 0, and we're satisfied, full confidence
        else:
            # If usage exceeds threshold, confidence decreases as usage increases
            if self.threshold > 0:
                excess = (current_usage - self.threshold) / self.threshold
                confidence = max(0.0, 0.5 - min(0.5, excess * 0.5))
            else:
                confidence = 0.0  # If threshold is 0, and we're not satisfied, zero confidence
        
        # Apply degradation function if provided
        if not is_satisfied and self.degradation_function is not None:
            # Calculate degraded confidence
            degraded_confidence = self.degradation_function(current_usage)
            confidence = min(confidence, degraded_confidence)
        
        return is_satisfied, confidence
    
    def _extract_resource_usage(self, subject: Any, context: Dict[str, Any]) -> float:
        """
        Extract the current resource usage from the subject and context.
        
        Args:
            subject: The subject to extract resource usage from
            context: Additional context information
            
        Returns:
            Float value representing the current resource usage
        """
        # First, determine the preferred key to look for
        usage_keys = [
            f"{self.resource_type}_usage",
            f"resource.{self.resource_type}",
            self.resource_type,
            "usage"
        ]
        
        # Check subject for resource usage information
        if isinstance(subject, dict):
            for key in usage_keys:
                if key in subject:
                    return float(subject[key])
        
        # Check context for resource usage information
        if context is not None:
            for key in usage_keys:
                if key in context:
                    return float(context[key])
            
            # Check for resource states in context
            if "resource_states" in context and isinstance(context["resource_states"], dict):
                resource_states = context["resource_states"]
                for key in usage_keys:
                    if key in resource_states:
                        return float(resource_states[key])
        
        # If we have a system resource monitor, try to get current usage directly
        if "system_monitor" in context:
            try:
                system_monitor = context["system_monitor"]
                if hasattr(system_monitor, "get_resource_usage"):
                    usage = system_monitor.get_resource_usage(self.resource_type)
                    if usage is not None:
                        return float(usage)
            except Exception as e:
                logger.debug(f"Error getting resource usage from system monitor: {e}")
        
        # If we can't extract a meaningful value, assume maximum usage for safety
        logger.debug(f"Could not extract resource usage for {self.resource_type}")
        return float('inf')
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ResourceConstraint':
        """
        Create a resource constraint from a dictionary.
        
        Args:
            data: Dictionary representation of a resource constraint
            
        Returns:
            ResourceConstraint instance
        """
        # Extract required fields with defaults
        name = data.get("name", "unnamed_resource_constraint")
        description = data.get("description", "")
        level_str = data.get("level", "ADVISORY")
        resource_type = data.get("resource_type", "general")
        threshold = data.get("threshold", 0.9)
        threshold_unit = data.get("threshold_unit", "relative")
        time_window = data.get("time_window")
        
        # Create metadata if present
        metadata = None
        if "metadata" in data:
            metadata = ConstraintMetadata.from_dict(data["metadata"])
        
        # Create the constraint (without constraint_func and degradation_function for now)
        constraint = cls(
            name=name,
            description=description,
            level=level_str,
            resource_type=resource_type,
            threshold=threshold,
            threshold_unit=threshold_unit,
            time_window=time_window,
            metadata=metadata
        )
        
        # If stats are provided, update them
        if "stats" in data and isinstance(data["stats"], dict):
            stats = data["stats"]
            constraint.check_count = stats.get("check_count", 0)
            constraint.violation_count = stats.get("violation_count", 0)
            constraint.last_check_time = stats.get("last_check_time", 0.0)
        
        return constraint
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the resource constraint to a dictionary for serialization.
        
        Returns:
            Dictionary representation of the resource constraint
        """
        data = super().to_dict()
        data.update({
            "resource_type": self.resource_type,
            "threshold": self.threshold,
            "threshold_unit": self.threshold_unit,
            "time_window": self.time_window
        })
        return data


class SecurityConstraint(BaseConstraint):
    """
    Constraint for system security.
    
    Security constraints enforce security policies, prevent unauthorized access,
    protect against attacks, and ensure secure coding practices. These constraints
    help maintain the confidentiality, integrity, and availability of the system.
    """
    
    def __init__(self, 
                name: str, 
                description: str, 
                level: Union[ConstraintLevel, str],
                constraint_func: Optional[ConstraintFunc] = None,
                security_domain: str = "general",
                risk_threshold: float = 0.3,
                mitigation_actions: List[str] = None,
                recovery_procedures: List[str] = None,
                metadata: Optional[ConstraintMetadata] = None):
        """
        Initialize a security constraint.
        
        Args:
            name: Unique identifier for the constraint
            description: Human-readable description of the constraint
            level: Enforcement level (ADVISORY, WARNING, RESTRICTIVE, CRITICAL)
            constraint_func: Function that evaluates if the constraint is satisfied
            security_domain: The security domain this constraint applies to
            risk_threshold: Maximum acceptable risk level
            mitigation_actions: List of actions to mitigate security risks
            recovery_procedures: List of procedures for recovery from security incidents
            metadata: Additional metadata for categorization and management
        """
        super().__init__(name, description, level, constraint_func, metadata)
        self.security_domain = security_domain
        self.risk_threshold = risk_threshold
        self.mitigation_actions = mitigation_actions or []
        self.recovery_procedures = recovery_procedures or []
        
        # Set default metadata category if not provided
        if metadata is None or metadata.category == "general":
            self.metadata.category = f"security.{security_domain}"
    
    def evaluate(self, subject: Any, context: Dict[str, Any]) -> Tuple[bool, float]:
        """
        Evaluate if the security constraint is satisfied.
        
        Args:
            subject: The subject to check (can be state, action, etc.)
            context: Additional context information
            
        Returns:
            Tuple of (is_satisfied, confidence_score)
        """
        # If a constraint function was provided, use it
        if self.constraint_func is not None:
            return self.constraint_func(subject, context)
        
        # Extract security risk information
        security_risk = self._extract_security_risk(subject, context)
        
        # Check if security risk is below threshold
        is_satisfied = security_risk <= self.risk_threshold
        
        # Calculate confidence score based on risk level
        if is_satisfied:
            # If risk is below threshold, confidence increases as risk decreases
            confidence = min(1.0, 0.5 + (self.risk_threshold - security_risk) / self.risk_threshold * 0.5)
        else:
            # If risk exceeds threshold, confidence decreases as risk increases
            excess_risk = (security_risk - self.risk_threshold) / (1.0 - self.risk_threshold)
            confidence = max(0.0, 0.5 - excess_risk * 0.5)
        
        return is_satisfied, confidence
    
    def _extract_security_risk(self, subject: Any, context: Dict[str, Any]) -> float:
        """
        Extract the security risk level from the subject and context.
        
        Args:
            subject: The subject to extract security risk from
            context: Additional context information
            
        Returns:
            Float value representing the security risk level
        """
        # Define keys to look for in subject and context
        risk_keys = [
            f"{self.security_domain}_risk",
            f"security.{self.security_domain}_risk",
            f"security_{self.security_domain}_risk",
            f"{self.security_domain}_security_risk",
            "security_risk"
        ]
        
        # Check subject for security risk information
        if isinstance(subject, dict):
            for key in risk_keys:
                if key in subject:
                    return float(subject[key])
            
            # Check for security risks section
            if "security_risks" in subject and isinstance(subject["security_risks"], dict):
                if self.security_domain in subject["security_risks"]:
                    return float(subject["security_risks"][self.security_domain])
                elif "general" in subject["security_risks"]:
                    return float(subject["security_risks"]["general"])
        
        # Check context for security risk information
        if context is not None:
            for key in risk_keys:
                if key in context:
                    return float(context[key])
            
            # Check for security risks section
            if "security_risks" in context and isinstance(context["security_risks"], dict):
                if self.security_domain in context["security_risks"]:
                    return float(context["security_risks"][self.security_domain])
                elif "general" in context["security_risks"]:
                    return float(context["security_risks"]["general"])
            
            # If we have authorization context, check for unauthorized action flags
            if "authorization" in context:
                auth = context["authorization"]
                if isinstance(auth, dict):
                    if auth.get("is_authorized", True) is False:
                        return 1.0  # Unauthorized actions have maximum risk
                    
                    risk_level = auth.get("risk_level")
                    if risk_level is not None:
                        return float(risk_level)
        
        # If we can't extract a meaningful value, assume a midpoint risk
        # (not zero, to be conservative, but not maximum either)
        logger.debug(f"Could not extract security risk for {self.security_domain}")
        return 0.5
    
    def get_mitigation_actions(self) -> List[str]:
        """
        Get the list of mitigation actions for this security constraint.
        
        Returns:
            List of action descriptions
        """
        return self.mitigation_actions.copy()
    
    def get_recovery_procedures(self) -> List[str]:
        """
        Get the list of recovery procedures for this security constraint.
        
        Returns:
            List of procedure descriptions
        """
        return self.recovery_procedures.copy()
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SecurityConstraint':
        """
        Create a security constraint from a dictionary.
        
        Args:
            data: Dictionary representation of a security constraint
            
        Returns:
            SecurityConstraint instance
        """
        # Extract required fields with defaults
        name = data.get("name", "unnamed_security_constraint")
        description = data.get("description", "")
        level_str = data.get("level", "ADVISORY")
        security_domain = data.get("security_domain", "general")
        risk_threshold = data.get("risk_threshold", 0.3)
        mitigation_actions = data.get("mitigation_actions", [])
        recovery_procedures = data.get("recovery_procedures", [])
        
        # Create metadata if present
        metadata = None
        if "metadata" in data:
            metadata = ConstraintMetadata.from_dict(data["metadata"])
        
        # Create the constraint (without constraint_func for now)
        constraint = cls(
            name=name,
            description=description,
            level=level_str,
            security_domain=security_domain,
            risk_threshold=risk_threshold,
            mitigation_actions=mitigation_actions,
            recovery_procedures=recovery_procedures,
            metadata=metadata
        )
        
        # If stats are provided, update them
        if "stats" in data and isinstance(data["stats"], dict):
            stats = data["stats"]
            constraint.check_count = stats.get("check_count", 0)
            constraint.violation_count = stats.get("violation_count", 0)
            constraint.last_check_time = stats.get("last_check_time", 0.0)
        
        return constraint
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the security constraint to a dictionary for serialization.
        
        Returns:
            Dictionary representation of the security constraint
        """
        data = super().to_dict()
        data.update({
            "security_domain": self.security_domain,
            "risk_threshold": self.risk_threshold,
            "mitigation_actions": self.mitigation_actions,
            "recovery_procedures": self.recovery_procedures
        })
        return data


class PrivacyConstraint(BaseConstraint):
    """
    Constraint for data privacy.
    
    Privacy constraints protect personal and sensitive data, ensure
    compliance with privacy regulations, and maintain user confidentiality.
    These constraints guide data collection, processing, storage, and sharing.
    """
    
    def __init__(self, 
                name: str, 
                description: str, 
                level: Union[ConstraintLevel, str],
                constraint_func: Optional[ConstraintFunc] = None,
                data_category: str = "general",
                privacy_risk_threshold: float = 0.2,
                data_minimization: bool = True,
                purpose_limitation: List[str] = None,
                retention_period: Optional[int] = None,  # in seconds, None for indefinite
                anonymization_required: bool = False,
                metadata: Optional[ConstraintMetadata] = None):
        """
        Initialize a privacy constraint.
        
        Args:
            name: Unique identifier for the constraint
            description: Human-readable description of the constraint
            level: Enforcement level (ADVISORY, WARNING, RESTRICTIVE, CRITICAL)
            constraint_func: Function that evaluates if the constraint is satisfied
            data_category: Category of data this constraint applies to
            privacy_risk_threshold: Maximum acceptable privacy risk level
            data_minimization: Whether data minimization is required
            purpose_limitation: List of allowed purposes for data use
            retention_period: Maximum retention period in seconds
            anonymization_required: Whether data anonymization is required
            metadata: Additional metadata for categorization and management
        """
        super().__init__(name, description, level, constraint_func, metadata)
        self.data_category = data_category
        self.privacy_risk_threshold = privacy_risk_threshold
        self.data_minimization = data_minimization
        self.purpose_limitation = purpose_limitation or ["necessary_processing"]
        self.retention_period = retention_period
        self.anonymization_required = anonymization_required
        
        # Set default metadata category if not provided
        if metadata is None or metadata.category == "general":
            self.metadata.category = f"privacy.{data_category}"
    
    def evaluate(self, subject: Any, context: Dict[str, Any]) -> Tuple[bool, float]:
        """
        Evaluate if the privacy constraint is satisfied.
        
        Args:
            subject: The subject to check (can be state, action, etc.)
            context: Additional context information
            
        Returns:
            Tuple of (is_satisfied, confidence_score)
        """
        # If a constraint function was provided, use it
        if self.constraint_func is not None:
            return self.constraint_func(subject, context)
        
        # Initialize variables to track multiple privacy requirements
        privacy_checks = []
        
        # Check privacy risk
        privacy_risk = self._extract_privacy_risk(subject, context)
        risk_check = privacy_risk <= self.privacy_risk_threshold
        privacy_checks.append((risk_check, 0.4))  # Weight risk check at 40%
        
        # Check purpose limitation
        if self.purpose_limitation:
            purpose_check = self._check_purpose_limitation(subject, context)
            privacy_checks.append((purpose_check, 0.3))  # Weight purpose check at 30%
        
        # Check data minimization
        if self.data_minimization:
            minimization_check = self._check_data_minimization(subject, context)
            privacy_checks.append((minimization_check, 0.1))  # Weight minimization check at 10%
        
        # Check retention period
        if self.retention_period is not None:
            retention_check = self._check_retention_period(subject, context)
            privacy_checks.append((retention_check, 0.1))  # Weight retention check at 10%
        
        # Check anonymization
        if self.anonymization_required:
            anonymization_check = self._check_anonymization(subject, context)
            privacy_checks.append((anonymization_check, 0.1))  # Weight anonymization check at 10%
        
        # If no checks were performed, default to not satisfied
        if not privacy_checks:
            return False, 0.0
        
        # Calculate weighted result of all checks
        total_weight = sum(weight for _, weight in privacy_checks)
        if total_weight == 0:
            return False, 0.0
        
        weighted_checks = sum(
            check * weight for check, weight in privacy_checks
        ) / total_weight
        
        # Overall satisfaction requires a high weighted score
        is_satisfied = weighted_checks > 0.8  # 80% of checks must pass
        
        # Confidence is based on how many checks passed and their weights
        confidence = weighted_checks
        
        return is_satisfied, confidence
    
    def _extract_privacy_risk(self, subject: Any, context: Dict[str, Any]) -> float:
        """
        Extract privacy risk level from subject and context.
        
        Args:
            subject: The subject to check
            context: Additional context information
            
        Returns:
            Float representing privacy risk (0.0 to 1.0)
        """
        # Define keys to check for privacy risk
        risk_keys = [
            f"{self.data_category}_privacy_risk",
            f"privacy.{self.data_category}_risk",
            f"privacy_{self.data_category}_risk",
            "privacy_risk"
        ]
        
        # Check subject for privacy risk information
        if isinstance(subject, dict):
            for key in risk_keys:
                if key in subject:
                    return float(subject[key])
            
            # Check for privacy risks section
            if "privacy_risks" in subject and isinstance(subject["privacy_risks"], dict):
                if self.data_category in subject["privacy_risks"]:
                    return float(subject["privacy_risks"][self.data_category])
                elif "general" in subject["privacy_risks"]:
                    return float(subject["privacy_risks"]["general"])
        
        # Check context for privacy risk information
        if context is not None:
            for key in risk_keys:
                if key in context:
                    return float(context[key])
            
            # Check for privacy risks section
            if "privacy_risks" in context and isinstance(context["privacy_risks"], dict):
                if self.data_category in context["privacy_risks"]:
                    return float(context["privacy_risks"][self.data_category])
                elif "general" in context["privacy_risks"]:
                    return float(context["privacy_risks"]["general"])
            
            # Check data protection fields in context
            if "data_protection" in context:
                data_protection = context["data_protection"]
                if isinstance(data_protection, dict):
                    # Check if our data category is mentioned
                    category_risk = data_protection.get(f"{self.data_category}_risk")
                    if category_risk is not None:
                        return float(category_risk)
                    
                    # Check general risk level
                    general_risk = data_protection.get("risk_level")
                    if general_risk is not None:
                        return float(general_risk)
        
        # If no privacy risk information is available, assume moderate risk
        logger.debug(f"Could not extract privacy risk for {self.data_category}")
        return 0.5
    
    def _check_purpose_limitation(self, subject: Any, context: Dict[str, Any]) -> bool:
        """
        Check if the data purpose complies with purpose limitations.
        
        Args:
            subject: The subject to check
            context: Additional context information
            
        Returns:
            Boolean indicating if purpose limitation is satisfied
        """
        # Extract the purpose from subject or context
        purpose = None
        
        # Try to find the purpose in subject
        if isinstance(subject, dict):
            if "purpose" in subject:
                purpose = subject["purpose"]
            elif "data_purpose" in subject:
                purpose = subject["data_purpose"]
            elif "processing_purpose" in subject:
                purpose = subject["processing_purpose"]
        
        # If not found, try context
        if purpose is None and context is not None:
            if "purpose" in context:
                purpose = context["purpose"]
            elif "data_purpose" in context:
                purpose = context["data_purpose"]
            elif "processing_purpose" in context:
                purpose = context["processing_purpose"]
            
            # Check data protection information
            if "data_protection" in context:
                data_protection = context["data_protection"]
                if isinstance(data_protection, dict) and "purpose" in data_protection:
                    purpose = data_protection["purpose"]
        
        # If purpose is a string, convert to list
        if isinstance(purpose, str):
            purpose = [purpose]
        
        # If purpose is a list, check if any allowed purpose is included
        if isinstance(purpose, list):
            return any(p in self.purpose_limitation for p in purpose)
        
        # If purpose information is not available, assume it doesn't comply
        return False
    
    def _check_data_minimization(self, subject: Any, context: Dict[str, Any]) -> bool:
        """
        Check if data minimization principles are followed.
        
        Args:
            subject: The subject to check
            context: Additional context information
            
        Returns:
            Boolean indicating if data minimization is satisfied
        """
        # Check for explicit data minimization flag
        minimization_flag = None
        
        # Check subject
        if isinstance(subject, dict):
            if "data_minimization" in subject:
                minimization_flag = subject["data_minimization"]
            elif "minimization" in subject:
                minimization_flag = subject["minimization"]
        
        # Check context
        if minimization_flag is None and context is not None:
            if "data_minimization" in context:
                minimization_flag = context["data_minimization"]
            elif "minimization" in context:
                minimization_flag = context["minimization"]
            
            # Check data protection information
            if "data_protection" in context:
                data_protection = context["data_protection"]
                if isinstance(data_protection, dict) and "minimization" in data_protection:
                    minimization_flag = data_protection["minimization"]
        
        # If we have an explicit flag, use it
        if minimization_flag is not None:
            if isinstance(minimization_flag, bool):
                return minimization_flag
            elif isinstance(minimization_flag, (int, float)):
                return minimization_flag > 0
            elif isinstance(minimization_flag, str):
                return minimization_flag.lower() in ("true", "yes", "1", "enabled")
        
        # If no minimization information is available, we can't determine compliance
        # Default to assuming compliance (to prevent false positives)
        return True
    
    def _check_retention_period(self, subject: Any, context: Dict[str, Any]) -> bool:
        """
        Check if data retention period is within limits.
        
        Args:
            subject: The subject to check
            context: Additional context information
            
        Returns:
            Boolean indicating if retention period is satisfied
        """
        # Extract retention period from subject or context
        actual_retention = None
        
        # Check subject
        if isinstance(subject, dict):
            if "retention_period" in subject:
                actual_retention = subject["retention_period"]
            elif "data_retention" in subject:
                actual_retention = subject["data_retention"]
        
        # Check context
        if actual_retention is None and context is not None:
            if "retention_period" in context:
                actual_retention = context["retention_period"]
            elif "data_retention" in context:
                actual_retention = context["data_retention"]
            
            # Check data protection information
            if "data_protection" in context:
                data_protection = context["data_protection"]
                if isinstance(data_protection, dict) and "retention_period" in data_protection:
                    actual_retention = data_protection["retention_period"]
        
        # If we have a retention period, check if it's within limits
        if actual_retention is not None:
            try:
                # Convert to seconds if it's a string
                if isinstance(actual_retention, str):
                    # Parse common formats like "30d", "2h", "10m", etc.
                    unit_multipliers = {
                        's': 1,
                        'm': 60,
                        'h': 3600,
                        'd': 86400,
                        'w': 604800,
                        'y': 31536000
                    }
                    
                    match = re.match(r'(\d+)([smhdwy])', actual_retention.lower())
                    if match:
                        value, unit = match.groups()
                        actual_retention = int(value) * unit_multipliers.get(unit, 1)
                    else:
                        # Try to parse as a simple number
                        actual_retention = float(actual_retention)
                
                # Now check if the retention period is within limits
                return actual_retention <= self.retention_period
            except (ValueError, TypeError):
                # If we can't parse the retention period, assume it doesn't comply
                return False
        
        # If no retention information is available, assume it complies
        # (since we can't determine non-compliance)
        return True
    
    def _check_anonymization(self, subject: Any, context: Dict[str, Any]) -> bool:
        """
        Check if required anonymization is performed.
        
        Args:
            subject: The subject to check
            context: Additional context information
            
        Returns:
            Boolean indicating if anonymization requirement is satisfied
        """
        # Extract anonymization information from subject or context
        anonymization_done = None
        
        # Check subject
        if isinstance(subject, dict):
            if "anonymized" in subject:
                anonymization_done = subject["anonymized"]
            elif "anonymization" in subject:
                anonymization_done = subject["anonymization"]
            elif "is_anonymized" in subject:
                anonymization_done = subject["is_anonymized"]
        
        # Check context
        if anonymization_done is None and context is not None:
            if "anonymized" in context:
                anonymization_done = context["anonymized"]
            elif "anonymization" in context:
                anonymization_done = context["anonymization"]
            elif "is_anonymized" in context:
                anonymization_done = context["is_anonymized"]
            
            # Check data protection information
            if "data_protection" in context:
                data_protection = context["data_protection"]
                if isinstance(data_protection, dict):
                    if "anonymized" in data_protection:
                        anonymization_done = data_protection["anonymized"]
                    elif "anonymization" in data_protection:
                        anonymization_done = data_protection["anonymization"]
                    elif "is_anonymized" in data_protection:
                        anonymization_done = data_protection["is_anonymized"]
        
        # If we have an explicit flag, use it
        if anonymization_done is not None:
            if isinstance(anonymization_done, bool):
                return anonymization_done
            elif isinstance(anonymization_done, (int, float)):
                return anonymization_done > 0
            elif isinstance(anonymization_done, str):
                return anonymization_done.lower() in ("true", "yes", "1", "enabled")
        
        # If no anonymization information is available, assume it doesn't comply
        return False
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PrivacyConstraint':
        """
        Create a privacy constraint from a dictionary.
        
        Args:
            data: Dictionary representation of a privacy constraint
            
        Returns:
            PrivacyConstraint instance
        """
        # Extract required fields with defaults
        name = data.get("name", "unnamed_privacy_constraint")
        description = data.get("description", "")
        level_str = data.get("level", "ADVISORY")
        data_category = data.get("data_category", "general")
        privacy_risk_threshold = data.get("privacy_risk_threshold", 0.2)
        data_minimization = data.get("data_minimization", True)
        purpose_limitation = data.get("purpose_limitation", ["necessary_processing"])
        retention_period = data.get("retention_period")
        anonymization_required = data.get("anonymization_required", False)
        
        # Create metadata if present
        metadata = None
        if "metadata" in data:
            metadata = ConstraintMetadata.from_dict(data["metadata"])
        
        # Create the constraint (without constraint_func for now)
        constraint = cls(
            name=name,
            description=description,
            level=level_str,
            data_category=data_category,
            privacy_risk_threshold=privacy_risk_threshold,
            data_minimization=data_minimization,
            purpose_limitation=purpose_limitation,
            retention_period=retention_period,
            anonymization_required=anonymization_required,
            metadata=metadata
        )
        
        # If stats are provided, update them
        if "stats" in data and isinstance(data["stats"], dict):
            stats = data["stats"]
            constraint.check_count = stats.get("check_count", 0)
            constraint.violation_count = stats.get("violation_count", 0)
            constraint.last_check_time = stats.get("last_check_time", 0.0)
        
        return constraint
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the privacy constraint to a dictionary for serialization.
        
        Returns:
            Dictionary representation of the privacy constraint
        """
        data = super().to_dict()
        data.update({
            "data_category": self.data_category,
            "privacy_risk_threshold": self.privacy_risk_threshold,
            "data_minimization": self.data_minimization,
            "purpose_limitation": self.purpose_limitation,
            "retention_period": self.retention_period,
            "anonymization_required": self.anonymization_required
        })
        return data


class ConstraintManager:
    """
    Manages a collection of constraints and provides access to them.
    
    The ConstraintManager is responsible for:
    - Storing and organizing constraints
    - Providing access to constraints by name, type, or other criteria
    - Loading constraints from configuration
    - Checking constraints against subjects (actions, states, etc.)
    - Tracking constraint statistics
    """
    
    def __init__(self, default_constraints_path: Optional[str] = None):
        """
        Initialize a constraint manager.
        
        Args:
            default_constraints_path: Optional path to load default constraints from
        """
        self.constraints = {}  # Dict of name -> constraint
        self.constraint_types = {
            "SafetyConstraint": SafetyConstraint,
            "EthicalConstraint": EthicalConstraint,
            "ResourceConstraint": ResourceConstraint,
            "SecurityConstraint": SecurityConstraint,
            "PrivacyConstraint": PrivacyConstraint
        }
        self._lock = threading.RLock()  # For thread safety
        
        # Load default constraints if path is provided
        if default_constraints_path and os.path.exists(default_constraints_path):
            self.load_constraints(default_constraints_path)
    
    def add_constraint(self, constraint: BaseConstraint) -> bool:
        """
        Add a constraint to the manager.
        
        Args:
            constraint: The constraint to add
            
        Returns:
            True if the constraint was successfully added, False otherwise
        """
        with self._lock:
            if constraint.name in self.constraints:
                logger.warning(f"Constraint with name '{constraint.name}' already exists, replacing")
            
            self.constraints[constraint.name] = constraint
            return True
    
    def remove_constraint(self, name: str) -> bool:
        """
        Remove a constraint from the manager.
        
        Args:
            name: The name of the constraint to remove
            
        Returns:
            True if the constraint was successfully removed, False otherwise
        """
        with self._lock:
            if name in self.constraints:
                del self.constraints[name]
                return True
            else:
                return False
    
    def get_constraint(self, name: str) -> Optional[BaseConstraint]:
        """
        Get a constraint by name.
        
        Args:
            name: The name of the constraint to get
            
        Returns:
            The constraint with the given name, or None if not found
        """
        with self._lock:
            return self.constraints.get(name)
    
    def get_all_constraints(self) -> List[BaseConstraint]:
        """
        Get all constraints managed by this manager.
        
        Returns:
            List of all constraints
        """
        with self._lock:
            return list(self.constraints.values())
    
    def count_constraints(self) -> int:
        """
        Get the number of constraints managed by this manager.
        
        Returns:
            The number of constraints
        """
        with self._lock:
            return len(self.constraints)
    
    def get_constraints_by_level(self, level: Union[ConstraintLevel, str]) -> List[BaseConstraint]:
        """
        Get constraints with the specified enforcement level.
        
        Args:
            level: The constraint level to filter by
            
        Returns:
            List of constraints with the specified level
        """
        # Convert string to ConstraintLevel enum if needed
        if isinstance(level, str):
            try:
                level = ConstraintLevel(level)
            except ValueError:
                logger.warning(f"Invalid constraint level '{level}'")
                return []
        
        with self._lock:
            return [c for c in self.constraints.values() if c.level == level]
    
    def get_constraints_by_type(self, constraint_type: str) -> List[BaseConstraint]:
        """
        Get constraints of the specified type.
        
        Args:
            constraint_type: The constraint type to filter by
            
        Returns:
            List of constraints of the specified type
        """
        with self._lock:
            return [c for c in self.constraints.values() if c.__class__.__name__ == constraint_type]
    
    def get_constraints_by_category(self, category: str) -> List[BaseConstraint]:
        """
        Get constraints in the specified category.
        
        Args:
            category: The metadata category to filter by
            
        Returns:
            List of constraints in the specified category
        """
        with self._lock:
            return [c for c in self.constraints.values() if c.metadata.category == category]
    
    def get_relevant_constraints(self, subject: Any, context: Dict[str, Any] = None) -> List[BaseConstraint]:
        """
        Get constraints that are relevant to the given subject and context.
        
        This method uses heuristics to determine which constraints
        are applicable to the current subject and context.
        
        Args:
            subject: The subject being evaluated
            context: Additional context information
            
        Returns:
            List of relevant constraints
        """
        with self._lock:
            # Start with all constraints
            all_constraints = list(self.constraints.values())
            
            # Filter constraints based on subject and context
            relevant_constraints = []
            
            # If subject is a dictionary, use its keys to determine relevant constraints
            if isinstance(subject, dict):
                # Extract potential relevant categories
                categories = set()
                
                # Add top-level keys as potential categories
                categories.update(subject.keys())
                
                # Add metadata categories if present
                if "metadata" in subject and isinstance(subject["metadata"], dict):
                    categories.update(subject["metadata"].keys())
                
                # Add context categories if present
                if context is not None:
                    categories.update(context.keys())
                    if "metadata" in context and isinstance(context["metadata"], dict):
                        categories.update(context["metadata"].keys())
                
                # Filter constraints by categories
                for constraint in all_constraints:
                    # Extract constraint category parts
                    category_parts = constraint.metadata.category.split(".")
                    
                    # Check if any category part is relevant
                    if any(part in categories for part in category_parts):
                        relevant_constraints.append(constraint)
                
                # If we have a specific action or resource type, include those constraints
                if "action" in subject:
                    action_type = subject["action"]
                    if isinstance(action_type, str):
                        for constraint in all_constraints:
                            # Include constraints with matching action type
                            if hasattr(constraint, "action_type") and constraint.action_type == action_type:
                                relevant_constraints.append(constraint)
                
                # Add subject-specific constraints
                if "type" in subject:
                    subject_type = subject["type"]
                    if isinstance(subject_type, str):
                        for constraint in all_constraints:
                            # Check for type-specific constraints
                            if (hasattr(constraint, "subject_type") and 
                                isinstance(constraint.subject_type, str) and 
                                constraint.subject_type == subject_type):
                                relevant_constraints.append(constraint)
            
            # If no relevant constraints were found, include critical constraints
            if not relevant_constraints:
                critical_constraints = self.get_constraints_by_level(ConstraintLevel.CRITICAL)
                relevant_constraints.extend(critical_constraints)
            
            # Remove duplicates while preserving order
            seen = set()
            relevant_constraints = [c for c in relevant_constraints 
                                   if not (c.name in seen or seen.add(c.name))]
            
            return relevant_constraints
    
    def check_all_constraints(self, subject: Any, context: Dict[str, Any] = None) -> Tuple[bool, float, List[str]]:
        """
        Check if a subject satisfies all constraints.
        
        Args:
            subject: The subject to check
            context: Additional context information
            
        Returns:
            Tuple of (all_satisfied, average_score, violated_constraints)
        """
        with self._lock:
            if not self.constraints:
                logger.warning("No constraints defined in ConstraintManager")
                return True, 1.0, []
            
            # Get relevant constraints
            relevant_constraints = self.get_relevant_constraints(subject, context)
            
            if not relevant_constraints:
                logger.debug("No relevant constraints found for subject")
                return True, 1.0, []
            
            # Check each constraint
            violated_constraints = []
            scores = []
            
            for constraint in relevant_constraints:
                try:
                    is_satisfied, score = constraint.check(subject, context)
                    scores.append(score)
                    
                    if not is_satisfied:
                        violated_constraints.append(constraint.name)
                        
                        # Critical constraints can immediately fail the check
                        if constraint.level == ConstraintLevel.CRITICAL:
                            return False, score, [constraint.name]
                except Exception as e:
                    logger.error(f"Error checking constraint {constraint.name}: {e}")
                    violated_constraints.append(f"{constraint.name} (error)")
            
            # Calculate overall results
            all_satisfied = len(violated_constraints) == 0
            average_score = sum(scores) / len(scores) if scores else 0.0
            
            return all_satisfied, average_score, violated_constraints
    
    def load_constraints(self, file_path: str) -> int:
        """
        Load constraints from a file.
        
        Args:
            file_path: Path to a JSON or YAML file containing constraint definitions
            
        Returns:
            Number of constraints successfully loaded
        """
        if not os.path.exists(file_path):
            logger.error(f"Constraint file not found: {file_path}")
            return 0
        
        try:
            # Determine file type by extension
            if file_path.endswith('.json'):
                with open(file_path, 'r') as f:
                    data = json.load(f)
            elif file_path.endswith(('.yaml', '.yml')):
                with open(file_path, 'r') as f:
                    data = yaml.safe_load(f)
            else:
                logger.error(f"Unsupported file format: {file_path}")
                return 0
            
            # Ensure we have a list of constraints
            if isinstance(data, dict):
                if "constraints" in data and isinstance(data["constraints"], list):
                    constraints_data = data["constraints"]
                else:
                    # Single constraint definition
                    constraints_data = [data]
            elif isinstance(data, list):
                constraints_data = data
            else:
                logger.error(f"Invalid constraint data format in {file_path}")
                return 0
            
            # Load each constraint
            loaded_count = 0
            for constraint_data in constraints_data:
                try:
                    # Check constraint type
                    constraint_type = constraint_data.get("type")
                    if not constraint_type or constraint_type not in self.constraint_types:
                        logger.warning(f"Unknown constraint type: {constraint_type}")
                        continue
                    
                    # Create constraint instance
                    constraint_class = self.constraint_types[constraint_type]
                    constraint = constraint_class.from_dict(constraint_data)
                    
                    # Add constraint to manager
                    if self.add_constraint(constraint):
                        loaded_count += 1
                except Exception as e:
                    logger.error(f"Error loading constraint: {e}")
            
            logger.info(f"Loaded {loaded_count} constraints from {file_path}")
            return loaded_count
            
        except Exception as e:
            logger.error(f"Error loading constraints from {file_path}: {e}")
            return 0
    
    def save_constraints(self, file_path: str, constraints: List[str] = None) -> bool:
        """
        Save constraints to a file.
        
        Args:
            file_path: Path to save the constraints to
            constraints: Optional list of constraint names to save, or None for all
            
        Returns:
            True if the constraints were successfully saved, False otherwise
        """
        try:
            # Determine which constraints to save
            with self._lock:
                if constraints is None:
                    # Save all constraints
                    constraints_to_save = list(self.constraints.values())
                else:
                    # Save only the specified constraints
                    constraints_to_save = [
                        self.constraints[name] for name in constraints
                        if name in self.constraints
                    ]
            
            if not constraints_to_save:
                logger.warning("No constraints to save")
                return False
            
            # Serialize constraints
            serialized_constraints = [constraint.to_dict() for constraint in constraints_to_save]
            
            # Write to file based on extension
            if file_path.endswith('.json'):
                with open(file_path, 'w') as f:
                    json.dump({"constraints": serialized_constraints}, f, indent=2)
            elif file_path.endswith(('.yaml', '.yml')):
                with open(file_path, 'w') as f:
                    yaml.dump({"constraints": serialized_constraints}, f)
            else:
                logger.error(f"Unsupported file format: {file_path}")
                return False
            
            logger.info(f"Saved {len(constraints_to_save)} constraints to {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving constraints to {file_path}: {e}")
            return False
    
    def get_constraint_stats(self) -> Dict[str, Any]:
        """
        Get statistics about all constraints.
        
        Returns:
            Dictionary with constraint statistics
        """
        with self._lock:
            stats = {
                "total_constraints": len(self.constraints),
                "constraints_by_level": {
                    level.value: len(self.get_constraints_by_level(level))
                    for level in ConstraintLevel
                },
                "constraints_by_type": {
                    constraint_type: len(self.get_constraints_by_type(constraint_type))
                    for constraint_type in self.constraint_types
                },
                "total_checks": sum(c.check_count for c in self.constraints.values()),
                "total_violations": sum(c.violation_count for c in self.constraints.values()),
                "constraints": [c.get_stats() for c in self.constraints.values()]
            }
            
            # Calculate violation rates
            total_checks = stats["total_checks"]
            if total_checks > 0:
                stats["overall_violation_rate"] = stats["total_violations"] / total_checks
            else:
                stats["overall_violation_rate"] = 0.0
            
            return stats
    
    def register_constraint_type(self, type_name: str, constraint_class: Type[BaseConstraint]) -> None:
        """
        Register a new constraint type.
        
        Args:
            type_name: Name of the constraint type
            constraint_class: Constraint class for this type
        """
        with self._lock:
            self.constraint_types[type_name] = constraint_class
            logger.info(f"Registered constraint type: {type_name}")
    
    def create_constraint(self, type_name: str, **kwargs) -> Optional[BaseConstraint]:
        """
        Create a constraint of the specified type with the given parameters.
        
        Args:
            type_name: Type of constraint to create
            **kwargs: Parameters to pass to the constraint constructor
            
        Returns:
            Created constraint, or None if the type is not recognized
        """
        with self._lock:
            if type_name not in self.constraint_types:
                logger.error(f"Unknown constraint type: {type_name}")
                return None
            
            constraint_class = self.constraint_types[type_name]
            try:
                constraint = constraint_class(**kwargs)
                return constraint
            except Exception as e:
                logger.error(f"Error creating constraint of type {type_name}: {e}")
                return None


# Utility functions for working with constraints

def create_safety_constraint(name, description, level="ADVISORY", threshold=0.5, safety_domain="general", **kwargs):
    """
    Create a safety constraint with the specified parameters.
    
    Args:
        name: Unique identifier for the constraint
        description: Human-readable description of the constraint
        level: Enforcement level (ADVISORY, WARNING, RESTRICTIVE, CRITICAL)
        threshold: Threshold value for the safety constraint
        safety_domain: Domain this safety constraint applies to
        **kwargs: Additional parameters for the constraint
        
    Returns:
        SafetyConstraint instance
    """
    return SafetyConstraint(
        name=name,
        description=description,
        level=level,
        threshold=threshold,
        safety_domain=safety_domain,
        **kwargs
    )

def create_resource_constraint(name, description, level="ADVISORY", resource_type="general", 
                             threshold=0.9, threshold_unit="relative", **kwargs):
    """
    Create a resource constraint with the specified parameters.
    
    Args:
        name: Unique identifier for the constraint
        description: Human-readable description of the constraint
        level: Enforcement level (ADVISORY, WARNING, RESTRICTIVE, CRITICAL)
        resource_type: Type of resource being constrained (memory, cpu, disk, etc.)
        threshold: Resource usage threshold for the constraint
        threshold_unit: Unit for threshold ("relative" for percentage, "absolute" for direct values)
        **kwargs: Additional parameters for the constraint
        
    Returns:
        ResourceConstraint instance
    """
    return ResourceConstraint(
        name=name,
        description=description,
        level=level,
        resource_type=resource_type,
        threshold=threshold,
        threshold_unit=threshold_unit,
        **kwargs
    )

def create_ethical_constraint(name, description, level="ADVISORY", ethical_principle="general", 
                            normative_weight=1.0, value_alignment=None, **kwargs):
    """
    Create an ethical constraint with the specified parameters.
    
    Args:
        name: Unique identifier for the constraint
        description: Human-readable description of the constraint
        level: Enforcement level (ADVISORY, WARNING, RESTRICTIVE, CRITICAL)
        ethical_principle: The ethical principle this constraint upholds
        normative_weight: Weight of this constraint in ethical calculations
        value_alignment: List of human values this constraint aligns with
        **kwargs: Additional parameters for the constraint
        
    Returns:
        EthicalConstraint instance
    """
    return EthicalConstraint(
        name=name,
        description=description,
        level=level,
        ethical_principle=ethical_principle,
        normative_weight=normative_weight,
        value_alignment=value_alignment,
        **kwargs
    )

def create_security_constraint(name, description, level="ADVISORY", security_domain="general", 
                             risk_threshold=0.3, **kwargs):
    """
    Create a security constraint with the specified parameters.
    
    Args:
        name: Unique identifier for the constraint
        description: Human-readable description of the constraint
        level: Enforcement level (ADVISORY, WARNING, RESTRICTIVE, CRITICAL)
        security_domain: The security domain this constraint applies to
        risk_threshold: Maximum acceptable risk level
        **kwargs: Additional parameters for the constraint
        
    Returns:
        SecurityConstraint instance
    """
    return SecurityConstraint(
        name=name,
        description=description,
        level=level,
        security_domain=security_domain,
        risk_threshold=risk_threshold,
        **kwargs
    )

def create_privacy_constraint(name, description, level="ADVISORY", data_category="general", 
                            privacy_risk_threshold=0.2, **kwargs):
    """
    Create a privacy constraint with the specified parameters.
    
    Args:
        name: Unique identifier for the constraint
        description: Human-readable description of the constraint
        level: Enforcement level (ADVISORY, WARNING, RESTRICTIVE, CRITICAL)
        data_category: Category of data this constraint applies to
        privacy_risk_threshold: Maximum acceptable privacy risk level
        **kwargs: Additional parameters for the constraint
        
    Returns:
        PrivacyConstraint instance
    """
    return PrivacyConstraint(
        name=name,
        description=description,
        level=level,
        data_category=data_category,
        privacy_risk_threshold=privacy_risk_threshold,
        **kwargs
    )

def load_constraint_from_file(file_path, constraint_name=None):
    """
    Load a single constraint from a file.
    
    Args:
        file_path: Path to a JSON or YAML file containing constraint definitions
        constraint_name: Optional name of the specific constraint to load
        
    Returns:
        Loaded constraint, or list of all constraints if name is None
    """
    manager = ConstraintManager()
    loaded_count = manager.load_constraints(file_path)
    
    if loaded_count == 0:
        return None
    
    if constraint_name:
        return manager.get_constraint(constraint_name)
    else:
        return manager.get_all_constraints()

def constraint_check(constraint, subject, context=None):
    """
    Check if a subject satisfies a constraint.
    
    Args:
        constraint: The constraint to check
        subject: The subject to check against the constraint
        context: Additional context information
        
    Returns:
        Tuple of (is_satisfied, confidence_score)
    """
    return constraint.check(subject, context)

def constraint_check_all(constraints, subject, context=None):
    """
    Check if a subject satisfies all constraints.
    
    Args:
        constraints: List of constraints to check
        subject: The subject to check against the constraints
        context: Additional context information
        
    Returns:
        Tuple of (all_satisfied, average_score, violated_constraints)
    """
    if not constraints:
        return True, 1.0, []
    
    violated_constraints = []
    scores = []
    
    for constraint in constraints:
        try:
            is_satisfied, score = constraint.check(subject, context)
            scores.append(score)
            
            if not is_satisfied:
                violated_constraints.append(constraint.name)
                
                # Critical constraints can immediately fail the check
                if constraint.level == ConstraintLevel.CRITICAL:
                    return False, score, [constraint.name]
        except Exception as e:
            logger.error(f"Error checking constraint {constraint.name}: {e}")
            violated_constraints.append(f"{constraint.name} (error)")
    
    # Calculate overall results
    all_satisfied = len(violated_constraints) == 0
    average_score = sum(scores) / len(scores) if scores else 0.0
    
    return all_satisfied, average_score, violated_constraints

# Default constraint manager instance
constraint_manager = ConstraintManager()