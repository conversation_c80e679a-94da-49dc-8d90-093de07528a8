#!/usr/bin/env python3
"""
ULTRA Safety Monitoring Module: Implements monitoring systems for ULTRA safety.

This module provides comprehensive monitoring capabilities for system resources,
behavior patterns, activities, and anomalies. It integrates various monitoring
components to provide a holistic view of system safety and health, enabling
proactive detection of potential issues and violations.

The monitoring system is mathematically grounded in statistical process control,
anomaly detection theory, and information theory, providing rigorous foundations
for safety monitoring and alerting.
"""

import os
import sys
import time
import json
import yaml
import uuid
import socket
import logging
import threading
import traceback
import numpy as np
import warnings
import queue
import re
import psutil
import collections
from enum import Enum
from typing import Dict, List, Set, Tuple, Any, Callable, Optional, Union, TypeVar, Generic, Deque
from dataclasses import dataclass, field, asdict, is_dataclass
from datetime import datetime, timedelta
from collections import defaultdict, deque
from concurrent.futures import ThreadPoolExecutor
from functools import wraps, lru_cache, partial
from abc import ABC, abstractmethod
import importlib.util
import signal
import hashlib
import math

# Set up logging
logger = logging.getLogger("ultra.safety.monitoring")
logger.setLevel(logging.INFO)
if not logger.handlers:
    handler = logging.StreamHandler(sys.stdout)
    handler.setFormatter(logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    ))
    logger.addHandler(handler)

# Check if we're running on a system with /proc filesystem
HAS_PROC_FILESYSTEM = os.path.exists('/proc')


# Define monitoring priority levels
class MonitorPriority(Enum):
    """Defines priority levels for monitors and alerts."""
    LOW = 0
    MEDIUM = 1
    HIGH = 2
    CRITICAL = 3


# Define monitoring alert severity levels
class AlertSeverity(Enum):
    """Defines severity levels for monitoring alerts."""
    INFO = 0
    WARNING = 1
    ERROR = 2
    CRITICAL = 3


@dataclass
class MonitoringAlert:
    """Represents an alert generated by the monitoring system."""
    id: str
    timestamp: float
    source: str
    severity: AlertSeverity
    title: str
    description: str
    data: Dict[str, Any] = field(default_factory=dict)
    tags: List[str] = field(default_factory=list)
    acknowledged: bool = False
    acknowledged_by: Optional[str] = None
    acknowledged_at: Optional[float] = None
    
    def __post_init__(self):
        """Validate and set defaults for the alert."""
        if not self.id:
            self.id = str(uuid.uuid4())
    
    def acknowledge(self, by: str = "system") -> None:
        """
        Acknowledge the alert.
        
        Args:
            by: Identifier of who acknowledged the alert
        """
        self.acknowledged = True
        self.acknowledged_by = by
        self.acknowledged_at = time.time()
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the alert to a dictionary.
        
        Returns:
            Dictionary representation of the alert
        """
        return {
            "id": self.id,
            "timestamp": self.timestamp,
            "source": self.source,
            "severity": self.severity.name,
            "title": self.title,
            "description": self.description,
            "data": self.data,
            "tags": self.tags,
            "acknowledged": self.acknowledged,
            "acknowledged_by": self.acknowledged_by,
            "acknowledged_at": self.acknowledged_at
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MonitoringAlert':
        """
        Create an alert from a dictionary.
        
        Args:
            data: Dictionary representation of an alert
            
        Returns:
            MonitoringAlert instance
        """
        # Convert severity string to enum
        severity_str = data.get("severity", "INFO")
        try:
            severity = AlertSeverity[severity_str]
        except (KeyError, TypeError):
            severity = AlertSeverity.INFO
            
        return cls(
            id=data.get("id", str(uuid.uuid4())),
            timestamp=data.get("timestamp", time.time()),
            source=data.get("source", "unknown"),
            severity=severity,
            title=data.get("title", "Untitled Alert"),
            description=data.get("description", ""),
            data=data.get("data", {}),
            tags=data.get("tags", []),
            acknowledged=data.get("acknowledged", False),
            acknowledged_by=data.get("acknowledged_by"),
            acknowledged_at=data.get("acknowledged_at")
        )


@dataclass
class MonitoringState:
    """Represents the state of a monitored component or metric."""
    id: str
    timestamp: float
    source: str
    name: str
    value: Any
    status: str = "normal"
    previous_value: Any = None
    change_threshold: float = 0.0
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Validate and set defaults for the state."""
        if not self.id:
            self.id = str(uuid.uuid4())
    
    def is_changed(self, new_value: Any, threshold: Optional[float] = None) -> bool:
        """
        Check if the value has changed beyond the threshold.
        
        Args:
            new_value: New value to compare with the current value
            threshold: Optional custom threshold to use instead of the default
            
        Returns:
            True if the value has changed beyond the threshold
        """
        if threshold is None:
            threshold = self.change_threshold
            
        # Handle different value types
        if isinstance(self.value, (int, float)) and isinstance(new_value, (int, float)):
            # For numeric values, check if the relative change exceeds the threshold
            if self.value == 0:
                # Avoid division by zero
                return abs(new_value) > threshold
            else:
                return abs((new_value - self.value) / self.value) > threshold
        else:
            # For non-numeric values, just check if they're different
            return new_value != self.value
    
    def update(self, new_value: Any) -> bool:
        """
        Update the state with a new value.
        
        Args:
            new_value: New value for the state
            
        Returns:
            True if the state value changed
        """
        changed = self.is_changed(new_value)
        
        if changed:
            self.previous_value = self.value
            self.value = new_value
            self.timestamp = time.time()
            
        return changed
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the state to a dictionary.
        
        Returns:
            Dictionary representation of the state
        """
        return {
            "id": self.id,
            "timestamp": self.timestamp,
            "source": self.source,
            "name": self.name,
            "value": self.value,
            "status": self.status,
            "previous_value": self.previous_value,
            "change_threshold": self.change_threshold,
            "tags": self.tags,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MonitoringState':
        """
        Create a state from a dictionary.
        
        Args:
            data: Dictionary representation of a state
            
        Returns:
            MonitoringState instance
        """
        return cls(
            id=data.get("id", str(uuid.uuid4())),
            timestamp=data.get("timestamp", time.time()),
            source=data.get("source", "unknown"),
            name=data.get("name", "unnamed"),
            value=data.get("value"),
            status=data.get("status", "normal"),
            previous_value=data.get("previous_value"),
            change_threshold=data.get("change_threshold", 0.0),
            tags=data.get("tags", []),
            metadata=data.get("metadata", {})
        )


class BaseMonitor(ABC):
    """
    Abstract base class for all monitoring components.
    
    BaseMonitor defines the common interface and functionality for all
    monitoring components in the system. It provides methods for starting,
    stopping, and configuring monitors, as well as processing and reporting
    monitoring results.
    """
    
    def __init__(self, 
                name: str, 
                check_interval: float = 60.0,
                priority: MonitorPriority = MonitorPriority.MEDIUM,
                enabled: bool = True,
                alert_handler: Optional[Callable[[MonitoringAlert], None]] = None):
        """
        Initialize a base monitor.
        
        Args:
            name: Unique name for this monitor
            check_interval: Interval between checks in seconds
            priority: Priority level for this monitor
            enabled: Whether this monitor is initially enabled
            alert_handler: Optional handler for alerts
        """
        self.name = name
        self.check_interval = check_interval
        self.priority = priority
        self.enabled = enabled
        self.alert_handler = alert_handler
        
        self.running = False
        self.thread = None
        self.last_check_time = 0.0
        self.last_error_time = 0.0
        self.consecutive_errors = 0
        self.check_count = 0
        self.error_count = 0
        self.alerts = []
        self.states = {}  # Dict of state name -> MonitoringState
        
        self._lock = threading.RLock()  # Thread safety
        self._stop_event = threading.Event()
    
    def start(self) -> bool:
        """
        Start the monitor.
        
        Returns:
            True if the monitor was successfully started
        """
        with self._lock:
            if self.running:
                logger.warning(f"Monitor {self.name} is already running")
                return False
                
            if not self.enabled:
                logger.warning(f"Monitor {self.name} is disabled, not starting")
                return False
                
            self._stop_event.clear()
            self.running = True
            
            # Start monitoring thread
            self.thread = threading.Thread(
                target=self._monitoring_loop,
                name=f"monitor-{self.name}",
                daemon=True
            )
            self.thread.start()
            
            logger.info(f"Started monitor: {self.name}")
            return True
    
    def stop(self) -> bool:
        """
        Stop the monitor.
        
        Returns:
            True if the monitor was successfully stopped
        """
        with self._lock:
            if not self.running:
                logger.warning(f"Monitor {self.name} is not running")
                return False
                
            self._stop_event.set()
            self.running = False
            
            if self.thread and self.thread.is_alive():
                self.thread.join(timeout=2.0)
                
            logger.info(f"Stopped monitor: {self.name}")
            return True
    
    def restart(self) -> bool:
        """
        Restart the monitor.
        
        Returns:
            True if the monitor was successfully restarted
        """
        self.stop()
        return self.start()
    
    def _monitoring_loop(self) -> None:
        """
        Main monitoring loop that periodically calls check().
        """
        logger.debug(f"Started monitoring loop for {self.name}")
        
        try:
            # Run initial check immediately
            self._run_check()
            
            # Main monitoring loop
            while not self._stop_event.is_set():
                # Wait for next check interval or until stopped
                if self._stop_event.wait(self.check_interval):
                    break
                
                # Run check if not stopped
                if not self._stop_event.is_set():
                    self._run_check()
        
        except Exception as e:
            logger.error(f"Error in monitoring loop for {self.name}: {e}")
            logger.debug(traceback.format_exc())
        
        logger.debug(f"Exited monitoring loop for {self.name}")
    
    def _run_check(self) -> None:
        """
        Run a single monitoring check, handling errors and updating metrics.
        """
        self.last_check_time = time.time()
        self.check_count += 1
        
        try:
            # Run the actual check
            self.check()
            
            # Reset consecutive errors on successful check
            self.consecutive_errors = 0
            
        except Exception as e:
            self.last_error_time = time.time()
            self.error_count += 1
            self.consecutive_errors += 1
            
            logger.error(f"Error in monitor {self.name}: {e}")
            logger.debug(traceback.format_exc())
            
            # Create an alert for the error
            self.create_alert(
                severity=AlertSeverity.ERROR,
                title=f"Monitor Error: {self.name}",
                description=f"Error during monitoring check: {str(e)}",
                data={"error": str(e), "traceback": traceback.format_exc()}
            )
    
    @abstractmethod
    def check(self) -> None:
        """
        Perform a monitoring check.
        
        This method should be implemented by subclasses to define the
        specific monitoring logic.
        """
        raise NotImplementedError(f"{self.__class__.__name__}.check() must be implemented by subclasses")
    
    def create_alert(self, 
                    severity: AlertSeverity, 
                    title: str, 
                    description: str, 
                    data: Dict[str, Any] = None,
                    tags: List[str] = None) -> MonitoringAlert:
        """
        Create and process a monitoring alert.
        
        Args:
            severity: Severity level of the alert
            title: Short title for the alert
            description: Detailed description of the alert
            data: Additional data related to the alert
            tags: Tags for categorizing the alert
            
        Returns:
            The created alert
        """
        alert = MonitoringAlert(
            id=str(uuid.uuid4()),
            timestamp=time.time(),
            source=self.name,
            severity=severity,
            title=title,
            description=description,
            data=data or {},
            tags=tags or []
        )
        
        # Add alert to our list
        with self._lock:
            self.alerts.append(alert)
            
            # Limit number of stored alerts
            if len(self.alerts) > 100:
                self.alerts = self.alerts[-100:]
        
        # Call alert handler if set
        if self.alert_handler:
            try:
                self.alert_handler(alert)
            except Exception as e:
                logger.error(f"Error in alert handler for {self.name}: {e}")
        
        return alert
    
    def update_state(self, 
                    name: str, 
                    value: Any, 
                    status: str = "normal",
                    change_threshold: float = 0.0,
                    tags: List[str] = None,
                    metadata: Dict[str, Any] = None) -> MonitoringState:
        """
        Update the state of a monitored value.
        
        Args:
            name: Name of the monitored value
            value: Current value
            status: Status of the value (normal, warning, error, etc.)
            change_threshold: Threshold for considering the value changed
            tags: Tags for categorizing the state
            metadata: Additional metadata for the state
            
        Returns:
            The updated state
        """
        with self._lock:
            # Get existing state or create new one
            if name in self.states:
                state = self.states[name]
                changed = state.update(value)
                state.status = status
                
                # Update optional fields if provided
                if tags is not None:
                    state.tags = tags
                if metadata is not None:
                    state.metadata = metadata
                    
                # Update threshold if provided
                if change_threshold != 0.0:
                    state.change_threshold = change_threshold
            else:
                # Create new state
                state = MonitoringState(
                    id=str(uuid.uuid4()),
                    timestamp=time.time(),
                    source=self.name,
                    name=name,
                    value=value,
                    status=status,
                    previous_value=None,
                    change_threshold=change_threshold,
                    tags=tags or [],
                    metadata=metadata or {}
                )
                self.states[name] = state
                changed = True
            
            return state
    
    def get_state(self, name: str) -> Optional[MonitoringState]:
        """
        Get the current state of a monitored value.
        
        Args:
            name: Name of the monitored value
            
        Returns:
            The current state, or None if not found
        """
        with self._lock:
            return self.states.get(name)
    
    def get_all_states(self) -> Dict[str, MonitoringState]:
        """
        Get all current monitoring states.
        
        Returns:
            Dictionary of state name -> MonitoringState
        """
        with self._lock:
            return self.states.copy()
    
    def get_recent_alerts(self, max_count: int = 10, min_severity: AlertSeverity = None) -> List[MonitoringAlert]:
        """
        Get recent alerts from this monitor.
        
        Args:
            max_count: Maximum number of alerts to return
            min_severity: Minimum severity level to include
            
        Returns:
            List of recent alerts
        """
        with self._lock:
            if min_severity is not None:
                filtered_alerts = [a for a in self.alerts if a.severity.value >= min_severity.value]
            else:
                filtered_alerts = self.alerts.copy()
                
            # Sort by timestamp (most recent first) and limit count
            return sorted(filtered_alerts, key=lambda a: a.timestamp, reverse=True)[:max_count]
    
    def get_status(self) -> Dict[str, Any]:
        """
        Get the current status of the monitor.
        
        Returns:
            Dictionary with monitoring status information
        """
        with self._lock:
            return {
                "name": self.name,
                "running": self.running,
                "enabled": self.enabled,
                "priority": self.priority.name,
                "check_interval": self.check_interval,
                "last_check_time": self.last_check_time,
                "last_error_time": self.last_error_time,
                "check_count": self.check_count,
                "error_count": self.error_count,
                "consecutive_errors": self.consecutive_errors,
                "error_rate": self.error_count / max(1, self.check_count),
                "state_count": len(self.states),
                "alert_count": len(self.alerts)
            }
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the monitor to a dictionary.
        
        Returns:
            Dictionary representation of the monitor
        """
        with self._lock:
            return {
                "name": self.name,
                "type": self.__class__.__name__,
                "check_interval": self.check_interval,
                "priority": self.priority.name,
                "enabled": self.enabled,
                "running": self.running,
                "status": self.get_status(),
                "states": {name: state.to_dict() for name, state in self.states.items()},
                "recent_alerts": [alert.to_dict() for alert in self.get_recent_alerts(5)]
            }


class ResourceMonitor(BaseMonitor):
    """
    Monitors system resource usage.
    
    ResourceMonitor tracks CPU, memory, disk, and network usage,
    providing alerts when resource usage exceeds thresholds.
    """
    
    def __init__(self, 
                name: str = "resource_monitor",
                check_interval: float = 10.0,
                priority: MonitorPriority = MonitorPriority.HIGH,
                enabled: bool = True,
                alert_handler: Optional[Callable[[MonitoringAlert], None]] = None,
                cpu_threshold: float = 0.9,
                memory_threshold: float = 0.9,
                disk_threshold: float = 0.9,
                network_threshold: float = 0.8,
                io_threshold: float = 0.8,
                gpu_monitoring: bool = False,
                history_size: int = 60):
        """
        Initialize a resource monitor.
        
        Args:
            name: Unique name for this monitor
            check_interval: Interval between checks in seconds
            priority: Priority level for this monitor
            enabled: Whether this monitor is initially enabled
            alert_handler: Optional handler for alerts
            cpu_threshold: Threshold for CPU usage alerts (0.0-1.0)
            memory_threshold: Threshold for memory usage alerts (0.0-1.0)
            disk_threshold: Threshold for disk usage alerts (0.0-1.0)
            network_threshold: Threshold for network usage alerts (0.0-1.0)
            io_threshold: Threshold for I/O usage alerts (0.0-1.0)
            gpu_monitoring: Whether to monitor GPU usage (if available)
            history_size: Number of historical data points to keep
        """
        super().__init__(name, check_interval, priority, enabled, alert_handler)
        
        self.cpu_threshold = cpu_threshold
        self.memory_threshold = memory_threshold
        self.disk_threshold = disk_threshold
        self.network_threshold = network_threshold
        self.io_threshold = io_threshold
        self.gpu_monitoring = gpu_monitoring
        self.history_size = history_size
        
        # Setup resource usage history
        self.cpu_history = deque(maxlen=history_size)
        self.memory_history = deque(maxlen=history_size)
        self.disk_history = deque(maxlen=history_size)
        self.network_history = deque(maxlen=history_size)
        self.io_history = deque(maxlen=history_size)
        self.gpu_history = deque(maxlen=history_size) if gpu_monitoring else None
        
        # Initial network counters
        self.last_net_io = psutil.net_io_counters()
        self.last_net_time = time.time()
        
        # Initial disk I/O counters
        self.last_disk_io = psutil.disk_io_counters() if hasattr(psutil, 'disk_io_counters') else None
        self.last_disk_time = time.time()
        
        # Initialize GPU monitoring if enabled
        self.gpu_available = False
        if gpu_monitoring:
            self._init_gpu_monitoring()
    
    def _init_gpu_monitoring(self) -> None:
        """
        Initialize GPU monitoring if available.
        """
        try:
            # Try to import necessary libraries for GPU monitoring
            # We'll try multiple libraries to support different GPU types
            
            # NVIDIA GPUs via pynvml
            try:
                import pynvml
                pynvml.nvmlInit()
                self.gpu_module = 'pynvml'
                self.gpu_available = True
                self.gpu_count = pynvml.nvmlDeviceGetCount()
                logger.info(f"Initialized NVIDIA GPU monitoring with {self.gpu_count} devices")
                return
            except (ImportError, Exception):
                pass
            
            # AMD GPUs via pyRocm
            try:
                from rocm_smi import rocm_smi
                rocm_smi.initializeRsmi()
                self.gpu_module = 'rocm_smi'
                self.gpu_available = True
                self.gpu_count = len(rocm_smi.listDevices())
                logger.info(f"Initialized AMD GPU monitoring with {self.gpu_count} devices")
                return
            except (ImportError, Exception):
                pass
            
            # Try simple GPU stats via gpustat
            try:
                import gpustat
                self.gpu_module = 'gpustat'
                self.gpu_available = True
                stats = gpustat.GPUStatCollection.new_query()
                self.gpu_count = len(stats)
                logger.info(f"Initialized GPU monitoring with gpustat for {self.gpu_count} devices")
                return
            except (ImportError, Exception):
                pass
            
            logger.warning("GPU monitoring requested but no GPU libraries available")
            self.gpu_available = False
            
        except Exception as e:
            logger.error(f"Error initializing GPU monitoring: {e}")
            self.gpu_available = False
    
    def check(self) -> None:
        """
        Check system resource usage.
        """
        # Collect CPU usage
        cpu_usage = psutil.cpu_percent(interval=None) / 100.0
        self.cpu_history.append((time.time(), cpu_usage))
        self.update_state("cpu_usage", cpu_usage, 
                         status=self._get_threshold_status(cpu_usage, self.cpu_threshold))
        
        # Check for CPU usage alert
        if cpu_usage > self.cpu_threshold:
            self.create_alert(
                severity=AlertSeverity.WARNING if cpu_usage < 0.95 else AlertSeverity.ERROR,
                title="High CPU Usage",
                description=f"CPU usage at {cpu_usage*100:.1f}%, exceeding threshold of {self.cpu_threshold*100:.1f}%",
                data={"cpu_usage": cpu_usage, "threshold": self.cpu_threshold},
                tags=["resource", "cpu"]
            )
        
        # Collect memory usage
        memory = psutil.virtual_memory()
        memory_usage = memory.percent / 100.0
        self.memory_history.append((time.time(), memory_usage))
        self.update_state("memory_usage", memory_usage,
                         status=self._get_threshold_status(memory_usage, self.memory_threshold))
        
        # Add detailed memory stats
        self.update_state("memory_total", memory.total)
        self.update_state("memory_available", memory.available)
        self.update_state("memory_used", memory.used)
        
        # Check for memory usage alert
        if memory_usage > self.memory_threshold:
            self.create_alert(
                severity=AlertSeverity.WARNING if memory_usage < 0.95 else AlertSeverity.ERROR,
                title="High Memory Usage",
                description=f"Memory usage at {memory_usage*100:.1f}%, exceeding threshold of {self.memory_threshold*100:.1f}%",
                data={"memory_usage": memory_usage, "threshold": self.memory_threshold},
                tags=["resource", "memory"]
            )
        
        # Collect disk usage for all mounted filesystems
        for partition in psutil.disk_partitions():
            try:
                usage = psutil.disk_usage(partition.mountpoint)
                disk_usage = usage.percent / 100.0
                
                # Skip non-standard filesystems
                if not partition.device.startswith(('/dev', 'C:', 'D:', 'E:')):
                    continue
                
                partition_name = os.path.basename(partition.mountpoint) or partition.mountpoint
                state_name = f"disk_usage_{partition_name}"
                
                self.disk_history.append((time.time(), disk_usage))
                self.update_state(state_name, disk_usage,
                                status=self._get_threshold_status(disk_usage, self.disk_threshold))
                
                # Check for disk usage alert
                if disk_usage > self.disk_threshold:
                    self.create_alert(
                        severity=AlertSeverity.WARNING if disk_usage < 0.98 else AlertSeverity.ERROR,
                        title="High Disk Usage",
                        description=f"Disk usage on {partition.mountpoint} at {disk_usage*100:.1f}%, exceeding threshold of {self.disk_threshold*100:.1f}%",
                        data={"disk_usage": disk_usage, "threshold": self.disk_threshold, "mountpoint": partition.mountpoint},
                        tags=["resource", "disk"]
                    )
            except (PermissionError, FileNotFoundError):
                # Skip filesystems we can't access
                pass
            except Exception as e:
                logger.debug(f"Error checking disk usage for {partition.mountpoint}: {e}")
        
        # Collect network usage
        current_net_io = psutil.net_io_counters()
        current_net_time = time.time()
        net_time_delta = current_net_time - self.last_net_time
        
        if net_time_delta > 0:
            # Calculate network bandwidth usage
            bytes_sent_per_sec = (current_net_io.bytes_sent - self.last_net_io.bytes_sent) / net_time_delta
            bytes_recv_per_sec = (current_net_io.bytes_recv - self.last_net_io.bytes_recv) / net_time_delta
            
            # Update network states
            self.update_state("network_bytes_sent", bytes_sent_per_sec)
            self.update_state("network_bytes_recv", bytes_recv_per_sec)
            self.update_state("network_packets_sent", 
                            (current_net_io.packets_sent - self.last_net_io.packets_sent) / net_time_delta)
            self.update_state("network_packets_recv", 
                            (current_net_io.packets_recv - self.last_net_io.packets_recv) / net_time_delta)
            self.update_state("network_errin", 
                            (current_net_io.errin - self.last_net_io.errin) / net_time_delta)
            self.update_state("network_errout", 
                            (current_net_io.errout - self.last_net_io.errout) / net_time_delta)
            
            # Estimate network usage as percentage of a reference capacity
            # This is approximate since we don't know the actual network capacity
            # We'll use 1 Gbps (125 MB/s) as a reference
            reference_capacity = 125 * 1024 * 1024  # 125 MB/s in bytes/s
            network_usage = max(bytes_sent_per_sec, bytes_recv_per_sec) / reference_capacity
            
            self.network_history.append((time.time(), network_usage))
            self.update_state("network_usage", network_usage,
                            status=self._get_threshold_status(network_usage, self.network_threshold))
            
            # Check for network usage alert
            if network_usage > self.network_threshold:
                self.create_alert(
                    severity=AlertSeverity.WARNING,
                    title="High Network Usage",
                    description=f"Network usage estimated at {network_usage*100:.1f}% of reference capacity",
                    data={
                        "network_usage": network_usage, 
                        "threshold": self.network_threshold,
                        "bytes_sent_per_sec": bytes_sent_per_sec,
                        "bytes_recv_per_sec": bytes_recv_per_sec
                    },
                    tags=["resource", "network"]
                )
        
        # Update network counters for next check
        self.last_net_io = current_net_io
        self.last_net_time = current_net_time
        
        # Collect disk I/O stats if available
        if hasattr(psutil, 'disk_io_counters') and self.last_disk_io is not None:
            try:
                current_disk_io = psutil.disk_io_counters()
                current_disk_time = time.time()
                disk_time_delta = current_disk_time - self.last_disk_time
                
                if disk_time_delta > 0:
                    # Calculate disk I/O rates
                    read_bytes_per_sec = (current_disk_io.read_bytes - self.last_disk_io.read_bytes) / disk_time_delta
                    write_bytes_per_sec = (current_disk_io.write_bytes - self.last_disk_io.write_bytes) / disk_time_delta
                    
                    # Update disk I/O states
                    self.update_state("disk_read_bytes", read_bytes_per_sec)
                    self.update_state("disk_write_bytes", write_bytes_per_sec)
                    self.update_state("disk_read_count", 
                                    (current_disk_io.read_count - self.last_disk_io.read_count) / disk_time_delta)
                    self.update_state("disk_write_count", 
                                    (current_disk_io.write_count - self.last_disk_io.write_count) / disk_time_delta)
                    
                    # Estimate disk I/O usage as percentage of a reference capacity
                    # Modern SSDs can do ~500 MB/s, so we'll use that as reference
                    reference_capacity = 500 * 1024 * 1024  # 500 MB/s in bytes/s
                    io_usage = (read_bytes_per_sec + write_bytes_per_sec) / reference_capacity
                    
                    self.io_history.append((time.time(), io_usage))
                    self.update_state("io_usage", io_usage,
                                    status=self._get_threshold_status(io_usage, self.io_threshold))
                    
                    # Check for I/O usage alert
                    if io_usage > self.io_threshold:
                        self.create_alert(
                            severity=AlertSeverity.WARNING,
                            title="High Disk I/O Usage",
                            description=f"Disk I/O usage estimated at {io_usage*100:.1f}% of reference capacity",
                            data={
                                "io_usage": io_usage, 
                                "threshold": self.io_threshold,
                                "read_bytes_per_sec": read_bytes_per_sec,
                                "write_bytes_per_sec": write_bytes_per_sec
                            },
                            tags=["resource", "io"]
                        )
                
                # Update disk I/O counters for next check
                self.last_disk_io = current_disk_io
                self.last_disk_time = current_disk_time
                
            except Exception as e:
                logger.debug(f"Error collecting disk I/O stats: {e}")
        
        # Collect GPU stats if enabled and available
        if self.gpu_monitoring and self.gpu_available:
            try:
                self._check_gpu_usage()
            except Exception as e:
                logger.error(f"Error checking GPU usage: {e}")
    
    def _check_gpu_usage(self) -> None:
        """
        Check GPU usage if available.
        """
        if not self.gpu_available:
            return
            
        try:
            if self.gpu_module == 'pynvml':
                import pynvml
                
                for i in range(self.gpu_count):
                    handle = pynvml.nvmlDeviceGetHandleByIndex(i)
                    
                    # Get GPU utilization
                    utilization = pynvml.nvmlDeviceGetUtilizationRates(handle)
                    gpu_usage = utilization.gpu / 100.0
                    
                    # Get memory utilization
                    memory_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
                    memory_usage = memory_info.used / memory_info.total
                    
                    # Update states
                    self.update_state(f"gpu_{i}_usage", gpu_usage,
                                    status=self._get_threshold_status(gpu_usage, self.cpu_threshold))
                    self.update_state(f"gpu_{i}_memory", memory_usage,
                                    status=self._get_threshold_status(memory_usage, self.memory_threshold))
                    
                    # Check for alerts
                    if gpu_usage > self.cpu_threshold:
                        self.create_alert(
                            severity=AlertSeverity.WARNING if gpu_usage < 0.95 else AlertSeverity.ERROR,
                            title=f"High GPU {i} Usage",
                            description=f"GPU {i} usage at {gpu_usage*100:.1f}%, exceeding threshold of {self.cpu_threshold*100:.1f}%",
                            data={"gpu_usage": gpu_usage, "threshold": self.cpu_threshold, "gpu_index": i},
                            tags=["resource", "gpu"]
                        )
                    
                    if memory_usage > self.memory_threshold:
                        self.create_alert(
                            severity=AlertSeverity.WARNING if memory_usage < 0.95 else AlertSeverity.ERROR,
                            title=f"High GPU {i} Memory Usage",
                            description=f"GPU {i} memory usage at {memory_usage*100:.1f}%, exceeding threshold of {self.memory_threshold*100:.1f}%",
                            data={"memory_usage": memory_usage, "threshold": self.memory_threshold, "gpu_index": i},
                            tags=["resource", "gpu", "memory"]
                        )
                    
                    # Aggregate GPU usage for history
                    if i == 0:  # Just use the first GPU for history
                        self.gpu_history.append((time.time(), gpu_usage))
                
            elif self.gpu_module == 'rocm_smi':
                from rocm_smi import rocm_smi
                
                for i, device_id in enumerate(rocm_smi.listDevices()):
                    # Get GPU utilization
                    usage_info = rocm_smi.getGpuUse(device_id)
                    gpu_usage = int(usage_info) / 100.0
                    
                    # Get memory utilization
                    mem_info = rocm_smi.getMemInfo(device_id)
                    memory_usage = mem_info['used'] / mem_info['total']
                    
                    # Update states
                    self.update_state(f"gpu_{i}_usage", gpu_usage,
                                    status=self._get_threshold_status(gpu_usage, self.cpu_threshold))
                    self.update_state(f"gpu_{i}_memory", memory_usage,
                                    status=self._get_threshold_status(memory_usage, self.memory_threshold))
                    
                    # Check for alerts
                    if gpu_usage > self.cpu_threshold:
                        self.create_alert(
                            severity=AlertSeverity.WARNING if gpu_usage < 0.95 else AlertSeverity.ERROR,
                            title=f"High GPU {i} Usage",
                            description=f"GPU {i} usage at {gpu_usage*100:.1f}%, exceeding threshold of {self.cpu_threshold*100:.1f}%",
                            data={"gpu_usage": gpu_usage, "threshold": self.cpu_threshold, "gpu_index": i},
                            tags=["resource", "gpu"]
                        )
                    
                    if memory_usage > self.memory_threshold:
                        self.create_alert(
                            severity=AlertSeverity.WARNING if memory_usage < 0.95 else AlertSeverity.ERROR,
                            title=f"High GPU {i} Memory Usage",
                            description=f"GPU {i} memory usage at {memory_usage*100:.1f}%, exceeding threshold of {self.memory_threshold*100:.1f}%",
                            data={"memory_usage": memory_usage, "threshold": self.memory_threshold, "gpu_index": i},
                            tags=["resource", "gpu", "memory"]
                        )
                    
                    # Aggregate GPU usage for history
                    if i == 0:  # Just use the first GPU for history
                        self.gpu_history.append((time.time(), gpu_usage))
                
            elif self.gpu_module == 'gpustat':
                import gpustat
                
                stats = gpustat.GPUStatCollection.new_query()
                for i, gpu in enumerate(stats):
                    # Get GPU utilization
                    gpu_usage = gpu.utilization / 100.0
                    
                    # Get memory utilization
                    memory_usage = gpu.memory_used / gpu.memory_total
                    
                    # Update states
                    self.update_state(f"gpu_{i}_usage", gpu_usage,
                                    status=self._get_threshold_status(gpu_usage, self.cpu_threshold))
                    self.update_state(f"gpu_{i}_memory", memory_usage,
                                    status=self._get_threshold_status(memory_usage, self.memory_threshold))
                    
                    # Check for alerts
                    if gpu_usage > self.cpu_threshold:
                        self.create_alert(
                            severity=AlertSeverity.WARNING if gpu_usage < 0.95 else AlertSeverity.ERROR,
                            title=f"High GPU {i} Usage",
                            description=f"GPU {i} usage at {gpu_usage*100:.1f}%, exceeding threshold of {self.cpu_threshold*100:.1f}%",
                            data={"gpu_usage": gpu_usage, "threshold": self.cpu_threshold, "gpu_index": i},
                            tags=["resource", "gpu"]
                        )
                    
                    if memory_usage > self.memory_threshold:
                        self.create_alert(
                            severity=AlertSeverity.WARNING if memory_usage < 0.95 else AlertSeverity.ERROR,
                            title=f"High GPU {i} Memory Usage",
                            description=f"GPU {i} memory usage at {memory_usage*100:.1f}%, exceeding threshold of {self.memory_threshold*100:.1f}%",
                            data={"memory_usage": memory_usage, "threshold": self.memory_threshold, "gpu_index": i},
                            tags=["resource", "gpu", "memory"]
                        )
                    
                    # Aggregate GPU usage for history
                    if i == 0:  # Just use the first GPU for history
                        self.gpu_history.append((time.time(), gpu_usage))
            
        except Exception as e:
            logger.error(f"Error checking GPU usage: {e}")
    
    def _get_threshold_status(self, value: float, threshold: float) -> str:
        """
        Determine status based on value compared to threshold.
        
        Args:
            value: Current value
            threshold: Threshold value
            
        Returns:
            Status string (normal, warning, error)
        """
        if value >= threshold * 0.95:
            return "error"
        elif value >= threshold * 0.8:
            return "warning"
        else:
            return "normal"
    
    def get_resource_usage(self, resource_type: str) -> Optional[float]:
        """
        Get the current usage for a specific resource.
        
        Args:
            resource_type: Type of resource (cpu, memory, disk, network, io, gpu)
            
        Returns:
            Current usage as a float between 0.0 and 1.0, or None if not available
        """
        if resource_type == "cpu":
            state = self.get_state("cpu_usage")
            return state.value if state else None
        
        elif resource_type == "memory":
            state = self.get_state("memory_usage")
            return state.value if state else None
        
        elif resource_type == "disk":
            # Return the highest disk usage across all monitored partitions
            disk_states = [state for name, state in self.states.items() if name.startswith("disk_usage_")]
            return max([state.value for state in disk_states]) if disk_states else None
        
        elif resource_type == "network":
            state = self.get_state("network_usage")
            return state.value if state else None
        
        elif resource_type == "io":
            state = self.get_state("io_usage")
            return state.value if state else None
        
        elif resource_type == "gpu":
            if not self.gpu_monitoring or not self.gpu_available:
                return None
                
            # Return the highest GPU usage across all GPUs
            gpu_states = [state for name, state in self.states.items() if name.startswith("gpu_") and name.endswith("_usage")]
            return max([state.value for state in gpu_states]) if gpu_states else None
            
        return None
    
    def get_resource_history(self) -> Dict[str, List[Tuple[float, float]]]:
        """
        Get historical resource usage data.
        
        Returns:
            Dictionary mapping resource type to list of (timestamp, usage) tuples
        """
        return {
            "cpu": list(self.cpu_history),
            "memory": list(self.memory_history),
            "disk": list(self.disk_history),
            "network": list(self.network_history),
            "io": list(self.io_history),
            "gpu": list(self.gpu_history) if self.gpu_history else []
        }
    
    def get_detailed_resource_stats(self) -> Dict[str, Any]:
        """
        Get detailed resource statistics.
        
        Returns:
            Dictionary with detailed resource statistics
        """
        stats = {}
        
        # Get all resource states
        with self._lock:
            states = self.states.copy()
        
        # CPU stats
        cpu_states = {k: v for k, v in states.items() if k.startswith("cpu_")}
        stats["cpu"] = {k.replace("cpu_", ""): v.value for k, v in cpu_states.items()}
        
        # Memory stats
        memory_states = {k: v for k, v in states.items() if k.startswith("memory_")}
        stats["memory"] = {k.replace("memory_", ""): v.value for k, v in memory_states.items()}
        
        # Disk stats
        disk_usage_states = {k: v for k, v in states.items() if k.startswith("disk_usage_")}
        stats["disk_usage"] = {k.replace("disk_usage_", ""): v.value for k, v in disk_usage_states.items()}
        
        disk_io_states = {k: v for k, v in states.items() if k in ["disk_read_bytes", "disk_write_bytes", 
                                                                  "disk_read_count", "disk_write_count"]}
        stats["disk_io"] = {k.replace("disk_", ""): v.value for k, v in disk_io_states.items()}
        
        # Network stats
        network_states = {k: v for k, v in states.items() if k.startswith("network_")}
        stats["network"] = {k.replace("network_", ""): v.value for k, v in network_states.items()}
        
        # GPU stats
        if self.gpu_monitoring and self.gpu_available:
            gpu_states = {k: v for k, v in states.items() if k.startswith("gpu_")}
            stats["gpu"] = {}
            
            for gpu_idx in range(self.gpu_count):
                gpu_usage_key = f"gpu_{gpu_idx}_usage"
                gpu_memory_key = f"gpu_{gpu_idx}_memory"
                
                if gpu_usage_key in gpu_states and gpu_memory_key in gpu_states:
                    stats["gpu"][str(gpu_idx)] = {
                        "usage": gpu_states[gpu_usage_key].value,
                        "memory": gpu_states[gpu_memory_key].value
                    }
        
        return stats


class BehaviorMonitor(BaseMonitor):
    """
    Monitors system behavior patterns.
    
    BehaviorMonitor tracks system behavior patterns and detects
    deviations from expected behavior, such as unusual API call
    patterns, abnormal execution flows, or unexpected state changes.
    """
    
    def __init__(self, 
                name: str = "behavior_monitor",
                check_interval: float = 30.0,
                priority: MonitorPriority = MonitorPriority.MEDIUM,
                enabled: bool = True,
                alert_handler: Optional[Callable[[MonitoringAlert], None]] = None,
                expected_behavior_model: Optional[str] = None,
                observation_window: int = 100,
                anomaly_threshold: float = 3.0,
                learn_normal_behavior: bool = True,
                tracking_categories: List[str] = None):
        """
        Initialize a behavior monitor.
        
        Args:
            name: Unique name for this monitor
            check_interval: Interval between checks in seconds
            priority: Priority level for this monitor
            enabled: Whether this monitor is initially enabled
            alert_handler: Optional handler for alerts
            expected_behavior_model: Path to a saved behavior model
            observation_window: Number of observations to keep in window
            anomaly_threshold: Threshold for anomaly detection (standard deviations)
            learn_normal_behavior: Whether to learn normal behavior patterns
            tracking_categories: Categories of behavior to track
        """
        super().__init__(name, check_interval, priority, enabled, alert_handler)
        
        self.expected_behavior_model = expected_behavior_model
        self.observation_window = observation_window
        self.anomaly_threshold = anomaly_threshold
        self.learn_normal_behavior = learn_normal_behavior
        self.tracking_categories = tracking_categories or [
            "api_calls", "function_calls", "state_transitions", 
            "resource_usage", "error_rates", "timing_patterns"
        ]
        
        # Behavioral observations (category -> list of observations)
        self.observations = {category: deque(maxlen=observation_window) for category in self.tracking_categories}
        
        # Statistical models of normal behavior
        self.behavior_models = {}
        
        # Initialize behavior models
        self._init_behavior_models()
    
    def _init_behavior_models(self) -> None:
        """
        Initialize behavior models from expected behavior model or defaults.
        """
        # If a model file is provided, try to load it
        if self.expected_behavior_model and os.path.exists(self.expected_behavior_model):
            try:
                self._load_behavior_models(self.expected_behavior_model)
                logger.info(f"Loaded behavior models from {self.expected_behavior_model}")
                return
            except Exception as e:
                logger.error(f"Error loading behavior models: {e}")
                # Fall back to default initialization
        
        # Initialize default behavior models
        for category in self.tracking_categories:
            self.behavior_models[category] = {
                "count_mean": 0.0,  # Mean count per interval
                "count_stddev": 1.0,  # Standard deviation of count
                "frequency_distribution": {},  # Distribution of item frequencies
                "transition_matrix": {},  # State transition probabilities
                "timing_mean": 0.0,  # Mean time between events
                "timing_stddev": 1.0,  # Standard deviation of timing
                "correlation_matrix": {},  # Correlations between items
                "anomaly_scores": deque(maxlen=100),  # Recent anomaly scores
                "last_update_time": time.time()
            }
        
        logger.info("Initialized default behavior models")
    
    def _load_behavior_models(self, model_path: str) -> None:
        """
        Load behavior models from a file.
        
        Args:
            model_path: Path to the behavior model file
        """
        # Determine file type by extension
        if model_path.endswith('.json'):
            with open(model_path, 'r') as f:
                self.behavior_models = json.load(f)
        elif model_path.endswith(('.yaml', '.yml')):
            with open(model_path, 'r') as f:
                self.behavior_models = yaml.safe_load(f)
        else:
            raise ValueError(f"Unsupported model file format: {model_path}")
        
        # Convert deques from lists
        for category, model in self.behavior_models.items():
            if "anomaly_scores" in model:
                model["anomaly_scores"] = deque(model["anomaly_scores"], maxlen=100)
    
    def save_behavior_models(self, model_path: str) -> bool:
        """
        Save behavior models to a file.
        
        Args:
            model_path: Path to save the behavior models to
            
        Returns:
            True if the models were successfully saved
        """
        try:
            # Convert deques to lists for serialization
            serializable_models = {}
            for category, model in self.behavior_models.items():
                serializable_model = {}
                for key, value in model.items():
                    if isinstance(value, deque):
                        serializable_model[key] = list(value)
                    else:
                        serializable_model[key] = value
                serializable_models[category] = serializable_model
            
            # Save to file based on extension
            if model_path.endswith('.json'):
                with open(model_path, 'w') as f:
                    json.dump(serializable_models, f, indent=2)
            elif model_path.endswith(('.yaml', '.yml')):
                with open(model_path, 'w') as f:
                    yaml.dump(serializable_models, f)
            else:
                logger.error(f"Unsupported model file format: {model_path}")
                return False
            
            logger.info(f"Saved behavior models to {model_path}")
            return True
                
        except Exception as e:
            logger.error(f"Error saving behavior models: {e}")
            return False
    
    def record_observation(self, category: str, observation: Any) -> None:
        """
        Record a behavioral observation.
        
        Args:
            category: Category of the observation
            observation: The observation data
        """
        if category not in self.tracking_categories:
            logger.warning(f"Unknown observation category: {category}")
            return
            
        with self._lock:
            # Add timestamp if not present
            if isinstance(observation, dict) and "timestamp" not in observation:
                observation = observation.copy()
                observation["timestamp"] = time.time()
            
            # Add observation to window
            self.observations[category].append(observation)
            
            # Update state for the category
            self.update_state(f"observation_count_{category}", len(self.observations[category]))
    
    def check(self) -> None:
        """
        Check system behavior patterns.
        """
        # Update behavior models if learning is enabled
        if self.learn_normal_behavior:
            self._update_behavior_models()
        
        # Check each category for anomalies
        for category in self.tracking_categories:
            if category in self.observations and len(self.observations[category]) > 0:
                anomaly_score, details = self._detect_anomalies(category)
                
                # Update state with anomaly score
                self.update_state(f"anomaly_score_{category}", anomaly_score,
                                status=self._get_anomaly_status(anomaly_score))
                
                # If we have a significant anomaly, create an alert
                if anomaly_score > self.anomaly_threshold:
                    self.create_alert(
                        severity=AlertSeverity.WARNING if anomaly_score < self.anomaly_threshold * 1.5 
                                                      else AlertSeverity.ERROR,
                        title=f"Behavior Anomaly Detected in {category}",
                        description=f"Anomaly score of {anomaly_score:.2f} exceeds threshold of {self.anomaly_threshold}",
                        data={
                            "anomaly_score": anomaly_score,
                            "threshold": self.anomaly_threshold,
                            "category": category,
                            "details": details
                        },
                        tags=["behavior", "anomaly", category]
                    )
        
        # Check for unusual transitions between states or APIs
        self._check_unusual_transitions()
        
        # Check timing patterns
        self._check_timing_patterns()
    
    def _update_behavior_models(self) -> None:
        """
        Update behavior models based on recent observations.
        """
        for category in self.tracking_categories:
            if category not in self.observations or len(self.observations[category]) == 0:
                continue
                
            model = self.behavior_models.get(category, {
                "count_mean": 0.0,
                "count_stddev": 1.0,
                "frequency_distribution": {},
                "transition_matrix": {},
                "timing_mean": 0.0,
                "timing_stddev": 1.0,
                "correlation_matrix": {},
                "anomaly_scores": deque(maxlen=100),
                "last_update_time": time.time()
            })
            
            # Get observations for this category
            observations = list(self.observations[category])
            
            # Update count statistics
            observation_count = len(observations)
            old_mean = model["count_mean"]
            old_stddev = model["count_stddev"]
            
            # Exponential moving average for mean
            alpha = 0.1  # Smoothing factor
            model["count_mean"] = old_mean * (1 - alpha) + observation_count * alpha
            
            # Update standard deviation using Welford's algorithm
            if observation_count > 0:
                count_delta = observation_count - old_mean
                model["count_stddev"] = math.sqrt(
                    (1 - alpha) * (old_stddev ** 2) + 
                    alpha * (count_delta ** 2)
                )
            
            # Update frequency distribution
            freq_dist = model["frequency_distribution"]
            
            for obs in observations:
                # Extract item identifier
                item_id = self._get_observation_id(obs)
                
                if item_id:
                    # Update frequency count
                    freq_dist[item_id] = freq_dist.get(item_id, 0) + 1
            
            # Normalize frequency distribution
            total_count = sum(freq_dist.values())
            if total_count > 0:
                for item_id in freq_dist:
                    freq_dist[item_id] = freq_dist[item_id] / total_count
            
            # Update transition matrix
            transition_matrix = model["transition_matrix"]
            
            if len(observations) >= 2:
                for i in range(len(observations) - 1):
                    from_id = self._get_observation_id(observations[i])
                    to_id = self._get_observation_id(observations[i + 1])
                    
                    if from_id and to_id:
                        if from_id not in transition_matrix:
                            transition_matrix[from_id] = {}
                        
                        if to_id not in transition_matrix[from_id]:
                            transition_matrix[from_id][to_id] = 0
                            
                        transition_matrix[from_id][to_id] += 1
                
                # Normalize transition probabilities
                for from_id, transitions in transition_matrix.items():
                    total = sum(transitions.values())
                    if total > 0:
                        for to_id in transitions:
                            transitions[to_id] = transitions[to_id] / total
            
            # Update timing patterns
            if len(observations) >= 2 and all(isinstance(o, dict) and "timestamp" in o for o in observations):
                # Calculate intervals between events
                intervals = [observations[i+1]["timestamp"] - observations[i]["timestamp"] 
                           for i in range(len(observations) - 1)]
                
                if intervals:
                    # Calculate mean interval
                    mean_interval = sum(intervals) / len(intervals)
                    
                    # Update timing mean with exponential moving average
                    model["timing_mean"] = model["timing_mean"] * (1 - alpha) + mean_interval * alpha
                    
                    # Update timing standard deviation
                    if len(intervals) > 1:
                        variance = sum((x - mean_interval) ** 2 for x in intervals) / len(intervals)
                        stddev = math.sqrt(variance)
                        model["timing_stddev"] = model["timing_stddev"] * (1 - alpha) + stddev * alpha
            
            # Update last update time
            model["last_update_time"] = time.time()
            
            # Save updated model
            self.behavior_models[category] = model
    
    def _detect_anomalies(self, category: str) -> Tuple[float, Dict[str, Any]]:
        """
        Detect anomalies in a behavior category.
        
        Args:
            category: Category to check for anomalies
            
        Returns:
            Tuple of (anomaly_score, anomaly_details)
        """
        if category not in self.observations or len(self.observations[category]) == 0:
            return 0.0, {}
            
        model = self.behavior_models.get(category)
        if not model:
            return 0.0, {}
            
        observations = list(self.observations[category])
        anomaly_details = {}
        anomaly_scores = []
        
        # Check count anomaly
        observation_count = len(observations)
        count_mean = model["count_mean"]
        count_stddev = max(1.0, model["count_stddev"])  # Avoid division by zero
        
        count_zscore = abs(observation_count - count_mean) / count_stddev
        anomaly_scores.append(count_zscore)
        anomaly_details["count"] = {
            "observed": observation_count,
            "expected_mean": count_mean,
            "expected_stddev": count_stddev,
            "zscore": count_zscore
        }
        
        # Check frequency distribution anomaly
        freq_dist = model["frequency_distribution"]
        if freq_dist and observations:
            # Calculate current frequency distribution
            current_freq = {}
            for obs in observations:
                item_id = self._get_observation_id(obs)
                if item_id:
                    current_freq[item_id] = current_freq.get(item_id, 0) + 1
            
            # Normalize
            total_count = sum(current_freq.values())
            if total_count > 0:
                for item_id in current_freq:
                    current_freq[item_id] = current_freq[item_id] / total_count
            
            # Calculate Jensen-Shannon divergence between distributions
            js_divergence = self._jensen_shannon_divergence(freq_dist, current_freq)
            
            # Convert to a z-score equivalent (4 * JS is approx. equivalent to z=3 for highly divergent distributions)
            freq_zscore = 4.0 * js_divergence
            anomaly_scores.append(freq_zscore)
            anomaly_details["frequency"] = {
                "jensen_shannon_divergence": js_divergence,
                "zscore_equivalent": freq_zscore
            }
        
        # Check transition anomalies
        transition_matrix = model["transition_matrix"]
        if transition_matrix and len(observations) >= 2:
            transition_anomalies = []
            
            for i in range(len(observations) - 1):
                from_id = self._get_observation_id(observations[i])
                to_id = self._get_observation_id(observations[i + 1])
                
                if from_id and to_id:
                    if from_id in transition_matrix:
                        # Check if this transition is known
                        if to_id in transition_matrix[from_id]:
                            probability = transition_matrix[from_id][to_id]
                            if probability < 0.05:  # Unusually rare transition
                                transition_anomalies.append({
                                    "from": from_id,
                                    "to": to_id,
                                    "probability": probability,
                                    "anomaly_score": (0.05 - probability) * 20  # Scale to 0-1 range
                                })
                        else:
                            # Unknown transition
                            transition_anomalies.append({
                                "from": from_id,
                                "to": to_id,
                                "probability": 0.0,
                                "anomaly_score": 1.0
                            })
            
            if transition_anomalies:
                max_transition_anomaly = max(a["anomaly_score"] for a in transition_anomalies)
                anomaly_scores.append(max_transition_anomaly * 3)  # Scale to z-score range
                anomaly_details["transitions"] = {
                    "anomalies": transition_anomalies,
                    "max_anomaly_score": max_transition_anomaly
                }
        
        # Check timing anomalies
        if len(observations) >= 2 and all(isinstance(o, dict) and "timestamp" in o for o in observations):
            intervals = [observations[i+1]["timestamp"] - observations[i]["timestamp"] 
                       for i in range(len(observations) - 1)]
            
            if intervals:
                mean_interval = sum(intervals) / len(intervals)
                timing_mean = model["timing_mean"]
                timing_stddev = max(0.001, model["timing_stddev"])  # Avoid division by zero
                
                timing_zscore = abs(mean_interval - timing_mean) / timing_stddev
                anomaly_scores.append(timing_zscore)
                anomaly_details["timing"] = {
                    "observed_mean_interval": mean_interval,
                    "expected_mean_interval": timing_mean,
                    "expected_stddev": timing_stddev,
                    "zscore": timing_zscore
                }
        
        # Calculate overall anomaly score
        # Use max of all anomaly scores as the primary indicator,
        # but also incorporate the mean to reflect multiple moderate anomalies
        if anomaly_scores:
            max_score = max(anomaly_scores)
            mean_score = sum(anomaly_scores) / len(anomaly_scores)
            overall_score = 0.8 * max_score + 0.2 * mean_score
            
            # Update anomaly score history
            if "anomaly_scores" in model:
                model["anomaly_scores"].append(overall_score)
            
            return overall_score, anomaly_details
        
        return 0.0, anomaly_details
    
    def _check_unusual_transitions(self) -> None:
        """
        Check for unusual transitions between states or APIs.
        """
        # This check focuses specifically on state_transitions and api_calls categories
        for category in ["state_transitions", "api_calls"]:
            if category not in self.observations or len(self.observations[category]) < 2:
                continue
                
            model = self.behavior_models.get(category)
            if not model or "transition_matrix" not in model:
                continue
                
            transition_matrix = model["transition_matrix"]
            if not transition_matrix:
                continue
                
            observations = list(self.observations[category])
            unusual_transitions = []
            
            for i in range(len(observations) - 1):
                from_id = self._get_observation_id(observations[i])
                to_id = self._get_observation_id(observations[i + 1])
                
                if from_id and to_id:
                    # Check if transition exists in model
                    if from_id in transition_matrix:
                        if to_id in transition_matrix[from_id]:
                            probability = transition_matrix[from_id][to_id]
                            
                            # If probability is very low, this is unusual
                            if probability < 0.01:
                                unusual_transitions.append({
                                    "from": from_id,
                                    "to": to_id,
                                    "probability": probability,
                                    "index": i,
                                    "timestamp": observations[i+1].get("timestamp", time.time())
                                })
                        else:
                            # Transition not in model at all
                            unusual_transitions.append({
                                "from": from_id,
                                "to": to_id,
                                "probability": 0.0,
                                "index": i,
                                "timestamp": observations[i+1].get("timestamp", time.time())
                            })
            
            # If we found unusual transitions, create an alert
            if unusual_transitions:
                # Only alert on the most unusual transitions
                top_unusual = sorted(unusual_transitions, key=lambda x: x["probability"])[:3]
                
                self.create_alert(
                    severity=AlertSeverity.WARNING,
                    title=f"Unusual {category.replace('_', ' ')} detected",
                    description=f"Detected {len(unusual_transitions)} unusual transitions in {category}",
                    data={
                        "category": category,
                        "unusual_transitions": top_unusual,
                        "total_unusual": len(unusual_transitions)
                    },
                    tags=["behavior", "anomaly", "transition", category]
                )
    
    def _check_timing_patterns(self) -> None:
        """
        Check for anomalies in timing patterns.
        """
        for category in self.tracking_categories:
            if category not in self.observations or len(self.observations[category]) < 3:
                continue
                
            model = self.behavior_models.get(category)
            if not model or "timing_mean" not in model or "timing_stddev" not in model:
                continue
                
            observations = list(self.observations[category])
            if not all(isinstance(o, dict) and "timestamp" in o for o in observations):
                continue
                
            # Sort by timestamp
            observations.sort(key=lambda x: x["timestamp"])
            
            # Calculate intervals
            intervals = [observations[i+1]["timestamp"] - observations[i]["timestamp"] 
                       for i in range(len(observations) - 1)]
            
            if not intervals:
                continue
                
            # Check for suspicious timing patterns
            timing_mean = model["timing_mean"]
            timing_stddev = max(0.001, model["timing_stddev"])
            
            # Check for unusually regular intervals
            if len(intervals) >= 5:
                interval_stddev = np.std(intervals)
                cv = interval_stddev / np.mean(intervals)  # Coefficient of variation
                
                # If coefficient of variation is very low, intervals are suspiciously regular
                if cv < 0.1 and np.mean(intervals) > 0.1:  # Avoid noise from very fast events
                    self.create_alert(
                        severity=AlertSeverity.WARNING,
                        title=f"Suspiciously regular timing pattern in {category}",
                        description=f"Events in {category} occurring at unusually regular intervals",
                        data={
                            "category": category,
                            "mean_interval": np.mean(intervals),
                            "interval_stddev": interval_stddev,
                            "coefficient_of_variation": cv,
                            "sample_count": len(intervals)
                        },
                        tags=["behavior", "anomaly", "timing", category]
                    )
            
            # Check for unusually fast intervals
            if timing_mean > 0:
                fastest_interval = min(intervals)
                if fastest_interval < timing_mean / 5 and fastest_interval < 0.01:
                    self.create_alert(
                        severity=AlertSeverity.WARNING,
                        title=f"Unusually fast events in {category}",
                        description=f"Detected unusually fast interval between events in {category}",
                        data={
                            "category": category,
                            "fastest_interval": fastest_interval,
                            "normal_mean_interval": timing_mean,
                            "ratio": timing_mean / fastest_interval if fastest_interval > 0 else float('inf')
                        },
                        tags=["behavior", "anomaly", "timing", category]
                    )
    
    def _get_observation_id(self, observation: Any) -> Optional[str]:
        """
        Extract a unique identifier from an observation.
        
        Args:
            observation: The observation to extract an ID from
            
        Returns:
            String identifier or None if not extractable
        """
        if isinstance(observation, dict):
            # Try various common ID fields
            for field in ["id", "name", "action", "state", "event", "type", "function"]:
                if field in observation and observation[field]:
                    return str(observation[field])
            
            # If no ID field found, try creating a content hash
            return self._hash_observation(observation)
        
        elif isinstance(observation, str):
            return observation
        
        else:
            return self._hash_observation(observation)
    
    def _hash_observation(self, observation: Any) -> str:
        """
        Create a hash of an observation for identification.
        
        Args:
            observation: The observation to hash
            
        Returns:
            Hash string
        """
        if isinstance(observation, dict):
            # Create a stable representation for hashing
            hash_input = json.dumps(observation, sort_keys=True)
        else:
            hash_input = str(observation)
            
        return hashlib.md5(hash_input.encode()).hexdigest()[:8]
    
    def _jensen_shannon_divergence(self, p: Dict[str, float], q: Dict[str, float]) -> float:
        """
        Calculate Jensen-Shannon divergence between two discrete probability distributions.
        
        Args:
            p: First probability distribution (dict of item -> probability)
            q: Second probability distribution (dict of item -> probability)
            
        Returns:
            Jensen-Shannon divergence (0 to 1, where 0 means identical distributions)
        """
        # Get all keys from both distributions
        all_keys = set(p.keys()) | set(q.keys())
        
        # Create numpy arrays with probabilities for each key
        p_array = np.array([p.get(k, 0.0) for k in all_keys])
        q_array = np.array([q.get(k, 0.0) for k in all_keys])
        
        # Normalize to ensure they sum to 1
        p_sum = np.sum(p_array)
        if p_sum > 0:
            p_array = p_array / p_sum
        
        q_sum = np.sum(q_array)
        if q_sum > 0:
            q_array = q_array / q_sum
        
        # Calculate the average distribution
        m_array = 0.5 * (p_array + q_array)
        
        # Calculate KL divergence for each distribution to the average
        # Add a small epsilon to avoid log(0)
        epsilon = 1e-10
        kl_p_m = np.sum(p_array * np.log2(p_array / (m_array + epsilon) + epsilon))
        kl_q_m = np.sum(q_array * np.log2(q_array / (m_array + epsilon) + epsilon))
        
        # Jensen-Shannon divergence is the average of the KL divergences
        js_divergence = 0.5 * (kl_p_m + kl_q_m)
        
        # Handle numerical issues
        if js_divergence < 0:
            js_divergence = 0.0
        elif js_divergence > 1.0:
            js_divergence = 1.0
            
        return js_divergence
    
    def _get_anomaly_status(self, score: float) -> str:
        """
        Determine status based on anomaly score.
        
        Args:
            score: Anomaly score (higher is more anomalous)
            
        Returns:
            Status string (normal, warning, error)
        """
        if score >= self.anomaly_threshold * 1.5:
            return "error"
        elif score >= self.anomaly_threshold:
            return "warning"
        else:
            return "normal"
    
    def get_anomaly_scores(self) -> Dict[str, float]:
        """
        Get current anomaly scores for all categories.
        
        Returns:
            Dictionary mapping category to anomaly score
        """
        scores = {}
        
        with self._lock:
            for category in self.tracking_categories:
                state = self.get_state(f"anomaly_score_{category}")
                if state:
                    scores[category] = state.value
                else:
                    scores[category] = 0.0
                    
        return scores


class ActivityTracker(BaseMonitor):
    """
    Tracks system activities and operations.
    
    ActivityTracker records activities, operations, and events in the system,
    providing a log of what happened for auditing, debugging, and analysis.
    """
    
    def __init__(self, 
                name: str = "activity_tracker",
                check_interval: float = 60.0,
                priority: MonitorPriority = MonitorPriority.LOW,
                enabled: bool = True,
                alert_handler: Optional[Callable[[MonitoringAlert], None]] = None,
                max_activities: int = 10000,
                log_to_file: bool = False,
                log_file_path: Optional[str] = None,
                activity_categories: List[str] = None):
        """
        Initialize an activity tracker.
        
        Args:
            name: Unique name for this monitor
            check_interval: Interval between checks in seconds
            priority: Priority level for this monitor
            enabled: Whether this monitor is initially enabled
            alert_handler: Optional handler for alerts
            max_activities: Maximum number of activities to keep in memory
            log_to_file: Whether to log activities to a file
            log_file_path: Path to the log file (None for default)
            activity_categories: Categories of activities to track
        """
        super().__init__(name, check_interval, priority, enabled, alert_handler)
        
        self.max_activities = max_activities
        self.log_to_file = log_to_file
        self.log_file_path = log_file_path or "activity_log.jsonl"
        self.activity_categories = activity_categories or [
            "api_call", "function_call", "state_change", "resource_usage", 
            "error", "security", "data_access", "user_action", "system_event"
        ]
        
        # Activity log (list of activity dictionaries)
        self.activities = deque(maxlen=max_activities)
        
        # Activity statistics
        self.activity_counts = {category: 0 for category in self.activity_categories}
        self.hourly_stats = {}  # hour timestamp -> category -> count
        self.daily_stats = {}   # day timestamp -> category -> count
        
        # File logger setup
        self.file_logger = None
        if self.log_to_file:
            self._setup_file_logger()
    
    def _setup_file_logger(self) -> None:
        """
        Set up the file logger for activity logging.
        """
        try:
            # Create a dedicated logger for activities
            self.file_logger = logging.getLogger(f"ultra.safety.activity_log")
            self.file_logger.setLevel(logging.INFO)
            
            # Remove any existing handlers
            for handler in self.file_logger.handlers[:]:
                self.file_logger.removeHandler(handler)
            
            # Add a file handler
            handler = logging.FileHandler(self.log_file_path)
            handler.setFormatter(logging.Formatter("%(message)s"))
            self.file_logger.addHandler(handler)
            
            # Don't propagate to parent logger
            self.file_logger.propagate = False
            
            logger.info(f"Activity logging to file: {self.log_file_path}")
            
        except Exception as e:
            logger.error(f"Error setting up activity file logger: {e}")
            self.log_to_file = False
    
    def record_activity(self, 
                      category: str, 
                      action: str, 
                      details: Dict[str, Any] = None,
                      source: str = None,
                      success: bool = True,
                      resource_id: str = None,
                      user_id: str = None,
                      tags: List[str] = None) -> str:
        """
        Record a system activity.
        
        Args:
            category: Category of the activity
            action: Action performed
            details: Additional details about the activity
            source: Source of the activity
            success: Whether the activity was successful
            resource_id: ID of the resource involved
            user_id: ID of the user involved
            tags: Tags for categorizing the activity
            
        Returns:
            ID of the recorded activity
        """
        # Validate category
        if category not in self.activity_categories:
            logger.warning(f"Unknown activity category: {category}")
            category = "system_event"  # Default category
        
        # Create activity record
        activity_id = str(uuid.uuid4())
        timestamp = time.time()
        
        activity = {
            "id": activity_id,
            "timestamp": timestamp,
            "datetime": datetime.fromtimestamp(timestamp).isoformat(),
            "category": category,
            "action": action,
            "details": details or {},
            "source": source or "system",
            "success": success,
            "resource_id": resource_id,
            "user_id": user_id,
            "tags": tags or []
        }
        
        with self._lock:
            # Add to activity log
            self.activities.append(activity)
            
            # Update activity count
            self.activity_counts[category] = self.activity_counts.get(category, 0) + 1
            
            # Update hourly stats
            hour_ts = int(timestamp // 3600) * 3600
            if hour_ts not in self.hourly_stats:
                self.hourly_stats[hour_ts] = {}
            
            if category not in self.hourly_stats[hour_ts]:
                self.hourly_stats[hour_ts][category] = 0
                
            self.hourly_stats[hour_ts][category] += 1
            
            # Update daily stats
            day_ts = int(timestamp // 86400) * 86400
            if day_ts not in self.daily_stats:
                self.daily_stats[day_ts] = {}
            
            if category not in self.daily_stats[day_ts]:
                self.daily_stats[day_ts][category] = 0
                
            self.daily_stats[day_ts][category] += 1
            
            # Update state for this category
            self.update_state(f"activity_count_{category}", self.activity_counts[category])
        
        # Log to file if enabled
        if self.log_to_file and self.file_logger:
            try:
                self.file_logger.info(json.dumps(activity))
            except Exception as e:
                logger.error(f"Error logging activity to file: {e}")
        
        return activity_id
    
    def get_activities(self, 
                     category: Optional[str] = None, 
                     start_time: Optional[float] = None,
                     end_time: Optional[float] = None,
                     filters: Dict[str, Any] = None,
                     max_count: int = 100,
                     include_details: bool = True) -> List[Dict[str, Any]]:
        """
        Get activities from the log with filtering.
        
        Args:
            category: Filter by category
            start_time: Filter by start time (timestamp)
            end_time: Filter by end time (timestamp)
            filters: Additional filters (field name -> value)
            max_count: Maximum number of activities to return
            include_details: Whether to include activity details
            
        Returns:
            List of matching activities
        """
        with self._lock:
            activities = list(self.activities)
        
        # Apply filters
        if category:
            activities = [a for a in activities if a["category"] == category]
        
        if start_time:
            activities = [a for a in activities if a["timestamp"] >= start_time]
        
        if end_time:
            activities = [a for a in activities if a["timestamp"] <= end_time]
        
        if filters:
            for field, value in filters.items():
                activities = [a for a in activities if field in a and a[field] == value]
        
        # Sort by timestamp (newest first)
        activities.sort(key=lambda a: a["timestamp"], reverse=True)
        
        # Limit count
        activities = activities[:max_count]
        
        # Remove details if not requested
        if not include_details:
            for activity in activities:
                activity.pop("details", None)
        
        return activities
    
    def get_activity_stats(self, 
                         period: str = "all",
                         category: Optional[str] = None) -> Dict[str, Any]:
        """
        Get activity statistics.
        
        Args:
            period: Time period for stats (all, day, week, month)
            category: Filter by category
            
        Returns:
            Dictionary of activity statistics
        """
        with self._lock:
            stats = {
                "total_activities": sum(self.activity_counts.values()),
                "activities_by_category": self.activity_counts.copy()
            }
            
            # Filter by time period
            now = time.time()
            
            if period == "day":
                # Last 24 hours
                start_time = now - 86400
                hourly_stats = {ts: counts for ts, counts in self.hourly_stats.items() if ts >= start_time}
                stats["hourly"] = hourly_stats
                
                # Calculate totals for this period
                period_totals = {cat: 0 for cat in self.activity_categories}
                for ts, counts in hourly_stats.items():
                    for cat, count in counts.items():
                        period_totals[cat] = period_totals.get(cat, 0) + count
                
                stats["period_totals"] = period_totals
                
            elif period == "week":
                # Last 7 days
                start_time = now - 7 * 86400
                daily_stats = {ts: counts for ts, counts in self.daily_stats.items() if ts >= start_time}
                stats["daily"] = daily_stats
                
                # Calculate totals for this period
                period_totals = {cat: 0 for cat in self.activity_categories}
                for ts, counts in daily_stats.items():
                    for cat, count in counts.items():
                        period_totals[cat] = period_totals.get(cat, 0) + count
                
                stats["period_totals"] = period_totals
                
            elif period == "month":
                # Last 30 days
                start_time = now - 30 * 86400
                daily_stats = {ts: counts for ts, counts in self.daily_stats.items() if ts >= start_time}
                stats["daily"] = daily_stats
                
                # Calculate totals for this period
                period_totals = {cat: 0 for cat in self.activity_categories}
                for ts, counts in daily_stats.items():
                    for cat, count in counts.items():
                        period_totals[cat] = period_totals.get(cat, 0) + count
                
                stats["period_totals"] = period_totals
            
            # Filter by category if specified
            if category:
                if "period_totals" in stats:
                    stats["period_totals"] = {cat: count for cat, count in stats["period_totals"].items() 
                                             if cat == category}
                
                if "hourly" in stats:
                    for ts, counts in stats["hourly"].items():
                        stats["hourly"][ts] = {cat: count for cat, count in counts.items() if cat == category}
                
                if "daily" in stats:
                    for ts, counts in stats["daily"].items():
                        stats["daily"][ts] = {cat: count for cat, count in counts.items() if cat == category}
        
        return stats
    
    def check(self) -> None:
        """
        Perform regular activity log maintenance and stats calculation.
        """
        with self._lock:
            # Clean up old stats entries
            now = time.time()
            
            # Remove hourly stats older than 7 days
            cutoff_hourly = now - 7 * 86400
            self.hourly_stats = {ts: counts for ts, counts in self.hourly_stats.items() 
                               if ts >= cutoff_hourly}
            
            # Remove daily stats older than 90 days
            cutoff_daily = now - 90 * 86400
            self.daily_stats = {ts: counts for ts, counts in self.daily_stats.items() 
                              if ts >= cutoff_daily}
            
            # Update states with latest activity counts
            for category, count in self.activity_counts.items():
                self.update_state(f"activity_count_{category}", count)
            
            # Calculate hourly rates for each category
            last_hour_ts = int(now // 3600) * 3600 - 3600  # Previous full hour
            if last_hour_ts in self.hourly_stats:
                for category, count in self.hourly_stats[last_hour_ts].items():
                    self.update_state(f"hourly_rate_{category}", count / 3600.0)
        
        # Check for unusual activity spikes
        self._check_activity_spikes()
    
    def _check_activity_spikes(self) -> None:
        """
        Check for unusual spikes in activity rates.
        """
        with self._lock:
            now = time.time()
            
            # Get the last few hours of data
            recent_hours = sorted([ts for ts in self.hourly_stats.keys() 
                                if now - ts <= 24 * 3600])
            
            if len(recent_hours) < 2:
                return  # Not enough data
            
            latest_hour = max(recent_hours)
            previous_hours = [ts for ts in recent_hours if ts < latest_hour]
            
            for category in self.activity_categories:
                # Calculate average rate for previous hours
                prev_counts = [self.hourly_stats[ts].get(category, 0) for ts in previous_hours]
                if not prev_counts:
                    continue
                    
                avg_count = sum(prev_counts) / len(prev_counts)
                if avg_count == 0:
                    continue  # Avoid division by zero
                
                # Get latest count
                latest_count = self.hourly_stats[latest_hour].get(category, 0)
                
                # Calculate ratio of latest to average
                ratio = latest_count / max(1, avg_count)
                
                # Check for significant spike
                if ratio > 3.0 and latest_count > 10:
                    self.create_alert(
                        severity=AlertSeverity.WARNING,
                        title=f"Activity spike detected in {category}",
                        description=f"Activity rate in {category} is {ratio:.1f}x higher than average",
                        data={
                            "category": category,
                            "latest_count": latest_count,
                            "average_count": avg_count,
                            "ratio": ratio,
                            "latest_hour": latest_hour,
                            "previous_hours": previous_hours
                        },
                        tags=["activity", "spike", category]
                    )


class AnomalyDetector(BaseMonitor):
    """
    Detects anomalies in system behavior and metrics.
    
    AnomalyDetector uses various statistical and machine learning techniques
    to detect anomalies in system behavior, resource usage, and other metrics,
    providing early warning of potential issues.
    """
    
    def __init__(self, 
                name: str = "anomaly_detector",
                check_interval: float = 60.0,
                priority: MonitorPriority = MonitorPriority.HIGH,
                enabled: bool = True,
                alert_handler: Optional[Callable[[MonitoringAlert], None]] = None,
                sensitivity: float = 0.8,
                window_size: int = 100,
                learning_rate: float = 0.1,
                detection_methods: List[str] = None,
                metrics_to_monitor: List[str] = None):
        """
        Initialize an anomaly detector.
        
        Args:
            name: Unique name for this monitor
            check_interval: Interval between checks in seconds
            priority: Priority level for this monitor
            enabled: Whether this monitor is initially enabled
            alert_handler: Optional handler for alerts
            sensitivity: Sensitivity level for anomaly detection (0.0-1.0)
            window_size: Number of observations to keep for each metric
            learning_rate: Learning rate for adaptive algorithms
            detection_methods: List of anomaly detection methods to use
            metrics_to_monitor: List of metrics to monitor for anomalies
        """
        super().__init__(name, check_interval, priority, enabled, alert_handler)
        
        self.sensitivity = sensitivity
        self.window_size = window_size
        self.learning_rate = learning_rate
        self.detection_methods = detection_methods or [
            "z_score", "moving_average", "isolation_forest", "entropy"
        ]
        self.metrics_to_monitor = metrics_to_monitor or [
            "cpu_usage", "memory_usage", "network_usage", "io_usage", 
            "disk_usage", "error_rate", "response_time", "throughput"
        ]
        
        # Data windows for each metric (metric_name -> deque of values)
        self.data_windows = {metric: deque(maxlen=window_size) for metric in self.metrics_to_monitor}
        
        # Models for each metric and detection method
        self.models = {}
        
        # Anomaly scores for each metric
        self.anomaly_scores = {metric: 0.0 for metric in self.metrics_to_monitor}
        
        # Anomaly thresholds for each metric
        self.thresholds = {metric: 3.0 for metric in self.metrics_to_monitor}  # Default z-score threshold
        
        # Initialize detection methods
        self._init_detection_methods()
    
    def _init_detection_methods(self) -> None:
        """
        Initialize anomaly detection methods.
        """
        # Initialize models for each metric and method
        for metric in self.metrics_to_monitor:
            self.models[metric] = {}
            
            # Z-score model
            if "z_score" in self.detection_methods:
                self.models[metric]["z_score"] = {
                    "mean": 0.0,
                    "stddev": 1.0,
                    "min": float('inf'),
                    "max": float('-inf'),
                    "initialized": False
                }
            
            # Moving average model
            if "moving_average" in self.detection_methods:
                self.models[metric]["moving_average"] = {
                    "short_window": deque(maxlen=int(self.window_size * 0.2)),  # 20% of window size
                    "long_window": deque(maxlen=self.window_size),
                    "short_avg": 0.0,
                    "long_avg": 0.0,
                    "threshold_ratio": 1.5,
                    "initialized": False
                }
            
            # Isolation Forest model (initialized on first check)
            if "isolation_forest" in self.detection_methods:
                self.models[metric]["isolation_forest"] = {
                    "model": None,
                    "score_history": deque(maxlen=50),
                    "initialized": False
                }
            
            # Entropy model
            if "entropy" in self.detection_methods:
                self.models[metric]["entropy"] = {
                    "baseline_entropy": 0.0,
                    "current_entropy": 0.0,
                    "bins": 10,  # Number of bins for histogram
                    "initialized": False
                }
    
    def add_metric_value(self, metric: str, value: float, timestamp: Optional[float] = None) -> None:
        """
        Add a metric value to the data window.
        
        Args:
            metric: Name of the metric
            value: Value of the metric
            timestamp: Optional timestamp for the value
        """
        if metric not in self.metrics_to_monitor:
            logger.warning(f"Unknown metric: {metric}")
            return
            
        timestamp = timestamp or time.time()
        
        with self._lock:
            # Add value to data window
            self.data_windows[metric].append((timestamp, value))
            
            # Update models with new value
            self._update_models(metric, value)
    
    def _update_models(self, metric: str, value: float) -> None:
        """
        Update anomaly detection models with a new metric value.
        
        Args:
            metric: Name of the metric
            value: Value of the metric
        """
        models = self.models.get(metric, {})
        
        # Update Z-score model
        if "z_score" in models:
            z_model = models["z_score"]
            
            if not z_model["initialized"]:
                # Initialize with first value
                z_model["mean"] = value
                z_model["stddev"] = value * 0.1 if value != 0 else 0.1  # Initial guess
                z_model["min"] = value
                z_model["max"] = value
                z_model["initialized"] = True
            else:
                # Update running statistics using exponential moving average
                old_mean = z_model["mean"]
                z_model["mean"] = old_mean * (1 - self.learning_rate) + value * self.learning_rate
                
                # Update variance using Welford's algorithm
                variance = z_model["stddev"] ** 2
                delta = value - old_mean
                delta2 = value - z_model["mean"]
                variance = (1 - self.learning_rate) * variance + self.learning_rate * delta * delta2
                z_model["stddev"] = max(0.001, math.sqrt(variance))  # Avoid division by zero
                
                # Update min/max
                z_model["min"] = min(z_model["min"], value)
                z_model["max"] = max(z_model["max"], value)
        
        # Update Moving Average model
        if "moving_average" in models:
            ma_model = models["moving_average"]
            
            # Add value to windows
            ma_model["short_window"].append(value)
            ma_model["long_window"].append(value)
            
            # Update averages
            if ma_model["short_window"]:
                ma_model["short_avg"] = sum(ma_model["short_window"]) / len(ma_model["short_window"])
            
            if ma_model["long_window"]:
                ma_model["long_avg"] = sum(ma_model["long_window"]) / len(ma_model["long_window"])
            
            ma_model["initialized"] = len(ma_model["long_window"]) >= 5  # Need at least 5 values
        
        # Update Isolation Forest model
        if "isolation_forest" in models and "isolation_forest" in self.detection_methods:
            iso_model = models["isolation_forest"]
            
            # Add data point
            data = list(self.data_windows[metric])
            
            # Train model when we have enough data
            if len(data) >= 50 and (iso_model["model"] is None or len(data) % 10 == 0):
                try:
                    # Try to import isolation forest
                    from sklearn.ensemble import IsolationForest
                    
                    # Extract values for training
                    X = np.array([v for _, v in data]).reshape(-1, 1)
                    
                    # Train model
                    iso_model["model"] = IsolationForest(
                        contamination=0.05,  # Assume 5% anomalies
                        random_state=42
                    ).fit(X)
                    
                    iso_model["initialized"] = True
                    
                except ImportError:
                    logger.warning("scikit-learn not available, disabling isolation_forest method")
                    self.detection_methods.remove("isolation_forest")
                except Exception as e:
                    logger.error(f"Error training isolation forest model: {e}")
        
        # Update Entropy model
        if "entropy" in models:
            ent_model = models["entropy"]
            
            # Add value to window
            data = [v for _, v in self.data_windows[metric]]
            
            if len(data) >= 10:  # Need at least 10 values
                # Calculate entropy of current window
                ent_model["current_entropy"] = self._calculate_entropy(data, ent_model["bins"])
                
                # Update baseline entropy with exponential moving average
                if not ent_model["initialized"]:
                    ent_model["baseline_entropy"] = ent_model["current_entropy"]
                    ent_model["initialized"] = True
                else:
                    ent_model["baseline_entropy"] = (
                        ent_model["baseline_entropy"] * (1 - self.learning_rate) +
                        ent_model["current_entropy"] * self.learning_rate
                    )
    
    def _calculate_entropy(self, data: List[float], bins: int) -> float:
        """
        Calculate entropy of a data distribution.
        
        Args:
            data: List of values
            bins: Number of bins for histogram
            
        Returns:
            Entropy value
        """
        # Handle empty or constant data
        if not data or all(x == data[0] for x in data):
            return 0.0
            
        # Create histogram
        try:
            # Get data range
            min_val = min(data)
            max_val = max(data)
            
            # Add small range if all values are the same
            if min_val == max_val:
                min_val = min_val * 0.99 if min_val != 0 else -0.01
                max_val = max_val * 1.01 if max_val != 0 else 0.01
            
            # Create bins
            bin_edges = np.linspace(min_val, max_val, bins + 1)
            
            # Count values in each bin
            hist, _ = np.histogram(data, bins=bin_edges)
            
            # Calculate probabilities
            p = hist / len(data)
            
            # Filter out zeros to avoid log(0)
            p = p[p > 0]
            
            # Calculate entropy
            entropy = -np.sum(p * np.log2(p))
            
            return entropy
            
        except Exception as e:
            logger.error(f"Error calculating entropy: {e}")
            return 0.0
    
    def check(self) -> None:
        """
        Check for anomalies in monitored metrics.
        """
        # Get metric values from associated states
        for metric in self.metrics_to_monitor:
            state = self.get_state(metric)
            if state is not None:
                # Add value to data window
                self.add_metric_value(metric, state.value)
        
        # Detect anomalies for each metric
        for metric in self.metrics_to_monitor:
            if len(self.data_windows[metric]) < 5:
                continue  # Not enough data points
                
            # Detect anomalies using all enabled methods
            anomaly_score = self._detect_anomalies(metric)
            
            # Update anomaly score state
            self.update_state(f"anomaly_score_{metric}", anomaly_score,
                             status=self._get_anomaly_status(anomaly_score))
            
            # Save current score
            self.anomaly_scores[metric] = anomaly_score
            
            # Create alert if anomaly score exceeds threshold
            if anomaly_score > self.thresholds[metric]:
                # Get current and recent values
                current_value = self.data_windows[metric][-1][1] if self.data_windows[metric] else None
                recent_values = [v for _, v in list(self.data_windows[metric])[-10:]] if self.data_windows[metric] else []
                
                self.create_alert(
                    severity=AlertSeverity.WARNING if anomaly_score < self.thresholds[metric] * 1.5 
                                                  else AlertSeverity.ERROR,
                    title=f"Anomaly detected in {metric}",
                    description=f"Anomaly score of {anomaly_score:.2f} exceeds threshold of {self.thresholds[metric]}",
                    data={
                        "metric": metric,
                        "anomaly_score": anomaly_score,
                        "threshold": self.thresholds[metric],
                        "current_value": current_value,
                        "recent_values": recent_values
                    },
                    tags=["anomaly", metric]
                )
    
    def _detect_anomalies(self, metric: str) -> float:
        """
        Detect anomalies for a metric using all enabled detection methods.
        
        Args:
            metric: Name of the metric
            
        Returns:
            Anomaly score (higher means more anomalous)
        """
        scores = []
        models = self.models.get(metric, {})
        data_window = self.data_windows.get(metric, deque())
        
        if not data_window:
            return 0.0
            
        # Extract latest value
        latest_value = data_window[-1][1]
        
        # Z-score method
        if "z_score" in models and models["z_score"]["initialized"]:
            z_model = models["z_score"]
            
            # Calculate z-score
            if z_model["stddev"] > 0:
                z_score = abs(latest_value - z_model["mean"]) / z_model["stddev"]
                scores.append(("z_score", z_score))
        
        # Moving Average method
        if "moving_average" in models and models["moving_average"]["initialized"]:
            ma_model = models["moving_average"]
            
            # Calculate ratio between short and long window averages
            if ma_model["long_avg"] != 0:
                ratio = ma_model["short_avg"] / ma_model["long_avg"]
                # Convert to deviation from normal (1.0)
                ma_score = abs(ratio - 1.0) * 3.0  # Scale to be comparable to z-score
                scores.append(("moving_average", ma_score))
        
        # Isolation Forest method
        if "isolation_forest" in models and models["isolation_forest"]["initialized"]:
            iso_model = models["isolation_forest"]
            
            if iso_model["model"] is not None:
                try:
                    # Predict anomaly score for latest value
                    X = np.array([latest_value]).reshape(1, -1)
                    raw_score = -iso_model["model"].score_samples(X)[0]  # Negative score, higher is more anomalous
                    
                    # Normalize score based on history
                    iso_model["score_history"].append(raw_score)
                    mean_score = np.mean(iso_model["score_history"])
                    std_score = np.std(iso_model["score_history"]) or 1.0
                    
                    # Convert to z-score like scale
                    iso_score = (raw_score - mean_score) / std_score if std_score > 0 else 0.0
                    iso_score = max(0.0, iso_score)  # Only consider positive anomalies
                    
                    scores.append(("isolation_forest", iso_score))
                    
                except Exception as e:
                    logger.debug(f"Error in isolation forest scoring: {e}")
        
        # Entropy method
        if "entropy" in models and models["entropy"]["initialized"]:
            ent_model = models["entropy"]
            
            # Calculate relative entropy change
            if ent_model["baseline_entropy"] > 0:
                entropy_ratio = ent_model["current_entropy"] / ent_model["baseline_entropy"]
                
                # Convert to anomaly score (deviation from 1.0)
                ent_score = abs(entropy_ratio - 1.0) * 3.0  # Scale to be comparable to z-score
                scores.append(("entropy", ent_score))
        
        # Combine scores from all methods
        if not scores:
            return 0.0
            
        # Use weighted average of scores, with weights based on sensitivity
        total_weight = 0.0
        weighted_score = 0.0
        
        for method, score in scores:
            weight = 1.0
            if method == "z_score":
                weight = 1.0 + self.sensitivity
            elif method == "moving_average":
                weight = 0.8 + self.sensitivity * 0.4
            elif method == "isolation_forest":
                weight = 0.7 + self.sensitivity * 0.6
            elif method == "entropy":
                weight = 0.6 + self.sensitivity * 0.4
            
            weighted_score += score * weight
            total_weight += weight
        
        if total_weight > 0:
            return weighted_score / total_weight
        else:
            return 0.0
    
    def _get_anomaly_status(self, score: float) -> str:
        """
        Determine status based on anomaly score.
        
        Args:
            score: Anomaly score
            
        Returns:
            Status string (normal, warning, error)
        """
        # Adjust thresholds based on sensitivity
        base_warning = 3.0
        base_error = 4.5
        
        warning_threshold = base_warning - self.sensitivity * 1.0
        error_threshold = base_error - self.sensitivity * 1.5
        
        if score >= error_threshold:
            return "error"
        elif score >= warning_threshold:
            return "warning"
        else:
            return "normal"
    
    def get_anomaly_scores(self) -> Dict[str, float]:
        """
        Get current anomaly scores for all metrics.
        
        Returns:
            Dictionary mapping metric to anomaly score
        """
        with self._lock:
            return self.anomaly_scores.copy()
    
    def set_sensitivity(self, sensitivity: float) -> None:
        """
        Set sensitivity level for anomaly detection.
        
        Args:
            sensitivity: New sensitivity level (0.0-1.0)
        """
        with self._lock:
            self.sensitivity = max(0.0, min(1.0, sensitivity))


class SystemMonitor:
    """
    Centralized system monitoring manager.
    
    SystemMonitor manages multiple monitoring components, coordinates
    their activities, aggregates their results, and provides a unified
    interface to the monitoring system.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize a system monitor.
        
        Args:
            config: Optional configuration dictionary
        """
        self.config = config or {}
        self.monitors = {}  # name -> BaseMonitor
        self.alert_handlers = []  # List of alert handler functions
        self.state_cache = {}  # name -> MonitoringState
        
        self.running = False
        self._lock = threading.RLock()
        
        # Configure alert handlers
        self._setup_alert_handlers()
    
    def _setup_alert_handlers(self) -> None:
        """
        Set up alert handlers based on configuration.
        """
        # Default handler to log alerts
        self.add_alert_handler(self._log_alert_handler)
        
        # Add handlers from config
        handlers = self.config.get("alert_handlers", [])
        
        for handler_config in handlers:
            handler_type = handler_config.get("type")
            
            if handler_type == "log":
                # Already added default log handler
                pass
            elif handler_type == "file":
                log_file = handler_config.get("file", "alerts.log")
                self.add_alert_handler(partial(self._file_alert_handler, log_file=log_file))
            elif handler_type == "callback":
                callback_name = handler_config.get("callback")
                if callback_name and callback_name in globals():
                    self.add_alert_handler(globals()[callback_name])
    
    def _log_alert_handler(self, alert: MonitoringAlert) -> None:
        """
        Default alert handler that logs alerts.
        
        Args:
            alert: The alert to handle
        """
        if alert.severity == AlertSeverity.CRITICAL:
            logger.critical(f"ALERT: {alert.title} - {alert.description}")
        elif alert.severity == AlertSeverity.ERROR:
            logger.error(f"ALERT: {alert.title} - {alert.description}")
        elif alert.severity == AlertSeverity.WARNING:
            logger.warning(f"ALERT: {alert.title} - {alert.description}")
        else:
            logger.info(f"ALERT: {alert.title} - {alert.description}")
    
    def _file_alert_handler(self, alert: MonitoringAlert, log_file: str) -> None:
        """
        Alert handler that logs alerts to a file.
        
        Args:
            alert: The alert to handle
            log_file: Path to the log file
        """
        try:
            with open(log_file, 'a') as f:
                alert_dict = alert.to_dict()
                f.write(json.dumps(alert_dict) + '\n')
        except Exception as e:
            logger.error(f"Error writing alert to file {log_file}: {e}")
    
    def add_monitor(self, monitor: BaseMonitor) -> bool:
        """
        Add a monitor to the system.
        
        Args:
            monitor: Monitor to add
            
        Returns:
            True if the monitor was successfully added
        """
        with self._lock:
            if monitor.name in self.monitors:
                logger.warning(f"Monitor with name '{monitor.name}' already exists")
                return False
            
            # Set alert handler to route alerts through system monitor
            monitor.alert_handler = self._handle_alert
            
            # Add monitor
            self.monitors[monitor.name] = monitor
            
            # Start monitor if system is running
            if self.running:
                monitor.start()
            
            logger.info(f"Added monitor: {monitor.name}")
            return True
    
    def remove_monitor(self, name: str) -> bool:
        """
        Remove a monitor from the system.
        
        Args:
            name: Name of the monitor to remove
            
        Returns:
            True if the monitor was successfully removed
        """
        with self._lock:
            if name not in self.monitors:
                logger.warning(f"Monitor '{name}' not found")
                return False
            
            # Stop monitor if it's running
            monitor = self.monitors[name]
            if monitor.running:
                monitor.stop()
            
            # Remove monitor
            del self.monitors[name]
            
            logger.info(f"Removed monitor: {name}")
            return True
    
    def get_monitor(self, name: str) -> Optional[BaseMonitor]:
        """
        Get a monitor by name.
        
        Args:
            name: Name of the monitor
            
        Returns:
            The monitor, or None if not found
        """
        with self._lock:
            return self.monitors.get(name)
    
    def start(self) -> bool:
        """
        Start all monitors.
        
        Returns:
            True if all monitors were successfully started
        """
        with self._lock:
            if self.running:
                logger.warning("System monitor is already running")
                return False
            
            self.running = True
            
            # Start all monitors
            success = True
            for name, monitor in self.monitors.items():
                try:
                    if not monitor.start():
                        logger.error(f"Failed to start monitor: {name}")
                        success = False
                except Exception as e:
                    logger.error(f"Error starting monitor {name}: {e}")
                    success = False
            
            logger.info(f"Started system monitor with {len(self.monitors)} monitors")
            return success
    
    def stop(self) -> bool:
        """
        Stop all monitors.
        
        Returns:
            True if all monitors were successfully stopped
        """
        with self._lock:
            if not self.running:
                logger.warning("System monitor is not running")
                return False
            
            self.running = False
            
            # Stop all monitors
            success = True
            for name, monitor in self.monitors.items():
                try:
                    if not monitor.stop():
                        logger.error(f"Failed to stop monitor: {name}")
                        success = False
                except Exception as e:
                    logger.error(f"Error stopping monitor {name}: {e}")
                    success = False
            
            logger.info("Stopped system monitor")
            return success
    
    def restart(self) -> bool:
        """
        Restart all monitors.
        
        Returns:
            True if all monitors were successfully restarted
        """
        with self._lock:
            self.stop()
            return self.start()
    
    def add_alert_handler(self, handler: Callable[[MonitoringAlert], None]) -> None:
        """
        Add an alert handler.
        
        Args:
            handler: Function that handles alerts
        """
        with self._lock:
            if handler not in self.alert_handlers:
                self.alert_handlers.append(handler)
    
    def remove_alert_handler(self, handler: Callable[[MonitoringAlert], None]) -> bool:
        """
        Remove an alert handler.
        
        Args:
            handler: Handler function to remove
            
        Returns:
            True if the handler was successfully removed
        """
        with self._lock:
            if handler in self.alert_handlers:
                self.alert_handlers.remove(handler)
                return True
            return False
    
    def _handle_alert(self, alert: MonitoringAlert) -> None:
        """
        Handle an alert from a monitor.
        
        Args:
            alert: The alert to handle
        """
        with self._lock:
            # Call all alert handlers
            for handler in self.alert_handlers:
                try:
                    handler(alert)
                except Exception as e:
                    logger.error(f"Error in alert handler: {e}")
    
    def get_state(self, name: str) -> Optional[MonitoringState]:
        """
        Get the current state of a monitored value.
        
        Args:
            name: Name of the monitored value
            
        Returns:
            The current state, or None if not found
        """
        with self._lock:
            # Check cache first
            if name in self.state_cache:
                return self.state_cache[name]
            
            # Search in all monitors
            for monitor in self.monitors.values():
                state = monitor.get_state(name)
                if state:
                    # Cache state for future lookups
                    self.state_cache[name] = state
                    return state
            
            return None
    
    def get_all_states(self) -> Dict[str, MonitoringState]:
        """
        Get all current monitoring states.
        
        Returns:
            Dictionary of state name -> MonitoringState
        """
        all_states = {}
        
        with self._lock:
            # Collect states from all monitors
            for monitor in self.monitors.values():
                monitor_states = monitor.get_all_states()
                all_states.update(monitor_states)
            
            # Update cache
            self.state_cache = all_states.copy()
        
        return all_states
    
    def get_recent_alerts(self, max_count: int = 10, min_severity: AlertSeverity = None, 
                         monitor_name: str = None) -> List[MonitoringAlert]:
        """
        Get recent alerts from all monitors.
        
        Args:
            max_count: Maximum number of alerts to return
            min_severity: Minimum severity level to include
            monitor_name: Optional name of specific monitor to get alerts from
            
        Returns:
            List of recent alerts
        """
        all_alerts = []
        
        with self._lock:
            if monitor_name:
                # Get alerts from specific monitor
                monitor = self.monitors.get(monitor_name)
                if monitor:
                    all_alerts.extend(monitor.get_recent_alerts(max_count=100, min_severity=min_severity))
            else:
                # Get alerts from all monitors
                for monitor in self.monitors.values():
                    all_alerts.extend(monitor.get_recent_alerts(max_count=100, min_severity=min_severity))
        
        # Filter by severity if needed
        if min_severity:
            all_alerts = [a for a in all_alerts if a.severity.value >= min_severity.value]
            
        # Sort by timestamp (most recent first) and limit count
        return sorted(all_alerts, key=lambda a: a.timestamp, reverse=True)[:max_count]
    
    def get_status(self) -> Dict[str, Any]:
        """
        Get the current status of the monitoring system.
        
        Returns:
            Dictionary with monitoring status information
        """
        with self._lock:
            # Get overall system status
            status = {
                "running": self.running,
                "monitor_count": len(self.monitors),
                "state_count": len(self.get_all_states()),
                "monitors": {}
            }
            
            # Add status for each monitor
            for name, monitor in self.monitors.items():
                status["monitors"][name] = monitor.get_status()
            
            return status
    
    def get_resource_usage(self) -> Dict[str, float]:
        """
        Get current resource usage metrics.
        
        Returns:
            Dictionary of resource type -> usage level (0.0-1.0)
        """
        resource_monitor = self.get_monitor("resource_monitor")
        if not resource_monitor:
            return {}
            
        # Get resource usage for common resource types
        resources = {}
        for resource_type in ["cpu", "memory", "disk", "network", "io", "gpu"]:
            usage = resource_monitor.get_resource_usage(resource_type)
            if usage is not None:
                resources[resource_type] = usage
                
        return resources
    
    def get_anomaly_scores(self) -> Dict[str, float]:
        """
        Get current anomaly scores for all metrics.
        
        Returns:
            Dictionary mapping metric to anomaly score
        """
        anomaly_detector = self.get_monitor("anomaly_detector")
        if not anomaly_detector:
            return {}
            
        return anomaly_detector.get_anomaly_scores()
    
    def get_current_state(self) -> Dict[str, Any]:
        """
        Get the current state of the system for safety verification.
        
        Returns:
            Dictionary with current system state information
        """
        # Get all monitoring states
        states = self.get_all_states()
        
        # Extract values into a single state dictionary
        state = {}
        
        # Add resource usage
        resource_usage = self.get_resource_usage()
        for resource_type, usage in resource_usage.items():
            state[f"{resource_type}_usage"] = usage
        
        # Add anomaly scores
        anomaly_scores = self.get_anomaly_scores()
        for metric, score in anomaly_scores.items():
            state[f"anomaly_score_{metric}"] = score
        
        # Add important state values
        for name, monitor_state in states.items():
            # Skip internal states
            if name.startswith("_"):
                continue
                
            # Add value to state dictionary
            state[name] = monitor_state.value
        
        return state
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the monitoring system to a dictionary.
        
        Returns:
            Dictionary representation of the monitoring system
        """
        with self._lock:
            return {
                "running": self.running,
                "monitors": {name: monitor.to_dict() for name, monitor in self.monitors.items()},
                "status": self.get_status()
            }


# Utility functions for monitoring

def create_resource_monitor(**kwargs) -> ResourceMonitor:
    """
    Create and configure a resource monitor.
    
    Args:
        **kwargs: Arguments to pass to ResourceMonitor constructor
        
    Returns:
        Configured ResourceMonitor instance
    """
    return ResourceMonitor(**kwargs)

def create_behavior_monitor(**kwargs) -> BehaviorMonitor:
    """
    Create and configure a behavior monitor.
    
    Args:
        **kwargs: Arguments to pass to BehaviorMonitor constructor
        
    Returns:
        Configured BehaviorMonitor instance
    """
    return BehaviorMonitor(**kwargs)

def create_activity_tracker(**kwargs) -> ActivityTracker:
    """
    Create and configure an activity tracker.
    
    Args:
        **kwargs: Arguments to pass to ActivityTracker constructor
        
    Returns:
        Configured ActivityTracker instance
    """
    return ActivityTracker(**kwargs)

def create_anomaly_detector(**kwargs) -> AnomalyDetector:
    """
    Create and configure an anomaly detector.
    
    Args:
        **kwargs: Arguments to pass to AnomalyDetector constructor
        
    Returns:
        Configured AnomalyDetector instance
    """
    return AnomalyDetector(**kwargs)

def create_system_monitor(config: Dict[str, Any] = None) -> SystemMonitor:
    """
    Create and configure a system monitor with standard monitors.
    
    Args:
        config: Optional configuration dictionary
        
    Returns:
        Configured SystemMonitor instance with standard monitors
    """
    system_monitor = SystemMonitor(config)
    
    # Add standard monitors
    system_monitor.add_monitor(create_resource_monitor())
    system_monitor.add_monitor(create_behavior_monitor())
    system_monitor.add_monitor(create_activity_tracker())
    system_monitor.add_monitor(create_anomaly_detector())
    
    return system_monitor

def monitor_resource_usage(**kwargs) -> Dict[str, float]:
    """
    Get current resource usage.
    
    Args:
        **kwargs: Additional arguments for resource monitoring
        
    Returns:
        Dictionary of resource type -> usage level (0.0-1.0)
    """
    monitor = ResourceMonitor(**kwargs)
    monitor.check()  # Perform a single check
    
    # Get resource usage for common resource types
    resources = {}
    for resource_type in ["cpu", "memory", "disk", "network", "io", "gpu"]:
        usage = monitor.get_resource_usage(resource_type)
        if usage is not None:
            resources[resource_type] = usage
            
    return resources

def detect_anomalies(metrics: Dict[str, List[float]], **kwargs) -> Dict[str, float]:
    """
    Detect anomalies in a set of metrics.
    
    Args:
        metrics: Dictionary of metric name -> list of values
        **kwargs: Additional arguments for anomaly detection
        
    Returns:
        Dictionary of metric name -> anomaly score
    """
    detector = AnomalyDetector(**kwargs)
    
    # Add metric values
    for metric_name, values in metrics.items():
        for value in values:
            detector.add_metric_value(metric_name, value)
    
    # Perform anomaly detection
    detector.check()
    
    # Return anomaly scores
    return detector.get_anomaly_scores()

def create_monitoring_alert(severity: AlertSeverity, title: str, description: str, 
                          source: str = "system", data: Dict[str, Any] = None,
                          tags: List[str] = None) -> MonitoringAlert:
    """
    Create a monitoring alert.
    
    Args:
        severity: Severity level of the alert
        title: Short title for the alert
        description: Detailed description of the alert
        source: Source of the alert
        data: Additional data related to the alert
        tags: Tags for categorizing the alert
        
    Returns:
        Created MonitoringAlert instance
    """
    return MonitoringAlert(
        id=str(uuid.uuid4()),
        timestamp=time.time(),
        source=source,
        severity=severity,
        title=title,
        description=description,
        data=data or {},
        tags=tags or []
    )