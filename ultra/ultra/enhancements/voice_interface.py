#!/usr/bin/env python3
"""
Voice Interface Manager
=======================

Provides speech-to-text and text-to-speech capabilities for ULTRA,
enabling voice-based interaction with the AGI system.
"""

import asyncio
import logging
import io
import wave
import numpy as np
from typing import Dict, List, Any, Optional, Union, Tuple
from dataclasses import dataclass
import tempfile
import os

logger = logging.getLogger(__name__)

# Try to import speech processing libraries
try:
    import speech_recognition as sr
    HAS_SPEECH_RECOGNITION = True
except ImportError:
    logger.warning("SpeechRecognition not found. Install with: pip install SpeechRecognition")
    HAS_SPEECH_RECOGNITION = False

try:
    import pyttsx3
    HAS_PYTTSX3 = True
except ImportError:
    logger.warning("pyttsx3 not found. Install with: pip install pyttsx3")
    HAS_PYTTSX3 = False

try:
    from transformers import pipeline
    import torch
    HAS_TRANSFORMERS = True
except ImportError:
    logger.warning("Transformers not available for advanced speech processing")
    HAS_TRANSFORMERS = False

try:
    import pyaudio
    HAS_PYAUDIO = True
except ImportError:
    logger.warning("PyAudio not found. Install with: pip install pyaudio")
    HAS_PYAUDIO = False

@dataclass
class VoiceConfig:
    """Configuration for voice interface"""
    
    # Speech-to-Text
    stt_engine: str = "google"  # google, whisper, sphinx
    stt_language: str = "en-US"
    stt_timeout: float = 5.0
    stt_phrase_timeout: float = 0.3
    
    # Text-to-Speech
    tts_engine: str = "pyttsx3"  # pyttsx3, espeak, festival
    tts_voice: str = "default"
    tts_rate: int = 200
    tts_volume: float = 0.9
    
    # Audio settings
    sample_rate: int = 16000
    chunk_size: int = 1024
    channels: int = 1
    audio_format: str = "wav"
    
    # Advanced models
    whisper_model: str = "base"
    use_gpu: bool = False

class VoiceInterfaceManager:
    """
    Manages voice input and output for ULTRA
    """
    
    def __init__(self, config):
        self.config = config
        self.voice_config = VoiceConfig()
        
        # Speech recognition
        self.recognizer = None
        self.microphone = None
        
        # Text-to-speech
        self.tts_engine = None
        
        # Advanced models
        self.whisper_pipeline = None
        self.tts_pipeline = None
        
        # Audio processing
        self.is_listening = False
        self.audio_buffer = []
        
        logger.info("Voice Interface Manager initialized")
    
    async def initialize(self):
        """Initialize voice interface components"""
        try:
            # Initialize speech recognition
            if HAS_SPEECH_RECOGNITION:
                self.recognizer = sr.Recognizer()
                
                # Adjust for ambient noise
                if HAS_PYAUDIO:
                    self.microphone = sr.Microphone()
                    with self.microphone as source:
                        logger.info("Calibrating microphone for ambient noise...")
                        self.recognizer.adjust_for_ambient_noise(source, duration=1)
                
                logger.info("✅ Speech recognition initialized")
            
            # Initialize text-to-speech
            if HAS_PYTTSX3:
                self.tts_engine = pyttsx3.init()
                
                # Configure TTS settings
                self.tts_engine.setProperty('rate', self.voice_config.tts_rate)
                self.tts_engine.setProperty('volume', self.voice_config.tts_volume)
                
                # Set voice if available
                voices = self.tts_engine.getProperty('voices')
                if voices:
                    # Try to find a good voice
                    for voice in voices:
                        if 'english' in voice.name.lower() or 'en' in voice.id.lower():
                            self.tts_engine.setProperty('voice', voice.id)
                            break
                
                logger.info("✅ Text-to-speech initialized")
            
            # Initialize advanced models if available
            if HAS_TRANSFORMERS and self.config.enable_voice_input:
                try:
                    self.whisper_pipeline = pipeline(
                        "automatic-speech-recognition",
                        model=f"openai/whisper-{self.voice_config.whisper_model}",
                        device=0 if self.voice_config.use_gpu and torch.cuda.is_available() else -1
                    )
                    logger.info("✅ Whisper ASR model loaded")
                except Exception as e:
                    logger.warning(f"Failed to load Whisper model: {e}")
            
            logger.info("✅ Voice Interface ready")
            
        except Exception as e:
            logger.error(f"Voice interface initialization failed: {e}")
    
    async def speech_to_text(self, audio_data: Union[bytes, str, None] = None) -> str:
        """
        Convert speech to text
        
        Args:
            audio_data: Audio data as bytes, file path, or None for live recording
            
        Returns:
            Transcribed text
        """
        try:
            if audio_data is None:
                # Record from microphone
                audio_data = await self._record_audio()
            
            if isinstance(audio_data, str):
                # File path provided
                return await self._transcribe_file(audio_data)
            elif isinstance(audio_data, bytes):
                # Raw audio bytes
                return await self._transcribe_bytes(audio_data)
            else:
                logger.error("Invalid audio data format")
                return ""
                
        except Exception as e:
            logger.error(f"Speech-to-text failed: {e}")
            return ""
    
    async def text_to_speech(self, text: str, output_file: Optional[str] = None) -> bytes:
        """
        Convert text to speech
        
        Args:
            text: Text to convert
            output_file: Optional file to save audio
            
        Returns:
            Audio data as bytes
        """
        try:
            if not text.strip():
                return b""
            
            # Clean text for TTS
            clean_text = self._clean_text_for_tts(text)
            
            if self.tts_engine:
                # Use pyttsx3 for TTS
                if output_file:
                    self.tts_engine.save_to_file(clean_text, output_file)
                    self.tts_engine.runAndWait()
                    
                    # Read the file and return bytes
                    with open(output_file, 'rb') as f:
                        return f.read()
                else:
                    # Generate to temporary file
                    with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as tmp_file:
                        self.tts_engine.save_to_file(clean_text, tmp_file.name)
                        self.tts_engine.runAndWait()
                        
                        with open(tmp_file.name, 'rb') as f:
                            audio_data = f.read()
                        
                        os.unlink(tmp_file.name)
                        return audio_data
            else:
                logger.warning("TTS engine not available")
                return b""
                
        except Exception as e:
            logger.error(f"Text-to-speech failed: {e}")
            return b""
    
    async def start_listening(self) -> bool:
        """Start continuous listening mode"""
        if not self.microphone or not self.recognizer:
            logger.error("Microphone not available")
            return False
        
        try:
            self.is_listening = True
            logger.info("🎤 Started listening...")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start listening: {e}")
            return False
    
    async def stop_listening(self):
        """Stop continuous listening mode"""
        self.is_listening = False
        logger.info("🔇 Stopped listening")
    
    async def _record_audio(self, duration: float = 5.0) -> bytes:
        """Record audio from microphone"""
        if not self.microphone or not self.recognizer:
            raise ValueError("Microphone not available")
        
        try:
            logger.info("🎤 Recording audio...")
            
            with self.microphone as source:
                # Listen for audio
                audio = self.recognizer.listen(
                    source, 
                    timeout=self.voice_config.stt_timeout,
                    phrase_time_limit=duration
                )
            
            # Convert to bytes
            return audio.get_wav_data()
            
        except sr.WaitTimeoutError:
            logger.warning("Recording timeout")
            return b""
        except Exception as e:
            logger.error(f"Recording failed: {e}")
            return b""
    
    async def _transcribe_bytes(self, audio_bytes: bytes) -> str:
        """Transcribe audio from bytes"""
        try:
            # Create AudioData object
            audio_data = sr.AudioData(audio_bytes, self.voice_config.sample_rate, 2)
            
            # Try Whisper first if available
            if self.whisper_pipeline:
                # Save to temporary file for Whisper
                with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as tmp_file:
                    tmp_file.write(audio_bytes)
                    tmp_file.flush()
                    
                    result = self.whisper_pipeline(tmp_file.name)
                    os.unlink(tmp_file.name)
                    
                    return result.get('text', '').strip()
            
            # Fallback to Google Speech Recognition
            elif self.recognizer:
                if self.voice_config.stt_engine == "google":
                    return self.recognizer.recognize_google(
                        audio_data, 
                        language=self.voice_config.stt_language
                    )
                elif self.voice_config.stt_engine == "sphinx":
                    return self.recognizer.recognize_sphinx(audio_data)
            
            return ""
            
        except sr.UnknownValueError:
            logger.warning("Could not understand audio")
            return ""
        except sr.RequestError as e:
            logger.error(f"Speech recognition service error: {e}")
            return ""
        except Exception as e:
            logger.error(f"Transcription failed: {e}")
            return ""
    
    async def _transcribe_file(self, file_path: str) -> str:
        """Transcribe audio from file"""
        try:
            if self.whisper_pipeline:
                # Use Whisper for file transcription
                result = self.whisper_pipeline(file_path)
                return result.get('text', '').strip()
            
            elif self.recognizer:
                # Use speech_recognition library
                with sr.AudioFile(file_path) as source:
                    audio = self.recognizer.record(source)
                
                return self.recognizer.recognize_google(
                    audio, 
                    language=self.voice_config.stt_language
                )
            
            return ""
            
        except Exception as e:
            logger.error(f"File transcription failed: {e}")
            return ""
    
    def _clean_text_for_tts(self, text: str) -> str:
        """Clean text for better TTS output"""
        # Remove markdown formatting
        text = text.replace('**', '').replace('*', '')
        text = text.replace('##', '').replace('#', '')
        
        # Remove emojis and special characters that might cause issues
        import re
        text = re.sub(r'[🤖🧠✨🔗🔄💭🎯🌟👁️📚🔬⚛️🎤🔇]', '', text)
        
        # Replace technical terms with pronounceable versions
        replacements = {
            'ULTRA': 'Ultra',
            'AGI': 'A G I',
            'API': 'A P I',
            'GPU': 'G P U',
            'CPU': 'C P U',
            'AI': 'A I'
        }
        
        for old, new in replacements.items():
            text = text.replace(old, new)
        
        # Limit length for TTS
        if len(text) > 500:
            text = text[:500] + "..."
        
        return text.strip()
    
    def get_voice_status(self) -> Dict[str, bool]:
        """Get status of voice interface components"""
        return {
            "speech_recognition": self.recognizer is not None,
            "text_to_speech": self.tts_engine is not None,
            "microphone": self.microphone is not None,
            "whisper_model": self.whisper_pipeline is not None,
            "is_listening": self.is_listening
        }

# Export main class
__all__ = ['VoiceInterfaceManager', 'VoiceConfig']
