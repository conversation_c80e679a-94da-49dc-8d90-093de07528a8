#!/usr/bin/env python3
"""
Internet Training System
========================

Provides capabilities for training ULTRA with knowledge from internet sources
including Wikipedia, scientific papers, news articles, and other web content.
"""

import asyncio
import aiohttp
import logging
import json
import time
import hashlib
from typing import Dict, List, Any, Optional, Union, Tuple
from dataclasses import dataclass
import re
from urllib.parse import urljoin, urlparse
import xml.etree.ElementTree as ET

logger = logging.getLogger(__name__)

# Try to import optional dependencies
try:
    from bs4 import BeautifulSoup
    HAS_BEAUTIFULSOUP = True
except ImportError:
    logger.warning("BeautifulSoup not found. Install with: pip install beautifulsoup4")
    HAS_BEAUTIFULSOUP = False

try:
    import feedparser
    HAS_FEEDPARSER = True
except ImportError:
    logger.warning("Feedparser not found. Install with: pip install feedparser")
    HAS_FEEDPARSER = False

try:
    import nltk
    from nltk.tokenize import sent_tokenize, word_tokenize
    from nltk.corpus import stopwords
    HAS_NLTK = True
except ImportError:
    logger.warning("NLTK not found. Install with: pip install nltk")
    HAS_NLTK = False

@dataclass
class TrainingSource:
    """Configuration for a training data source"""
    name: str
    url: str
    source_type: str  # wikipedia, arxiv, rss, web
    rate_limit: float = 1.0
    max_articles: int = 100
    enabled: bool = True
    api_key: Optional[str] = None

@dataclass
class TrainingConfig:
    """Configuration for internet training"""
    max_articles_per_source: int = 100
    max_content_length: int = 5000
    min_content_length: int = 100
    rate_limit_default: float = 1.0
    concurrent_requests: int = 5
    timeout: float = 30.0
    
    # Content filtering
    filter_languages: List[str] = None
    exclude_domains: List[str] = None
    quality_threshold: float = 0.5
    
    def __post_init__(self):
        if self.filter_languages is None:
            self.filter_languages = ['en']
        if self.exclude_domains is None:
            self.exclude_domains = ['spam.com', 'ads.com']

class InternetTrainingSystem:
    """
    Manages training ULTRA with knowledge from internet sources
    """
    
    def __init__(self, config):
        self.config = config
        self.training_config = TrainingConfig()
        self.session = None
        self.training_data = {}
        self.processed_urls = set()
        
        # Configure training sources
        self.sources = {
            'wikipedia': TrainingSource(
                name='Wikipedia',
                url='https://en.wikipedia.org/api/rest_v1',
                source_type='wikipedia',
                rate_limit=0.5,
                max_articles=200
            ),
            'arxiv': TrainingSource(
                name='ArXiv',
                url='http://export.arxiv.org/api/query',
                source_type='arxiv',
                rate_limit=3.0,
                max_articles=50
            ),
            'news_feeds': TrainingSource(
                name='Science News',
                url='https://feeds.sciencedaily.com/sciencedaily/top_news',
                source_type='rss',
                rate_limit=2.0,
                max_articles=100
            ),
            'pubmed': TrainingSource(
                name='PubMed',
                url='https://eutils.ncbi.nlm.nih.gov/entrez/eutils',
                source_type='pubmed',
                rate_limit=0.34,
                max_articles=50
            )
        }
        
        self.last_request_time = {}
        
        logger.info("Internet Training System initialized")
    
    async def initialize(self):
        """Initialize the training system"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.training_config.timeout),
            headers={'User-Agent': 'ULTRA-AGI-Training/1.0 (Educational Research)'}
        )
        
        # Download NLTK data if available
        if HAS_NLTK:
            try:
                nltk.download('punkt', quiet=True)
                nltk.download('stopwords', quiet=True)
            except:
                pass
        
        logger.info("✅ Internet Training System ready")
    
    async def train_on_topics(self, topics: List[str]) -> Dict[str, Any]:
        """
        Train ULTRA on specific topics by gathering internet knowledge
        
        Args:
            topics: List of topics to gather knowledge about
            
        Returns:
            Training summary with statistics
        """
        training_summary = {
            "topics": topics,
            "sources_used": [],
            "articles_collected": 0,
            "total_content_length": 0,
            "training_time": 0,
            "knowledge_areas": {},
            "errors": []
        }
        
        start_time = time.time()
        
        try:
            logger.info(f"🌐 Starting internet training on topics: {topics}")
            
            # Gather knowledge from all sources
            for topic in topics:
                logger.info(f"📚 Gathering knowledge on: {topic}")
                
                topic_knowledge = await self._gather_topic_knowledge(topic)
                
                if topic_knowledge:
                    training_summary["knowledge_areas"][topic] = {
                        "articles": len(topic_knowledge),
                        "sources": list(set([article.get("source", "unknown") for article in topic_knowledge]))
                    }
                    
                    # Process and integrate knowledge
                    processed_knowledge = await self._process_knowledge(topic_knowledge)
                    self.training_data[topic] = processed_knowledge
                    
                    training_summary["articles_collected"] += len(topic_knowledge)
                    training_summary["total_content_length"] += sum(
                        len(article.get("content", "")) for article in topic_knowledge
                    )
            
            # Update training summary
            training_summary["sources_used"] = list(self.sources.keys())
            training_summary["training_time"] = time.time() - start_time
            
            logger.info(f"✅ Internet training completed: {training_summary['articles_collected']} articles collected")
            
            return training_summary
            
        except Exception as e:
            logger.error(f"Internet training failed: {e}")
            training_summary["errors"].append(str(e))
            return training_summary
    
    async def _gather_topic_knowledge(self, topic: str) -> List[Dict[str, Any]]:
        """Gather knowledge about a specific topic from all sources"""
        knowledge = []
        
        # Gather from each source concurrently
        tasks = []
        
        for source_name, source in self.sources.items():
            if source.enabled:
                if source.source_type == 'wikipedia':
                    tasks.append(self._gather_wikipedia_knowledge(topic, source))
                elif source.source_type == 'arxiv':
                    tasks.append(self._gather_arxiv_knowledge(topic, source))
                elif source.source_type == 'rss':
                    tasks.append(self._gather_rss_knowledge(topic, source))
                elif source.source_type == 'pubmed':
                    tasks.append(self._gather_pubmed_knowledge(topic, source))
        
        # Execute tasks with concurrency limit
        semaphore = asyncio.Semaphore(self.training_config.concurrent_requests)
        
        async def limited_task(task):
            async with semaphore:
                return await task
        
        results = await asyncio.gather(
            *[limited_task(task) for task in tasks],
            return_exceptions=True
        )
        
        # Collect results
        for result in results:
            if isinstance(result, Exception):
                logger.warning(f"Knowledge gathering task failed: {result}")
            elif result:
                knowledge.extend(result)
        
        return knowledge
    
    async def _gather_wikipedia_knowledge(self, topic: str, source: TrainingSource) -> List[Dict[str, Any]]:
        """Gather knowledge from Wikipedia"""
        try:
            await self._respect_rate_limit(source.name)
            
            # Search for articles
            search_url = f"https://en.wikipedia.org/api/rest_v1/page/summary/{topic.replace(' ', '_')}"
            
            async with self.session.get(search_url) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    if 'extract' in data and len(data['extract']) > self.training_config.min_content_length:
                        return [{
                            "title": data.get('title', topic),
                            "content": data['extract'],
                            "url": data.get('content_urls', {}).get('desktop', {}).get('page', ''),
                            "source": "Wikipedia",
                            "topic": topic,
                            "type": "encyclopedia"
                        }]
            
        except Exception as e:
            logger.warning(f"Wikipedia knowledge gathering failed for {topic}: {e}")
        
        return []
    
    async def _gather_arxiv_knowledge(self, topic: str, source: TrainingSource) -> List[Dict[str, Any]]:
        """Gather knowledge from ArXiv"""
        try:
            await self._respect_rate_limit(source.name)
            
            # Search ArXiv
            params = {
                'search_query': f'all:{topic}',
                'start': 0,
                'max_results': min(source.max_articles, 10),
                'sortBy': 'relevance'
            }
            
            url = f"{source.url}?" + "&".join([f"{k}={v}" for k, v in params.items()])
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    content = await response.text()
                    return self._parse_arxiv_response(content, topic)
            
        except Exception as e:
            logger.warning(f"ArXiv knowledge gathering failed for {topic}: {e}")
        
        return []
    
    async def _gather_rss_knowledge(self, topic: str, source: TrainingSource) -> List[Dict[str, Any]]:
        """Gather knowledge from RSS feeds"""
        try:
            await self._respect_rate_limit(source.name)
            
            if not HAS_FEEDPARSER:
                return []
            
            # Fetch RSS feed
            async with self.session.get(source.url) as response:
                if response.status == 200:
                    content = await response.text()
                    
                    # Parse RSS feed
                    feed = feedparser.parse(content)
                    
                    articles = []
                    for entry in feed.entries[:source.max_articles]:
                        # Check if entry is relevant to topic
                        if self._is_relevant_to_topic(entry, topic):
                            article_content = await self._extract_article_content(entry.link)
                            
                            if article_content:
                                articles.append({
                                    "title": entry.title,
                                    "content": article_content,
                                    "url": entry.link,
                                    "source": source.name,
                                    "topic": topic,
                                    "type": "news",
                                    "published": getattr(entry, 'published', '')
                                })
                    
                    return articles
            
        except Exception as e:
            logger.warning(f"RSS knowledge gathering failed for {topic}: {e}")
        
        return []
    
    async def _gather_pubmed_knowledge(self, topic: str, source: TrainingSource) -> List[Dict[str, Any]]:
        """Gather knowledge from PubMed"""
        try:
            await self._respect_rate_limit(source.name)
            
            # Search PubMed
            search_url = f"{source.url}/esearch.fcgi"
            params = {
                'db': 'pubmed',
                'term': topic,
                'retmax': min(source.max_articles, 5),
                'retmode': 'json'
            }
            
            async with self.session.get(search_url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    if 'esearchresult' in data and 'idlist' in data['esearchresult']:
                        ids = data['esearchresult']['idlist']
                        
                        # Get abstracts for papers
                        articles = []
                        for paper_id in ids[:3]:  # Limit to avoid rate limits
                            abstract = await self._get_pubmed_abstract(paper_id, source)
                            if abstract:
                                articles.append(abstract)
                        
                        return articles
            
        except Exception as e:
            logger.warning(f"PubMed knowledge gathering failed for {topic}: {e}")
        
        return []
    
    async def _process_knowledge(self, knowledge_articles: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Process and structure gathered knowledge"""
        processed = {
            "articles": knowledge_articles,
            "summary": "",
            "key_concepts": [],
            "sources": list(set([article.get("source", "") for article in knowledge_articles])),
            "total_length": sum(len(article.get("content", "")) for article in knowledge_articles)
        }
        
        # Extract key concepts if NLTK is available
        if HAS_NLTK and knowledge_articles:
            all_text = " ".join([article.get("content", "") for article in knowledge_articles])
            processed["key_concepts"] = self._extract_key_concepts(all_text)
        
        # Generate summary
        if knowledge_articles:
            processed["summary"] = self._generate_knowledge_summary(knowledge_articles)
        
        return processed
    
    def _parse_arxiv_response(self, xml_content: str, topic: str) -> List[Dict[str, Any]]:
        """Parse ArXiv XML response"""
        articles = []
        
        try:
            # Simple XML parsing for ArXiv
            if '<entry>' in xml_content:
                entries = xml_content.split('<entry>')[1:]
                
                for entry in entries[:3]:  # Limit results
                    title_match = re.search(r'<title>(.*?)</title>', entry, re.DOTALL)
                    summary_match = re.search(r'<summary>(.*?)</summary>', entry, re.DOTALL)
                    
                    if title_match and summary_match:
                        title = title_match.group(1).strip()
                        summary = summary_match.group(1).strip()
                        
                        if len(summary) > self.training_config.min_content_length:
                            articles.append({
                                "title": title,
                                "content": summary,
                                "source": "ArXiv",
                                "topic": topic,
                                "type": "scientific_paper"
                            })
        
        except Exception as e:
            logger.warning(f"ArXiv response parsing failed: {e}")
        
        return articles
    
    def _is_relevant_to_topic(self, entry, topic: str) -> bool:
        """Check if RSS entry is relevant to topic"""
        topic_words = topic.lower().split()
        entry_text = f"{entry.title} {getattr(entry, 'summary', '')}".lower()
        
        # Simple relevance check
        return any(word in entry_text for word in topic_words)
    
    async def _extract_article_content(self, url: str) -> str:
        """Extract main content from article URL"""
        if not HAS_BEAUTIFULSOUP:
            return ""
        
        try:
            async with self.session.get(url) as response:
                if response.status == 200:
                    html = await response.text()
                    soup = BeautifulSoup(html, 'html.parser')
                    
                    # Remove scripts and styles
                    for script in soup(["script", "style"]):
                        script.extract()
                    
                    # Get text content
                    text = soup.get_text()
                    lines = (line.strip() for line in text.splitlines())
                    chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
                    text = ' '.join(chunk for chunk in chunks if chunk)
                    
                    # Limit length
                    return text[:self.training_config.max_content_length]
        
        except Exception as e:
            logger.warning(f"Content extraction failed for {url}: {e}")
        
        return ""
    
    async def _respect_rate_limit(self, source_name: str):
        """Respect rate limits for sources"""
        if source_name in self.last_request_time:
            elapsed = time.time() - self.last_request_time[source_name]
            rate_limit = self.sources[source_name].rate_limit
            
            if elapsed < rate_limit:
                await asyncio.sleep(rate_limit - elapsed)
        
        self.last_request_time[source_name] = time.time()
    
    def _extract_key_concepts(self, text: str) -> List[str]:
        """Extract key concepts from text using NLTK"""
        try:
            # Tokenize and remove stopwords
            words = word_tokenize(text.lower())
            stop_words = set(stopwords.words('english'))
            
            # Filter words
            filtered_words = [
                word for word in words 
                if word.isalpha() and len(word) > 3 and word not in stop_words
            ]
            
            # Get most frequent words as key concepts
            from collections import Counter
            word_freq = Counter(filtered_words)
            
            return [word for word, freq in word_freq.most_common(10)]
        
        except Exception as e:
            logger.warning(f"Key concept extraction failed: {e}")
            return []
    
    def _generate_knowledge_summary(self, articles: List[Dict[str, Any]]) -> str:
        """Generate a summary of gathered knowledge"""
        if not articles:
            return "No knowledge gathered"
        
        sources = list(set([article.get("source", "") for article in articles]))
        total_articles = len(articles)
        
        summary = f"Gathered {total_articles} articles from {len(sources)} sources: {', '.join(sources)}. "
        
        # Add content preview
        if articles:
            first_article = articles[0]
            content_preview = first_article.get("content", "")[:200]
            summary += f"Sample content: {content_preview}..."
        
        return summary
    
    async def close(self):
        """Close the training system"""
        if self.session:
            await self.session.close()
        logger.info("Internet Training System closed")
    
    def get_training_status(self) -> Dict[str, Any]:
        """Get training system status"""
        return {
            "sources_configured": len(self.sources),
            "sources_enabled": sum(1 for s in self.sources.values() if s.enabled),
            "topics_trained": len(self.training_data),
            "total_articles": sum(
                len(data.get("articles", [])) for data in self.training_data.values()
            ),
            "dependencies": {
                "beautifulsoup": HAS_BEAUTIFULSOUP,
                "feedparser": HAS_FEEDPARSER,
                "nltk": HAS_NLTK
            }
        }

# Export main class
__all__ = ['InternetTrainingSystem', 'TrainingConfig', 'TrainingSource']
