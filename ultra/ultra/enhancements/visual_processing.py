#!/usr/bin/env python3
"""
Visual Processing Engine
========================

Provides image analysis, object detection, and visual understanding
capabilities for ULTRA AGI system.
"""

import asyncio
import logging
import io
import base64
from typing import Dict, List, Any, Optional, Union, Tuple
from dataclasses import dataclass
import numpy as np

logger = logging.getLogger(__name__)

# Try to import image processing libraries
try:
    from PIL import Image, ImageDraw, ImageFont
    HAS_PIL = True
except ImportError:
    logger.warning("PIL not found. Install with: pip install Pillow")
    HAS_PIL = False

try:
    import cv2
    HAS_OPENCV = True
except ImportError:
    logger.warning("OpenCV not found. Install with: pip install opencv-python")
    HAS_OPENCV = False

try:
    from transformers import pipeline, CLIPProcessor, CLIPModel
    import torch
    HAS_TRANSFORMERS = True
except ImportError:
    logger.warning("Transformers not available for advanced vision processing")
    HAS_TRANSFORMERS = False

try:
    import matplotlib.pyplot as plt
    import matplotlib.patches as patches
    HAS_MATPLOTLIB = True
except ImportError:
    logger.warning("Matplotlib not found. Install with: pip install matplotlib")
    HAS_MATPLOTLIB = False

@dataclass
class VisualConfig:
    """Configuration for visual processing"""
    
    # Image processing
    max_image_size: Tuple[int, int] = (512, 512)
    image_quality: int = 85
    supported_formats: List[str] = None
    
    # Object detection
    detection_threshold: float = 0.5
    max_detections: int = 10
    
    # Image captioning
    caption_max_length: int = 100
    caption_num_beams: int = 4
    
    # CLIP model
    clip_model: str = "openai/clip-vit-base-patch32"
    
    # Processing settings
    use_gpu: bool = False
    batch_size: int = 1
    
    def __post_init__(self):
        if self.supported_formats is None:
            self.supported_formats = ['jpg', 'jpeg', 'png', 'bmp', 'gif', 'tiff']

class VisualProcessingEngine:
    """
    Handles visual processing and image understanding for ULTRA
    """
    
    def __init__(self, config):
        self.config = config
        self.visual_config = VisualConfig()
        
        # Vision models
        self.clip_model = None
        self.clip_processor = None
        self.caption_pipeline = None
        self.object_detection_pipeline = None
        
        # Device configuration
        self.device = self._get_device()
        
        logger.info("Visual Processing Engine initialized")
    
    async def initialize(self):
        """Initialize visual processing models"""
        try:
            if HAS_TRANSFORMERS:
                # Initialize CLIP for image-text understanding
                logger.info("Loading CLIP model for image-text understanding...")
                self.clip_processor = CLIPProcessor.from_pretrained(
                    self.visual_config.clip_model
                )
                self.clip_model = CLIPModel.from_pretrained(
                    self.visual_config.clip_model
                ).to(self.device)
                
                # Initialize image captioning
                logger.info("Loading image captioning model...")
                self.caption_pipeline = pipeline(
                    "image-to-text",
                    model="nlpconnect/vit-gpt2-image-captioning",
                    device=0 if self.visual_config.use_gpu and torch.cuda.is_available() else -1
                )
                
                # Initialize object detection
                logger.info("Loading object detection model...")
                self.object_detection_pipeline = pipeline(
                    "object-detection",
                    model="facebook/detr-resnet-50",
                    device=0 if self.visual_config.use_gpu and torch.cuda.is_available() else -1
                )
                
                logger.info("✅ Visual processing models loaded")
            
            logger.info("✅ Visual Processing Engine ready")
            
        except Exception as e:
            logger.error(f"Visual processing initialization failed: {e}")
    
    async def analyze_image(self, image_input: Union[str, bytes, Image.Image]) -> Dict[str, Any]:
        """
        Comprehensive image analysis
        
        Args:
            image_input: Image as file path, bytes, or PIL Image
            
        Returns:
            Dictionary with analysis results
        """
        try:
            # Load and preprocess image
            image = await self._load_image(image_input)
            if image is None:
                return {"error": "Failed to load image"}
            
            analysis_results = {
                "image_info": await self._get_image_info(image),
                "description": "",
                "objects": [],
                "text_similarity": {},
                "visual_features": {}
            }
            
            # Generate image description
            if self.caption_pipeline:
                description = await self._generate_caption(image)
                analysis_results["description"] = description
            
            # Detect objects
            if self.object_detection_pipeline:
                objects = await self._detect_objects(image)
                analysis_results["objects"] = objects
            
            # Analyze with CLIP if available
            if self.clip_model and self.clip_processor:
                clip_analysis = await self._analyze_with_clip(image)
                analysis_results["visual_features"] = clip_analysis
            
            # Basic image analysis
            basic_analysis = await self._basic_image_analysis(image)
            analysis_results.update(basic_analysis)
            
            return analysis_results
            
        except Exception as e:
            logger.error(f"Image analysis failed: {e}")
            return {"error": str(e)}
    
    async def compare_images(self, image1: Union[str, bytes], image2: Union[str, bytes]) -> Dict[str, Any]:
        """Compare two images for similarity"""
        try:
            img1 = await self._load_image(image1)
            img2 = await self._load_image(image2)
            
            if img1 is None or img2 is None:
                return {"error": "Failed to load one or both images"}
            
            # Use CLIP for semantic similarity
            if self.clip_model and self.clip_processor:
                similarity = await self._compute_clip_similarity(img1, img2)
                return {
                    "semantic_similarity": similarity,
                    "comparison_method": "CLIP"
                }
            
            # Fallback to basic comparison
            basic_similarity = await self._basic_image_similarity(img1, img2)
            return {
                "basic_similarity": basic_similarity,
                "comparison_method": "histogram"
            }
            
        except Exception as e:
            logger.error(f"Image comparison failed: {e}")
            return {"error": str(e)}
    
    async def search_by_text(self, images: List[Union[str, bytes]], query: str) -> List[Dict[str, Any]]:
        """Search images by text description using CLIP"""
        if not self.clip_model or not self.clip_processor:
            return []
        
        try:
            results = []
            
            for i, image_input in enumerate(images):
                image = await self._load_image(image_input)
                if image is None:
                    continue
                
                # Compute similarity with text query
                similarity = await self._compute_text_image_similarity(image, query)
                
                results.append({
                    "image_index": i,
                    "similarity_score": similarity,
                    "query": query
                })
            
            # Sort by similarity
            results.sort(key=lambda x: x["similarity_score"], reverse=True)
            return results
            
        except Exception as e:
            logger.error(f"Text-based image search failed: {e}")
            return []
    
    async def _load_image(self, image_input: Union[str, bytes, Image.Image]) -> Optional[Image.Image]:
        """Load image from various input formats"""
        try:
            if isinstance(image_input, Image.Image):
                return image_input
            elif isinstance(image_input, str):
                # File path or base64
                if image_input.startswith('data:image'):
                    # Base64 encoded
                    header, data = image_input.split(',', 1)
                    image_bytes = base64.b64decode(data)
                    return Image.open(io.BytesIO(image_bytes))
                else:
                    # File path
                    return Image.open(image_input)
            elif isinstance(image_input, bytes):
                return Image.open(io.BytesIO(image_input))
            else:
                logger.error(f"Unsupported image input type: {type(image_input)}")
                return None
                
        except Exception as e:
            logger.error(f"Failed to load image: {e}")
            return None
    
    async def _get_image_info(self, image: Image.Image) -> Dict[str, Any]:
        """Get basic image information"""
        return {
            "size": image.size,
            "mode": image.mode,
            "format": getattr(image, 'format', 'Unknown'),
            "has_transparency": image.mode in ('RGBA', 'LA') or 'transparency' in image.info
        }
    
    async def _generate_caption(self, image: Image.Image) -> str:
        """Generate image caption"""
        try:
            # Resize image if too large
            if max(image.size) > 512:
                image = image.resize((512, 512), Image.Resampling.LANCZOS)
            
            result = self.caption_pipeline(image)
            
            if result and len(result) > 0:
                return result[0].get('generated_text', '').strip()
            
            return "Unable to generate caption"
            
        except Exception as e:
            logger.error(f"Caption generation failed: {e}")
            return "Caption generation failed"
    
    async def _detect_objects(self, image: Image.Image) -> List[Dict[str, Any]]:
        """Detect objects in image"""
        try:
            # Resize image if too large
            if max(image.size) > 800:
                image = image.resize((800, 600), Image.Resampling.LANCZOS)
            
            results = self.object_detection_pipeline(image)
            
            objects = []
            for detection in results[:self.visual_config.max_detections]:
                if detection['score'] >= self.visual_config.detection_threshold:
                    objects.append({
                        "label": detection['label'],
                        "confidence": detection['score'],
                        "box": detection['box']
                    })
            
            return objects
            
        except Exception as e:
            logger.error(f"Object detection failed: {e}")
            return []
    
    async def _analyze_with_clip(self, image: Image.Image) -> Dict[str, Any]:
        """Analyze image with CLIP model"""
        try:
            # Prepare image
            inputs = self.clip_processor(images=image, return_tensors="pt").to(self.device)
            
            # Get image features
            with torch.no_grad():
                image_features = self.clip_model.get_image_features(**inputs)
                image_features = image_features / image_features.norm(dim=-1, keepdim=True)
            
            # Test against common concepts
            concepts = [
                "a photo of a person", "a photo of an animal", "a photo of a building",
                "a photo of nature", "a photo of food", "a photo of a vehicle",
                "a photo of technology", "a photo of art", "indoor scene", "outdoor scene"
            ]
            
            concept_scores = {}
            for concept in concepts:
                similarity = await self._compute_text_image_similarity(image, concept)
                concept_scores[concept] = float(similarity)
            
            return {
                "concept_scores": concept_scores,
                "top_concept": max(concept_scores.items(), key=lambda x: x[1])
            }
            
        except Exception as e:
            logger.error(f"CLIP analysis failed: {e}")
            return {}
    
    async def _compute_text_image_similarity(self, image: Image.Image, text: str) -> float:
        """Compute similarity between image and text using CLIP"""
        try:
            inputs = self.clip_processor(
                text=[text], 
                images=image, 
                return_tensors="pt", 
                padding=True
            ).to(self.device)
            
            with torch.no_grad():
                outputs = self.clip_model(**inputs)
                logits_per_image = outputs.logits_per_image
                similarity = torch.softmax(logits_per_image, dim=1)[0, 0]
            
            return float(similarity)
            
        except Exception as e:
            logger.error(f"Text-image similarity computation failed: {e}")
            return 0.0
    
    async def _basic_image_analysis(self, image: Image.Image) -> Dict[str, Any]:
        """Basic image analysis without deep learning"""
        try:
            # Convert to numpy array
            img_array = np.array(image)
            
            analysis = {
                "brightness": float(np.mean(img_array)),
                "contrast": float(np.std(img_array)),
                "dominant_colors": [],
                "is_grayscale": len(img_array.shape) == 2 or (len(img_array.shape) == 3 and img_array.shape[2] == 1)
            }
            
            # Get dominant colors (simplified)
            if not analysis["is_grayscale"] and len(img_array.shape) == 3:
                # Reshape and get unique colors
                pixels = img_array.reshape(-1, img_array.shape[-1])
                unique_colors = np.unique(pixels, axis=0)
                
                # Take first few dominant colors
                analysis["dominant_colors"] = unique_colors[:5].tolist()
            
            return analysis
            
        except Exception as e:
            logger.error(f"Basic image analysis failed: {e}")
            return {}
    
    def _get_device(self) -> torch.device:
        """Get appropriate device for processing"""
        if torch.cuda.is_available() and self.visual_config.use_gpu:
            return torch.device('cuda')
        elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            return torch.device('mps')
        else:
            return torch.device('cpu')
    
    def get_visual_status(self) -> Dict[str, bool]:
        """Get status of visual processing components"""
        return {
            "clip_model": self.clip_model is not None,
            "caption_pipeline": self.caption_pipeline is not None,
            "object_detection": self.object_detection_pipeline is not None,
            "pil_available": HAS_PIL,
            "opencv_available": HAS_OPENCV,
            "transformers_available": HAS_TRANSFORMERS
        }

# Export main class
__all__ = ['VisualProcessingEngine', 'VisualConfig']
