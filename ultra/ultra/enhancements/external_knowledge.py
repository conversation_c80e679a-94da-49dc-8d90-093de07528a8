#!/usr/bin/env python3
"""
External Knowledge Manager
==========================

Integrates external knowledge sources including Wikipedia, scientific databases,
and other knowledge repositories into ULTRA's reasoning system.
"""

import asyncio
import aiohttp
import json
import logging
import time
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
import hashlib

logger = logging.getLogger(__name__)

# Try to import optional dependencies
try:
    import wikipedia
    HAS_WIKIPEDIA = True
except ImportError:
    logger.warning("Wikipedia package not found. Install with: pip install wikipedia")
    HAS_WIKIPEDIA = False

try:
    import requests
    HAS_REQUESTS = True
except ImportError:
    logger.warning("Requests package not found. Install with: pip install requests")
    HAS_REQUESTS = False

@dataclass
class KnowledgeSource:
    """Configuration for a knowledge source"""
    name: str
    url: str
    api_key: Optional[str] = None
    rate_limit: float = 1.0  # seconds between requests
    enabled: bool = True

class ExternalKnowledgeManager:
    """
    Manages integration with external knowledge sources
    """
    
    def __init__(self, config):
        self.config = config
        self.session = None
        self.cache = {}
        self.last_request_time = {}
        
        # Configure knowledge sources
        self.sources = {
            'wikipedia': KnowledgeSource(
                name='Wikipedia',
                url='https://en.wikipedia.org/api/rest_v1',
                rate_limit=0.5
            ),
            'arxiv': KnowledgeSource(
                name='ArXiv',
                url='http://export.arxiv.org/api/query',
                rate_limit=3.0
            ),
            'pubmed': KnowledgeSource(
                name='PubMed',
                url='https://eutils.ncbi.nlm.nih.gov/entrez/eutils',
                rate_limit=0.34  # Max 3 requests per second
            ),
            'crossref': KnowledgeSource(
                name='CrossRef',
                url='https://api.crossref.org/works',
                rate_limit=1.0
            )
        }
        
        logger.info("External Knowledge Manager initialized")
    
    async def initialize(self):
        """Initialize the knowledge manager"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={'User-Agent': 'ULTRA-AGI/1.0 (Educational Research)'}
        )
        logger.info("✅ External Knowledge Manager ready")
    
    async def get_relevant_knowledge(self, query: str) -> Dict[str, Any]:
        """
        Get relevant knowledge from external sources for a query
        """
        knowledge = {}
        
        try:
            # Check cache first
            cache_key = self._get_cache_key(query)
            if cache_key in self.cache:
                logger.debug(f"Cache hit for query: {query[:50]}...")
                return self.cache[cache_key]
            
            # Gather knowledge from different sources
            tasks = []
            
            if self.config.enable_wikipedia:
                tasks.append(self._get_wikipedia_knowledge(query))
            
            if self.config.enable_scientific_databases:
                tasks.append(self._get_scientific_knowledge(query))
            
            # Execute all tasks concurrently
            if tasks:
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        logger.warning(f"Knowledge source {i} failed: {result}")
                    elif result:
                        knowledge.update(result)
            
            # Cache the results
            self.cache[cache_key] = knowledge
            
            # Limit cache size
            if len(self.cache) > self.config.knowledge_cache_size:
                # Remove oldest entries
                oldest_keys = list(self.cache.keys())[:len(self.cache) // 2]
                for key in oldest_keys:
                    del self.cache[key]
            
            return knowledge
            
        except Exception as e:
            logger.error(f"Error getting external knowledge: {e}")
            return {}
    
    async def _get_wikipedia_knowledge(self, query: str) -> Dict[str, Any]:
        """Get knowledge from Wikipedia"""
        try:
            await self._respect_rate_limit('wikipedia')
            
            if HAS_WIKIPEDIA:
                # Use wikipedia library for better results
                try:
                    # Search for relevant pages
                    search_results = wikipedia.search(query, results=3)
                    
                    if search_results:
                        # Get summary of the most relevant page
                        page_title = search_results[0]
                        summary = wikipedia.summary(page_title, sentences=3)
                        
                        return {
                            'wikipedia': {
                                'title': page_title,
                                'summary': summary,
                                'url': wikipedia.page(page_title).url
                            }
                        }
                except wikipedia.exceptions.DisambiguationError as e:
                    # Handle disambiguation by taking the first option
                    summary = wikipedia.summary(e.options[0], sentences=3)
                    return {
                        'wikipedia': {
                            'title': e.options[0],
                            'summary': summary,
                            'url': wikipedia.page(e.options[0]).url
                        }
                    }
                except wikipedia.exceptions.PageError:
                    logger.warning(f"Wikipedia page not found for: {query}")
            
            else:
                # Fallback to direct API call
                search_url = f"https://en.wikipedia.org/api/rest_v1/page/summary/{query.replace(' ', '_')}"
                
                async with self.session.get(search_url) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {
                            'wikipedia': {
                                'title': data.get('title', ''),
                                'summary': data.get('extract', ''),
                                'url': data.get('content_urls', {}).get('desktop', {}).get('page', '')
                            }
                        }
            
        except Exception as e:
            logger.warning(f"Wikipedia knowledge fetch failed: {e}")
        
        return {}
    
    async def _get_scientific_knowledge(self, query: str) -> Dict[str, Any]:
        """Get knowledge from scientific databases"""
        scientific_knowledge = {}
        
        try:
            # Try ArXiv for scientific papers
            arxiv_results = await self._search_arxiv(query)
            if arxiv_results:
                scientific_knowledge['arxiv'] = arxiv_results
            
            # Try PubMed for medical/biological papers
            pubmed_results = await self._search_pubmed(query)
            if pubmed_results:
                scientific_knowledge['pubmed'] = pubmed_results
            
        except Exception as e:
            logger.warning(f"Scientific knowledge fetch failed: {e}")
        
        return {'scientific': scientific_knowledge} if scientific_knowledge else {}
    
    async def _search_arxiv(self, query: str) -> Optional[Dict[str, Any]]:
        """Search ArXiv for relevant papers"""
        try:
            await self._respect_rate_limit('arxiv')
            
            # ArXiv API query
            params = {
                'search_query': f'all:{query}',
                'start': 0,
                'max_results': 3,
                'sortBy': 'relevance',
                'sortOrder': 'descending'
            }
            
            url = f"{self.sources['arxiv'].url}?" + "&".join([f"{k}={v}" for k, v in params.items()])
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    content = await response.text()
                    
                    # Parse XML response (simplified)
                    if '<entry>' in content:
                        # Extract first paper info (basic parsing)
                        start = content.find('<title>') + 7
                        end = content.find('</title>', start)
                        title = content[start:end] if start > 6 and end > start else "Unknown"
                        
                        start = content.find('<summary>') + 9
                        end = content.find('</summary>', start)
                        summary = content[start:end] if start > 8 and end > start else "No summary"
                        
                        return {
                            'title': title.strip(),
                            'summary': summary.strip()[:500] + "..." if len(summary) > 500 else summary.strip(),
                            'source': 'ArXiv'
                        }
            
        except Exception as e:
            logger.warning(f"ArXiv search failed: {e}")
        
        return None
    
    async def _search_pubmed(self, query: str) -> Optional[Dict[str, Any]]:
        """Search PubMed for relevant papers"""
        try:
            await self._respect_rate_limit('pubmed')
            
            # PubMed search (simplified)
            search_url = f"{self.sources['pubmed'].url}/esearch.fcgi"
            params = {
                'db': 'pubmed',
                'term': query,
                'retmax': 3,
                'retmode': 'json'
            }
            
            async with self.session.get(search_url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    if 'esearchresult' in data and 'idlist' in data['esearchresult']:
                        ids = data['esearchresult']['idlist']
                        
                        if ids:
                            # Get details for first paper
                            detail_url = f"{self.sources['pubmed'].url}/esummary.fcgi"
                            detail_params = {
                                'db': 'pubmed',
                                'id': ids[0],
                                'retmode': 'json'
                            }
                            
                            async with self.session.get(detail_url, params=detail_params) as detail_response:
                                if detail_response.status == 200:
                                    detail_data = await detail_response.json()
                                    
                                    if 'result' in detail_data and ids[0] in detail_data['result']:
                                        paper = detail_data['result'][ids[0]]
                                        
                                        return {
                                            'title': paper.get('title', 'Unknown'),
                                            'summary': paper.get('abstract', 'No abstract available')[:500] + "...",
                                            'authors': ', '.join(paper.get('authors', [])),
                                            'source': 'PubMed'
                                        }
            
        except Exception as e:
            logger.warning(f"PubMed search failed: {e}")
        
        return None
    
    async def _respect_rate_limit(self, source_name: str):
        """Respect rate limits for external APIs"""
        if source_name in self.last_request_time:
            elapsed = time.time() - self.last_request_time[source_name]
            rate_limit = self.sources[source_name].rate_limit
            
            if elapsed < rate_limit:
                await asyncio.sleep(rate_limit - elapsed)
        
        self.last_request_time[source_name] = time.time()
    
    def _get_cache_key(self, query: str) -> str:
        """Generate cache key for query"""
        return hashlib.md5(query.lower().encode()).hexdigest()
    
    async def close(self):
        """Close the knowledge manager"""
        if self.session:
            await self.session.close()
        logger.info("External Knowledge Manager closed")

# Export main class
__all__ = ['ExternalKnowledgeManager', 'KnowledgeSource']
