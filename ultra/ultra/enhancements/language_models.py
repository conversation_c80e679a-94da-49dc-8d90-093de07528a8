#!/usr/bin/env python3
"""
Pre-trained Language Model Integrator
=====================================

Integrates pre-trained language models (GPT, BERT, T5, etc.) to enhance
ULTRA's natural language generation and understanding capabilities.
"""

import asyncio
import logging
import torch
import torch.nn.functional as F
from typing import Dict, List, Any, Optional, Union, Tuple
from dataclasses import dataclass
import numpy as np

logger = logging.getLogger(__name__)

# Try to import transformers
try:
    from transformers import (
        AutoTokenizer, AutoModelForCausalLM, AutoModelForSeq2SeqLM,
        GPT2LMHeadModel, GPT2Tokenizer,
        T5ForConditionalGeneration, T5Tokenizer,
        BertModel, BertTokenizer,
        pipeline
    )
    HAS_TRANSFORMERS = True
except ImportError:
    logger.warning("Transformers library not found. Install with: pip install transformers")
    HAS_TRANSFORMERS = False

@dataclass
class LanguageModelConfig:
    """Configuration for language model integration"""
    model_name: str = "microsoft/DialoGPT-medium"
    model_type: str = "causal_lm"  # causal_lm, seq2seq, encoder
    max_length: int = 512
    temperature: float = 0.7
    top_p: float = 0.9
    top_k: int = 50
    do_sample: bool = True
    num_return_sequences: int = 1
    device: str = "auto"
    use_cache: bool = True

class PretrainedLanguageModelIntegrator:
    """
    Integrates pre-trained language models to enhance ULTRA's responses
    """
    
    def __init__(self, config):
        self.config = config
        self.models = {}
        self.tokenizers = {}
        self.device = self._get_device()
        
        # Model configurations for different purposes
        self.model_configs = {
            'conversational': LanguageModelConfig(
                model_name="microsoft/DialoGPT-medium",
                model_type="causal_lm",
                temperature=0.7
            ),
            'fluency': LanguageModelConfig(
                model_name="gpt2",
                model_type="causal_lm",
                temperature=0.5
            ),
            'summarization': LanguageModelConfig(
                model_name="facebook/bart-large-cnn",
                model_type="seq2seq",
                temperature=0.3
            ),
            'question_answering': LanguageModelConfig(
                model_name="deepset/roberta-base-squad2",
                model_type="encoder",
                temperature=0.1
            )
        }
        
        logger.info("Pre-trained Language Model Integrator initialized")
    
    async def initialize(self):
        """Initialize language models"""
        if not HAS_TRANSFORMERS:
            logger.warning("Transformers not available, using fallback responses")
            return
        
        try:
            # Initialize conversational model for enhanced responses
            await self._load_model('conversational')
            
            # Initialize fluency model for text improvement
            await self._load_model('fluency')
            
            logger.info("✅ Pre-trained Language Models ready")
            
        except Exception as e:
            logger.error(f"Failed to initialize language models: {e}")
    
    async def enhance_fluency(self, 
                            text: str, 
                            query: str, 
                            context: Dict[str, Any]) -> str:
        """
        Enhance the fluency and naturalness of ULTRA's response
        """
        if not HAS_TRANSFORMERS or 'fluency' not in self.models:
            return text
        
        try:
            # Use the fluency model to improve text
            enhanced_text = await self._generate_enhanced_text(
                text, query, 'fluency'
            )
            
            # Combine original ULTRA reasoning with enhanced fluency
            if enhanced_text and len(enhanced_text.strip()) > 10:
                # Keep ULTRA's structured thinking but improve flow
                return self._merge_responses(text, enhanced_text)
            
            return text
            
        except Exception as e:
            logger.error(f"Error enhancing fluency: {e}")
            return text
    
    async def generate_conversational_response(self, 
                                             query: str, 
                                             context: Dict[str, Any]) -> str:
        """
        Generate a conversational response using pre-trained models
        """
        if not HAS_TRANSFORMERS or 'conversational' not in self.models:
            return ""
        
        try:
            response = await self._generate_enhanced_text(
                query, "", 'conversational'
            )
            return response
            
        except Exception as e:
            logger.error(f"Error generating conversational response: {e}")
            return ""
    
    async def summarize_knowledge(self, text: str) -> str:
        """
        Summarize external knowledge using pre-trained models
        """
        if not HAS_TRANSFORMERS:
            return text[:200] + "..." if len(text) > 200 else text
        
        try:
            if 'summarization' not in self.models:
                await self._load_model('summarization')
            
            if 'summarization' in self.models:
                summary = await self._generate_summary(text)
                return summary if summary else text[:200] + "..."
            
        except Exception as e:
            logger.error(f"Error summarizing knowledge: {e}")
        
        return text[:200] + "..." if len(text) > 200 else text
    
    async def _load_model(self, model_type: str):
        """Load a specific model type"""
        if model_type not in self.model_configs:
            logger.warning(f"Unknown model type: {model_type}")
            return
        
        config = self.model_configs[model_type]
        
        try:
            logger.info(f"Loading {model_type} model: {config.model_name}")
            
            # Load tokenizer
            self.tokenizers[model_type] = AutoTokenizer.from_pretrained(
                config.model_name,
                padding_side='left'
            )
            
            # Add pad token if missing
            if self.tokenizers[model_type].pad_token is None:
                self.tokenizers[model_type].pad_token = self.tokenizers[model_type].eos_token
            
            # Load model based on type
            if config.model_type == "causal_lm":
                self.models[model_type] = AutoModelForCausalLM.from_pretrained(
                    config.model_name,
                    torch_dtype=torch.float16 if self.device.type == 'cuda' else torch.float32,
                    device_map="auto" if self.device.type == 'cuda' else None
                )
            elif config.model_type == "seq2seq":
                self.models[model_type] = AutoModelForSeq2SeqLM.from_pretrained(
                    config.model_name,
                    torch_dtype=torch.float16 if self.device.type == 'cuda' else torch.float32,
                    device_map="auto" if self.device.type == 'cuda' else None
                )
            
            # Move to device if not using device_map
            if self.device.type != 'cuda':
                self.models[model_type] = self.models[model_type].to(self.device)
            
            # Set to evaluation mode
            self.models[model_type].eval()
            
            logger.info(f"✅ {model_type} model loaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to load {model_type} model: {e}")
    
    async def _generate_enhanced_text(self, 
                                    text: str, 
                                    query: str, 
                                    model_type: str) -> str:
        """Generate enhanced text using specified model"""
        if model_type not in self.models or model_type not in self.tokenizers:
            return ""
        
        try:
            model = self.models[model_type]
            tokenizer = self.tokenizers[model_type]
            config = self.model_configs[model_type]
            
            # Prepare input
            if model_type == 'conversational':
                # For conversational models, use query as input
                input_text = f"Human: {query}\nAI:"
            else:
                # For fluency models, use the text to enhance
                input_text = text[:400]  # Limit input length
            
            # Tokenize
            inputs = tokenizer.encode(
                input_text,
                return_tensors='pt',
                max_length=config.max_length // 2,
                truncation=True,
                padding=True
            ).to(self.device)
            
            # Generate
            with torch.no_grad():
                outputs = model.generate(
                    inputs,
                    max_length=min(config.max_length, inputs.shape[1] + 150),
                    temperature=config.temperature,
                    top_p=config.top_p,
                    top_k=config.top_k,
                    do_sample=config.do_sample,
                    num_return_sequences=config.num_return_sequences,
                    pad_token_id=tokenizer.pad_token_id,
                    eos_token_id=tokenizer.eos_token_id,
                    no_repeat_ngram_size=3
                )
            
            # Decode
            generated_text = tokenizer.decode(
                outputs[0], 
                skip_special_tokens=True
            )
            
            # Extract only the generated part
            if model_type == 'conversational':
                # Remove the input prompt
                if "AI:" in generated_text:
                    generated_text = generated_text.split("AI:")[-1].strip()
            else:
                # For fluency, get the continuation
                if len(generated_text) > len(input_text):
                    generated_text = generated_text[len(input_text):].strip()
            
            return generated_text
            
        except Exception as e:
            logger.error(f"Error generating enhanced text: {e}")
            return ""
    
    async def _generate_summary(self, text: str) -> str:
        """Generate summary using summarization model"""
        try:
            model = self.models['summarization']
            tokenizer = self.tokenizers['summarization']
            
            # Prepare input for summarization
            input_text = f"summarize: {text[:1000]}"  # Limit input
            
            inputs = tokenizer.encode(
                input_text,
                return_tensors='pt',
                max_length=512,
                truncation=True
            ).to(self.device)
            
            with torch.no_grad():
                outputs = model.generate(
                    inputs,
                    max_length=150,
                    min_length=30,
                    temperature=0.3,
                    do_sample=True,
                    early_stopping=True
                )
            
            summary = tokenizer.decode(outputs[0], skip_special_tokens=True)
            return summary
            
        except Exception as e:
            logger.error(f"Error generating summary: {e}")
            return ""
    
    def _merge_responses(self, original: str, enhanced: str) -> str:
        """
        Merge ULTRA's original structured response with enhanced fluency
        """
        # Keep ULTRA's structure but improve readability
        if "**" in original and len(enhanced) > 20:
            # ULTRA has structured response, add enhanced version
            return f"{original}\n\n💬 **Enhanced Response:**\n{enhanced}"
        else:
            # Simple text, replace with enhanced version
            return enhanced if len(enhanced) > len(original) // 2 else original
    
    def _get_device(self) -> torch.device:
        """Get the appropriate device for models"""
        if torch.cuda.is_available():
            return torch.device('cuda')
        elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            return torch.device('mps')
        else:
            return torch.device('cpu')
    
    def get_model_status(self) -> Dict[str, bool]:
        """Get status of loaded models"""
        return {
            model_type: model_type in self.models
            for model_type in self.model_configs.keys()
        }

# Export main class
__all__ = ['PretrainedLanguageModelIntegrator', 'LanguageModelConfig']
