#!/usr/bin/env python3
"""
ULTRA Meta-Cognitive System: Self-Critique Loop

This module implements the Self-Critique Loop component of the ULTRA Meta-Cognitive System.
The Self-Critique Loop enables the system to evaluate and refine its own reasoning process
by identifying flaws, biases, and weaknesses, and then suggesting improvements. This meta-cognitive
capability is crucial for robust reasoning, error correction, and continuous improvement.

Key capabilities include:
1. Detection of different types of reasoning flaws (logical fallacies, evidence gaps, etc.)
2. Generation of targeted critiques for specific reasoning components
3. Iterative refinement of reasoning to address identified issues
4. Evaluation of improvement between reasoning iterations
5. Integration with other meta-cognitive components for comprehensive reasoning enhancement
"""

import os
import time
import math
import json
import logging
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Any, Optional, Union, Set, Callable, Iterator
from dataclasses import dataclass, field
from enum import Enum, auto
from collections import defaultdict, deque
import re
import copy

# Import ULTRA system components
from ultra.config import get_config, SystemConfig
from ultra.utils.ultra_logging import get_logger
from ultra.meta_cognitive.chain_of_thought import ReasoningStepType

# Configure logger
logger = get_logger(__name__)

class CritiqueType(Enum):
    """Types of critiques that can be applied to reasoning."""
    LOGICAL_FALLACY = auto()     # Logical fallacies or errors in reasoning
    EVIDENCE_GAP = auto()        # Missing or insufficient evidence
    BIAS = auto()                # Cognitive biases in reasoning
    AMBIGUITY = auto()           # Unclear or ambiguous statements
    CONTRADICTION = auto()       # Internal contradictions
    OVERGENERALIZATION = auto()  # Unwarranted generalizations
    RELEVANCE_ISSUE = auto()     # Irrelevant information or tangents
    CONCEPTUAL_ERROR = auto()    # Misunderstandings of concepts
    CALCULATION_ERROR = auto()   # Errors in calculations or quantitative reasoning
    ASSUMPTION_ISSUE = auto()    # Problematic or unstated assumptions
    COMPLETENESS_ISSUE = auto()  # Incompleteness in addressing all aspects
    METHOD_ISSUE = auto()        # Issues with the method or approach

@dataclass
class CritiqueElement:
    """A specific critique of a reasoning element."""
    critique_type: CritiqueType                 # Type of critique
    description: str                            # Description of the issue
    location: Optional[str] = None              # Location in the reasoning (e.g., step number)
    severity: float = 1.0                       # Severity of the issue (0-1)
    improvement_suggestion: Optional[str] = None # Suggested improvement
    metadata: Dict[str, Any] = field(default_factory=dict) # Additional metadata
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "critique_type": self.critique_type.name,
            "description": self.description,
            "location": self.location,
            "severity": self.severity,
            "improvement_suggestion": self.improvement_suggestion,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CritiqueElement':
        """Create a CritiqueElement from a dictionary."""
        return cls(
            critique_type=CritiqueType[data["critique_type"]],
            description=data["description"],
            location=data.get("location"),
            severity=data.get("severity", 1.0),
            improvement_suggestion=data.get("improvement_suggestion"),
            metadata=data.get("metadata", {})
        )

@dataclass
class Critique:
    """A complete critique of a reasoning process."""
    elements: List[CritiqueElement]                # Critique elements
    overall_assessment: str                        # Overall assessment of the reasoning
    overall_score: float                           # Overall quality score (0-1)
    strengths: List[str] = field(default_factory=list) # Identified strengths
    improvement_priorities: List[str] = field(default_factory=list) # Prioritized improvements
    metadata: Dict[str, Any] = field(default_factory=dict) # Additional metadata
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "elements": [elem.to_dict() for elem in self.elements],
            "overall_assessment": self.overall_assessment,
            "overall_score": self.overall_score,
            "strengths": self.strengths,
            "improvement_priorities": self.improvement_priorities,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Critique':
        """Create a Critique from a dictionary."""
        return cls(
            elements=[CritiqueElement.from_dict(elem) for elem in data["elements"]],
            overall_assessment=data["overall_assessment"],
            overall_score=data["overall_score"],
            strengths=data.get("strengths", []),
            improvement_priorities=data.get("improvement_priorities", []),
            metadata=data.get("metadata", {})
        )
    
    def get_most_severe_issues(self, limit: int = 3) -> List[CritiqueElement]:
        """
        Get the most severe critique elements.
        
        Args:
            limit: Maximum number of elements to return
            
        Returns:
            List of the most severe critique elements
        """
        sorted_elements = sorted(self.elements, key=lambda e: e.severity, reverse=True)
        return sorted_elements[:limit]
    
    def has_critical_issues(self) -> bool:
        """
        Check if the critique contains critical issues.
        
        Returns:
            True if critical issues exist, False otherwise
        """
        return any(elem.severity > 0.8 for elem in self.elements)
    
    def summarize(self) -> str:
        """
        Generate a summarized version of the critique.
        
        Returns:
            Summarized critique text
        """
        summary = [f"Overall Assessment ({self.overall_score:.2f}/1.00): {self.overall_assessment}"]
        
        if self.strengths:
            summary.append("\nStrengths:")
            for i, strength in enumerate(self.strengths, 1):
                summary.append(f"{i}. {strength}")
        
        critical_issues = [elem for elem in self.elements if elem.severity > 0.7]
        if critical_issues:
            summary.append("\nCritical Issues:")
            for i, issue in enumerate(critical_issues, 1):
                summary.append(f"{i}. {issue.description} ({issue.critique_type.name})")
        
        if self.improvement_priorities:
            summary.append("\nImprovement Priorities:")
            for i, priority in enumerate(self.improvement_priorities, 1):
                summary.append(f"{i}. {priority}")
        
        return "\n".join(summary)

class CritiqueTechnique:
    """Base class for critique techniques."""
    
    def __init__(self, name: str):
        """
        Initialize the critique technique.
        
        Args:
            name: Name of the technique
        """
        self.name = name
    
    def critique(self, reasoning: str) -> List[CritiqueElement]:
        """
        Generate critique elements for a reasoning process.
        
        Args:
            reasoning: Reasoning text to critique
            
        Returns:
            List of critique elements
        """
        raise NotImplementedError("Subclasses must implement this method")
    
    def get_technique_metadata(self) -> Dict[str, Any]:
        """
        Get metadata about the technique.
        
        Returns:
            Technique metadata
        """
        return {"name": self.name}

class LogicalFallacyDetector(CritiqueTechnique):
    """Detects logical fallacies in reasoning."""
    
    def __init__(self, language_model=None):
        """
        Initialize the logical fallacy detector.
        
        Args:
            language_model: Optional language model for sophisticated fallacy detection
        """
        super().__init__("logical_fallacy_detector")
        self.language_model = language_model
        
        # Define common logical fallacies and their patterns
        self.fallacy_patterns = {
            "ad_hominem": [
                r"attack.*person",
                r"discredit.*character",
                r"because .*is a",
                r"since .*is a"
            ],
            "strawman": [
                r"distort.*argument",
                r"misrepresent.*position",
                r"exaggerate.*claim"
            ],
            "false_dichotomy": [
                r"(either|only).*or",
                r"two options",
                r"two choices",
                r"black and white"
            ],
            "slippery_slope": [
                r"lead to",
                r"result in",
                r"cascade of",
                r"chain of events"
            ],
            "hasty_generalization": [
                r"all",
                r"always",
                r"every",
                r"never",
                r"none"
            ],
            "circular_reasoning": [
                r"because it is",
                r"true because it",
                r"is so because"
            ],
            "appeal_to_authority": [
                r"expert",
                r"authority",
                r"according to",
                r"said that"
            ],
            "post_hoc": [
                r"after this",
                r"followed by",
                r"since this happened",
                r"because it came"
            ],
            "correlation_causation": [
                r"correlate",
                r"associated with",
                r"related to",
                r"linked to"
            ],
            "appeal_to_emotion": [
                r"feel",
                r"emotion",
                r"believe",
                r"think about",
                r"consider the"
            ]
        }
        
        # Define fallacy descriptions
        self.fallacy_descriptions = {
            "ad_hominem": "Attacking the person rather than addressing their argument",
            "strawman": "Misrepresenting someone's argument to make it easier to attack",
            "false_dichotomy": "Presenting only two options when others exist",
            "slippery_slope": "Asserting that a small step will lead to a chain of events without adequate justification",
            "hasty_generalization": "Drawing a general conclusion from insufficient evidence",
            "circular_reasoning": "Using the conclusion as a premise in the argument",
            "appeal_to_authority": "Relying on an authority figure rather than evidence or reasoning",
            "post_hoc": "Assuming that because B followed A, A caused B",
            "correlation_causation": "Confusing correlation with causation",
            "appeal_to_emotion": "Using emotional appeals instead of logical reasoning"
        }
        
        # Define fallacy improvement suggestions
        self.fallacy_improvements = {
            "ad_hominem": "Focus on addressing the argument rather than the person making it",
            "strawman": "Accurately represent the original argument before responding to it",
            "false_dichotomy": "Consider additional alternatives beyond the two options presented",
            "slippery_slope": "Provide evidence for each step in the causal chain or acknowledge uncertainty",
            "hasty_generalization": "Provide more evidence before making general claims, or qualify the scope",
            "circular_reasoning": "Introduce independent premises that don't assume the conclusion",
            "appeal_to_authority": "Explain the reasoning or evidence behind the authority's position",
            "post_hoc": "Investigate causal mechanisms rather than assuming causation from sequence",
            "correlation_causation": "Consider alternative explanations like reverse causation or confounding variables",
            "appeal_to_emotion": "Support points with evidence and logical reasoning instead of emotional appeals"
        }
    
    def critique(self, reasoning: str) -> List[CritiqueElement]:
        """
        Detect logical fallacies in reasoning.
        
        Args:
            reasoning: Reasoning text to critique
            
        Returns:
            List of critique elements for logical fallacies
        """
        critique_elements = []
        
        # Use language model for sophisticated fallacy detection if available
        if self.language_model:
            try:
                critique_elements.extend(self._critique_with_language_model(reasoning))
            except Exception as e:
                logger.warning(f"Error in language model fallacy detection: {e}")
                # Fall back to pattern-based detection
                critique_elements.extend(self._critique_with_patterns(reasoning))
        else:
            # Use pattern-based detection
            critique_elements.extend(self._critique_with_patterns(reasoning))
        
        return critique_elements
    
    def _critique_with_language_model(self, reasoning: str) -> List[CritiqueElement]:
        """
        Use language model to detect logical fallacies.
        
        Args:
            reasoning: Reasoning text to critique
            
        Returns:
            List of critique elements for logical fallacies
        """
        prompt = f"""
        Analyze the following reasoning for logical fallacies. For each fallacy you find, specify:
        1. The type of fallacy
        2. Where it occurs
        3. Why it's a fallacy
        4. How to improve the reasoning
        
        Reasoning:
        {reasoning}
        
        List each fallacy in the format:
        FALLACY_TYPE: [specific type]
        LOCATION: [relevant text or step number]
        DESCRIPTION: [explanation of why it's a fallacy]
        SEVERITY: [a number between 0 and 1, where 1 is most severe]
        IMPROVEMENT: [suggestion for improvement]
        """
        
        response = self.language_model.generate(prompt)
        
        # Parse the response to extract fallacies
        fallacies = []
        current_fallacy = {}
        
        for line in response.strip().split('\n'):
            line = line.strip()
            if not line:
                continue
                
            if line.startswith('FALLACY_TYPE:'):
                # Start a new fallacy
                if current_fallacy and 'fallacy_type' in current_fallacy:
                    fallacies.append(current_fallacy)
                current_fallacy = {}
                current_fallacy['fallacy_type'] = line.split(':', 1)[1].strip()
            elif line.startswith('LOCATION:') and current_fallacy:
                current_fallacy['location'] = line.split(':', 1)[1].strip()
            elif line.startswith('DESCRIPTION:') and current_fallacy:
                current_fallacy['description'] = line.split(':', 1)[1].strip()
            elif line.startswith('SEVERITY:') and current_fallacy:
                try:
                    severity = float(line.split(':', 1)[1].strip())
                    current_fallacy['severity'] = min(1.0, max(0.0, severity))
                except ValueError:
                    current_fallacy['severity'] = 0.7  # Default severity
            elif line.startswith('IMPROVEMENT:') and current_fallacy:
                current_fallacy['improvement'] = line.split(':', 1)[1].strip()
        
        # Add the last fallacy if it exists
        if current_fallacy and 'fallacy_type' in current_fallacy:
            fallacies.append(current_fallacy)
        
        # Convert parsed fallacies to critique elements
        critique_elements = []
        for fallacy in fallacies:
            # Map fallacy_type to a CritiqueType
            try:
                critique_type = CritiqueType.LOGICAL_FALLACY
                
                critique_elements.append(CritiqueElement(
                    critique_type=critique_type,
                    description=fallacy.get('description', 'Logical fallacy detected'),
                    location=fallacy.get('location'),
                    severity=fallacy.get('severity', 0.7),
                    improvement_suggestion=fallacy.get('improvement'),
                    metadata={"fallacy_type": fallacy.get('fallacy_type')}
                ))
            except Exception as e:
                logger.warning(f"Error creating critique element: {e}")
        
        return critique_elements
    
    def _critique_with_patterns(self, reasoning: str) -> List[CritiqueElement]:
        """
        Use pattern matching to detect logical fallacies.
        
        Args:
            reasoning: Reasoning text to critique
            
        Returns:
            List of critique elements for logical fallacies
        """
        critique_elements = []
        
        # Split reasoning into steps or paragraphs
        steps = reasoning.split('\n')
        
        # Check each step for fallacy patterns
        for i, step in enumerate(steps):
            step_lower = step.lower()
            
            for fallacy_name, patterns in self.fallacy_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, step_lower):
                        # Check for false positives
                        if self._is_false_positive(fallacy_name, step_lower):
                            continue
                        
                        # Create critique element
                        critique_elements.append(CritiqueElement(
                            critique_type=CritiqueType.LOGICAL_FALLACY,
                            description=f"{self.fallacy_descriptions.get(fallacy_name, 'Logical fallacy')} in step {i+1}",
                            location=f"Step {i+1}",
                            severity=self._calculate_fallacy_severity(fallacy_name, step),
                            improvement_suggestion=self.fallacy_improvements.get(fallacy_name),
                            metadata={"fallacy_type": fallacy_name, "step_index": i}
                        ))
                        
                        # Only report one instance of each fallacy per step
                        break
        
        return critique_elements
    
    def _is_false_positive(self, fallacy_name: str, text: str) -> bool:
        """
        Check if a fallacy detection is likely a false positive.
        
        Args:
            fallacy_name: Name of the detected fallacy
            text: Text where the fallacy was detected
            
        Returns:
            True if likely a false positive, False otherwise
        """
        # Define false positive patterns for different fallacies
        false_positives = {
            "ad_hominem": [
                r"not attacking",
                r"avoid ad hominem",
                r"without attacking"
            ],
            "strawman": [
                r"not distort",
                r"avoid strawman",
                r"not misrepresent"
            ],
            "false_dichotomy": [
                r"not (just|only) two",
                r"many options",
                r"multiple choices",
                r"avoid false dichotomy"
            ],
            "appeal_to_authority": [
                r"not just because",
                r"evidence from",
                r"according to research",
                r"studies show"
            ]
        }
        
        # Check if the text matches any false positive patterns
        if fallacy_name in false_positives:
            for pattern in false_positives[fallacy_name]:
                if re.search(pattern, text):
                    return True
        
        return False
    
    def _calculate_fallacy_severity(self, fallacy_name: str, text: str) -> float:
        """
        Calculate the severity of a fallacy based on its type and context.
        
        Args:
            fallacy_name: Name of the detected fallacy
            text: Text where the fallacy was detected
            
        Returns:
            Severity score (0-1)
        """
        # Base severities for different fallacy types
        base_severities = {
            "ad_hominem": 0.9,          # Very severe - directly attacks person
            "strawman": 0.8,            # Severe - misrepresents arguments
            "false_dichotomy": 0.7,     # Moderately severe - limits options
            "slippery_slope": 0.6,      # Moderately severe - unjustified causation
            "hasty_generalization": 0.7, # Moderately severe - insufficient evidence
            "circular_reasoning": 0.8,   # Severe - logically invalid
            "appeal_to_authority": 0.5,  # Less severe - depends on context
            "post_hoc": 0.6,            # Moderately severe - unjustified causation
            "correlation_causation": 0.7, # Moderately severe - unjustified causation
            "appeal_to_emotion": 0.5     # Less severe - depends on context
        }
        
        # Get base severity for the fallacy
        severity = base_severities.get(fallacy_name, 0.7)
        
        # Adjust severity based on text characteristics
        
        # Check emphasis (ALL CAPS, exclamation marks)
        if re.search(r'[A-Z]{3,}', text) or '!' in text:
            severity += 0.1
            
        # Check certainty language
        certainty_words = ["definitely", "certainly", "absolutely", "clearly", "obviously"]
        if any(word in text.lower() for word in certainty_words):
            severity += 0.1
            
        # Check if it's a central claim or conclusion
        conclusion_indicators = ["therefore", "thus", "hence", "in conclusion", "consequently"]
        if any(indicator in text.lower() for indicator in conclusion_indicators):
            severity += 0.1
            
        # Ensure severity is in [0, 1]
        return min(1.0, max(0.0, severity))

class EvidenceGapDetector(CritiqueTechnique):
    """Detects gaps in evidence or support for claims."""
    
    def __init__(self, language_model=None):
        """
        Initialize the evidence gap detector.
        
        Args:
            language_model: Optional language model for sophisticated gap detection
        """
        super().__init__("evidence_gap_detector")
        self.language_model = language_model
        
        # Keywords that indicate claims requiring evidence
        self.claim_indicators = [
            "therefore", "thus", "hence", "consequently", "shows that", "proves that",
            "demonstrates that", "indicates that", "because", "since", "due to",
            "as a result", "conclude", "establish", "confirm", "verify"
        ]
        
        # Words that suggest unsupported assertions
        self.unsupported_indicators = [
            "always", "never", "all", "none", "everyone", "nobody", "certainly",
            "definitely", "absolutely", "undoubtedly", "obviously", "clearly"
        ]
        
        # Evidence indicators (presence reduces likelihood of gaps)
        self.evidence_indicators = [
            "according to", "research shows", "studies indicate", "data suggests",
            "evidence demonstrates", "as shown by", "is supported by", "statistics show",
            "experiments confirm", "observations reveal", "analysis indicates",
            "literature suggests", "experts agree", "findings show"
        ]
    
    def critique(self, reasoning: str) -> List[CritiqueElement]:
        """
        Detect evidence gaps in reasoning.
        
        Args:
            reasoning: Reasoning text to critique
            
        Returns:
            List of critique elements for evidence gaps
        """
        critique_elements = []
        
        # Use language model for sophisticated gap detection if available
        if self.language_model:
            try:
                critique_elements.extend(self._critique_with_language_model(reasoning))
            except Exception as e:
                logger.warning(f"Error in language model evidence gap detection: {e}")
                # Fall back to pattern-based detection
                critique_elements.extend(self._critique_with_patterns(reasoning))
        else:
            # Use pattern-based detection
            critique_elements.extend(self._critique_with_patterns(reasoning))
        
        return critique_elements
    
    def _critique_with_language_model(self, reasoning: str) -> List[CritiqueElement]:
        """
        Use language model to detect evidence gaps.
        
        Args:
            reasoning: Reasoning text to critique
            
        Returns:
            List of critique elements for evidence gaps
        """
        prompt = f"""
        Analyze the following reasoning for evidence gaps. For each claim that lacks sufficient evidence or support, specify:
        1. The claim being made
        2. Where it occurs
        3. What evidence is missing
        4. How to improve the evidence
        
        Reasoning:
        {reasoning}
        
        List each evidence gap in the format:
        CLAIM: [the specific claim]
        LOCATION: [relevant text or step number]
        DESCRIPTION: [explanation of why evidence is insufficient]
        SEVERITY: [a number between 0 and 1, where 1 is most severe]
        IMPROVEMENT: [suggestion for what evidence to add]
        """
        
        response = self.language_model.generate(prompt)
        
        # Parse the response to extract evidence gaps
        gaps = []
        current_gap = {}
        
        for line in response.strip().split('\n'):
            line = line.strip()
            if not line:
                continue
                
            if line.startswith('CLAIM:'):
                # Start a new gap
                if current_gap and 'claim' in current_gap:
                    gaps.append(current_gap)
                current_gap = {}
                current_gap['claim'] = line.split(':', 1)[1].strip()
            elif line.startswith('LOCATION:') and current_gap:
                current_gap['location'] = line.split(':', 1)[1].strip()
            elif line.startswith('DESCRIPTION:') and current_gap:
                current_gap['description'] = line.split(':', 1)[1].strip()
            elif line.startswith('SEVERITY:') and current_gap:
                try:
                    severity = float(line.split(':', 1)[1].strip())
                    current_gap['severity'] = min(1.0, max(0.0, severity))
                except ValueError:
                    current_gap['severity'] = 0.7  # Default severity
            elif line.startswith('IMPROVEMENT:') and current_gap:
                current_gap['improvement'] = line.split(':', 1)[1].strip()
        
        # Add the last gap if it exists
        if current_gap and 'claim' in current_gap:
            gaps.append(current_gap)
        
        # Convert parsed gaps to critique elements
        critique_elements = []
        for gap in gaps:
            critique_elements.append(CritiqueElement(
                critique_type=CritiqueType.EVIDENCE_GAP,
                description=gap.get('description', 'Insufficient evidence for claim'),
                location=gap.get('location'),
                severity=gap.get('severity', 0.7),
                improvement_suggestion=gap.get('improvement'),
                metadata={"claim": gap.get('claim')}
            ))
        
        return critique_elements
    
    def _critique_with_patterns(self, reasoning: str) -> List[CritiqueElement]:
        """
        Use pattern matching to detect evidence gaps.
        
        Args:
            reasoning: Reasoning text to critique
            
        Returns:
            List of critique elements for evidence gaps
        """
        critique_elements = []
        
        # Split reasoning into steps or paragraphs
        steps = reasoning.split('\n')
        
        # Track evidence relationships between steps
        evidence_relations = {}  # maps step_idx -> list of steps that provide evidence
        claim_steps = []  # steps containing claims
        
        # First pass: identify claims and evidence indicators
        for i, step in enumerate(steps):
            step_lower = step.lower()
            
            # Check if step contains a claim
            if any(indicator in step_lower for indicator in self.claim_indicators):
                claim_steps.append(i)
                
            # Check if step contains evidence for other steps
            if any(indicator in step_lower for indicator in self.evidence_indicators):
                # Find steps that this might provide evidence for
                for j, other_step in enumerate(steps):
                    if i != j and self._provides_evidence_for(step, other_step):
                        if j not in evidence_relations:
                            evidence_relations[j] = []
                        evidence_relations[j].append(i)
        
        # Second pass: identify unsupported claims
        for i, step in enumerate(steps):
            step_lower = step.lower()
            
            # Check if step contains a claim indicator
            contains_claim = any(indicator in step_lower for indicator in self.claim_indicators)
            
            # Check if step contains an unsupported assertion indicator
            contains_unsupported = any(indicator in step_lower for indicator in self.unsupported_indicators)
            
            # Check if step has evidence support
            has_evidence = i in evidence_relations and len(evidence_relations[i]) > 0
            
            # Check if step contains its own evidence indicator
            has_own_evidence = any(indicator in step_lower for indicator in self.evidence_indicators)
            
            # If step makes a claim but lacks evidence, flag it
            if (contains_claim or contains_unsupported) and not (has_evidence or has_own_evidence):
                severity = 0.7  # Default severity
                
                # Increase severity for stronger claims
                if contains_unsupported:
                    severity += 0.1
                    
                # Increase severity for conclusions
                conclusion_indicators = ["therefore", "thus", "hence", "in conclusion", "consequently"]
                if any(indicator in step_lower for indicator in conclusion_indicators):
                    severity += 0.1
                
                critique_elements.append(CritiqueElement(
                    critique_type=CritiqueType.EVIDENCE_GAP,
                    description=f"Insufficient evidence for claim in step {i+1}",
                    location=f"Step {i+1}",
                    severity=min(1.0, severity),
                    improvement_suggestion="Provide specific evidence, data, or reasoning to support this claim",
                    metadata={"step_index": i, "has_evidence_indicators": has_own_evidence}
                ))
        
        return critique_elements
    
    def _provides_evidence_for(self, evidence_step: str, claim_step: str) -> bool:
        """
        Determine if one step provides evidence for another.
        
        Args:
            evidence_step: Step that might provide evidence
            claim_step: Step that might contain a claim
            
        Returns:
            True if evidence_step likely supports claim_step, False otherwise
        """
        evidence_lower = evidence_step.lower()
        claim_lower = claim_step.lower()
        
        # Check for specific reference patterns
        reference_patterns = [
            r"this (shows|demonstrates|proves|supports)",
            r"therefore",
            r"thus",
            r"hence",
            r"consequently",
            r"as a result"
        ]
        
        if any(re.search(pattern, claim_lower) for pattern in reference_patterns):
            # Check for shared key terms
            evidence_words = set(re.findall(r'\b\w+\b', evidence_lower))
            claim_words = set(re.findall(r'\b\w+\b', claim_lower))
            
            # Calculate word overlap (Jaccard similarity)
            intersection = evidence_words.intersection(claim_words)
            union = evidence_words.union(claim_words)
            
            if len(union) > 0 and len(intersection) / len(union) > 0.2:
                return True
        
        return False

class BiasDetector(CritiqueTechnique):
    """Detects cognitive biases in reasoning."""
    
    def __init__(self, language_model=None):
        """
        Initialize the bias detector.
        
        Args:
            language_model: Optional language model for sophisticated bias detection
        """
        super().__init__("bias_detector")
        self.language_model = language_model
        
        # Define common cognitive biases and their patterns
        self.bias_patterns = {
            "confirmation_bias": [
                r"only.*evidence.*support",
                r"ignor.*contrary",
                r"dismiss.*opposing",
                r"reinforc.*belief"
            ],
            "anchoring_bias": [
                r"initial.*estimate",
                r"first.*impression",
                r"start.*point",
                r"fixat.*on"
            ],
            "availability_bias": [
                r"recent.*example",
                r"comes to mind",
                r"vividly remember",
                r"salient.*instance"
            ],
            "hindsight_bias": [
                r"obvious.*all along",
                r"knew.*would happen",
                r"should have seen",
                r"inevitable"
            ],
            "overconfidence_bias": [
                r"certainly",
                r"definitely",
                r"absolutely",
                r"no doubt",
                r"(100|hundred) percent"
            ],
            "framing_bias": [
                r"presented as",
                r"framed as",
                r"context of",
                r"perspective of"
            ],
            "authority_bias": [
                r"expert.*said",
                r"authority.*states",
                r"according to.*famous",
                r"renowned"
            ]
        }
        
        # Define bias descriptions
        self.bias_descriptions = {
            "confirmation_bias": "Tendency to favor information that confirms existing beliefs",
            "anchoring_bias": "Over-reliance on the first piece of information encountered",
            "availability_bias": "Overestimating the likelihood of events based on their availability in memory",
            "hindsight_bias": "Belief that past events were predictable after they have occurred",
            "overconfidence_bias": "Excessive confidence in one's knowledge or abilities",
            "framing_bias": "Being influenced by how information is presented rather than the information itself",
            "authority_bias": "Tendency to attribute greater accuracy to opinions of authority figures"
        }
        
        # Define bias improvement suggestions
        self.bias_improvements = {
            "confirmation_bias": "Actively seek out evidence that contradicts your hypothesis or belief",
            "anchoring_bias": "Consider multiple starting points or reference values before making judgments",
            "availability_bias": "Look for statistical or representative data rather than relying on memorable examples",
            "hindsight_bias": "Acknowledge that outcomes were not as predictable beforehand as they seem afterward",
            "overconfidence_bias": "Express appropriate levels of uncertainty and consider confidence intervals",
            "framing_bias": "Reframe the problem in multiple ways before reaching a conclusion",
            "authority_bias": "Evaluate the evidence and reasoning behind claims, not just the source"
        }
    
    def critique(self, reasoning: str) -> List[CritiqueElement]:
        """
        Detect cognitive biases in reasoning.
        
        Args:
            reasoning: Reasoning text to critique
            
        Returns:
            List of critique elements for cognitive biases
        """
        critique_elements = []
        
        # Use language model for sophisticated bias detection if available
        if self.language_model:
            try:
                critique_elements.extend(self._critique_with_language_model(reasoning))
            except Exception as e:
                logger.warning(f"Error in language model bias detection: {e}")
                # Fall back to pattern-based detection
                critique_elements.extend(self._critique_with_patterns(reasoning))
        else:
            # Use pattern-based detection
            critique_elements.extend(self._critique_with_patterns(reasoning))
        
        return critique_elements
    
    def _critique_with_language_model(self, reasoning: str) -> List[CritiqueElement]:
        """
        Use language model to detect cognitive biases.
        
        Args:
            reasoning: Reasoning text to critique
            
        Returns:
            List of critique elements for cognitive biases
        """
        prompt = f"""
        Analyze the following reasoning for cognitive biases. For each bias you detect, specify:
        1. The type of cognitive bias
        2. Where it occurs
        3. How it affects the reasoning
        4. How to mitigate the bias
        
        Common cognitive biases include: confirmation bias, anchoring bias, availability bias, 
        hindsight bias, overconfidence bias, framing bias, authority bias, but there are many others.
        
        Reasoning:
        {reasoning}
        
        List each cognitive bias in the format:
        BIAS_TYPE: [specific type]
        LOCATION: [relevant text or step number]
        DESCRIPTION: [explanation of how the bias affects reasoning]
        SEVERITY: [a number between 0 and 1, where 1 is most severe]
        IMPROVEMENT: [suggestion for mitigating the bias]
        """
        
        response = self.language_model.generate(prompt)
        
        # Parse the response to extract biases
        biases = []
        current_bias = {}
        
        for line in response.strip().split('\n'):
            line = line.strip()
            if not line:
                continue
                
            if line.startswith('BIAS_TYPE:'):
                # Start a new bias
                if current_bias and 'bias_type' in current_bias:
                    biases.append(current_bias)
                current_bias = {}
                current_bias['bias_type'] = line.split(':', 1)[1].strip()
            elif line.startswith('LOCATION:') and current_bias:
                current_bias['location'] = line.split(':', 1)[1].strip()
            elif line.startswith('DESCRIPTION:') and current_bias:
                current_bias['description'] = line.split(':', 1)[1].strip()
            elif line.startswith('SEVERITY:') and current_bias:
                try:
                    severity = float(line.split(':', 1)[1].strip())
                    current_bias['severity'] = min(1.0, max(0.0, severity))
                except ValueError:
                    current_bias['severity'] = 0.7  # Default severity
            elif line.startswith('IMPROVEMENT:') and current_bias:
                current_bias['improvement'] = line.split(':', 1)[1].strip()
        
        # Add the last bias if it exists
        if current_bias and 'bias_type' in current_bias:
            biases.append(current_bias)
        
        # Convert parsed biases to critique elements
        critique_elements = []
        for bias in biases:
            critique_elements.append(CritiqueElement(
                critique_type=CritiqueType.BIAS,
                description=bias.get('description', 'Cognitive bias detected'),
                location=bias.get('location'),
                severity=bias.get('severity', 0.7),
                improvement_suggestion=bias.get('improvement'),
                metadata={"bias_type": bias.get('bias_type')}
            ))
        
        return critique_elements
    
    def _critique_with_patterns(self, reasoning: str) -> List[CritiqueElement]:
        """
        Use pattern matching to detect cognitive biases.
        
        Args:
            reasoning: Reasoning text to critique
            
        Returns:
            List of critique elements for cognitive biases
        """
        critique_elements = []
        
        # Split reasoning into steps or paragraphs
        steps = reasoning.split('\n')
        
        # Check each step for bias patterns
        for i, step in enumerate(steps):
            step_lower = step.lower()
            
            for bias_name, patterns in self.bias_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, step_lower):
                        # Check for false positives
                        if self._is_false_positive(bias_name, step_lower):
                            continue
                        
                        # Create critique element
                        critique_elements.append(CritiqueElement(
                            critique_type=CritiqueType.BIAS,
                            description=f"{self.bias_descriptions.get(bias_name, 'Cognitive bias')} in step {i+1}",
                            location=f"Step {i+1}",
                            severity=self._calculate_bias_severity(bias_name, step),
                            improvement_suggestion=self.bias_improvements.get(bias_name),
                            metadata={"bias_type": bias_name, "step_index": i}
                        ))
                        
                        # Only report one instance of each bias per step
                        break
        
        return critique_elements
    
    def _is_false_positive(self, bias_name: str, text: str) -> bool:
        """
        Check if a bias detection is likely a false positive.
        
        Args:
            bias_name: Name of the detected bias
            text: Text where the bias was detected
            
        Returns:
            True if likely a false positive, False otherwise
        """
        # Define false positive patterns for different biases
        false_positives = {
            "confirmation_bias": [
                r"avoid(?:ing)? confirmation bias",
                r"prevent(?:ing)? confirmation bias",
                r"without confirmation bias"
            ],
            "anchoring_bias": [
                r"avoid(?:ing)? anchoring",
                r"prevent(?:ing)? anchoring",
                r"independent of.*initial"
            ],
            "overconfidence_bias": [
                r"avoid(?:ing)? overconfidence",
                r"not (be|being) overconfident",
                r"express(?:ing)? uncertainty"
            ]
        }
        
        # Check if the text matches any false positive patterns
        if bias_name in false_positives:
            for pattern in false_positives[bias_name]:
                if re.search(pattern, text):
                    return True
        
        return False
    
    def _calculate_bias_severity(self, bias_name: str, text: str) -> float:
        """
        Calculate the severity of a bias based on its type and context.
        
        Args:
            bias_name: Name of the detected bias
            text: Text where the bias was detected
            
        Returns:
            Severity score (0-1)
        """
        # Base severities for different bias types
        base_severities = {
            "confirmation_bias": 0.8,       # Very severe - fundamental reasoning error
            "anchoring_bias": 0.6,          # Moderately severe - affects judgments
            "availability_bias": 0.7,       # Moderately severe - skews probability judgment
            "hindsight_bias": 0.5,          # Less severe - retrospective error
            "overconfidence_bias": 0.7,     # Moderately severe - affects uncertainty estimates
            "framing_bias": 0.6,            # Moderately severe - affects judgments
            "authority_bias": 0.6           # Moderately severe - affects evaluation of evidence
        }
        
        # Get base severity for the bias
        severity = base_severities.get(bias_name, 0.6)
        
        # Adjust severity based on text characteristics
        
        # Check emphasis (ALL CAPS, exclamation marks)
        if re.search(r'[A-Z]{3,}', text) or '!' in text:
            severity += 0.1
            
        # Check certainty language
        certainty_words = ["definitely", "certainly", "absolutely", "clearly", "obviously"]
        if any(word in text.lower() for word in certainty_words):
            severity += 0.1
            
        # Check if it's a central claim or conclusion
        conclusion_indicators = ["therefore", "thus", "hence", "in conclusion", "consequently"]
        if any(indicator in text.lower() for indicator in conclusion_indicators):
            severity += 0.1
            
        # Ensure severity is in [0, 1]
        return min(1.0, max(0.0, severity))

class AmbiguityDetector(CritiqueTechnique):
    """Detects ambiguous or unclear statements in reasoning."""
    
    def __init__(self, language_model=None):
        """
        Initialize the ambiguity detector.
        
        Args:
            language_model: Optional language model for sophisticated ambiguity detection
        """
        super().__init__("ambiguity_detector")
        self.language_model = language_model
        
        # Words or phrases that often indicate ambiguity
        self.ambiguity_indicators = [
            "could be", "might be", "may be", "possibly", "perhaps", "seems like",
            "appears to be", "somewhat", "kind of", "sort of", "in a way", "thing",
            "something", "somehow", "in some cases", "generally", "usually",
            "typically", "often", "likely", "unlikely", "this", "that", "these", "those",
            "it", "they", "one", "ones", "some"
        ]
        
        # Pronouns that may be unclear without clear referents
        self.unclear_pronouns = [
            "it", "they", "them", "their", "this", "that", "these", "those",
            "which", "who", "whom", "whose"
        ]
    
    def critique(self, reasoning: str) -> List[CritiqueElement]:
        """
        Detect ambiguities in reasoning.
        
        Args:
            reasoning: Reasoning text to critique
            
        Returns:
            List of critique elements for ambiguities
        """
        critique_elements = []
        
        # Use language model for sophisticated ambiguity detection if available
        if self.language_model:
            try:
                critique_elements.extend(self._critique_with_language_model(reasoning))
            except Exception as e:
                logger.warning(f"Error in language model ambiguity detection: {e}")
                # Fall back to pattern-based detection
                critique_elements.extend(self._critique_with_patterns(reasoning))
        else:
            # Use pattern-based detection
            critique_elements.extend(self._critique_with_patterns(reasoning))
        
        return critique_elements
    
    def _critique_with_language_model(self, reasoning: str) -> List[CritiqueElement]:
        """
        Use language model to detect ambiguities.
        
        Args:
            reasoning: Reasoning text to critique
            
        Returns:
            List of critique elements for ambiguities
        """
        prompt = f"""
        Analyze the following reasoning for ambiguities or unclear statements. For each ambiguity you detect, specify:
        1. The ambiguous statement or term
        2. Where it occurs
        3. Why it's ambiguous or unclear
        4. How to make it clearer
        
        Look for issues like vague terms, unclear pronoun references, multiple possible interpretations, 
        underspecified conditions, or lack of precision in crucial terms.
        
        Reasoning:
        {reasoning}
        
        List each ambiguity in the format:
        AMBIGUITY: [ambiguous statement or term]
        LOCATION: [relevant text or step number]
        DESCRIPTION: [explanation of why it's ambiguous]
        SEVERITY: [a number between 0 and 1, where 1 is most severe]
        IMPROVEMENT: [suggestion for making it clearer]
        """
        
        response = self.language_model.generate(prompt)
        
        # Parse the response to extract ambiguities
        ambiguities = []
        current_ambiguity = {}
        
        for line in response.strip().split('\n'):
            line = line.strip()
            if not line:
                continue
                
            if line.startswith('AMBIGUITY:'):
                # Start a new ambiguity
                if current_ambiguity and 'ambiguity' in current_ambiguity:
                    ambiguities.append(current_ambiguity)
                current_ambiguity = {}
                current_ambiguity['ambiguity'] = line.split(':', 1)[1].strip()
            elif line.startswith('LOCATION:') and current_ambiguity:
                current_ambiguity['location'] = line.split(':', 1)[1].strip()
            elif line.startswith('DESCRIPTION:') and current_ambiguity:
                current_ambiguity['description'] = line.split(':', 1)[1].strip()
            elif line.startswith('SEVERITY:') and current_ambiguity:
                try:
                    severity = float(line.split(':', 1)[1].strip())
                    current_ambiguity['severity'] = min(1.0, max(0.0, severity))
                except ValueError:
                    current_ambiguity['severity'] = 0.6  # Default severity
            elif line.startswith('IMPROVEMENT:') and current_ambiguity:
                current_ambiguity['improvement'] = line.split(':', 1)[1].strip()
        
        # Add the last ambiguity if it exists
        if current_ambiguity and 'ambiguity' in current_ambiguity:
            ambiguities.append(current_ambiguity)
        
        # Convert parsed ambiguities to critique elements
        critique_elements = []
        for ambiguity in ambiguities:
            critique_elements.append(CritiqueElement(
                critique_type=CritiqueType.AMBIGUITY,
                description=ambiguity.get('description', 'Ambiguous statement detected'),
                location=ambiguity.get('location'),
                severity=ambiguity.get('severity', 0.6),
                improvement_suggestion=ambiguity.get('improvement'),
                metadata={"ambiguous_term": ambiguity.get('ambiguity')}
            ))
        
        return critique_elements
    
    def _critique_with_patterns(self, reasoning: str) -> List[CritiqueElement]:
        """
        Use pattern matching to detect ambiguities.
        
        Args:
            reasoning: Reasoning text to critique
            
        Returns:
            List of critique elements for ambiguities
        """
        critique_elements = []
        
        # Split reasoning into steps or paragraphs
        steps = reasoning.split('\n')
        
        # Check each step for ambiguity indicators
        for i, step in enumerate(steps):
            step_lower = step.lower()
            
            # Check for ambiguity indicators
            ambiguities = []
            for indicator in self.ambiguity_indicators:
                if indicator in step_lower:
                    ambiguities.append(indicator)
            
            # Check for unclear pronoun references
            pronouns = []
            for pronoun in self.unclear_pronouns:
                # Find all occurrences of the pronoun with word boundaries
                pattern = r'\b' + re.escape(pronoun) + r'\b'
                matches = list(re.finditer(pattern, step_lower))
                
                for match in matches:
                    # Check if the pronoun has a clear referent
                    if not self._has_clear_referent(step, match.span(), reasoning, i):
                        pronouns.append(pronoun)
            
            # If ambiguities were found, create a critique element
            if ambiguities:
                ambiguity_str = ", ".join(ambiguities[:3])
                if len(ambiguities) > 3:
                    ambiguity_str += f", and {len(ambiguities) - 3} more"
                
                critique_elements.append(CritiqueElement(
                    critique_type=CritiqueType.AMBIGUITY,
                    description=f"Ambiguous terms in step {i+1}: {ambiguity_str}",
                    location=f"Step {i+1}",
                    severity=0.6,
                    improvement_suggestion="Replace ambiguous terms with more specific language",
                    metadata={"ambiguous_terms": ambiguities, "step_index": i}
                ))
            
            # If unclear pronouns were found, create a critique element
            if pronouns:
                unique_pronouns = list(set(pronouns))
                pronoun_str = ", ".join(unique_pronouns[:3])
                if len(unique_pronouns) > 3:
                    pronoun_str += f", and {len(unique_pronouns) - 3} more"
                
                critique_elements.append(CritiqueElement(
                    critique_type=CritiqueType.AMBIGUITY,
                    description=f"Unclear pronoun references in step {i+1}: {pronoun_str}",
                    location=f"Step {i+1}",
                    severity=0.7,
                    improvement_suggestion="Clarify pronoun references by explicitly stating what they refer to",
                    metadata={"unclear_pronouns": unique_pronouns, "step_index": i}
                ))
        
        return critique_elements
    
    def _has_clear_referent(self, step: str, pronoun_span: Tuple[int, int], 
                           full_reasoning: str, step_index: int) -> bool:
        """
        Determine if a pronoun has a clear referent.
        
        Args:
            step: The current reasoning step
            pronoun_span: (start, end) position of the pronoun in the step
            full_reasoning: The complete reasoning text
            step_index: Index of the current step
            
        Returns:
            True if the pronoun has a clear referent, False otherwise
        """
        pronoun = step[pronoun_span[0]:pronoun_span[1]].lower()
        
        # Simple heuristic: check if there's a potential noun phrase before the pronoun
        # in the same step or in the previous step
        
        # Get text before the pronoun in the current step
        text_before = step[:pronoun_span[0]]
        
        # Check if there's a potential noun before the pronoun in the current step
        if self._contains_noun(text_before):
            return True
            
        # Check previous step if available
        if step_index > 0:
            steps = full_reasoning.split('\n')
            if step_index > 0 and step_index < len(steps):
                prev_step = steps[step_index - 1]
                if self._contains_noun(prev_step):
                    return True
        
        return False
    
    def _contains_noun(self, text: str) -> bool:
        """
        Check if text contains at least one noun.
        
        Args:
            text: Text to check
            
        Returns:
            True if the text contains a noun, False otherwise
        """
        # Simple check: look for capitalized words not at the start of a sentence
        words = text.split()
        for i, word in enumerate(words):
            if i > 0 and word[0].isupper():
                return True
                
        # Also check for common nouns
        common_nouns = ["person", "place", "thing", "idea", "concept", "problem", "solution",
                       "method", "approach", "example", "model", "theory", "system",
                       "process", "result", "conclusion", "evidence", "data"]
        
        return any(noun in text.lower() for noun in common_nouns)

class ContradictionDetector(CritiqueTechnique):
    """Detects internal contradictions in reasoning."""
    
    def __init__(self, language_model=None):
        """
        Initialize the contradiction detector.
        
        Args:
            language_model: Optional language model for sophisticated contradiction detection
        """
        super().__init__("contradiction_detector")
        self.language_model = language_model
        
        # Contradiction indicators
        self.contradiction_indicators = [
            "but", "however", "yet", "although", "nevertheless", "nonetheless",
            "on the other hand", "conversely", "in contrast", "on the contrary",
            "instead", "rather", "whereas", "while", "despite", "in spite of"
        ]
        
        # Pairs of contradictory terms
        self.contradictory_pairs = [
            ("always", "never"), ("all", "none"), ("everyone", "nobody"),
            ("increase", "decrease"), ("positive", "negative"), ("high", "low"),
            ("more", "less"), ("greater", "smaller"), ("better", "worse"),
            ("true", "false"), ("correct", "incorrect"), ("right", "wrong"),
            ("accept", "reject"), ("support", "oppose"), ("agree", "disagree"),
            ("confirm", "deny"), ("prove", "disprove"), ("valid", "invalid"),
            ("significant", "insignificant"), ("possible", "impossible")
        ]
    
    def critique(self, reasoning: str) -> List[CritiqueElement]:
        """
        Detect contradictions in reasoning.
        
        Args:
            reasoning: Reasoning text to critique
            
        Returns:
            List of critique elements for contradictions
        """
        critique_elements = []
        
        # Use language model for sophisticated contradiction detection if available
        if self.language_model:
            try:
                critique_elements.extend(self._critique_with_language_model(reasoning))
            except Exception as e:
                logger.warning(f"Error in language model contradiction detection: {e}")
                # Fall back to pattern-based detection
                critique_elements.extend(self._critique_with_patterns(reasoning))
        else:
            # Use pattern-based detection
            critique_elements.extend(self._critique_with_patterns(reasoning))
        
        return critique_elements
    
    def _critique_with_language_model(self, reasoning: str) -> List[CritiqueElement]:
        """
        Use language model to detect contradictions.
        
        Args:
            reasoning: Reasoning text to critique
            
        Returns:
            List of critique elements for contradictions
        """
        prompt = f"""
        Analyze the following reasoning for internal contradictions. For each contradiction you find, specify:
        1. The contradictory statements
        2. Where they occur
        3. Why they contradict each other
        4. How to resolve the contradiction
        
        Look for statements that directly oppose each other, or claims that cannot both be true simultaneously.
        
        Reasoning:
        {reasoning}
        
        List each contradiction in the format:
        CONTRADICTION: [pair of contradictory statements]
        LOCATION: [relevant text or step numbers]
        DESCRIPTION: [explanation of why they contradict]
        SEVERITY: [a number between 0 and 1, where 1 is most severe]
        RESOLUTION: [suggestion for resolving the contradiction]
        """
        
        response = self.language_model.generate(prompt)
        
        # Parse the response to extract contradictions
        contradictions = []
        current_contradiction = {}
        
        for line in response.strip().split('\n'):
            line = line.strip()
            if not line:
                continue
                
            if line.startswith('CONTRADICTION:'):
                # Start a new contradiction
                if current_contradiction and 'contradiction' in current_contradiction:
                    contradictions.append(current_contradiction)
                current_contradiction = {}
                current_contradiction['contradiction'] = line.split(':', 1)[1].strip()
            elif line.startswith('LOCATION:') and current_contradiction:
                current_contradiction['location'] = line.split(':', 1)[1].strip()
            elif line.startswith('DESCRIPTION:') and current_contradiction:
                current_contradiction['description'] = line.split(':', 1)[1].strip()
            elif line.startswith('SEVERITY:') and current_contradiction:
                try:
                    severity = float(line.split(':', 1)[1].strip())
                    current_contradiction['severity'] = min(1.0, max(0.0, severity))
                except ValueError:
                    current_contradiction['severity'] = 0.8  # Default severity
            elif line.startswith('RESOLUTION:') and current_contradiction:
                current_contradiction['resolution'] = line.split(':', 1)[1].strip()
        
        # Add the last contradiction if it exists
        if current_contradiction and 'contradiction' in current_contradiction:
            contradictions.append(current_contradiction)
        
        # Convert parsed contradictions to critique elements
        critique_elements = []
        for contradiction in contradictions:
            critique_elements.append(CritiqueElement(
                critique_type=CritiqueType.CONTRADICTION,
                description=contradiction.get('description', 'Internal contradiction detected'),
                location=contradiction.get('location'),
                severity=contradiction.get('severity', 0.8),
                improvement_suggestion=contradiction.get('resolution'),
                metadata={"contradictory_statements": contradiction.get('contradiction')}
            ))
        
        return critique_elements
    
    def _critique_with_patterns(self, reasoning: str) -> List[CritiqueElement]:
        """
        Use pattern matching to detect contradictions.
        
        Args:
            reasoning: Reasoning text to critique
            
        Returns:
            List of critique elements for contradictions
        """
        critique_elements = []
        
        # Split reasoning into steps or paragraphs
        steps = reasoning.split('\n')
        
        # Check for contradictory statements across steps
        for i, step1 in enumerate(steps):
            step1_lower = step1.lower()
            
            for j in range(i+1, len(steps)):
                step2 = steps[j]
                step2_lower = step2.lower()
                
                # Check for contradictory pairs
                for term1, term2 in self.contradictory_pairs:
                    if term1 in step1_lower and term2 in step2_lower:
                        if self._check_actual_contradiction(step1, step2, term1, term2):
                            critique_elements.append(CritiqueElement(
                                critique_type=CritiqueType.CONTRADICTION,
                                description=f"Contradiction between steps {i+1} and {j+1}: '{term1}' vs '{term2}'",
                                location=f"Steps {i+1} and {j+1}",
                                severity=0.8,
                                improvement_suggestion="Resolve the contradiction by clarifying conditions or qualifying statements",
                                metadata={"step_indices": [i, j], "contradictory_terms": [term1, term2]}
                            ))
                    elif term2 in step1_lower and term1 in step2_lower:
                        if self._check_actual_contradiction(step1, step2, term2, term1):
                            critique_elements.append(CritiqueElement(
                                critique_type=CritiqueType.CONTRADICTION,
                                description=f"Contradiction between steps {i+1} and {j+1}: '{term2}' vs '{term1}'",
                                location=f"Steps {i+1} and {j+1}",
                                severity=0.8,
                                improvement_suggestion="Resolve the contradiction by clarifying conditions or qualifying statements",
                                metadata={"step_indices": [i, j], "contradictory_terms": [term2, term1]}
                            ))
                
                # Check for direct statement/negation pairs
                if self._contains_negation(step1_lower, step2_lower):
                    critique_elements.append(CritiqueElement(
                        critique_type=CritiqueType.CONTRADICTION,
                        description=f"Contradiction between steps {i+1} and {j+1}: one statement negates the other",
                        location=f"Steps {i+1} and {j+1}",
                        severity=0.9,
                        improvement_suggestion="Resolve the contradiction by clarifying conditions or qualifying statements",
                        metadata={"step_indices": [i, j]}
                    ))
        
        return critique_elements
    
    def _check_actual_contradiction(self, step1: str, step2: str, term1: str, term2: str) -> bool:
        """
        Check if two steps with potentially contradictory terms actually contradict.
        
        Args:
            step1: First reasoning step
            step2: Second reasoning step
            term1: First potentially contradictory term
            term2: Second potentially contradictory term
            
        Returns:
            True if the steps likely contradict, False otherwise
        """
        step1_lower = step1.lower()
        step2_lower = step2.lower()
        
        # Check for shared context words around the contradictory terms
        context1 = self._get_context_words(step1_lower, term1)
        context2 = self._get_context_words(step2_lower, term2)
        
        # Calculate Jaccard similarity of contexts
        if not context1 or not context2:
            return False
            
        intersection = set(context1).intersection(set(context2))
        union = set(context1).union(set(context2))
        
        similarity = len(intersection) / len(union) if union else 0
        
        # If contexts are similar, likely a contradiction
        return similarity > 0.2
    
    def _get_context_words(self, text: str, term: str) -> List[str]:
        """
        Get context words around a term in text.
        
        Args:
            text: Text containing the term
            term: Term to get context for
            
        Returns:
            List of context words
        """
        words = text.split()
        if term not in words:
            # Handle multi-word terms or substrings
            return []
            
        term_index = words.index(term)
        
        # Get words in a window around the term
        window_size = 3
        start = max(0, term_index - window_size)
        end = min(len(words), term_index + window_size + 1)
        
        # Exclude the term itself and common stopwords
        stopwords = {"the", "a", "an", "and", "or", "but", "if", "of", "to", "in", "is", "it", "for"}
        context = [w for w in words[start:end] if w != term and w not in stopwords]
        
        return context
    
    def _contains_negation(self, text1: str, text2: str) -> bool:
        """
        Check if one text contains the negation of the other.
        
        Args:
            text1: First text
            text2: Second text
            
        Returns:
            True if one text negates the other, False otherwise
        """
        # Simple negation patterns
        negation_patterns = [
            ("is", "is not"), ("are", "are not"), ("will", "will not"),
            ("can", "cannot"), ("has", "has not"), ("have", "have not"),
            ("does", "does not"), ("do", "do not"), ("should", "should not")
        ]
        
        for pos, neg in negation_patterns:
            if (pos in text1 and neg in text2) or (neg in text1 and pos in text2):
                # Check for shared context to ensure it's the same statement
                shared_words = set(text1.split()).intersection(set(text2.split()))
                shared_words = shared_words - {pos, neg, "not"}
                
                # If there are multiple shared words, likely a contradiction
                if len(shared_words) >= 3:
                    return True
        
        return False

class CombinationCritiqueTechnique(CritiqueTechnique):
    """Combines multiple critique techniques for comprehensive analysis."""
    
    def __init__(self, techniques: List[CritiqueTechnique], name: str = "combination_technique"):
        """
        Initialize the combination critique technique.
        
        Args:
            techniques: List of critique techniques to combine
            name: Name of the combination technique
        """
        super().__init__(name)
        self.techniques = techniques
    
    def critique(self, reasoning: str) -> List[CritiqueElement]:
        """
        Apply multiple critique techniques and combine results.
        
        Args:
            reasoning: Reasoning text to critique
            
        Returns:
            Combined list of critique elements
        """
        all_elements = []
        
        # Apply each technique
        for technique in self.techniques:
            try:
                elements = technique.critique(reasoning)
                all_elements.extend(elements)
            except Exception as e:
                logger.warning(f"Error applying {technique.name}: {e}")
        
        # Sort by severity (descending)
        all_elements.sort(key=lambda e: e.severity, reverse=True)
        
        return all_elements
    
    def get_technique_metadata(self) -> Dict[str, Any]:
        """
        Get metadata about the technique.
        
        Returns:
            Technique metadata
        """
        return {
            "name": self.name,
            "component_techniques": [technique.name for technique in self.techniques]
        }

class SelfCritiqueLoop:
    """
    Main class implementing the Self-Critique Loop of the ULTRA Meta-Cognitive System.
    
    The Self-Critique Loop enables the system to evaluate and refine its own reasoning
    through iterative critique and improvement.
    """
    
    def __init__(
        self, 
        language_model=None,
        iterations: int = 3,
        critique_techniques: Optional[List[CritiqueTechnique]] = None,
        config: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize the Self-Critique Loop.
        
        Args:
            language_model: Optional language model for critique generation and refinement
            iterations: Maximum number of critique-refinement iterations
            critique_techniques: Optional list of specific critique techniques to use
            config: Optional configuration dictionary
        """
        self.language_model = language_model
        self.iterations = iterations
        self.config = config or {}
        
        # Initialize critique techniques
        if critique_techniques:
            self.critique_techniques = critique_techniques
        else:
            self.critique_techniques = self._initialize_critique_techniques()
        
        # Initialize combined technique
        self.combined_technique = CombinationCritiqueTechnique(self.critique_techniques)
        
        # History of critiques and refinements
        self.critique_history = []
        
        # Logger
        self.logger = get_logger(__name__)
    
    def _initialize_critique_techniques(self) -> List[CritiqueTechnique]:
        """
        Initialize the default set of critique techniques.
        
        Returns:
            List of critique techniques
        """
        techniques = []
        
        # Add logical fallacy detector
        techniques.append(LogicalFallacyDetector(self.language_model))
        
        # Add evidence gap detector
        techniques.append(EvidenceGapDetector(self.language_model))
        
        # Add bias detector
        techniques.append(BiasDetector(self.language_model))
        
        # Add ambiguity detector
        techniques.append(AmbiguityDetector(self.language_model))
        
        # Add contradiction detector
        techniques.append(ContradictionDetector(self.language_model))
        
        return techniques
    
    def generate_critique(self, problem: str, solution: str) -> Critique:
        """
        Generate a critique of a reasoning solution.
        
        Args:
            problem: The problem statement
            solution: The reasoning solution to critique
            
        Returns:
            A Critique object containing the critique elements
        """
        # Apply the combined critique technique
        critique_elements = self.combined_technique.critique(solution)
        
        # Generate overall assessment using language model if available
        if self.language_model:
            overall_assessment = self._generate_overall_assessment(problem, solution, critique_elements)
            overall_score = self._calculate_overall_score(critique_elements)
            strengths = self._identify_strengths(problem, solution)
            improvement_priorities = self._prioritize_improvements(critique_elements)
        else:
            # Fall back to simpler assessment
            overall_assessment = self._generate_simple_assessment(critique_elements)
            overall_score = self._calculate_overall_score(critique_elements)
            strengths = []
            improvement_priorities = [elem.description for elem in critique_elements[:3]]
        
        # Create Critique object
        critique = Critique(
            elements=critique_elements,
            overall_assessment=overall_assessment,
            overall_score=overall_score,
            strengths=strengths,
            improvement_priorities=improvement_priorities
        )
        
        return critique
    
    def _generate_overall_assessment(
        self, 
        problem: str, 
        solution: str, 
        critique_elements: List[CritiqueElement]
    ) -> str:
        """
        Generate an overall assessment of the reasoning solution using a language model.
        
        Args:
            problem: The problem statement
            solution: The reasoning solution
            critique_elements: List of critique elements
            
        Returns:
            Overall assessment text
        """
        if not self.language_model:
            return self._generate_simple_assessment(critique_elements)
            
        # Extract key issues
        issues = []
        for elem in critique_elements:
            issues.append(f"{elem.critique_type.name}: {elem.description}")
        
        issues_text = "\n".join(issues[:5])  # Limit to top 5 issues
        
        prompt = f"""
        Evaluate the following reasoning solution and provide an overall assessment.
        
        Problem: {problem}
        
        Solution:
        {solution}
        
        Key issues identified:
        {issues_text}
        
        Provide a balanced overall assessment in 2-3 sentences that summarizes the quality 
        of the reasoning, mentioning both strengths and weaknesses. Focus on aspects like
        logical validity, evidence quality, clarity, and potential biases.
        
        Overall assessment:
        """
        
        try:
            response = self.language_model.generate(prompt)
            return response.strip()
        except Exception as e:
            self.logger.warning(f"Error generating overall assessment: {e}")
            return self._generate_simple_assessment(critique_elements)
    
    def _generate_simple_assessment(self, critique_elements: List[CritiqueElement]) -> str:
        """
        Generate a simple overall assessment based on critique elements.
        
        Args:
            critique_elements: List of critique elements
            
        Returns:
            Simple overall assessment text
        """
        # Count issues by type
        issue_counts = {}
        total_severity = 0.0
        
        for elem in critique_elements:
            critique_type = elem.critique_type.name
            if critique_type not in issue_counts:
                issue_counts[critique_type] = 0
            issue_counts[critique_type] += 1
            total_severity += elem.severity
        
        # Generate assessment based on issue counts and total severity
        num_issues = len(critique_elements)
        avg_severity = total_severity / num_issues if num_issues > 0 else 0.0
        
        if num_issues == 0:
            return "The reasoning appears sound with no significant issues detected."
        elif num_issues <= 2 and avg_severity < 0.6:
            return f"The reasoning is generally sound, with {num_issues} minor issues that could be improved."
        elif num_issues <= 5 and avg_severity < 0.7:
            return f"The reasoning has {num_issues} issues of moderate concern that should be addressed for clarity and validity."
        else:
            # Identify main issue types
            main_issues = sorted(issue_counts.items(), key=lambda x: x[1], reverse=True)[:2]
            issue_text = ", ".join([f"{count} {issue_type.lower().replace('_', ' ')} issues" for issue_type, count in main_issues])
            
            return f"The reasoning has significant problems, including {issue_text}, that substantially affect its validity and clarity."
    
    def _calculate_overall_score(self, critique_elements: List[CritiqueElement]) -> float:
        """
        Calculate an overall quality score based on critique elements.
        
        Args:
            critique_elements: List of critique elements
            
        Returns:
            Overall quality score (0-1)
        """
        if not critique_elements:
            return 1.0  # Perfect score if no issues
            
        # Base score
        base_score = 1.0
        
        # Deductions based on critique elements
        for elem in critique_elements:
            # Different types of issues have different weights
            weight = {
                CritiqueType.LOGICAL_FALLACY: 0.4,
                CritiqueType.EVIDENCE_GAP: 0.3,
                CritiqueType.CONTRADICTION: 0.4,
                CritiqueType.BIAS: 0.25,
                CritiqueType.AMBIGUITY: 0.2,
                CritiqueType.OVERGENERALIZATION: 0.25,
                CritiqueType.RELEVANCE_ISSUE: 0.2,
                CritiqueType.CONCEPTUAL_ERROR: 0.3,
                CritiqueType.CALCULATION_ERROR: 0.35,
                CritiqueType.ASSUMPTION_ISSUE: 0.25,
                CritiqueType.COMPLETENESS_ISSUE: 0.2,
                CritiqueType.METHOD_ISSUE: 0.25
            }.get(elem.critique_type, 0.25)
            
            # Deduction is weight * severity
            deduction = weight * elem.severity
            
            # Apply diminishing returns for multiple issues of the same type
            issue_count = sum(1 for e in critique_elements if e.critique_type == elem.critique_type)
            if issue_count > 1:
                deduction *= (1 + math.log(issue_count)) / issue_count
                
            base_score -= deduction / len(critique_elements)
        
        # Ensure score is in [0, 1]
        return max(0.0, min(1.0, base_score))
    
    def _identify_strengths(self, problem: str, solution: str) -> List[str]:
        """
        Identify strengths in the reasoning solution.
        
        Args:
            problem: The problem statement
            solution: The reasoning solution
            
        Returns:
            List of identified strengths
        """
        if not self.language_model:
            return []
            
        prompt = f"""
        Identify strengths in the following reasoning solution:
        
        Problem: {problem}
        
        Solution:
        {solution}
        
        List 2-3 specific strengths of this reasoning approach. Focus on aspects like 
        logical structure, evidence quality, clarity of expression, consideration of 
        alternatives, or appropriate use of concepts.
        
        Strengths:
        """
        
        try:
            response = self.language_model.generate(prompt)
            
            # Parse strengths
            strengths = []
            for line in response.strip().split('\n'):
                # Look for numbered or bulleted lines
                if re.match(r'^\d+[\.\)]|^-|^\*', line.strip()):
                    # Remove the numbering or bullet
                    strength = re.sub(r'^\d+[\.\)]|^-|^\*\s*', '', line).strip()
                    if strength:
                        strengths.append(strength)
                elif line.strip():
                    # If not following the expected format, add the whole line
                    strengths.append(line.strip())
            
            return strengths[:3]  # Limit to 3 strengths
        except Exception as e:
            self.logger.warning(f"Error identifying strengths: {e}")
            return []
    
    def _prioritize_improvements(self, critique_elements: List[CritiqueElement]) -> List[str]:
        """
        Prioritize improvements based on critique elements.
        
        Args:
            critique_elements: List of critique elements
            
        Returns:
            List of prioritized improvement suggestions
        """
        if not critique_elements:
            return ["No specific improvements needed."]
            
        # Sort elements by severity
        sorted_elements = sorted(critique_elements, key=lambda e: e.severity, reverse=True)
        
        # Extract improvement suggestions
        improvements = []
        for elem in sorted_elements:
            if elem.improvement_suggestion:
                improvements.append(elem.improvement_suggestion)
                
        # Limit to top 5 unique improvements
        unique_improvements = []
        for imp in improvements:
            if imp not in unique_improvements:
                unique_improvements.append(imp)
                if len(unique_improvements) >= 5:
                    break
        
        return unique_improvements
    
    def refine_reasoning(self, problem: str, initial_solution: str) -> str:
        """
        Refine a reasoning solution through iterative critique and improvement.
        
        Args:
            problem: The problem statement
            initial_solution: The initial reasoning solution
            
        Returns:
            Refined reasoning solution
        """
        # Reset critique history
        self.critique_history = []
        
        current_solution = initial_solution
        
        # Iterative refinement
        for iteration in range(self.iterations):
            self.logger.info(f"Starting refinement iteration {iteration+1}/{self.iterations}")
            
            # Generate critique
            critique = self.generate_critique(problem, current_solution)
            
            # Store critique in history
            self.critique_history.append({
                "iteration": iteration + 1,
                "solution": current_solution,
                "critique": critique.to_dict()
            })
            
            # Check if further refinement is needed
            if critique.overall_score > 0.9:
                self.logger.info(f"Refinement complete after {iteration+1} iterations with score {critique.overall_score:.2f}")
                break
                
            # Refine the solution
            refined_solution = self._apply_refinement(problem, current_solution, critique)
            
            # Check if refinement made a significant improvement
            if self._solutions_very_similar(current_solution, refined_solution):
                self.logger.info("Refinement made minimal changes, stopping iterations")
                break
                
            current_solution = refined_solution
        
        # Return final refined solution
        return current_solution
    
    def _apply_refinement(self, problem: str, solution: str, critique: Critique) -> str:
        """
        Apply refinement to a solution based on critique.
        
        Args:
            problem: The problem statement
            solution: The current reasoning solution
            critique: The generated critique
            
        Returns:
            Refined reasoning solution
        """
        if self.language_model:
            return self._refine_with_language_model(problem, solution, critique)
        else:
            return self._refine_with_rules(solution, critique)
    
    def _refine_with_language_model(self, problem: str, solution: str, critique: Critique) -> str:
        """
        Refine a solution using a language model.
        
        Args:
            problem: The problem statement
            solution: The current reasoning solution
            critique: The generated critique
            
        Returns:
            Refined reasoning solution
        """
        # Extract key issues and improvements
        issues = []
        for elem in critique.elements:
            issues.append(f"{elem.critique_type.name}: {elem.description}")
        
        issues_text = "\n".join(issues[:5])  # Limit to top 5 issues
        
        improvements_text = "\n".join([f"- {imp}" for imp in critique.improvement_priorities])
        
        prompt = f"""
        Revise the following reasoning solution to address the identified issues.
        
        Problem: {problem}
        
        Current solution:
        {solution}
        
        Issues to address:
        {issues_text}
        
        Suggested improvements:
        {improvements_text}
        
        Please provide a revised version of the reasoning that:
        1. Maintains the valid aspects of the original reasoning
        2. Addresses the identified issues
        3. Incorporates the suggested improvements
        4. Ensures logical consistency and clarity throughout
        
        Revised solution:
        """
        
        try:
            response = self.language_model.generate(prompt)
            return response.strip()
        except Exception as e:
            self.logger.warning(f"Error refining solution with language model: {e}")
            return self._refine_with_rules(solution, critique)
    
    def _refine_with_rules(self, solution: str, critique: Critique) -> str:
        """
        Refine a solution using rule-based refinements.
        
        Args:
            solution: The current reasoning solution
            critique: The generated critique
            
        Returns:
            Refined reasoning solution
        """
        # Simple rule-based refinements
        lines = solution.split('\n')
        
        # Sort critique elements by severity
        sorted_elements = sorted(critique.elements, key=lambda e: e.severity, reverse=True)
        
        for elem in sorted_elements:
            # Try to identify the step to modify
            step_index = None
            if elem.location and "Step" in elem.location:
                try:
                    step_number = int(re.search(r'Step\s+(\d+)', elem.location).group(1))
                    step_index = step_number - 1
                except (AttributeError, ValueError):
                    step_index = None
            
            if step_index is not None and 0 <= step_index < len(lines):
                # Apply refinement based on critique type
                if elem.critique_type == CritiqueType.LOGICAL_FALLACY:
                    lines[step_index] = self._refine_fallacy(lines[step_index], elem)
                elif elem.critique_type == CritiqueType.EVIDENCE_GAP:
                    lines[step_index] = self._refine_evidence_gap(lines[step_index], elem)
                elif elem.critique_type == CritiqueType.BIAS:
                    lines[step_index] = self._refine_bias(lines[step_index], elem)
                elif elem.critique_type == CritiqueType.AMBIGUITY:
                    lines[step_index] = self._refine_ambiguity(lines[step_index], elem)
                elif elem.critique_type == CritiqueType.CONTRADICTION:
                    lines[step_index] = self._refine_contradiction(lines[step_index], elem)
        
        return '\n'.join(lines)
    
    def _refine_fallacy(self, text: str, critique_elem: CritiqueElement) -> str:
        """Refine text to address a logical fallacy."""
        # Simple refinement: append a caveat
        if critique_elem.improvement_suggestion:
            return f"{text} [Refined: {critique_elem.improvement_suggestion}]"
        else:
            return f"{text} [Refined to avoid logical fallacy]"
    
    def _refine_evidence_gap(self, text: str, critique_elem: CritiqueElement) -> str:
        """Refine text to address an evidence gap."""
        # Simple refinement: add evidence qualifier
        return f"{text} [Refined: This would benefit from additional supporting evidence.]"
    
    def _refine_bias(self, text: str, critique_elem: CritiqueElement) -> str:
        """Refine text to address a cognitive bias."""
        # Simple refinement: add bias awareness
        bias_type = critique_elem.metadata.get("bias_type", "cognitive bias")
        return f"{text} [Refined: Considering multiple perspectives to avoid {bias_type}.]"
    
    def _refine_ambiguity(self, text: str, critique_elem: CritiqueElement) -> str:
        """Refine text to address ambiguity."""
        # Simple refinement: add clarification note
        return f"{text} [Refined: To clarify, this refers specifically to the preceding point.]"
    
    def _refine_contradiction(self, text: str, critique_elem: CritiqueElement) -> str:
        """Refine text to address a contradiction."""
        # Simple refinement: add qualification
        return f"{text} [Refined: This applies only under specific conditions and should be qualified.]"
    
    def _solutions_very_similar(self, solution1: str, solution2: str) -> bool:
        """
        Determine if two solutions are very similar.
        
        Args:
            solution1: First solution
            solution2: Second solution
            
        Returns:
            True if solutions are very similar, False otherwise
        """
        # Remove whitespace and convert to lowercase for comparison
        s1 = re.sub(r'\s+', ' ', solution1).strip().lower()
        s2 = re.sub(r'\s+', ' ', solution2).strip().lower()
        
        # Calculate character-level difference ratio
        import difflib
        similarity = difflib.SequenceMatcher(None, s1, s2).ratio()
        
        # If solutions are more than 95% similar, consider them very similar
        return similarity > 0.95
    
    def get_critique_history(self) -> List[Dict[str, Any]]:
        """
        Get the history of critiques and refinements.
        
        Returns:
            List of critique history entries
        """
        return self.critique_history
    
    def calculate_improvement(self) -> Dict[str, Any]:
        """
        Calculate the improvement achieved through refinement.
        
        Returns:
            Dictionary with improvement metrics
        """
        if not self.critique_history:
            return {"improvement": 0.0}
            
        # Get initial and final scores
        initial_score = self.critique_history[0]["critique"]["overall_score"]
        final_score = self.critique_history[-1]["critique"]["overall_score"]
        
        # Calculate improvement
        improvement = final_score - initial_score
        improvement_percent = (improvement / max(0.01, initial_score)) * 100
        
        # Count issues by type, initial and final
        initial_issues = {}
        for elem in self.critique_history[0]["critique"]["elements"]:
            critique_type = elem["critique_type"]
            if critique_type not in initial_issues:
                initial_issues[critique_type] = 0
            initial_issues[critique_type] += 1
        
        final_issues = {}
        for elem in self.critique_history[-1]["critique"]["elements"]:
            critique_type = elem["critique_type"]
            if critique_type not in final_issues:
                final_issues[critique_type] = 0
            final_issues[critique_type] += 1
        
        # Calculate issue reduction by type
        issue_reduction = {}
        for issue_type, count in initial_issues.items():
            final_count = final_issues.get(issue_type, 0)
            reduction = count - final_count
            reduction_percent = (reduction / count) * 100 if count > 0 else 0
            issue_reduction[issue_type] = {
                "initial_count": count,
                "final_count": final_count,
                "reduction": reduction,
                "reduction_percent": reduction_percent
            }
        
        return {
            "initial_score": initial_score,
            "final_score": final_score,
            "improvement": improvement,
            "improvement_percent": improvement_percent,
            "iterations": len(self.critique_history),
            "issue_reduction": issue_reduction
        }
    
    def visualize_improvement(self, output_path: Optional[str] = None) -> None:
        """
        Visualize the improvement achieved through refinement.
        
        Args:
            output_path: Path to save the visualization (if None, display instead)
        """
        try:
            import matplotlib.pyplot as plt
            
            if not self.critique_history:
                self.logger.warning("No critique history to visualize")
                return
                
            # Extract scores by iteration
            iterations = [entry["iteration"] for entry in self.critique_history]
            scores = [entry["critique"]["overall_score"] for entry in self.critique_history]
            
            # Count issues by type and iteration
            issue_counts = {}
            for entry in self.critique_history:
                iteration = entry["iteration"]
                
                if iteration not in issue_counts:
                    issue_counts[iteration] = {}
                    
                for elem in entry["critique"]["elements"]:
                    critique_type = elem["critique_type"]
                    if critique_type not in issue_counts[iteration]:
                        issue_counts[iteration][critique_type] = 0
                    issue_counts[iteration][critique_type] += 1
            
            # Create figure with subplots
            fig, axs = plt.subplots(2, 1, figsize=(10, 12))
            
            # Plot 1: Overall scores by iteration
            axs[0].plot(iterations, scores, 'o-', linewidth=2)
            axs[0].set_xlabel('Iteration')
            axs[0].set_ylabel('Overall Score')
            axs[0].set_title('Reasoning Quality Improvement')
            axs[0].grid(True)
            
            # Plot 2: Issue counts by type and iteration
            issue_types = set()
            for counts in issue_counts.values():
                issue_types.update(counts.keys())
                
            issue_types = sorted(list(issue_types))
            
            # Prepare data for stacked bar chart
            issue_data = {}
            for issue_type in issue_types:
                issue_data[issue_type] = [issue_counts.get(it, {}).get(issue_type, 0) for it in iterations]
                
            # Create stacked bar chart
            bottom = np.zeros(len(iterations))
            for issue_type in issue_types:
                axs[1].bar(iterations, issue_data[issue_type], bottom=bottom, label=issue_type)
                bottom += issue_data[issue_type]
                
            axs[1].set_xlabel('Iteration')
            axs[1].set_ylabel('Issue Count')
            axs[1].set_title('Issue Reduction by Type')
            axs[1].legend()
            
            plt.tight_layout()
            
            # Save or display visualization
            if output_path:
                plt.savefig(output_path)
                plt.close()
            else:
                plt.show()
                
        except ImportError as e:
            self.logger.warning(f"Visualization requires matplotlib: {e}")
        except Exception as e:
            self.logger.error(f"Error in visualization: {e}")

# Make key classes available at module level
__all__ = [
    'SelfCritiqueLoop',
    'Critique',
    'CritiqueElement',
    'CritiqueType',
    'CritiqueTechnique',
    'LogicalFallacyDetector',
    'EvidenceGapDetector',
    'BiasDetector',
    'AmbiguityDetector',
    'ContradictionDetector',
    'CombinationCritiqueTechnique'
]

# Optional initialization function to create a default instance
def create_default_self_critique(language_model=None):
    """
    Create a default SelfCritiqueLoop instance.
    
    Args:
        language_model: Optional language model for critique generation and refinement
        
    Returns:
        Configured SelfCritiqueLoop instance
    """
    return SelfCritiqueLoop(
        language_model=language_model,
        iterations=3
    )

# Main execution for standalone usage and testing
if __name__ == "__main__":
    # Example usage
    problem = "Determine if increasing interest rates will reduce inflation."
    
    # Create a mock language model for testing
    class MockLanguageModel:
        def generate(self, prompt):
            if "critique" in prompt.lower():
                return """
                LOGICAL_FALLACY: Oversimplification
                LOCATION: Step 2
                DESCRIPTION: The reasoning assumes a direct causal relationship without considering other economic factors
                SEVERITY: 0.8
                IMPROVEMENT: Consider the complex interplay of multiple economic factors
                
                EVIDENCE_GAP: Insufficient support for key claim
                LOCATION: Step 3
                DESCRIPTION: The claim about spending reduction lacks specific evidence
                SEVERITY: 0.7
                IMPROVEMENT: Provide empirical evidence or economic theory references
                """
            elif "strengths" in prompt.lower():
                return """
                1. The reasoning correctly identifies the relationship between interest rates and borrowing costs.
                2. The logic connecting reduced spending to lower demand is sound.
                3. The overall structure of the argument follows economic principles.
                """
            elif "revise" in prompt.lower():
                return """
                1. When central banks increase interest rates, the cost of borrowing money increases for both consumers and businesses.
                
                2. With higher borrowing costs, consumers and businesses tend to reduce spending. However, this relationship is influenced by numerous factors including consumer confidence, employment rates, existing debt levels, and expectations about future economic conditions.
                
                3. Reduced spending typically leads to lower aggregate demand in the economy. Economic research, such as studies by the Federal Reserve, has shown that consumer spending often decreases by 0.5-1.5% for each percentage point increase in interest rates, though the effect varies by sector.
                
                4. When demand decreases while supply remains constant, prices tend to fall or rise more slowly according to basic economic principles.
                
                5. Therefore, increasing interest rates can help reduce inflation through this demand-reduction mechanism, though the effectiveness depends on the specific economic context and may involve time lags of 6-18 months before full effects are observed.
                """
            else:
                return "Generated response"
    
    # Initial reasoning solution
    initial_solution = """
    1. When interest rates increase, borrowing money becomes more expensive.
    
    2. Because borrowing is more expensive, people and businesses will spend less money.
    
    3. When people spend less money, demand for goods and services decreases.
    
    4. When demand decreases, prices fall.
    
    5. Therefore, increasing interest rates will reduce inflation.
    """
    
    # Create a self-critique loop
    critique_loop = SelfCritiqueLoop(
        language_model=MockLanguageModel(),
        iterations=2
    )
    
    # Generate critique
    critique = critique_loop.generate_critique(problem, initial_solution)
    print("Critique Summary:")
    print(critique.summarize())
    
    # Refine the reasoning
    refined_solution = critique_loop.refine_reasoning(problem, initial_solution)
    print("\nRefined Solution:")
    print(refined_solution)
    
    # Calculate improvement
    improvement = critique_loop.calculate_improvement()
    print("\nImprovement Metrics:")
    for key, value in improvement.items():
        if isinstance(value, dict):
            print(f"{key}:")
            for subkey, subvalue in value.items():
                print(f"  {subkey}: {subvalue}")
        else:
            print(f"{key}: {value}")