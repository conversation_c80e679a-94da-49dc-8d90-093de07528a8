#!/usr/bin/env python3
#!/usr/bin/env python3
"""
ULTRA Meta-Cognitive System: Meta-Learning Controller

This module implements the Meta-Learning Controller component of the ULTRA Meta-Cognitive System.
The Meta-Learning Controller adapts reasoning strategies based on problem characteristics and
past performance. It functions as the "learning to learn" component, extracting patterns from
reasoning experiences to improve future strategy selection and parameter tuning.

Key capabilities include:
1. Problem feature extraction and classification
2. Strategy selection based on similarity to past problems
3. Performance tracking and analysis
4. Dynamic adaptation of hyperparameters and reasoning approaches
5. Continual learning from reasoning experiences
"""

import os
import time
import math
import json
import logging
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Any, Optional, Union, Set, Callable, Iterator
from dataclasses import dataclass, field
from enum import Enum, auto
from collections import defaultdict, deque, Counter
import heapq
import pickle
import random
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.cluster import KMeans
from sklearn.decomposition import PCA

# Import ULTRA system components
from ultra.config import get_config, ULTRAConfigManager
from ultra.utils.ultra_logging import get_logger
from ultra.meta_cognitive.chain_of_thought import MultiPathChainOfThought, ReasoningPath, ReasoningStepType
from ultra.meta_cognitive.tree_of_thought import TreeOfThoughtExploration
from ultra.meta_cognitive.reasoning_graphs import ReasoningGraphs
from ultra.meta_cognitive.self_critique import SelfCritiqueLoop
from ultra.knowledge_management.episodic_knowledge import EpisodicKnowledgeBase

# Configure logger
logger = get_logger(__name__)

class ProblemDomain(Enum):
    """Categorization of problem domains for meta-learning."""
    MATHEMATICAL = auto()      # Mathematical problems (algebra, calculus, etc.)
    LOGICAL = auto()           # Logical problems (deduction, inference, etc.)
    SCIENTIFIC = auto()        # Scientific problems (physics, chemistry, biology, etc.)
    COMMONSENSE = auto()       # Commonsense reasoning problems
    CREATIVE = auto()          # Creative or open-ended problems
    FACTUAL = auto()           # Factual information retrieval
    PROCEDURAL = auto()        # Step-by-step procedures or how-to problems
    PLANNING = auto()          # Planning and decision-making problems
    ETHICAL = auto()           # Ethical or moral dilemmas
    UNDEFINED = auto()         # Problems that don't fit neatly into other categories

class ProblemComplexity(Enum):
    """Categorization of problem complexity for meta-learning."""
    SIMPLE = auto()            # Simple, straightforward problems
    MODERATE = auto()          # Moderately complex problems
    COMPLEX = auto()           # Complex problems requiring extensive reasoning
    VERY_COMPLEX = auto()      # Very complex problems requiring sophisticated approaches

@dataclass
class ProblemFeatures:
    """Features extracted from a problem statement for meta-learning."""
    # Text-based features
    length: int                           # Length of problem statement
    keyword_counts: Dict[str, int]        # Counts of key domain-specific words
    question_count: int                   # Number of questions in the problem
    constraint_count: int                 # Number of constraints identified
    
    # Semantic features
    domain: ProblemDomain                 # Problem domain
    complexity: ProblemComplexity         # Problem complexity
    embedding: Optional[np.ndarray] = None # Vector embedding of the problem
    
    # Structural features
    has_numerical_values: bool = False    # Whether the problem contains numerical values
    requires_calculation: bool = False    # Whether the problem requires calculations
    requires_external_knowledge: bool = False # Whether the problem needs external knowledge
    is_multi_step: bool = False           # Whether the problem requires multiple steps
    
    # Metadata
    metadata: Dict[str, Any] = field(default_factory=dict) # Additional metadata
    
    def get_feature_vector(self) -> np.ndarray:
        """Convert features to a numerical vector for similarity comparison."""
        # Combine numerical features
        numerical_features = [
            self.length,
            self.question_count,
            self.constraint_count,
            int(self.has_numerical_values),
            int(self.requires_calculation),
            int(self.requires_external_knowledge),
            int(self.is_multi_step)
        ]
        
        # Add encoded categorical features
        domain_encoding = [0] * len(ProblemDomain)
        domain_idx = list(ProblemDomain).index(self.domain)
        domain_encoding[domain_idx] = 1
        
        complexity_encoding = [0] * len(ProblemComplexity)
        complexity_idx = list(ProblemComplexity).index(self.complexity)
        complexity_encoding[complexity_idx] = 1
        
        # Combine all features
        feature_vector = numerical_features + domain_encoding + complexity_encoding
        
        return np.array(feature_vector)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "length": self.length,
            "keyword_counts": self.keyword_counts,
            "question_count": self.question_count,
            "constraint_count": self.constraint_count,
            "domain": self.domain.name,
            "complexity": self.complexity.name,
            "embedding": self.embedding.tolist() if self.embedding is not None else None,
            "has_numerical_values": self.has_numerical_values,
            "requires_calculation": self.requires_calculation,
            "requires_external_knowledge": self.requires_external_knowledge,
            "is_multi_step": self.is_multi_step,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ProblemFeatures':
        """Create a ProblemFeatures instance from a dictionary."""
        embedding = np.array(data["embedding"]) if data["embedding"] is not None else None
        
        return cls(
            length=data["length"],
            keyword_counts=data["keyword_counts"],
            question_count=data["question_count"],
            constraint_count=data["constraint_count"],
            domain=ProblemDomain[data["domain"]],
            complexity=ProblemComplexity[data["complexity"]],
            embedding=embedding,
            has_numerical_values=data["has_numerical_values"],
            requires_calculation=data["requires_calculation"],
            requires_external_knowledge=data["requires_external_knowledge"],
            is_multi_step=data["is_multi_step"],
            metadata=data["metadata"]
        )

@dataclass
class StrategyPerformance:
    """Performance metrics for a reasoning strategy on a problem."""
    strategy_name: str                      # Name of the reasoning strategy
    execution_time: float                   # Execution time in seconds
    confidence: float                       # Confidence in the solution (0-1)
    correctness: Optional[float] = None     # Correctness if ground truth available (0-1)
    reasoning_quality: float = 0.0          # Quality of the reasoning process (0-1)
    resource_efficiency: float = 0.0        # Efficiency of resource usage (0-1)
    
    # Components of reasoning quality
    coherence: float = 0.0                 # Logical coherence of reasoning
    relevance: float = 0.0                 # Relevance to the original problem
    completeness: float = 0.0              # Completeness of the reasoning
    
    # Additional metrics
    error_recovery: float = 0.0            # Ability to recover from errors
    creative_problem_solving: float = 0.0  # Creativity in problem-solving
    
    # Metadata
    additional_metrics: Dict[str, float] = field(default_factory=dict) # Additional metrics
    
    def get_overall_score(self) -> float:
        """Calculate an overall performance score (0-1)."""
        # Combine core metrics with appropriate weights
        weighted_sum = (
            0.3 * (self.correctness if self.correctness is not None else self.confidence) +
            0.3 * self.reasoning_quality +
            0.2 * self.resource_efficiency +
            0.1 * self.error_recovery +
            0.1 * self.creative_problem_solving
        )
        
        return min(1.0, max(0.0, weighted_sum))
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "strategy_name": self.strategy_name,
            "execution_time": self.execution_time,
            "confidence": self.confidence,
            "correctness": self.correctness,
            "reasoning_quality": self.reasoning_quality,
            "resource_efficiency": self.resource_efficiency,
            "coherence": self.coherence,
            "relevance": self.relevance,
            "completeness": self.completeness,
            "error_recovery": self.error_recovery,
            "creative_problem_solving": self.creative_problem_solving,
            "additional_metrics": self.additional_metrics,
            "overall_score": self.get_overall_score()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'StrategyPerformance':
        """Create a StrategyPerformance instance from a dictionary."""
        return cls(
            strategy_name=data["strategy_name"],
            execution_time=data["execution_time"],
            confidence=data["confidence"],
            correctness=data["correctness"],
            reasoning_quality=data["reasoning_quality"],
            resource_efficiency=data["resource_efficiency"],
            coherence=data["coherence"],
            relevance=data["relevance"],
            completeness=data["completeness"],
            error_recovery=data["error_recovery"],
            creative_problem_solving=data["creative_problem_solving"],
            additional_metrics=data["additional_metrics"]
        )

@dataclass
class ReasoningExperience:
    """Record of a reasoning experience for meta-learning."""
    problem: str                                     # Original problem statement
    features: ProblemFeatures                        # Extracted problem features
    strategy_performances: Dict[str, StrategyPerformance] # Performance of each strategy
    best_strategy: str                               # Best performing strategy
    timestamp: float                                 # Time when the experience was recorded
    
    # Outcomes
    solution: Optional[str] = None                   # Final solution reached
    reasoning_path: Optional[List[str]] = None       # Steps in the reasoning process
    
    # Metadata
    context: Dict[str, Any] = field(default_factory=dict) # Additional context
    tags: List[str] = field(default_factory=list)    # Tags for categorization
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "problem": self.problem,
            "features": self.features.to_dict(),
            "strategy_performances": {k: v.to_dict() for k, v in self.strategy_performances.items()},
            "best_strategy": self.best_strategy,
            "timestamp": self.timestamp,
            "solution": self.solution,
            "reasoning_path": self.reasoning_path,
            "context": self.context,
            "tags": self.tags
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ReasoningExperience':
        """Create a ReasoningExperience instance from a dictionary."""
        features = ProblemFeatures.from_dict(data["features"])
        strategy_performances = {
            k: StrategyPerformance.from_dict(v) for k, v in data["strategy_performances"].items()
        }
        
        return cls(
            problem=data["problem"],
            features=features,
            strategy_performances=strategy_performances,
            best_strategy=data["best_strategy"],
            timestamp=data["timestamp"],
            solution=data["solution"],
            reasoning_path=data["reasoning_path"],
            context=data["context"],
            tags=data["tags"]
        )

class ExperienceMemory:
    """
    Memory system for storing and retrieving reasoning experiences for meta-learning.
    
    This component maintains a database of past reasoning experiences, enabling efficient
    storage, retrieval, and analysis of reasoning performance across different problems
    and strategies.
    """
    
    def __init__(self, capacity: int = 1000):
        """
        Initialize the experience memory.
        
        Args:
            capacity: Maximum number of experiences to store
        """
        self.capacity = capacity
        self.experiences = deque(maxlen=capacity)
        self.experience_index = {}  # Maps problem hash to experience index
        
        # Initialize vectorizers for text similarity
        self.problem_vectorizer = TfidfVectorizer(max_features=1000)
        self.problem_vectors = None
        
        # Initialize clustering for experience organization
        self.clusterer = None
        self.cluster_labels = None
        
        # Domain-specific keyword lists for feature extraction
        self.domain_keywords = self._initialize_domain_keywords()
    
    def _initialize_domain_keywords(self) -> Dict[ProblemDomain, List[str]]:
        """Initialize domain-specific keywords for feature extraction."""
        return {
            ProblemDomain.MATHEMATICAL: [
                "calculate", "compute", "solve", "equation", "function", "derivative",
                "integral", "algebra", "geometry", "calculus", "probability", "statistics",
                "matrix", "vector", "theorem", "proof", "formula", "variable"
            ],
            ProblemDomain.LOGICAL: [
                "logic", "deduce", "infer", "premise", "conclusion", "valid", "invalid",
                "argument", "fallacy", "contradiction", "implication", "syllogism",
                "necessary", "sufficient", "condition", "statement", "proposition"
            ],
            ProblemDomain.SCIENTIFIC: [
                "experiment", "hypothesis", "theory", "observation", "data", "evidence",
                "research", "results", "analysis", "method", "procedure", "investigation",
                "physics", "chemistry", "biology", "experiment", "measure", "laboratory"
            ],
            ProblemDomain.COMMONSENSE: [
                "everyday", "common", "usual", "typical", "normal", "regular", "routine",
                "practical", "sensible", "reasonable", "rational", "experience", "intuition"
            ],
            ProblemDomain.CREATIVE: [
                "create", "design", "develop", "invent", "imagine", "novel", "original",
                "innovative", "artistic", "creative", "inspiration", "idea", "concept"
            ],
            ProblemDomain.FACTUAL: [
                "fact", "information", "data", "detail", "specific", "identify", "list",
                "enumerate", "when", "where", "who", "what", "which", "historical", "date"
            ],
            ProblemDomain.PROCEDURAL: [
                "step", "procedure", "process", "method", "approach", "technique", "protocol",
                "algorithm", "instruction", "guide", "how to", "implement", "execute"
            ],
            ProblemDomain.PLANNING: [
                "plan", "goal", "objective", "strategy", "tactic", "decision", "choice",
                "option", "alternative", "optimal", "prioritize", "schedule", "sequence"
            ],
            ProblemDomain.ETHICAL: [
                "ethical", "moral", "value", "principle", "right", "wrong", "good", "bad",
                "fair", "just", "unjust", "virtue", "vice", "duty", "obligation", "dilemma"
            ]
        }
    
    def add_experience(self, experience: ReasoningExperience) -> None:
        """
        Add a reasoning experience to memory.
        
        Args:
            experience: The reasoning experience to add
        """
        # Generate a hash for the problem
        problem_hash = hash(experience.problem)
        
        # If this problem already exists, update instead of adding
        if problem_hash in self.experience_index:
            idx = self.experience_index[problem_hash]
            self.experiences[idx] = experience
        else:
            # Add new experience
            self.experiences.append(experience)
            self.experience_index[problem_hash] = len(self.experiences) - 1
            
        # Invalidate cached vectors and clusters
        self.problem_vectors = None
        self.cluster_labels = None
    
    def get_experience(self, problem: str) -> Optional[ReasoningExperience]:
        """
        Retrieve an experience by problem statement.
        
        Args:
            problem: The problem statement to retrieve
            
        Returns:
            The retrieved experience, or None if not found
        """
        problem_hash = hash(problem)
        if problem_hash in self.experience_index:
            idx = self.experience_index[problem_hash]
            return self.experiences[idx]
        return None
    
    def get_similar_experiences(
        self, 
        problem: str, 
        features: Optional[ProblemFeatures] = None,
        k: int = 5,
        similarity_threshold: float = 0.3
    ) -> List[Tuple[ReasoningExperience, float]]:
        """
        Retrieve experiences similar to a problem.
        
        Args:
            problem: The problem statement to find similar experiences for
            features: Optional extracted features for more accurate similarity
            k: Maximum number of similar experiences to retrieve
            similarity_threshold: Minimum similarity threshold
            
        Returns:
            List of (experience, similarity) tuples
        """
        # If no experiences yet, return empty list
        if not self.experiences:
            return []
            
        # Calculate similarity based on problem text
        text_similarities = self._calculate_text_similarity(problem)
        
        # If features are provided, also calculate feature similarity
        if features is not None:
            feature_similarities = self._calculate_feature_similarity(features)
            
            # Combine text and feature similarities (average)
            similarities = [
                (idx, (text_sim + feature_similarities[idx]) / 2)
                for idx, text_sim in enumerate(text_similarities)
            ]
        else:
            similarities = [(idx, sim) for idx, sim in enumerate(text_similarities)]
        
        # Filter by similarity threshold and sort by descending similarity
        similar_experiences = [
            (self.experiences[idx], sim)
            for idx, sim in similarities
            if sim >= similarity_threshold
        ]
        similar_experiences.sort(key=lambda x: x[1], reverse=True)
        
        # Return top-k similar experiences
        return similar_experiences[:k]
    
    def _calculate_text_similarity(self, problem: str) -> List[float]:
        """
        Calculate text-based similarity between a problem and all stored experiences.
        
        Args:
            problem: The problem statement to calculate similarity for
            
        Returns:
            List of similarity scores corresponding to stored experiences
        """
        # Extract problem texts from experiences
        problems = [exp.problem for exp in self.experiences]
        
        # If no problem vectors yet, compute them
        if self.problem_vectors is None:
            # Add the new problem to ensure it's in the vocabulary
            all_problems = problems + [problem]
            self.problem_vectorizer.fit(all_problems)
            self.problem_vectors = self.problem_vectorizer.transform(problems)
        
        # Vectorize the query problem
        problem_vector = self.problem_vectorizer.transform([problem])
        
        # Calculate cosine similarity
        similarities = cosine_similarity(problem_vector, self.problem_vectors)[0]
        
        return similarities.tolist()
    
    def _calculate_feature_similarity(self, features: ProblemFeatures) -> List[float]:
        """
        Calculate feature-based similarity between a problem and all stored experiences.
        
        Args:
            features: The problem features to calculate similarity for
            
        Returns:
            List of similarity scores corresponding to stored experiences
        """
        # Extract feature vectors from experiences
        exp_features = [exp.features.get_feature_vector() for exp in self.experiences]
        
        # Get query feature vector
        query_features = features.get_feature_vector()
        
        # Calculate cosine similarity
        similarities = []
        for feat in exp_features:
            # If feature vectors have different lengths (due to different enums),
            # pad the shorter one with zeros
            if len(feat) != len(query_features):
                if len(feat) < len(query_features):
                    feat = np.pad(feat, (0, len(query_features) - len(feat)))
                else:
                    query_padded = np.pad(query_features, (0, len(feat) - len(query_features)))
                    similarity = cosine_similarity([query_padded], [feat])[0][0]
                    similarities.append(similarity)
                    continue
                    
            similarity = cosine_similarity([query_features], [feat])[0][0]
            similarities.append(similarity)
        
        return similarities
    
    def cluster_experiences(self, n_clusters: int = 10) -> Dict[int, List[int]]:
        """
        Cluster stored experiences into groups.
        
        Args:
            n_clusters: Number of clusters to create
            
        Returns:
            Dictionary mapping cluster IDs to experience indices
        """
        # If no experiences, return empty dict
        if not self.experiences:
            return {}
        
        # Ensure problem vectors are computed
        if self.problem_vectors is None:
            problems = [exp.problem for exp in self.experiences]
            self.problem_vectorizer.fit(problems)
            self.problem_vectors = self.problem_vectorizer.transform(problems)
        
        # Apply dimensionality reduction if necessary
        if self.problem_vectors.shape[1] > 100:
            pca = PCA(n_components=min(100, self.problem_vectors.shape[0]))
            problem_vectors_reduced = pca.fit_transform(self.problem_vectors.toarray())
        else:
            problem_vectors_reduced = self.problem_vectors.toarray()
        
        # Apply K-means clustering
        self.clusterer = KMeans(n_clusters=min(n_clusters, len(self.experiences)))
        self.cluster_labels = self.clusterer.fit_predict(problem_vectors_reduced)
        
        # Organize experiences by cluster
        clusters = defaultdict(list)
        for i, label in enumerate(self.cluster_labels):
            clusters[int(label)].append(i)
        
        return dict(clusters)
    
    def analyze_strategy_performance(self) -> Dict[str, Dict[ProblemDomain, float]]:
        """
        Analyze strategy performance across different problem domains.
        
        Returns:
            Dictionary mapping strategy names to their performance in each domain
        """
        # Initialize performance tracking
        strategy_domain_scores = defaultdict(lambda: defaultdict(list))
        
        # Collect performance by strategy and domain
        for exp in self.experiences:
            domain = exp.features.domain
            for strategy_name, performance in exp.strategy_performances.items():
                strategy_domain_scores[strategy_name][domain].append(performance.get_overall_score())
        
        # Calculate average performance by strategy and domain
        result = {}
        for strategy_name, domain_scores in strategy_domain_scores.items():
            result[strategy_name] = {}
            for domain, scores in domain_scores.items():
                if scores:
                    result[strategy_name][domain] = sum(scores) / len(scores)
        
        return result
    
    def extract_problem_features(self, problem: str, language_model=None) -> ProblemFeatures:
        """
        Extract features from a problem statement.
        
        Args:
            problem: The problem statement to extract features from
            language_model: Optional language model for advanced feature extraction
            
        Returns:
            Extracted problem features
        """
        # Basic text features
        words = problem.split()
        length = len(words)
        question_count = problem.count('?')
        
        # Count domain-specific keywords
        keyword_counts = {}
        for domain, keywords in self.domain_keywords.items():
            count = sum(1 for keyword in keywords if keyword.lower() in problem.lower())
            keyword_counts[domain.name] = count
        
        # Determine the likely domain
        domain_scores = {}
        for domain, keywords in self.domain_keywords.items():
            # Calculate the proportion of domain keywords present
            present_keywords = sum(1 for keyword in keywords if keyword.lower() in problem.lower())
            normalized_score = present_keywords / max(1, len(keywords))
            domain_scores[domain] = normalized_score
        
        likely_domain = max(domain_scores.items(), key=lambda x: x[1])[0]
        if max(domain_scores.values()) < 0.1:
            likely_domain = ProblemDomain.UNDEFINED
        
        # Check for numerical values
        has_numerical_values = any(word.replace('.', '').isdigit() for word in words)
        
        # Check for multiple steps
        is_multi_step = length > 50 or question_count > 1
        
        # Determine complexity based on length, question count, and other features
        if length < 30 and question_count <= 1 and not is_multi_step:
            complexity = ProblemComplexity.SIMPLE
        elif length < 100 and question_count <= 2:
            complexity = ProblemComplexity.MODERATE
        elif length < 200:
            complexity = ProblemComplexity.COMPLEX
        else:
            complexity = ProblemComplexity.VERY_COMPLEX
        
        # Use language model for advanced feature extraction if available
        constraint_count = 0
        requires_calculation = has_numerical_values
        requires_external_knowledge = False
        
        if language_model:
            try:
                # Use LM to identify constraints
                prompt = f"""
                Analyze the following problem and identify how many distinct constraints or requirements it contains:
                
                Problem: {problem}
                
                Number of constraints:
                """
                
                response = language_model.generate(prompt).strip()
                constraint_count = int(response) if response.isdigit() else 0
                
                # Use LM to determine if calculation is required
                prompt = f"""
                Does solving the following problem require mathematical calculations? Answer YES or NO.
                
                Problem: {problem}
                
                Answer:
                """
                
                response = language_model.generate(prompt).strip().upper()
                requires_calculation = "YES" in response
                
                # Use LM to determine if external knowledge is required
                prompt = f"""
                Does solving the following problem require specialized knowledge or information not contained in the problem statement itself? Answer YES or NO.
                
                Problem: {problem}
                
                Answer:
                """
                
                response = language_model.generate(prompt).strip().upper()
                requires_external_knowledge = "YES" in response
                
                # Use LM to refine complexity assessment
                prompt = f"""
                Rate the complexity of the following problem on a scale from 1-4, where:
                1 = Simple (straightforward, one-step solution)
                2 = Moderate (requires a few steps but no complex reasoning)
                3 = Complex (requires multi-step reasoning and careful analysis)
                4 = Very Complex (requires sophisticated problem-solving techniques)
                
                Problem: {problem}
                
                Complexity rating (1-4):
                """
                
                response = language_model.generate(prompt).strip()
                try:
                    complexity_rating = int(response)
                    if 1 <= complexity_rating <= 4:
                        complexity = list(ProblemComplexity)[complexity_rating - 1]
                except ValueError:
                    pass  # Keep the default complexity assessment
            
            except Exception as e:
                logger.warning(f"Error in language model feature extraction: {e}")
        
        # Create and return the feature object
        return ProblemFeatures(
            length=length,
            keyword_counts=keyword_counts,
            question_count=question_count,
            constraint_count=constraint_count,
            domain=likely_domain,
            complexity=complexity,
            has_numerical_values=has_numerical_values,
            requires_calculation=requires_calculation,
            requires_external_knowledge=requires_external_knowledge,
            is_multi_step=is_multi_step
        )
    
    def save(self, filepath: str) -> bool:
        """
        Save the experience memory to a file.
        
        Args:
            filepath: Path to save the experience memory to
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Convert experiences to serializable format
            data = {
                "capacity": self.capacity,
                "experiences": [exp.to_dict() for exp in self.experiences]
            }
            
            # Save to file
            with open(filepath, 'wb') as f:
                pickle.dump(data, f)
                
            return True
        except Exception as e:
            logger.error(f"Error saving experience memory: {e}")
            return False
    
    def load(self, filepath: str) -> bool:
        """
        Load the experience memory from a file.
        
        Args:
            filepath: Path to load the experience memory from
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Load from file
            with open(filepath, 'rb') as f:
                data = pickle.load(f)
                
            # Restore data
            self.capacity = data["capacity"]
            self.experiences = deque(
                [ReasoningExperience.from_dict(exp_data) for exp_data in data["experiences"]],
                maxlen=self.capacity
            )
            
            # Rebuild index
            self.experience_index = {
                hash(exp.problem): i for i, exp in enumerate(self.experiences)
            }
            
            # Invalidate cached vectors and clusters
            self.problem_vectors = None
            self.cluster_labels = None
            
            return True
        except Exception as e:
            logger.error(f"Error loading experience memory: {e}")
            return False

class StrategySelector:
    """
    Selects reasoning strategies based on problem characteristics and past performance.
    
    This component learns to map problem features to optimal reasoning strategies
    based on analysis of past experiences and performance metrics.
    """
    
    def __init__(
        self, 
        reasoning_modules: Dict[str, Any], 
        experience_memory: ExperienceMemory,
        exploration_rate: float = 0.2
    ):
        """
        Initialize the strategy selector.
        
        Args:
            reasoning_modules: Dictionary mapping strategy names to reasoning modules
            experience_memory: Memory of past reasoning experiences
            exploration_rate: Rate of exploration vs. exploitation (0-1)
        """
        self.reasoning_modules = reasoning_modules
        self.experience_memory = experience_memory
        self.exploration_rate = exploration_rate
        
        # Initialize performance tracking
        self.strategy_performance = {
            strategy_name: {
                "successes": 0,
                "failures": 0,
                "total_score": 0.0,
                "count": 0
            }
            for strategy_name in reasoning_modules.keys()
        }
        
        # Initialize strategy-domain mapping
        self.strategy_domain_scores = {}
        
        # Initialize strategy feature models
        self.strategy_feature_weights = self._initialize_feature_weights()
    
    def _initialize_feature_weights(self) -> Dict[str, Dict[str, float]]:
        """
        Initialize feature weights for strategy selection.
        
        Returns:
            Dictionary mapping strategy names to feature weight dictionaries
        """
        feature_weights = {}
        for strategy_name in self.reasoning_modules.keys():
            # Initialize random weights for features
            weights = {
                "length": random.uniform(-0.1, 0.1),
                "question_count": random.uniform(-0.1, 0.1),
                "constraint_count": random.uniform(-0.1, 0.1),
                "has_numerical_values": random.uniform(-0.1, 0.1),
                "requires_calculation": random.uniform(-0.1, 0.1),
                "requires_external_knowledge": random.uniform(-0.1, 0.1),
                "is_multi_step": random.uniform(-0.1, 0.1)
            }
            
            # Initialize weights for each domain
            for domain in ProblemDomain:
                weights[f"domain_{domain.name}"] = random.uniform(-0.1, 0.1)
                
            # Initialize weights for each complexity level
            for complexity in ProblemComplexity:
                weights[f"complexity_{complexity.name}"] = random.uniform(-0.1, 0.1)
                
            feature_weights[strategy_name] = weights
        
        return feature_weights
    
    def select_strategy(
        self, 
        problem: str, 
        features: Optional[ProblemFeatures] = None, 
        context: Optional[Dict[str, Any]] = None, 
        constraints: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Select the best reasoning strategy for a problem.
        
        Args:
            problem: The problem to select a strategy for
            features: Optional extracted problem features
            context: Optional context information
            constraints: Optional constraints on strategy selection
            
        Returns:
            Name of the selected strategy
        """
        # Extract features if not provided
        if features is None:
            language_model = context.get("language_model") if context else None
            features = self.experience_memory.extract_problem_features(problem, language_model)
        
        # Apply exploration-exploitation strategy
        if random.random() < self.exploration_rate:
            # Exploration: try a random strategy
            return self._explore_strategy(features, constraints)
        else:
            # Exploitation: use past experience to select the best strategy
            return self._exploit_strategy(problem, features, constraints)
    
    def _explore_strategy(
        self, 
        features: ProblemFeatures, 
        constraints: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Explore a strategy that hasn't been tried much for this type of problem.
        
        Args:
            features: Extracted problem features
            constraints: Optional constraints on strategy selection
            
        Returns:
            Name of the selected strategy
        """
        # Get available strategies after applying constraints
        available_strategies = self._filter_strategies_by_constraints(constraints)
        if not available_strategies:
            available_strategies = list(self.reasoning_modules.keys())
        
        # Get trial counts for each strategy
        strategy_trials = {
            strategy_name: self.strategy_performance[strategy_name]["count"] 
            for strategy_name in available_strategies
        }
        
        # Strategies with fewest trials get higher probability
        inverse_counts = {
            s: 1.0 / (count + 1) for s, count in strategy_trials.items()
        }
        
        # Normalize to create a probability distribution
        total = sum(inverse_counts.values())
        probabilities = {
            s: count / total for s, count in inverse_counts.items()
        }
        
        # Select strategy based on probability distribution
        strategies = list(probabilities.keys())
        probs = [probabilities[s] for s in strategies]
        
        return np.random.choice(strategies, p=probs)
    
    def _exploit_strategy(
        self, 
        problem: str, 
        features: ProblemFeatures, 
        constraints: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Exploit past experience to select the best strategy.
        
        Args:
            problem: The problem to select a strategy for
            features: Extracted problem features
            constraints: Optional constraints on strategy selection
            
        Returns:
            Name of the selected strategy
        """
        # Get available strategies after applying constraints
        available_strategies = self._filter_strategies_by_constraints(constraints)
        if not available_strategies:
            available_strategies = list(self.reasoning_modules.keys())
        
        # Find similar problems and their best strategies
        similar_experiences = self.experience_memory.get_similar_experiences(
            problem=problem,
            features=features,
            k=5,
            similarity_threshold=0.3
        )
        
        # If we have similar experiences, leverage them
        if similar_experiences:
            # Count strategy successes in similar problems
            strategy_counts = Counter()
            strategy_similarities = defaultdict(float)
            
            for experience, similarity in similar_experiences:
                # Only consider strategies that are currently available
                if experience.best_strategy in available_strategies:
                    strategy_counts[experience.best_strategy] += 1
                    strategy_similarities[experience.best_strategy] += similarity
            
            # If we have relevant strategy data, use it
            if strategy_counts:
                # Calculate weighted score based on count and similarity
                strategy_scores = {
                    strategy: count * strategy_similarities[strategy] / sum(strategy_counts.values())
                    for strategy, count in strategy_counts.items()
                }
                
                # Return the best strategy
                return max(strategy_scores.items(), key=lambda x: x[1])[0]
        
        # If no similar experiences or none with available strategies,
        # use feature-based selection
        return self._select_by_features(features, available_strategies)
    
    def _select_by_features(
        self, 
        features: ProblemFeatures, 
        available_strategies: List[str]
    ) -> str:
        """
        Select a strategy based on problem features.
        
        Args:
            features: Extracted problem features
            available_strategies: List of available strategies
            
        Returns:
            Name of the selected strategy
        """
        # Calculate score for each strategy based on feature weights
        strategy_scores = {}
        
        for strategy_name in available_strategies:
            if strategy_name in self.strategy_feature_weights:
                weights = self.strategy_feature_weights[strategy_name]
                
                # Calculate score as weighted sum of features
                score = 0.0
                
                # Numerical features
                score += weights["length"] * min(1.0, features.length / 200)
                score += weights["question_count"] * min(1.0, features.question_count / 5)
                score += weights["constraint_count"] * min(1.0, features.constraint_count / 5)
                
                # Boolean features
                score += weights["has_numerical_values"] * (1.0 if features.has_numerical_values else 0.0)
                score += weights["requires_calculation"] * (1.0 if features.requires_calculation else 0.0)
                score += weights["requires_external_knowledge"] * (1.0 if features.requires_external_knowledge else 0.0)
                score += weights["is_multi_step"] * (1.0 if features.is_multi_step else 0.0)
                
                # Domain feature
                domain_key = f"domain_{features.domain.name}"
                if domain_key in weights:
                    score += weights[domain_key]
                
                # Complexity feature
                complexity_key = f"complexity_{features.complexity.name}"
                if complexity_key in weights:
                    score += weights[complexity_key]
                
                strategy_scores[strategy_name] = score
        
        # If we have scores, return the strategy with the highest score
        if strategy_scores:
            return max(strategy_scores.items(), key=lambda x: x[1])[0]
        
        # Fallback to a random strategy
        return random.choice(available_strategies)
    
    def _filter_strategies_by_constraints(
        self, 
        constraints: Optional[Dict[str, Any]] = None
    ) -> List[str]:
        """
        Filter available strategies based on constraints.
        
        Args:
            constraints: Constraints on strategy selection
            
        Returns:
            List of available strategies after applying constraints
        """
        if not constraints:
            return list(self.reasoning_modules.keys())
        
        available_strategies = list(self.reasoning_modules.keys())
        
        # Filter by explicitly specified strategies
        if "strategies" in constraints:
            allowed_strategies = constraints["strategies"]
            available_strategies = [s for s in available_strategies if s in allowed_strategies]
        
        # Filter by time constraint
        if "max_time" in constraints:
            max_time = constraints["max_time"]
            # Filter out strategies that typically take longer than max_time
            available_strategies = [
                s for s in available_strategies
                if self._get_avg_execution_time(s) <= max_time
            ]
        
        # Filter by memory constraint
        if "max_memory" in constraints:
            max_memory = constraints["max_memory"]
            # Filter out strategies that typically use more memory than max_memory
            available_strategies = [
                s for s in available_strategies
                if self._get_avg_memory_usage(s) <= max_memory
            ]
        
        # If no strategies remain after filtering, return all strategies
        if not available_strategies:
            return list(self.reasoning_modules.keys())
        
        return available_strategies
    
    def _get_avg_execution_time(self, strategy_name: str) -> float:
        """
        Get the average execution time for a strategy.
        
        Args:
            strategy_name: Name of the strategy
            
        Returns:
            Average execution time in seconds
        """
        # Placeholder - in a real implementation, this would be calculated from experience data
        # Default execution times for different strategies
        default_times = {
            "CHAIN_OF_THOUGHT": 2.0,
            "TREE_OF_THOUGHT": 5.0,
            "REASONING_GRAPH": 4.0,
            "MULTI_PATH": 7.0,
            "SELF_CRITIQUE": 6.0,
            "HYBRID": 10.0
        }
        
        return default_times.get(strategy_name, 5.0)
    
    def _get_avg_memory_usage(self, strategy_name: str) -> float:
        """
        Get the average memory usage for a strategy.
        
        Args:
            strategy_name: Name of the strategy
            
        Returns:
            Average memory usage in MB
        """
        # Placeholder - in a real implementation, this would be calculated from experience data
        # Default memory usage for different strategies
        default_memory = {
            "CHAIN_OF_THOUGHT": 100.0,
            "TREE_OF_THOUGHT": 250.0,
            "REASONING_GRAPH": 200.0,
            "MULTI_PATH": 300.0,
            "SELF_CRITIQUE": 150.0,
            "HYBRID": 500.0
        }
        
        return default_memory.get(strategy_name, 200.0)
    
    def update_with_performance(
        self, 
        strategy_name: str, 
        performance: StrategyPerformance, 
        features: ProblemFeatures
    ) -> None:
        """
        Update the strategy selector with performance feedback.
        
        Args:
            strategy_name: Name of the strategy
            performance: Performance metrics for the strategy
            features: Features of the problem the strategy was applied to
        """
        # Update strategy performance tracking
        if strategy_name in self.strategy_performance:
            perf = self.strategy_performance[strategy_name]
            
            # Update success/failure counts
            overall_score = performance.get_overall_score()
            if overall_score >= 0.7:
                perf["successes"] += 1
            else:
                perf["failures"] += 1
                
            # Update total score and count
            perf["total_score"] += overall_score
            perf["count"] += 1
        
        # Update feature weights for the strategy
        if strategy_name in self.strategy_feature_weights:
            self._update_feature_weights(strategy_name, features, performance)
    
    def _update_feature_weights(
        self, 
        strategy_name: str, 
        features: ProblemFeatures, 
        performance: StrategyPerformance
    ) -> None:
        """
        Update feature weights for a strategy based on performance feedback.
        
        Args:
            strategy_name: Name of the strategy
            features: Features of the problem the strategy was applied to
            performance: Performance metrics for the strategy
        """
        # Get current weights
        weights = self.strategy_feature_weights[strategy_name]
        
        # Calculate performance score
        score = performance.get_overall_score()
        
        # If performance is good, increase weights for matching features
        # If performance is bad, decrease weights for matching features
        learning_rate = 0.05
        adjustment = (score - 0.5) * learning_rate  # -0.5 to +0.5 range
        
        # Update weights for numerical features
        weights["length"] += adjustment * min(1.0, features.length / 200)
        weights["question_count"] += adjustment * min(1.0, features.question_count / 5)
        weights["constraint_count"] += adjustment * min(1.0, features.constraint_count / 5)
        
        # Update weights for boolean features
        weights["has_numerical_values"] += adjustment * (1.0 if features.has_numerical_values else 0.0)
        weights["requires_calculation"] += adjustment * (1.0 if features.requires_calculation else 0.0)
        weights["requires_external_knowledge"] += adjustment * (1.0 if features.requires_external_knowledge else 0.0)
        weights["is_multi_step"] += adjustment * (1.0 if features.is_multi_step else 0.0)
        
        # Update weight for this domain
        domain_key = f"domain_{features.domain.name}"
        if domain_key in weights:
            weights[domain_key] += adjustment
        
        # Update weight for this complexity level
        complexity_key = f"complexity_{features.complexity.name}"
        if complexity_key in weights:
            weights[complexity_key] += adjustment
    
    def analyze_strategy_performance(self) -> Dict[str, Dict[str, float]]:
        """
        Analyze the performance of different strategies.
        
        Returns:
            Dictionary mapping strategy names to performance metrics
        """
        result = {}
        
        for strategy_name, perf in self.strategy_performance.items():
            if perf["count"] > 0:
                # Calculate performance metrics
                success_rate = perf["successes"] / max(1, perf["count"])
                avg_score = perf["total_score"] / max(1, perf["count"])
                
                result[strategy_name] = {
                    "success_rate": success_rate,
                    "avg_score": avg_score,
                    "count": perf["count"]
                }
        
        return result
    
    def get_strategy_domain_affinities(self) -> Dict[str, Dict[str, float]]:
        """
        Get the affinity of each strategy for different problem domains.
        
        Returns:
            Dictionary mapping strategy names to domain affinities
        """
        affinities = {}
        
        for strategy_name, weights in self.strategy_feature_weights.items():
            domain_weights = {
                domain.name: weights.get(f"domain_{domain.name}", 0.0)
                for domain in ProblemDomain
            }
            
            # Normalize weights to [0, 1] range
            min_weight = min(domain_weights.values())
            max_weight = max(domain_weights.values())
            
            if max_weight > min_weight:
                normalized_weights = {
                    domain: (weight - min_weight) / (max_weight - min_weight)
                    for domain, weight in domain_weights.items()
                }
            else:
                normalized_weights = {
                    domain: 0.5 for domain in domain_weights.keys()
                }
                
            affinities[strategy_name] = normalized_weights
        
        return affinities
    
    def get_strategy_complexity_affinities(self) -> Dict[str, Dict[str, float]]:
        """
        Get the affinity of each strategy for different problem complexity levels.
        
        Returns:
            Dictionary mapping strategy names to complexity affinities
        """
        affinities = {}
        
        for strategy_name, weights in self.strategy_feature_weights.items():
            complexity_weights = {
                complexity.name: weights.get(f"complexity_{complexity.name}", 0.0)
                for complexity in ProblemComplexity
            }
            
            # Normalize weights to [0, 1] range
            min_weight = min(complexity_weights.values())
            max_weight = max(complexity_weights.values())
            
            if max_weight > min_weight:
                normalized_weights = {
                    complexity: (weight - min_weight) / (max_weight - min_weight)
                    for complexity, weight in complexity_weights.items()
                }
            else:
                normalized_weights = {
                    complexity: 0.5 for complexity in complexity_weights.keys()
                }
                
            affinities[strategy_name] = normalized_weights
        
        return affinities

class HyperParameterOptimizer:
    """
    Optimizes hyperparameters for reasoning strategies based on performance feedback.
    
    This component adaptively tunes the parameters of different reasoning strategies
    to maximize performance across diverse problem domains.
    """
    
    def __init__(self, reasoning_modules: Dict[str, Any], learning_rate: float = 0.05):
        """
        Initialize the hyperparameter optimizer.
        
        Args:
            reasoning_modules: Dictionary mapping strategy names to reasoning modules
            learning_rate: Learning rate for hyperparameter updates
        """
        self.reasoning_modules = reasoning_modules
        self.learning_rate = learning_rate
        
        # Initialize hyperparameter tracking
        self.strategy_hyperparams = {
            strategy_name: self._extract_hyperparams(module)
            for strategy_name, module in reasoning_modules.items()
        }
        
        # Initialize performance tracking for different hyperparameter settings
        self.param_performance = defaultdict(list)
        
        # Initialize best hyperparameter settings
        self.best_hyperparams = {
            strategy_name: self._extract_hyperparams(module)
            for strategy_name, module in reasoning_modules.items()
        }
        self.best_performance = {
            strategy_name: 0.0
            for strategy_name in reasoning_modules.keys()
        }
    
    def _extract_hyperparams(self, module: Any) -> Dict[str, Any]:
        """
        Extract hyperparameters from a reasoning module.
        
        Args:
            module: The reasoning module to extract hyperparameters from
            
        Returns:
            Dictionary of hyperparameters
        """
        hyperparams = {}
        
        # Attempt to extract common hyperparameters
        if hasattr(module, 'max_paths'):
            hyperparams['max_paths'] = module.max_paths
        
        if hasattr(module, 'beam_width'):
            hyperparams['beam_width'] = module.beam_width
        
        if hasattr(module, 'max_iterations'):
            hyperparams['max_iterations'] = module.max_iterations
            
        if hasattr(module, 'exploration_rate'):
            hyperparams['exploration_rate'] = module.exploration_rate
            
        if hasattr(module, 'temperature'):
            hyperparams['temperature'] = module.temperature
            
        if hasattr(module, 'resource_allocator') and hasattr(module.resource_allocator, 'temperature'):
            hyperparams['allocator_temperature'] = module.resource_allocator.temperature
            
        if hasattr(module, 'path_evaluator') and hasattr(module.path_evaluator, 'weights'):
            hyperparams['evaluator_weights'] = module.path_evaluator.weights.copy()
            
        # Get any explicitly defined hyperparameters
        if hasattr(module, 'get_hyperparameters'):
            module_hyperparams = module.get_hyperparameters()
            hyperparams.update(module_hyperparams)
            
        return hyperparams
    
    def optimize(self, strategy_name: str, performance: StrategyPerformance, problem_features: ProblemFeatures) -> None:
        """
        Optimize hyperparameters for a strategy based on performance feedback.
        
        Args:
            strategy_name: Name of the strategy to optimize
            performance: Performance metrics for the strategy
            problem_features: Features of the problem the strategy was applied to
        """
        if strategy_name not in self.strategy_hyperparams or strategy_name not in self.reasoning_modules:
            return
            
        # Get current hyperparameters
        current_hyperparams = self.strategy_hyperparams[strategy_name]
        
        # Calculate performance score
        performance_score = performance.get_overall_score()
        
        # Record performance for this hyperparameter setting
        param_key = self._get_param_key(strategy_name, current_hyperparams)
        self.param_performance[param_key].append(performance_score)
        
        # Check if this is the best performance for this strategy
        if performance_score > self.best_performance[strategy_name]:
            self.best_performance[strategy_name] = performance_score
            self.best_hyperparams[strategy_name] = current_hyperparams.copy()
            
        # Every few examples, update hyperparameters based on performance
        if len(self.param_performance[param_key]) >= 3:
            avg_performance = sum(self.param_performance[param_key]) / len(self.param_performance[param_key])
            
            # If performance is good, make small adjustments to explore local optimum
            # If performance is bad, make larger adjustments to explore different areas
            if avg_performance > 0.7:
                self._fine_tune_hyperparams(strategy_name, problem_features, avg_performance)
            else:
                self._explore_hyperparams(strategy_name, problem_features, avg_performance)
    
    def _get_param_key(self, strategy_name: str, hyperparams: Dict[str, Any]) -> str:
        """
        Generate a unique key for a set of hyperparameters.
        
        Args:
            strategy_name: Name of the strategy
            hyperparams: Dictionary of hyperparameters
            
        Returns:
            A unique key for the hyperparameter combination
        """
        # Serialize hyperparameters, excluding non-hashable values like dictionaries
        param_items = []
        for key, value in sorted(hyperparams.items()):
            if isinstance(value, (int, float, str, bool)):
                param_items.append(f"{key}={value}")
            elif isinstance(value, dict):
                # For dictionaries like evaluator_weights, use a hash of the items
                dict_hash = hash(frozenset(value.items()))
                param_items.append(f"{key}_hash={dict_hash}")
        
        return f"{strategy_name}_" + "_".join(param_items)
    
    def _fine_tune_hyperparams(self, strategy_name: str, problem_features: ProblemFeatures, performance: float) -> None:
        """
        Fine-tune hyperparameters for a strategy.
        
        Args:
            strategy_name: Name of the strategy to fine-tune
            problem_features: Features of the problem the strategy was applied to
            performance: Current performance score
        """
        hyperparams = self.strategy_hyperparams[strategy_name]
        module = self.reasoning_modules[strategy_name]
        
        # Make small adjustments to explore local optimum
        if 'max_paths' in hyperparams:
            # Adjust max_paths based on problem complexity
            if problem_features.complexity in [ProblemComplexity.COMPLEX, ProblemComplexity.VERY_COMPLEX]:
                hyperparams['max_paths'] = min(hyperparams['max_paths'] + 1, 10)
            else:
                hyperparams['max_paths'] = max(hyperparams['max_paths'] - 1, 2)
                
            # Apply the updated value to the module
            if hasattr(module, 'max_paths'):
                module.max_paths = hyperparams['max_paths']
        
        if 'beam_width' in hyperparams:
            # Adjust beam_width based on problem complexity
            if problem_features.complexity in [ProblemComplexity.COMPLEX, ProblemComplexity.VERY_COMPLEX]:
                hyperparams['beam_width'] = min(hyperparams['beam_width'] + 1, 5)
            else:
                hyperparams['beam_width'] = max(hyperparams['beam_width'] - 1, 2)
                
            # Apply the updated value to the module
            if hasattr(module, 'beam_width'):
                module.beam_width = hyperparams['beam_width']
        
        if 'temperature' in hyperparams:
            # Adjust temperature based on performance
            if performance > 0.8:
                # Higher performance -> lower temperature (more exploitation)
                hyperparams['temperature'] = max(hyperparams['temperature'] * 0.9, 0.05)
            else:
                # Lower performance -> higher temperature (more exploration)
                hyperparams['temperature'] = min(hyperparams['temperature'] * 1.1, 1.0)
                
            # Apply the updated value to the module
            if hasattr(module, 'temperature'):
                module.temperature = hyperparams['temperature']
        
        if 'allocator_temperature' in hyperparams and hasattr(module, 'resource_allocator'):
            # Adjust allocator temperature based on performance
            if performance > 0.8:
                # Higher performance -> lower temperature (more exploitation)
                hyperparams['allocator_temperature'] = max(hyperparams['allocator_temperature'] * 0.9, 0.05)
            else:
                # Lower performance -> higher temperature (more exploration)
                hyperparams['allocator_temperature'] = min(hyperparams['allocator_temperature'] * 1.1, 1.0)
                
            # Apply the updated value to the module
            module.resource_allocator.temperature = hyperparams['allocator_temperature']
        
        if 'evaluator_weights' in hyperparams and hasattr(module, 'path_evaluator'):
            # Adjust evaluator weights based on problem domain
            weights = hyperparams['evaluator_weights']
            
            if problem_features.domain == ProblemDomain.MATHEMATICAL:
                # For mathematical problems, prioritize validity
                weights['validity'] = min(weights['validity'] + 0.05, 0.6)
                weights['progress'] = max(weights['progress'] - 0.05, 0.2)
            elif problem_features.domain == ProblemDomain.CREATIVE:
                # For creative problems, prioritize diversity
                weights['diversity'] = min(weights['diversity'] + 0.05, 0.4)
                weights['validity'] = max(weights['validity'] - 0.05, 0.2)
                
            # Normalize weights to sum to 1
            total = sum(weights.values())
            for key in weights:
                weights[key] = weights[key] / total
                
            # Apply the updated weights to the module
            module.path_evaluator.weights = weights.copy()
    
    def _explore_hyperparams(self, strategy_name: str, problem_features: ProblemFeatures, performance: float) -> None:
        """
        Explore different hyperparameter settings for a strategy.
        
        Args:
            strategy_name: Name of the strategy to explore
            problem_features: Features of the problem the strategy was applied to
            performance: Current performance score
        """
        hyperparams = self.strategy_hyperparams[strategy_name]
        module = self.reasoning_modules[strategy_name]
        
        # With low performance, make larger adjustments to explore different areas
        # Sometimes revert to the best known hyperparameters
        if random.random() < 0.3:
            # Revert to best known hyperparameters
            best_params = self.best_hyperparams[strategy_name]
            for key, value in best_params.items():
                hyperparams[key] = value
                
                # Apply the updated value to the module
                if key == 'evaluator_weights' and hasattr(module, 'path_evaluator'):
                    module.path_evaluator.weights = value.copy()
                elif hasattr(module, key):
                    setattr(module, key, value)
                elif key == 'allocator_temperature' and hasattr(module, 'resource_allocator'):
                    module.resource_allocator.temperature = value
        else:
            # Explore new hyperparameter settings
            if 'max_paths' in hyperparams:
                # Explore a wider range for max_paths
                hyperparams['max_paths'] = random.randint(2, 10)
                
                # Apply the updated value to the module
                if hasattr(module, 'max_paths'):
                    module.max_paths = hyperparams['max_paths']
            
            if 'beam_width' in hyperparams:
                # Explore a wider range for beam_width
                hyperparams['beam_width'] = random.randint(2, 5)
                
                # Apply the updated value to the module
                if hasattr(module, 'beam_width'):
                    module.beam_width = hyperparams['beam_width']
            
            if 'temperature' in hyperparams:
                # Explore a wider range for temperature
                hyperparams['temperature'] = random.uniform(0.05, 1.0)
                
                # Apply the updated value to the module
                if hasattr(module, 'temperature'):
                    module.temperature = hyperparams['temperature']
            
            if 'allocator_temperature' in hyperparams and hasattr(module, 'resource_allocator'):
                # Explore a wider range for allocator temperature
                hyperparams['allocator_temperature'] = random.uniform(0.05, 1.0)
                
                # Apply the updated value to the module
                module.resource_allocator.temperature = hyperparams['allocator_temperature']
            
            if 'evaluator_weights' in hyperparams and hasattr(module, 'path_evaluator'):
                # Explore different weight combinations
                weights = hyperparams['evaluator_weights']
                
                # Randomly adjust weights
                for key in weights:
                    weights[key] = random.uniform(0.1, 0.8)
                    
                # Normalize weights to sum to 1
                total = sum(weights.values())
                for key in weights:
                    weights[key] = weights[key] / total
                    
                # Apply the updated weights to the module
                module.path_evaluator.weights = weights.copy()
    
    def get_best_hyperparams(self, strategy_name: str) -> Dict[str, Any]:
        """
        Get the best hyperparameters for a strategy.
        
        Args:
            strategy_name: Name of the strategy
            
        Returns:
            Dictionary of best hyperparameters
        """
        if strategy_name in self.best_hyperparams:
            return self.best_hyperparams[strategy_name]
        
        return {}
    
    def apply_best_hyperparams(self, strategy_name: str) -> bool:
        """
        Apply the best hyperparameters to a strategy.
        
        Args:
            strategy_name: Name of the strategy
            
        Returns:
            True if successful, False otherwise
        """
        if strategy_name not in self.best_hyperparams or strategy_name not in self.reasoning_modules:
            return False
            
        best_params = self.best_hyperparams[strategy_name]
        module = self.reasoning_modules[strategy_name]
        
        # Apply best parameters to the module
        for key, value in best_params.items():
            if key == 'evaluator_weights' and hasattr(module, 'path_evaluator'):
                module.path_evaluator.weights = value.copy()
            elif hasattr(module, key):
                setattr(module, key, value)
            elif key == 'allocator_temperature' and hasattr(module, 'resource_allocator'):
                module.resource_allocator.temperature = value
                
        return True

class MetaLearningController:
    """
    Main class for the Meta-Learning Controller of the ULTRA Meta-Cognitive System.
    
    The Meta-Learning Controller adapts reasoning strategies based on problem characteristics
    and past performance, functioning as the "learning to learn" component of the system.
    """
    
    def __init__(
        self, 
        reasoning_modules: Dict[str, Any],
        language_model=None,
        knowledge_base: Optional[EpisodicKnowledgeBase] = None,
        config: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize the Meta-Learning Controller.
        
        Args:
            reasoning_modules: Dictionary mapping strategy names to reasoning modules
            language_model: Optional language model for advanced feature extraction
            knowledge_base: Optional knowledge base for reasoning
            config: Optional configuration dictionary
        """
        self.reasoning_modules = reasoning_modules
        self.language_model = language_model
        self.knowledge_base = knowledge_base
        self.config = config or {}
        
        # Initialize experience memory
        memory_capacity = self.config.get("memory_capacity", 1000)
        self.experience_memory = ExperienceMemory(capacity=memory_capacity)
        
        # Initialize strategy selector
        exploration_rate = self.config.get("exploration_rate", 0.2)
        self.strategy_selector = StrategySelector(
            reasoning_modules=reasoning_modules,
            experience_memory=self.experience_memory,
            exploration_rate=exploration_rate
        )
        
        # Initialize hyperparameter optimizer
        learning_rate = self.config.get("learning_rate", 0.05)
        self.hyperparameter_optimizer = HyperParameterOptimizer(
            reasoning_modules=reasoning_modules,
            learning_rate=learning_rate
        )
        
        # Initialize performance tracking
        self.performance_history = []
        
        # Logger
        self.logger = get_logger(__name__)
    
    def select_reasoning_strategy(
        self, 
        problem: str,
        context: Optional[Dict[str, Any]] = None,
        constraints: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Select the best reasoning strategy for a problem.
        
        Args:
            problem: The problem to select a strategy for
            context: Optional context information
            constraints: Optional constraints on strategy selection
            
        Returns:
            Name of the selected strategy
        """
        # Extract problem features
        features = self.experience_memory.extract_problem_features(problem, self.language_model)
        
        # Add context and language model for strategy selection
        selection_context = context or {}
        if self.language_model:
            selection_context["language_model"] = self.language_model
            
        # Select strategy
        strategy = self.strategy_selector.select_strategy(
            problem=problem,
            features=features,
            context=selection_context,
            constraints=constraints
        )
        
        self.logger.info(f"Selected reasoning strategy: {strategy}")
        
        return strategy
    
    def update_with_results(
        self, 
        problem: str,
        strategy: str,
        result: Dict[str, Any]
    ) -> None:
        """
        Update the Meta-Learning Controller with reasoning results.
        
        Args:
            problem: The problem statement
            strategy: The reasoning strategy used
            result: Dictionary of reasoning results
        """
        # Extract problem features
        features = self.experience_memory.extract_problem_features(problem, self.language_model)
        
        # Extract performance metrics
        performance = self._extract_performance_metrics(strategy, result)
        
        # Create a reasoning experience record
        experience = ReasoningExperience(
            problem=problem,
            features=features,
            strategy_performances={strategy: performance},
            best_strategy=strategy,
            timestamp=time.time(),
            solution=result.get("conclusion", None),
            reasoning_path=result.get("reasoning_path", None)
        )
        
        # Add to experience memory
        self.experience_memory.add_experience(experience)
        
        # Update strategy selector with performance
        self.strategy_selector.update_with_performance(
            strategy_name=strategy,
            performance=performance,
            features=features
        )
        
        # Optimize hyperparameters
        self.hyperparameter_optimizer.optimize(
            strategy_name=strategy,
            performance=performance,
            problem_features=features
        )
        
        # Track performance
        self.performance_history.append({
            "problem": problem[:100],  # Truncate for brevity
            "strategy": strategy,
            "performance": performance.to_dict(),
            "timestamp": time.time()
        })
    
    def _extract_performance_metrics(self, strategy: str, result: Dict[str, Any]) -> StrategyPerformance:
        """
        Extract performance metrics from reasoning results.
        
        Args:
            strategy: The reasoning strategy used
            result: Dictionary of reasoning results
            
        Returns:
            Performance metrics for the strategy
        """
        # Extract basic metrics
        execution_time = result.get("execution_time", 0.0)
        confidence = result.get("confidence", 0.0)
        
        # Extract reasoning quality components
        reasoning_quality = 0.0
        coherence = 0.0
        relevance = 0.0
        completeness = 0.0
        
        # Extract from component scores if available
        component_scores = result.get("component_scores", {})
        if component_scores:
            reasoning_quality = component_scores.get("validity", 0.0)
            coherence = component_scores.get("coherence", 0.0)
            relevance = component_scores.get("relevance", 0.0)
            completeness = component_scores.get("completeness", 0.0)
        else:
            # Estimate reasoning quality from score
            score = result.get("score", 0.0)
            reasoning_quality = score
        
        # Estimate resource efficiency
        resource_efficiency = 1.0 - min(1.0, execution_time / 30.0)  # Assume 30s is upper bound
        
        # Estimate error recovery and creative problem solving
        error_recovery = result.get("error_recovery", 0.0)
        creative_problem_solving = result.get("creative_problem_solving", 0.0)
        
        # Create performance metrics
        return StrategyPerformance(
            strategy_name=strategy,
            execution_time=execution_time,
            confidence=confidence,
            reasoning_quality=reasoning_quality,
            resource_efficiency=resource_efficiency,
            coherence=coherence,
            relevance=relevance,
            completeness=completeness,
            error_recovery=error_recovery,
            creative_problem_solving=creative_problem_solving,
            additional_metrics=result.get("additional_metrics", {})
        )
    
    def analyze_low_confidence_case(
        self, 
        problem: str,
        strategy: str,
        result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Analyze a reasoning case with low confidence.
        
        Args:
            problem: The problem statement
            strategy: The reasoning strategy used
            result: Dictionary of reasoning results
            
        Returns:
            Analysis results
        """
        # Extract confidence
        confidence = result.get("confidence", 0.0)
        
        # Don't analyze if confidence isn't actually low
        if confidence >= 0.6:
            return {
                "analysis": "Confidence is not low enough to warrant analysis."
            }
            
        # Extract problem features
        features = self.experience_memory.extract_problem_features(problem, self.language_model)
        
        # Find similar problems with better results
        similar_experiences = self.experience_memory.get_similar_experiences(
            problem=problem,
            features=features,
            k=3,
            similarity_threshold=0.3
        )
        
        # Filter for experiences with higher confidence
        better_experiences = [
            (exp, sim) for exp, sim in similar_experiences
            if any(
                perf.confidence > confidence + 0.2  # Only consider significantly better results
                for perf in exp.strategy_performances.values()
            )
        ]
        
        # If we have better experiences, analyze what made them better
        if better_experiences:
            better_strategies = []
            for exp, _ in better_experiences:
                for strategy_name, perf in exp.strategy_performances.items():
                    if perf.confidence > confidence + 0.2:
                        better_strategies.append((strategy_name, perf.confidence))
            
            # Count how many times each strategy worked better
            strategy_counts = Counter([s for s, _ in better_strategies])
            
            # Find most common better strategy
            common_better_strategies = strategy_counts.most_common(2)
            
            # Prepare analysis results
            analysis = {
                "problem_domain": features.domain.name,
                "problem_complexity": features.complexity.name,
                "attempted_strategy": strategy,
                "confidence": confidence,
                "potential_better_strategies": [
                    {"strategy": s, "count": c}
                    for s, c in common_better_strategies
                ],
                "analysis": "Similar problems were solved more confidently with different strategies."
            }
            
            return analysis
        
        # If no similar problems with better results, analyze problem features
        difficult_features = []
        
        if features.complexity in [ProblemComplexity.COMPLEX, ProblemComplexity.VERY_COMPLEX]:
            difficult_features.append("high complexity")
            
        if features.requires_external_knowledge:
            difficult_features.append("requires external knowledge")
            
        if features.domain == ProblemDomain.UNDEFINED:
            difficult_features.append("undefined domain")
            
        # Prepare analysis results
        analysis = {
            "problem_domain": features.domain.name,
            "problem_complexity": features.complexity.name,
            "attempted_strategy": strategy,
            "confidence": confidence,
            "difficult_features": difficult_features,
            "analysis": "The problem has inherently challenging features."
        }
        
        return analysis
    
    def get_strategy_recommendations(
        self, 
        problem: str,
        top_k: int = 3
    ) -> List[Dict[str, Any]]:
        """
        Get strategy recommendations for a problem, ranked by expected performance.
        
        Args:
            problem: The problem to get recommendations for
            top_k: Number of recommendations to return
            
        Returns:
            List of strategy recommendations with expected performance
        """
        # Extract problem features
        features = self.experience_memory.extract_problem_features(problem, self.language_model)
        
        # Find similar problems
        similar_experiences = self.experience_memory.get_similar_experiences(
            problem=problem,
            features=features,
            k=5,
            similarity_threshold=0.3
        )
        
        # Collect strategy performance on similar problems
        strategy_scores = defaultdict(list)
        
        for experience, similarity in similar_experiences:
            for strategy_name, performance in experience.strategy_performances.items():
                # Weight score by similarity
                weighted_score = performance.get_overall_score() * similarity
                strategy_scores[strategy_name].append(weighted_score)
        
        # Calculate expected performance for each strategy
        expected_performance = {}
        
        for strategy_name, scores in strategy_scores.items():
            if scores:
                expected_performance[strategy_name] = sum(scores) / len(scores)
                
        # If we don't have enough data from similar problems,
        # use domain and complexity affinities
        if len(expected_performance) < min(top_k, len(self.reasoning_modules)):
            domain_affinities = self.strategy_selector.get_strategy_domain_affinities()
            complexity_affinities = self.strategy_selector.get_strategy_complexity_affinities()
            
            for strategy_name in self.reasoning_modules.keys():
                if strategy_name not in expected_performance:
                    # Estimate performance based on domain and complexity affinity
                    domain_affinity = domain_affinities.get(strategy_name, {}).get(features.domain.name, 0.5)
                    complexity_affinity = complexity_affinities.get(strategy_name, {}).get(features.complexity.name, 0.5)
                    
                    # Combine affinities for an estimated score
                    estimated_score = 0.6 * domain_affinity + 0.4 * complexity_affinity
                    expected_performance[strategy_name] = estimated_score
        
        # Sort strategies by expected performance
        ranked_strategies = sorted(
            expected_performance.items(),
            key=lambda x: x[1],
            reverse=True
        )
        
        # Return top_k recommendations
        recommendations = [
            {
                "strategy": strategy_name,
                "expected_performance": score,
                "reasoning": self._generate_recommendation_reasoning(strategy_name, features)
            }
            for strategy_name, score in ranked_strategies[:top_k]
        ]
        
        return recommendations
    
    def _generate_recommendation_reasoning(
        self, 
        strategy_name: str,
        features: ProblemFeatures
    ) -> str:
        """
        Generate reasoning for a strategy recommendation.
        
        Args:
            strategy_name: Name of the recommended strategy
            features: Features of the problem
            
        Returns:
            Reasoning text explaining the recommendation
        """
        # Generate reasoning based on strategy characteristics and problem features
        domain_affinities = self.strategy_selector.get_strategy_domain_affinities()
        complexity_affinities = self.strategy_selector.get_strategy_complexity_affinities()
        
        domain_affinity = domain_affinities.get(strategy_name, {}).get(features.domain.name, 0.5)
        complexity_affinity = complexity_affinities.get(strategy_name, {}).get(features.complexity.name, 0.5)
        
        # Strategy-specific characteristics
        strategy_characteristics = {
            "CHAIN_OF_THOUGHT": "sequential reasoning",
            "TREE_OF_THOUGHT": "exploring multiple paths and backtracking",
            "REASONING_GRAPH": "representing complex logical relationships",
            "MULTI_PATH": "exploring diverse approaches simultaneously",
            "SELF_CRITIQUE": "rigorous evaluation and refinement",
            "HYBRID": "combining multiple reasoning approaches"
        }
        
        characteristic = strategy_characteristics.get(strategy_name, "general reasoning")
        
        # Domain-specific reasoning
        domain_reasoning = ""
        if domain_affinity > 0.7:
            domain_reasoning = f"This strategy has shown strong performance on {features.domain.name.lower()} problems. "
        elif domain_affinity > 0.5:
            domain_reasoning = f"This strategy is well-suited for {features.domain.name.lower()} problems. "
            
        # Complexity-specific reasoning
        complexity_reasoning = ""
        if complexity_affinity > 0.7:
            complexity_reasoning = f"It works particularly well for {features.complexity.name.lower()} complexity problems. "
        elif complexity_affinity > 0.5:
            complexity_reasoning = f"It is suitable for handling {features.complexity.name.lower()} complexity problems. "
            
        # Combined reasoning
        reasoning = f"Recommended for its strength in {characteristic}. {domain_reasoning}{complexity_reasoning}"
        
        return reasoning
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """
        Get performance metrics for the Meta-Learning Controller.
        
        Returns:
            Dictionary of performance metrics
        """
        # Get strategy performance analysis
        strategy_analysis = self.strategy_selector.analyze_strategy_performance()
        
        # Get domain performance analysis from experience memory
        domain_analysis = self.experience_memory.analyze_strategy_performance()
        
        # Calculate overall metrics
        overall_metrics = {}
        
        # Calculate average success rate across strategies
        success_rates = [
            metrics["success_rate"]
            for _, metrics in strategy_analysis.items()
            if "success_rate" in metrics
        ]
        
        if success_rates:
            overall_metrics["avg_success_rate"] = sum(success_rates) / len(success_rates)
            
        # Calculate average score across strategies
        avg_scores = [
            metrics["avg_score"]
            for _, metrics in strategy_analysis.items()
            if "avg_score" in metrics
        ]
        
        if avg_scores:
            overall_metrics["avg_score"] = sum(avg_scores) / len(avg_scores)
            
        # Return combined metrics
        return {
            "overall": overall_metrics,
            "strategy_performance": strategy_analysis,
            "domain_performance": domain_analysis
        }
    
    def visualize_performance(self, output_path: Optional[str] = None) -> None:
        """
        Visualize performance metrics.
        
        Args:
            output_path: Optional path to save the visualization
        """
        try:
            import matplotlib.pyplot as plt
            
            # Get performance metrics
            metrics = self.get_performance_metrics()
            
            # Create figure with subplots
            fig, axs = plt.subplots(2, 2, figsize=(14, 10))
            
            # Plot 1: Strategy success rates
            strategy_perf = metrics["strategy_performance"]
            strategies = list(strategy_perf.keys())
            success_rates = [perf["success_rate"] for perf in strategy_perf.values()]
            avg_scores = [perf["avg_score"] for perf in strategy_perf.values()]
            
            x = np.arange(len(strategies))
            width = 0.35
            
            axs[0, 0].bar(x - width/2, success_rates, width, label='Success Rate')
            axs[0, 0].bar(x + width/2, avg_scores, width, label='Average Score')
            axs[0, 0].set_ylabel('Performance')
            axs[0, 0].set_title('Strategy Performance')
            axs[0, 0].set_xticks(x)
            axs[0, 0].set_xticklabels(strategies, rotation=45, ha='right')
            axs[0, 0].legend()
            
            # Plot 2: Domain performance
            domain_perf = metrics["domain_performance"]
            if domain_perf:
                # Prepare data for domain performance
                domains = []
                strategy_domain_scores = defaultdict(list)
                
                for strategy, domain_scores in domain_perf.items():
                    for domain, score in domain_scores.items():
                        if domain not in domains:
                            domains.append(domain)
                        strategy_domain_scores[strategy].append(score)
                
                # Create domain performance plot
                x = np.arange(len(domains))
                width = 0.8 / len(strategy_domain_scores)
                
                for i, (strategy, scores) in enumerate(strategy_domain_scores.items()):
                    axs[0, 1].bar(x + (i - len(strategy_domain_scores)/2 + 0.5) * width, scores, width, label=strategy)
                
                axs[0, 1].set_ylabel('Performance')
                axs[0, 1].set_title('Strategy Performance by Domain')
                axs[0, 1].set_xticks(x)
                axs[0, 1].set_xticklabels([d.name for d in domains], rotation=45, ha='right')
                axs[0, 1].legend()
            
            # Plot 3: Performance over time
            if self.performance_history:
                timestamps = [entry["timestamp"] for entry in self.performance_history]
                relative_times = [(t - timestamps[0]) / 60 for t in timestamps]  # Convert to minutes
                
                performances = []
                for entry in self.performance_history:
                    if "performance" in entry and hasattr(entry["performance"], "get_overall_score"):
                        performances.append(entry["performance"].get_overall_score())
                    elif "performance" in entry and isinstance(entry["performance"], dict):
                        scores = []
                        if "overall_score" in entry["performance"]:
                            scores.append(entry["performance"]["overall_score"])
                        if "confidence" in entry["performance"]:
                            scores.append(entry["performance"]["confidence"])
                        if scores:
                            performances.append(sum(scores) / len(scores))
                        else:
                            performances.append(0.5)
                    else:
                        performances.append(0.5)
                
                # Create a smoothed trend line
                from scipy.signal import savgol_filter
                if len(performances) > 5:
                    window_size = min(len(performances) - 1, 5)
                    if window_size % 2 == 0:
                        window_size += 1
                    smoothed = savgol_filter(performances, window_size, 1)
                else:
                    smoothed = performances
                
                axs[1, 0].plot(relative_times, performances, 'o-', alpha=0.5, label='Raw Performance')
                axs[1, 0].plot(relative_times, smoothed, 'r-', label='Trend')
                axs[1, 0].set_xlabel('Time (minutes)')
                axs[1, 0].set_ylabel('Performance Score')
                axs[1, 0].set_title('Performance Over Time')
                axs[1, 0].legend()
            
            # Plot 4: Strategy distribution
            if self.performance_history:
                strategy_counts = Counter([entry["strategy"] for entry in self.performance_history])
                strategies = list(strategy_counts.keys())
                counts = list(strategy_counts.values())
                
                axs[1, 1].pie(counts, labels=strategies, autopct='%1.1f%%')
                axs[1, 1].set_title('Strategy Usage Distribution')
            
            # Adjust layout and save or show
            plt.tight_layout()
            
            if output_path:
                plt.savefig(output_path, dpi=300, bbox_inches='tight')
                plt.close()
            else:
                plt.show()
                
        except ImportError as e:
            self.logger.warning(f"Visualization requires matplotlib: {e}")
        except Exception as e:
            self.logger.error(f"Error in visualization: {e}")
    
    def save_state(self, filepath: str) -> bool:
        """
        Save the current state of the Meta-Learning Controller.
        
        Args:
            filepath: Path to save the state to
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Create directory if it doesn't exist
            directory = os.path.dirname(filepath)
            if directory and not os.path.exists(directory):
                os.makedirs(directory)
                
            # Save experience memory
            experience_path = f"{filepath}.experience"
            self.experience_memory.save(experience_path)
            
            # Save controller state
            state = {
                "performance_history": self.performance_history,
                "strategy_performance": self.strategy_selector.strategy_performance,
                "strategy_feature_weights": self.strategy_selector.strategy_feature_weights,
                "best_hyperparams": self.hyperparameter_optimizer.best_hyperparams,
                "best_performance": self.hyperparameter_optimizer.best_performance
            }
            
            with open(filepath, 'wb') as f:
                pickle.dump(state, f)
                
            self.logger.info(f"Meta-Learning Controller state saved to {filepath}")
            return True
        except Exception as e:
            self.logger.error(f"Error saving state: {e}")
            return False
    
    def load_state(self, filepath: str) -> bool:
        """
        Load the state of the Meta-Learning Controller.
        
        Args:
            filepath: Path to load the state from
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Load experience memory
            experience_path = f"{filepath}.experience"
            if os.path.exists(experience_path):
                self.experience_memory.load(experience_path)
                
            # Load controller state
            with open(filepath, 'rb') as f:
                state = pickle.load(f)
                
            self.performance_history = state["performance_history"]
            self.strategy_selector.strategy_performance = state["strategy_performance"]
            self.strategy_selector.strategy_feature_weights = state["strategy_feature_weights"]
            self.hyperparameter_optimizer.best_hyperparams = state["best_hyperparams"]
            self.hyperparameter_optimizer.best_performance = state["best_performance"]
            
            self.logger.info(f"Meta-Learning Controller state loaded from {filepath}")
            return True
        except Exception as e:
            self.logger.error(f"Error loading state: {e}")
            return False

# Make key classes available at module level
__all__ = [
    'MetaLearningController',
    'ProblemFeatures',
    'ProblemDomain',
    'ProblemComplexity',
    'StrategyPerformance',
    'ReasoningExperience',
    'ExperienceMemory',
    'StrategySelector',
    'HyperParameterOptimizer'
]

# Optional initialization function to create a default instance
def create_default_meta_learning_controller(reasoning_modules, language_model=None):
    """
    Create a default Meta-Learning Controller instance.
    
    Args:
        reasoning_modules: Dictionary mapping strategy names to reasoning modules
        language_model: Optional language model for advanced feature extraction
        
    Returns:
        Configured MetaLearningController instance
    """
    config = {
        "memory_capacity": 1000,
        "exploration_rate": 0.2,
        "learning_rate": 0.05
    }
    
    return MetaLearningController(
        reasoning_modules=reasoning_modules,
        language_model=language_model,
        config=config
    )

# Main execution for standalone usage and testing
if __name__ == "__main__":
    # Example usage
    
    # Create mock reasoning modules for testing
    class MockReasoningModule:
        def __init__(self, name):
            self.name = name
            self.max_paths = 5
            self.beam_width = 3
            self.temperature = 0.1
    
    # Create mock modules
    reasoning_modules = {
        "CHAIN_OF_THOUGHT": MockReasoningModule("CHAIN_OF_THOUGHT"),
        "TREE_OF_THOUGHT": MockReasoningModule("TREE_OF_THOUGHT"),
        "REASONING_GRAPH": MockReasoningModule("REASONING_GRAPH")
    }
    
    # Create a mock language model for testing
    class MockLanguageModel:
        def generate(self, prompt):
            import random
            return str(random.randint(1, 5))
    
    # Create the meta-learning controller
    meta_learning = MetaLearningController(
        reasoning_modules=reasoning_modules,
        language_model=MockLanguageModel()
    )
    
    # Example problems
    problems = [
        "Calculate the integral of 2x^3 + 5x^2 - 3x + 7.",
        "What are the ethical implications of autonomous vehicles?",
        "Design a novel transportation system for urban environments.",
        "If all A are B, and some B are C, can we conclude that some A are C?"
    ]
    
    # Test strategy selection
    for problem in problems:
        strategy = meta_learning.select_reasoning_strategy(problem)
        print(f"Problem: {problem}")
        print(f"Selected strategy: {strategy}")
        
        # Simulate reasoning results
        import random
        result = {
            "execution_time": random.uniform(1.0, 10.0),
            "confidence": random.uniform(0.5, 0.9),
            "score": random.uniform(0.6, 0.95),
            "component_scores": {
                "validity": random.uniform(0.6, 0.9),
                "progress": random.uniform(0.6, 0.9),
                "diversity": random.uniform(0.6, 0.9)
            }
        }
        
        # Update with results
        meta_learning.update_with_results(problem, strategy, result)
        print(f"Updated with results: {result['score']:.2f} score\n")
    
    # Get strategy recommendations
    recommendations = meta_learning.get_strategy_recommendations(
        "What is the best approach to solve a system of linear equations?"
    )
    
    print("Strategy recommendations:")
    for rec in recommendations:
        print(f"- {rec['strategy']}: {rec['expected_performance']:.2f}")
        print(f"  Reasoning: {rec['reasoning']}")
        
    # Get performance metrics
    metrics = meta_learning.get_performance_metrics()
    print("\nPerformance metrics:")
    if "overall" in metrics:
        for metric, value in metrics["overall"].items():
            print(f"- {metric}: {value:.2f}")