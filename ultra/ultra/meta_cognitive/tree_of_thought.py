#!/usr/bin/env python3
"""
ULTRA Meta-Cognitive System: Tree of Thought Exploration

This module implements the Tree of Thought (ToT) component of the ULTRA Meta-Cognitive System.
Tree of Thought extends chain-of-thought reasoning by organizing reasoning paths in a hierarchical
tree structure, which enables backtracking, branching, and exploration of multiple solution paths.
This approach is particularly effective for complex problems requiring search, planning, or
systematic exploration of alternative approaches.

Key capabilities include:
1. Tree-structured exploration of reasoning paths
2. Dynamic evaluation and pruning of unproductive branches
3. Strategic backtracking when reasoning reaches dead ends
4. Balancing exploration vs. exploitation in search
5. Integration with other reasoning methods for hybrid approaches
"""

import os
import time
import math
import json
import logging
import heapq
import uuid
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Any, Optional, Union, Set, Callable, Iterator, TypeVar, Generic, NamedTuple
from dataclasses import dataclass, field
from enum import Enum, auto
from collections import defaultdict, deque
import random
import copy
import re
import networkx as nx

# Import ULTRA system components
from ultra.config import get_config, SystemConfig
from ultra.utils.ultra_logging import get_logger
from ultra.meta_cognitive.chain_of_thought import ReasoningStepType
from ultra.diffusion_reasoning.thought_latent_space import ThoughtLatentSpace
from ultra.knowledge_management.episodic_knowledge import EpisodicKnowledgeBase

# Configure logger
logger = get_logger(__name__)

class TreeNodeType(Enum):
    """Types of nodes in a reasoning tree."""
    ROOT = auto()              # Root node of the tree
    OBSERVATION = auto()       # Observation from the problem or environment
    HYPOTHESIS = auto()        # Hypothesis or potential approach
    DECOMPOSITION = auto()     # Breaking down of a problem
    EXPLORATION = auto()       # Exploration of a concept or approach
    VERIFICATION = auto()      # Verification of a previous step
    CALCULATION = auto()       # Calculation or computation
    INFERENCE = auto()         # Drawing inferences
    CONCLUSION = auto()        # Reaching a conclusion
    DEAD_END = auto()          # Recognized unproductive path
    REFLECTION = auto()        # Meta-cognitive reflection

class SearchStrategy(Enum):
    """Search strategies for tree exploration."""
    DEPTH_FIRST = auto()       # Depth-first search
    BREADTH_FIRST = auto()     # Breadth-first search
    BEST_FIRST = auto()        # Best-first search using heuristics
    MONTE_CARLO = auto()       # Monte Carlo tree search
    BEAM_SEARCH = auto()       # Beam search
    DYNAMIC = auto()           # Dynamic strategy selection

@dataclass
class TreeNode:
    """Represents a node in the reasoning tree."""
    id: str                                  # Unique node identifier
    content: str                             # Text content of the node
    node_type: TreeNodeType                  # Type of node
    parent_id: Optional[str] = None          # ID of parent node
    children_ids: List[str] = field(default_factory=list) # IDs of child nodes
    value: float = 0.0                       # Value/score of this node
    visits: int = 0                          # Number of times this node has been visited
    depth: int = 0                           # Depth in the tree
    created_at: float = field(default_factory=time.time) # Creation timestamp
    metadata: Dict[str, Any] = field(default_factory=dict) # Additional metadata
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "id": self.id,
            "content": self.content,
            "node_type": self.node_type.name,
            "parent_id": self.parent_id,
            "children_ids": self.children_ids,
            "value": self.value,
            "visits": self.visits,
            "depth": self.depth,
            "created_at": self.created_at,
            "metadata": self.metadata
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TreeNode':
        """Create a TreeNode from a dictionary."""
        return cls(
            id=data["id"],
            content=data["content"],
            node_type=TreeNodeType[data["node_type"]],
            parent_id=data.get("parent_id"),
            children_ids=data.get("children_ids", []),
            value=data.get("value", 0.0),
            visits=data.get("visits", 0),
            depth=data.get("depth", 0),
            created_at=data.get("created_at", time.time()),
            metadata=data.get("metadata", {})
        )
    
    def is_leaf(self) -> bool:
        """Check if this is a leaf node (no children)."""
        return len(self.children_ids) == 0
    
    def is_root(self) -> bool:
        """Check if this is the root node (no parent)."""
        return self.parent_id is None
    
    def has_unexplored_potential(self) -> bool:
        """Check if this node has unexplored potential for expansion."""
        # Nodes that are explicitly marked as dead ends have no potential
        if self.node_type == TreeNodeType.DEAD_END:
            return False
        
        # Already expanded nodes with children are considered explored
        if not self.is_leaf():
            return False
            
        # Check if the node has been explicitly marked as fully explored
        if self.metadata.get("fully_explored", False):
            return False
            
        return True
    
    def get_uct_score(self, exploration_weight: float = 1.0) -> float:
        """
        Calculate the UCT (Upper Confidence Bound for Trees) score for MCTS.
        
        Args:
            exploration_weight: Weight for the exploration term
            
        Returns:
            UCT score
        """
        # If never visited, return infinity to ensure exploration
        if self.visits == 0:
            return float('inf')
            
        parent_visits = self.metadata.get("parent_visits", 0)
        
        # If parent never visited, use this node's visits instead
        if parent_visits == 0:
            parent_visits = max(1, self.visits)
            
        # Calculate exploitation term
        exploitation = self.value
        
        # Calculate exploration term (UCB1 formula)
        exploration = exploration_weight * math.sqrt(math.log(parent_visits) / self.visits)
        
        return exploitation + exploration

class ReasoningTree:
    """Represents a tree of reasoning steps and pathways."""
    
    def __init__(self):
        """Initialize an empty reasoning tree."""
        self.nodes: Dict[str, TreeNode] = {}
        self.root_id: Optional[str] = None
        self.node_counter = 0
        self.current_node_id: Optional[str] = None
        self.metadata: Dict[str, Any] = {}
        
        # Performance tracking
        self.performance_metrics = {
            "nodes_created": 0,
            "nodes_pruned": 0,
            "max_depth": 0,
            "exploration_paths": 0,
            "backtracking_events": 0
        }
    
    def add_node(
        self, 
        content: str, 
        node_type: TreeNodeType, 
        parent_id: Optional[str] = None,
        value: float = 0.0,
        node_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Add a node to the reasoning tree.
        
        Args:
            content: Text content of the node
            node_type: Type of node
            parent_id: ID of parent node (if None, this will be a root node)
            value: Initial value/score of the node
            node_id: Optional ID for the node (generated if None)
            metadata: Optional metadata for the node
            
        Returns:
            ID of the added node
        """
        # Generate node ID if not provided
        if node_id is None:
            node_id = f"node_{self.node_counter}"
            self.node_counter += 1
        
        # Calculate depth based on parent
        depth = 0
        if parent_id and parent_id in self.nodes:
            depth = self.nodes[parent_id].depth + 1
            
            # Update parent's children list
            self.nodes[parent_id].children_ids.append(node_id)
            
            # Update parent's visit count in metadata
            parent_visits = self.nodes[parent_id].visits
            if metadata is None:
                metadata = {}
            metadata["parent_visits"] = parent_visits
        
        # Create the node
        node = TreeNode(
            id=node_id,
            content=content,
            node_type=node_type,
            parent_id=parent_id,
            children_ids=[],
            value=value,
            visits=1,  # Initialize with 1 visit
            depth=depth,
            metadata=metadata or {}
        )
        
        # Add to nodes dictionary
        self.nodes[node_id] = node
        
        # If this is the first node, set it as root
        if not self.root_id:
            self.root_id = node_id
        
        # Set as current node if not already set
        if self.current_node_id is None:
            self.current_node_id = node_id
        
        # Update performance metrics
        self.performance_metrics["nodes_created"] += 1
        self.performance_metrics["max_depth"] = max(self.performance_metrics["max_depth"], depth)
        
        return node_id
    
    def get_node(self, node_id: str) -> Optional[TreeNode]:
        """
        Get a node from the reasoning tree.
        
        Args:
            node_id: ID of the node to get
            
        Returns:
            The node, or None if not found
        """
        return self.nodes.get(node_id)
    
    def get_current_node(self) -> Optional[TreeNode]:
        """
        Get the current node in the reasoning tree.
        
        Returns:
            The current node, or None if not set
        """
        if self.current_node_id is None:
            return None
        return self.get_node(self.current_node_id)
    
    def set_current_node(self, node_id: str) -> bool:
        """
        Set the current node in the reasoning tree.
        
        Args:
            node_id: ID of the node to set as current
            
        Returns:
            True if successful, False if node not found
        """
        if node_id in self.nodes:
            self.current_node_id = node_id
            return True
        return False
    
    def get_root_node(self) -> Optional[TreeNode]:
        """
        Get the root node of the reasoning tree.
        
        Returns:
            The root node, or None if not set
        """
        if self.root_id is None:
            return None
        return self.get_node(self.root_id)
    
    def update_node(
        self, 
        node_id: str, 
        content: Optional[str] = None,
        node_type: Optional[TreeNodeType] = None,
        value: Optional[float] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Update a node in the reasoning tree.
        
        Args:
            node_id: ID of the node to update
            content: New content for the node (if None, keep current content)
            node_type: New type for the node (if None, keep current type)
            value: New value for the node (if None, keep current value)
            metadata: Metadata to update (merged with existing metadata)
            
        Returns:
            True if successful, False if node not found
        """
        node = self.get_node(node_id)
        if node is None:
            return False
        
        if content is not None:
            node.content = content
        
        if node_type is not None:
            node.node_type = node_type
        
        if value is not None:
            node.value = value
        
        if metadata is not None:
            node.metadata.update(metadata)
        
        # Increment visit count
        node.visits += 1
        
        return True
    
    def mark_dead_end(self, node_id: str, reason: str = "Unproductive path") -> bool:
        """
        Mark a node as a dead end.
        
        Args:
            node_id: ID of the node to mark
            reason: Reason for marking as dead end
            
        Returns:
            True if successful, False if node not found
        """
        node = self.get_node(node_id)
        if node is None:
            return False
        
        # Update node type and metadata
        node.node_type = TreeNodeType.DEAD_END
        node.metadata["dead_end_reason"] = reason
        node.metadata["fully_explored"] = True
        
        # Update performance metrics
        self.performance_metrics["nodes_pruned"] += 1
        
        return True
    
    def backtrack(self) -> Optional[str]:
        """
        Backtrack to the parent of the current node.
        
        Returns:
            ID of the new current node, or None if already at root
        """
        current_node = self.get_current_node()
        if current_node is None or current_node.parent_id is None:
            return None
        
        # Set current node to parent
        self.current_node_id = current_node.parent_id
        
        # Update performance metrics
        self.performance_metrics["backtracking_events"] += 1
        
        return self.current_node_id
    
    def get_ancestors(self, node_id: str) -> List[TreeNode]:
        """
        Get the ancestors of a node, ordered from parent to root.
        
        Args:
            node_id: ID of the node
            
        Returns:
            List of ancestor nodes
        """
        ancestors = []
        current_id = node_id
        
        while True:
            node = self.get_node(current_id)
            if node is None or node.parent_id is None:
                break
            
            parent = self.get_node(node.parent_id)
            if parent is None:
                break
                
            ancestors.append(parent)
            current_id = parent.id
        
        return ancestors
    
    def get_path_to_root(self, node_id: str) -> List[TreeNode]:
        """
        Get the path from a node to the root, inclusive of the node and root.
        
        Args:
            node_id: ID of the node
            
        Returns:
            List of nodes from the specified node to the root
        """
        path = []
        
        # Add the starting node
        node = self.get_node(node_id)
        if node is None:
            return []
            
        path.append(node)
        
        # Add ancestors
        ancestors = self.get_ancestors(node_id)
        path.extend(ancestors)
        
        return path
    
    def get_path_from_root(self, node_id: str) -> List[TreeNode]:
        """
        Get the path from the root to a node, inclusive of the root and node.
        
        Args:
            node_id: ID of the node
            
        Returns:
            List of nodes from the root to the specified node
        """
        path = self.get_path_to_root(node_id)
        path.reverse()
        return path
    
    def get_subtree_nodes(self, node_id: str) -> List[TreeNode]:
        """
        Get all nodes in the subtree rooted at the specified node.
        
        Args:
            node_id: ID of the root of the subtree
            
        Returns:
            List of nodes in the subtree
        """
        if node_id not in self.nodes:
            return []
            
        subtree = []
        queue = deque([node_id])
        
        while queue:
            current_id = queue.popleft()
            node = self.get_node(current_id)
            
            if node:
                subtree.append(node)
                queue.extend(node.children_ids)
        
        return subtree
    
    def prune_subtree(self, node_id: str) -> int:
        """
        Prune a subtree, removing all nodes except the specified node.
        
        Args:
            node_id: ID of the root of the subtree to prune
            
        Returns:
            Number of nodes pruned
        """
        node = self.get_node(node_id)
        if node is None:
            return 0
            
        # Get all nodes in the subtree
        subtree = self.get_subtree_nodes(node_id)
        
        # Skip the root of the subtree
        nodes_to_remove = [n.id for n in subtree if n.id != node_id]
        
        # Remove child references
        node.children_ids = []
        
        # Remove nodes
        for remove_id in nodes_to_remove:
            if remove_id in self.nodes:
                del self.nodes[remove_id]
        
        # Update performance metrics
        self.performance_metrics["nodes_pruned"] += len(nodes_to_remove)
        
        return len(nodes_to_remove)
    
    def get_best_leaf_node(self) -> Optional[TreeNode]:
        """
        Get the leaf node with the highest value.
        
        Returns:
            The best leaf node, or None if no leaf nodes exist
        """
        leaf_nodes = [node for node in self.nodes.values() if node.is_leaf()]
        if not leaf_nodes:
            return None
            
        return max(leaf_nodes, key=lambda n: n.value)
    
    def get_most_promising_unexplored_node(self, exploration_weight: float = 1.0) -> Optional[TreeNode]:
        """
        Get the most promising unexplored node using UCT scores.
        
        Args:
            exploration_weight: Weight for the exploration term in UCT
            
        Returns:
            The most promising unexplored node, or None if no unexplored nodes exist
        """
        # Find nodes with unexplored potential
        unexplored_nodes = [node for node in self.nodes.values() if node.has_unexplored_potential()]
        if not unexplored_nodes:
            return None
            
        # Calculate UCT scores
        for node in unexplored_nodes:
            node.metadata["uct_score"] = node.get_uct_score(exploration_weight)
            
        # Return node with highest UCT score
        return max(unexplored_nodes, key=lambda n: n.metadata.get("uct_score", 0))
    
    def calculate_tree_statistics(self) -> Dict[str, Any]:
        """
        Calculate statistics about the tree.
        
        Returns:
            Dictionary of tree statistics
        """
        if not self.nodes:
            return {
                "num_nodes": 0,
                "max_depth": 0,
                "num_leaf_nodes": 0,
                "average_branching_factor": 0,
                "average_node_value": 0,
                "node_type_distribution": {}
            }
            
        # Calculate basic statistics
        num_nodes = len(self.nodes)
        max_depth = max(node.depth for node in self.nodes.values())
        leaf_nodes = [node for node in self.nodes.values() if node.is_leaf()]
        num_leaf_nodes = len(leaf_nodes)
        
        # Calculate branching factor
        non_leaf_nodes = [node for node in self.nodes.values() if not node.is_leaf()]
        if non_leaf_nodes:
            total_children = sum(len(node.children_ids) for node in non_leaf_nodes)
            average_branching_factor = total_children / len(non_leaf_nodes)
        else:
            average_branching_factor = 0
            
        # Calculate average node value
        average_node_value = sum(node.value for node in self.nodes.values()) / num_nodes
        
        # Calculate node type distribution
        node_type_distribution = {}
        for node in self.nodes.values():
            node_type = node.node_type.name
            if node_type not in node_type_distribution:
                node_type_distribution[node_type] = 0
            node_type_distribution[node_type] += 1
            
        # Return statistics
        return {
            "num_nodes": num_nodes,
            "max_depth": max_depth,
            "num_leaf_nodes": num_leaf_nodes,
            "average_branching_factor": average_branching_factor,
            "average_node_value": average_node_value,
            "node_type_distribution": node_type_distribution
        }
    
    def get_best_path(self) -> List[TreeNode]:
        """
        Get the path with the highest cumulative value.
        
        Returns:
            List of nodes in the best path from root to leaf
        """
        # Get leaf nodes
        leaf_nodes = [node for node in self.nodes.values() if node.is_leaf()]
        if not leaf_nodes:
            return []
            
        # Calculate path values
        path_values = {}
        for leaf in leaf_nodes:
            path = self.get_path_from_root(leaf.id)
            path_value = sum(node.value for node in path)
            path_values[leaf.id] = path_value
            
        # Find leaf with highest path value
        best_leaf_id = max(path_values.items(), key=lambda x: x[1])[0]
        
        # Return path from root to best leaf
        return self.get_path_from_root(best_leaf_id)
    
    def to_networkx(self) -> nx.DiGraph:
        """
        Convert the reasoning tree to a NetworkX DiGraph.
        
        Returns:
            NetworkX DiGraph representation
        """
        G = nx.DiGraph()
        
        # Add nodes
        for node_id, node in self.nodes.items():
            G.add_node(
                node_id,
                content=node.content,
                node_type=node.node_type.name,
                value=node.value,
                visits=node.visits,
                depth=node.depth
            )
        
        # Add edges
        for node_id, node in self.nodes.items():
            for child_id in node.children_ids:
                G.add_edge(node_id, child_id)
        
        return G
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the reasoning tree to a dictionary representation.
        
        Returns:
            Dictionary representation of the tree
        """
        return {
            "nodes": {node_id: node.to_dict() for node_id, node in self.nodes.items()},
            "root_id": self.root_id,
            "current_node_id": self.current_node_id,
            "metadata": self.metadata,
            "performance_metrics": self.performance_metrics
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ReasoningTree':
        """
        Create a ReasoningTree from a dictionary representation.
        
        Args:
            data: Dictionary representation of the tree
            
        Returns:
            ReasoningTree instance
        """
        tree = cls()
        
        # Load nodes
        for node_id, node_data in data["nodes"].items():
            tree.nodes[node_id] = TreeNode.from_dict(node_data)
            
        # Set node counter to avoid ID conflicts
        node_ids = [node_id for node_id in tree.nodes.keys() if node_id.startswith("node_")]
        if node_ids:
            max_id = max([int(node_id.split("_")[1]) for node_id in node_ids if node_id.split("_")[1].isdigit()])
            tree.node_counter = max_id + 1
        
        # Set other attributes
        tree.root_id = data.get("root_id")
        tree.current_node_id = data.get("current_node_id")
        tree.metadata = data.get("metadata", {})
        tree.performance_metrics = data.get("performance_metrics", {
            "nodes_created": len(tree.nodes),
            "nodes_pruned": 0,
            "max_depth": max([node.depth for node in tree.nodes.values()], default=0),
            "exploration_paths": 0,
            "backtracking_events": 0
        })
        
        return tree
    
    def visualize(self, output_path: Optional[str] = None) -> None:
        """
        Visualize the reasoning tree.
        
        Args:
            output_path: Path to save the visualization (if None, display instead)
        """
        try:
            import matplotlib.pyplot as plt
            
            # Convert to NetworkX graph
            G = self.to_networkx()
            
            # Create figure
            plt.figure(figsize=(12, 10))
            
            # Define node colors based on type
            node_colors = {
                TreeNodeType.ROOT.name: 'lightgray',
                TreeNodeType.OBSERVATION.name: 'skyblue',
                TreeNodeType.HYPOTHESIS.name: 'lightgreen',
                TreeNodeType.DECOMPOSITION.name: 'lightcoral',
                TreeNodeType.EXPLORATION.name: 'wheat',
                TreeNodeType.VERIFICATION.name: 'lightcyan',
                TreeNodeType.CALCULATION.name: 'lightyellow',
                TreeNodeType.INFERENCE.name: 'lightpink',
                TreeNodeType.CONCLUSION.name: 'lightblue',
                TreeNodeType.DEAD_END.name: 'red',
                TreeNodeType.REFLECTION.name: 'lavender'
            }
            
            # Get node colors
            colors = []
            for node in G.nodes():
                node_type = G.nodes[node].get('node_type', TreeNodeType.OBSERVATION.name)
                if node == self.current_node_id:
                    colors.append('gold')  # Highlight current node
                else:
                    colors.append(node_colors.get(node_type, 'lightgray'))
            
            # Get node sizes based on value
            node_sizes = []
            for node in G.nodes():
                value = G.nodes[node].get('value', 0.0)
                # Scale size by value: base size 300, max size 1000
                node_sizes.append(300 + 700 * max(0, min(1, value)))
            
            # Use hierarchical layout
            try:
                pos = nx.nx_agraph.graphviz_layout(G, prog='dot')
            except:
                # Fall back to spring layout if graphviz is not available
                pos = nx.spring_layout(G, seed=42)
            
            # Draw the graph
            nx.draw_networkx_nodes(G, pos, node_color=colors, node_size=node_sizes, alpha=0.8)
            nx.draw_networkx_edges(G, pos, edge_color='gray', width=1.5, alpha=0.7, 
                                  arrowsize=15)
            
            # Add labels
            labels = {}
            for node in G.nodes():
                content = G.nodes[node].get('content', '')
                # Truncate long content
                if len(content) > 30:
                    content = content[:27] + "..."
                labels[node] = content
            
            nx.draw_networkx_labels(G, pos, labels=labels, font_size=8)
            
            # Add a legend for node types
            legend_elements = []
            import matplotlib.patches as mpatches
            for node_type, color in node_colors.items():
                legend_elements.append(mpatches.Patch(color=color, label=node_type))
            
            plt.legend(handles=legend_elements, loc='upper right')
            
            # Set title
            plt.title("Reasoning Tree")
            
            # Remove axis
            plt.axis('off')
            
            # Save or display the graph
            if output_path:
                plt.savefig(output_path, bbox_inches='tight', dpi=300)
                plt.close()
            else:
                plt.tight_layout()
                plt.show()
                
        except Exception as e:
            logger.error(f"Error visualizing tree: {e}")

class NodeEvaluator:
    """Base class for node evaluation strategies."""
    
    def evaluate(self, node: TreeNode, tree: ReasoningTree, context: Dict[str, Any]) -> float:
        """
        Evaluate a node to determine its value/score.
        
        Args:
            node: The node to evaluate
            tree: The reasoning tree
            context: Additional context for evaluation
            
        Returns:
            Value/score for the node (0-1)
        """
        raise NotImplementedError("Subclasses must implement this method")

class SimpleNodeEvaluator(NodeEvaluator):
    """Simple node evaluator based on content analysis."""
    
    def __init__(self, language_model=None):
        """
        Initialize the simple node evaluator.
        
        Args:
            language_model: Optional language model for content evaluation
        """
        self.language_model = language_model
        
        # Keywords that suggest higher value
        self.positive_keywords = [
            "therefore", "thus", "hence", "because", "since", "consequently",
            "significant", "important", "critical", "key", "central", "essential",
            "correct", "accurate", "precise", "valid", "proven", "verified",
            "solution", "answer", "conclusion", "result", "finding"
        ]
        
        # Keywords that suggest lower value
        self.negative_keywords = [
            "maybe", "perhaps", "possibly", "uncertain", "unclear", "ambiguous",
            "mistake", "error", "wrong", "incorrect", "invalid", "unproven",
            "unlikely", "doubtful", "questionable", "problematic", "flawed"
        ]
    
    def evaluate(self, node: TreeNode, tree: ReasoningTree, context: Dict[str, Any]) -> float:
        """
        Evaluate a node based on content analysis.
        
        Args:
            node: The node to evaluate
            tree: The reasoning tree
            context: Additional context for evaluation
            
        Returns:
            Value/score for the node (0-1)
        """
        # Use language model for sophisticated evaluation if available
        if self.language_model:
            try:
                return self._evaluate_with_language_model(node, tree, context)
            except Exception as e:
                logger.warning(f"Error in language model node evaluation: {e}")
                # Fall back to heuristic evaluation
                return self._evaluate_with_heuristics(node, tree, context)
        
        # Fall back to heuristic evaluation
        return self._evaluate_with_heuristics(node, tree, context)
    
    def _evaluate_with_language_model(self, node: TreeNode, tree: ReasoningTree, context: Dict[str, Any]) -> float:
        """
        Evaluate a node using a language model.
        
        Args:
            node: The node to evaluate
            tree: The reasoning tree
            context: Additional context for evaluation
            
        Returns:
            Value/score for the node (0-1)
        """
        # Extract problem statement from context
        problem_statement = context.get("problem_statement", "")
        
        # Get path from root to this node
        path = tree.get_path_from_root(node.id)
        path_text = "\n".join([n.content for n in path])
        
        prompt = f"""
        Evaluate the quality and promise of the following reasoning step as part of a solution path.
        
        Problem: {problem_statement}
        
        Reasoning path so far:
        {path_text}
        
        Rate this reasoning path on a scale from 0 to 1, where 0 is very unpromising and 1 is very promising.
        Consider factors like:
        - Logical validity and coherence
        - Progress toward solving the problem
        - Accuracy of any claims or calculations
        - Relevance to the problem
        - Potential to lead to a correct solution
        
        Rating (0-1):
        """
        
        response = self.language_model.generate(prompt).strip()
        
        # Extract numerical rating
        try:
            # Match any decimal number between 0 and 1
            match = re.search(r'0\.\d+|1\.0|1|0', response)
            if match:
                return float(match.group(0))
            else:
                # If no clear number, estimate based on sentiment
                if any(word in response.lower() for word in ["excellent", "great", "very promising", "strong"]):
                    return 0.9
                elif any(word in response.lower() for word in ["good", "promising", "solid"]):
                    return 0.7
                elif any(word in response.lower() for word in ["average", "moderate", "fair"]):
                    return 0.5
                elif any(word in response.lower() for word in ["poor", "weak", "problematic"]):
                    return 0.3
                elif any(word in response.lower() for word in ["very poor", "useless", "irrelevant"]):
                    return 0.1
                else:
                    return 0.5  # Default to neutral if sentiment unclear
        except:
            # Fall back to heuristic evaluation
            return self._evaluate_with_heuristics(node, tree, context)
    
    def _evaluate_with_heuristics(self, node: TreeNode, tree: ReasoningTree, context: Dict[str, Any]) -> float:
        """
        Evaluate a node using heuristics.
        
        Args:
            node: The node to evaluate
            tree: The reasoning tree
            context: Additional context for evaluation
            
        Returns:
            Value/score for the node (0-1)
        """
        content = node.content.lower()
        
        # Base score depends on node type
        base_scores = {
            TreeNodeType.ROOT: 0.5,
            TreeNodeType.OBSERVATION: 0.5,
            TreeNodeType.HYPOTHESIS: 0.6,
            TreeNodeType.DECOMPOSITION: 0.6,
            TreeNodeType.EXPLORATION: 0.5,
            TreeNodeType.VERIFICATION: 0.7,
            TreeNodeType.CALCULATION: 0.7,
            TreeNodeType.INFERENCE: 0.6,
            TreeNodeType.CONCLUSION: 0.8,
            TreeNodeType.DEAD_END: 0.1,
            TreeNodeType.REFLECTION: 0.6
        }
        
        base_score = base_scores.get(node.node_type, 0.5)
        
        # Adjust score based on depth
        depth_factor = min(node.depth / 5, 1.0)  # Favor deeper nodes up to a point
        
        # Count positive and negative keywords
        positive_count = sum(1 for keyword in self.positive_keywords if keyword in content)
        negative_count = sum(1 for keyword in self.negative_keywords if keyword in content)
        
        # Calculate keyword score
        max_keywords = max(1, positive_count + negative_count)
        keyword_score = (positive_count - negative_count) / max_keywords + 0.5  # Scale to [0, 1]
        
        # Additional heuristics
        
        # Length heuristic (favor more substantive steps)
        length = len(content.split())
        length_score = min(length / 50, 1.0)  # Cap at 50 words
        
        # Complexity heuristic (favor more complex reasoning)
        # Count logical connectives
        logical_connectives = ["because", "therefore", "thus", "hence", "since", "if", "then"]
        connective_count = sum(1 for conn in logical_connectives if conn in content)
        complexity_score = min(connective_count / 3, 1.0)  # Cap at 3 connectives
        
        # Combine scores
        combined_score = (
            0.3 * base_score +
            0.2 * depth_factor +
            0.3 * keyword_score +
            0.1 * length_score +
            0.1 * complexity_score
        )
        
        # Ensure score is in [0, 1]
        return max(0.0, min(1.0, combined_score))

class MCTSNodeEvaluator(NodeEvaluator):
    """Monte Carlo Tree Search (MCTS) based node evaluator."""
    
    def __init__(self, language_model=None, num_simulations: int = 5):
        """
        Initialize the MCTS node evaluator.
        
        Args:
            language_model: Optional language model for simulations
            num_simulations: Number of simulations to run
        """
        self.language_model = language_model
        self.num_simulations = num_simulations
        self.base_evaluator = SimpleNodeEvaluator(language_model)
    
    def evaluate(self, node: TreeNode, tree: ReasoningTree, context: Dict[str, Any]) -> float:
        """
        Evaluate a node using Monte Carlo simulations.
        
        Args:
            node: The node to evaluate
            tree: The reasoning tree
            context: Additional context for evaluation
            
        Returns:
            Value/score for the node (0-1)
        """
        # If language model not available, fall back to simple evaluation
        if not self.language_model:
            return self.base_evaluator.evaluate(node, tree, context)
        
        # Get base score
        base_score = self.base_evaluator.evaluate(node, tree, context)
        
        # Run simulations
        simulation_scores = []
        
        for _ in range(self.num_simulations):
            try:
                sim_score = self._run_simulation(node, tree, context)
                simulation_scores.append(sim_score)
            except Exception as e:
                logger.warning(f"Error in MCTS simulation: {e}")
        
        # If no successful simulations, return base score
        if not simulation_scores:
            return base_score
        
        # Combine base score with average simulation score
        avg_sim_score = sum(simulation_scores) / len(simulation_scores)
        combined_score = 0.3 * base_score + 0.7 * avg_sim_score
        
        return combined_score
    
    def _run_simulation(self, node: TreeNode, tree: ReasoningTree, context: Dict[str, Any]) -> float:
        """
        Run a single Monte Carlo simulation.
        
        Args:
            node: The starting node for the simulation
            tree: The reasoning tree
            context: Additional context for evaluation
            
        Returns:
            Simulation score (0-1)
        """
        # Extract problem statement from context
        problem_statement = context.get("problem_statement", "")
        
        # Get path from root to this node
        path = tree.get_path_from_root(node.id)
        path_text = "\n".join([n.content for n in path])
        
        # Generate a simulation of continuing this path
        prompt = f"""
        Continue the following reasoning path to solve the problem. Add 2-3 more steps that would 
        logically follow from the current path and lead to a solution.
        
        Problem: {problem_statement}
        
        Current reasoning path:
        {path_text}
        
        Additional reasoning steps:
        """
        
        simulation = self.language_model.generate(prompt).strip()
        
        # Evaluate the simulated continuation
        eval_prompt = f"""
        Evaluate how likely the following reasoning path is to lead to a correct solution.
        
        Problem: {problem_statement}
        
        Complete reasoning path:
        {path_text}
        
        {simulation}
        
        Rate the likelihood of this path leading to a correct solution on a scale from 0 to 1,
        where 0 is very unlikely and 1 is very likely.
        
        Rating (0-1):
        """
        
        response = self.language_model.generate(eval_prompt).strip()
        
        # Extract numerical rating
        try:
            # Match any decimal number between 0 and 1
            match = re.search(r'0\.\d+|1\.0|1|0', response)
            if match:
                return float(match.group(0))
            else:
                # Fallback to sentiment analysis
                if any(word in response.lower() for word in ["excellent", "great", "very likely", "strong"]):
                    return 0.9
                elif any(word in response.lower() for word in ["good", "likely", "promising"]):
                    return 0.7
                elif any(word in response.lower() for word in ["average", "moderate", "possible"]):
                    return 0.5
                elif any(word in response.lower() for word in ["unlikely", "poor", "weak"]):
                    return 0.3
                elif any(word in response.lower() for word in ["very unlikely", "useless", "wrong"]):
                    return 0.1
                else:
                    return 0.5  # Default to neutral
        except:
            # Default score if extraction fails
            return 0.5

class SearchController:
    """Controls the search process in the reasoning tree."""
    
    def __init__(
        self, 
        strategy: SearchStrategy = SearchStrategy.BEST_FIRST,
        evaluator: Optional[NodeEvaluator] = None,
        language_model=None,
        thought_latent_space: Optional[ThoughtLatentSpace] = None,
        max_depth: int = 10,
        exploration_weight: float = 1.0
    ):
        """
        Initialize the search controller.
        
        Args:
            strategy: Search strategy to use
            evaluator: Node evaluator (if None, SimpleNodeEvaluator is used)
            language_model: Optional language model for node generation and evaluation
            thought_latent_space: Optional thought latent space for embedding reasoning states
            max_depth: Maximum search depth
            exploration_weight: Exploration weight for search strategies that use it
        """
        self.strategy = strategy
        self.language_model = language_model
        self.thought_latent_space = thought_latent_space
        self.max_depth = max_depth
        self.exploration_weight = exploration_weight
        
        # Initialize evaluator if not provided
        if evaluator is None:
            if language_model and strategy == SearchStrategy.MONTE_CARLO:
                self.evaluator = MCTSNodeEvaluator(language_model)
            else:
                self.evaluator = SimpleNodeEvaluator(language_model)
        else:
            self.evaluator = evaluator
        
        # Performance tracking
        self.search_statistics = {
            "nodes_evaluated": 0,
            "nodes_expanded": 0,
            "max_depth_reached": 0,
            "backtracking_events": 0,
            "strategy_switches": 0
        }
    
    def search(
        self, 
        tree: ReasoningTree, 
        context: Dict[str, Any],
        max_iterations: int = 100
    ) -> Optional[List[TreeNode]]:
        """
        Perform search in the reasoning tree.
        
        Args:
            tree: The reasoning tree to search
            context: Context for search, including problem statement
            max_iterations: Maximum number of search iterations
            
        Returns:
            List of nodes in the best path found, or None if no solution found
        """
        # Check if tree is empty
        if not tree.root_id:
            logger.warning("Cannot search empty tree")
            return None
        
        # Reset search statistics
        self.search_statistics = {
            "nodes_evaluated": 0,
            "nodes_expanded": 0,
            "max_depth_reached": 0,
            "backtracking_events": 0,
            "strategy_switches": 0
        }
        
        # Track best path found so far
        best_path = None
        best_path_score = -float('inf')
        
        # Reset current node to root
        tree.current_node_id = tree.root_id
        
        # Main search loop
        for iteration in range(max_iterations):
            # Apply current search strategy to select next node to expand
            next_node_id = self._select_next_node(tree, context)
            
            # If no next node, try backtracking
            if next_node_id is None:
                if tree.current_node_id == tree.root_id:
                    # If at root and no next node, search is complete
                    break
                
                # Backtrack
                tree.backtrack()
                self.search_statistics["backtracking_events"] += 1
                continue
            
            # Set current node to selected node
            tree.current_node_id = next_node_id
            current_node = tree.get_node(next_node_id)
            
            # Update max depth reached
            self.search_statistics["max_depth_reached"] = max(
                self.search_statistics["max_depth_reached"],
                current_node.depth
            )
            
            # Evaluate current node
            node_value = self.evaluator.evaluate(current_node, tree, context)
            current_node.value = node_value
            self.search_statistics["nodes_evaluated"] += 1
            
            # If node is a conclusion, check if it's the best path so far
            if current_node.node_type == TreeNodeType.CONCLUSION:
                path = tree.get_path_from_root(current_node.id)
                path_score = sum(node.value for node in path)
                
                if path_score > best_path_score:
                    best_path = path
                    best_path_score = path_score
            
            # If node is a leaf, expand it
            if current_node.is_leaf():
                self._expand_node(current_node, tree, context)
                self.search_statistics["nodes_expanded"] += 1
                
            # If maximum depth reached, backtrack
            if current_node.depth >= self.max_depth:
                tree.backtrack()
                self.search_statistics["backtracking_events"] += 1
                
            # Dynamic strategy adjustment
            if self.strategy == SearchStrategy.DYNAMIC and iteration % 10 == 0:
                new_strategy = self._adjust_strategy(tree, context)
                if new_strategy != self.strategy:
                    self.search_statistics["strategy_switches"] += 1
        
        # Return best path found
        if best_path:
            return best_path
            
        # If no conclusion found, return the path to the best leaf node
        best_leaf = tree.get_best_leaf_node()
        if best_leaf:
            return tree.get_path_from_root(best_leaf.id)
            
        # If no leaf nodes, return the path to the current node
        if tree.current_node_id:
            return tree.get_path_from_root(tree.current_node_id)
            
        return None
    
    def _select_next_node(self, tree: ReasoningTree, context: Dict[str, Any]) -> Optional[str]:
        """
        Select the next node to expand using the current search strategy.
        
        Args:
            tree: The reasoning tree
            context: Context for selection
            
        Returns:
            ID of the next node to expand, or None if no suitable node found
        """
        if self.strategy == SearchStrategy.DEPTH_FIRST:
            return self._select_depth_first(tree)
        elif self.strategy == SearchStrategy.BREADTH_FIRST:
            return self._select_breadth_first(tree)
        elif self.strategy == SearchStrategy.BEST_FIRST:
            return self._select_best_first(tree, context)
        elif self.strategy == SearchStrategy.MONTE_CARLO:
            return self._select_monte_carlo(tree, context)
        elif self.strategy == SearchStrategy.BEAM_SEARCH:
            return self._select_beam_search(tree, context)
        elif self.strategy == SearchStrategy.DYNAMIC:
            # Select based on current tree state
            return self._select_dynamic(tree, context)
        else:
            # Default to best-first
            return self._select_best_first(tree, context)
    
    def _select_depth_first(self, tree: ReasoningTree) -> Optional[str]:
        """
        Select the next node using depth-first search.
        
        Args:
            tree: The reasoning tree
            
        Returns:
            ID of the next node to expand, or None if no suitable node found
        """
        current_node = tree.get_current_node()
        if current_node is None:
            return None
            
        # If current node has unexplored children, return first unexplored child
        if current_node.children_ids:
            # DFS prioritizes the first child (leftmost in the tree)
            return current_node.children_ids[0]
            
        # If current node is a leaf with unexplored potential, expand it
        if current_node.has_unexplored_potential():
            return current_node.id
            
        # Otherwise, backtrack
        return None
    
    def _select_breadth_first(self, tree: ReasoningTree) -> Optional[str]:
        """
        Select the next node using breadth-first search.
        
        Args:
            tree: The reasoning tree
            
        Returns:
            ID of the next node to expand, or None if no suitable node found
        """
        # BFS uses a queue to track nodes at each level
        queue = []
        
        # Start with root
        root_node = tree.get_root_node()
        if root_node is None:
            return None
            
        queue.append(root_node.id)
        
        # BFS traversal
        while queue:
            node_id = queue.pop(0)
            node = tree.get_node(node_id)
            
            if node is None:
                continue
                
            # If node has unexplored potential, return it
            if node.has_unexplored_potential():
                return node.id
                
            # Add children to queue
            queue.extend(node.children_ids)
        
        # If no nodes with unexplored potential, return None
        return None
    
    def _select_best_first(self, tree: ReasoningTree, context: Dict[str, Any]) -> Optional[str]:
        """
        Select the next node using best-first search.
        
        Args:
            tree: The reasoning tree
            context: Context for selection
            
        Returns:
            ID of the next node to expand, or None if no suitable node found
        """
        # Track best node and score
        best_node_id = None
        best_score = -float('inf')
        
        # Evaluate all nodes with unexplored potential
        for node_id, node in tree.nodes.items():
            if node.has_unexplored_potential():
                # If not already evaluated, evaluate node
                if node.value == 0.0 and node.visits == 1:
                    node.value = self.evaluator.evaluate(node, tree, context)
                    self.search_statistics["nodes_evaluated"] += 1
                
                # Calculate score (value + exploration bonus)
                exploration_bonus = self.exploration_weight * (1.0 / (node.visits + 1))
                score = node.value + exploration_bonus
                
                if score > best_score:
                    best_score = score
                    best_node_id = node_id
        
        return best_node_id
    
    def _select_monte_carlo(self, tree: ReasoningTree, context: Dict[str, Any]) -> Optional[str]:
        """
        Select the next node using Monte Carlo Tree Search.
        
        Args:
            tree: The reasoning tree
            context: Context for selection
            
        Returns:
            ID of the next node to expand, or None if no suitable node found
        """
        # Get the most promising unexplored node using UCT
        best_node = tree.get_most_promising_unexplored_node(self.exploration_weight)
        
        if best_node:
            return best_node.id
            
        # If no unexplored nodes, try to select the best child of the current node
        current_node = tree.get_current_node()
        if current_node and current_node.children_ids:
            best_child_id = None
            best_uct = -float('inf')
            
            for child_id in current_node.children_ids:
                child = tree.get_node(child_id)
                if child:
                    uct = child.get_uct_score(self.exploration_weight)
                    if uct > best_uct:
                        best_uct = uct
                        best_child_id = child_id
                        
            return best_child_id
        
        return None
    
    def _select_beam_search(self, tree: ReasoningTree, context: Dict[str, Any]) -> Optional[str]:
        """
        Select the next node using beam search.
        
        Args:
            tree: The reasoning tree
            context: Context for selection
            
        Returns:
            ID of the next node to expand, or None if no suitable node found
        """
        # Beam search maintains a "beam" of the best k nodes at each level
        beam_width = min(5, len(tree.nodes))  # Adjust beam width based on tree size
        
        # Group nodes by depth
        nodes_by_depth = defaultdict(list)
        for node_id, node in tree.nodes.items():
            if node.has_unexplored_potential():
                nodes_by_depth[node.depth].append(node)
        
        # Start with the deepest level
        max_depth = max(nodes_by_depth.keys()) if nodes_by_depth else 0
        
        # Select nodes from the deepest level first
        for depth in range(max_depth, -1, -1):
            if depth in nodes_by_depth:
                nodes = nodes_by_depth[depth]
                
                # Evaluate all nodes at this depth
                for node in nodes:
                    if node.value == 0.0 and node.visits == 1:
                        node.value = self.evaluator.evaluate(node, tree, context)
                        self.search_statistics["nodes_evaluated"] += 1
                
                # Sort by value and select the best
                nodes.sort(key=lambda n: n.value, reverse=True)
                best_nodes = nodes[:beam_width]
                
                if best_nodes:
                    return best_nodes[0].id
        
        return None
    
    def _select_dynamic(self, tree: ReasoningTree, context: Dict[str, Any]) -> Optional[str]:
        """
        Select the next node using a dynamically adjusted strategy.
        
        Args:
            tree: The reasoning tree
            context: Context for selection
            
        Returns:
            ID of the next node to expand, or None if no suitable node found
        """
        # Get tree statistics to determine best strategy
        tree_stats = tree.calculate_tree_statistics()
        
        # Adjust strategy based on tree statistics
        if tree_stats["num_nodes"] < 10:
            # For small trees, use breadth-first to explore broadly
            return self._select_breadth_first(tree)
        elif tree_stats["average_branching_factor"] > 3:
            # For trees with high branching factor, use beam search
            return self._select_beam_search(tree, context)
        elif self.search_statistics["backtracking_events"] > 0.2 * self.search_statistics["nodes_expanded"]:
            # If backtracking is frequent, use Monte Carlo for better exploration
            return self._select_monte_carlo(tree, context)
        else:
            # Default to best-first search
            return self._select_best_first(tree, context)
    
    def _adjust_strategy(self, tree: ReasoningTree, context: Dict[str, Any]) -> SearchStrategy:
        """
        Dynamically adjust the search strategy based on the current state.
        
        Args:
            tree: The reasoning tree
            context: Context for adjustment
            
        Returns:
            Adjusted search strategy
        """
        # Get tree statistics
        tree_stats = tree.calculate_tree_statistics()
        
        # Calculate success metrics
        success_rate = self.search_statistics["nodes_expanded"] / max(1, self.search_statistics["nodes_evaluated"])
        depth_progress = self.search_statistics["max_depth_reached"] / max(1, self.max_depth)
        
        # Adjust strategy based on metrics
        if success_rate < 0.3:
            # If success rate is low, try Monte Carlo for better exploration
            return SearchStrategy.MONTE_CARLO
        elif depth_progress < 0.3:
            # If depth progress is low, try depth-first to go deeper
            return SearchStrategy.DEPTH_FIRST
        elif tree_stats["average_branching_factor"] > 3:
            # If branching factor is high, use beam search to focus
            return SearchStrategy.BEAM_SEARCH
        else:
            # Default to best-first search
            return SearchStrategy.BEST_FIRST
    
    def _expand_node(self, node: TreeNode, tree: ReasoningTree, context: Dict[str, Any]) -> List[str]:
        """
        Expand a node by generating children.
        
        Args:
            node: The node to expand
            tree: The reasoning tree
            context: Context for expansion
            
        Returns:
            List of generated child node IDs
        """
        # If language model is available, use it to generate children
        if self.language_model:
            return self._expand_with_language_model(node, tree, context)
        else:
            # Fall back to rule-based expansion
            return self._expand_with_rules(node, tree, context)
    
    def _expand_with_language_model(self, node: TreeNode, tree: ReasoningTree, context: Dict[str, Any]) -> List[str]:
        """
        Expand a node using a language model.
        
        Args:
            node: The node to expand
            tree: The reasoning tree
            context: Context for expansion
            
        Returns:
            List of generated child node IDs
        """
        # Extract problem statement from context
        problem_statement = context.get("problem_statement", "")
        
        # Get path from root to this node
        path = tree.get_path_from_root(node.id)
        path_text = "\n".join([n.content for n in path])
        
        # Determine how many branches to create based on context
        branching_factor = context.get("branching_factor", 2)
        
        # Generate next steps
        prompt = f"""
        Generate the next {branching_factor} possible steps in this reasoning process.
        Each step should be a logical continuation of the reasoning path so far and help make progress toward solving the problem.
        Make the steps diverse, exploring different approaches or aspects of the problem.
        
        Problem: {problem_statement}
        
        Reasoning path so far:
        {path_text}
        
        Generate {branching_factor} different next steps, each in the format:
        STEP: [step content]
        TYPE: [one of OBSERVATION, HYPOTHESIS, DECOMPOSITION, EXPLORATION, VERIFICATION, CALCULATION, INFERENCE, CONCLUSION, REFLECTION]
        
        Next steps:
        """
        
        try:
            response = self.language_model.generate(prompt)
            
            # Parse generated steps
            generated_steps = []
            current_step = {"content": "", "type": None}
            
            for line in response.strip().split('\n'):
                line = line.strip()
                
                if line.startswith('STEP:'):
                    # Start a new step
                    if current_step["content"] and current_step["type"]:
                        generated_steps.append(current_step)
                    current_step = {"content": line[5:].strip(), "type": None}
                elif line.startswith('TYPE:'):
                    type_str = line[5:].strip().upper()
                    try:
                        node_type = TreeNodeType[type_str]
                        current_step["type"] = node_type
                    except KeyError:
                        # Default to inference if type is invalid
                        current_step["type"] = TreeNodeType.INFERENCE
                elif current_step["content"]:
                    # Append to current step content
                    current_step["content"] += " " + line
            
            # Add the last step if it exists
            if current_step["content"] and current_step["type"]:
                generated_steps.append(current_step)
            
            # If no steps were parsed, fall back to rule-based expansion
            if not generated_steps:
                return self._expand_with_rules(node, tree, context)
                
            # Add steps to tree
            child_ids = []
            for step in generated_steps:
                child_id = tree.add_node(
                    content=step["content"],
                    node_type=step["type"],
                    parent_id=node.id,
                    value=0.0  # Will be evaluated later
                )
                child_ids.append(child_id)
                
            return child_ids
            
        except Exception as e:
            logger.warning(f"Error in language model node expansion: {e}")
            return self._expand_with_rules(node, tree, context)
    
    def _expand_with_rules(self, node: TreeNode, tree: ReasoningTree, context: Dict[str, Any]) -> List[str]:
        """
        Expand a node using rule-based methods.
        
        Args:
            node: The node to expand
            tree: The reasoning tree
            context: Context for expansion
            
        Returns:
            List of generated child node IDs
        """
        # Extract problem statement from context
        problem_statement = context.get("problem_statement", "")
        
        # Simple rule-based expansion
        child_ids = []
        
        # Generate 1-2 children based on node type
        if node.node_type == TreeNodeType.ROOT:
            # For root, generate initial approaches
            child_ids.append(tree.add_node(
                content=f"Let's break down the problem: {problem_statement}",
                node_type=TreeNodeType.DECOMPOSITION,
                parent_id=node.id,
                value=0.5
            ))
            
            child_ids.append(tree.add_node(
                content=f"Let's consider what we know about this problem.",
                node_type=TreeNodeType.OBSERVATION,
                parent_id=node.id,
                value=0.5
            ))
            
        elif node.node_type == TreeNodeType.OBSERVATION:
            # For observations, generate hypotheses or decompositions
            child_ids.append(tree.add_node(
                content=f"Based on these observations, one hypothesis is...",
                node_type=TreeNodeType.HYPOTHESIS,
                parent_id=node.id,
                value=0.5
            ))
            
        elif node.node_type == TreeNodeType.HYPOTHESIS:
            # For hypotheses, generate inferences or verifications
            child_ids.append(tree.add_node(
                content=f"If this hypothesis is correct, then we would expect...",
                node_type=TreeNodeType.INFERENCE,
                parent_id=node.id,
                value=0.5
            ))
            
            child_ids.append(tree.add_node(
                content=f"To verify this hypothesis, we should check...",
                node_type=TreeNodeType.VERIFICATION,
                parent_id=node.id,
                value=0.5
            ))
            
        elif node.node_type == TreeNodeType.DECOMPOSITION:
            # For decompositions, generate explorations of sub-problems
            child_ids.append(tree.add_node(
                content=f"Let's focus on the first sub-problem...",
                node_type=TreeNodeType.EXPLORATION,
                parent_id=node.id,
                value=0.5
            ))
            
        elif node.node_type == TreeNodeType.EXPLORATION:
            # For explorations, generate inferences or calculations
            child_ids.append(tree.add_node(
                content=f"From this exploration, we can infer...",
                node_type=TreeNodeType.INFERENCE,
                parent_id=node.id,
                value=0.5
            ))
            
        elif node.node_type == TreeNodeType.VERIFICATION:
            # For verifications, generate confirmations or refutations
            child_ids.append(tree.add_node(
                content=f"The verification confirms that...",
                node_type=TreeNodeType.INFERENCE,
                parent_id=node.id,
                value=0.6
            ))
            
            child_ids.append(tree.add_node(
                content=f"The verification suggests we need to reconsider...",
                node_type=TreeNodeType.REFLECTION,
                parent_id=node.id,
                value=0.4
            ))
            
        elif node.node_type == TreeNodeType.CALCULATION:
            # For calculations, generate inferences from results
            child_ids.append(tree.add_node(
                content=f"From the calculation, we can conclude...",
                node_type=TreeNodeType.INFERENCE,
                parent_id=node.id,
                value=0.6
            ))
            
        elif node.node_type == TreeNodeType.INFERENCE:
            # For inferences, generate further inferences or conclusions
            child_ids.append(tree.add_node(
                content=f"Building on this inference, we can further deduce...",
                node_type=TreeNodeType.INFERENCE,
                parent_id=node.id,
                value=0.5
            ))
            
            child_ids.append(tree.add_node(
                content=f"Therefore, a reasonable conclusion is...",
                node_type=TreeNodeType.CONCLUSION,
                parent_id=node.id,
                value=0.7
            ))
            
        elif node.node_type == TreeNodeType.REFLECTION:
            # For reflections, generate new hypotheses or approaches
            child_ids.append(tree.add_node(
                content=f"Upon reflection, a better approach might be...",
                node_type=TreeNodeType.HYPOTHESIS,
                parent_id=node.id,
                value=0.5
            ))
            
        else:
            # For other node types, generate a generic continuation
            child_ids.append(tree.add_node(
                content=f"The next step in the reasoning process is...",
                node_type=TreeNodeType.INFERENCE,
                parent_id=node.id,
                value=0.5
            ))
        
        return child_ids

class TreeOfThoughtExploration:
    """
    Main class implementing the Tree of Thought approach for reasoning exploration.
    
    This approach organizes reasoning paths in a hierarchical tree structure, enabling
    backtracking, branching, and exploration of multiple solution paths.
    """
    
    def __init__(
        self, 
        language_model=None,
        thought_latent_space: Optional[ThoughtLatentSpace] = None,
        knowledge_base: Optional[EpisodicKnowledgeBase] = None,
        max_depth: int = 10,
        branching_factor: int = 3,
        search_strategy: SearchStrategy = SearchStrategy.BEST_FIRST,
        exploration_weight: float = 1.0,
        config: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize the Tree of Thought exploration.
        
        Args:
            language_model: Optional language model for reasoning
            thought_latent_space: Optional thought latent space for embedding reasoning states
            knowledge_base: Optional knowledge base for retrieving information
            max_depth: Maximum depth of the reasoning tree
            branching_factor: Maximum branching factor for node expansion
            search_strategy: Strategy for exploring the reasoning tree
            exploration_weight: Weight for exploration vs. exploitation
            config: Optional configuration dictionary
        """
        self.language_model = language_model
        self.thought_latent_space = thought_latent_space
        self.knowledge_base = knowledge_base
        self.max_depth = max_depth
        self.branching_factor = branching_factor
        self.search_strategy = search_strategy
        self.exploration_weight = exploration_weight
        self.config = config or {}
        
        # Initialize components
        self.evaluator = self._initialize_evaluator()
        self.search_controller = SearchController(
            strategy=search_strategy,
            evaluator=self.evaluator,
            language_model=language_model,
            thought_latent_space=thought_latent_space,
            max_depth=max_depth,
            exploration_weight=exploration_weight
        )
        
        # Register handlers for critique and bias detection
        self.critique_handler = None
        self.bias_handler = None
        
        # History of reasoning trees
        self.tree_history = []
        
        # Performance metrics
        self.performance_metrics = {
            "total_trees": 0,
            "total_nodes": 0,
            "successful_explorations": 0,
            "average_path_length": 0
        }
        
        # Logger
        self.logger = get_logger(__name__)
    
    def _initialize_evaluator(self) -> NodeEvaluator:
        """
        Initialize the node evaluator based on available components.
        
        Returns:
            Initialized NodeEvaluator instance
        """
        if self.language_model and self.search_strategy == SearchStrategy.MONTE_CARLO:
            return MCTSNodeEvaluator(self.language_model)
        else:
            return SimpleNodeEvaluator(self.language_model)
    
    def register_critique_handler(self, handler: Callable) -> None:
        """
        Register a handler for critiquing reasoning.
        
        Args:
            handler: Function that takes a reasoning text and returns a critique
        """
        self.critique_handler = handler
    
    def register_bias_handler(self, handler: Callable) -> None:
        """
        Register a handler for detecting biases in reasoning.
        
        Args:
            handler: Function that takes a reasoning text and returns detected biases
        """
        self.bias_handler = handler
    
    def explore(
        self, 
        problem_statement: str,
        context: Optional[Dict[str, Any]] = None,
        max_iterations: int = 100
    ) -> Tuple[ReasoningTree, List[str]]:
        """
        Explore solutions to a problem using Tree of Thought.
        
        Args:
            problem_statement: The problem to solve
            context: Optional context information
            max_iterations: Maximum number of exploration iterations
            
        Returns:
            Tuple of (reasoning_tree, best_path)
        """
        # Initialize context
        if context is None:
            context = {}
        context["problem_statement"] = problem_statement
        context["branching_factor"] = self.branching_factor
        
        # Initialize reasoning tree
        tree = self._initialize_tree(problem_statement)
        
        # Run search
        best_path_nodes = self.search_controller.search(tree, context, max_iterations)
        
        # Extract best path as text
        best_path = []
        if best_path_nodes:
            best_path = [node.content for node in best_path_nodes]
            
            # Apply critique if available
            if self.critique_handler:
                path_text = "\n".join(best_path)
                critique = self.critique_handler(path_text)
                tree.metadata["critique"] = critique
                
            # Apply bias detection if available
            if self.bias_handler:
                path_text = "\n".join(best_path)
                biases = self.bias_handler(path_text)
                tree.metadata["detected_biases"] = biases
        
        # Store tree in history
        self.tree_history.append({
            "problem_statement": problem_statement,
            "tree": tree.to_dict(),
            "best_path": best_path,
            "search_stats": self.search_controller.search_statistics.copy()
        })
        
        # Update performance metrics
        self._update_performance_metrics(tree, best_path)
        
        return tree, best_path
    
    def explore_and_return_multiple_paths(
        self, 
        problem_statement: str,
        context: Optional[Dict[str, Any]] = None,
        max_paths: int = 3,
        max_iterations: int = 100
    ) -> Tuple[ReasoningTree, List[List[str]]]:
        """
        Explore solutions and return multiple alternative paths.
        
        Args:
            problem_statement: The problem to solve
            context: Optional context information
            max_paths: Maximum number of paths to return
            max_iterations: Maximum number of exploration iterations
            
        Returns:
            Tuple of (reasoning_tree, list_of_paths)
        """
        # Initialize context
        if context is None:
            context = {}
        context["problem_statement"] = problem_statement
        context["branching_factor"] = self.branching_factor
        
        # Initialize reasoning tree
        tree = self._initialize_tree(problem_statement)
        
        # Run search
        _ = self.search_controller.search(tree, context, max_iterations)
        
        # Extract multiple paths
        paths = []
        
        # Get leaf nodes
        leaf_nodes = [node for node in tree.nodes.values() if node.is_leaf()]
        
        # Sort by value
        leaf_nodes.sort(key=lambda node: node.value, reverse=True)
        
        # Extract top paths
        for leaf in leaf_nodes[:max_paths]:
            path_nodes = tree.get_path_from_root(leaf.id)
            path = [node.content for node in path_nodes]
            paths.append(path)
            
            # Stop if we have enough paths
            if len(paths) >= max_paths:
                break
        
        # If we don't have enough paths, generate more through different branches
        if len(paths) < max_paths and tree.root_id:
            root_node = tree.get_node(tree.root_id)
            if root_node and root_node.children_ids:
                for child_id in root_node.children_ids:
                    # Skip if we already have enough paths
                    if len(paths) >= max_paths:
                        break
                        
                    child_node = tree.get_node(child_id)
                    if child_node:
                        # Get best path from this branch
                        branch_leaf_nodes = []
                        for node_id, node in tree.nodes.items():
                            if node.is_leaf() and child_id in [n.id for n in tree.get_path_to_root(node_id)]:
                                branch_leaf_nodes.append(node)
                                
                        if branch_leaf_nodes:
                            best_branch_leaf = max(branch_leaf_nodes, key=lambda node: node.value)
                            branch_path_nodes = tree.get_path_from_root(best_branch_leaf.id)
                            branch_path = [node.content for node in branch_path_nodes]
                            
                            # Check if this path is sufficiently different from existing paths
                            if not any(self._paths_are_similar(branch_path, existing_path) for existing_path in paths):
                                paths.append(branch_path)
        
        # Ensure we return at least one path
        if not paths and tree.root_id:
            root_node = tree.get_node(tree.root_id)
            paths.append([root_node.content])
        
        # Store tree in history
        self.tree_history.append({
            "problem_statement": problem_statement,
            "tree": tree.to_dict(),
            "paths": paths,
            "search_stats": self.search_controller.search_statistics.copy()
        })
        
        # Update performance metrics
        self._update_performance_metrics(tree, paths[0] if paths else [])
        
        return tree, paths
    
    def _initialize_tree(self, problem_statement: str) -> ReasoningTree:
        """
        Initialize a reasoning tree with the problem statement.
        
        Args:
            problem_statement: The problem to solve
            
        Returns:
            Initialized ReasoningTree
        """
        tree = ReasoningTree()
        
        # Add root node
        tree.add_node(
            content=f"We need to solve the following problem: {problem_statement}",
            node_type=TreeNodeType.ROOT,
            value=0.5
        )
        
        return tree
    
    def _paths_are_similar(self, path1: List[str], path2: List[str]) -> bool:
        """
        Check if two paths are semantically similar.
        
        Args:
            path1: First path as list of strings
            path2: Second path as list of strings
            
        Returns:
            True if paths are similar, False otherwise
        """
        # Short-circuit if paths are identical
        if path1 == path2:
            return True
            
        # If path lengths differ significantly, they're different
        if abs(len(path1) - len(path2)) > 2:
            return False
            
        # Compare path endings (conclusions)
        if len(path1) > 0 and len(path2) > 0:
            last1 = path1[-1].lower()
            last2 = path2[-1].lower()
            
            # Calculate word overlap for conclusions
            words1 = set(last1.split())
            words2 = set(last2.split())
            
            if not words1 or not words2:
                return False
                
            overlap = len(words1.intersection(words2))
            similarity = overlap / min(len(words1), len(words2))
            
            # If conclusions are very similar, paths are similar
            if similarity > 0.7:
                return True
        
        # If we have a thought latent space, use it for semantic similarity
        if self.thought_latent_space:
            try:
                # Embed both paths
                path1_text = " ".join(path1)
                path2_text = " ".join(path2)
                
                embed1 = self.thought_latent_space.encode_concept(path1_text)
                embed2 = self.thought_latent_space.encode_concept(path2_text)
                
                # Calculate cosine similarity
                similarity = self.thought_latent_space.compute_similarity(embed1, embed2)
                
                # If similarity is high, paths are similar
                return similarity > 0.8
            except Exception as e:
                self.logger.warning(f"Error calculating path similarity: {e}")
        
        # Default to not similar
        return False
    
    def _update_performance_metrics(self, tree: ReasoningTree, best_path: List[str]) -> None:
        """
        Update performance metrics after an exploration.
        
        Args:
            tree: Reasoning tree
            best_path: Best path found
        """
        # Update metrics
        self.performance_metrics["total_trees"] += 1
        self.performance_metrics["total_nodes"] += len(tree.nodes)
        
        # Consider exploration successful if a path was found
        if best_path:
            self.performance_metrics["successful_explorations"] += 1
            
            # Update average path length
            current_avg = self.performance_metrics["average_path_length"]
            current_count = self.performance_metrics["successful_explorations"]
            
            # Incremental average update
            self.performance_metrics["average_path_length"] = (
                (current_avg * (current_count - 1) + len(best_path)) / current_count
            )
    
    def calculate_path_confidence(self, path: List[str]) -> float:
        """
        Calculate confidence in a reasoning path.
        
        Args:
            path: Reasoning path as list of strings
            
        Returns:
            Confidence score (0-1)
        """
        if not path:
            return 0.0
            
        # If language model is available, use it to evaluate the path
        if self.language_model:
            try:
                path_text = "\n".join(path)
                
                prompt = f"""
                Evaluate the quality and confidence of the following reasoning path.
                
                Reasoning path:
                {path_text}
                
                Consider factors like:
                - Logical validity and coherence
                - Depth and thoroughness of reasoning
                - Accuracy of any claims or calculations
                - Strength of the conclusion
                
                On a scale from 0 to 1, where 0 is very low confidence and 1 is very high confidence,
                rate your confidence in this reasoning path.
                
                Confidence score (0-1):
                """
                
                response = self.language_model.generate(prompt).strip()
                
                # Extract numerical rating
                match = re.search(r'0\.\d+|1\.0|1|0', response)
                if match:
                    return float(match.group(0))
                else:
                    # Fallback to heuristic confidence
                    return self._calculate_heuristic_confidence(path)
            except Exception as e:
                self.logger.warning(f"Error calculating path confidence: {e}")
                return self._calculate_heuristic_confidence(path)
        else:
            # Use heuristic confidence calculation
            return self._calculate_heuristic_confidence(path)
    
    def _calculate_heuristic_confidence(self, path: List[str]) -> float:
        """
        Calculate confidence in a path using heuristics.
        
        Args:
            path: Reasoning path as list of strings
            
        Returns:
            Confidence score (0-1)
        """
        if not path:
            return 0.0
            
        # Base confidence depends on path length
        length_confidence = min(len(path) / 10, 1.0)  # Cap at 10 steps
        
        # Check for logical connectives
        logical_connectives = ["therefore", "thus", "because", "since", "consequently", "hence"]
        connective_count = sum(
            1 for step in path for connective in logical_connectives if connective in step.lower()
        )
        connective_confidence = min(connective_count / 5, 1.0)  # Cap at 5 connectives
        
        # Check for confidence indicators
        confidence_indicators = ["clearly", "certainly", "definitely", "must", "will"]
        confidence_count = sum(
            1 for step in path for indicator in confidence_indicators if indicator in step.lower()
        )
        
        # Check for uncertainty indicators
        uncertainty_indicators = ["maybe", "perhaps", "possibly", "might", "could"]
        uncertainty_count = sum(
            1 for step in path for indicator in uncertainty_indicators if indicator in step.lower()
        )
        
        # Calculate certainty confidence
        if confidence_count + uncertainty_count > 0:
            certainty_ratio = confidence_count / (confidence_count + uncertainty_count)
        else:
            certainty_ratio = 0.5  # Neutral if no indicators
            
        certainty_confidence = 0.3 + (certainty_ratio * 0.7)  # Scale to [0.3, 1.0]
        
        # Combine confidence components
        overall_confidence = (
            0.4 * length_confidence +
            0.4 * connective_confidence +
            0.2 * certainty_confidence
        )
        
        return overall_confidence
    
    def find_alternative_paths(self, tree: ReasoningTree, n: int = 3) -> List[List[str]]:
        """
        Find alternative reasoning paths in a tree.
        
        Args:
            tree: Reasoning tree to analyze
            n: Maximum number of alternative paths to find
            
        Returns:
            List of alternative paths as lists of strings
        """
        # Get leaf nodes
        leaf_nodes = [node for node in tree.nodes.values() if node.is_leaf()]
        
        # Sort by value
        leaf_nodes.sort(key=lambda node: node.value, reverse=True)
        
        # Extract top paths
        paths = []
        for leaf in leaf_nodes[:n+1]:  # Get n+1 to account for possibly skipping the first path
            path_nodes = tree.get_path_from_root(leaf.id)
            path = [node.content for node in path_nodes]
            
            # Ensure we get diverse paths
            if paths and self._paths_are_similar(path, paths[0]):
                continue
                
            paths.append(path)
            
            # Stop if we have enough paths
            if len(paths) >= n:
                break
        
        return paths
    
    def visualize_tree(self, tree: ReasoningTree, output_path: Optional[str] = None) -> None:
        """
        Visualize a reasoning tree.
        
        Args:
            tree: Reasoning tree to visualize
            output_path: Path to save the visualization (if None, display instead)
        """
        tree.visualize(output_path)
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """
        Get performance metrics for Tree of Thought exploration.
        
        Returns:
            Dictionary of performance metrics
        """
        metrics = self.performance_metrics.copy()
        
        # Calculate additional metrics
        if metrics["total_trees"] > 0:
            metrics["success_rate"] = metrics["successful_explorations"] / metrics["total_trees"]
            metrics["average_nodes_per_tree"] = metrics["total_nodes"] / metrics["total_trees"]
        else:
            metrics["success_rate"] = 0.0
            metrics["average_nodes_per_tree"] = 0.0
        
        return metrics
    
    def get_tree_statistics(self, tree: ReasoningTree) -> Dict[str, Any]:
        """
        Get statistics for a reasoning tree.
        
        Args:
            tree: Reasoning tree to analyze
            
        Returns:
            Dictionary of tree statistics
        """
        return tree.calculate_tree_statistics()
    
    def save_tree(self, tree: ReasoningTree, filepath: str) -> bool:
        """
        Save a reasoning tree to a file.
        
        Args:
            tree: Reasoning tree to save
            filepath: Path to save the tree to
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Convert tree to dictionary
            tree_dict = tree.to_dict()
            
            # Save to file
            with open(filepath, 'w') as f:
                json.dump(tree_dict, f, indent=2)
                
            return True
        except Exception as e:
            self.logger.error(f"Error saving tree: {e}")
            return False
    
    def load_tree(self, filepath: str) -> Optional[ReasoningTree]:
        """
        Load a reasoning tree from a file.
        
        Args:
            filepath: Path to load the tree from
            
        Returns:
            Loaded ReasoningTree, or None if loading failed
        """
        try:
            # Load from file
            with open(filepath, 'r') as f:
                tree_dict = json.load(f)
                
            # Convert to tree
            tree = ReasoningTree.from_dict(tree_dict)
            
            return tree
        except Exception as e:
            self.logger.error(f"Error loading tree: {e}")
            return None
    
    def reset(self) -> None:
        """Reset the state of the Tree of Thought exploration."""
        self.tree_history = []
        self.performance_metrics = {
            "total_trees": 0,
            "total_nodes": 0,
            "successful_explorations": 0,
            "average_path_length": 0
        }

# Make key classes available at module level
__all__ = [
    'TreeOfThoughtExploration',
    'ReasoningTree',
    'TreeNode',
    'TreeNodeType',
    'SearchStrategy',
    'NodeEvaluator',
    'SimpleNodeEvaluator',
    'MCTSNodeEvaluator',
    'SearchController'
]

# Optional initialization function to create a default instance
def create_default_tree_of_thought(language_model=None):
    """
    Create a default TreeOfThoughtExploration instance.
    
    Args:
        language_model: Optional language model for reasoning
        
    Returns:
        Configured TreeOfThoughtExploration instance
    """
    return TreeOfThoughtExploration(
        language_model=language_model,
        max_depth=10,
        branching_factor=3,
        search_strategy=SearchStrategy.BEST_FIRST,
        exploration_weight=1.0
    )

# Main execution for standalone usage and testing
if __name__ == "__main__":
    # Example usage
    problem = "What is the most efficient algorithm for finding the shortest path in a weighted graph?"
    
    # Create a mock language model for testing
    class MockLanguageModel:
        def generate(self, prompt):
            if "next" in prompt.lower():
                return """
                STEP: We can use Dijkstra's algorithm for finding the shortest path in a weighted graph with non-negative weights.
                TYPE: HYPOTHESIS
                
                STEP: Another approach is the Bellman-Ford algorithm, which can handle negative weights.
                TYPE: HYPOTHESIS
                
                STEP: A* algorithm could be more efficient if we have additional heuristic information.
                TYPE: HYPOTHESIS
                """
            elif "evaluate" in prompt.lower():
                return "0.7"
            else:
                return "Generated response"
    
    # Create a tree of thought exploration instance
    tot = TreeOfThoughtExploration(
        language_model=MockLanguageModel(),
        max_depth=5,
        branching_factor=2
    )
    
    # Explore the problem
    tree, best_path = tot.explore(problem, max_iterations=10)
    
    # Print the best path
    print("Best path:")
    for i, step in enumerate(best_path, 1):
        print(f"{i}. {step}")
    
    # Calculate confidence in the path
    confidence = tot.calculate_path_confidence(best_path)
    print(f"\nPath confidence: {confidence:.2f}")
    
    # Find alternative paths
    alternative_paths = tot.find_alternative_paths(tree, n=2)
    if alternative_paths:
        print("\nAlternative path:")
        for i, step in enumerate(alternative_paths[0], 1):
            print(f"{i}. {step}")
    
    # Get tree statistics
    stats = tot.get_tree_statistics(tree)
    print("\nTree statistics:")
    for key, value in stats.items():
        print(f"{key}: {value}")