#!/usr/bin/env python3
"""
ULTRA Meta-Cognitive System: Multi-Path Chain of Thought

This module implements the Multi-Path Chain of Thought (MCoT) component of the ULTRA Meta-Cognitive System.
MCoT extends traditional chain-of-thought reasoning by exploring multiple reasoning pathways simultaneously,
with dynamic resource allocation based on the promise of each path. This enables more robust reasoning,
recovery from errors, and exploration of diverse approaches to complex problems.

The implementation integrates with both language models for reasoning generation and the ThoughtLatentSpace
for embedding and navigating reasoning paths in a continuous conceptual space.
"""

import os
import time
import math
import json
import logging
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Any, Optional, Union, Set, Callable, Iterator
from dataclasses import dataclass, field
from enum import Enum, auto
from collections import defaultdict, deque
import heapq
import networkx as nx
from abc import ABC, abstractmethod
import re

# Import ULTRA system components
from ultra.config import get_config, ULTRAConfigManager
from ultra.utils.ultra_logging import get_logger
from ultra.diffusion_reasoning.thought_latent_space import ThoughtLatentSpace
from ultra.neuro_symbolic.neuro_symbolic_bridge import NeuroSymbolicBridge
from ultra.knowledge_management.episodic_knowledge import EpisodicKnowledgeBase

# Configure logger
logger = get_logger(__name__)

class ReasoningStepType(Enum):
    """Types of reasoning steps in a chain of thought."""
    OBSERVATION = auto()       # Observing facts or data
    HYPOTHESIS = auto()        # Forming a hypothesis
    INFERENCE = auto()         # Making an inference from premises
    CALCULATION = auto()       # Performing a calculation
    COMPARISON = auto()        # Comparing multiple items/options
    CONSTRAINT = auto()        # Identifying a constraint
    DECOMPOSITION = auto()     # Breaking down the problem
    ANALOGY = auto()           # Using an analogy
    EXAMPLE = auto()           # Providing an example
    DEFINITION = auto()        # Defining a concept
    COUNTERARGUMENT = auto()   # Presenting a counterargument
    CONCLUSION = auto()        # Reaching a conclusion

@dataclass
class ReasoningStep:
    """Represents a single step in a chain of thought reasoning process."""
    content: str
    step_type: ReasoningStepType
    confidence: float = 1.0
    # Unique identifier for the step
    step_id: str = field(default_factory=lambda: f"step_{time.time()}_{id(object())}")
    # Dependencies on previous steps (list of step_ids)
    dependencies: List[str] = field(default_factory=list)
    # Metadata related to this step
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "content": self.content,
            "step_type": self.step_type.name,
            "confidence": self.confidence,
            "step_id": self.step_id,
            "dependencies": self.dependencies,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ReasoningStep':
        """Create a ReasoningStep from a dictionary."""
        return cls(
            content=data["content"],
            step_type=ReasoningStepType[data["step_type"]],
            confidence=data["confidence"],
            step_id=data["step_id"],
            dependencies=data["dependencies"],
            metadata=data["metadata"]
        )

@dataclass
class ReasoningPath:
    """Represents a chain of thought reasoning path with multiple steps."""
    steps: List[ReasoningStep]
    score: float = 0.0
    confidence: float = 0.0
    # Graph representation of the reasoning path
    graph: Optional[nx.DiGraph] = None
    # Metadata about the reasoning path
    metadata: Dict[str, Any] = field(default_factory=dict)
    # Vector representation in thought latent space (if available)
    embedding: Optional[np.ndarray] = None
    
    def __post_init__(self):
        """Initialize the graph if not provided."""
        if self.graph is None:
            self.graph = self._build_graph()
            
    def _build_graph(self) -> nx.DiGraph:
        """Build a directed graph representation of the reasoning path."""
        G = nx.DiGraph()
        
        # Add nodes for each step
        for step in self.steps:
            G.add_node(step.step_id, 
                       content=step.content, 
                       step_type=step.step_type.name,
                       confidence=step.confidence)
        
        # Add edges based on dependencies
        for step in self.steps:
            for dep_id in step.dependencies:
                if G.has_node(dep_id):
                    G.add_edge(dep_id, step.step_id)
                    
        # If no explicit dependencies, assume sequential dependencies
        if len(G.edges()) == 0 and len(self.steps) > 1:
            for i in range(1, len(self.steps)):
                G.add_edge(self.steps[i-1].step_id, self.steps[i].step_id)
                
        return G
    
    def add_step(self, step: ReasoningStep) -> None:
        """Add a new step to the reasoning path."""
        self.steps.append(step)
        
        # Update the graph
        if self.graph is not None:
            self.graph.add_node(step.step_id, 
                                content=step.content, 
                                step_type=step.step_type.name,
                                confidence=step.confidence)
            
            # Add edges based on dependencies
            for dep_id in step.dependencies:
                if self.graph.has_node(dep_id):
                    self.graph.add_edge(dep_id, step.step_id)
                    
            # If no explicit dependencies, assume dependency on the previous step
            if not step.dependencies and len(self.steps) > 1:
                self.graph.add_edge(self.steps[-2].step_id, step.step_id)
    
    def get_text_representation(self) -> str:
        """Get a text representation of the reasoning path."""
        return "\n".join([f"{i+1}. {step.content}" for i, step in enumerate(self.steps)])
    
    def get_terminal_steps(self) -> List[ReasoningStep]:
        """Get the terminal steps in the reasoning path (steps with no dependents)."""
        if self.graph is None:
            return [self.steps[-1]] if self.steps else []
            
        terminal_nodes = [node for node, out_degree in self.graph.out_degree() if out_degree == 0]
        return [step for step in self.steps if step.step_id in terminal_nodes]
    
    def get_conclusion(self) -> Optional[str]:
        """Extract the conclusion from the reasoning path."""
        # First check for conclusion step type
        conclusion_steps = [step for step in self.steps if step.step_type == ReasoningStepType.CONCLUSION]
        if conclusion_steps:
            return conclusion_steps[-1].content
            
        # Check terminal steps
        terminal_steps = self.get_terminal_steps()
        if terminal_steps:
            return terminal_steps[-1].content
            
        # Fallback to last step
        return self.steps[-1].content if self.steps else None
    
    def calculate_coherence(self) -> float:
        """Calculate the logical coherence of the reasoning path."""
        if len(self.steps) <= 1:
            return 1.0  # Single step is coherent by definition
            
        # Check if graph is connected
        if not nx.is_weakly_connected(self.graph):
            return 0.5  # Disconnected reasoning should be penalized
            
        # Calculate coherence based on step dependencies and confidence
        coherence_scores = []
        
        for i, step in enumerate(self.steps[1:], 1):
            # Get direct dependencies
            deps = step.dependencies
            
            # If no explicit dependencies, use previous step
            if not deps and i > 0:
                deps = [self.steps[i-1].step_id]
                
            # Calculate coherence as average confidence of dependencies
            dep_confidence = 0.0
            for dep_id in deps:
                for prev_step in self.steps[:i]:
                    if prev_step.step_id == dep_id:
                        dep_confidence += prev_step.confidence
                        break
                        
            avg_dep_confidence = dep_confidence / max(1, len(deps))
            step_coherence = avg_dep_confidence * step.confidence
            coherence_scores.append(step_coherence)
            
        return sum(coherence_scores) / max(1, len(coherence_scores))
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "steps": [step.to_dict() for step in self.steps],
            "score": self.score,
            "confidence": self.confidence,
            "metadata": self.metadata,
            "embedding": self.embedding.tolist() if self.embedding is not None else None
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ReasoningPath':
        """Create a ReasoningPath from a dictionary."""
        steps = [ReasoningStep.from_dict(step_data) for step_data in data["steps"]]
        embedding = np.array(data["embedding"]) if data["embedding"] is not None else None
        
        return cls(
            steps=steps,
            score=data["score"],
            confidence=data["confidence"],
            metadata=data["metadata"],
            embedding=embedding
        )
    
    def branch(self, new_step: ReasoningStep) -> 'ReasoningPath':
        """Create a new branched reasoning path with an additional step."""
        # Create a copy of the current steps
        new_steps = self.steps.copy()
        
        # Add the new step
        new_steps.append(new_step)
        
        # Create a new reasoning path with the extended steps
        return ReasoningPath(
            steps=new_steps,
            score=self.score,  # Initial score copied from parent
            confidence=self.confidence,  # Initial confidence copied from parent
            metadata=self.metadata.copy()
        )

class PathEvaluator(ABC):
    """Abstract base class for reasoning path evaluators."""
    
    @abstractmethod
    def evaluate(self, path: ReasoningPath, problem_statement: str) -> float:
        """
        Evaluate a reasoning path and return a score.
        
        Args:
            path: The reasoning path to evaluate
            problem_statement: The original problem statement
            
        Returns:
            A score representing the quality of the reasoning path (0-1)
        """
        pass

class CompositePathEvaluator(PathEvaluator):
    """
    Evaluates reasoning paths using a weighted combination of multiple criteria.
    
    This evaluator assesses validity (logical coherence), progress (towards solving the problem),
    and diversity (exploring unique approaches) of reasoning paths.
    """
    
    def __init__(
        self, 
        language_model=None,
        thought_latent_space: Optional[ThoughtLatentSpace] = None,
        weights: Optional[Dict[str, float]] = None
    ):
        """
        Initialize the composite path evaluator.
        
        Args:
            language_model: The language model for text-based evaluation
            thought_latent_space: The thought latent space for embedding-based evaluation
            weights: Weights for different evaluation criteria
        """
        self.language_model = language_model
        self.thought_latent_space = thought_latent_space
        
        # Default weights
        self.weights = weights or {
            "validity": 0.4,
            "progress": 0.4,
            "diversity": 0.2
        }
        
        # Normalize weights
        weight_sum = sum(self.weights.values())
        for k in self.weights:
            self.weights[k] /= weight_sum
    
    def evaluate(self, path: ReasoningPath, problem_statement: str) -> float:
        """
        Evaluate a reasoning path using multiple criteria.
        
        Args:
            path: The reasoning path to evaluate
            problem_statement: The original problem statement
            
        Returns:
            A weighted score representing the quality of the reasoning path (0-1)
        """
        # Ensure the path has steps
        if not path.steps:
            return 0.0
            
        # Calculate validity score (logical coherence)
        validity_score = self._evaluate_validity(path)
        
        # Calculate progress score (moving towards a solution)
        progress_score = self._evaluate_progress(path, problem_statement)
        
        # Calculate diversity score (exploring unique approaches)
        diversity_score = self._evaluate_diversity(path)
        
        # Calculate weighted composite score
        composite_score = (
            self.weights["validity"] * validity_score +
            self.weights["progress"] * progress_score +
            self.weights["diversity"] * diversity_score
        )
        
        # Update path metadata with component scores
        path.metadata["component_scores"] = {
            "validity": validity_score,
            "progress": progress_score,
            "diversity": diversity_score
        }
        
        return composite_score
    
    def _evaluate_validity(self, path: ReasoningPath) -> float:
        """
        Evaluate the logical validity of a reasoning path.
        
        Args:
            path: The reasoning path to evaluate
            
        Returns:
            A score representing the logical validity (0-1)
        """
        # Calculate coherence using path's internal method
        coherence = path.calculate_coherence()
        
        # Check graph structure for logical flow
        if path.graph is not None:
            # Penalize for cycles (circular reasoning)
            try:
                cycles = list(nx.simple_cycles(path.graph))
                cycle_penalty = 0.2 * min(1.0, len(cycles))
            except nx.NetworkXNoCycle:
                cycle_penalty = 0.0
                
            coherence = max(0.0, coherence - cycle_penalty)
            
            # Reward for proper structure (directed acyclic graph)
            if nx.is_directed_acyclic_graph(path.graph):
                coherence = min(1.0, coherence + 0.1)
        
        # If language model is available, use it to assess logical validity
        if self.language_model and len(path.steps) > 1:
            try:
                path_text = path.get_text_representation()
                
                prompt = f"""
                Evaluate the logical validity of the following reasoning:
                
                {path_text}
                
                On a scale from 0 to 1, where 0 is completely invalid and 1 is perfectly valid,
                rate the logical validity of this reasoning. Consider logical connections between
                steps, absence of fallacies, and soundness of inferences.
                
                Logical validity score (0-1):
                """
                
                response = self.language_model.generate(prompt).strip()
                
                # Extract score from response
                match = re.search(r'([0-9]*[.])?[0-9]+', response)
                if match:
                    lm_validity = float(match.group(0))
                    lm_validity = max(0.0, min(1.0, lm_validity))  # Clamp to [0, 1]
                    
                    # Combine with structural coherence
                    return 0.4 * coherence + 0.6 * lm_validity
            except Exception as e:
                logger.warning(f"Error in LM validity evaluation: {e}")
        
        return coherence
    
    def _evaluate_progress(self, path: ReasoningPath, problem_statement: str) -> float:
        """
        Evaluate progress towards solving the problem.
        
        Args:
            path: The reasoning path to evaluate
            problem_statement: The original problem statement
            
        Returns:
            A score representing the progress (0-1)
        """
        # If thought latent space is available, use it to measure progress
        if self.thought_latent_space is not None:
            try:
                # Embed problem statement and current conclusion
                problem_embedding = self.thought_latent_space.encode_concept(problem_statement)
                
                conclusion = path.get_conclusion()
                if conclusion:
                    conclusion_embedding = self.thought_latent_space.encode_concept(conclusion)
                    
                    # Calculate directional similarity
                    # Higher similarity means progress towards solving the problem
                    similarity = self.thought_latent_space.directional_similarity(
                        problem_embedding, conclusion_embedding
                    )
                    
                    # Normalize to [0, 1]
                    progress_score = max(0.0, min(1.0, (similarity + 1) / 2))
                    return progress_score
            except Exception as e:
                logger.warning(f"Error in thought space progress evaluation: {e}")
        
        # Fallback to language model for progress evaluation
        if self.language_model:
            try:
                path_text = path.get_text_representation()
                
                prompt = f"""
                Problem statement: {problem_statement}
                
                Reasoning path:
                {path_text}
                
                On a scale from 0 to 1, how much progress does this reasoning make towards solving the problem?
                Consider relevance to the problem, depth of analysis, and proximity to a solution.
                
                Progress score (0-1):
                """
                
                response = self.language_model.generate(prompt).strip()
                
                # Extract score from response
                match = re.search(r'([0-9]*[.])?[0-9]+', response)
                if match:
                    progress_score = float(match.group(0))
                    return max(0.0, min(1.0, progress_score))  # Clamp to [0, 1]
            except Exception as e:
                logger.warning(f"Error in LM progress evaluation: {e}")
        
        # Fallback to heuristic evaluation
        # Count conclusion steps as progress indicators
        conclusion_steps = sum(1 for step in path.steps if step.step_type == ReasoningStepType.CONCLUSION)
        conclusion_progress = min(1.0, conclusion_steps / 2)  # Max out at 2 conclusion steps
        
        # Length-based heuristic (some progress for longer paths, but with diminishing returns)
        length_factor = math.log(1 + len(path.steps)) / math.log(11)  # log base 10 of (1 + steps)
        length_progress = min(1.0, length_factor)
        
        # Average confidence as progress indicator
        avg_confidence = sum(step.confidence for step in path.steps) / max(1, len(path.steps))
        
        # Combine heuristics
        return 0.3 * conclusion_progress + 0.3 * length_progress + 0.4 * avg_confidence
    
    def _evaluate_diversity(self, path: ReasoningPath) -> float:
        """
        Evaluate the diversity and originality of a reasoning path.
        
        Args:
            path: The reasoning path to evaluate
            
        Returns:
            A score representing the diversity (0-1)
        """
        # If no path embedding, default diversity score
        if path.embedding is None:
            return 0.5
            
        # If thought latent space is available, use it to measure diversity
        if self.thought_latent_space is not None:
            try:
                # Get the stored paths in the thought space
                stored_paths = self.thought_latent_space.get_stored_paths()
                
                if not stored_paths:
                    return 1.0  # First path is maximally diverse
                    
                # Compute diversity as average distance to other paths
                distances = []
                for stored_path in stored_paths:
                    if stored_path['embedding'] is not None:
                        distance = self.thought_latent_space.compute_distance(
                            path.embedding, stored_path['embedding']
                        )
                        distances.append(distance)
                        
                if not distances:
                    return 1.0
                    
                # Normalize diversity (higher distances = more diverse)
                avg_distance = sum(distances) / len(distances)
                
                # Scale to [0, 1] range (assuming typical distances in the range [0, 2])
                diversity_score = min(1.0, avg_distance / 2)
                return diversity_score
            except Exception as e:
                logger.warning(f"Error in thought space diversity evaluation: {e}")
        
        # Fallback to heuristic evaluation
        # Higher diversity for less common step types
        step_types = [step.step_type for step in path.steps]
        # Rarer step types get higher scores
        rarity_scores = {
            ReasoningStepType.OBSERVATION: 0.3,
            ReasoningStepType.HYPOTHESIS: 0.4,
            ReasoningStepType.INFERENCE: 0.3,
            ReasoningStepType.CALCULATION: 0.5,
            ReasoningStepType.COMPARISON: 0.6,
            ReasoningStepType.CONSTRAINT: 0.7,
            ReasoningStepType.DECOMPOSITION: 0.7,
            ReasoningStepType.ANALOGY: 0.8,
            ReasoningStepType.EXAMPLE: 0.5,
            ReasoningStepType.DEFINITION: 0.4,
            ReasoningStepType.COUNTERARGUMENT: 0.8,
            ReasoningStepType.CONCLUSION: 0.3
        }
        
        avg_rarity = sum(rarity_scores.get(step_type, 0.5) for step_type in step_types) / max(1, len(step_types))
        
        # Balance of different step types indicates diversity
        unique_types = len(set(step_types))
        type_diversity = min(1.0, unique_types / 6)  # Max out at 6 different types
        
        # Combine heuristics
        return 0.6 * avg_rarity + 0.4 * type_diversity

class ResourceAllocator:
    """
    Allocates computational resources to reasoning paths based on their promise.
    
    Implements a temperature-based softmax allocation strategy to balance
    exploitation of promising paths with exploration of diverse paths.
    """
    
    def __init__(self, temperature: float = 0.1):
        """
        Initialize the resource allocator.
        
        Args:
            temperature: Temperature parameter for softmax allocation (lower = more exploitation)
        """
        self.temperature = temperature
    
    def allocate(self, paths: List[Dict[str, Any]], total_budget: float) -> Dict[str, float]:
        """
        Allocate resources to paths based on their scores.
        
        Args:
            paths: List of path dictionaries with path_id and score
            total_budget: Total resources to allocate
            
        Returns:
            Dictionary mapping path_id to allocated resources
        """
        if not paths:
            return {}
            
        # Extract scores
        path_ids = [p["path_id"] for p in paths]
        scores = np.array([p["score"] for p in paths])
        
        # Apply softmax with temperature
        exp_scores = np.exp(scores / self.temperature)
        weights = exp_scores / np.sum(exp_scores)
        
        # Allocate budget proportionally
        allocations = weights * total_budget
        
        return dict(zip(path_ids, allocations))
    
    def adjust_temperature(self, performance_metrics: Dict[str, float]):
        """
        Adjust temperature based on performance metrics.
        
        Args:
            performance_metrics: Dictionary of performance metrics
        """
        # Adjust temperature based on diversity and progress
        diversity = performance_metrics.get("diversity", 0.5)
        progress = performance_metrics.get("progress", 0.5)
        
        # If diversity is low, increase temperature to encourage exploration
        if diversity < 0.3:
            self.temperature = min(1.0, self.temperature * 1.2)
        
        # If progress is high, decrease temperature to focus on promising paths
        if progress > 0.7:
            self.temperature = max(0.01, self.temperature * 0.8)

class PathStepGenerator(ABC):
    """Abstract base class for step generators in chain of thought reasoning."""
    
    @abstractmethod
    def generate_steps(
        self, 
        path: ReasoningPath, 
        problem_statement: str,
        num_steps: int = 3
    ) -> List[Tuple[ReasoningStep, float]]:
        """
        Generate possible next steps for a reasoning path.
        
        Args:
            path: The current reasoning path
            problem_statement: The original problem statement
            num_steps: Number of steps to generate
            
        Returns:
            List of (step, score) tuples
        """
        pass

class LMPathStepGenerator(PathStepGenerator):
    """
    Generates reasoning steps using a language model.
    
    Implements beam search to generate diverse, high-quality next steps for a reasoning path.
    """
    
    def __init__(
        self, 
        language_model,
        thought_latent_space: Optional[ThoughtLatentSpace] = None,
        neuro_symbolic_bridge: Optional[NeuroSymbolicBridge] = None
    ):
        """
        Initialize the language model step generator.
        
        Args:
            language_model: The language model for step generation
            thought_latent_space: Optional thought latent space for embedding-based generation
            neuro_symbolic_bridge: Optional neuro-symbolic bridge for symbolic reasoning
        """
        self.language_model = language_model
        self.thought_latent_space = thought_latent_space
        self.neuro_symbolic_bridge = neuro_symbolic_bridge
        
        # Regular expressions for identifying step types
        self.step_type_patterns = {
            ReasoningStepType.OBSERVATION: [
                r"(?:I|we) observe that", 
                r"looking at .* we see", 
                r"the (data|information|text) shows"
            ],
            ReasoningStepType.HYPOTHESIS: [
                r"(?:I|we) hypothesize", 
                r"(?:let's|we could) assume", 
                r"(?:one|a) possibility is"
            ],
            ReasoningStepType.INFERENCE: [
                r"(?:therefore|thus|hence|so)", 
                r"(?:this|it) (?:implies|means|suggests)",
                r"we can (?:infer|deduce|conclude)"
            ],
            ReasoningStepType.CALCULATION: [
                r"calculating", 
                r"(?:if we|let's) compute", 
                r"the (?:result|answer|solution) is"
            ],
            ReasoningStepType.COMPARISON: [
                r"comparing", 
                r"(?:in|by) contrast", 
                r"(?:the|these) (?:differ|differ in|are different)"
            ],
            ReasoningStepType.CONSTRAINT: [
                r"(?:we are|is) constrained by", 
                r"(?:a|the) (?:limitation|constraint|restriction) is",
                r"we (?:must|need to|have to)"
            ],
            ReasoningStepType.DECOMPOSITION: [
                r"(?:let's|we can) break (?:this|it) down", 
                r"(?:dividing|splitting) (?:this|the problem)",
                r"(?:step|part) (?:one|1|two|2|three|3)"
            ],
            ReasoningStepType.ANALOGY: [
                r"(?:this is|it's) (?:like|similar to)", 
                r"(?:by|through) analogy",
                r"(?:we can|let's) compare this to"
            ],
            ReasoningStepType.EXAMPLE: [
                r"(?:for|as an) example", 
                r"(?:to|let's) illustrate",
                r"(?:instance|case in point)"
            ],
            ReasoningStepType.DEFINITION: [
                r"(?:is|are) defined as", 
                r"(?:the|a) definition of",
                r"(?:means|refers to)"
            ],
            ReasoningStepType.COUNTERARGUMENT: [
                r"(?:however|but|yet|on the other hand)", 
                r"(?:a|one) counterargument",
                r"(?:one|we) might object"
            ],
            ReasoningStepType.CONCLUSION: [
                r"(?:in conclusion|to conclude|finally|ultimately)", 
                r"(?:the|our|my) (?:conclusion|answer|solution) is",
                r"(?:so|thus|therefore|hence), (?:the|we|I)"
            ]
        }
    
    def generate_steps(
        self, 
        path: ReasoningPath, 
        problem_statement: str,
        num_steps: int = 3
    ) -> List[Tuple[ReasoningStep, float]]:
        """
        Generate possible next steps for a reasoning path using the language model.
        
        Args:
            path: The current reasoning path
            problem_statement: The original problem statement
            num_steps: Number of steps to generate
            
        Returns:
            List of (step, score) tuples
        """
        if not self.language_model:
            logger.warning("No language model available for step generation")
            return []
            
        # Get the current reasoning path as text
        path_text = path.get_text_representation()
        
        # Create prompt for next step generation
        prompt = f"""
        Problem: {problem_statement}
        
        Current reasoning:
        {path_text}
        
        Generate {num_steps} possible next steps in this reasoning process. 
        Each step should be logically connected to the previous reasoning and help make progress toward a solution.
        Make the steps diverse in their approach, considering different angles or methods.
        
        Next steps:
        """
        
        try:
            # Generate multiple candidate next steps
            response = self.language_model.generate(prompt)
            
            # Parse the steps from the response
            step_texts = self._parse_steps(response)
            
            # Process each step
            results = []
            for i, step_text in enumerate(step_texts[:num_steps]):
                # Identify step type
                step_type = self._identify_step_type(step_text)
                
                # Create reasoning step
                step = ReasoningStep(
                    content=step_text,
                    step_type=step_type,
                    confidence=0.8,  # Initial confidence
                    dependencies=[path.steps[-1].step_id] if path.steps else []
                )
                
                # Calculate step score
                score = self._score_step(step, path, problem_statement)
                
                results.append((step, score))
                
            # Sort by score
            results.sort(key=lambda x: x[1], reverse=True)
            
            return results
            
        except Exception as e:
            logger.error(f"Error generating steps: {e}")
            return []
    
    def _parse_steps(self, response: str) -> List[str]:
        """
        Parse individual steps from the language model response.
        
        Args:
            response: Raw language model response
            
        Returns:
            List of step texts
        """
        # Split by numbered lines (e.g., "1.", "2.", etc.)
        numbered_pattern = r'(?:\d+[\)\.]\s*)(.*?)(?=\d+[\)\.]\s*|$)'
        matches = re.findall(numbered_pattern, response, re.DOTALL)
        
        if matches:
            steps = [match.strip() for match in matches]
            return steps
            
        # Try splitting by newlines if numbered pattern failed
        lines = [line.strip() for line in response.strip().split('\n') if line.strip()]
        return lines
    
    def _identify_step_type(self, step_text: str) -> ReasoningStepType:
        """
        Identify the type of reasoning step based on its content.
        
        Args:
            step_text: The text content of the step
            
        Returns:
            Identified step type
        """
        step_text_lower = step_text.lower()
        
        # Check each pattern for each step type
        for step_type, patterns in self.step_type_patterns.items():
            for pattern in patterns:
                if re.search(pattern, step_text_lower):
                    return step_type
        
        # Default to inference if no clear pattern match
        return ReasoningStepType.INFERENCE
    
    def _score_step(self, step: ReasoningStep, path: ReasoningPath, problem_statement: str) -> float:
        """
        Calculate a preliminary score for a generated step.
        
        Args:
            step: The generated reasoning step
            path: The current reasoning path
            problem_statement: The original problem statement
            
        Returns:
            A score for the step (0-1)
        """
        # Higher scores for conclusions near the end of reasoning
        if step.step_type == ReasoningStepType.CONCLUSION and len(path.steps) >= 3:
            return 0.9
        
        # Higher scores for counterarguments after several steps
        if step.step_type == ReasoningStepType.COUNTERARGUMENT and len(path.steps) >= 2:
            return 0.8
        
        # Higher scores for observations near the beginning
        if step.step_type == ReasoningStepType.OBSERVATION and len(path.steps) <= 1:
            return 0.8
        
        # Higher scores for decomposition near the beginning
        if step.step_type == ReasoningStepType.DECOMPOSITION and len(path.steps) <= 2:
            return 0.8
        
        # Score step based on content length, complexity, etc.
        content_length = len(step.content.split())
        length_factor = min(1.0, content_length / 30)  # Prefer reasonably detailed steps
        
        # Steps that introduce new concepts get higher scores
        new_concept_score = 0.0
        if len(path.steps) > 0:
            # Check for unique words not in previous steps
            step_words = set(step.content.lower().split())
            path_words = set(' '.join([s.content.lower() for s in path.steps]).split())
            new_words = step_words - path_words
            new_concept_score = min(1.0, len(new_words) / 5)  # 5+ new words gives full score
            
        # Combine factors
        return 0.6 * step.confidence + 0.2 * length_factor + 0.2 * new_concept_score

class BeamSearchGenerator(PathStepGenerator):
    """
    Generates reasoning steps using beam search over multiple approaches.
    
    Combines language model generation with neuro-symbolic reasoning to produce
    diverse, high-quality next steps.
    """
    
    def __init__(
        self, 
        language_model=None,
        thought_latent_space: Optional[ThoughtLatentSpace] = None,
        neuro_symbolic_bridge: Optional[NeuroSymbolicBridge] = None,
        knowledge_base: Optional[EpisodicKnowledgeBase] = None,
        beam_width: int = 3
    ):
        """
        Initialize the beam search generator.
        
        Args:
            language_model: The language model for step generation
            thought_latent_space: The thought latent space for embedding-based generation
            neuro_symbolic_bridge: The neuro-symbolic bridge for symbolic reasoning
            knowledge_base: The knowledge base for retrieving relevant information
            beam_width: Width of the beam search
        """
        self.language_model = language_model
        self.thought_latent_space = thought_latent_space
        self.neuro_symbolic_bridge = neuro_symbolic_bridge
        self.knowledge_base = knowledge_base
        self.beam_width = beam_width
        
        # Create component generators
        self.generators = []
        
        # Add language model generator if available
        if language_model:
            self.generators.append(
                LMPathStepGenerator(
                    language_model=language_model,
                    thought_latent_space=thought_latent_space,
                    neuro_symbolic_bridge=neuro_symbolic_bridge
                )
            )
            
        # Add additional generators based on available components
        if neuro_symbolic_bridge:
            self.generators.append(self._create_symbolic_generator())
            
        if knowledge_base:
            self.generators.append(self._create_knowledge_generator())
    
    def generate_steps(
        self, 
        path: ReasoningPath, 
        problem_statement: str,
        num_steps: int = 3
    ) -> List[Tuple[ReasoningStep, float]]:
        """
        Generate possible next steps for a reasoning path using beam search.
        
        Args:
            path: The current reasoning path
            problem_statement: The original problem statement
            num_steps: Number of steps to generate
            
        Returns:
            List of (step, score) tuples
        """
        all_candidates = []
        
        # Generate candidates from each component generator
        for generator in self.generators:
            candidates = generator.generate_steps(
                path=path,
                problem_statement=problem_statement,
                num_steps=max(1, num_steps // len(self.generators))
            )
            all_candidates.extend(candidates)
            
        # If no candidates, return empty list
        if not all_candidates:
            return []
            
        # Sort candidates by score
        all_candidates.sort(key=lambda x: x[1], reverse=True)
        
        # Select the best candidates while ensuring diversity
        selected_candidates = self._select_diverse_candidates(all_candidates, num_steps)
        
        return selected_candidates
    
    def _select_diverse_candidates(
        self, 
        candidates: List[Tuple[ReasoningStep, float]], 
        num_steps: int
    ) -> List[Tuple[ReasoningStep, float]]:
        """
        Select diverse candidates from a list of candidates.
        
        Args:
            candidates: List of (step, score) tuples
            num_steps: Number of steps to select
            
        Returns:
            List of selected (step, score) tuples
        """
        if len(candidates) <= num_steps:
            return candidates
            
        # Initialize with the highest-scoring candidate
        selected = [candidates[0]]
        remaining = candidates[1:]
        
        # Add candidates that maximize diversity + score
        while len(selected) < num_steps and remaining:
            # Find the candidate that maximizes a combination of score and diversity
            best_candidate = None
            best_score = -float('inf')
            
            for i, (step, score) in enumerate(remaining):
                # Calculate diversity as minimum distance to already selected steps
                diversity = min(
                    self._step_distance(step, selected_step)
                    for selected_step, _ in selected
                )
                
                # Combine score and diversity
                combined_score = score * 0.7 + diversity * 0.3
                
                if combined_score > best_score:
                    best_score = combined_score
                    best_candidate = i
                    
            # Add the best candidate
            selected.append(remaining[best_candidate])
            remaining.pop(best_candidate)
            
        return selected
    
    def _step_distance(self, step1: ReasoningStep, step2: ReasoningStep) -> float:
        """
        Calculate the semantic distance between two reasoning steps.
        
        Args:
            step1: First reasoning step
            step2: Second reasoning step
            
        Returns:
            Semantic distance between the steps (0-1)
        """
        # Different step types contribute to diversity
        if step1.step_type != step2.step_type:
            type_distance = 0.5
        else:
            type_distance = 0.0
            
        # Calculate content distance based on word overlap
        words1 = set(step1.content.lower().split())
        words2 = set(step2.content.lower().split())
        
        if not words1 or not words2:
            content_distance = 0.5
        else:
            overlap = len(words1.intersection(words2))
            content_distance = 1.0 - (overlap / min(len(words1), len(words2)))
            
        # Combine distances
        return 0.4 * type_distance + 0.6 * content_distance
    
    def _create_symbolic_generator(self) -> PathStepGenerator:
        """Create a step generator using the neuro-symbolic bridge."""
        # This would be implemented to use symbolic reasoning for step generation
        # For now, return a placeholder that acts as the LM generator
        return LMPathStepGenerator(
            language_model=self.language_model,
            thought_latent_space=self.thought_latent_space,
            neuro_symbolic_bridge=self.neuro_symbolic_bridge
        )
    
    def _create_knowledge_generator(self) -> PathStepGenerator:
        """Create a step generator using the knowledge base."""
        # This would be implemented to use knowledge base for step generation
        # For now, return a placeholder that acts as the LM generator
        return LMPathStepGenerator(
            language_model=self.language_model,
            thought_latent_space=self.thought_latent_space,
            neuro_symbolic_bridge=self.neuro_symbolic_bridge
        )

class MultiPathChainOfThought:
    """
    Main class implementing Multi-Path Chain of Thought reasoning.
    
    This class extends traditional chain-of-thought reasoning by exploring multiple
    reasoning pathways simultaneously, with dynamic resource allocation based on
    the promise of each path.
    """
    
    def __init__(
        self, 
        language_model=None,
        max_paths: int = 5,
        beam_width: int = 3,
        thought_latent_space: Optional[ThoughtLatentSpace] = None,
        neuro_symbolic_bridge: Optional[NeuroSymbolicBridge] = None,
        knowledge_base: Optional[EpisodicKnowledgeBase] = None
    ):
        """
        Initialize the Multi-Path Chain of Thought reasoning system.
        
        Args:
            language_model: Language model for reasoning
            max_paths: Maximum number of active reasoning paths to maintain
            beam_width: Width of the beam search for step generation
            thought_latent_space: Optional thought latent space for reasoning
            neuro_symbolic_bridge: Optional neuro-symbolic bridge for reasoning
            knowledge_base: Optional knowledge base for reasoning
        """
        self.language_model = language_model
        self.max_paths = max_paths
        self.beam_width = beam_width
        self.thought_latent_space = thought_latent_space
        self.neuro_symbolic_bridge = neuro_symbolic_bridge
        self.knowledge_base = knowledge_base
        
        # Initialize path evaluator
        self.path_evaluator = CompositePathEvaluator(
            language_model=language_model,
            thought_latent_space=thought_latent_space
        )
        
        # Initialize step generator
        self.step_generator = BeamSearchGenerator(
            language_model=language_model,
            thought_latent_space=thought_latent_space,
            neuro_symbolic_bridge=neuro_symbolic_bridge,
            knowledge_base=knowledge_base,
            beam_width=beam_width
        )
        
        # Initialize resource allocator
        self.resource_allocator = ResourceAllocator(temperature=0.1)
        
        # Register handlers for critique and bias detection
        self.critique_handler = None
        self.bias_handler = None
        
        # Tracking active paths
        self.active_paths = []
        self.completed_paths = []
        self.total_resource = 1.0
        
        # Tracking performance metrics
        self.performance_metrics = {
            "diversity": 0.5,
            "progress": 0.0,
            "validity": 0.5
        }
        
        # Logger
        self.logger = get_logger(__name__)
    
    def register_critique_handler(self, handler: Callable) -> None:
        """
        Register a handler for critiquing reasoning paths.
        
        Args:
            handler: Function that takes a reasoning path and returns a critique
        """
        self.critique_handler = handler
    
    def register_bias_handler(self, handler: Callable) -> None:
        """
        Register a handler for detecting biases in reasoning paths.
        
        Args:
            handler: Function that takes a reasoning path and returns detected biases
        """
        self.bias_handler = handler
    
    def generate_reasoning_paths(
        self, 
        problem_statement: str,
        context: Optional[Dict[str, Any]] = None,
        max_iterations: int = 10,
        max_paths: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Generate multiple reasoning paths for a problem statement.
        
        Args:
            problem_statement: The problem to reason about
            context: Optional context information
            max_iterations: Maximum number of reasoning iterations
            max_paths: Maximum number of paths to maintain (overrides instance setting)
            
        Returns:
            List of reasoning paths and their scores
        """
        # Reset state
        self.active_paths = []
        self.completed_paths = []
        
        # Use overridden max_paths if provided
        active_max_paths = max_paths or self.max_paths
        
        # Initialize with a single path
        self._initialize_paths(problem_statement, context)
        
        # Perform reasoning iterations
        for iteration in range(max_iterations):
            self.logger.info(f"Reasoning iteration {iteration+1}/{max_iterations} with {len(self.active_paths)} active paths")
            
            # Calculate resource allocation
            path_info = [{"path_id": str(i), "score": path.score} for i, path in enumerate(self.active_paths)]
            allocations = self.resource_allocator.allocate(path_info, self.total_resource)
            
            # Expand paths based on allocations
            new_active_paths = []
            
            for i, path in enumerate(self.active_paths):
                path_id = str(i)
                resource = allocations.get(path_id, 0.0)
                
                # Skip paths with negligible resources
                if resource < 0.01:
                    continue
                    
                # Convert resource to number of expansion steps
                num_expansions = max(1, int(resource * 10))
                expanded_paths = self._expand_path(path, problem_statement, context, num_expansions)
                
                # Add expanded paths to new active paths
                new_active_paths.extend(expanded_paths)
                
            # Update active paths
            self.active_paths = new_active_paths
            
            # Prune to maintain max_paths active paths
            if len(self.active_paths) > active_max_paths:
                # Sort by score
                self.active_paths.sort(key=lambda p: p.score, reverse=True)
                
                # Move excess paths to completed paths
                self.completed_paths.extend(self.active_paths[active_max_paths:])
                self.active_paths = self.active_paths[:active_max_paths]
                
            # Check for completed reasoning
            self._check_completion(problem_statement)
            
            # Update performance metrics
            self._update_performance_metrics()
            
            # Adjust resource allocator based on performance
            self.resource_allocator.adjust_temperature(self.performance_metrics)
            
            # Break if all paths are completed
            if not self.active_paths:
                break
                
        # Move any remaining active paths to completed
        self.completed_paths.extend(self.active_paths)
        self.active_paths = []
        
        # Sort completed paths by score
        self.completed_paths.sort(key=lambda p: p.score, reverse=True)
        
        # Convert paths to dictionaries for return
        return [
            {
                "text": path.get_text_representation(),
                "score": path.score,
                "confidence": path.confidence,
                "conclusion": path.get_conclusion(),
                "metadata": path.metadata
            }
            for path in self.completed_paths
        ]
    
    def explore_with_resource_allocation(
        self, 
        problem_statement: str,
        context: Optional[Dict[str, Any]] = None,
        max_iterations: int = 10
    ) -> Dict[str, Any]:
        """
        Explore reasoning paths with dynamic resource allocation.
        
        Args:
            problem_statement: The problem to reason about
            context: Optional context information
            max_iterations: Maximum number of reasoning iterations
            
        Returns:
            Dictionary with paths, resource allocation, and performance metrics
        """
        # Reset state
        self.active_paths = []
        self.completed_paths = []
        
        # Initialize with a single path
        self._initialize_paths(problem_statement, context)
        
        # Track resource allocation over iterations
        allocation_history = []
        
        # Perform reasoning iterations
        for iteration in range(max_iterations):
            self.logger.info(f"Resource allocation iteration {iteration+1}/{max_iterations} with {len(self.active_paths)} active paths")
            
            # Calculate resource allocation
            path_info = [{"path_id": str(i), "score": path.score} for i, path in enumerate(self.active_paths)]
            allocations = self.resource_allocator.allocate(path_info, self.total_resource)
            
            # Record allocation
            iteration_allocation = {
                "iteration": iteration + 1,
                "allocations": dict(allocations)
            }
            allocation_history.append(iteration_allocation)
            
            # Expand paths based on allocations
            new_active_paths = []
            
            for i, path in enumerate(self.active_paths):
                path_id = str(i)
                resource = allocations.get(path_id, 0.0)
                
                # Skip paths with negligible resources
                if resource < 0.01:
                    continue
                    
                # Convert resource to number of expansion steps
                num_expansions = max(1, int(resource * 10))
                expanded_paths = self._expand_path(path, problem_statement, context, num_expansions)
                
                # Add expanded paths to new active paths
                new_active_paths.extend(expanded_paths)
                
            # Update active paths
            self.active_paths = new_active_paths
            
            # Prune to maintain max_paths active paths
            if len(self.active_paths) > self.max_paths:
                # Sort by score
                self.active_paths.sort(key=lambda p: p.score, reverse=True)
                
                # Move excess paths to completed paths
                self.completed_paths.extend(self.active_paths[self.max_paths:])
                self.active_paths = self.active_paths[:self.max_paths]
                
            # Check for completed reasoning
            self._check_completion(problem_statement)
            
            # Update performance metrics
            self._update_performance_metrics()
            
            # Adjust resource allocator based on performance
            self.resource_allocator.adjust_temperature(self.performance_metrics)
            
            # Break if all paths are completed
            if not self.active_paths:
                break
                
        # Move any remaining active paths to completed
        self.completed_paths.extend(self.active_paths)
        self.active_paths = []
        
        # Sort completed paths by score
        self.completed_paths.sort(key=lambda p: p.score, reverse=True)
        
        # Calculate final scores for all paths
        for path in self.completed_paths:
            self._evaluate_path(path, problem_statement)
            
        # Convert paths to dictionaries for return
        path_results = [
            {
                "text": path.get_text_representation(),
                "initial_score": path.metadata.get("initial_score", 0.0),
                "final_score": path.score,
                "confidence": path.confidence,
                "conclusion": path.get_conclusion(),
                "metadata": path.metadata
            }
            for path in self.completed_paths
        ]
        
        return {
            "paths": path_results,
            "resource_allocation": allocation_history,
            "performance_metrics": self.performance_metrics
        }
    
    def _initialize_paths(self, problem_statement: str, context: Optional[Dict[str, Any]] = None) -> None:
        """
        Initialize reasoning paths based on the problem statement.
        
        Args:
            problem_statement: The problem to reason about
            context: Optional context information
        """
        # Create initial reasoning step based on problem statement
        initial_step = ReasoningStep(
            content=f"I need to solve the following problem: {problem_statement}",
            step_type=ReasoningStepType.OBSERVATION,
            confidence=1.0
        )
        
        # Create initial path
        initial_path = ReasoningPath(
            steps=[initial_step],
            score=0.5,  # Neutral initial score
            confidence=1.0,
            metadata={"context": context or {}}
        )
        
        # If we have a thought latent space, embed the problem
        if self.thought_latent_space:
            try:
                problem_embedding = self.thought_latent_space.encode_concept(problem_statement)
                initial_path.metadata["problem_embedding"] = problem_embedding
            except Exception as e:
                self.logger.warning(f"Error embedding problem: {e}")
        
        # Add to active paths
        self.active_paths = [initial_path]
        
        # Add more diverse initial paths if language model is available
        if self.language_model:
            try:
                prompt = f"""
                Problem: {problem_statement}
                
                Generate 2 different approaches to start solving this problem.
                Each approach should reflect a distinct perspective or methodology.
                Keep each approach to 1-2 sentences.
                
                Approaches:
                """
                
                response = self.language_model.generate(prompt)
                
                # Parse approaches
                approaches = []
                for line in response.strip().split('\n'):
                    if line.strip():
                        # Check if line starts with a number (1., 2., etc.)
                        if re.match(r'^\d+[\)\.]\s+', line):
                            # Remove the number and keep the text
                            approach = re.sub(r'^\d+[\)\.]\s+', '', line).strip()
                            approaches.append(approach)
                        else:
                            approaches.append(line.strip())
                
                # Create a path for each unique approach
                for i, approach in enumerate(approaches[:2]):  # Limit to 2 approaches
                    approach_step = ReasoningStep(
                        content=approach,
                        step_type=ReasoningStepType.HYPOTHESIS,
                        confidence=0.9,
                        dependencies=[initial_step.step_id]
                    )
                    
                    approach_path = ReasoningPath(
                        steps=[initial_step, approach_step],
                        score=0.5,  # Neutral initial score
                        confidence=0.9,
                        metadata={"context": context or {}}
                    )
                    
                    # Add to active paths
                    self.active_paths.append(approach_path)
                    
            except Exception as e:
                self.logger.warning(f"Error generating diverse initial paths: {e}")
        
        # Evaluate all paths
        for path in self.active_paths:
            self._evaluate_path(path, problem_statement)
    
    def _expand_path(
        self, 
        path: ReasoningPath, 
        problem_statement: str, 
        context: Optional[Dict[str, Any]] = None,
        num_expansions: int = 1
    ) -> List[ReasoningPath]:
        """
        Expand a reasoning path with new steps.
        
        Args:
            path: The reasoning path to expand
            problem_statement: The original problem statement
            context: Optional context information
            num_expansions: Number of expansions to perform
            
        Returns:
            List of expanded paths
        """
        # Generate candidate next steps
        candidates = self.step_generator.generate_steps(
            path=path,
            problem_statement=problem_statement,
            num_steps=min(self.beam_width, num_expansions * 2)  # Generate more candidates than needed
        )
        
        # If no candidates, return original path
        if not candidates:
            return [path]
            
        # Create expanded paths
        expanded_paths = []
        
        for i, (step, score) in enumerate(candidates[:num_expansions]):
            # Create a new path with the additional step
            new_path = path.branch(step)
            
            # Copy metadata from original path
            new_path.metadata.update(path.metadata)
            
            # Evaluate the new path
            self._evaluate_path(new_path, problem_statement)
            
            # Apply critique if handler is registered
            if self.critique_handler:
                critique = self.critique_handler(new_path.get_text_representation())
                if critique:
                    new_path.metadata["critique"] = critique
                    
            # Apply bias detection if handler is registered
            if self.bias_handler:
                biases = self.bias_handler(new_path.get_text_representation())
                if biases:
                    new_path.metadata["detected_biases"] = biases
                    
            # Add to expanded paths
            expanded_paths.append(new_path)
            
        # If thought latent space is available, embed the new paths
        if self.thought_latent_space:
            for new_path in expanded_paths:
                try:
                    path_text = new_path.get_text_representation()
                    embedding = self.thought_latent_space.encode_concept(path_text)
                    new_path.embedding = embedding
                except Exception as e:
                    self.logger.warning(f"Error embedding path: {e}")
        
        return expanded_paths
    
    def _evaluate_path(self, path: ReasoningPath, problem_statement: str) -> None:
        """
        Evaluate a reasoning path and update its score and confidence.
        
        Args:
            path: The reasoning path to evaluate
            problem_statement: The original problem statement
        """
        # Calculate score using path evaluator
        score = self.path_evaluator.evaluate(path, problem_statement)
        
        # Store original score as metadata
        if "initial_score" not in path.metadata:
            path.metadata["initial_score"] = path.score
            
        # Store component scores from evaluator
        if "component_scores" in path.metadata:
            self.performance_metrics.update(path.metadata["component_scores"])
            
        # Update path score
        path.score = score
        
        # Calculate confidence based on path coherence and score
        coherence = path.calculate_coherence()
        path.confidence = (coherence + score) / 2
    
    def _check_completion(self, problem_statement: str) -> None:
        """
        Check if any active paths have reached a conclusion and should be completed.
        
        Args:
            problem_statement: The original problem statement
        """
        # Paths to remove from active paths
        completed_indices = []
        
        for i, path in enumerate(self.active_paths):
            # Check if path has a conclusion step
            has_conclusion = any(step.step_type == ReasoningStepType.CONCLUSION for step in path.steps)
            
            # Check for minimum steps to avoid premature completion
            has_minimum_steps = len(path.steps) >= 3
            
            # Check if score is high enough for completion
            has_high_score = path.score >= 0.8
            
            # Check if path forms a complete argument
            if has_conclusion and has_minimum_steps and has_high_score:
                completed_indices.append(i)
                
        # Move completed paths from active to completed
        for i in sorted(completed_indices, reverse=True):
            self.completed_paths.append(self.active_paths[i])
            self.active_paths.pop(i)
    
    def _update_performance_metrics(self) -> None:
        """Update performance metrics based on the current state of reasoning."""
        # Calculate path diversity
        if len(self.active_paths) > 1 and self.thought_latent_space is not None:
            try:
                # Calculate average pairwise distance between path embeddings
                distances = []
                path_embeddings = [p.embedding for p in self.active_paths if p.embedding is not None]
                
                if len(path_embeddings) > 1:
                    for i in range(len(path_embeddings)):
                        for j in range(i+1, len(path_embeddings)):
                            dist = self.thought_latent_space.compute_distance(
                                path_embeddings[i], path_embeddings[j]
                            )
                            distances.append(dist)
                    
                    # Update diversity metric (normalized to [0, 1])
                    avg_distance = sum(distances) / len(distances)
                    self.performance_metrics["diversity"] = min(1.0, avg_distance / 2)
            except Exception as e:
                self.logger.warning(f"Error calculating path diversity: {e}")
        
        # Calculate overall progress
        all_paths = self.active_paths + self.completed_paths
        if all_paths:
            # Weighted average of path scores, giving more weight to completed paths
            active_scores = [path.score for path in self.active_paths]
            completed_scores = [path.score for path in self.completed_paths]
            
            if active_scores and completed_scores:
                progress = (sum(active_scores) + 2 * sum(completed_scores)) / (len(active_scores) + 2 * len(completed_scores))
            elif active_scores:
                progress = sum(active_scores) / len(active_scores)
            elif completed_scores:
                progress = sum(completed_scores) / len(completed_scores)
            else:
                progress = 0.0
                
            self.performance_metrics["progress"] = progress
            
        # Calculate validity from component scores
        validity_scores = []
        for path in all_paths:
            if "component_scores" in path.metadata and "validity" in path.metadata["component_scores"]:
                validity_scores.append(path.metadata["component_scores"]["validity"])
                
        if validity_scores:
            self.performance_metrics["validity"] = sum(validity_scores) / len(validity_scores)
    
    def reset(self) -> None:
        """Reset the reasoning state."""
        self.active_paths = []
        self.completed_paths = []
        self.performance_metrics = {
            "diversity": 0.5,
            "progress": 0.0,
            "validity": 0.5
        }
    
    def visualize_reasoning_paths(self, output_path: Optional[str] = None) -> Optional[nx.DiGraph]:
        """
        Visualize the reasoning paths as a graph.
        
        Args:
            output_path: Optional path to save the visualization
            
        Returns:
            NetworkX graph of the reasoning paths
        """
        try:
            import matplotlib.pyplot as plt
            
            # Create a graph to hold all paths
            G = nx.DiGraph()
            
            # Add paths to the graph
            all_paths = self.active_paths + self.completed_paths
            
            for i, path in enumerate(all_paths):
                # Get the path's graph
                path_graph = path.graph
                
                # Add path index to node attributes
                for node in path_graph.nodes():
                    path_graph.nodes[node]['path_idx'] = i
                    
                    # Add score to node attributes
                    path_graph.nodes[node]['path_score'] = path.score
                    
                # Merge into the main graph
                G.add_nodes_from(path_graph.nodes(data=True))
                G.add_edges_from(path_graph.edges(data=True))
                
            # If no output path, return the graph
            if not output_path:
                return G
                
            # Create a visualization
            plt.figure(figsize=(12, 8))
            
            # Use spring layout
            pos = nx.spring_layout(G, seed=42)
            
            # Draw nodes colored by path
            path_colors = plt.cm.tab10.colors
            for i, path in enumerate(all_paths):
                path_nodes = [node for node, attrs in G.nodes(data=True) 
                             if attrs.get('path_idx') == i]
                nx.draw_networkx_nodes(
                    G, pos, 
                    nodelist=path_nodes,
                    node_color=[path_colors[i % len(path_colors)]], 
                    alpha=0.8,
                    node_size=500
                )
                
            # Draw edges
            nx.draw_networkx_edges(G, pos, alpha=0.5, arrows=True)
            
            # Draw labels (truncated for readability)
            labels = {}
            for node in G.nodes():
                content = G.nodes[node].get('content', '')
                # Truncate long content
                if len(content) > 30:
                    content = content[:27] + "..."
                labels[node] = content
                
            nx.draw_networkx_labels(G, pos, labels=labels, font_size=8)
            
            # Set title and layout
            plt.title("Multi-Path Chain of Thought Reasoning")
            plt.axis('off')
            plt.tight_layout()
            
            # Save figure
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            # Return the graph
            return G
            
        except ImportError as e:
            self.logger.warning(f"Visualization requires matplotlib and networkx: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Error in visualization: {e}")
            return None

# Make key classes available at module level
__all__ = [
    'MultiPathChainOfThought',
    'ReasoningPath',
    'ReasoningStep',
    'ReasoningStepType',
    'PathEvaluator',
    'CompositePathEvaluator',
    'BeamSearchGenerator',
    'ResourceAllocator'
]

# Optional initialization function to create a default instance
def create_default_mcot(language_model=None, thought_latent_space=None):
    """
    Create a default Multi-Path Chain of Thought instance.
    
    Args:
        language_model: Optional language model for reasoning
        thought_latent_space: Optional thought latent space for reasoning
        
    Returns:
        Configured MultiPathChainOfThought instance
    """
    return MultiPathChainOfThought(
        language_model=language_model,
        max_paths=5,
        beam_width=3,
        thought_latent_space=thought_latent_space
    )

# Main execution for standalone usage and testing
if __name__ == "__main__":
    # Example usage
    example_problem = "What is the most efficient algorithm for finding the shortest path in a weighted graph?"
    
    # Create a mock language model for testing
    class MockLanguageModel:
        def generate(self, prompt):
            import random
            responses = [
                "We can use Dijkstra's algorithm, which has a time complexity of O(V^2) or O(E + V log V) with a priority queue.",
                "A* algorithm is optimal for shortest path when we have heuristic information about node distances.",
                "Bellman-Ford algorithm handles negative weights in O(VE) time, unlike Dijkstra's algorithm.",
                "For unweighted graphs, a simple breadth-first search (BFS) would suffice with O(V + E) complexity.",
                "Floyd-Warshall algorithm can find all pairs shortest paths in O(V^3) time."
            ]
            return random.choice(responses)
    
    # Create a chain of thought reasoner
    cot = MultiPathChainOfThought(
        language_model=MockLanguageModel(),
        max_paths=3,
        beam_width=2
    )
    
    # Generate reasoning paths
    paths = cot.generate_reasoning_paths(
        problem_statement=example_problem,
        max_iterations=3
    )
    
    # Print results
    print(f"Generated {len(paths)} reasoning paths:")
    for i, path in enumerate(paths):
        print(f"\nPath {i+1} (score: {path['score']:.2f}):")
        print(path['text'])
        print(f"Conclusion: {path['conclusion']}")