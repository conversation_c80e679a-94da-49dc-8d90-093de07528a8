#!/usr/bin/env python3
"""
ULTRA Meta-Cognitive System

This module implements the Meta-Cognitive System for the ULTRA framework, which serves as 
the executive function of the system. It implements high-level reasoning strategies, monitors 
and evaluates reasoning processes, and adapts strategies based on performance feedback.

The Meta-Cognitive System consists of several integrated components:
1. Multi-Path Chain of Thought: Explores multiple reasoning pathways simultaneously
2. Tree of Thought Exploration: Organizes reasoning paths in a hierarchical tree structure
3. Reasoning Graphs: Extends reasoning to graph structures for complex, non-linear deduction
4. Self-Critique Loop: Evaluates and refines reasoning by identifying and correcting errors
5. Bias Detection and Correction: Identifies and mitigates cognitive biases
6. Meta-Learning Controller: Adapts reasoning strategies based on performance and task characteristics
"""

import os
import sys
import time
import logging
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Any, Optional, Union, Set, Callable
from dataclasses import dataclass, field
from enum import Enum, auto
from collections import defaultdict
import networkx as nx

# Import other ULTRA components
from ultra.config import get_config, ULTRAConfigManager
from ultra.utils.ultra_logging import get_logger
from ultra.core_neural.neuromorphic_core import Neuromorphic<PERSON>ore
from ultra.hyper_transformer.dynamic_attention import SelfEvolvingDynamicAttention as DynamicAttention
from ultra.diffusion_reasoning.thought_latent_space import ThoughtLatentSpace
from ultra.knowledge_management.episodic_knowledge import EpisodicKnowledgeBase
from ultra.neuro_symbolic.neuro_symbolic_bridge import NeuroSymbolicBridge

# Import Meta-Cognitive components
from ultra.meta_cognitive.chain_of_thought import MultiPathChainOfThought
from ultra.meta_cognitive.tree_of_thought import TreeOfThoughtExploration
from ultra.meta_cognitive.reasoning_graphs import ReasoningGraphs
from ultra.meta_cognitive.self_critique import SelfCritiqueLoop
from ultra.meta_cognitive.bias_detection import BiasDetection
from ultra.meta_cognitive.meta_learning import MetaLearningController

logger = get_logger(__name__)

class ReasoningStrategy(Enum):
    """Enumeration of available reasoning strategies in the Meta-Cognitive System."""
    CHAIN_OF_THOUGHT = auto()
    TREE_OF_THOUGHT = auto()
    REASONING_GRAPH = auto()
    MULTI_PATH = auto()
    SELF_CRITIQUE = auto()
    HYBRID = auto()

class ReasoningResult:
    """
    Class representing the result of a reasoning process, including the reasoning path,
    confidence score, alternative paths, and justifications.
    """
    def __init__(
        self, 
        conclusion: str, 
        confidence: float, 
        reasoning_path: List[str], 
        alternatives: Optional[List[Tuple[str, float]]] = None,
        justifications: Optional[Dict[str, List[str]]] = None,
        graph: Optional[Any] = None,
        execution_time: float = 0.0
    ):
        self.conclusion = conclusion
        self.confidence = confidence
        self.reasoning_path = reasoning_path
        self.alternatives = alternatives or []
        self.justifications = justifications or {}
        self.graph = graph
        self.execution_time = execution_time
        self.critique = None
        self.biases = []
        
    def add_critique(self, critique: str):
        """Add a critique to the reasoning result."""
        self.critique = critique
        
    def add_bias(self, bias_type: str, bias_score: float):
        """Add a detected bias to the reasoning result."""
        self.biases.append((bias_type, bias_score))
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert the reasoning result to a dictionary."""
        return {
            "conclusion": self.conclusion,
            "confidence": self.confidence,
            "reasoning_path": self.reasoning_path,
            "alternatives": self.alternatives,
            "justifications": self.justifications,
            "execution_time": self.execution_time,
            "critique": self.critique,
            "biases": self.biases
        }

@dataclass
class MetaCognitiveState:
    """
    Class representing the internal state of the Meta-Cognitive System.
    This maintains the current reasoning context, history, and performance metrics.
    """
    problem_statement: str = ""
    reasoning_strategy: ReasoningStrategy = ReasoningStrategy.CHAIN_OF_THOUGHT
    active_reasoning_paths: List[Any] = field(default_factory=list)
    completed_reasoning_paths: List[Any] = field(default_factory=list)
    current_tree: Any = None
    current_graph: Any = None
    reasoning_history: List[Dict[str, Any]] = field(default_factory=list)
    strategy_performance: Dict[ReasoningStrategy, List[float]] = field(default_factory=lambda: defaultdict(list))
    resource_allocation: Dict[str, float] = field(default_factory=dict)
    uncertainty_estimates: Dict[str, float] = field(default_factory=dict)
    attention_focus: Dict[str, float] = field(default_factory=dict)
    
    def reset(self):
        """Reset the state for a new reasoning task."""
        self.problem_statement = ""
        self.active_reasoning_paths.clear()
        self.completed_reasoning_paths.clear()
        self.current_tree = None
        self.current_graph = None
        self.resource_allocation.clear()
        self.uncertainty_estimates.clear()
        self.attention_focus.clear()
    
    def update_strategy_performance(self, strategy: ReasoningStrategy, performance: float):
        """Update the performance record for a reasoning strategy."""
        self.strategy_performance[strategy].append(performance)
        
    def get_average_strategy_performance(self, strategy: ReasoningStrategy) -> float:
        """Get the average performance of a reasoning strategy."""
        performances = self.strategy_performance.get(strategy, [])
        return sum(performances) / len(performances) if performances else 0.0


class MetaCognitiveSystem:
    """
    Main class implementing the Meta-Cognitive System of ULTRA.
    
    This system integrates multiple reasoning approaches and serves as the executive
    control for ULTRA's reasoning capabilities. It monitors, evaluates, and adapts
    reasoning strategies based on performance feedback.
    """
    
    def __init__(
        self, 
        config: Optional[Dict[str, Any]] = None,
        language_model=None,
        neuromorphic_core: Optional[NeuromorphicCore] = None,
        thought_latent_space: Optional[ThoughtLatentSpace] = None,
        neuro_symbolic_bridge: Optional[NeuroSymbolicBridge] = None,
        knowledge_base: Optional[EpisodicKnowledgeBase] = None
    ):
        """
        Initialize the Meta-Cognitive System.
        
        Args:
            config: Configuration dictionary for the Meta-Cognitive System
            language_model: Language model to use for reasoning (if not provided, the system will use its internal models)
            neuromorphic_core: Link to the Core Neural Architecture
            thought_latent_space: Link to the Diffusion-Based Reasoning component
            neuro_symbolic_bridge: Link to the Neuro-Symbolic Integration component
            knowledge_base: Link to the Knowledge Management System
        """
        self.config = config or {}
        self.language_model = language_model
        self.neuromorphic_core = neuromorphic_core
        self.thought_latent_space = thought_latent_space
        self.neuro_symbolic_bridge = neuro_symbolic_bridge
        self.knowledge_base = knowledge_base
        
        # Initialize state
        self.state = MetaCognitiveState()
        
        # Initialize logging
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(self.config.get("log_level", logging.INFO))
        
        # Initialize Meta-Cognitive components
        self._init_components()
        
        # Performance metrics
        self.performance_history = []
        
    def _init_components(self):
        """Initialize all Meta-Cognitive components."""
        # Component initialization parameters
        max_paths = self.config.get("max_reasoning_paths", 5)
        beam_width = self.config.get("beam_width", 3)
        max_depth = self.config.get("max_tree_depth", 5)
        branching_factor = self.config.get("tree_branching_factor", 3)
        iterations = self.config.get("critique_iterations", 3)
        
        # Initialize components with appropriate parameters and connections
        self.multi_path_cot = MultiPathChainOfThought(
            language_model=self.language_model,
            max_paths=max_paths,
            beam_width=beam_width,
            thought_latent_space=self.thought_latent_space
        )
        
        self.tree_of_thought = TreeOfThoughtExploration(
            language_model=self.language_model,
            max_depth=max_depth,
            branching_factor=branching_factor,
            thought_latent_space=self.thought_latent_space
        )
        
        self.reasoning_graphs = ReasoningGraphs(
            language_model=self.language_model,
            neuro_symbolic_bridge=self.neuro_symbolic_bridge
        )
        
        self.self_critique = SelfCritiqueLoop(
            language_model=self.language_model,
            iterations=iterations
        )
        
        self.bias_detection = BiasDetection(
            language_model=self.language_model,
            neuromorphic_core=self.neuromorphic_core
        )
        
        # Initialize the Meta-Learning Controller with all available reasoning modules
        self.meta_learning = MetaLearningController(
            reasoning_modules={
                ReasoningStrategy.CHAIN_OF_THOUGHT: self.multi_path_cot,
                ReasoningStrategy.TREE_OF_THOUGHT: self.tree_of_thought,
                ReasoningStrategy.REASONING_GRAPH: self.reasoning_graphs,
                ReasoningStrategy.SELF_CRITIQUE: self.self_critique,
            },
            language_model=self.language_model,
            knowledge_base=self.knowledge_base
        )
        
        # Register event handlers for component interactions
        self._register_event_handlers()
        
    def _register_event_handlers(self):
        """Register event handlers for component interactions."""
        # Connect the Self-Critique Loop to each reasoning strategy
        self.multi_path_cot.register_critique_handler(self.self_critique.critique)
        self.tree_of_thought.register_critique_handler(self.self_critique.critique)
        self.reasoning_graphs.register_critique_handler(self.self_critique.critique)
        
        # Connect the Bias Detection to each reasoning strategy
        self.multi_path_cot.register_bias_handler(self.bias_detection.detect_biases)
        self.tree_of_thought.register_bias_handler(self.bias_detection.detect_biases)
        self.reasoning_graphs.register_bias_handler(self.bias_detection.detect_biases)
        
    def reason(
        self, 
        problem_statement: str, 
        strategy: Union[str, ReasoningStrategy] = None, 
        context: Optional[Dict[str, Any]] = None,
        constraints: Optional[Dict[str, Any]] = None,
        max_iterations: int = 10,
        meta_cognitive: bool = True
    ) -> ReasoningResult:
        """
        Execute a reasoning process on a given problem statement.
        
        Args:
            problem_statement: The problem statement to reason about
            strategy: The reasoning strategy to use (if None, the Meta-Learning Controller selects the best strategy)
            context: Additional context for the reasoning process
            constraints: Constraints on the reasoning process
            max_iterations: Maximum number of reasoning iterations
            meta_cognitive: Whether to use meta-cognitive monitoring and adaptation
            
        Returns:
            A ReasoningResult containing the conclusion, confidence, reasoning path, etc.
        """
        # Reset state for new reasoning task
        self.state.reset()
        self.state.problem_statement = problem_statement
        
        # Convert string strategy to enum if needed
        if isinstance(strategy, str):
            try:
                strategy = ReasoningStrategy[strategy.upper()]
            except KeyError:
                self.logger.warning(f"Unknown strategy: {strategy}. Using Meta-Learning Controller.")
                strategy = None
        
        # If no strategy specified, use Meta-Learning Controller to select best strategy
        if strategy is None:
            self.logger.info("Selecting reasoning strategy using Meta-Learning Controller")
            strategy = self.meta_learning.select_reasoning_strategy(
                problem_statement, 
                context=context, 
                constraints=constraints
            )
            
        self.state.reasoning_strategy = strategy
        self.logger.info(f"Selected reasoning strategy: {strategy.name}")
        
        # Prepare reasoning parameters
        reasoning_params = {
            "problem_statement": problem_statement,
            "context": context or {},
            "constraints": constraints or {},
            "max_iterations": max_iterations
        }
        
        # Execute reasoning based on selected strategy
        start_time = time.time()
        
        if strategy == ReasoningStrategy.CHAIN_OF_THOUGHT:
            result = self._reason_with_cot(**reasoning_params)
        elif strategy == ReasoningStrategy.TREE_OF_THOUGHT:
            result = self._reason_with_tot(**reasoning_params)
        elif strategy == ReasoningStrategy.REASONING_GRAPH:
            result = self._reason_with_graph(**reasoning_params)
        elif strategy == ReasoningStrategy.MULTI_PATH:
            result = self._reason_with_multi_path(**reasoning_params)
        elif strategy == ReasoningStrategy.SELF_CRITIQUE:
            # First use CoT, then apply self-critique
            initial_result = self._reason_with_cot(**reasoning_params)
            result = self._apply_self_critique(initial_result)
        elif strategy == ReasoningStrategy.HYBRID:
            result = self._reason_with_hybrid(**reasoning_params)
        else:
            raise ValueError(f"Unknown reasoning strategy: {strategy}")
            
        # Record execution time
        result.execution_time = time.time() - start_time
            
        # Apply bias detection
        self._detect_and_correct_biases(result)
        
        # Update performance metrics
        self._update_performance_metrics(strategy, result)
        
        # If meta-cognitive mode enabled, analyze reasoning performance and adapt strategies
        if meta_cognitive:
            self._meta_cognitive_analysis(result)
            
        return result
            
    def _reason_with_cot(self, **kwargs) -> ReasoningResult:
        """Execute reasoning using Chain of Thought."""
        problem = kwargs["problem_statement"]
        context = kwargs["context"]
        
        # Generate reasoning paths using Multi-Path Chain of Thought
        paths = self.multi_path_cot.generate_reasoning_paths(
            problem_statement=problem,
            context=context
        )
        
        # Store paths in state
        self.state.completed_reasoning_paths = paths
        
        # Get the highest-scoring path
        best_path = max(paths, key=lambda x: x["score"])
        
        # Extract conclusion from the best path
        conclusion = self._extract_conclusion(best_path["text"])
        
        # Create reasoning steps list
        reasoning_steps = best_path["text"].split("\n")
        
        # Calculate confidence from the score
        confidence = min(best_path["score"], 1.0)
        
        # Create alternatives from other paths
        alternatives = [
            (self._extract_conclusion(path["text"]), path["score"])
            for path in paths if path != best_path
        ]
        
        return ReasoningResult(
            conclusion=conclusion,
            confidence=confidence,
            reasoning_path=reasoning_steps,
            alternatives=alternatives
        )
        
    def _reason_with_tot(self, **kwargs) -> ReasoningResult:
        """Execute reasoning using Tree of Thought."""
        problem = kwargs["problem_statement"]
        context = kwargs["context"]
        max_iterations = kwargs["max_iterations"]
        
        # Explore the tree of thought
        tree, best_path = self.tree_of_thought.explore(
            problem_statement=problem,
            context=context,
            max_iterations=max_iterations
        )
        
        # Store tree in state
        self.state.current_tree = tree
        
        # Extract conclusion and reasoning path
        conclusion = best_path[-1] if best_path else ""
        reasoning_path = best_path
        
        # Calculate confidence based on tree exploration
        confidence = self.tree_of_thought.calculate_path_confidence(best_path)
        
        # Find alternative paths
        alternative_paths = self.tree_of_thought.find_alternative_paths(tree, n=3)
        alternatives = [
            (path[-1], self.tree_of_thought.calculate_path_confidence(path))
            for path in alternative_paths
        ]
        
        return ReasoningResult(
            conclusion=conclusion,
            confidence=confidence,
            reasoning_path=reasoning_path,
            alternatives=alternatives,
            graph=tree  # Store the tree for visualization
        )
        
    def _reason_with_graph(self, **kwargs) -> ReasoningResult:
        """Execute reasoning using Reasoning Graphs."""
        problem = kwargs["problem_statement"]
        context = kwargs["context"]
        
        # Build the reasoning graph
        graph = self.reasoning_graphs.build_reasoning_graph(
            problem_statement=problem,
            context=context
        )
        
        # Store graph in state
        self.state.current_graph = graph
        
        # Find the strongest conclusion
        conclusion = self.reasoning_graphs.find_strongest_conclusion(graph)
        
        # Extract reasoning path as a list of key steps
        reasoning_path = self.reasoning_graphs.extract_reasoning_path(graph, conclusion)
        
        # Calculate confidence
        confidence = self.reasoning_graphs.calculate_conclusion_confidence(graph, conclusion)
        
        # Find alternative conclusions
        alternatives = self.reasoning_graphs.find_alternative_conclusions(graph, n=3)
        
        # Extract justifications for the conclusion
        justifications = self.reasoning_graphs.extract_justifications(graph, conclusion)
        
        return ReasoningResult(
            conclusion=conclusion,
            confidence=confidence,
            reasoning_path=reasoning_path,
            alternatives=alternatives,
            justifications=justifications,
            graph=graph  # Store the graph for visualization
        )
        
    def _reason_with_multi_path(self, **kwargs) -> ReasoningResult:
        """Execute reasoning using Multi-Path exploration with dynamic resource allocation."""
        problem = kwargs["problem_statement"]
        context = kwargs["context"]
        max_iterations = kwargs["max_iterations"]
        
        # Execute multi-path reasoning with dynamic resource allocation
        results = self.multi_path_cot.explore_with_resource_allocation(
            problem_statement=problem,
            context=context,
            max_iterations=max_iterations
        )
        
        # Store resource allocation in state
        self.state.resource_allocation = results["resource_allocation"]
        
        # Get paths and their final scores
        paths = results["paths"]
        self.state.completed_reasoning_paths = paths
        
        # Select the best path
        best_path = max(paths, key=lambda x: x["final_score"])
        
        # Extract conclusion
        conclusion = self._extract_conclusion(best_path["text"])
        
        # Create reasoning steps
        reasoning_steps = best_path["text"].split("\n")
        
        # Calculate confidence
        confidence = min(best_path["final_score"], 1.0)
        
        # Create alternatives
        alternatives = [
            (self._extract_conclusion(path["text"]), path["final_score"])
            for path in paths if path != best_path
        ]
        
        # Extract justifications across paths
        justifications = self._extract_cross_path_justifications(paths)
        
        return ReasoningResult(
            conclusion=conclusion,
            confidence=confidence,
            reasoning_path=reasoning_steps,
            alternatives=alternatives,
            justifications=justifications
        )
    
    def _reason_with_hybrid(self, **kwargs) -> ReasoningResult:
        """
        Execute reasoning using a hybrid approach that combines multiple strategies.
        
        This approach:
        1. Uses Tree of Thought to explore the high-level reasoning space
        2. Uses Reasoning Graphs to analyze the logical structure of promising paths
        3. Uses Chain of Thought for detailed exploration of the most promising branches
        4. Applies Self-Critique to refine the final reasoning
        """
        problem = kwargs["problem_statement"]
        context = kwargs["context"]
        max_iterations = kwargs["max_iterations"]
        
        # Phase 1: Tree of Thought exploration to identify promising paths
        self.logger.info("Hybrid reasoning phase 1: Tree of Thought exploration")
        tree, promising_paths = self.tree_of_thought.explore_and_return_multiple_paths(
            problem_statement=problem,
            context=context,
            max_paths=3,
            max_iterations=max_iterations // 3  # Allocate 1/3 of iterations to this phase
        )
        
        # Phase 2: Build reasoning graphs for the most promising paths
        self.logger.info("Hybrid reasoning phase 2: Reasoning Graph analysis")
        graphs = []
        for path in promising_paths:
            # Convert path to a subproblem focused on this direction
            subproblem = self._path_to_subproblem(problem, path)
            graph = self.reasoning_graphs.build_reasoning_graph(
                problem_statement=subproblem,
                context=context
            )
            graphs.append((path, graph))
        
        # Evaluate and select the most structurally sound reasoning direction
        best_path, best_graph = max(
            graphs, 
            key=lambda x: self.reasoning_graphs.evaluate_graph_structure(x[1])
        )
        
        # Phase 3: Detailed Chain of Thought exploration of the best path
        self.logger.info("Hybrid reasoning phase 3: Detailed Chain of Thought")
        subproblem = self._path_to_subproblem(problem, best_path)
        detailed_paths = self.multi_path_cot.generate_reasoning_paths(
            problem_statement=subproblem,
            context=context,
            max_paths=max_iterations // 3  # Allocate 1/3 of iterations
        )
        
        # Select the best detailed path
        best_detailed_path = max(detailed_paths, key=lambda x: x["score"])
        
        # Phase 4: Self-critique and refinement
        self.logger.info("Hybrid reasoning phase 4: Self-critique and refinement")
        initial_result = ReasoningResult(
            conclusion=self._extract_conclusion(best_detailed_path["text"]),
            confidence=best_detailed_path["score"],
            reasoning_path=best_detailed_path["text"].split("\n"),
            graph=best_graph
        )
        
        refined_result = self._apply_self_critique(initial_result)
        
        # Store relevant state
        self.state.current_tree = tree
        self.state.current_graph = best_graph
        self.state.completed_reasoning_paths = detailed_paths
        
        return refined_result
    
    def _apply_self_critique(self, initial_result: ReasoningResult) -> ReasoningResult:
        """Apply self-critique loop to refine reasoning."""
        # Combine reasoning path into a single text
        initial_solution = "\n".join(initial_result.reasoning_path)
        
        # Apply self-critique to refine the reasoning
        refined_solution = self.self_critique.refine_reasoning(
            problem=self.state.problem_statement,
            initial_solution=initial_solution
        )
        
        # Extract the critique for inclusion in the result
        critique = self.self_critique.generate_critique(
            problem=self.state.problem_statement,
            solution=initial_solution
        )
        
        # Create a new result with the refined reasoning
        refined_result = ReasoningResult(
            conclusion=self._extract_conclusion(refined_solution),
            confidence=initial_result.confidence * 1.1,  # Slightly increase confidence after refinement
            reasoning_path=refined_solution.split("\n"),
            alternatives=initial_result.alternatives,
            justifications=initial_result.justifications,
            graph=initial_result.graph
        )
        
        # Add the critique to the result
        refined_result.add_critique(critique)
        
        return refined_result
        
    def _detect_and_correct_biases(self, result: ReasoningResult):
        """Detect and correct biases in the reasoning result."""
        # Combine reasoning path into a single text
        reasoning_text = "\n".join(result.reasoning_path)
        
        # Detect biases
        detected_biases = self.bias_detection.detect_biases(reasoning_text)
        
        # Add detected biases to the result
        for bias_type, bias_score in detected_biases:
            result.add_bias(bias_type, bias_score)
            
        # If significant biases detected, correct the reasoning
        if detected_biases and any(score > 0.7 for _, score in detected_biases):
            corrected_reasoning = self.bias_detection.correct_biased_reasoning(
                reasoning_text, detected_biases
            )
            
            # Update the reasoning path and conclusion
            result.reasoning_path = corrected_reasoning.split("\n")
            result.conclusion = self._extract_conclusion(corrected_reasoning)
            
            # Adjust confidence based on bias correction
            result.confidence *= 0.9  # Slightly decrease confidence after bias correction
            
    def _update_performance_metrics(self, strategy: ReasoningStrategy, result: ReasoningResult):
        """Update performance metrics for the reasoning strategy."""
        # Calculate a performance score based on confidence and execution time
        # Higher confidence and lower execution time yield better performance
        max_exec_time = self.config.get("max_acceptable_exec_time", 10.0)
        time_factor = max(0, 1 - (result.execution_time / max_exec_time))
        
        # Adjust for detected biases - more biases means lower performance
        bias_penalty = sum(score for _, score in result.biases) / 10.0 if result.biases else 0
        
        # Calculate overall performance score
        performance = result.confidence * 0.7 + time_factor * 0.3 - bias_penalty
        performance = max(0, min(1, performance))  # Clamp to [0, 1]
        
        # Update strategy performance in state
        self.state.update_strategy_performance(strategy, performance)
        
        # Add to performance history
        self.performance_history.append({
            "strategy": strategy.name,
            "problem": self.state.problem_statement[:100],  # Truncate for brevity
            "performance": performance,
            "confidence": result.confidence,
            "execution_time": result.execution_time,
            "biases": result.biases,
            "timestamp": time.time()
        })
        
    def _meta_cognitive_analysis(self, result: ReasoningResult):
        """
        Perform meta-cognitive analysis of the reasoning process to improve future reasoning.
        This includes analyzing failures, adapting strategies, and updating internal models.
        """
        # Analyze uncertainty in the reasoning
        self._analyze_uncertainty(result)
        
        # Update the Meta-Learning Controller with the results
        self.meta_learning.update_with_results(
            problem=self.state.problem_statement,
            strategy=self.state.reasoning_strategy,
            result=result.to_dict()
        )
        
        # If confidence is low, analyze why and adjust strategy selection parameters
        if result.confidence < 0.6:
            self.meta_learning.analyze_low_confidence_case(
                problem=self.state.problem_statement,
                strategy=self.state.reasoning_strategy,
                result=result.to_dict()
            )
            
        # Record reasoning case for future reference
        self.state.reasoning_history.append({
            "problem": self.state.problem_statement,
            "strategy": self.state.reasoning_strategy.name,
            "result": result.to_dict(),
            "timestamp": time.time()
        })
        
    def _analyze_uncertainty(self, result: ReasoningResult):
        """Analyze uncertainty in the reasoning result."""
        # Extract uncertainty sources from the reasoning
        uncertainty_sources = {}
        
        # Check for uncertainty terms in the reasoning path
        uncertainty_terms = ["might", "could", "perhaps", "maybe", "uncertain", 
                            "possibly", "unclear", "probability", "chance", "likelihood"]
        
        for i, step in enumerate(result.reasoning_path):
            for term in uncertainty_terms:
                if term in step.lower():
                    uncertainty_sources[f"step_{i}"] = uncertainty_sources.get(f"step_{i}", 0) + 1
        
        # Check for divergence in alternative conclusions
        if result.alternatives:
            alt_conclusions = [alt[0] for alt in result.alternatives]
            conclusion_diversity = len(set(alt_conclusions)) / len(alt_conclusions)
            uncertainty_sources["conclusion_diversity"] = conclusion_diversity
            
        # Update state with uncertainty estimates
        self.state.uncertainty_estimates = uncertainty_sources
        
    def _extract_conclusion(self, text: str) -> str:
        """Extract the conclusion from reasoning text."""
        # Look for conclusion indicators
        conclusion_indicators = [
            "Therefore,", "Thus,", "Hence,", "So,", "In conclusion,", 
            "Consequently,", "As a result,", "The answer is", "The conclusion is"
        ]
        
        lines = text.split("\n")
        
        # Check for lines with conclusion indicators
        for i, line in enumerate(lines):
            for indicator in conclusion_indicators:
                if indicator in line:
                    return line.strip()
        
        # If no explicit indicator, use last line as conclusion
        if lines:
            return lines[-1].strip()
        
        return "No conclusion found"
        
    def _extract_cross_path_justifications(self, paths: List[Dict[str, Any]]) -> Dict[str, List[str]]:
        """Extract justifications that appear across multiple reasoning paths."""
        # Extract key points from each path
        all_points = []
        for path in paths:
            points = self._extract_key_points(path["text"])
            all_points.append(points)
            
        # Count occurrences of similar points
        point_counts = defaultdict(int)
        for points in all_points:
            for point in points:
                # Use a simplified representation for matching
                simple_point = self._simplify_point(point)
                point_counts[simple_point] += 1
                
        # Extract points that appear in multiple paths
        justifications = defaultdict(list)
        for simple_point, count in point_counts.items():
            if count > 1:  # Point appears in multiple paths
                # Find the original text for this simplified point
                for path_idx, points in enumerate(all_points):
                    for point in points:
                        if self._simplify_point(point) == simple_point:
                            justifications[f"Path_{path_idx+1}"].append(point)
                            break
                            
        return dict(justifications)
        
    def _extract_key_points(self, text: str) -> List[str]:
        """Extract key points from reasoning text."""
        lines = text.split("\n")
        key_points = []
        
        for line in lines:
            # Skip empty lines
            if not line.strip():
                continue
                
            # If line starts with a number or bullet, likely a key point
            if (line.strip().startswith(("-", "*", "•")) or 
                (line.strip()[0].isdigit() and line.strip()[1:3] in [". ", ") "])):
                key_points.append(line.strip())
            # If line contains certain phrases, likely a key point
            elif any(phrase in line for phrase in ["because", "since", "due to", "therefore", "thus"]):
                key_points.append(line.strip())
                
        return key_points
        
    def _simplify_point(self, point: str) -> str:
        """Simplify a point for fuzzy matching."""
        # Remove punctuation, lowercase, and keep only alphanumeric words
        import re
        words = re.findall(r'\b\w+\b', point.lower())
        # Keep only significant words (exclude common stopwords)
        stopwords = {"a", "an", "the", "and", "or", "but", "if", "as", "of", "at", "by", "for", "with", "to", "in", "on"}
        significant_words = [word for word in words if word not in stopwords]
        # Sort to ignore word order
        significant_words.sort()
        return " ".join(significant_words)
        
    def _path_to_subproblem(self, problem: str, path: List[str]) -> str:
        """Convert a reasoning path to a subproblem statement for focused exploration."""
        # Extract the high-level direction from the path
        direction = " ".join(path)
        
        # Create a subproblem that focuses on this direction
        subproblem = f"{problem}\n\nStarting with the following reasoning direction:\n{direction}\n\nContinue this reasoning to reach a detailed conclusion."
        
        return subproblem
        
    def evaluate_reasoning(self, result: ReasoningResult, ground_truth: Optional[str] = None) -> Dict[str, float]:
        """
        Evaluate the quality of reasoning against a ground truth (if available)
        or using internal evaluation metrics.
        
        Args:
            result: The reasoning result to evaluate
            ground_truth: The ground truth answer (if available)
            
        Returns:
            A dictionary of evaluation metrics
        """
        metrics = {}
        
        # If ground truth available, calculate accuracy
        if ground_truth is not None:
            # Calculate string similarity between conclusion and ground truth
            import Levenshtein
            similarity = 1.0 - (Levenshtein.distance(result.conclusion, ground_truth) / 
                               max(len(result.conclusion), len(ground_truth)))
            metrics["accuracy"] = similarity
            
        # Calculate reasoning quality metrics
        metrics["confidence"] = result.confidence
        metrics["reasoning_length"] = len(result.reasoning_path)
        metrics["alternatives_count"] = len(result.alternatives)
        metrics["execution_time"] = result.execution_time
        metrics["bias_count"] = len(result.biases)
        
        # Calculate bias severity if biases present
        if result.biases:
            metrics["bias_severity"] = sum(score for _, score in result.biases) / len(result.biases)
        else:
            metrics["bias_severity"] = 0.0
            
        # Calculate reasoning coherence
        coherence = self._calculate_reasoning_coherence(result.reasoning_path)
        metrics["coherence"] = coherence
        
        # Calculate reasoning divergence (how much alternatives differ)
        if result.alternatives:
            divergence = self._calculate_reasoning_divergence(
                result.conclusion, 
                [alt[0] for alt in result.alternatives]
            )
            metrics["divergence"] = divergence
        else:
            metrics["divergence"] = 0.0
            
        return metrics
        
    def _calculate_reasoning_coherence(self, reasoning_path: List[str]) -> float:
        """Calculate coherence of a reasoning path based on logical flow between steps."""
        if len(reasoning_path) <= 1:
            return 1.0  # Only one step, so coherent by default
            
        coherence_scores = []
        for i in range(1, len(reasoning_path)):
            # Calculate coherence between consecutive steps
            prev_step = reasoning_path[i-1]
            curr_step = reasoning_path[i]
            
            # Use overlap of key terms as a simple coherence metric
            prev_terms = set(self._extract_key_terms(prev_step))
            curr_terms = set(self._extract_key_terms(curr_step))
            
            if not prev_terms or not curr_terms:
                coherence_scores.append(0.5)  # Neutral score if no terms
                continue
                
            # Calculate Jaccard similarity
            overlap = len(prev_terms.intersection(curr_terms))
            union = len(prev_terms.union(curr_terms))
            
            coherence_scores.append(overlap / union if union > 0 else 0)
            
        return sum(coherence_scores) / len(coherence_scores) if coherence_scores else 0
        
    def _extract_key_terms(self, text: str) -> List[str]:
        """Extract key terms from text for coherence calculation."""
        import re
        # Extract words, excluding common stopwords
        stopwords = {"a", "an", "the", "and", "or", "but", "if", "as", "of", "at", "by", "for", "with", "to", "in", "on"}
        words = re.findall(r'\b\w+\b', text.lower())
        return [word for word in words if word not in stopwords and len(word) > 2]
        
    def _calculate_reasoning_divergence(self, main_conclusion: str, alternative_conclusions: List[str]) -> float:
        """Calculate how much alternative conclusions diverge from the main conclusion."""
        if not alternative_conclusions:
            return 0.0
            
        import Levenshtein
        distances = []
        
        for alt_conclusion in alternative_conclusions:
            # Calculate normalized Levenshtein distance
            distance = Levenshtein.distance(main_conclusion, alt_conclusion)
            normalized_distance = distance / max(len(main_conclusion), len(alt_conclusion))
            distances.append(normalized_distance)
            
        return sum(distances) / len(distances) if distances else 0
        
    def visualize_reasoning(self, result: ReasoningResult, output_path: Optional[str] = None):
        """
        Generate a visualization of the reasoning process.
        
        Args:
            result: The reasoning result to visualize
            output_path: Path to save the visualization (if None, visualization will be displayed)
        """
        try:
            import matplotlib.pyplot as plt
            import networkx as nx
            
            # Create a figure
            plt.figure(figsize=(12, 8))
            
            # Check if we have a graph or tree to visualize
            if result.graph is not None:
                # Check the type of graph
                if isinstance(result.graph, nx.Graph):
                    # It's already a NetworkX graph
                    G = result.graph
                else:
                    # Try to convert to a NetworkX graph
                    G = self._convert_to_networkx(result.graph)
                    
                # Visualize the graph
                pos = nx.spring_layout(G)
                nx.draw_networkx(G, pos, with_labels=True, font_size=10, 
                                node_size=700, node_color='skyblue')
                nx.draw_networkx_edge_labels(G, pos, edge_labels=nx.get_edge_attributes(G, 'label'))
                
                plt.title(f"Reasoning Graph: {result.conclusion[:40]}...")
                
            else:
                # Visualize the reasoning path as a linear flow
                G = nx.DiGraph()
                
                # Add nodes for each reasoning step
                for i, step in enumerate(result.reasoning_path):
                    # Truncate long steps for display
                    display_step = step[:50] + "..." if len(step) > 50 else step
                    G.add_node(i, label=display_step)
                    
                    # Add edge from previous step
                    if i > 0:
                        G.add_edge(i-1, i)
                        
                # Draw the graph
                pos = nx.spring_layout(G)
                nx.draw_networkx(G, pos, with_labels=True, 
                                labels={n: G.nodes[n]['label'] for n in G.nodes},
                                font_size=8, node_size=700, node_color='lightgreen')
                
                plt.title(f"Reasoning Path: {result.conclusion[:40]}...")
                
            plt.tight_layout()
            
            # Save or display the visualization
            if output_path:
                plt.savefig(output_path)
                self.logger.info(f"Visualization saved to {output_path}")
            else:
                plt.show()
                
        except ImportError as e:
            self.logger.warning(f"Visualization requires matplotlib and networkx: {e}")
            
    def _convert_to_networkx(self, graph_data) -> nx.Graph:
        """Convert a graph-like structure to a NetworkX graph."""
        import networkx as nx
        
        # Create a new directed graph
        G = nx.DiGraph()
        
        # Try to infer the structure of the input graph data
        if hasattr(graph_data, 'nodes') and hasattr(graph_data, 'edges'):
            # Appears to be a graph-like object with nodes and edges
            for node_id, node_data in graph_data.nodes.items():
                G.add_node(node_id, **node_data)
                
            for edge_id, edge_data in graph_data.edges.items():
                source = edge_data.get('from')
                target = edge_data.get('to')
                if source and target:
                    G.add_edge(source, target, **edge_data)
                    
        elif isinstance(graph_data, dict) and 'nodes' in graph_data and 'edges' in graph_data:
            # Dictionary with 'nodes' and 'edges' keys
            for node_id, node_data in graph_data['nodes'].items():
                G.add_node(node_id, **node_data)
                
            for edge_id, edge_data in graph_data['edges'].items():
                source = edge_data.get('from')
                target = edge_data.get('to')
                if source and target:
                    G.add_edge(source, target, **edge_data)
                    
        elif isinstance(graph_data, dict):
            # Generic dictionary, assume keys are nodes
            for node_id, connections in graph_data.items():
                G.add_node(node_id)
                if isinstance(connections, dict):
                    for target, edge_data in connections.items():
                        G.add_edge(node_id, target, **edge_data)
                elif isinstance(connections, list):
                    for target in connections:
                        G.add_edge(node_id, target)
                        
        else:
            # As a fallback, create a simple linear path
            self.logger.warning("Unable to convert graph data to NetworkX, creating a linear path")
            for i in range(10):
                G.add_node(i, label=f"Node {i}")
                if i > 0:
                    G.add_edge(i-1, i)
                    
        return G
        
    def save_state(self, filepath: str):
        """Save the current state of the Meta-Cognitive System to a file."""
        import pickle
        with open(filepath, 'wb') as f:
            pickle.dump({
                'state': self.state,
                'performance_history': self.performance_history
            }, f)
        self.logger.info(f"Meta-Cognitive state saved to {filepath}")
        
    def load_state(self, filepath: str):
        """Load the state of the Meta-Cognitive System from a file."""
        import pickle
        try:
            with open(filepath, 'rb') as f:
                data = pickle.load(f)
                self.state = data['state']
                self.performance_history = data['performance_history']
            self.logger.info(f"Meta-Cognitive state loaded from {filepath}")
            return True
        except Exception as e:
            self.logger.error(f"Error loading state: {e}")
            return False

# Import all components to make them available when importing the module
__all__ = [
    'MetaCognitiveSystem',
    'MultiPathChainOfThought',
    'TreeOfThoughtExploration',
    'ReasoningGraphs',
    'SelfCritiqueLoop',
    'BiasDetection',
    'MetaLearningController',
    'ReasoningStrategy',
    'ReasoningResult',
    'MetaCognitiveState'
]