#!/usr/bin/env python3
"""
ULTRA Meta-Cognitive System: Reasoning Graphs

This module implements the Reasoning Graphs component of the ULTRA Meta-Cognitive System.
Reasoning Graphs extend linear chain-of-thought reasoning to graph structures, enabling
complex, non-linear deduction and inference. This approach represents concepts, evidence,
and logical relationships as nodes and edges in a graph, allowing for multi-path reasoning,
logical validation, and identification of the strongest conclusions.

Key capabilities include:
1. Construction of graph-based reasoning from concepts and relationships
2. Message passing and inference propagation through the graph
3. Extraction of reasoning paths and justifications
4. Identification of the strongest conclusions based on graph analysis
5. Visualization and evaluation of reasoning structures
"""

import os
import time
import math
import json
import logging
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Any, Optional, Union, Set, Callable, Iterator
from dataclasses import dataclass, field
from enum import Enum, auto
from collections import defaultdict, deque
import copy
import re
import networkx as nx
from scipy.sparse import csr_matrix
import matplotlib.pyplot as plt

# Import ULTRA system components
from ultra.config import get_config, ULTRAConfigManager
from ultra.utils.ultra_logging import get_logger
from ultra.meta_cognitive.chain_of_thought import ReasoningStepType, ReasoningStep
from ultra.neuro_symbolic.neuro_symbolic_bridge import NeuroSymbolicBridge
from ultra.knowledge_management.episodic_knowledge import EpisodicKnowledgeBase

# Configure logger
logger = get_logger(__name__)

class NodeType(Enum):
    """Types of nodes in a reasoning graph."""
    OBSERVATION = auto()       # Observations from the problem or context
    HYPOTHESIS = auto()        # Hypotheses or conjectures
    EVIDENCE = auto()          # Supporting evidence
    INFERENCE = auto()         # Inferences drawn from other nodes
    CALCULATION = auto()       # Results of calculations
    CONSTRAINT = auto()        # Constraints on the solution
    COUNTERPOINT = auto()      # Counter-arguments or objections
    DEFINITION = auto()        # Definitions of terms or concepts
    PRINCIPLE = auto()         # General principles or rules
    ANALOGY = auto()           # Analogical reasoning
    EXAMPLE = auto()           # Examples or instances
    CONCLUSION = auto()        # Conclusions drawn from the reasoning

class EdgeType(Enum):
    """Types of edges (relationships) in a reasoning graph."""
    SUPPORTS = auto()          # Node A supports Node B
    CONTRADICTS = auto()       # Node A contradicts Node B
    IMPLIES = auto()           # Node A implies Node B
    CAUSES = auto()            # Node A causes Node B
    FOLLOWS_FROM = auto()      # Node B follows from Node A
    INSTANCE_OF = auto()       # Node A is an instance of Node B
    PART_OF = auto()           # Node A is part of Node B
    DERIVED_FROM = auto()      # Node A is derived from Node B
    RELATED_TO = auto()        # Node A is related to Node B
    DEPENDS_ON = auto()        # Node A depends on Node B
    ELABORATES = auto()        # Node A elaborates on Node B
    ANALOGOUS_TO = auto()      # Node A is analogous to Node B

@dataclass
class GraphNode:
    """Represents a node in the reasoning graph."""
    id: str
    content: str                            # Text content of the node
    node_type: NodeType                     # Type of node
    confidence: float = 1.0                 # Confidence in the node (0-1)
    source: Optional[str] = None            # Source of the node (e.g., problem statement, inference)
    embedding: Optional[np.ndarray] = None  # Vector embedding of the node
    metadata: Dict[str, Any] = field(default_factory=dict) # Additional metadata
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "id": self.id,
            "content": self.content,
            "node_type": self.node_type.name,
            "confidence": self.confidence,
            "source": self.source,
            "embedding": self.embedding.tolist() if self.embedding is not None else None,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'GraphNode':
        """Create a GraphNode from a dictionary."""
        embedding = np.array(data["embedding"]) if data.get("embedding") is not None else None
        
        return cls(
            id=data["id"],
            content=data["content"],
            node_type=NodeType[data["node_type"]],
            confidence=data["confidence"],
            source=data.get("source"),
            embedding=embedding,
            metadata=data.get("metadata", {})
        )
    
    def update_confidence(self, new_confidence: float) -> None:
        """Update the confidence value of the node."""
        # Ensure confidence is in the range [0, 1]
        self.confidence = max(0.0, min(1.0, new_confidence))
    
    def update_with_message(self, message: Dict[str, Any]) -> None:
        """Update the node with a message from another node."""
        # Update confidence based on the message
        message_confidence = message.get("confidence", 0.0)
        message_weight = message.get("weight", 0.5)
        
        # Update confidence using a weighted average
        self.confidence = (1 - message_weight) * self.confidence + message_weight * message_confidence
        
        # Update metadata
        if "metadata" in message:
            for key, value in message["metadata"].items():
                if key in self.metadata:
                    # If metadata exists, update it (e.g., increment a counter)
                    if isinstance(value, (int, float)) and isinstance(self.metadata[key], (int, float)):
                        self.metadata[key] += value
                    else:
                        self.metadata[key] = value
                else:
                    # Add new metadata
                    self.metadata[key] = value

@dataclass
class GraphEdge:
    """Represents an edge in the reasoning graph."""
    source_id: str
    target_id: str
    edge_type: EdgeType
    weight: float = 1.0                     # Weight/strength of the edge (0-1)
    bidirectional: bool = False             # Whether the edge is bidirectional
    metadata: Dict[str, Any] = field(default_factory=dict) # Additional metadata
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "source_id": self.source_id,
            "target_id": self.target_id,
            "edge_type": self.edge_type.name,
            "weight": self.weight,
            "bidirectional": self.bidirectional,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'GraphEdge':
        """Create a GraphEdge from a dictionary."""
        return cls(
            source_id=data["source_id"],
            target_id=data["target_id"],
            edge_type=EdgeType[data["edge_type"]],
            weight=data["weight"],
            bidirectional=data["bidirectional"],
            metadata=data.get("metadata", {})
        )
    
    def get_edge_id(self) -> str:
        """Generate a unique ID for the edge."""
        return f"{self.source_id}__{self.target_id}"
    
    def get_reverse_edge_id(self) -> str:
        """Generate a unique ID for the reverse edge (if bidirectional)."""
        return f"{self.target_id}__{self.source_id}"
    
    def get_reverse_edge(self) -> 'GraphEdge':
        """Create a reverse edge (for bidirectional edges)."""
        return GraphEdge(
            source_id=self.target_id,
            target_id=self.source_id,
            edge_type=self.edge_type,
            weight=self.weight,
            bidirectional=True,
            metadata=copy.deepcopy(self.metadata)
        )

class ReasoningGraph:
    """
    Represents a graph-based reasoning structure.
    
    A ReasoningGraph is a directed graph where nodes represent concepts, evidence,
    or conclusions, and edges represent logical relationships between them.
    """
    
    def __init__(self):
        """Initialize an empty reasoning graph."""
        self.nodes: Dict[str, GraphNode] = {}
        self.edges: Dict[str, GraphEdge] = {}
        self.node_counter = 0
        self.graph = nx.DiGraph()
        self.message_history = []
        self.metadata = {}
    
    def add_node(self, content: str, node_type: NodeType, confidence: float = 1.0, 
                 source: Optional[str] = None, node_id: Optional[str] = None,
                 embedding: Optional[np.ndarray] = None) -> str:
        """
        Add a node to the reasoning graph.
        
        Args:
            content: Text content of the node
            node_type: Type of the node
            confidence: Confidence in the node (0-1)
            source: Source of the node
            node_id: Optional ID for the node (generated if None)
            embedding: Optional vector embedding of the node
            
        Returns:
            ID of the added node
        """
        # Generate node ID if not provided
        if node_id is None:
            node_id = f"node_{self.node_counter}"
            self.node_counter += 1
        
        # Create the node
        node = GraphNode(
            id=node_id,
            content=content,
            node_type=node_type,
            confidence=confidence,
            source=source,
            embedding=embedding
        )
        
        # Add to nodes dictionary
        self.nodes[node_id] = node
        
        # Add to NetworkX graph
        self.graph.add_node(
            node_id, 
            content=content, 
            node_type=node_type.name,
            confidence=confidence,
            source=source
        )
        
        return node_id
    
    def add_edge(self, source_id: str, target_id: str, edge_type: EdgeType, 
                weight: float = 1.0, bidirectional: bool = False) -> str:
        """
        Add an edge to the reasoning graph.
        
        Args:
            source_id: ID of the source node
            target_id: ID of the target node
            edge_type: Type of the edge
            weight: Weight/strength of the edge (0-1)
            bidirectional: Whether the edge is bidirectional
            
        Returns:
            ID of the added edge
        """
        # Ensure nodes exist
        if source_id not in self.nodes:
            raise ValueError(f"Source node {source_id} does not exist")
        if target_id not in self.nodes:
            raise ValueError(f"Target node {target_id} does not exist")
        
        # Create the edge
        edge = GraphEdge(
            source_id=source_id,
            target_id=target_id,
            edge_type=edge_type,
            weight=weight,
            bidirectional=bidirectional
        )
        
        # Generate edge ID
        edge_id = edge.get_edge_id()
        
        # Add to edges dictionary
        self.edges[edge_id] = edge
        
        # Add to NetworkX graph
        self.graph.add_edge(
            source_id, 
            target_id, 
            edge_type=edge_type.name,
            weight=weight,
            bidirectional=bidirectional
        )
        
        # If bidirectional, add the reverse edge
        if bidirectional:
            reverse_edge = edge.get_reverse_edge()
            reverse_edge_id = reverse_edge.get_edge_id()
            self.edges[reverse_edge_id] = reverse_edge
            
            # Add to NetworkX graph
            self.graph.add_edge(
                target_id, 
                source_id, 
                edge_type=edge_type.name,
                weight=weight,
                bidirectional=True
            )
        
        return edge_id
    
    def remove_node(self, node_id: str) -> bool:
        """
        Remove a node from the reasoning graph.
        
        Args:
            node_id: ID of the node to remove
            
        Returns:
            True if the node was removed, False otherwise
        """
        if node_id not in self.nodes:
            return False
        
        # Remove connected edges
        edges_to_remove = []
        for edge_id, edge in self.edges.items():
            if edge.source_id == node_id or edge.target_id == node_id:
                edges_to_remove.append(edge_id)
        
        for edge_id in edges_to_remove:
            del self.edges[edge_id]
        
        # Remove from nodes dictionary
        del self.nodes[node_id]
        
        # Remove from NetworkX graph
        self.graph.remove_node(node_id)
        
        return True
    
    def remove_edge(self, edge_id: str) -> bool:
        """
        Remove an edge from the reasoning graph.
        
        Args:
            edge_id: ID of the edge to remove
            
        Returns:
            True if the edge was removed, False otherwise
        """
        if edge_id not in self.edges:
            return False
        
        # Get the edge
        edge = self.edges[edge_id]
        
        # Remove from edges dictionary
        del self.edges[edge_id]
        
        # Remove from NetworkX graph
        self.graph.remove_edge(edge.source_id, edge.target_id)
        
        # If bidirectional, remove the reverse edge
        if edge.bidirectional:
            reverse_edge_id = edge.get_reverse_edge_id()
            if reverse_edge_id in self.edges:
                del self.edges[reverse_edge_id]
                self.graph.remove_edge(edge.target_id, edge.source_id)
        
        return True
    
    def get_node(self, node_id: str) -> Optional[GraphNode]:
        """
        Get a node from the reasoning graph.
        
        Args:
            node_id: ID of the node to get
            
        Returns:
            The node, or None if not found
        """
        return self.nodes.get(node_id)
    
    def get_edge(self, source_id: str, target_id: str) -> Optional[GraphEdge]:
        """
        Get an edge from the reasoning graph.
        
        Args:
            source_id: ID of the source node
            target_id: ID of the target node
            
        Returns:
            The edge, or None if not found
        """
        edge_id = f"{source_id}__{target_id}"
        return self.edges.get(edge_id)
    
    def get_nodes_by_type(self, node_type: NodeType) -> List[GraphNode]:
        """
        Get all nodes of a specific type.
        
        Args:
            node_type: Type of nodes to get
            
        Returns:
            List of nodes of the specified type
        """
        return [node for node in self.nodes.values() if node.node_type == node_type]
    
    def get_edges_by_type(self, edge_type: EdgeType) -> List[GraphEdge]:
        """
        Get all edges of a specific type.
        
        Args:
            edge_type: Type of edges to get
            
        Returns:
            List of edges of the specified type
        """
        return [edge for edge in self.edges.values() if edge.edge_type == edge_type]
    
    def get_connected_nodes(self, node_id: str, direction: str = 'outgoing') -> List[str]:
        """
        Get IDs of nodes connected to a given node.
        
        Args:
            node_id: ID of the node
            direction: 'outgoing', 'incoming', or 'both'
            
        Returns:
            List of connected node IDs
        """
        if node_id not in self.nodes:
            return []
        
        if direction == 'outgoing':
            return list(self.graph.successors(node_id))
        elif direction == 'incoming':
            return list(self.graph.predecessors(node_id))
        elif direction == 'both':
            return list(set(self.graph.successors(node_id)) | set(self.graph.predecessors(node_id)))
        else:
            raise ValueError(f"Invalid direction: {direction}")
    
    def to_networkx(self) -> nx.DiGraph:
        """
        Convert the reasoning graph to a NetworkX DiGraph.
        
        Returns:
            NetworkX DiGraph representation
        """
        # The NetworkX graph is maintained throughout, so just return it
        return self.graph
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the reasoning graph to a dictionary representation.
        
        Returns:
            Dictionary representation of the graph
        """
        return {
            "nodes": {node_id: node.to_dict() for node_id, node in self.nodes.items()},
            "edges": {edge_id: edge.to_dict() for edge_id, edge in self.edges.items()},
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ReasoningGraph':
        """
        Create a ReasoningGraph from a dictionary representation.
        
        Args:
            data: Dictionary representation of the graph
            
        Returns:
            ReasoningGraph instance
        """
        graph = cls()
        
        # Load nodes
        for node_id, node_data in data["nodes"].items():
            node = GraphNode.from_dict(node_data)
            graph.nodes[node_id] = node
            
            # Add to NetworkX graph
            graph.graph.add_node(
                node_id, 
                content=node.content, 
                node_type=node.node_type.name,
                confidence=node.confidence,
                source=node.source
            )
        
        # Load edges
        for edge_id, edge_data in data["edges"].items():
            edge = GraphEdge.from_dict(edge_data)
            graph.edges[edge_id] = edge
            
            # Add to NetworkX graph
            graph.graph.add_edge(
                edge.source_id, 
                edge.target_id, 
                edge_type=edge.edge_type.name,
                weight=edge.weight,
                bidirectional=edge.bidirectional
            )
        
        # Set node counter to avoid ID conflicts
        node_ids = [node_id for node_id in graph.nodes.keys() if node_id.startswith("node_")]
        if node_ids:
            max_id = max([int(node_id.split("_")[1]) for node_id in node_ids if node_id.split("_")[1].isdigit()])
            graph.node_counter = max_id + 1
        
        # Load metadata
        graph.metadata = data.get("metadata", {})
        
        return graph
    
    def merge_with(self, other_graph: 'ReasoningGraph', prefix: str = '') -> None:
        """
        Merge another reasoning graph into this one.
        
        Args:
            other_graph: ReasoningGraph to merge
            prefix: Prefix to add to node IDs from the other graph to avoid conflicts
        """
        # Add nodes from other graph
        for node_id, node in other_graph.nodes.items():
            prefixed_id = f"{prefix}{node_id}"
            if prefixed_id not in self.nodes:
                self.add_node(
                    content=node.content,
                    node_type=node.node_type,
                    confidence=node.confidence,
                    source=node.source,
                    node_id=prefixed_id,
                    embedding=node.embedding
                )
        
        # Add edges from other graph
        for edge_id, edge in other_graph.edges.items():
            prefixed_source = f"{prefix}{edge.source_id}"
            prefixed_target = f"{prefix}{edge.target_id}"
            if (prefixed_source in self.nodes and 
                prefixed_target in self.nodes):
                self.add_edge(
                    source_id=prefixed_source,
                    target_id=prefixed_target,
                    edge_type=edge.edge_type,
                    weight=edge.weight,
                    bidirectional=edge.bidirectional
                )
    
    def copy(self) -> 'ReasoningGraph':
        """
        Create a deep copy of the reasoning graph.
        
        Returns:
            A deep copy of the reasoning graph
        """
        graph_dict = self.to_dict()
        return ReasoningGraph.from_dict(graph_dict)
    
    def message_passing(self, num_iterations: int = 3) -> None:
        """
        Perform message passing to propagate information through the graph.
        
        Args:
            num_iterations: Number of message passing iterations
        """
        for iteration in range(num_iterations):
            # Store messages for each node
            node_messages = defaultdict(list)
            
            # Generate messages from each edge
            for edge_id, edge in self.edges.items():
                source_node = self.nodes[edge.source_id]
                target_node = self.nodes[edge.target_id]
                
                # Generate message from source to target
                message = self._generate_message(source_node, target_node, edge)
                node_messages[edge.target_id].append(message)
                
                # If bidirectional, generate message from target to source
                if edge.bidirectional:
                    reverse_edge = edge.get_reverse_edge()
                    reverse_message = self._generate_message(target_node, source_node, reverse_edge)
                    node_messages[edge.source_id].append(reverse_message)
            
            # Update nodes with messages
            updates = {}
            for node_id, messages in node_messages.items():
                # Aggregate messages
                aggregated_message = self._aggregate_messages(messages)
                
                # Update node with aggregated message
                node = copy.deepcopy(self.nodes[node_id])
                node.update_with_message(aggregated_message)
                updates[node_id] = node
            
            # Apply updates
            for node_id, updated_node in updates.items():
                self.nodes[node_id] = updated_node
                
                # Update NetworkX graph
                self.graph.nodes[node_id]['confidence'] = updated_node.confidence
            
            # Log iteration summary
            avg_confidence = sum(node.confidence for node in self.nodes.values()) / len(self.nodes)
            self.message_history.append({
                "iteration": iteration,
                "avg_confidence": avg_confidence,
                "messages_passed": sum(len(messages) for messages in node_messages.values())
            })
    
    def _generate_message(self, source_node: GraphNode, target_node: GraphNode, 
                         edge: GraphEdge) -> Dict[str, Any]:
        """
        Generate a message from a source node to a target node.
        
        Args:
            source_node: Source node
            target_node: Target node
            edge: Edge connecting the nodes
            
        Returns:
            Message dictionary
        """
        # Base message components
        message = {
            "source_id": source_node.id,
            "confidence": source_node.confidence * edge.weight,
            "weight": edge.weight,
            "edge_type": edge.edge_type.name,
            "metadata": {
                "message_type": edge.edge_type.name,
                "source_type": source_node.node_type.name
            }
        }
        
        # Adjust message based on edge type
        if edge.edge_type == EdgeType.SUPPORTS:
            # Supporting evidence strengthens confidence
            message["confidence"] = min(1.0, source_node.confidence * edge.weight)
            message["weight"] = edge.weight
            
        elif edge.edge_type == EdgeType.CONTRADICTS:
            # Contradicting evidence reduces confidence
            message["confidence"] = max(0.0, 1.0 - source_node.confidence * edge.weight)
            message["weight"] = edge.weight
            
        elif edge.edge_type == EdgeType.IMPLIES:
            # Implications transfer confidence
            message["confidence"] = source_node.confidence * edge.weight
            message["weight"] = edge.weight
            
        elif edge.edge_type == EdgeType.FOLLOWS_FROM:
            # Logical consequences depend on the source confidence
            message["confidence"] = source_node.confidence * edge.weight
            message["weight"] = edge.weight
            
        else:
            # Default case
            message["confidence"] = source_node.confidence * edge.weight
            message["weight"] = 0.5 * edge.weight
        
        return message
    
    def _aggregate_messages(self, messages: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Aggregate multiple messages.
        
        Args:
            messages: List of messages to aggregate
            
        Returns:
            Aggregated message
        """
        if not messages:
            return {"confidence": 0.0, "weight": 0.0, "metadata": {}}
        
        # Separate messages by type for specialized aggregation
        supporting_messages = []
        contradicting_messages = []
        other_messages = []
        
        for message in messages:
            edge_type = message.get("edge_type")
            if edge_type == EdgeType.SUPPORTS.name:
                supporting_messages.append(message)
            elif edge_type == EdgeType.CONTRADICTS.name:
                contradicting_messages.append(message)
            else:
                other_messages.append(message)
        
        # Aggregate supporting messages (using a combination rule)
        if supporting_messages:
            # Use noisy-OR for combining supporting evidence
            supporting_conf = 1.0 - np.prod([1.0 - msg["confidence"] for msg in supporting_messages])
        else:
            supporting_conf = 0.0
        
        # Aggregate contradicting messages
        if contradicting_messages:
            # Use maximum for contradicting evidence
            contradicting_conf = max([msg["confidence"] for msg in contradicting_messages])
        else:
            contradicting_conf = 0.0
        
        # Aggregate other messages (using weighted average)
        if other_messages:
            weighted_sum = sum(msg["confidence"] * msg["weight"] for msg in other_messages)
            total_weight = sum(msg["weight"] for msg in other_messages)
            other_conf = weighted_sum / total_weight if total_weight > 0 else 0.0
        else:
            other_conf = 0.0
        
        # Combine the different types of aggregated messages
        # Supporting evidence increases confidence, contradicting reduces it
        net_support = supporting_conf - contradicting_conf
        
        # Weighted combination of support/contradiction and other messages
        final_conf = 0.7 * (0.5 + net_support/2) + 0.3 * other_conf
        final_conf = max(0.0, min(1.0, final_conf))  # Clamp to [0, 1]
        
        # Determine overall message weight
        total_messages = len(messages)
        overall_weight = min(0.8, 0.3 + 0.1 * total_messages)  # Caps at 0.8
        
        # Aggregate metadata
        aggregated_metadata = {}
        for message in messages:
            if "metadata" in message:
                for key, value in message["metadata"].items():
                    if key in aggregated_metadata:
                        # Combine numbers by summation
                        if isinstance(value, (int, float)) and isinstance(aggregated_metadata[key], (int, float)):
                            aggregated_metadata[key] += value
                        # Combine lists by extension
                        elif isinstance(value, list) and isinstance(aggregated_metadata[key], list):
                            aggregated_metadata[key].extend(value)
                        # Otherwise just replace with the latest value
                        else:
                            aggregated_metadata[key] = value
                    else:
                        aggregated_metadata[key] = value
        
        # Add message statistics to metadata
        aggregated_metadata["num_messages"] = total_messages
        aggregated_metadata["num_supporting"] = len(supporting_messages)
        aggregated_metadata["num_contradicting"] = len(contradicting_messages)
        
        return {
            "confidence": final_conf,
            "weight": overall_weight,
            "metadata": aggregated_metadata
        }
    
    def find_strongest_conclusion(self) -> Optional[GraphNode]:
        """
        Find the node that represents the strongest conclusion.
        
        Returns:
            The strongest conclusion node, or None if no conclusions exist
        """
        # Get all conclusion nodes
        conclusion_nodes = self.get_nodes_by_type(NodeType.CONCLUSION)
        
        if not conclusion_nodes:
            # If no explicit conclusions, look for nodes with no outgoing edges
            terminal_nodes = []
            for node_id, node in self.nodes.items():
                if not list(self.graph.successors(node_id)):
                    terminal_nodes.append(node)
            
            if not terminal_nodes:
                return None
                
            # Find the terminal node with highest confidence * support
            return max(terminal_nodes, key=lambda n: n.confidence * self._calculate_node_support(n.id))
        
        # Find the conclusion with highest confidence * support
        return max(conclusion_nodes, key=lambda n: n.confidence * self._calculate_node_support(n.id))
    
    def _calculate_node_support(self, node_id: str) -> float:
        """
        Calculate the support for a node based on its incoming connections.
        
        Args:
            node_id: ID of the node
            
        Returns:
            Support score (0-1)
        """
        # Get incoming connections
        incoming_edges = []
        for edge_id, edge in self.edges.items():
            if edge.target_id == node_id:
                incoming_edges.append(edge)
        
        if not incoming_edges:
            return 0.5  # Default for nodes without support
        
        # Calculate support based on incoming edges
        supporting_score = 0.0
        contradicting_score = 0.0
        
        for edge in incoming_edges:
            source_node = self.nodes[edge.source_id]
            source_confidence = source_node.confidence
            
            if edge.edge_type == EdgeType.SUPPORTS:
                supporting_score += source_confidence * edge.weight
            elif edge.edge_type == EdgeType.CONTRADICTS:
                contradicting_score += source_confidence * edge.weight
            elif edge.edge_type in [EdgeType.IMPLIES, EdgeType.FOLLOWS_FROM]:
                supporting_score += source_confidence * edge.weight * 0.8
            elif edge.edge_type == EdgeType.DERIVED_FROM:
                supporting_score += source_confidence * edge.weight * 0.7
            elif edge.edge_type == EdgeType.DEPENDS_ON:
                supporting_score += source_confidence * edge.weight * 0.6
        
        # Normalize scores
        total_edges = len(incoming_edges)
        supporting_score = supporting_score / total_edges if total_edges > 0 else 0.0
        contradicting_score = contradicting_score / total_edges if total_edges > 0 else 0.0
        
        # Calculate net support
        net_support = supporting_score - contradicting_score
        
        # Scale to [0, 1]
        return max(0.0, min(1.0, 0.5 + net_support))
    
    def extract_reasoning_path(self, conclusion_id: Optional[str] = None) -> List[str]:
        """
        Extract a linear reasoning path leading to a conclusion.
        
        Args:
            conclusion_id: ID of the conclusion node (uses strongest conclusion if None)
            
        Returns:
            List of node contents representing the reasoning path
        """
        # Find conclusion node if not specified
        if conclusion_id is None:
            conclusion_node = self.find_strongest_conclusion()
            if conclusion_node is None:
                return []
            conclusion_id = conclusion_node.id
        elif conclusion_id not in self.nodes:
            return []
        
        # Find highest-confidence path to conclusion
        paths = self.find_paths_to_node(conclusion_id)
        
        if not paths:
            return [self.nodes[conclusion_id].content]
        
        # Select the highest-confidence path
        best_path = max(paths, key=lambda p: self._calculate_path_confidence(p))
        
        # Extract node contents
        return [self.nodes[node_id].content for node_id in best_path]
    
    def find_paths_to_node(self, target_id: str, max_depth: int = 10) -> List[List[str]]:
        """
        Find all paths leading to a target node.
        
        Args:
            target_id: ID of the target node
            max_depth: Maximum path depth
            
        Returns:
            List of paths (each a list of node IDs)
        """
        paths = []
        visited = set()
        
        def dfs(current_id: str, path: List[str], depth: int):
            if depth >= max_depth:
                return
                
            if current_id in path:  # Avoid cycles
                return
                
            # Add current node to path
            new_path = path + [current_id]
            
            # If we've reached a source node (no incoming edges)
            if not list(self.graph.predecessors(current_id)):
                paths.append(new_path)
                return
                
            # Continue DFS
            for pred_id in self.graph.predecessors(current_id):
                if (current_id, pred_id) not in visited:
                    visited.add((current_id, pred_id))
                    dfs(pred_id, new_path, depth + 1)
        
        # Start DFS from the target node
        dfs(target_id, [], 0)
        
        # Reverse paths to start from source and end at target
        return [list(reversed(path)) for path in paths]
    
    def _calculate_path_confidence(self, path: List[str]) -> float:
        """
        Calculate the confidence of a reasoning path.
        
        Args:
            path: List of node IDs representing a path
            
        Returns:
            Confidence score (0-1)
        """
        if not path:
            return 0.0
        
        # Calculate node confidences
        node_confidences = [self.nodes[node_id].confidence for node_id in path]
        
        # Calculate edge weights along the path
        edge_weights = []
        for i in range(len(path) - 1):
            source_id = path[i]
            target_id = path[i + 1]
            edge = self.get_edge(source_id, target_id)
            if edge:
                edge_weights.append(edge.weight)
            else:
                edge_weights.append(0.5)  # Default for missing edges
        
        # Calculate path confidence as a combination of node confidences and edge weights
        if not edge_weights:
            return node_confidences[0]  # Single node path
            
        # Combine node confidences and edge weights
        # Using a recurrence relation: conf[i+1] = conf[i] * edge_weight[i] * node_conf[i+1]
        path_conf = node_confidences[0]
        for i in range(len(edge_weights)):
            path_conf = path_conf * edge_weights[i] * node_confidences[i + 1]
        
        # Apply a length penalty for very long paths
        length_penalty = max(0.0, 1.0 - 0.05 * max(0, len(path) - 5))
        
        return path_conf * length_penalty
    
    def extract_justifications(self, conclusion_id: Optional[str] = None) -> Dict[str, List[str]]:
        """
        Extract justifications for a conclusion.
        
        Args:
            conclusion_id: ID of the conclusion node (uses strongest conclusion if None)
            
        Returns:
            Dictionary mapping justification types to lists of justifications
        """
        # Find conclusion node if not specified
        if conclusion_id is None:
            conclusion_node = self.find_strongest_conclusion()
            if conclusion_node is None:
                return {}
            conclusion_id = conclusion_node.id
        elif conclusion_id not in self.nodes:
            return {}
        
        # Extract incoming edges to the conclusion
        justifications = defaultdict(list)
        
        # Direct support
        for edge_id, edge in self.edges.items():
            if edge.target_id == conclusion_id:
                source_node = self.nodes[edge.source_id]
                
                if edge.edge_type == EdgeType.SUPPORTS:
                    justifications["supporting_evidence"].append(source_node.content)
                elif edge.edge_type == EdgeType.IMPLIES:
                    justifications["logical_implications"].append(source_node.content)
                elif edge.edge_type == EdgeType.FOLLOWS_FROM:
                    justifications["logical_consequences"].append(source_node.content)
                elif edge.edge_type == EdgeType.DERIVED_FROM:
                    justifications["derived_from"].append(source_node.content)
                elif edge.edge_type == EdgeType.CONTRADICTS:
                    justifications["counterarguments"].append(source_node.content)
                else:
                    justifications["related_concepts"].append(source_node.content)
        
        # Indirect support (second-level connections)
        for edge_id, edge in self.edges.items():
            if edge.target_id == conclusion_id and edge.edge_type in [EdgeType.SUPPORTS, EdgeType.IMPLIES]:
                source_id = edge.source_id
                
                # Find nodes supporting this source
                for edge2_id, edge2 in self.edges.items():
                    if edge2.target_id == source_id and edge2.edge_type == EdgeType.SUPPORTS:
                        source2_node = self.nodes[edge2.source_id]
                        justifications["indirect_support"].append(source2_node.content)
        
        return dict(justifications)
    
    def calculate_conclusion_confidence(self, conclusion_id: Optional[str] = None) -> float:
        """
        Calculate the confidence in a conclusion based on supporting and contradicting evidence.
        
        Args:
            conclusion_id: ID of the conclusion node (uses strongest conclusion if None)
            
        Returns:
            Confidence score (0-1)
        """
        # Find conclusion node if not specified
        if conclusion_id is None:
            conclusion_node = self.find_strongest_conclusion()
            if conclusion_node is None:
                return 0.0
            conclusion_id = conclusion_node.id
        elif conclusion_id not in self.nodes:
            return 0.0
        
        # Get the conclusion node
        conclusion_node = self.nodes[conclusion_id]
        
        # Base confidence from the node itself
        base_confidence = conclusion_node.confidence
        
        # Support score from incoming connections
        support_score = self._calculate_node_support(conclusion_id)
        
        # Combine base confidence and support using a weighted average
        combined_confidence = 0.4 * base_confidence + 0.6 * support_score
        
        return combined_confidence
    
    def find_alternative_conclusions(self, k: int = 3) -> List[Tuple[GraphNode, float]]:
        """
        Find alternative conclusions and their confidence scores.
        
        Args:
            k: Maximum number of alternative conclusions to find
            
        Returns:
            List of (conclusion_node, confidence) tuples
        """
        # Get all conclusion nodes
        conclusion_nodes = self.get_nodes_by_type(NodeType.CONCLUSION)
        
        if not conclusion_nodes:
            # If no explicit conclusions, look for nodes with no outgoing edges
            terminal_nodes = []
            for node_id, node in self.nodes.items():
                if not list(self.graph.successors(node_id)):
                    terminal_nodes.append(node)
            
            conclusion_nodes = terminal_nodes
            
        if not conclusion_nodes:
            return []
        
        # Calculate confidence for each conclusion
        conclusions_with_confidence = [
            (node, self.calculate_conclusion_confidence(node.id))
            for node in conclusion_nodes
        ]
        
        # Sort by confidence
        conclusions_with_confidence.sort(key=lambda x: x[1], reverse=True)
        
        # Return top-k conclusions
        return conclusions_with_confidence[:k]
    
    def evaluate_graph_structure(self) -> Dict[str, float]:
        """
        Evaluate the structure of the reasoning graph.
        
        Returns:
            Dictionary of structural metrics
        """
        # Convert to NetworkX graph
        G = self.to_networkx()
        
        metrics = {}
        
        # Number of nodes and edges
        metrics["num_nodes"] = G.number_of_nodes()
        metrics["num_edges"] = G.number_of_edges()
        
        # Graph density
        metrics["density"] = nx.density(G)
        
        # Check if the graph is connected
        try:
            metrics["is_weakly_connected"] = nx.is_weakly_connected(G)
        except:
            metrics["is_weakly_connected"] = False
        
        # Check if the graph is acyclic
        try:
            metrics["is_dag"] = nx.is_directed_acyclic_graph(G)
        except:
            metrics["is_dag"] = False
        
        # Average clustering coefficient
        try:
            metrics["avg_clustering"] = nx.average_clustering(G.to_undirected())
        except:
            metrics["avg_clustering"] = 0.0
        
        # Average path length
        try:
            metrics["avg_path_length"] = nx.average_shortest_path_length(G)
        except:
            metrics["avg_path_length"] = 0.0
        
        # Diameter
        try:
            metrics["diameter"] = nx.diameter(G.to_undirected())
        except:
            metrics["diameter"] = 0.0
        
        # Centrality measures for nodes
        try:
            degree_centrality = nx.degree_centrality(G)
            metrics["max_degree_centrality"] = max(degree_centrality.values()) if degree_centrality else 0.0
            metrics["avg_degree_centrality"] = sum(degree_centrality.values()) / len(degree_centrality) if degree_centrality else 0.0
        except:
            metrics["max_degree_centrality"] = 0.0
            metrics["avg_degree_centrality"] = 0.0
        
        # Overall structural score
        structural_score = 0.0
        weights = {
            "is_weakly_connected": 0.3,
            "is_dag": 0.2,
            "density": -0.2  # Penalize overly dense graphs
        }
        
        for metric, weight in weights.items():
            if metric in metrics:
                value = metrics[metric]
                if metric == "density":
                    # Optimal density is around 0.2-0.3
                    value = 1.0 - abs(value - 0.25) * 2
                    value = max(0.0, min(1.0, value))
                structural_score += value * weight
        
        # Normalize the score
        metrics["structural_score"] = max(0.0, min(1.0, structural_score + 0.5))
        
        return metrics
    
    def calculate_graph_consistency(self) -> float:
        """
        Calculate the logical consistency of the reasoning graph.
        
        Returns:
            Consistency score (0-1)
        """
        # Check for explicit contradictions
        contradictions = 0
        nodes_checked = 0
        
        for node_id, node in self.nodes.items():
            # Get all nodes that contradict this node
            contradictors = []
            for edge_id, edge in self.edges.items():
                if edge.target_id == node_id and edge.edge_type == EdgeType.CONTRADICTS:
                    contradictors.append(edge.source_id)
                elif edge.source_id == node_id and edge.edge_type == EdgeType.CONTRADICTS:
                    contradictors.append(edge.target_id)
            
            if contradictors:
                # Calculate contradiction severity based on confidence of contradicting nodes
                total_contradiction = sum(self.nodes[contra_id].confidence for contra_id in contradictors)
                normalized_contradiction = min(1.0, total_contradiction / len(contradictors))
                contradictions += normalized_contradiction
                nodes_checked += 1
        
        # Calculate contradiction ratio
        if nodes_checked > 0:
            contradiction_ratio = contradictions / nodes_checked
        else:
            contradiction_ratio = 0.0
        
        # Higher contradiction ratio means lower consistency
        contradiction_factor = 1.0 - 0.8 * contradiction_ratio
        
        # Check for cycles (can indicate circular reasoning)
        G = self.to_networkx()
        
        try:
            is_dag = nx.is_directed_acyclic_graph(G)
            cycle_penalty = 0.0 if is_dag else 0.2
        except:
            cycle_penalty = 0.1  # Default penalty if check fails
        
        # Calculate final consistency score
        consistency_score = contradiction_factor - cycle_penalty
        
        return max(0.0, min(1.0, consistency_score))
    
    def visualize(self, output_path: Optional[str] = None, 
                 highlight_node: Optional[str] = None) -> None:
        """
        Visualize the reasoning graph.
        
        Args:
            output_path: Path to save the visualization (if None, display instead)
            highlight_node: ID of a node to highlight
        """
        try:
            # Convert to NetworkX graph
            G = self.to_networkx()
            
            # Create figure
            plt.figure(figsize=(12, 10))
            
            # Define node colors based on type
            node_colors = {
                NodeType.OBSERVATION.name: 'skyblue',
                NodeType.HYPOTHESIS.name: 'lightgreen',
                NodeType.EVIDENCE.name: 'lightblue',
                NodeType.INFERENCE.name: 'gold',
                NodeType.CALCULATION.name: 'lightgray',
                NodeType.CONSTRAINT.name: 'pink',
                NodeType.COUNTERPOINT.name: 'salmon',
                NodeType.DEFINITION.name: 'beige',
                NodeType.PRINCIPLE.name: 'lavender',
                NodeType.ANALOGY.name: 'lightcyan',
                NodeType.EXAMPLE.name: 'wheat',
                NodeType.CONCLUSION.name: 'lightcoral'
            }
            
            # Get node colors
            colors = []
            for node in G.nodes():
                node_type = G.nodes[node].get('node_type', 'OBSERVATION')
                if highlight_node and node == highlight_node:
                    colors.append('red')  # Highlight the specified node
                else:
                    colors.append(node_colors.get(node_type, 'lightgray'))
            
            # Get edge colors
            edge_colors = []
            for u, v in G.edges():
                edge_type = G.edges[u, v].get('edge_type', 'RELATED_TO')
                if edge_type == EdgeType.SUPPORTS.name:
                    edge_colors.append('green')
                elif edge_type == EdgeType.CONTRADICTS.name:
                    edge_colors.append('red')
                elif edge_type == EdgeType.IMPLIES.name:
                    edge_colors.append('blue')
                else:
                    edge_colors.append('gray')
            
            # Get node sizes based on confidence
            node_sizes = []
            for node in G.nodes():
                confidence = G.nodes[node].get('confidence', 0.5)
                node_sizes.append(300 + 700 * confidence)  # Scale size by confidence
            
            # Use a hierarchical layout
            try:
                pos = nx.nx_agraph.graphviz_layout(G, prog='dot')
            except:
                # Fall back to spring layout if graphviz is not available
                pos = nx.spring_layout(G, seed=42)
            
            # Draw the graph
            nx.draw_networkx_nodes(G, pos, node_color=colors, node_size=node_sizes, alpha=0.8)
            nx.draw_networkx_edges(G, pos, edge_color=edge_colors, width=1.5, alpha=0.7, 
                                  arrowsize=15, connectionstyle='arc3,rad=0.1')
            
            # Add labels
            labels = {}
            for node in G.nodes():
                content = G.nodes[node].get('content', '')
                # Truncate long content
                if len(content) > 30:
                    content = content[:27] + "..."
                labels[node] = content
            
            nx.draw_networkx_labels(G, pos, labels=labels, font_size=8)
            
            # Add a legend for node types
            legend_elements = []
            import matplotlib.patches as mpatches
            for node_type, color in node_colors.items():
                legend_elements.append(mpatches.Patch(color=color, label=node_type))
            
            plt.legend(handles=legend_elements, loc='upper right')
            
            # Set title
            plt.title("Reasoning Graph")
            
            # Remove axis
            plt.axis('off')
            
            # Save or display the graph
            if output_path:
                plt.savefig(output_path, bbox_inches='tight', dpi=300)
                plt.close()
            else:
                plt.tight_layout()
                plt.show()
                
        except Exception as e:
            logger.error(f"Error visualizing graph: {e}")

class ReasoningGraphs:
    """
    Main class for the Reasoning Graphs component of the ULTRA Meta-Cognitive System.
    
    This class provides methods for building, analyzing, and utilizing graph-structured
    reasoning for complex problem-solving and inference.
    """
    
    def __init__(
        self, 
        language_model=None,
        neuro_symbolic_bridge: Optional[NeuroSymbolicBridge] = None,
        knowledge_base: Optional[EpisodicKnowledgeBase] = None,
        config: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize the Reasoning Graphs component.
        
        Args:
            language_model: Optional language model for text generation
            neuro_symbolic_bridge: Optional neuro-symbolic bridge for logical reasoning
            knowledge_base: Optional knowledge base for retrieving information
            config: Optional configuration dictionary
        """
        self.language_model = language_model
        self.neuro_symbolic_bridge = neuro_symbolic_bridge
        self.knowledge_base = knowledge_base
        self.config = config or {}
        
        # Initialize TFIDF vectorizer for text similarity
        self.vectorizer = None
        
        # Register handlers for critique and bias detection
        self.critique_handler = None
        self.bias_handler = None
        
        # Default message passing iterations for belief propagation
        self.message_passing_iterations = self.config.get("message_passing_iterations", 3)
        
        # Logger
        self.logger = get_logger(__name__)
    
    def register_critique_handler(self, handler: Callable) -> None:
        """
        Register a handler for critiquing reasoning.
        
        Args:
            handler: Function that takes a reasoning text and returns a critique
        """
        self.critique_handler = handler
    
    def register_bias_handler(self, handler: Callable) -> None:
        """
        Register a handler for detecting biases in reasoning.
        
        Args:
            handler: Function that takes a reasoning text and returns detected biases
        """
        self.bias_handler = handler
    
    def build_reasoning_graph(
        self, 
        problem_statement: str,
        context: Optional[Dict[str, Any]] = None
    ) -> ReasoningGraph:
        """
        Build a reasoning graph for a problem statement.
        
        Args:
            problem_statement: The problem to reason about
            context: Optional context information
            
        Returns:
            A ReasoningGraph instance
        """
        # Initialize a new reasoning graph
        graph = ReasoningGraph()
        
        # Initialize the vectorizer for text similarity
        if self.vectorizer is None:
            from sklearn.feature_extraction.text import TfidfVectorizer
            self.vectorizer = TfidfVectorizer(max_features=5000)
        
        # Use language model to build the graph if available
        if self.language_model:
            graph = self._build_with_language_model(problem_statement, context, graph)
        else:
            # Fall back to rule-based graph construction
            graph = self._build_with_rules(problem_statement, context, graph)
        
        # Apply belief propagation through message passing
        graph.message_passing(num_iterations=self.message_passing_iterations)
        
        # Evaluate graph structure
        structure_metrics = graph.evaluate_graph_structure()
        graph.metadata["structure_metrics"] = structure_metrics
        
        # Calculate graph consistency
        consistency = graph.calculate_graph_consistency()
        graph.metadata["consistency"] = consistency
        
        # Apply critique if available
        if self.critique_handler:
            strongest_conclusion = graph.find_strongest_conclusion()
            if strongest_conclusion:
                reasoning_path = graph.extract_reasoning_path(strongest_conclusion.id)
                reasoning_text = "\n".join(reasoning_path)
                critique = self.critique_handler(reasoning_text)
                graph.metadata["critique"] = critique
        
        # Apply bias detection if available
        if self.bias_handler:
            strongest_conclusion = graph.find_strongest_conclusion()
            if strongest_conclusion:
                reasoning_path = graph.extract_reasoning_path(strongest_conclusion.id)
                reasoning_text = "\n".join(reasoning_path)
                biases = self.bias_handler(reasoning_text)
                graph.metadata["detected_biases"] = biases
        
        return graph
    
    def _build_with_language_model(
        self, 
        problem_statement: str,
        context: Optional[Dict[str, Any]],
        graph: ReasoningGraph
    ) -> ReasoningGraph:
        """
        Build a reasoning graph using a language model.
        
        Args:
            problem_statement: The problem to reason about
            context: Optional context information
            graph: ReasoningGraph to build upon
            
        Returns:
            The constructed ReasoningGraph
        """
        # Add the problem statement as an observation node
        problem_node_id = graph.add_node(
            content=problem_statement,
            node_type=NodeType.OBSERVATION,
            confidence=1.0,
            source="problem_statement"
        )
        
        # Extract key observations from the problem statement
        observations = self._extract_observations(problem_statement)
        
        # Add observations as nodes
        observation_node_ids = []
        for observation in observations:
            obs_node_id = graph.add_node(
                content=observation,
                node_type=NodeType.OBSERVATION,
                confidence=0.9,
                source="extracted_from_problem"
            )
            observation_node_ids.append(obs_node_id)
            
            # Connect observation to problem node
            graph.add_edge(
                source_id=problem_node_id,
                target_id=obs_node_id,
                edge_type=EdgeType.DERIVED_FROM,
                weight=0.9
            )
        
        # Generate hypotheses from observations
        hypothesis_node_ids = []
        for obs_node_id in observation_node_ids:
            observation = graph.get_node(obs_node_id).content
            hypotheses = self._generate_hypotheses(observation)
            
            for hypothesis in hypotheses:
                hyp_node_id = graph.add_node(
                    content=hypothesis,
                    node_type=NodeType.HYPOTHESIS,
                    confidence=0.7,
                    source="generated_from_observation"
                )
                hypothesis_node_ids.append(hyp_node_id)
                
                # Connect hypothesis to observation
                graph.add_edge(
                    source_id=obs_node_id,
                    target_id=hyp_node_id,
                    edge_type=EdgeType.IMPLIES,
                    weight=0.7
                )
        
        # Generate constraints and principles
        principles = self._extract_principles(problem_statement)
        principle_node_ids = []
        
        for principle in principles:
            prin_node_id = graph.add_node(
                content=principle,
                node_type=NodeType.PRINCIPLE,
                confidence=0.8,
                source="extracted_principle"
            )
            principle_node_ids.append(prin_node_id)
            
            # Connect principle to problem node
            graph.add_edge(
                source_id=problem_node_id,
                target_id=prin_node_id,
                edge_type=EdgeType.RELATED_TO,
                weight=0.8
            )
        
        # Generate inferences from hypotheses
        inference_node_ids = []
        for hyp_node_id in hypothesis_node_ids:
            hypothesis = graph.get_node(hyp_node_id).content
            inferences = self._generate_inferences(hypothesis)
            
            for inference in inferences:
                inf_node_id = graph.add_node(
                    content=inference,
                    node_type=NodeType.INFERENCE,
                    confidence=0.6,
                    source="generated_from_hypothesis"
                )
                inference_node_ids.append(inf_node_id)
                
                # Connect inference to hypothesis
                graph.add_edge(
                    source_id=hyp_node_id,
                    target_id=inf_node_id,
                    edge_type=EdgeType.FOLLOWS_FROM,
                    weight=0.8
                )
                
                # Check if this inference contradicts any principles
                for prin_node_id in principle_node_ids:
                    principle = graph.get_node(prin_node_id).content
                    if self._is_contradiction(inference, principle):
                        # Add contradicts edge
                        graph.add_edge(
                            source_id=prin_node_id,
                            target_id=inf_node_id,
                            edge_type=EdgeType.CONTRADICTS,
                            weight=0.7
                        )
        
        # Generate conclusions from inferences
        conclusion_candidates = []
        
        # Group related inferences
        inference_groups = self._group_related_nodes(
            [graph.get_node(node_id) for node_id in inference_node_ids]
        )
        
        for group in inference_groups:
            group_node_ids = [node.id for node in group]
            
            # Generate a conclusion from this group of inferences
            if len(group) >= 2:
                group_texts = [node.content for node in group]
                conclusion = self._generate_conclusion(group_texts)
                
                # Add conclusion node
                concl_node_id = graph.add_node(
                    content=conclusion,
                    node_type=NodeType.CONCLUSION,
                    confidence=0.7,
                    source="generated_from_inferences"
                )
                conclusion_candidates.append((concl_node_id, len(group)))
                
                # Connect inferences to conclusion
                for inf_node_id in group_node_ids:
                    graph.add_edge(
                        source_id=inf_node_id,
                        target_id=concl_node_id,
                        edge_type=EdgeType.SUPPORTS,
                        weight=0.8
                    )
        
        # If we have multiple conclusion candidates, add relationships between them
        if len(conclusion_candidates) > 1:
            # Sort by group size (descending)
            conclusion_candidates.sort(key=lambda x: x[1], reverse=True)
            
            # Primary conclusion is the one supported by the most inferences
            primary_conclusion_id = conclusion_candidates[0][0]
            
            for concl_node_id, _ in conclusion_candidates[1:]:
                # Determine relationship between conclusions
                primary_content = graph.get_node(primary_conclusion_id).content
                secondary_content = graph.get_node(concl_node_id).content
                
                relationship = self._determine_conclusion_relationship(primary_content, secondary_content)
                
                if relationship == "supports":
                    graph.add_edge(
                        source_id=concl_node_id,
                        target_id=primary_conclusion_id,
                        edge_type=EdgeType.SUPPORTS,
                        weight=0.7
                    )
                elif relationship == "contradicts":
                    graph.add_edge(
                        source_id=concl_node_id,
                        target_id=primary_conclusion_id,
                        edge_type=EdgeType.CONTRADICTS,
                        weight=0.7
                    )
                elif relationship == "elaborates":
                    graph.add_edge(
                        source_id=concl_node_id,
                        target_id=primary_conclusion_id,
                        edge_type=EdgeType.ELABORATES,
                        weight=0.8
                    )
                else:  # "related"
                    graph.add_edge(
                        source_id=concl_node_id,
                        target_id=primary_conclusion_id,
                        edge_type=EdgeType.RELATED_TO,
                        weight=0.6
                    )
        
        # Generate counterarguments for conclusions
        for concl_node_id, _ in conclusion_candidates:
            conclusion = graph.get_node(concl_node_id).content
            counterarguments = self._generate_counterarguments(conclusion)
            
            for counterarg in counterarguments:
                counter_node_id = graph.add_node(
                    content=counterarg,
                    node_type=NodeType.COUNTERPOINT,
                    confidence=0.6,
                    source="generated_counterargument"
                )
                
                # Connect counterargument to conclusion
                graph.add_edge(
                    source_id=counter_node_id,
                    target_id=concl_node_id,
                    edge_type=EdgeType.CONTRADICTS,
                    weight=0.7
                )
        
        return graph
    
    def _build_with_rules(
        self, 
        problem_statement: str,
        context: Optional[Dict[str, Any]],
        graph: ReasoningGraph
    ) -> ReasoningGraph:
        """
        Build a reasoning graph using rule-based methods (fallback when no language model is available).
        
        Args:
            problem_statement: The problem to reason about
            context: Optional context information
            graph: ReasoningGraph to build upon
            
        Returns:
            The constructed ReasoningGraph
        """
        # Add the problem statement as an observation node
        problem_node_id = graph.add_node(
            content=problem_statement,
            node_type=NodeType.OBSERVATION,
            confidence=1.0,
            source="problem_statement"
        )
        
        # Extract sentences from the problem statement
        sentences = self._extract_sentences(problem_statement)
        
        # Add each sentence as an observation
        observation_node_ids = []
        for sentence in sentences:
            # Skip very short sentences
            if len(sentence.split()) < 3:
                continue
                
            obs_node_id = graph.add_node(
                content=sentence,
                node_type=NodeType.OBSERVATION,
                confidence=0.9,
                source="extracted_from_problem"
            )
            observation_node_ids.append(obs_node_id)
            
            # Connect observation to problem node
            graph.add_edge(
                source_id=problem_node_id,
                target_id=obs_node_id,
                edge_type=EdgeType.DERIVED_FROM,
                weight=0.9
            )
        
        # Identify potential hypotheses from questions or statements with certain keywords
        hypothesis_indicators = ["could", "might", "may", "possibly", "perhaps", "if", "assuming", "consider"]
        
        hypothesis_node_ids = []
        for obs_node_id in observation_node_ids:
            observation = graph.get_node(obs_node_id).content
            
            # Check if the observation contains a hypothesis indicator
            if any(indicator in observation.lower() for indicator in hypothesis_indicators):
                hyp_node_id = graph.add_node(
                    content=f"Hypothesis: {observation}",
                    node_type=NodeType.HYPOTHESIS,
                    confidence=0.7,
                    source="extracted_from_observation"
                )
                hypothesis_node_ids.append(hyp_node_id)
                
                # Connect hypothesis to observation
                graph.add_edge(
                    source_id=obs_node_id,
                    target_id=hyp_node_id,
                    edge_type=EdgeType.DERIVED_FROM,
                    weight=0.8
                )
        
        # Identify potential conclusions from statements with conclusion indicators
        conclusion_indicators = ["therefore", "thus", "hence", "consequently", "so", "concluding", "in conclusion"]
        
        conclusion_node_ids = []
        for obs_node_id in observation_node_ids:
            observation = graph.get_node(obs_node_id).content
            
            # Check if the observation contains a conclusion indicator
            if any(indicator in observation.lower() for indicator in conclusion_indicators):
                concl_node_id = graph.add_node(
                    content=f"Conclusion: {observation}",
                    node_type=NodeType.CONCLUSION,
                    confidence=0.8,
                    source="extracted_from_observation"
                )
                conclusion_node_ids.append(concl_node_id)
                
                # Connect conclusion to observation
                graph.add_edge(
                    source_id=obs_node_id,
                    target_id=concl_node_id,
                    edge_type=EdgeType.DERIVED_FROM,
                    weight=0.8
                )
        
        # If no explicit conclusions found, generate one from the observations
        if not conclusion_node_ids:
            # Create a simple conclusion from the problem statement
            conclusion = f"Based on the problem statement, a conclusion is needed for: {problem_statement}"
            
            concl_node_id = graph.add_node(
                content=conclusion,
                node_type=NodeType.CONCLUSION,
                confidence=0.6,
                source="generated_conclusion"
            )
            conclusion_node_ids.append(concl_node_id)
            
            # Connect observations to conclusion
            for obs_node_id in observation_node_ids:
                graph.add_edge(
                    source_id=obs_node_id,
                    target_id=concl_node_id,
                    edge_type=EdgeType.SUPPORTS,
                    weight=0.6
                )
        
        # Create connections between hypotheses and conclusions
        for hyp_node_id in hypothesis_node_ids:
            for concl_node_id in conclusion_node_ids:
                # For simplicity, assume hypotheses support conclusions in this basic version
                graph.add_edge(
                    source_id=hyp_node_id,
                    target_id=concl_node_id,
                    edge_type=EdgeType.SUPPORTS,
                    weight=0.5
                )
        
        return graph
    
    def _extract_observations(self, text: str) -> List[str]:
        """
        Extract key observations from text.
        
        Args:
            text: Text to extract observations from
            
        Returns:
            List of observations
        """
        if self.language_model:
            try:
                prompt = f"""
                Extract key observations from the following text. 
                An observation is a factual statement or piece of information presented in the text.
                Focus on clear, concrete facts or explicit statements.
                
                Text: {text}
                
                Key observations (provide 3-5 observations as a numbered list):
                """
                
                response = self.language_model.generate(prompt).strip()
                
                # Extract observations from the response
                observations = []
                for line in response.split('\n'):
                    # Look for numbered or bulleted lines
                    if re.match(r'^\d+[\.\)]|^-|^\*', line.strip()):
                        # Remove the numbering or bullet
                        observation = re.sub(r'^\d+[\.\)]|^-|^\*\s*', '', line).strip()
                        if observation:
                            observations.append(observation)
                
                if not observations:
                    # Fall back to using the entire response if parsing failed
                    observations = [line.strip() for line in response.split('\n') if line.strip()]
                
                return observations[:5]  # Limit to 5 observations
            except Exception as e:
                self.logger.warning(f"Error extracting observations with language model: {e}")
        
        # Fall back to sentence extraction
        return self._extract_sentences(text)[:5]
    
    def _extract_sentences(self, text: str) -> List[str]:
        """
        Extract sentences from text.
        
        Args:
            text: Text to extract sentences from
            
        Returns:
            List of sentences
        """
        # Simple sentence splitting
        sentences = re.split(r'(?<=[.!?])\s+', text)
        return [s.strip() for s in sentences if s.strip()]
    
    def _generate_hypotheses(self, observation: str) -> List[str]:
        """
        Generate hypotheses based on an observation.
        
        Args:
            observation: Observation to generate hypotheses from
            
        Returns:
            List of hypotheses
        """
        if self.language_model:
            try:
                prompt = f"""
                Generate potential hypotheses based on the following observation:
                
                Observation: {observation}
                
                Generate 2-3 hypotheses that could explain this observation or build upon it.
                Each hypothesis should be a single sentence, clearly stated, and logically related to the observation.
                
                Hypotheses:
                """
                
                response = self.language_model.generate(prompt).strip()
                
                # Extract hypotheses from the response
                hypotheses = []
                for line in response.split('\n'):
                    # Look for numbered or bulleted lines
                    if re.match(r'^\d+[\.\)]|^-|^\*', line.strip()):
                        # Remove the numbering or bullet
                        hypothesis = re.sub(r'^\d+[\.\)]|^-|^\*\s*', '', line).strip()
                        if hypothesis:
                            hypotheses.append(hypothesis)
                
                if not hypotheses:
                    # Fall back to using the entire response
                    hypotheses = [line.strip() for line in response.split('\n') if line.strip()]
                
                return hypotheses[:3]  # Limit to 3 hypotheses
            except Exception as e:
                self.logger.warning(f"Error generating hypotheses with language model: {e}")
        
        # Fall back to a default hypothesis
        return [f"It's possible that {observation.lower()}"]
    
    def _extract_principles(self, text: str) -> List[str]:
        """
        Extract principles or constraints from text.
        
        Args:
            text: Text to extract principles from
            
        Returns:
            List of principles
        """
        if self.language_model:
            try:
                prompt = f"""
                Extract key principles, rules, or constraints from the following text.
                Focus on identifying general principles, constraints, requirements, or rules that should be followed.
                
                Text: {text}
                
                Principles (provide 2-3 principles as a numbered list):
                """
                
                response = self.language_model.generate(prompt).strip()
                
                # Extract principles from the response
                principles = []
                for line in response.split('\n'):
                    # Look for numbered or bulleted lines
                    if re.match(r'^\d+[\.\)]|^-|^\*', line.strip()):
                        # Remove the numbering or bullet
                        principle = re.sub(r'^\d+[\.\)]|^-|^\*\s*', '', line).strip()
                        if principle:
                            principles.append(principle)
                
                if not principles:
                    # Fall back to using the entire response
                    principles = [line.strip() for line in response.split('\n') if line.strip()]
                
                return principles[:3]  # Limit to 3 principles
            except Exception as e:
                self.logger.warning(f"Error extracting principles with language model: {e}")
        
        # Fall back to looking for constraint keywords
        constraint_keywords = ["must", "should", "required", "necessary", "always", "never", "constraint"]
        sentences = self._extract_sentences(text)
        principles = []
        
        for sentence in sentences:
            if any(keyword in sentence.lower() for keyword in constraint_keywords):
                principles.append(sentence)
        
        return principles[:3]  # Limit to 3 principles
    
    def _generate_inferences(self, hypothesis: str) -> List[str]:
        """
        Generate inferences from a hypothesis.
        
        Args:
            hypothesis: Hypothesis to generate inferences from
            
        Returns:
            List of inferences
        """
        if self.language_model:
            try:
                prompt = f"""
                Generate logical inferences that follow from this hypothesis:
                
                Hypothesis: {hypothesis}
                
                Generate 2-3 inferences that would logically follow if this hypothesis were true.
                Each inference should be clearly stated in a single sentence.
                
                Inferences:
                """
                
                response = self.language_model.generate(prompt).strip()
                
                # Extract inferences from the response
                inferences = []
                for line in response.split('\n'):
                    # Look for numbered or bulleted lines
                    if re.match(r'^\d+[\.\)]|^-|^\*', line.strip()):
                        # Remove the numbering or bullet
                        inference = re.sub(r'^\d+[\.\)]|^-|^\*\s*', '', line).strip()
                        if inference:
                            inferences.append(inference)
                
                if not inferences:
                    # Fall back to using the entire response
                    inferences = [line.strip() for line in response.split('\n') if line.strip()]
                
                return inferences[:3]  # Limit to 3 inferences
            except Exception as e:
                self.logger.warning(f"Error generating inferences with language model: {e}")
        
        # Fall back to a default inference
        return [f"If {hypothesis.lower()}, then we would expect some consequences."]
    
    def _is_contradiction(self, text1: str, text2: str) -> bool:
        """
        Determine if two texts contradict each other.
        
        Args:
            text1: First text
            text2: Second text
            
        Returns:
            True if the texts contradict, False otherwise
        """
        if self.language_model:
            try:
                prompt = f"""
                Do the following two statements contradict each other? Answer with 'Yes' or 'No'.
                
                Statement 1: {text1}
                Statement 2: {text2}
                
                Do these statements contradict? (Yes/No)
                """
                
                response = self.language_model.generate(prompt).strip().lower()
                
                return "yes" in response
            except Exception as e:
                self.logger.warning(f"Error checking contradiction with language model: {e}")
        
        # Fall back to checking for negation keywords
        negation_keywords = ["not", "never", "no", "none", "cannot", "impossible", "opposite"]
        
        # Check if one statement contains a negation of the other
        text1_lower = text1.lower()
        text2_lower = text2.lower()
        
        for keyword in negation_keywords:
            if keyword in text1_lower and keyword not in text2_lower:
                return True
            if keyword in text2_lower and keyword not in text1_lower:
                return True
        
        return False
    
    def _group_related_nodes(self, nodes: List[GraphNode]) -> List[List[GraphNode]]:
        """
        Group related nodes together using text similarity.
        
        Args:
            nodes: List of nodes to group
            
        Returns:
            List of node groups
        """
        if not nodes:
            return []
        
        # Extract node contents
        texts = [node.content for node in nodes]
        
        # Transform texts to TF-IDF vectors
        try:
            vectorizer = self.vectorizer
            
            # Fit the vectorizer if not already fitted
            if not hasattr(vectorizer, 'vocabulary_'):
                vectorizer.fit(texts)
                
            tfidf_matrix = vectorizer.transform(texts)
            
            # Calculate pairwise similarities
            similarity_matrix = (tfidf_matrix * tfidf_matrix.T).toarray()
            
            # Set diagonal to 0 to avoid self-similarity
            np.fill_diagonal(similarity_matrix, 0)
            
            # Apply a simple clustering algorithm
            if len(nodes) <= 5:
                # For small number of nodes, use a simpler approach
                groups = []
                remaining = set(range(len(nodes)))
                
                while remaining:
                    # Start a new group with the next available node
                    idx = next(iter(remaining))
                    group = [idx]
                    remaining.remove(idx)
                    
                    # Find similar nodes to add to the group
                    for other_idx in list(remaining):
                        if similarity_matrix[idx, other_idx] > 0.3:  # Similarity threshold
                            group.append(other_idx)
                            remaining.remove(other_idx)
                    
                    groups.append([nodes[i] for i in group])
            else:
                # For larger sets, use K-means
                from sklearn.cluster import KMeans
                
                # Determine number of clusters (sqrt rule of thumb)
                n_clusters = min(int(math.sqrt(len(nodes))), len(nodes))
                
                # Apply K-means
                kmeans = KMeans(n_clusters=n_clusters, random_state=42)
                cluster_labels = kmeans.fit_predict(tfidf_matrix.toarray())
                
                # Group nodes by cluster
                groups = [[] for _ in range(n_clusters)]
                for i, label in enumerate(cluster_labels):
                    groups[label].append(nodes[i])
                
                # Remove empty groups
                groups = [g for g in groups if g]
            
            return groups
            
        except Exception as e:
            self.logger.warning(f"Error grouping nodes: {e}")
            
            # Fall back to treating each node as its own group
            return [[node] for node in nodes]
    
    def _generate_conclusion(self, texts: List[str]) -> str:
        """
        Generate a conclusion from multiple texts.
        
        Args:
            texts: List of texts to generate a conclusion from
            
        Returns:
            Generated conclusion
        """
        if self.language_model:
            try:
                combined_text = "\n".join([f"- {text}" for text in texts])
                
                prompt = f"""
                Based on the following information, generate a clear and concise conclusion.
                
                Information:
                {combined_text}
                
                Conclusion (in a single paragraph):
                """
                
                response = self.language_model.generate(prompt).strip()
                
                return response
            except Exception as e:
                self.logger.warning(f"Error generating conclusion with language model: {e}")
        
        # Fall back to a simple conclusion
        if texts:
            return f"Therefore, based on the evidence, it can be concluded that {texts[0].lower()}"
        else:
            return "No conclusion can be drawn due to insufficient information."
    
    def _determine_conclusion_relationship(self, conclusion1: str, conclusion2: str) -> str:
        """
        Determine the relationship between two conclusions.
        
        Args:
            conclusion1: First conclusion
            conclusion2: Second conclusion
            
        Returns:
            Relationship type: "supports", "contradicts", "elaborates", or "related"
        """
        if self.language_model:
            try:
                prompt = f"""
                Analyze the relationship between these two conclusions. 
                Choose the most accurate relationship type from: 'supports', 'contradicts', 'elaborates', or 'related'.
                
                Conclusion 1: {conclusion1}
                Conclusion 2: {conclusion2}
                
                Relationship type (supports/contradicts/elaborates/related):
                """
                
                response = self.language_model.generate(prompt).strip().lower()
                
                if "support" in response:
                    return "supports"
                elif "contradict" in response:
                    return "contradicts"
                elif "elaborate" in response:
                    return "elaborates"
                else:
                    return "related"
            except Exception as e:
                self.logger.warning(f"Error determining conclusion relationship with language model: {e}")
        
        # Fall back to checking for contradiction keywords
        if self._is_contradiction(conclusion1, conclusion2):
            return "contradicts"
        
        # Check for similarity
        if self.vectorizer:
            try:
                tfidf_matrix = self.vectorizer.transform([conclusion1, conclusion2])
                similarity_matrix = (tfidf_matrix * tfidf_matrix.T).toarray()
                similarity = similarity_matrix[0, 1]
                
                if similarity > 0.8:  # High similarity
                    return "elaborates"
                elif similarity > 0.5:  # Moderate similarity
                    return "supports"
                else:
                    return "related"
            except Exception as e:
                self.logger.warning(f"Error calculating similarity: {e}")
        
        return "related"  # Default relationship
    
    def _generate_counterarguments(self, conclusion: str) -> List[str]:
        """
        Generate counterarguments to a conclusion.
        
        Args:
            conclusion: Conclusion to generate counterarguments for
            
        Returns:
            List of counterarguments
        """
        if self.language_model:
            try:
                prompt = f"""
                Generate counterarguments to the following conclusion:
                
                Conclusion: {conclusion}
                
                Generate 1-2 strong counterarguments that challenge this conclusion.
                Each counterargument should be clearly stated in a single sentence.
                
                Counterarguments:
                """
                
                response = self.language_model.generate(prompt).strip()
                
                # Extract counterarguments from the response
                counterarguments = []
                for line in response.split('\n'):
                    # Look for numbered or bulleted lines
                    if re.match(r'^\d+[\.\)]|^-|^\*', line.strip()):
                        # Remove the numbering or bullet
                        counterarg = re.sub(r'^\d+[\.\)]|^-|^\*\s*', '', line).strip()
                        if counterarg:
                            counterarguments.append(counterarg)
                
                if not counterarguments:
                    # Fall back to using the entire response
                    counterarguments = [line.strip() for line in response.split('\n') if line.strip()]
                
                return counterarguments[:2]  # Limit to 2 counterarguments
            except Exception as e:
                self.logger.warning(f"Error generating counterarguments with language model: {e}")
        
        # Fall back to a default counterargument
        return [f"A potential counterargument is that the conclusion '{conclusion}' might not fully account for all factors."]
    
    def find_strongest_conclusion(self, graph: ReasoningGraph) -> Optional[str]:
        """
        Find the strongest conclusion in a reasoning graph.
        
        Args:
            graph: ReasoningGraph to analyze
            
        Returns:
            The text of the strongest conclusion, or None if no conclusions exist
        """
        # Find the strongest conclusion node
        conclusion_node = graph.find_strongest_conclusion()
        
        if conclusion_node:
            return conclusion_node.content
        
        return None
    
    def extract_reasoning_path(self, graph: ReasoningGraph, conclusion: Optional[str] = None) -> List[str]:
        """
        Extract a linear reasoning path from a reasoning graph.
        
        Args:
            graph: ReasoningGraph to extract path from
            conclusion: Optional conclusion to extract path for (uses strongest if None)
            
        Returns:
            List of reasoning steps
        """
        # Find conclusion node ID if a conclusion text is provided
        conclusion_id = None
        if conclusion:
            for node_id, node in graph.nodes.items():
                if node.content == conclusion:
                    conclusion_id = node_id
                    break
        
        # Extract the reasoning path
        return graph.extract_reasoning_path(conclusion_id)
    
    def extract_justifications(
        self, 
        graph: ReasoningGraph, 
        conclusion: Optional[str] = None
    ) -> Dict[str, List[str]]:
        """
        Extract justifications for a conclusion from a reasoning graph.
        
        Args:
            graph: ReasoningGraph to extract justifications from
            conclusion: Optional conclusion to extract justifications for (uses strongest if None)
            
        Returns:
            Dictionary mapping justification types to lists of justifications
        """
        # Find conclusion node ID if a conclusion text is provided
        conclusion_id = None
        if conclusion:
            for node_id, node in graph.nodes.items():
                if node.content == conclusion:
                    conclusion_id = node_id
                    break
        
        # Extract the justifications
        return graph.extract_justifications(conclusion_id)
    
    def calculate_conclusion_confidence(
        self, 
        graph: ReasoningGraph, 
        conclusion: Optional[str] = None
    ) -> float:
        """
        Calculate the confidence in a conclusion from a reasoning graph.
        
        Args:
            graph: ReasoningGraph to calculate confidence from
            conclusion: Optional conclusion to calculate confidence for (uses strongest if None)
            
        Returns:
            Confidence score (0-1)
        """
        # Find conclusion node ID if a conclusion text is provided
        conclusion_id = None
        if conclusion:
            for node_id, node in graph.nodes.items():
                if node.content == conclusion:
                    conclusion_id = node_id
                    break
        
        # Calculate the confidence
        return graph.calculate_conclusion_confidence(conclusion_id)
    
    def find_alternative_conclusions(
        self, 
        graph: ReasoningGraph, 
        n: int = 3
    ) -> List[Tuple[str, float]]:
        """
        Find alternative conclusions from a reasoning graph.
        
        Args:
            graph: ReasoningGraph to find alternative conclusions from
            n: Maximum number of alternative conclusions to find
            
        Returns:
            List of (conclusion_text, confidence) tuples
        """
        # Get alternative conclusions with confidence
        alternatives = graph.find_alternative_conclusions(k=n)
        
        # Convert to (text, confidence) tuples
        return [(node.content, confidence) for node, confidence in alternatives]
    
    def evaluate_graph_structure(self, graph: ReasoningGraph) -> Dict[str, float]:
        """
        Evaluate the structure of a reasoning graph.
        
        Args:
            graph: ReasoningGraph to evaluate
            
        Returns:
            Dictionary of structural metrics
        """
        return graph.evaluate_graph_structure()
    
    def calculate_graph_consistency(self, graph: ReasoningGraph) -> float:
        """
        Calculate the logical consistency of a reasoning graph.
        
        Args:
            graph: ReasoningGraph to calculate consistency for
            
        Returns:
            Consistency score (0-1)
        """
        return graph.calculate_graph_consistency()
    
    def visualize_graph(
        self, 
        graph: ReasoningGraph, 
        output_path: Optional[str] = None,
        highlight_conclusion: bool = True
    ) -> None:
        """
        Visualize a reasoning graph.
        
        Args:
            graph: ReasoningGraph to visualize
            output_path: Path to save the visualization (if None, display instead)
            highlight_conclusion: Whether to highlight the strongest conclusion
        """
        highlight_node = None
        
        if highlight_conclusion:
            conclusion_node = graph.find_strongest_conclusion()
            if conclusion_node:
                highlight_node = conclusion_node.id
                
        graph.visualize(output_path=output_path, highlight_node=highlight_node)
    
    def merge_graphs(self, graphs: List[ReasoningGraph]) -> ReasoningGraph:
        """
        Merge multiple reasoning graphs into one.
        
        Args:
            graphs: List of ReasoningGraph instances to merge
            
        Returns:
            Merged ReasoningGraph
        """
        if not graphs:
            return ReasoningGraph()
            
        merged_graph = graphs[0].copy()
        
        for i, graph in enumerate(graphs[1:], 1):
            merged_graph.merge_with(graph, prefix=f"g{i}_")
            
        return merged_graph
    
    def reset(self) -> None:
        """Reset the internal state of the reasoning graphs component."""
        self.vectorizer = None
        self.critique_handler = None
        self.bias_handler = None

# Make key classes available at module level
__all__ = [
    'ReasoningGraphs',
    'ReasoningGraph',
    'GraphNode',
    'GraphEdge',
    'NodeType',
    'EdgeType'
]

# Optional initialization function to create a default instance
def create_default_reasoning_graphs(language_model=None):
    """
    Create a default ReasoningGraphs instance.
    
    Args:
        language_model: Optional language model for text generation
        
    Returns:
        Configured ReasoningGraphs instance
    """
    config = {
        "message_passing_iterations": 3
    }
    
    return ReasoningGraphs(
        language_model=language_model,
        config=config
    )

# Main execution for standalone usage and testing
if __name__ == "__main__":
    # Example usage
    problem = "If all humans are mortal, and Socrates is human, what can we conclude about Socrates?"
    
    # Create a mock language model for testing
    class MockLanguageModel:
        def generate(self, prompt):
            if "observations" in prompt.lower():
                return "1. All humans are mortal.\n2. Socrates is human."
            elif "hypotheses" in prompt.lower():
                return "1. Socrates might be mortal.\n2. Being human implies mortality."
            elif "principles" in prompt.lower():
                return "1. Logical deduction follows syllogistic rules.\n2. Class membership transfers properties."
            elif "inferences" in prompt.lower():
                return "1. If Socrates is human, and all humans have mortality, then Socrates has mortality.\n2. Mortality is a property of all humans including Socrates."
            elif "conclusion" in prompt.lower():
                return "Therefore, Socrates is mortal."
            elif "counterarguments" in prompt.lower():
                return "1. If Socrates were not human, this conclusion would not follow."
            elif "contradict" in prompt.lower():
                return "No"
            elif "relationship" in prompt.lower():
                return "elaborates"
            else:
                return "This is a response."
    
    # Create a reasoning graphs instance
    reasoning_graphs = ReasoningGraphs(language_model=MockLanguageModel())
    
    # Build a reasoning graph
    graph = reasoning_graphs.build_reasoning_graph(problem)
    
    # Find the strongest conclusion
    conclusion = reasoning_graphs.find_strongest_conclusion(graph)
    print(f"Strongest conclusion: {conclusion}")
    
    # Extract reasoning path
    path = reasoning_graphs.extract_reasoning_path(graph)
    print("\nReasoning path:")
    for i, step in enumerate(path, 1):
        print(f"{i}. {step}")
    
    # Extract justifications
    justifications = reasoning_graphs.extract_justifications(graph)
    print("\nJustifications:")
    for j_type, j_list in justifications.items():
        print(f"{j_type.replace('_', ' ').title()}:")
        for j in j_list:
            print(f"- {j}")
    
    # Calculate confidence
    confidence = reasoning_graphs.calculate_conclusion_confidence(graph)
    print(f"\nConclusion confidence: {confidence:.2f}")
    
    # Find alternative conclusions
    alternatives = reasoning_graphs.find_alternative_conclusions(graph)
    print("\nAlternative conclusions:")
    for conclusion, conf in alternatives:
        print(f"- {conclusion} (confidence: {conf:.2f})")
    
    # Evaluate graph structure
    structure = reasoning_graphs.evaluate_graph_structure(graph)
    print("\nGraph structure metrics:")
    for metric, value in structure.items():
        print(f"{metric}: {value}")
    
    # Visualize the graph
    print("\nVisualizing reasoning graph...")
    reasoning_graphs.visualize_graph(graph)