#!/usr/bin/env python3
"""
ULTRA Meta-Cognitive System: Bias Detection and Correction

This module implements the bias detection and correction capabilities of the ULTRA Meta-Cognitive System.
It provides mechanisms to identify cognitive biases in reasoning processes, quantify their severity,
and correct or mitigate their effects through automated reasoning refinement.

The bias detection system integrates with both the language model and neuromorphic core to leverage
pattern recognition and statistical analysis for identifying subtle biases in reasoning chains.
"""

import os
import re
import json
import math
import logging
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Any, Optional, Union, Set, Callable
from dataclasses import dataclass, field
from enum import Enum, auto
from collections import defaultdict, Counter
import spacy
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from transformers import AutoTokenizer, AutoModelForSequenceClassification
import matplotlib.pyplot as plt
import networkx as nx

# Import ULTRA system components
from ultra.config import get_config, SystemConfig
from ultra.utils.ultra_logging import get_logger
from ultra.core_neural.neuromorphic_core import NeuromorphicCore

# Configure logger
logger = get_logger(__name__)

class BiasCategory(Enum):
    """Categorization of cognitive biases by their underlying mechanisms."""
    INFORMATION_PROCESSING = auto()  # Biases in how we process information
    BELIEF_PERSISTENCE = auto()      # Biases in maintaining existing beliefs
    SOCIAL_INFLUENCE = auto()        # Biases due to social factors
    MEMORY = auto()                  # Biases in memory recall
    PROBABILITY_ESTIMATION = auto()  # Biases in probability judgments
    DECISION_MAKING = auto()         # Biases in decision processes

@dataclass
class BiasType:
    """Detailed representation of a cognitive bias type."""
    name: str
    category: BiasCategory
    description: str
    detection_patterns: List[str]
    correction_strategies: List[str]
    examples: List[str]
    keywords: List[str]
    counter_keywords: List[str] = field(default_factory=list)
    false_positive_patterns: List[str] = field(default_factory=list)

@dataclass
class BiasDetectionResult:
    """Results of bias detection on a piece of reasoning."""
    bias_type: BiasType
    severity: float  # 0.0 to 1.0 scale
    affected_segments: List[Tuple[int, int]]  # Start and end indices
    evidence: List[str]
    confidence: float  # 0.0 to 1.0 scale
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "bias_type": self.bias_type.name,
            "category": self.bias_type.category.name,
            "severity": self.severity,
            "affected_segments": self.affected_segments,
            "evidence": self.evidence,
            "confidence": self.confidence
        }

class BiasDetectionStrategy(Enum):
    """Different strategies for bias detection."""
    KEYWORD_MATCHING = auto()     # Based on detecting bias-indicative keywords
    PATTERN_RECOGNITION = auto()  # Based on recognized reasoning patterns
    STATISTICAL_ANALYSIS = auto() # Based on statistical properties of the reasoning
    NEURAL_EVALUATION = auto()    # Using neural models for bias detection
    HYBRID = auto()               # Combining multiple strategies

class BiasDetector:
    """Base class for bias detectors."""
    
    def __init__(self, bias_type: BiasType):
        self.bias_type = bias_type
        self.threshold = 0.7  # Default detection threshold
        
    def detect(self, reasoning_text: str) -> Optional[BiasDetectionResult]:
        """Detect the specific bias in the reasoning text."""
        raise NotImplementedError("Subclasses must implement this method")
        
    def confidence(self, evidence: List[str]) -> float:
        """Compute confidence in the bias detection."""
        # Base implementation - override in subclasses
        return min(0.5 + 0.1 * len(evidence), 1.0)

class KeywordBiasDetector(BiasDetector):
    """Detector using keyword and phrase matching."""
    
    def __init__(self, bias_type: BiasType):
        super().__init__(bias_type)
        self.nlp = spacy.load("en_core_web_sm")
        
    def detect(self, reasoning_text: str) -> Optional[BiasDetectionResult]:
        # Preprocess the text
        doc = self.nlp(reasoning_text.lower())
        text_lower = reasoning_text.lower()
        sentences = [sent.text for sent in doc.sents]
        
        # Search for keyword matches
        evidence = []
        affected_segments = []
        
        # Check each keyword
        for keyword in self.bias_type.keywords:
            # Check for exact matches
            for match in re.finditer(r'\b' + re.escape(keyword) + r'\b', text_lower):
                start, end = match.span()
                # Get the sentence containing this match
                for i, sent in enumerate(sentences):
                    if keyword in sent.lower():
                        evidence.append(f"Keyword '{keyword}' found in sentence: '{sent}'")
                        # Find sentence boundaries in original text
                        sent_start = reasoning_text.find(sent)
                        if sent_start >= 0:
                            affected_segments.append((sent_start, sent_start + len(sent)))
                        break
                        
        # Check counter-keywords that would indicate the bias is being avoided
        counter_evidence = []
        for counter_keyword in self.bias_type.counter_keywords:
            for match in re.finditer(r'\b' + re.escape(counter_keyword) + r'\b', text_lower):
                for i, sent in enumerate(sentences):
                    if counter_keyword in sent.lower():
                        counter_evidence.append(f"Counter-keyword '{counter_keyword}' found in: '{sent}'")
                        break
        
        # Calculate severity based on evidence and counter-evidence
        if not evidence:
            return None
            
        severity = min(0.5 + 0.1 * len(evidence) - 0.2 * len(counter_evidence), 1.0)
        severity = max(severity, 0.0)
        
        if severity < self.threshold:
            return None
            
        # Calculate confidence
        conf = self.confidence(evidence)
        
        # Check for false positive patterns
        for pattern in self.bias_type.false_positive_patterns:
            if re.search(pattern, text_lower):
                conf *= 0.5  # Reduce confidence if false positive pattern found
                
        return BiasDetectionResult(
            bias_type=self.bias_type,
            severity=severity,
            affected_segments=affected_segments,
            evidence=evidence,
            confidence=conf
        )
        
    def confidence(self, evidence: List[str]) -> float:
        """Compute confidence based on evidence quality and quantity."""
        base_confidence = super().confidence(evidence)
        
        # Adjust confidence based on evidence diversity
        unique_keywords = set()
        for item in evidence:
            for keyword in self.bias_type.keywords:
                if keyword in item.lower():
                    unique_keywords.add(keyword)
                    
        keyword_diversity = len(unique_keywords) / max(1, len(self.bias_type.keywords))
        
        return base_confidence * (0.5 + 0.5 * keyword_diversity)

class PatternBiasDetector(BiasDetector):
    """Detector using more complex reasoning pattern recognition."""
    
    def __init__(self, bias_type: BiasType):
        super().__init__(bias_type)
        self.nlp = spacy.load("en_core_web_sm")
        # Compile regex patterns for faster matching
        self.compiled_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in self.bias_type.detection_patterns]
        
    def detect(self, reasoning_text: str) -> Optional[BiasDetectionResult]:
        doc = self.nlp(reasoning_text)
        evidence = []
        affected_segments = []
        
        # Check each pattern
        for i, pattern in enumerate(self.compiled_patterns):
            for match in pattern.finditer(reasoning_text):
                start, end = match.span()
                matched_text = reasoning_text[start:end]
                
                # Find the sentence(s) containing this match
                for sent in doc.sents:
                    sent_start = sent.start_char
                    sent_end = sent.end_char
                    
                    # If match overlaps with sentence
                    if not (end <= sent_start or start >= sent_end):
                        full_sent = sent.text
                        evidence.append(f"Pattern '{self.bias_type.detection_patterns[i]}' matched in: '{full_sent}'")
                        affected_segments.append((sent_start, sent_end))
                        break
        
        # If no evidence found, return None
        if not evidence:
            return None
            
        # Calculate severity and confidence
        severity = min(0.6 + 0.1 * len(evidence), 1.0)
        
        if severity < self.threshold:
            return None
            
        # Pattern matches are more reliable than keyword matches
        confidence = min(0.7 + 0.05 * len(evidence), 1.0)
        
        return BiasDetectionResult(
            bias_type=self.bias_type,
            severity=severity,
            affected_segments=affected_segments,
            evidence=evidence,
            confidence=confidence
        )

class StatisticalBiasDetector(BiasDetector):
    """Detector using statistical properties of the reasoning."""
    
    def __init__(self, bias_type: BiasType):
        super().__init__(bias_type)
        self.nlp = spacy.load("en_core_web_sm")
        self.vectorizer = TfidfVectorizer(ngram_range=(1, 3), min_df=1, max_df=0.9)
        
    def detect(self, reasoning_text: str) -> Optional[BiasDetectionResult]:
        doc = self.nlp(reasoning_text)
        sentences = [sent.text for sent in doc.sents]
        
        if len(sentences) < 3:  # Need at least a few sentences for statistical analysis
            return None
            
        evidence = []
        affected_segments = []
        
        # Detect confirmation bias through repetition of similar points
        if self.bias_type.name == "Confirmation bias":
            # Vectorize sentences
            try:
                sentence_vectors = self.vectorizer.fit_transform(sentences)
                similarities = cosine_similarity(sentence_vectors)
                
                # Find highly similar sentence pairs (excluding self-similarity)
                for i in range(len(sentences)):
                    for j in range(i+1, len(sentences)):
                        if similarities[i, j] > 0.6:  # High similarity threshold
                            evidence.append(f"Similar points repeated in: '{sentences[i]}' and '{sentences[j]}'")
                            # Find sentence boundaries in original text
                            i_start = reasoning_text.find(sentences[i])
                            j_start = reasoning_text.find(sentences[j])
                            if i_start >= 0:
                                affected_segments.append((i_start, i_start + len(sentences[i])))
                            if j_start >= 0:
                                affected_segments.append((j_start, j_start + len(sentences[j])))
            except:
                # Handle case where vectorizer fails (e.g., empty input)
                pass
                
        # Detect availability bias through overemphasis on certain terms
        elif self.bias_type.name == "Availability bias":
            # Count term frequencies
            term_counts = Counter()
            for token in doc:
                if not token.is_stop and not token.is_punct and token.is_alpha:
                    term_counts[token.lemma_] += 1
                    
            # Find overrepresented terms
            total_terms = sum(term_counts.values())
            for term, count in term_counts.most_common(5):
                term_ratio = count / total_terms
                if term_ratio > 0.1:  # Term appears in more than 10% of content
                    # Find sentences containing this term
                    for i, sent in enumerate(sentences):
                        sent_doc = self.nlp(sent)
                        if any(token.lemma_ == term for token in sent_doc):
                            evidence.append(f"Term '{term}' overrepresented ({count} occurrences) in: '{sent}'")
                            sent_start = reasoning_text.find(sent)
                            if sent_start >= 0:
                                affected_segments.append((sent_start, sent_start + len(sent)))
        
        # Detect base rate fallacy through skewed probability focus
        elif self.bias_type.name == "Base rate fallacy":
            probability_terms = {"probability", "chance", "likelihood", "percent", "rate", "frequency", "proportion"}
            has_specific_prob = False
            has_base_rate = False
            
            for sent in sentences:
                sent_lower = sent.lower()
                
                # Check for specific probability mentions
                if any(term in sent_lower for term in probability_terms) and any(re.search(r'\d+\s*%', sent_lower) or re.search(r'\d+\s*out of\s*\d+', sent_lower)):
                    has_specific_prob = True
                    
                # Check for base rate mentions
                if "base rate" in sent_lower or "prior probability" in sent_lower or "background rate" in sent_lower:
                    has_base_rate = True
                    
            # If specific probabilities mentioned but no base rate
            if has_specific_prob and not has_base_rate:
                for sent in sentences:
                    sent_lower = sent.lower()
                    if any(term in sent_lower for term in probability_terms) and any(re.search(r'\d+\s*%', sent_lower) or re.search(r'\d+\s*out of\s*\d+', sent_lower)):
                        evidence.append(f"Probability mentioned without base rate consideration in: '{sent}'")
                        sent_start = reasoning_text.find(sent)
                        if sent_start >= 0:
                            affected_segments.append((sent_start, sent_start + len(sent)))
                            
        # If no evidence found, return None
        if not evidence:
            return None
            
        # Calculate severity based on evidence
        severity = min(0.5 + 0.1 * len(evidence), 1.0)
        
        if severity < self.threshold:
            return None
            
        # Statistical detection provides medium confidence
        confidence = min(0.6 + 0.05 * len(evidence), 0.9)
        
        return BiasDetectionResult(
            bias_type=self.bias_type,
            severity=severity,
            affected_segments=affected_segments,
            evidence=evidence,
            confidence=confidence
        )

class NeuralBiasDetector(BiasDetector):
    """Detector using neural models for bias detection."""
    
    def __init__(self, bias_type: BiasType, model_path: Optional[str] = None):
        super().__init__(bias_type)
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # Load pre-trained model or use default
        if model_path and os.path.exists(model_path):
            self.tokenizer = AutoTokenizer.from_pretrained(model_path)
            self.model = AutoModelForSequenceClassification.from_pretrained(model_path).to(self.device)
        else:
            # Use a generic model for classification
            model_name = "distilbert-base-uncased"
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            self.model = AutoModelForSequenceClassification.from_pretrained(
                model_name, num_labels=2).to(self.device)
            logger.warning(f"Using default model {model_name} - consider fine-tuning for bias detection")
            
        self.nlp = spacy.load("en_core_web_sm")
        
    def detect(self, reasoning_text: str) -> Optional[BiasDetectionResult]:
        doc = self.nlp(reasoning_text)
        sentences = [sent.text for sent in doc.sents]
        
        if not sentences:
            return None
            
        # Prepare bias detection prompt
        bias_description = f"{self.bias_type.description}\nExamples: {'; '.join(self.bias_type.examples[:2])}"
        
        evidence = []
        affected_segments = []
        sentence_scores = []
        
        # Evaluate each sentence for bias presence
        for i, sent in enumerate(sentences):
            # Skip very short sentences
            if len(sent.split()) < 3:
                continue
                
            # Prepare input for the model
            prompt = f"Bias type: {self.bias_type.name}. {bias_description}\nText: {sent}\nDoes this text exhibit the bias?"
            
            inputs = self.tokenizer(prompt, return_tensors="pt", truncation=True, padding=True).to(self.device)
            
            with torch.no_grad():
                outputs = self.model(**inputs)
                
            # Get prediction (assuming binary classification: no bias, bias)
            probabilities = F.softmax(outputs.logits, dim=-1)
            bias_score = probabilities[0, 1].item()  # Probability of bias
            sentence_scores.append((i, bias_score))
            
            # If bias score exceeds threshold, add as evidence
            if bias_score > self.threshold:
                evidence.append(f"Neural model identified bias in: '{sent}' (confidence: {bias_score:.2f})")
                sent_start = reasoning_text.find(sent)
                if sent_start >= 0:
                    affected_segments.append((sent_start, sent_start + len(sent)))
                    
        # If no evidence found, return None
        if not evidence:
            return None
            
        # Calculate overall severity as weighted average of sentence scores
        severity_scores = [score for _, score in sentence_scores if score > self.threshold]
        severity = sum(severity_scores) / len(severity_scores) if severity_scores else 0.0
        
        if severity < self.threshold:
            return None
            
        # Neural detection can provide high confidence with sufficient evidence
        confidence = min(0.7 + 0.05 * len(evidence), 0.95)
        
        return BiasDetectionResult(
            bias_type=self.bias_type,
            severity=severity,
            affected_segments=affected_segments,
            evidence=evidence,
            confidence=confidence
        )
        
    def fine_tune(self, examples: List[Tuple[str, bool]], epochs: int = 3):
        """Fine-tune the neural model on bias examples."""
        # Convert examples to dataset
        texts = [text for text, _ in examples]
        labels = [1 if is_biased else 0 for _, is_biased in examples]
        
        dataset = list(zip(texts, labels))
        
        # Basic training loop
        optimizer = torch.optim.AdamW(self.model.parameters(), lr=5e-5)
        loss_fn = nn.CrossEntropyLoss()
        
        self.model.train()
        for epoch in range(epochs):
            total_loss = 0
            
            # Shuffle data for each epoch
            np.random.shuffle(dataset)
            
            for text, label in dataset:
                # Prepare input
                inputs = self.tokenizer(text, return_tensors="pt", truncation=True, padding=True).to(self.device)
                labels = torch.tensor([label]).to(self.device)
                
                # Forward pass
                outputs = self.model(**inputs)
                
                # Compute loss
                loss = loss_fn(outputs.logits, labels)
                total_loss += loss.item()
                
                # Backward pass and optimization
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
                
            logger.info(f"Epoch {epoch+1}/{epochs}, Loss: {total_loss/len(dataset):.4f}")
            
        self.model.eval()
        logger.info(f"Fine-tuning completed for {self.bias_type.name} detector")

class HybridBiasDetector(BiasDetector):
    """Combines multiple detection strategies for more robust bias detection."""
    
    def __init__(self, bias_type: BiasType, detectors: List[BiasDetector]):
        super().__init__(bias_type)
        self.detectors = detectors
        
    def detect(self, reasoning_text: str) -> Optional[BiasDetectionResult]:
        # Run all detectors
        results = []
        for detector in self.detectors:
            result = detector.detect(reasoning_text)
            if result:
                results.append(result)
                
        if not results:
            return None
            
        # Combine evidence and affected segments
        all_evidence = []
        all_segments = []
        for result in results:
            all_evidence.extend(result.evidence)
            all_segments.extend(result.affected_segments)
            
        # Remove duplicate segments
        unique_segments = []
        for segment in all_segments:
            if segment not in unique_segments:
                unique_segments.append(segment)
                
        # Calculate combined severity (weighted by confidence)
        severity_sum = sum(r.severity * r.confidence for r in results)
        confidence_sum = sum(r.confidence for r in results)
        combined_severity = severity_sum / confidence_sum if confidence_sum > 0 else 0.0
        
        # Calculate combined confidence (higher with more detectors)
        # Base confidence is the weighted average of individual confidences
        base_confidence = confidence_sum / len(results)
        # Adjust based on agreement between detectors
        detector_ratio = len(results) / len(self.detectors)
        combined_confidence = base_confidence * (0.7 + 0.3 * detector_ratio)
        
        if combined_severity < self.threshold:
            return None
            
        return BiasDetectionResult(
            bias_type=self.bias_type,
            severity=combined_severity,
            affected_segments=unique_segments,
            evidence=all_evidence,
            confidence=combined_confidence
        )

class BiasCorrector:
    """Base class for bias correction mechanisms."""
    
    def __init__(self, language_model=None):
        self.language_model = language_model
        
    def correct(self, reasoning_text: str, bias_results: List[BiasDetectionResult]) -> str:
        """Correct biases in the reasoning text."""
        raise NotImplementedError("Subclasses must implement this method")

class PromptBasedBiasCorrector(BiasCorrector):
    """Corrector using language model prompting for bias correction."""
    
    def __init__(self, language_model=None):
        super().__init__(language_model)
        
    def correct(self, reasoning_text: str, bias_results: List[BiasDetectionResult]) -> str:
        if not bias_results or not self.language_model:
            return reasoning_text
            
        # Extract bias information for the prompt
        bias_info = ""
        for result in bias_results:
            bias_type = result.bias_type
            bias_info += (f"- {bias_type.name} (detected with {result.confidence:.2f} confidence, "
                        f"severity: {result.severity:.2f})\n")
            bias_info += f"  Description: {bias_type.description}\n"
            for i, evidence in enumerate(result.evidence[:3]):  # Limit to 3 pieces of evidence per bias
                bias_info += f"  Evidence {i+1}: {evidence}\n"
            bias_info += f"  Correction strategies: {'; '.join(bias_type.correction_strategies[:2])}\n\n"
            
        # Create prompt for bias correction
        prompt = f"""
        Original reasoning: {reasoning_text}
        
        The reasoning contains the following cognitive biases:
        {bias_info}
        
        Please rewrite the reasoning to correct these biases while preserving the valid parts of the original reasoning.
        Follow these general guidelines:
        1. Consider alternative perspectives and counterarguments
        2. Evaluate evidence more objectively
        3. Consider base rates and statistical principles properly
        4. Avoid emotional language that might skew judgment
        5. Maintain logical consistency
        
        Corrected reasoning:
        """
        
        # Generate corrected reasoning
        try:
            corrected_reasoning = self.language_model.generate(prompt).strip()
            return corrected_reasoning
        except Exception as e:
            logger.error(f"Error in bias correction: {e}")
            return reasoning_text

class SegmentBasedBiasCorrector(BiasCorrector):
    """Corrector focused on specific segments with detected bias."""
    
    def __init__(self, language_model=None):
        super().__init__(language_model)
        self.nlp = spacy.load("en_core_web_sm")
        
    def correct(self, reasoning_text: str, bias_results: List[BiasDetectionResult]) -> str:
        if not bias_results or not self.language_model:
            return reasoning_text
            
        # Get all affected segments
        all_segments = []
        for result in bias_results:
            all_segments.extend(result.affected_segments)
            
        # Sort segments by start position and merge overlapping ones
        all_segments.sort(key=lambda x: x[0])
        merged_segments = []
        for segment in all_segments:
            if not merged_segments or segment[0] > merged_segments[-1][1]:
                merged_segments.append(segment)
            else:
                merged_segments[-1] = (merged_segments[-1][0], max(merged_segments[-1][1], segment[1]))
                
        # Extract biases affecting each segment
        segment_biases = {i: [] for i in range(len(merged_segments))}
        for result in bias_results:
            for segment in result.affected_segments:
                for i, merged_segment in enumerate(merged_segments):
                    if (segment[0] >= merged_segment[0] and segment[0] < merged_segment[1]) or \
                       (segment[1] > merged_segment[0] and segment[1] <= merged_segment[1]) or \
                       (segment[0] <= merged_segment[0] and segment[1] >= merged_segment[1]):
                        segment_biases[i].append(result.bias_type)
                        break
        
        # Correct each segment
        corrected_text = reasoning_text
        offset = 0  # Track text position changes due to corrections
        
        for i, (start, end) in enumerate(merged_segments):
            biases = segment_biases[i]
            if not biases:
                continue
                
            # Extract segment
            adjusted_start = start + offset
            adjusted_end = end + offset
            segment = corrected_text[adjusted_start:adjusted_end]
            
            # Create correction prompt
            bias_info = ""
            for bias_type in biases:
                bias_info += f"- {bias_type.name}: {bias_type.description}\n"
                bias_info += f"  Correction strategies: {'; '.join(bias_type.correction_strategies[:2])}\n"
                
            prompt = f"""
            Original text segment: {segment}
            
            This segment exhibits these cognitive biases:
            {bias_info}
            
            Please rewrite this segment to correct these biases while preserving the valid content.
            Make your correction focused and concise, matching the style of the original.
            
            Corrected segment:
            """
            
            try:
                corrected_segment = self.language_model.generate(prompt).strip()
                
                # Replace segment in corrected text
                corrected_text = corrected_text[:adjusted_start] + corrected_segment + corrected_text[adjusted_end:]
                
                # Update offset
                offset += len(corrected_segment) - (adjusted_end - adjusted_start)
            except Exception as e:
                logger.error(f"Error in segment correction: {e}")
                
        return corrected_text

class RuleBasedBiasCorrector(BiasCorrector):
    """Corrector using predefined rules for bias correction."""
    
    def __init__(self, language_model=None):
        super().__init__(language_model)
        self.nlp = spacy.load("en_core_web_sm")
        self.correction_rules = {
            "Confirmation bias": self._correct_confirmation_bias,
            "Availability bias": self._correct_availability_bias,
            "Anchoring bias": self._correct_anchoring_bias,
            "Overconfidence bias": self._correct_overconfidence_bias,
            "Fundamental attribution error": self._correct_fundamental_attribution_error,
            "Hindsight bias": self._correct_hindsight_bias,
            "Framing effect": self._correct_framing_effect,
            "Sunk cost fallacy": self._correct_sunk_cost_fallacy,
            "Representativeness heuristic": self._correct_representativeness_heuristic,
            "Base rate fallacy": self._correct_base_rate_fallacy
        }
        
    def correct(self, reasoning_text: str, bias_results: List[BiasDetectionResult]) -> str:
        if not bias_results:
            return reasoning_text
            
        corrected_text = reasoning_text
        
        # Apply corrections for each detected bias
        for result in bias_results:
            bias_name = result.bias_type.name
            if bias_name in self.correction_rules:
                corrected_text = self.correction_rules[bias_name](corrected_text, result)
                
        return corrected_text
        
    def _correct_confirmation_bias(self, text: str, bias_result: BiasDetectionResult) -> str:
        """Apply rules to correct confirmation bias."""
        doc = self.nlp(text)
        sentences = [sent.text for sent in doc.sents]
        corrected_sentences = []
        
        affected_indices = set()
        for start, end in bias_result.affected_segments:
            for i, sent in enumerate(sentences):
                sent_start = text.find(sent)
                sent_end = sent_start + len(sent)
                if (start <= sent_start and end >= sent_end) or \
                   (start >= sent_start and start < sent_end) or \
                   (end > sent_start and end <= sent_end):
                    affected_indices.add(i)
        
        for i, sent in enumerate(sentences):
            if i in affected_indices:
                # Add counterargument qualifier
                if not any(phrase in sent.lower() for phrase in ["however", "on the other hand", "alternatively", "conversely"]):
                    qualification = "However, it's important to consider alternative perspectives. "
                    corrected_sentences.append(qualification + sent)
                else:
                    corrected_sentences.append(sent)
            else:
                corrected_sentences.append(sent)
                
        # Add a concluding sentence about balanced consideration if not present
        if not any("both sides" in sent.lower() or "multiple perspectives" in sent.lower() for sent in sentences):
            corrected_sentences.append("Considering multiple perspectives helps ensure a more balanced conclusion.")
            
        return " ".join(corrected_sentences)
        
    def _correct_availability_bias(self, text: str, bias_result: BiasDetectionResult) -> str:
        """Apply rules to correct availability bias."""
        doc = self.nlp(text)
        sentences = [sent.text for sent in doc.sents]
        corrected_sentences = []
        
        has_statistical_reference = any("statistic" in sent.lower() or "data" in sent.lower() or "study" in sent.lower() for sent in sentences)
        
        affected_indices = set()
        for start, end in bias_result.affected_segments:
            for i, sent in enumerate(sentences):
                sent_start = text.find(sent)
                sent_end = sent_start + len(sent)
                if (start <= sent_start and end >= sent_end) or \
                   (start >= sent_start and start < sent_end) or \
                   (end > sent_start and end <= sent_end):
                    affected_indices.add(i)
        
        for i, sent in enumerate(sentences):
            if i in affected_indices:
                # Check if the sentence relies on anecdotal evidence
                if any(word in sent.lower() for word in ["i know", "i've seen", "i witnessed", "i heard", "i remember"]):
                    corrected_sent = sent + " While this anecdotal observation provides one perspective, systematic data would offer a more complete picture."
                    corrected_sentences.append(corrected_sent)
                else:
                    corrected_sentences.append(sent)
            else:
                corrected_sentences.append(sent)
                
        # Add statistical consideration if missing
        if not has_statistical_reference:
            corrected_sentences.append("A comprehensive analysis would benefit from statistical evidence rather than relying solely on readily available examples.")
            
        return " ".join(corrected_sentences)
        
    def _correct_anchoring_bias(self, text: str, bias_result: BiasDetectionResult) -> str:
        """Apply rules to correct anchoring bias."""
        doc = self.nlp(text)
        sentences = [sent.text for sent in doc.sents]
        corrected_sentences = []
        
        # Look for numbers that might serve as anchors
        numbers = []
        for sent in sentences:
            numbers.extend(re.findall(r'\b\d+(?:\.\d+)?\b', sent))
        
        affected_indices = set()
        for start, end in bias_result.affected_segments:
            for i, sent in enumerate(sentences):
                sent_start = text.find(sent)
                sent_end = sent_start + len(sent)
                if (start <= sent_start and end >= sent_end) or \
                   (start >= sent_start and start < sent_end) or \
                   (end > sent_start and end <= sent_end):
                    affected_indices.add(i)
        
        for i, sent in enumerate(sentences):
            if i in affected_indices:
                # Check if sentence has a number that could be an anchor
                sent_numbers = re.findall(r'\b\d+(?:\.\d+)?\b', sent)
                if sent_numbers and sent_numbers[0] in numbers:
                    corrected_sent = sent + " It's important to consider a range of values rather than fixating on this initial figure."
                    corrected_sentences.append(corrected_sent)
                else:
                    corrected_sentences.append(sent)
            else:
                corrected_sentences.append(sent)
                
        # Add conclusion about range consideration
        corrected_sentences.append("A more balanced approach involves considering the full range of possibilities without being unduly influenced by initial values.")
        
        return " ".join(corrected_sentences)
        
    def _correct_overconfidence_bias(self, text: str, bias_result: BiasDetectionResult) -> str:
        """Apply rules to correct overconfidence bias."""
        doc = self.nlp(text)
        sentences = [sent.text for sent in doc.sents]
        corrected_sentences = []
        
        affected_indices = set()
        for start, end in bias_result.affected_segments:
            for i, sent in enumerate(sentences):
                sent_start = text.find(sent)
                sent_end = sent_start + len(sent)
                if (start <= sent_start and end >= sent_end) or \
                   (start >= sent_start and start < sent_end) or \
                   (end > sent_start and end <= sent_end):
                    affected_indices.add(i)
        
        certainty_terms = ["certainly", "definitely", "absolutely", "obviously", "clearly", "undoubtedly", "always", "never"]
        
        for i, sent in enumerate(sentences):
            if i in affected_indices:
                # Replace certainty terms with more moderate language
                modified_sent = sent
                for term in certainty_terms:
                    modified_sent = re.sub(r'\b' + re.escape(term) + r'\b', 'likely', modified_sent, flags=re.IGNORECASE)
                    
                # Add qualifier for very confident statements
                if any(phrase in sent.lower() for phrase in ["i am sure", "there is no doubt", "it is clear"]):
                    modified_sent += " Though there is some uncertainty in this assessment."
                    
                corrected_sentences.append(modified_sent)
            else:
                corrected_sentences.append(sent)
                
        # Add uncertainty acknowledgment if missing
        if not any("uncertain" in sent.lower() or "probability" in sent.lower() or "likelihood" in sent.lower() for sent in sentences):
            corrected_sentences.append("As with many complex issues, there remains some uncertainty in these conclusions.")
            
        return " ".join(corrected_sentences)
        
    def _correct_fundamental_attribution_error(self, text: str, bias_result: BiasDetectionResult) -> str:
        """Apply rules to correct fundamental attribution error."""
        doc = self.nlp(text)
        sentences = [sent.text for sent in doc.sents]
        corrected_sentences = []
        
        affected_indices = set()
        for start, end in bias_result.affected_segments:
            for i, sent in enumerate(sentences):
                sent_start = text.find(sent)
                sent_end = sent_start + len(sent)
                if (start <= sent_start and end >= sent_end) or \
                   (start >= sent_start and start < sent_end) or \
                   (end > sent_start and end <= sent_end):
                    affected_indices.add(i)
        
        for i, sent in enumerate(sentences):
            if i in affected_indices:
                # Look for personality attributions
                personality_terms = ["lazy", "hardworking", "intelligent", "stupid", "arrogant", "humble", "selfish", "generous"]
                
                if any(term in sent.lower() for term in personality_terms):
                    modified_sent = sent + " External circumstances and situational factors might also contribute to this behavior."
                    corrected_sentences.append(modified_sent)
                else:
                    corrected_sentences.append(sent)
            else:
                corrected_sentences.append(sent)
                
        # Add consideration of situational factors
        situational_mentioned = any("situation" in sent.lower() or "circumstance" in sent.lower() or "context" in sent.lower() for sent in sentences)
        if not situational_mentioned:
            corrected_sentences.append("A more complete analysis would consider both personal characteristics and situational factors that influence behavior.")
            
        return " ".join(corrected_sentences)
        
    def _correct_hindsight_bias(self, text: str, bias_result: BiasDetectionResult) -> str:
        """Apply rules to correct hindsight bias."""
        doc = self.nlp(text)
        sentences = [sent.text for sent in doc.sents]
        corrected_sentences = []
        
        affected_indices = set()
        for start, end in bias_result.affected_segments:
            for i, sent in enumerate(sentences):
                sent_start = text.find(sent)
                sent_end = sent_start + len(sent)
                if (start <= sent_start and end >= sent_end) or \
                   (start >= sent_start and start < sent_end) or \
                   (end > sent_start and end <= sent_end):
                    affected_indices.add(i)
        
        hindsight_phrases = ["should have seen it coming", "obvious in retrospect", "was inevitable", "was predictable"]
        
        for i, sent in enumerate(sentences):
            if i in affected_indices:
                # Check for hindsight language
                if any(phrase in sent.lower() for phrase in hindsight_phrases):
                    modified_sent = sent + " However, it's important to acknowledge that events often appear more predictable after they've occurred than they were beforehand."
                    corrected_sentences.append(modified_sent)
                else:
                    corrected_sentences.append(sent)
            else:
                corrected_sentences.append(sent)
                
        # Add acknowledgment of complexity and uncertainty
        corrected_sentences.append("Reconstructing the uncertainty that existed prior to known outcomes helps avoid overestimating the predictability of past events.")
        
        return " ".join(corrected_sentences)
        
    def _correct_framing_effect(self, text: str, bias_result: BiasDetectionResult) -> str:
        """Apply rules to correct framing effect."""
        doc = self.nlp(text)
        sentences = [sent.text for sent in doc.sents]
        corrected_sentences = []
        
        affected_indices = set()
        for start, end in bias_result.affected_segments:
            for i, sent in enumerate(sentences):
                sent_start = text.find(sent)
                sent_end = sent_start + len(sent)
                if (start <= sent_start and end >= sent_end) or \
                   (start >= sent_start and start < sent_end) or \
                   (end > sent_start and end <= sent_end):
                    affected_indices.add(i)
        
        has_positive_frame = any(phrase in text.lower() for phrase in ["gain", "save", "benefit", "success rate"])
        has_negative_frame = any(phrase in text.lower() for phrase in ["loss", "cost", "risk", "failure rate"])
        
        for i, sent in enumerate(sentences):
            if i in affected_indices:
                if has_positive_frame and not has_negative_frame:
                    # Reframe positive as negative
                    if "gain" in sent.lower():
                        corrected_sentences.append(sent + " This could alternatively be framed as avoiding a loss.")
                    elif "success rate" in sent.lower():
                        corrected_sentences.append(sent + " This could also be expressed as a failure rate.")
                    else:
                        corrected_sentences.append(sent)
                elif has_negative_frame and not has_positive_frame:
                    # Reframe negative as positive
                    if "loss" in sent.lower():
                        corrected_sentences.append(sent + " This could alternatively be framed as a potential gain.")
                    elif "failure rate" in sent.lower():
                        corrected_sentences.append(sent + " This could also be expressed as a success rate.")
                    else:
                        corrected_sentences.append(sent)
                else:
                    corrected_sentences.append(sent)
            else:
                corrected_sentences.append(sent)
                
        # Add note about considering alternative frames
        corrected_sentences.append("Considering both positive and negative frames helps avoid biases in judgment that arise from presentation format alone.")
        
        return " ".join(corrected_sentences)
        
    def _correct_sunk_cost_fallacy(self, text: str, bias_result: BiasDetectionResult) -> str:
        """Apply rules to correct sunk cost fallacy."""
        doc = self.nlp(text)
        sentences = [sent.text for sent in doc.sents]
        corrected_sentences = []
        
        affected_indices = set()
        for start, end in bias_result.affected_segments:
            for i, sent in enumerate(sentences):
                sent_start = text.find(sent)
                sent_end = sent_start + len(sent)
                if (start <= sent_start and end >= sent_end) or \
                   (start >= sent_start and start < sent_end) or \
                   (end > sent_start and end <= sent_end):
                    affected_indices.add(i)
        
        sunk_cost_phrases = ["already invested", "put so much into", "can't back out now", "too much time/money", "might as well continue"]
        
        for i, sent in enumerate(sentences):
            if i in affected_indices:
                # Check for sunk cost language
                if any(phrase in sent.lower() for phrase in sunk_cost_phrases):
                    modified_sent = sent + " However, decisions should be based on future expected outcomes, not past investments."
                    corrected_sentences.append(modified_sent)
                else:
                    corrected_sentences.append(sent)
            else:
                corrected_sentences.append(sent)
                
        # Add forward-looking perspective
        corrected_sentences.append("The optimal decision depends on future prospects, not sunk costs that cannot be recovered.")
        
        return " ".join(corrected_sentences)
        
    def _correct_representativeness_heuristic(self, text: str, bias_result: BiasDetectionResult) -> str:
        """Apply rules to correct representativeness heuristic."""
        doc = self.nlp(text)
        sentences = [sent.text for sent in doc.sents]
        corrected_sentences = []
        
        affected_indices = set()
        for start, end in bias_result.affected_segments:
            for i, sent in enumerate(sentences):
                sent_start = text.find(sent)
                sent_end = sent_start + len(sent)
                if (start <= sent_start and end >= sent_end) or \
                   (start >= sent_start and start < sent_end) or \
                   (end > sent_start and end <= sent_end):
                    affected_indices.add(i)
        
        stereotype_phrases = ["typically", "stereotype", "usually", "generally", "most are", "kind of person"]
        
        for i, sent in enumerate(sentences):
            if i in affected_indices:
                # Check for stereotype language
                if any(phrase in sent.lower() for phrase in stereotype_phrases):
                    modified_sent = sent + " Individual cases may vary significantly from group averages or stereotypical representations."
                    corrected_sentences.append(modified_sent)
                else:
                    corrected_sentences.append(sent)
            else:
                corrected_sentences.append(sent)
                
        # Add statistical perspective
        has_base_rate = any("base rate" in sent.lower() or "prior probability" in sent.lower() for sent in sentences)
        if not has_base_rate:
            corrected_sentences.append("Statistical reasoning should account for both specific characteristics and underlying base rates in the population.")
            
        return " ".join(corrected_sentences)
        
    def _correct_base_rate_fallacy(self, text: str, bias_result: BiasDetectionResult) -> str:
        """Apply rules to correct base rate fallacy."""
        doc = self.nlp(text)
        sentences = [sent.text for sent in doc.sents]
        corrected_sentences = []
        
        affected_indices = set()
        for start, end in bias_result.affected_segments:
            for i, sent in enumerate(sentences):
                sent_start = text.find(sent)
                sent_end = sent_start + len(sent)
                if (start <= sent_start and end >= sent_end) or \
                   (start >= sent_start and start < sent_end) or \
                   (end > sent_start and end <= sent_end):
                    affected_indices.add(i)
        
        has_specific_probability = any(re.search(r'\d+\s*%', sent) or "probability" in sent.lower() for sent in sentences)
        
        for i, sent in enumerate(sentences):
            if i in affected_indices:
                # Check for specific probability mentions without base rate
                if (re.search(r'\d+\s*%', sent) or "probability" in sent.lower()) and not ("base rate" in sent.lower()):
                    modified_sent = sent + " This assessment should be considered in the context of the underlying base rate in the population."
                    corrected_sentences.append(modified_sent)
                else:
                    corrected_sentences.append(sent)
            else:
                corrected_sentences.append(sent)
                
        # Add Bayes' theorem perspective if discussing probabilities
        if has_specific_probability:
            corrected_sentences.append("A proper Bayesian analysis would combine both the specific evidence and the prior probability (base rate) to reach a more accurate conclusion.")
            
        return " ".join(corrected_sentences)

class HybridBiasCorrector(BiasCorrector):
    """Combines multiple correction strategies for comprehensive bias correction."""
    
    def __init__(self, language_model=None):
        super().__init__(language_model)
        # Initialize component correctors
        self.prompt_corrector = PromptBasedBiasCorrector(language_model)
        self.segment_corrector = SegmentBasedBiasCorrector(language_model)
        self.rule_corrector = RuleBasedBiasCorrector(language_model)
        
    def correct(self, reasoning_text: str, bias_results: List[BiasDetectionResult]) -> str:
        if not bias_results:
            return reasoning_text
            
        # Group biases by type
        bias_by_type = {}
        for result in bias_results:
            bias_name = result.bias_type.name
            if bias_name in bias_by_type:
                # Update with higher severity/confidence if appropriate
                if result.severity > bias_by_type[bias_name].severity:
                    bias_by_type[bias_name] = result
            else:
                bias_by_type[bias_name] = result
        
        # First apply rule-based corrections for specific biases
        rule_corrected_text = self.rule_corrector.correct(reasoning_text, list(bias_by_type.values()))
        
        # Then apply segment-based corrections for remaining biases
        high_confidence_biases = [result for result in bias_results if result.confidence > 0.8]
        if high_confidence_biases and self.language_model:
            segment_corrected_text = self.segment_corrector.correct(rule_corrected_text, high_confidence_biases)
        else:
            segment_corrected_text = rule_corrected_text
            
        # Finally, if major biases remain, apply prompt-based correction
        major_biases = [result for result in bias_results if result.severity > 0.8]
        if major_biases and self.language_model:
            # For severe biases, we may want a more comprehensive rewrite
            final_corrected_text = self.prompt_corrector.correct(segment_corrected_text, major_biases)
        else:
            final_corrected_text = segment_corrected_text
            
        return final_corrected_text

class BiasDetection:
    """
    Main class for the bias detection and correction system.
    
    This class coordinates the detection of cognitive biases in reasoning processes and
    provides mechanisms to correct or mitigate their effects.
    """
    
    def __init__(
        self, 
        language_model=None, 
        neuromorphic_core: Optional['NeuromorphicCore'] = None,
        config: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize the bias detection system.
        
        Args:
            language_model: Language model for bias detection and correction
            neuromorphic_core: Link to the ULTRA Core Neural Architecture
            config: Configuration options for bias detection
        """
        self.language_model = language_model
        self.neuromorphic_core = neuromorphic_core
        self.config = config or {}
        
        # Configure logging
        self.logger = get_logger(__name__)
        self.logger.setLevel(self.config.get("log_level", logging.INFO))
        
        # Initialize bias types
        self._init_bias_types()
        
        # Initialize detection strategies for each bias type
        self._init_detectors()
        
        # Initialize bias corrector
        self._init_corrector()
        
        # Performance tracking
        self.detection_history = []
        
    def _init_bias_types(self):
        """Initialize the catalog of cognitive bias types."""
        # Define bias types with detailed information
        self.bias_types = {
            "confirmation_bias": BiasType(
                name="Confirmation bias",
                category=BiasCategory.BELIEF_PERSISTENCE,
                description="The tendency to search for, interpret, favor, and recall information in a way that confirms one's preexisting beliefs.",
                detection_patterns=[
                    r"only consider(?:ed|ing)?\s+evidence\s+that\s+supports",
                    r"ignor(?:e|ed|ing)\s+contrary evidence",
                    r"focus(?:ed|ing)?\s+exclusively on\s+supporting\s+(data|evidence|facts)"
                ],
                correction_strategies=[
                    "Actively seek evidence contrary to your hypothesis",
                    "Consider alternative explanations for the data",
                    "Apply the same standards of evaluation to supporting and contradicting evidence"
                ],
                examples=[
                    "These three studies confirm my theory, so I'll focus on them and ignore the two that don't.",
                    "The positive reviews clearly show this is a great product, regardless of some negative feedback."
                ],
                keywords=["confirm", "support", "prove", "validate", "reinforce", "substantiate", "agree with", "back up"],
                counter_keywords=["disconfirm", "falsify", "challenge", "contradict", "alternative explanation", "counterevidence", "on the other hand"],
                false_positive_patterns=[
                    r"avoid(?:ed|ing)?\s+confirmation\s+bias",
                    r"consider(?:ed|ing)?\s+both\s+supporting\s+and\s+contrary\s+evidence"
                ]
            ),
            "availability_bias": BiasType(
                name="Availability bias",
                category=BiasCategory.INFORMATION_PROCESSING,
                description="The tendency to overestimate the likelihood of events with greater 'availability' in memory, which can be influenced by recency or vividness.",
                detection_patterns=[
                    r"(?:recent|vivid|memorable|striking)\s+examples\s+suggest",
                    r"based\s+on\s+(?:my|our|the)\s+experience",
                    r"(?:comes|came)\s+to\s+mind\s+immediately"
                ],
                correction_strategies=[
                    "Consider systematic data rather than readily available examples",
                    "Look for base rates and statistical evidence",
                    "Question whether memorable examples are representative"
                ],
                examples=[
                    "Plane crashes are more dangerous than car accidents because they get more news coverage.",
                    "I remember three cases of that side effect, so it must be common."
                ],
                keywords=["remember", "recall", "comes to mind", "recent example", "vivid", "news coverage", "hear about", "personal experience"],
                counter_keywords=["statistical evidence", "systematic review", "base rate", "comprehensive data", "representative sample"],
                false_positive_patterns=[
                    r"avoid(?:ed|ing)?\s+availability\s+bias",
                    r"systematic(?:ally)?\s+collect(?:ed|ing)?\s+data"
                ]
            ),
            "anchoring_bias": BiasType(
                name="Anchoring bias",
                category=BiasCategory.INFORMATION_PROCESSING,
                description="The tendency to rely too heavily on the first piece of information encountered (the 'anchor') when making decisions.",
                detection_patterns=[
                    r"(?:start|starting|started)\s+with\s+(?:the|an?)\s+initial\s+(?:value|estimate|number)",
                    r"(?:based|built)\s+(?:on|upon)\s+(?:the|an?)\s+(?:original|initial|first)",
                    r"adjust(?:ed|ing)?\s+(?:from|based\s+on)\s+(?:the|an?)\s+(?:starting|initial)"
                ],
                correction_strategies=[
                    "Consider the problem from multiple starting points",
                    "Deliberately use different anchors and observe how they affect your judgment",
                    "Seek objective criteria independent of initial values"
                ],
                examples=[
                    "The initial price was $100, so $80 seems like a good deal even though the item is worth $40.",
                    "Starting from last year's budget of $1M, we should increase slightly to $1.1M."
                ],
                keywords=["initially", "starting point", "first estimate", "based on the original", "adjust from", "beginning with", "starting value"],
                counter_keywords=["independent assessment", "multiple starting points", "objective criteria", "disregard initial"],
                false_positive_patterns=[
                    r"avoid(?:ed|ing)?\s+anchoring\s+bias",
                    r"ignor(?:e|ed|ing)\s+(?:the|any)\s+initial\s+value"
                ]
            ),
            "overconfidence_bias": BiasType(
                name="Overconfidence bias",
                category=BiasCategory.INFORMATION_PROCESSING,
                description="The tendency to overestimate one's abilities, knowledge, or the accuracy of one's beliefs.",
                detection_patterns=[
                    r"(?:absolutely|definitely|certainly|clearly|obviously)\s+(?:true|right|correct)",
                    r"(?:no|zero|little)\s+(?:doubt|uncertainty|chance)",
                    r"(?:complete|full|100%)\s+(?:confidence|certainty)"
                ],
                correction_strategies=[
                    "Express confidence levels with appropriate uncertainty",
                    "Consider what evidence would change your mind",
                    "Estimate confidence intervals rather than point estimates"
                ],
                examples=[
                    "I'm absolutely certain this investment will yield at least 15% returns.",
                    "There's no question that my approach is the right one."
                ],
                keywords=["certain", "definitely", "absolutely", "no doubt", "indisputable", "undoubtedly", "obviously", "clearly", "100%", "guaranteed"],
                counter_keywords=["possibly", "perhaps", "uncertain", "probability", "likelihood", "estimate", "confidence interval", "may", "might"],
                false_positive_patterns=[
                    r"avoid(?:ed|ing)?\s+overconfidence",
                    r"express(?:ed|ing)?\s+appropriate\s+uncertainty"
                ]
            ),
            "fundamental_attribution_error": BiasType(
                name="Fundamental attribution error",
                category=BiasCategory.SOCIAL_INFLUENCE,
                description="The tendency to over-emphasize personality-based explanations for behaviors while under-emphasizing situational explanations.",
                detection_patterns=[
                    r"(?:because|due\s+to)\s+(?:their|his|her|its)\s+(?:personality|character|nature|disposition)",
                    r"(?:the\s+kind\s+of\s+person|type\s+of\s+individual)\s+who",
                    r"(?:lazy|careless|sloppy|irresponsible|smart|intelligent|clever)\s+(?:person|individual|people)"
                ],
                correction_strategies=[
                    "Consider situational factors that might explain the behavior",
                    "Ask what constraints or incentives the person faced",
                    "Consider how you might behave in the same situation"
                ],
                examples=[
                    "He failed the test because he's lazy and doesn't care about learning.",
                    "She's successful because she's naturally talented and ambitious."
                ],
                keywords=["personality", "character", "nature", "disposition", "inherent", "trait", "type of person", "always", "never"],
                counter_keywords=["situation", "circumstances", "context", "environment", "incentives", "constraints", "external factors"],
                false_positive_patterns=[
                    r"avoid(?:ed|ing)?\s+fundamental\s+attribution\s+error",
                    r"consider(?:ed|ing)?\s+situational\s+factors"
                ]
            ),
            "hindsight_bias": BiasType(
                name="Hindsight bias",
                category=BiasCategory.MEMORY,
                description="The tendency to perceive past events as having been predictable at the time they occurred, despite there having been little or no objective basis for predicting them.",
                detection_patterns=[
                    r"(?:should\s+have\s+(?:known|seen|predicted|anticipated))",
                    r"(?:obvious|evident|clear)\s+in\s+(?:hindsight|retrospect)",
                    r"(?:now|looking\s+back)\s+(?:it's|it\s+is|it\s+was)\s+(?:obvious|clear|evident)"
                ],
                correction_strategies=[
                    "Recall your actual predictions before the outcome was known",
                    "Consider alternative outcomes that seemed plausible at the time",
                    "Acknowledge the role of unpredictable factors"
                ],
                examples=[
                    "It was obvious this investment would fail; the warning signs were clear.",
                    "We should have seen that market crash coming; all the indicators were there."
                ],
                keywords=["obvious in retrospect", "should have seen it coming", "inevitable", "predictable", "looking back", "hindsight", "retrospect"],
                counter_keywords=["unpredictable", "uncertain at the time", "multiple possible outcomes", "no way to know", "probabilistic"],
                false_positive_patterns=[
                    r"avoid(?:ed|ing)?\s+hindsight\s+bias",
                    r"at\s+the\s+time,\s+(?:it\s+was|there\s+was)\s+(?:uncertainty|unclear)"
                ]
            ),
            "framing_effect": BiasType(
                name="Framing effect",
                category=BiasCategory.DECISION_MAKING,
                description="The tendency to draw different conclusions from the same information, depending on how that information is presented or 'framed'.",
                detection_patterns=[
                    r"(?:described|presented|framed)\s+(?:as|in\s+terms\s+of)\s+(?:a\s+gain|gains|winning|benefits)",
                    r"(?:described|presented|framed)\s+(?:as|in\s+terms\s+of)\s+(?:a\s+loss|losses|losing|costs)",
                    r"(?:focused|emphasis|attention)\s+on\s+(?:positive|negative)\s+aspects"
                ],
                correction_strategies=[
                    "Reframe the problem in both positive and negative terms",
                    "Focus on absolute outcomes rather than relative gains or losses",
                    "Consider multiple perspectives and descriptions of the same situation"
                ],
                examples=[
                    "The medical procedure has a 90% survival rate (vs. focusing on the 10% mortality rate).",
                    "You save $20 with this discount (vs. you avoid losing $20)."
                ],
                keywords=["gain", "loss", "save", "cost", "benefit", "risk", "positive", "negative", "frame", "perspective", "way of looking"],
                counter_keywords=["multiple perspectives", "both frames", "alternative description", "another way to view this", "absolute outcomes"],
                false_positive_patterns=[
                    r"avoid(?:ed|ing)?\s+(?:the)?\s+framing\s+effect",
                    r"consider(?:ed|ing)?\s+both\s+(?:positive|negative)\s+frame"
                ]
            ),
            "sunk_cost_fallacy": BiasType(
                name="Sunk cost fallacy",
                category=BiasCategory.DECISION_MAKING,
                description="The tendency to continue an endeavor due to previously invested resources (time, money, effort) despite new evidence suggesting it is not the best choice.",
                detection_patterns=[
                    r"(?:already|have)\s+invested\s+(?:so\s+much|too\s+much|a\s+lot)",
                    r"(?:can't|cannot)\s+(?:back\s+out|give\s+up|stop)\s+now",
                    r"(?:after|given)\s+all\s+(?:the|this)\s+(?:time|money|effort|resources|work)"
                ],
                correction_strategies=[
                    "Evaluate future costs and benefits, ignoring past investments",
                    "Ask if you would start this project today given what you know now",
                    "Consider the opportunity cost of continuing"
                ],
                examples=[
                    "I've already spent $5,000 on repairing this car, so I should keep investing in it.",
                    "We've devoted three years to this project, so we need to finish it regardless of new alternatives."
                ],
                keywords=["already invested", "committed resources", "spent too much", "can't back out now", "given all the time", "too far along"],
                counter_keywords=["future prospects", "forward-looking", "opportunity cost", "alternate uses", "regardless of past investments"],
                false_positive_patterns=[
                    r"avoid(?:ed|ing)?\s+(?:the)?\s+sunk\s+cost\s+fallacy",
                    r"despite\s+past\s+investments"
                ]
            ),
            "representativeness_heuristic": BiasType(
                name="Representativeness heuristic",
                category=BiasCategory.PROBABILITY_ESTIMATION,
                description="The tendency to judge the probability or frequency of a hypothesis by considering how much the hypothesis resembles available data rather than using statistical reasoning.",
                detection_patterns=[
                    r"(?:looks|seems|appears)\s+like\s+a\s+typical",
                    r"(?:matches|fits)\s+the\s+(?:profile|pattern|stereotype)",
                    r"(?:similar\s+to|resembles)\s+(?:the\s+usual|typical|common)"
                ],
                correction_strategies=[
                    "Consider base rates and prior probabilities",
                    "Ask for statistical evidence rather than relying on pattern matching",
                    "Be wary of stereotypes and typical cases"
                ],
                examples=[
                    "He wears glasses and likes math, so he's probably a programmer.",
                    "This company has the characteristics of successful startups, so it will likely succeed."
                ],
                keywords=["typical", "stereotype", "profile", "pattern", "resembles", "looks like", "characteristic of", "representative of"],
                counter_keywords=["base rate", "statistical evidence", "prior probability", "actual frequency", "quantitative analysis"],
                false_positive_patterns=[
                    r"avoid(?:ed|ing)?\s+(?:the)?\s+representativeness\s+heuristic",
                    r"consider(?:ed|ing)?\s+(?:the)?\s+base\s+rate"
                ]
            ),
            "base_rate_fallacy": BiasType(
                name="Base rate fallacy",
                category=BiasCategory.PROBABILITY_ESTIMATION,
                description="The tendency to ignore base rate information and focus exclusively on case-specific information when making probabilistic judgments.",
                detection_patterns=[
                    r"(?:despite|regardless\s+of)\s+(?:the|its)\s+(?:rarity|uncommon|infrequent)",
                    r"(?:ignores|overlooks|disregards)\s+(?:how\s+(?:common|rare)|prevalence|frequency)",
                    r"(?:diagnostic|test|specific)\s+evidence\s+(?:overrides|outweighs|is\s+more\s+important\s+than)"
                ],
                correction_strategies=[
                    "Start with the base rate as your prior probability",
                    "Use Bayes' theorem to incorporate both base rates and specific evidence",
                    "Articulate both the probability of evidence given hypothesis and probability of hypothesis in population"
                ],
                examples=[
                    "The test is 99% accurate, so if you test positive, you're 99% likely to have the disease (ignoring that the disease affects only 1 in 10,000 people).",
                    "The witness identified the blue cab correctly, so it was probably a blue cab (even though 85% of cabs in the city are green)."
                ],
                keywords=["test accuracy", "reliability", "specific case", "particular instance", "detected", "identified", "characteristic"],
                counter_keywords=["base rate", "prevalence", "prior probability", "population frequency", "Bayes' theorem", "underlying rate"],
                false_positive_patterns=[
                    r"avoid(?:ed|ing)?\s+(?:the)?\s+base\s+rate\s+fallacy",
                    r"consider(?:ed|ing)?\s+(?:the)?\s+(?:prevalence|frequency|base\s+rate)"
                ]
            ),
        }
        
        # Additional biases could be defined here
        
    def _init_detectors(self):
        """Initialize detectors for each bias type."""
        self.detectors = {}
        
        # For each bias type, create appropriate detectors
        for bias_id, bias_type in self.bias_types.items():
            # Create component detectors
            keyword_detector = KeywordBiasDetector(bias_type)
            pattern_detector = PatternBiasDetector(bias_type)
            stat_detector = StatisticalBiasDetector(bias_type)
            
            # Create neural detector if language model is available
            neural_detector = None
            if self.language_model and self.config.get("use_neural_detectors", True):
                neural_detector = NeuralBiasDetector(bias_type)
                
            # Create hybrid detector combining the components
            component_detectors = [keyword_detector, pattern_detector, stat_detector]
            if neural_detector:
                component_detectors.append(neural_detector)
                
            hybrid_detector = HybridBiasDetector(bias_type, component_detectors)
            
            # Store the detector
            self.detectors[bias_id] = hybrid_detector
            
    def _init_corrector(self):
        """Initialize the bias corrector."""
        if self.language_model:
            self.bias_corrector = HybridBiasCorrector(self.language_model)
        else:
            # Fallback to rule-based correction if no language model
            self.bias_corrector = RuleBasedBiasCorrector()
            
    def detect_biases(self, reasoning_text: str, threshold: float = 0.7) -> List[BiasDetectionResult]:
        """
        Detect cognitive biases in the reasoning text.
        
        Args:
            reasoning_text: The reasoning text to analyze
            threshold: Confidence threshold for bias detection (0-1)
            
        Returns:
            List of detected biases with their severity and evidence
        """
        if not reasoning_text or len(reasoning_text.strip()) == 0:
            return []
            
        # Run all detectors
        detected_biases = []
        for bias_id, detector in self.detectors.items():
            detector.threshold = threshold  # Set detection threshold
            result = detector.detect(reasoning_text)
            if result:
                detected_biases.append(result)
                
        # Sort by severity and confidence
        detected_biases.sort(key=lambda x: (x.severity * x.confidence), reverse=True)
        
        # Add neuromorphic validation if available
        if self.neuromorphic_core and self.config.get("use_neuromorphic_validation", False):
            detected_biases = self._validate_with_neuromorphic_core(reasoning_text, detected_biases)
            
        # Record detection for performance tracking
        self._record_detection(reasoning_text, detected_biases)
        
        return detected_biases
        
    def _validate_with_neuromorphic_core(self, reasoning_text: str, detected_biases: List[BiasDetectionResult]) -> List[BiasDetectionResult]:
        """
        Use the neuromorphic core to validate and refine bias detection results.
        This leverages the pattern recognition capabilities of the neuromorphic system.
        
        Args:
            reasoning_text: Original reasoning text
            detected_biases: Initial bias detection results
            
        Returns:
            Refined bias detection results
        """
        try:
            # Skip if no neuromorphic core
            if not self.neuromorphic_core:
                return detected_biases
                
            # Prepare input for neuromorphic core
            input_data = {
                "text": reasoning_text,
                "detected_biases": [result.to_dict() for result in detected_biases]
            }
            
            # Process through neuromorphic core (implementation depends on core interface)
            refined_results = self.neuromorphic_core.process_cognitive_bias(input_data)
            
            # Update detection results based on neuromorphic processing
            updated_biases = []
            for result in detected_biases:
                bias_name = result.bias_type.name
                if bias_name in refined_results:
                    # Update confidence and severity based on neuromorphic assessment
                    refinement = refined_results[bias_name]
                    result.confidence = (result.confidence + refinement.get("confidence", result.confidence)) / 2
                    result.severity = (result.severity + refinement.get("severity", result.severity)) / 2
                    
                    # Only keep if still above threshold
                    if result.confidence * result.severity > result.bias_type.threshold:
                        updated_biases.append(result)
                else:
                    # Neuromorphic core didn't detect this bias, reduce confidence
                    result.confidence *= 0.8
                    if result.confidence * result.severity > result.bias_type.threshold:
                        updated_biases.append(result)
                        
            return updated_biases
            
        except Exception as e:
            self.logger.warning(f"Error in neuromorphic validation: {e}")
            return detected_biases
            
    def _record_detection(self, reasoning_text: str, detected_biases: List[BiasDetectionResult]):
        """Record bias detection for performance tracking."""
        detection_record = {
            "timestamp": time.time(),
            "text_length": len(reasoning_text),
            "detected_biases": [result.to_dict() for result in detected_biases],
            "bias_types": [result.bias_type.name for result in detected_biases]
        }
        self.detection_history.append(detection_record)
        
    def correct_biased_reasoning(
        self, 
        reasoning_text: str, 
        detected_biases: Optional[List[BiasDetectionResult]] = None,
        threshold: float = 0.7
    ) -> str:
        """
        Correct cognitive biases in the reasoning text.
        
        Args:
            reasoning_text: The reasoning text to correct
            detected_biases: Pre-detected biases, or None to detect them first
            threshold: Confidence threshold for bias detection if not pre-detected
            
        Returns:
            Reasoning text with biases corrected
        """
        # Detect biases if not provided
        if detected_biases is None:
            detected_biases = self.detect_biases(reasoning_text, threshold)
            
        # Return original text if no biases detected
        if not detected_biases:
            return reasoning_text
            
        # Apply bias correction
        corrected_text = self.bias_corrector.correct(reasoning_text, detected_biases)
        
        return corrected_text
        
    def analyze_reasoning(self, reasoning_text: str) -> Dict[str, Any]:
        """
        Perform comprehensive bias analysis of reasoning.
        
        Args:
            reasoning_text: The reasoning text to analyze
            
        Returns:
            Dictionary with detailed bias analysis
        """
        # Detect biases
        detected_biases = self.detect_biases(reasoning_text)
        
        # If no biases detected, return minimal analysis
        if not detected_biases:
            return {
                "has_bias": False,
                "bias_count": 0,
                "bias_types": [],
                "overall_bias_severity": 0.0,
                "corrected_reasoning": reasoning_text
            }
            
        # Correct biases
        corrected_reasoning = self.correct_biased_reasoning(reasoning_text, detected_biases)
        
        # Calculate overall bias metrics
        overall_severity = sum(result.severity * result.confidence for result in detected_biases) / len(detected_biases)
        bias_by_category = defaultdict(list)
        for result in detected_biases:
            bias_by_category[result.bias_type.category.name].append(result.bias_type.name)
            
        # Prepare detailed bias report
        bias_details = []
        for result in detected_biases:
            bias_details.append({
                "type": result.bias_type.name,
                "category": result.bias_type.category.name,
                "description": result.bias_type.description,
                "severity": result.severity,
                "confidence": result.confidence,
                "evidence": result.evidence[:5],  # Limit to top 5 pieces of evidence
                "correction_strategies": result.bias_type.correction_strategies
            })
            
        return {
            "has_bias": True,
            "bias_count": len(detected_biases),
            "bias_types": [result.bias_type.name for result in detected_biases],
            "bias_categories": dict(bias_by_category),
            "overall_bias_severity": overall_severity,
            "bias_details": bias_details,
            "corrected_reasoning": corrected_reasoning
        }
        
    def get_bias_categories(self) -> Dict[str, List[str]]:
        """
        Get a mapping of bias categories to bias types.
        
        Returns:
            Dictionary mapping each category to a list of bias types
        """
        categories = defaultdict(list)
        for bias_id, bias_type in self.bias_types.items():
            categories[bias_type.category.name].append(bias_type.name)
            
        return dict(categories)
        
    def get_bias_descriptions(self) -> Dict[str, str]:
        """
        Get descriptions for all bias types.
        
        Returns:
            Dictionary mapping bias names to descriptions
        """
        return {bias_type.name: bias_type.description for _, bias_type in self.bias_types.items()}
        
    def generate_bias_report(self, reasoning_text: str, output_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate a comprehensive bias analysis report, optionally with visualization.
        
        Args:
            reasoning_text: The reasoning text to analyze
            output_path: Path to save visualizations (if None, no visualizations are generated)
            
        Returns:
            Dictionary with bias analysis and report data
        """
        # Get basic analysis
        analysis = self.analyze_reasoning(reasoning_text)
        
        # Generate visualizations if output path is provided
        if output_path and analysis["has_bias"]:
            # Generate and save visualizations
            try:
                self._generate_bias_visualizations(analysis, output_path)
                analysis["visualizations"] = {
                    "bias_distribution": f"{output_path}/bias_distribution.png",
                    "bias_severity": f"{output_path}/bias_severity.png",
                    "text_heatmap": f"{output_path}/text_heatmap.png"
                }
            except Exception as e:
                self.logger.error(f"Error generating visualizations: {e}")
                analysis["visualizations"] = None
                
        # Add overall report summary
        analysis["summary"] = self._generate_report_summary(analysis)
        
        return analysis
        
    def _generate_bias_visualizations(self, analysis: Dict[str, Any], output_path: str):
        """Generate visualizations for bias analysis."""
        # Ensure output directory exists
        os.makedirs(output_path, exist_ok=True)
        
        # 1. Bias distribution by category
        if analysis["bias_categories"]:
            plt.figure(figsize=(10, 6))
            categories = list(analysis["bias_categories"].keys())
            counts = [len(biases) for biases in analysis["bias_categories"].values()]
            
            colors = plt.cm.viridis(np.linspace(0, 1, len(categories)))
            plt.bar(categories, counts, color=colors)
            plt.title("Cognitive Bias Distribution by Category")
            plt.xlabel("Bias Category")
            plt.ylabel("Count")
            plt.xticks(rotation=45, ha="right")
            plt.tight_layout()
            plt.savefig(f"{output_path}/bias_distribution.png")
            plt.close()
            
        # 2. Bias severity by type
        if analysis["bias_details"]:
            plt.figure(figsize=(12, 7))
            bias_types = [detail["type"] for detail in analysis["bias_details"]]
            severities = [detail["severity"] for detail in analysis["bias_details"]]
            confidences = [detail["confidence"] for detail in analysis["bias_details"]]
            
            x = range(len(bias_types))
            plt.bar(x, severities, alpha=0.7, color="blue", label="Severity")
            plt.bar(x, confidences, alpha=0.7, color="orange", label="Detection Confidence")
            
            plt.axhline(y=0.7, color="red", linestyle="--", label="Threshold")
            plt.title("Cognitive Bias Severity and Detection Confidence")
            plt.xlabel("Bias Type")
            plt.ylabel("Score (0-1)")
            plt.xticks(x, bias_types, rotation=45, ha="right")
            plt.legend()
            plt.tight_layout()
            plt.savefig(f"{output_path}/bias_severity.png")
            plt.close()
            
        # 3. Text heatmap showing biased segments
        # This would require more complex rendering, so we'll create a simple visualization
        # showing which parts of the text contain biases
        if "corrected_reasoning" in analysis:
            original_text = analysis.get("original_reasoning", "")
            if not original_text and "bias_details" in analysis:
                # Try to reconstruct from bias details - this is an approximation
                bias_segments = []
                for detail in analysis["bias_details"]:
                    for evidence in detail["evidence"]:
                        # Extract quoted text from evidence
                        match = re.search(r"'(.*?)'", evidence)
                        if match:
                            bias_segments.append(match.group(1))
                            
                # Create simple text chart
                fig, ax = plt.subplots(figsize=(14, 8))
                ax.axis("off")
                
                for i, segment in enumerate(bias_segments[:10]):  # Limit to 10 segments
                    ax.text(0.05, 0.95 - i*0.08, segment, fontsize=10, 
                            bbox=dict(facecolor="lightcoral", alpha=0.5))
                    
                plt.title("Biased Text Segments")
                plt.tight_layout()
                plt.savefig(f"{output_path}/text_heatmap.png")
                plt.close()
                
    def _generate_report_summary(self, analysis: Dict[str, Any]) -> str:
        """Generate a textual summary of the bias analysis."""
        if not analysis["has_bias"]:
            return "No cognitive biases were detected in the reasoning."
            
        summary = f"Analysis detected {analysis['bias_count']} cognitive biases with an overall severity of {analysis['overall_bias_severity']:.2f}.\n\n"
        
        # Summarize bias categories
        if "bias_categories" in analysis:
            summary += "Biases by category:\n"
            for category, biases in analysis["bias_categories"].items():
                summary += f"- {category}: {', '.join(biases)}\n"
                
        # Add most severe bias details
        if "bias_details" in analysis:
            most_severe = max(analysis["bias_details"], key=lambda x: x["severity"])
            summary += f"\nMost severe bias: {most_severe['type']} (severity: {most_severe['severity']:.2f})\n"
            summary += f"Description: {most_severe['description']}\n"
            summary += "Evidence:\n"
            for i, evidence in enumerate(most_severe["evidence"][:3]):
                summary += f"- {evidence}\n"
                
            summary += "Correction strategies:\n"
            for i, strategy in enumerate(most_severe["correction_strategies"][:3]):
                summary += f"- {strategy}\n"
                
        return summary
    
    def fine_tune_detectors(self, training_examples: Dict[str, List[Tuple[str, bool]]], epochs: int = 3):
        """
        Fine-tune bias detectors using labeled examples.
        
        Args:
            training_examples: Dictionary mapping bias_id to list of (text, is_biased) examples
            epochs: Number of training epochs
        """
        if not self.language_model:
            self.logger.warning("Cannot fine-tune detectors without language model")
            return
            
        for bias_id, examples in training_examples.items():
            if bias_id not in self.detectors:
                self.logger.warning(f"Unknown bias type: {bias_id}")
                continue
                
            # Find the neural detector in the hybrid detector
            hybrid_detector = self.detectors[bias_id]
            neural_detector = None
            for detector in hybrid_detector.detectors:
                if isinstance(detector, NeuralBiasDetector):
                    neural_detector = detector
                    break
                    
            if neural_detector:
                self.logger.info(f"Fine-tuning detector for {bias_id} with {len(examples)} examples")
                neural_detector.fine_tune(examples, epochs)
            else:
                self.logger.warning(f"No neural detector found for {bias_id}")
                
    def update_detection_thresholds(self, feedback_data: List[Tuple[str, List[str], List[str]]]):
        """
        Update detection thresholds based on feedback.
        
        Args:
            feedback_data: List of (text, correctly_detected_biases, incorrectly_detected_biases)
        """
        # Track performance for each detector
        true_positives = defaultdict(int)
        false_positives = defaultdict(int)
        
        for text, correct_biases, incorrect_biases in feedback_data:
            # Run detection with low threshold to get all potential biases
            all_detected = self.detect_biases(text, threshold=0.3)
            detected_names = [result.bias_type.name for result in all_detected]
            
            # Count true and false positives
            for bias in correct_biases:
                for result in all_detected:
                    if result.bias_type.name == bias:
                        true_positives[bias] += 1
                        
            for bias in incorrect_biases:
                for result in all_detected:
                    if result.bias_type.name == bias:
                        false_positives[bias] += 1
                        
        # Update thresholds based on performance
        for bias_id, bias_type in self.bias_types.items():
            bias_name = bias_type.name
            tp = true_positives.get(bias_name, 0)
            fp = false_positives.get(bias_name, 0)
            
            if tp + fp > 0:
                precision = tp / (tp + fp)
                
                # Adjust threshold to balance precision
                if precision < 0.7 and fp > tp:
                    # Poor precision, increase threshold
                    new_threshold = min(0.9, self.detectors[bias_id].threshold + 0.05)
                elif precision > 0.9 and tp > fp * 3:
                    # High precision, can lower threshold slightly
                    new_threshold = max(0.5, self.detectors[bias_id].threshold - 0.03)
                else:
                    # Keep current threshold
                    new_threshold = self.detectors[bias_id].threshold
                    
                self.logger.info(f"Adjusting threshold for {bias_name}: {self.detectors[bias_id].threshold:.2f} -> {new_threshold:.2f} (precision: {precision:.2f})")
                self.detectors[bias_id].threshold = new_threshold
                
    def get_performance_metrics(self) -> Dict[str, Any]:
        """
        Get performance metrics for the bias detection system.
        
        Returns:
            Dictionary with performance metrics
        """
        if not self.detection_history:
            return {"total_detections": 0}
            
        # Count bias types
        bias_counts = Counter()
        for record in self.detection_history:
            for bias in record["bias_types"]:
                bias_counts[bias] += 1
                
        # Calculate timing metrics
        timestamps = [record["timestamp"] for record in self.detection_history]
        detection_rates = []
        for i in range(1, len(timestamps)):
            time_diff = timestamps[i] - timestamps[i-1]
            if time_diff > 0:
                detection_rates.append(1 / time_diff)
                
        avg_detection_rate = sum(detection_rates) / len(detection_rates) if detection_rates else 0
        
        # Calculate common co-occurrences
        co_occurrences = Counter()
        for record in self.detection_history:
            biases = record["bias_types"]
            for i, bias1 in enumerate(biases):
                for bias2 in biases[i+1:]:
                    co_occurrences[(bias1, bias2)] += 1
                    
        top_co_occurrences = co_occurrences.most_common(5)
        
        return {
            "total_detections": len(self.detection_history),
            "unique_bias_types_detected": len(bias_counts),
            "bias_counts": dict(bias_counts),
            "avg_detection_rate": avg_detection_rate,
            "top_co_occurrences": [
                {"biases": list(pair), "count": count}
                for pair, count in top_co_occurrences
            ]
        }
        
    def reset_history(self):
        """Clear detection history."""
        self.detection_history = []
        
    def save_state(self, filepath: str):
        """Save current state to file."""
        import pickle
        state = {
            "detection_history": self.detection_history,
            "detector_thresholds": {bid: detector.threshold for bid, detector in self.detectors.items()}
        }
        with open(filepath, 'wb') as f:
            pickle.dump(state, f)
        self.logger.info(f"Bias detection state saved to {filepath}")
        
    def load_state(self, filepath: str):
        """Load state from file."""
        import pickle
        try:
            with open(filepath, 'rb') as f:
                state = pickle.load(f)
                
            self.detection_history = state.get("detection_history", [])
            
            # Restore thresholds
            for bias_id, threshold in state.get("detector_thresholds", {}).items():
                if bias_id in self.detectors:
                    self.detectors[bias_id].threshold = threshold
                    
            self.logger.info(f"Bias detection state loaded from {filepath}")
            return True
        except Exception as e:
            self.logger.error(f"Error loading state: {e}")
            return False

# Make key classes available at module level
__all__ = [
    'BiasDetection',
    'BiasCategory',
    'BiasType',
    'BiasDetectionResult',
    'BiasDetector',
    'KeywordBiasDetector',
    'PatternBiasDetector',
    'StatisticalBiasDetector',
    'NeuralBiasDetector',
    'HybridBiasDetector',
    'BiasCorrector',
    'PromptBasedBiasCorrector',
    'SegmentBasedBiasCorrector',
    'RuleBasedBiasCorrector',
    'HybridBiasCorrector'
]

# Optional initialization function to create a default bias detection instance
def create_default_bias_detection(language_model=None, neuromorphic_core=None):
    """
    Create a default bias detection instance with standard configuration.
    
    Args:
        language_model: Optional language model for text generation
        neuromorphic_core: Optional neuromorphic core for neural processing
        
    Returns:
        Configured BiasDetection instance
    """
    config = {
        "log_level": logging.INFO,
        "use_neural_detectors": language_model is not None,
        "use_neuromorphic_validation": neuromorphic_core is not None
    }
    
    return BiasDetection(
        language_model=language_model,
        neuromorphic_core=neuromorphic_core,
        config=config
    )

# Main execution for standalone usage and testing
if __name__ == "__main__":
    # Example usage
    example_text = """
    I'm absolutely certain that this approach is correct because it aligns with what I've seen in the past.
    Since we've already invested so much time in this project, we have to see it through to completion.
    This person failed because they're lazy and unmotivated, not because of any external circumstances.
    """
    
    # Create a bias detection instance
    bias_detection = BiasDetection()
    
    # Detect biases
    detected_biases = bias_detection.detect_biases(example_text)
    
    # Print detected biases
    print(f"Detected {len(detected_biases)} cognitive biases:")
    for bias_result in detected_biases:
        print(f"- {bias_result.bias_type.name} (severity: {bias_result.severity:.2f}, confidence: {bias_result.confidence:.2f})")
        for evidence in bias_result.evidence[:2]:  # Show first 2 pieces of evidence
            print(f"  * {evidence}")
    
    # Correct biased reasoning
    corrected_text = bias_detection.correct_biased_reasoning(example_text, detected_biases)
    
    print("\nCorrected reasoning:")
    print(corrected_text)