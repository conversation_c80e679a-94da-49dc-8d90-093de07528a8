"""
ULTRA System Initialization
===========================

Comprehensive system initialization for the ULTRA AGI framework.
Handles startup sequence, component initialization, dependency management,
and system health verification.
"""

import os
import sys
import time
import logging
import threading
import traceback
from typing import Dict, Any, List, Optional, Tuple, Callable
from pathlib import Path
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import importlib
import inspect
import gc

# Add the current directory to Python path for direct script execution
if __name__ == "__main__":
    current_dir = Path(__file__).parent
    sys.path.insert(0, str(current_dir))

# Import ULTRA configuration and utilities with fallback for different execution contexts
try:
    # Try relative import first (when used as a module)
    from ultra.config import (
        load_configuration, 
        get_config, 
        ConfigEnvironment,
        optimize_for_environment
    )
except ImportError:
    try:
        # Try absolute import from the config package
        from config import (
            load_configuration, 
            get_config, 
            ConfigEnvironment,
            optimize_for_environment
        )
    except ImportError:
        # Manual fallback - import from local config directory
        current_dir = Path(__file__).parent
        config_dir = current_dir / "config"
        sys.path.insert(0, str(config_dir))
        from __init__ import (
            load_configuration, 
            get_config, 
            ConfigEnvironment,
            optimize_for_environment
        )

try:
    from ultra.utils.ultra_logging import (
        get_logger, 
        LogCategory, 
        LogLevel,
        setup_default_logging
    )
except ImportError:
    try:
        from utils.ultra_logging import (
            get_logger, 
            LogCategory, 
            LogLevel,
            setup_default_logging
        )
    except ImportError:
        # Fallback to basic logging
        def get_logger(name):
            return logging.getLogger(name)
        
        class LogCategory:
            SYSTEM = "system"
            NEURAL = "neural"
            CONFIG = "config"
            
        class LogLevel:
            DEBUG = logging.DEBUG
            INFO = logging.INFO
            WARNING = logging.WARNING
            ERROR = logging.ERROR
            
        def setup_default_logging():
            logging.basicConfig(level=logging.INFO)

try:
    from ultra.utils.monitoring import (
        initialize_monitoring,
        get_global_monitor,
        SubsystemStatus
    )
except ImportError:
    try:
        from utils.monitoring import (
            initialize_monitoring,
            get_global_monitor,
            SubsystemStatus
        )
    except ImportError:
        # Fallback monitoring stubs
        def initialize_monitoring():
            pass
        
        def get_global_monitor():
            return None
        
        class SubsystemStatus:
            HEALTHY = "healthy"
            DEGRADED = "degraded"
            FAILED = "failed"

try:
    from ultra.utils.visualization import (
        initialize_visualization,
        get_visualization_system
    )
except ImportError:
    try:
        from utils.visualization import (
            initialize_visualization,
            get_visualization_system
        )
    except ImportError:
        # Fallback visualization stubs
        def initialize_visualization():
            pass
        
        def get_visualization_system():
            return None

class InitializationStage(Enum):
    """System initialization stages"""
    CONFIGURATION = "configuration"
    LOGGING = "logging" 
    MONITORING = "monitoring"
    VISUALIZATION = "visualization"
    DEPENDENCIES = "dependencies"
    CORE_NEURAL = "core_neural"
    HYPER_TRANSFORMER = "hyper_transformer"
    DIFFUSION_REASONING = "diffusion_reasoning"
    META_COGNITIVE = "meta_cognitive"
    NEUROMORPHIC = "neuromorphic"
    CONSCIOUSNESS = "consciousness"
    NEURO_SYMBOLIC = "neuro_symbolic"
    SELF_EVOLUTION = "self_evolution"
    INTEGRATION = "integration"
    HEALTH_CHECKS = "health_checks"
    READY = "ready"

class InitializationError(Exception):
    """Custom exception for initialization errors"""
    pass

@dataclass
class ComponentInfo:
    """Information about a system component"""
    name: str
    module_path: str
    class_name: str
    config_key: str
    dependencies: List[str] = field(default_factory=list)
    optional: bool = False
    initialization_timeout: float = 30.0
    health_check_interval: float = 60.0

@dataclass
class InitializationResult:
    """Result of component initialization"""
    component: str
    success: bool
    instance: Optional[Any] = None
    error: Optional[str] = None
    initialization_time: float = 0.0
    memory_usage: float = 0.0

class DependencyResolver:
    """Resolves component dependencies and determines initialization order"""
    
    def __init__(self, components: Dict[str, ComponentInfo]):
        self.components = components
        self.dependency_graph = self._build_dependency_graph()
        
    def _build_dependency_graph(self) -> Dict[str, List[str]]:
        """Build dependency graph from component information"""
        graph = {}
        
        for name, component in self.components.items():
            graph[name] = component.dependencies.copy()
            
        return graph
        
    def get_initialization_order(self) -> List[str]:
        """Get components in dependency-resolved initialization order"""
        visited = set()
        temp_visited = set()
        order = []
        
        def visit(node: str):
            if node in temp_visited:
                raise InitializationError(f"Circular dependency detected involving {node}")
            if node in visited:
                return
                
            temp_visited.add(node)
            
            # Visit dependencies first
            for dependency in self.dependency_graph.get(node, []):
                if dependency in self.components:
                    visit(dependency)
                    
            temp_visited.remove(node)
            visited.add(node)
            order.append(node)
            
        # Visit all components
        for component_name in self.components:
            if component_name not in visited:
                visit(component_name)
                
        return order
        
    def validate_dependencies(self) -> List[str]:
        """Validate that all dependencies exist"""
        missing = []
        
        for name, component in self.components.items():
            for dependency in component.dependencies:
                if dependency not in self.components:
                    missing.append(f"Component {name} depends on missing component {dependency}")
                    
        return missing

class HealthChecker:
    """System health checking and monitoring"""
    
    def __init__(self):
        self.health_checks = {}
        self.check_results = {}
        self.running = False
        self.thread = None
        
    def register_health_check(self, component: str, check_func: Callable[[], Tuple[bool, str]]):
        """Register a health check function for a component"""
        self.health_checks[component] = check_func
        
    def run_all_checks(self) -> Dict[str, Tuple[bool, str]]:
        """Run all registered health checks"""
        results = {}
        
        for component, check_func in self.health_checks.items():
            try:
                is_healthy, message = check_func()
                results[component] = (is_healthy, message)
            except Exception as e:
                results[component] = (False, f"Health check failed: {str(e)}")
                
        self.check_results = results
        return results
        
    def get_system_health(self) -> Tuple[bool, Dict[str, Tuple[bool, str]]]:
        """Get overall system health status"""
        results = self.run_all_checks()
        overall_healthy = all(result[0] for result in results.values())
        return overall_healthy, results
        
    def start_continuous_monitoring(self, interval: float = 60.0):
        """Start continuous health monitoring"""
        if not self.running:
            self.running = True
            self.thread = threading.Thread(
                target=self._monitoring_loop, 
                args=(interval,), 
                daemon=True
            )
            self.thread.start()
            
    def stop_monitoring(self):
        """Stop continuous health monitoring"""
        self.running = False
        if self.thread:
            self.thread.join()
            
    def _monitoring_loop(self, interval: float):
        """Main monitoring loop"""
        while self.running:
            try:
                self.run_all_checks()
                time.sleep(interval)
            except Exception as e:
                logging.error(f"Health monitoring error: {e}")
                time.sleep(interval)

class ComponentLoader:
    """Dynamic component loading and instantiation"""
    
    def __init__(self, logger):
        self.logger = logger
        self.loaded_modules = {}
        
    def load_component(self, component_info: ComponentInfo, config: Dict[str, Any]) -> InitializationResult:
        """Load and initialize a component"""
        start_time = time.time()
        start_memory = self._get_memory_usage()
        
        try:
            # Import module
            module = self._import_module(component_info.module_path)
            
            # Get component class
            component_class = getattr(module, component_info.class_name)
            
            # Get component configuration
            component_config = config.get(component_info.config_key, {})
            
            # Initialize component
            if inspect.signature(component_class.__init__).parameters:
                # Component accepts configuration
                try:
                    instance = component_class(**component_config)
                except TypeError:
                    # Fallback: pass config as single argument
                    instance = component_class(component_config)
            else:
                # Component has no configuration parameters
                instance = component_class()
                
            end_time = time.time()
            end_memory = self._get_memory_usage()
            
            self.logger.info(f"Successfully initialized {component_info.name}")
            
            return InitializationResult(
                component=component_info.name,
                success=True,
                instance=instance,
                initialization_time=end_time - start_time,
                memory_usage=end_memory - start_memory
            )
            
        except Exception as e:
            end_time = time.time()
            error_msg = f"Failed to initialize {component_info.name}: {str(e)}"
            self.logger.error(error_msg)
            self.logger.debug(traceback.format_exc())
            
            return InitializationResult(
                component=component_info.name,
                success=False,
                error=error_msg,
                initialization_time=end_time - start_time
            )
            
    def _import_module(self, module_path: str):
        """Import module with caching"""
        if module_path not in self.loaded_modules:
            try:
                self.loaded_modules[module_path] = importlib.import_module(module_path)
            except ImportError as e:
                raise ImportError(f"Could not import module {module_path}: {str(e)}")
                
        return self.loaded_modules[module_path]
        
    def _get_memory_usage(self) -> float:
        """Get current memory usage in MB"""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss / (1024**2)
        except ImportError:
            return 0.0

class ULTRASystemInitializer:
    """
    Main system initializer for ULTRA AGI framework
    
    Manages the complete initialization sequence including:
    - Configuration loading and validation
    - Logging system setup
    - Monitoring and visualization initialization
    - Component loading with dependency resolution
    - Health check registration
    - System integration and validation
    """
    
    def __init__(self, 
                 config_dir: Optional[Path] = None,
                 environment: Optional[str] = None,
                 enable_monitoring: bool = True,
                 enable_visualization: bool = True,
                 enable_health_checks: bool = True):
        
        self.config_dir = config_dir
        self.environment = environment
        self.enable_monitoring = enable_monitoring
        self.enable_visualization = enable_visualization
        self.enable_health_checks = enable_health_checks
        
        # Initialization state
        self.current_stage = None
        self.initialized_components = {}
        self.initialization_results = {}
        self.start_time = None
        
        # Core systems
        self.config_manager = None
        self.logger = None
        self.monitor = None
        self.visualizer = None
        self.health_checker = HealthChecker()
        
        # Component management
        self.component_loader = None
        self.dependency_resolver = None
        
        # Define component specifications
        self.component_specs = self._define_component_specs()
        
    def _define_component_specs(self) -> Dict[str, ComponentInfo]:
        """Define specifications for all ULTRA components"""
        return {
            'core_neural': ComponentInfo(
                name='Core Neural Architecture',
                module_path='ultra.core_neural',
                class_name='CoreNeuralArchitecture',
                config_key='core_neural',
                dependencies=[],
                initialization_timeout=45.0
            ),
            'hyper_transformer': ComponentInfo(
                name='Hyper-Dimensional Transformer',
                module_path='ultra.hyper_transformer',
                class_name='HyperDimensionalTransformer',
                config_key='hyper_transformer',
                dependencies=['core_neural'],
                initialization_timeout=30.0
            ),
            'diffusion_reasoning': ComponentInfo(
                name='Diffusion-Based Reasoning',
                module_path='ultra.diffusion_reasoning',
                class_name='DiffusionBasedReasoning',
                config_key='diffusion_reasoning',
                dependencies=['hyper_transformer'],
                initialization_timeout=35.0
            ),
            'meta_cognitive': ComponentInfo(
                name='Meta-Cognitive System',
                module_path='ultra.meta_cognitive',
                class_name='MetaCognitiveSystem',
                config_key='meta_cognitive',
                dependencies=['diffusion_reasoning', 'hyper_transformer'],
                initialization_timeout=25.0
            ),
            'neuromorphic': ComponentInfo(
                name='Neuromorphic Processing',
                module_path='ultra.neuromorphic_processing',
                class_name='NeuromorphicProcessing',
                config_key='neuromorphic_processing',
                dependencies=['core_neural'],
                initialization_timeout=40.0
            ),
            'consciousness': ComponentInfo(
                name='Emergent Consciousness Lattice',
                module_path='ultra.emergent_consciousness',
                class_name='EmergentConsciousnessLattice',
                config_key='emergent_consciousness',
                dependencies=['meta_cognitive', 'neuromorphic'],
                initialization_timeout=50.0
            ),
            'neuro_symbolic': ComponentInfo(
                name='Neuro-Symbolic Integration',
                module_path='ultra.neuro_symbolic',
                class_name='NeuroSymbolicIntegration',
                config_key='neuro_symbolic',
                dependencies=['meta_cognitive', 'hyper_transformer'],
                initialization_timeout=30.0
            ),
            'self_evolution': ComponentInfo(
                name='Self-Evolution System',
                module_path='ultra.self_evolution',
                class_name='SelfEvolutionSystem',
                config_key='self_evolution',
                dependencies=['consciousness', 'neuro_symbolic'],
                initialization_timeout=60.0
            )
        }
        
    def initialize(self) -> bool:
        """
        Initialize the complete ULTRA system
        
        Returns:
            True if initialization successful, False otherwise
        """
        self.start_time = time.time()
        
        try:
            # Stage 1: Configuration
            self._set_stage(InitializationStage.CONFIGURATION)
            if not self._initialize_configuration():
                return False
                
            # Stage 2: Logging
            self._set_stage(InitializationStage.LOGGING)
            if not self._initialize_logging():
                return False
                
            # Stage 3: Monitoring
            self._set_stage(InitializationStage.MONITORING)
            if not self._initialize_monitoring():
                return False
                
            # Stage 4: Visualization
            self._set_stage(InitializationStage.VISUALIZATION)
            if not self._initialize_visualization():
                return False
                
            # Stage 5: Dependencies
            self._set_stage(InitializationStage.DEPENDENCIES)
            if not self._validate_dependencies():
                return False
                
            # Stage 6-13: Component Initialization
            if not self._initialize_components():
                return False
                
            # Stage 14: Integration
            self._set_stage(InitializationStage.INTEGRATION)
            if not self._initialize_integration():
                return False
                
            # Stage 15: Health Checks
            self._set_stage(InitializationStage.HEALTH_CHECKS)
            if not self._initialize_health_checks():
                return False
                
            # Stage 16: Ready
            self._set_stage(InitializationStage.READY)
            self._log_initialization_complete()
            
            return True
            
        except Exception as e:
            self._log_initialization_error(e)
            return False
            
    def _set_stage(self, stage: InitializationStage):
        """Set current initialization stage"""
        self.current_stage = stage
        if hasattr(self, 'logger') and self.logger:
            self.logger.info(f"Initialization stage: {stage.value}")
        else:
            print(f"ULTRA Initialization: {stage.value}")
            
    def _initialize_configuration(self) -> bool:
        """Initialize configuration system"""
        try:
            self.config_manager = load_configuration(
                config_dir=self.config_dir,
                environment=self.environment,
                auto_optimize=True
            )
            
            # Optimize for environment
            if self.environment:
                optimize_for_environment(self.environment)
                
            return True
            
        except Exception as e:
            print(f"Configuration initialization failed: {e}")
            return False
            
    def _initialize_logging(self) -> bool:
        """Initialize logging system"""
        try:
            # Setup system logger
            self.logger = get_logger(
                'ultra.system',
                subsystem=LogCategory.SYSTEM,
                level=LogLevel[get_config('system.logging.level', 'INFO')],
                enable_structured=True,
                enable_async=True
            )
            
            self.logger.info("ULTRA System initialization started")
            self.logger.info(f"Environment: {self.environment or 'development'}")
            self.logger.info(f"Configuration directory: {self.config_dir}")
            
            return True
            
        except Exception as e:
            print(f"Logging initialization failed: {e}")
            return False
            
    def _initialize_monitoring(self) -> bool:
        """Initialize monitoring system"""
        if not self.enable_monitoring:
            self.logger.info("Monitoring disabled")
            return True
            
        try:
            monitoring_config = get_config('system.monitoring', {})
            
            self.monitor = initialize_monitoring(
                storage_dir=Path("monitoring"),
                enable_prometheus=monitoring_config.get('prometheus_enabled', False),
                redis_url=monitoring_config.get('redis_url')
            )
            
            self.logger.info("Monitoring system initialized")
            return True
            
        except Exception as e:
            self.logger.error(f"Monitoring initialization failed: {e}")
            # Monitoring is not critical, continue without it
            return True
            
    def _initialize_visualization(self) -> bool:
        """Initialize visualization system"""
        if not self.enable_visualization:
            self.logger.info("Visualization disabled")
            return True
            
        try:
            self.visualizer = initialize_visualization(
                output_dir=Path("visualizations"),
                monitoring_system=self.monitor
            )
            
            self.logger.info("Visualization system initialized")
            return True
            
        except Exception as e:
            self.logger.error(f"Visualization initialization failed: {e}")
            # Visualization is not critical, continue without it
            return True
            
    def _validate_dependencies(self) -> bool:
        """Validate component dependencies"""
        try:
            self.dependency_resolver = DependencyResolver(self.component_specs)
            missing_deps = self.dependency_resolver.validate_dependencies()
            
            if missing_deps:
                for missing in missing_deps:
                    self.logger.error(missing)
                return False
                
            self.logger.info("Component dependencies validated")
            return True
            
        except Exception as e:
            self.logger.error(f"Dependency validation failed: {e}")
            return False
            
    def _initialize_components(self) -> bool:
        """Initialize all ULTRA components in dependency order"""
        try:
            # Create component loader
            self.component_loader = ComponentLoader(self.logger)
            
            # Get initialization order
            init_order = self.dependency_resolver.get_initialization_order()
            self.logger.info(f"Component initialization order: {init_order}")
            
            # Initialize components
            for component_name in init_order:
                component_info = self.component_specs[component_name]
                
                # Set stage for this component
                stage_name = component_name.upper()
                if hasattr(InitializationStage, stage_name):
                    self._set_stage(getattr(InitializationStage, stage_name))
                    
                # Get component configuration
                config = get_config(component_info.config_key, {})
                
                # Initialize component with timeout
                result = self._initialize_component_with_timeout(
                    component_info, 
                    config, 
                    component_info.initialization_timeout
                )
                
                self.initialization_results[component_name] = result
                
                if result.success:
                    self.initialized_components[component_name] = result.instance
                    self.logger.info(
                        f"Component {component_info.name} initialized successfully "
                        f"({result.initialization_time:.2f}s, {result.memory_usage:.1f}MB)"
                    )
                    
                    # Register for monitoring
                    if self.monitor:
                        self.monitor.record_metric(
                            f"component.{component_name}.initialization_time",
                            result.initialization_time,
                            {"component": component_name}
                        )
                        
                else:
                    if component_info.optional:
                        self.logger.warning(f"Optional component {component_info.name} failed to initialize: {result.error}")
                    else:
                        self.logger.error(f"Critical component {component_info.name} failed to initialize: {result.error}")
                        return False
                        
            return True
            
        except Exception as e:
            self.logger.error(f"Component initialization failed: {e}")
            return False
            
    def _initialize_component_with_timeout(self, 
                                         component_info: ComponentInfo, 
                                         config: Dict[str, Any], 
                                         timeout: float) -> InitializationResult:
        """Initialize component with timeout protection"""
        
        result = [None]  # Use list for mutable reference
        
        def init_worker():
            result[0] = self.component_loader.load_component(component_info, config)
            
        # Start initialization in separate thread
        thread = threading.Thread(target=init_worker)
        thread.daemon = True
        thread.start()
        thread.join(timeout)
        
        if thread.is_alive():
            # Timeout occurred
            self.logger.error(f"Component {component_info.name} initialization timed out after {timeout}s")
            return InitializationResult(
                component=component_info.name,
                success=False,
                error=f"Initialization timeout ({timeout}s)",
                initialization_time=timeout
            )
        elif result[0] is None:
            # Thread completed but no result
            return InitializationResult(
                component=component_info.name,
                success=False,
                error="Initialization failed with no result",
                initialization_time=timeout
            )
        else:
            return result[0]
            
    def _initialize_integration(self) -> bool:
        """Initialize cross-component integration"""
        try:
            # Import integration bridges
            from ultra.integration import (
                neuromorphic_transformer_bridge,
                diffusion_metacognitive_bridge,
                consciousness_lattice_bridge,
                neuro_symbolic_bridge,
                self_evolution_bridge
            )
            
            # Initialize integration bridges with initialized components
            integrations = {
                'neuromorphic_transformer': neuromorphic_transformer_bridge,
                'diffusion_metacognitive': diffusion_metacognitive_bridge,
                'consciousness_lattice': consciousness_lattice_bridge,
                'neuro_symbolic': neuro_symbolic_bridge,
                'self_evolution': self_evolution_bridge
            }
            
            for integration_name, bridge_module in integrations.items():
                try:
                    if hasattr(bridge_module, 'initialize_bridge'):
                        bridge_module.initialize_bridge(self.initialized_components)
                        self.logger.info(f"Integration bridge {integration_name} initialized")
                except Exception as e:
                    self.logger.warning(f"Integration bridge {integration_name} failed: {e}")
                    
            self.logger.info("Component integration completed")
            return True
            
        except Exception as e:
            self.logger.error(f"Integration initialization failed: {e}")
            return False
            
    def _initialize_health_checks(self) -> bool:
        """Initialize health monitoring for all components"""
        if not self.enable_health_checks:
            self.logger.info("Health checks disabled")
            return True
            
        try:
            # Register health checks for initialized components
            for component_name, instance in self.initialized_components.items():
                if hasattr(instance, 'health_check'):
                    self.health_checker.register_health_check(
                        component_name, 
                        instance.health_check
                    )
                else:
                    # Create default health check
                    def default_health_check():
                        return (True, f"{component_name} operational")
                    self.health_checker.register_health_check(
                        component_name, 
                        default_health_check
                    )
                    
            # Run initial health check
            is_healthy, results = self.health_checker.get_system_health()
            
            if is_healthy:
                self.logger.info("All components passed initial health check")
            else:
                unhealthy = [comp for comp, (healthy, _) in results.items() if not healthy]
                self.logger.warning(f"Health check failed for components: {unhealthy}")
                
            # Start continuous monitoring if requested
            health_config = get_config('system.monitoring.health_checks', {})
            if health_config.get('continuous_monitoring', True):
                interval = health_config.get('check_interval', 60.0)
                self.health_checker.start_continuous_monitoring(interval)
                self.logger.info(f"Continuous health monitoring started (interval: {interval}s)")
                
            return True
            
        except Exception as e:
            self.logger.error(f"Health check initialization failed: {e}")
            return False
            
    def _log_initialization_complete(self):
        """Log successful initialization completion"""
        total_time = time.time() - self.start_time
        
        # Calculate statistics
        total_components = len(self.component_specs)
        successful_components = len([r for r in self.initialization_results.values() if r.success])
        total_memory = sum(r.memory_usage for r in self.initialization_results.values())
        
        self.logger.info("=" * 60)
        self.logger.info("ULTRA System Initialization Complete")
        self.logger.info("=" * 60)
        self.logger.info(f"Total time: {total_time:.2f} seconds")
        self.logger.info(f"Components initialized: {successful_components}/{total_components}")
        self.logger.info(f"Total memory usage: {total_memory:.1f} MB")
        self.logger.info(f"Environment: {self.environment or 'development'}")
        
        # Log component details
        for component_name, result in self.initialization_results.items():
            status = "SUCCESS" if result.success else "FAILED"
            self.logger.info(f"  {component_name}: {status} ({result.initialization_time:.2f}s)")
            
        self.logger.info("System ready for operation")
        
        # Record initialization metrics
        if self.monitor:
            self.monitor.record_metric("system.initialization.total_time", total_time)
            self.monitor.record_metric("system.initialization.successful_components", successful_components)
            self.monitor.record_metric("system.initialization.total_memory", total_memory)
            
    def _log_initialization_error(self, error: Exception):
        """Log initialization failure"""
        total_time = time.time() - self.start_time if self.start_time else 0
        
        error_msg = f"ULTRA System initialization failed at stage {self.current_stage.value if self.current_stage else 'unknown'}: {str(error)}"
        
        if self.logger:
            self.logger.critical(error_msg)
            self.logger.debug(traceback.format_exc())
        else:
            print(f"CRITICAL: {error_msg}")
            traceback.print_exc()
            
        # Log partial results
        if self.initialization_results:
            successful = len([r for r in self.initialization_results.values() if r.success])
            total = len(self.initialization_results)
            if self.logger:
                self.logger.info(f"Partial initialization: {successful}/{total} components completed in {total_time:.2f}s")
            else:
                print(f"Partial initialization: {successful}/{total} components completed in {total_time:.2f}s")
                
    def get_initialization_summary(self) -> Dict[str, Any]:
        """Get summary of initialization process"""
        total_time = time.time() - self.start_time if self.start_time else 0
        
        return {
            'total_time': total_time,
            'current_stage': self.current_stage.value if self.current_stage else None,
            'components': {
                name: {
                    'success': result.success,
                    'initialization_time': result.initialization_time,
                    'memory_usage': result.memory_usage,
                    'error': result.error
                }
                for name, result in self.initialization_results.items()
            },
            'successful_components': len([r for r in self.initialization_results.values() if r.success]),
            'total_components': len(self.component_specs),
            'monitoring_enabled': self.enable_monitoring,
            'visualization_enabled': self.enable_visualization,
            'health_checks_enabled': self.enable_health_checks
        }
        
    def shutdown(self):
        """Gracefully shutdown the ULTRA system"""
        if self.logger:
            self.logger.info("Initiating ULTRA system shutdown")
            
        # Stop health monitoring
        if self.health_checker:
            self.health_checker.stop_monitoring()
            
        # Shutdown components in reverse order
        if self.dependency_resolver and self.initialized_components:
            shutdown_order = list(reversed(self.dependency_resolver.get_initialization_order()))
            
            for component_name in shutdown_order:
                if component_name in self.initialized_components:
                    instance = self.initialized_components[component_name]
                    
                    try:
                        if hasattr(instance, 'shutdown'):
                            instance.shutdown()
                            if self.logger:
                                self.logger.info(f"Component {component_name} shutdown successfully")
                    except Exception as e:
                        if self.logger:
                            self.logger.error(f"Error shutting down component {component_name}: {e}")
                            
        # Stop monitoring system
        if self.monitor:
            self.monitor.stop()
            
        # Final cleanup
        gc.collect()
        
        if self.logger:
            self.logger.info("ULTRA system shutdown complete")

# Global system initializer instance
_system_initializer = None

def initialize_ultra_system(config_dir: Optional[Path] = None,
                           environment: Optional[str] = None,
                           enable_monitoring: bool = True,
                           enable_visualization: bool = True,
                           enable_health_checks: bool = True) -> ULTRASystemInitializer:
    """
    Initialize the ULTRA system with specified configuration
    
    Args:
        config_dir: Configuration directory path
        environment: Target environment (development, testing, production)
        enable_monitoring: Enable system monitoring
        enable_visualization: Enable visualization capabilities
        enable_health_checks: Enable health check monitoring
        
    Returns:
        Initialized ULTRASystemInitializer instance
    """
    global _system_initializer
    
    if _system_initializer is None:
        _system_initializer = ULTRASystemInitializer(
            config_dir=config_dir,
            environment=environment,
            enable_monitoring=enable_monitoring,
            enable_visualization=enable_visualization,
            enable_health_checks=enable_health_checks
        )
        
        success = _system_initializer.initialize()
        if not success:
            raise InitializationError("ULTRA system initialization failed")
            
    return _system_initializer

def get_system_initializer() -> Optional[ULTRASystemInitializer]:
    """Get the global system initializer instance"""
    return _system_initializer

def shutdown_ultra_system():
    """Shutdown the ULTRA system"""
    global _system_initializer
    
    if _system_initializer:
        _system_initializer.shutdown()
        _system_initializer = None

def get_initialized_component(component_name: str) -> Optional[Any]:
    """Get an initialized component by name"""
    if _system_initializer:
        return _system_initializer.initialized_components.get(component_name)
    return None

def is_system_initialized() -> bool:
    """Check if the ULTRA system is fully initialized"""
    return (_system_initializer is not None and 
            _system_initializer.current_stage == InitializationStage.READY)

def get_system_health() -> Tuple[bool, Dict[str, Tuple[bool, str]]]:
    """Get current system health status"""
    if _system_initializer and _system_initializer.health_checker:
        return _system_initializer.health_checker.get_system_health()
    return (False, {})

def wait_for_initialization(timeout: float = 300.0) -> bool:
    """
    Wait for system initialization to complete
    
    Args:
        timeout: Maximum time to wait in seconds
        
    Returns:
        True if initialization completed successfully, False if timeout
    """
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        if is_system_initialized():
            return True
        time.sleep(1.0)
        
    return False

# Auto-initialization on import (can be disabled with environment variable)
if os.getenv('ULTRA_AUTO_INIT', '1').lower() in ('1', 'true', 'yes'):
    try:
        # Only auto-initialize in appropriate environments
        env = os.getenv('ULTRA_ENVIRONMENT', 'development')
        if env in ['development', 'testing']:
            initialize_ultra_system(environment=env)
    except Exception as e:
        # Silently fail auto-initialization - user can manually initialize
        pass