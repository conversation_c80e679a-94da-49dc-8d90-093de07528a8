#!/usr/bin/env python3
"""
Quick GUI Test Script
====================

Quick test to verify the ULTRA Advanced GUI works properly
before launching the full system.
"""

import sys
import os
from pathlib import Path

# Add ULTRA to path
ultra_root = Path(__file__).parent
sys.path.insert(0, str(ultra_root))

def test_gui_import():
    """Test if GUI can be imported"""
    print("🧪 Testing GUI import...")
    
    try:
        from interfaces.gui.ultra_advanced_gui import ULTRAAdvancedGUI
        print("✅ GUI import successful")
        return True
    except Exception as e:
        print(f"❌ GUI import failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_initialization():
    """Test GUI initialization without running"""
    print("🧪 Testing GUI initialization...")
    
    try:
        from interfaces.gui.ultra_advanced_gui import ULTRAAdvancedGUI
        
        # Create GUI instance (but don't run mainloop)
        app = ULTRAAdvancedGUI()
        print("✅ GUI initialization successful")
        
        # Test some basic properties
        print(f"💻 CPU cores detected: {app.cpu_cores}")
        print(f"🎨 Theme: {'Dark' if app.dark_theme else 'Light'}")
        print(f"🖥️ Window title: {app.root.title()}")
        
        # Destroy the window
        app.root.destroy()
        print("✅ GUI cleanup successful")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI initialization failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🚀 ULTRA GUI Quick Test")
    print("=" * 30)
    
    # Test 1: Import
    if not test_gui_import():
        print("\n❌ GUI import test failed")
        return False
    
    # Test 2: Initialization
    if not test_gui_initialization():
        print("\n❌ GUI initialization test failed")
        return False
    
    print("\n🎉 All GUI tests passed!")
    print("✅ GUI is ready for launch")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
