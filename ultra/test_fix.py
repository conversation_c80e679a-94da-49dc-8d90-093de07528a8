#!/usr/bin/env python3

import sys
import os
sys.path.append('/workspaces/Ultra/ultra')

from tests.test_meta_cognitive.test_meta_learning import TestULTRAMetaLearning
import unittest

def test_fix():
    """Test our weight adjustment fix"""
    test_case = TestULTRAMetaLearning()
    try:
        test_case.test_comprehensive_learning_workflow()
        print("✅ test_comprehensive_learning_workflow PASSED")
        return True
    except Exception as e:
        print(f"❌ test_comprehensive_learning_workflow FAILED: {e}")
        return False

if __name__ == "__main__":
    print("Testing weight adjustment fix...")
    success = test_fix()
    if success:
        print("\n🎉 ALL TESTS PASSED! The fix is successful.")
    else:
        print("\n💭 Test still failing, need to investigate further.")
