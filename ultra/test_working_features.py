#!/usr/bin/env python3
"""
Test Working ULTRA Features
===========================

Test the actual functionality of working ULTRA components.
"""

import sys
import os

# Add ULTRA to Python path
sys.path.insert(0, os.path.abspath('.'))

def test_core_neural():
    """Test Core Neural Architecture"""
    print("🧬 Testing Core Neural Architecture...")
    
    try:
        from ultra.core_neural import CoreNeuralInterface
        
        # Create interface
        core = CoreNeuralInterface()
        print("✅ CoreNeuralInterface created successfully")
        
        # Test basic functionality
        if hasattr(core, 'process'):
            result = core.process("test input")
            print(f"✅ Core processing works: {type(result)}")
        else:
            print("⚠️ No process method found")
            
        return True
        
    except Exception as e:
        print(f"❌ Core Neural test failed: {str(e)}")
        return False

def test_diffusion_reasoning():
    """Test Diffusion Reasoning System"""
    print("\n🌊 Testing Diffusion Reasoning...")
    
    try:
        from ultra.diffusion_reasoning import DiffusionBasedReasoning
        
        # Create reasoning system
        reasoning = DiffusionBasedReasoning()
        print("✅ DiffusionBasedReasoning created successfully")
        
        # Test reasoning
        if hasattr(reasoning, 'reason'):
            result = reasoning.reason("What is the meaning of life?")
            print(f"✅ Reasoning works: {len(str(result))} chars")
            print(f"   Sample: {str(result)[:100]}...")
        else:
            print("⚠️ No reason method found")
            
        return True
        
    except Exception as e:
        print(f"❌ Diffusion Reasoning test failed: {str(e)}")
        return False

def test_neuro_symbolic():
    """Test Neuro Symbolic Integration"""
    print("\n🔗 Testing Neuro Symbolic Integration...")
    
    try:
        from ultra.neuro_symbolic import LogicalReasoningEngine
        
        # Create reasoning engine
        engine = LogicalReasoningEngine()
        print("✅ LogicalReasoningEngine created successfully")
        
        # Test logical reasoning
        if hasattr(engine, 'reason'):
            result = engine.reason("If A implies B, and A is true, what can we conclude?")
            print(f"✅ Logical reasoning works: {result}")
        else:
            print("⚠️ No reason method found")
            
        return True
        
    except Exception as e:
        print(f"❌ Neuro Symbolic test failed: {str(e)}")
        return False

def test_consciousness():
    """Test Emergent Consciousness"""
    print("\n✨ Testing Emergent Consciousness...")
    
    try:
        from ultra.emergent_consciousness import GlobalWorkspace
        
        # Create consciousness system
        consciousness = GlobalWorkspace()
        print("✅ GlobalWorkspace created successfully")
        
        # Test consciousness
        if hasattr(consciousness, 'process'):
            result = consciousness.process("I am thinking about thinking")
            print(f"✅ Consciousness processing works: {type(result)}")
        else:
            print("⚠️ No process method found")
            
        return True
        
    except Exception as e:
        print(f"❌ Consciousness test failed: {str(e)}")
        return False

def test_visualization():
    """Test Visualization Tools"""
    print("\n📊 Testing Visualization Tools...")
    
    try:
        from ultra.utils.visualization import SystemVisualizer
        
        # Create visualizer
        viz = SystemVisualizer()
        print("✅ SystemVisualizer created successfully")
        
        # Test visualization
        if hasattr(viz, 'plot_system_state'):
            print("✅ Visualization methods available")
        else:
            print("⚠️ Limited visualization methods")
            
        return True
        
    except Exception as e:
        print(f"❌ Visualization test failed: {str(e)}")
        return False

def test_simple_reasoning():
    """Test simple reasoning with available components"""
    print("\n🤔 Testing Simple Reasoning Pipeline...")
    
    try:
        # Try to create a simple reasoning pipeline
        from ultra.diffusion_reasoning import DiffusionBasedReasoning
        from ultra.neuro_symbolic import LogicalReasoningEngine
        
        # Create components
        diffusion = DiffusionBasedReasoning()
        logic = LogicalReasoningEngine()
        
        print("✅ Reasoning components created")
        
        # Test simple question
        question = "What is 2 + 2?"
        
        print(f"🤔 Question: {question}")
        
        # Try diffusion reasoning
        if hasattr(diffusion, 'reason'):
            diff_result = diffusion.reason(question)
            print(f"🌊 Diffusion result: {str(diff_result)[:200]}...")
        
        # Try logical reasoning
        if hasattr(logic, 'reason'):
            logic_result = logic.reason(question)
            print(f"🔗 Logic result: {logic_result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Simple reasoning test failed: {str(e)}")
        return False

def demonstrate_working_features():
    """Demonstrate what actually works in ULTRA"""
    print("🎯 ULTRA Working Features Demonstration")
    print("=" * 60)
    
    # Test each component
    tests = [
        test_core_neural,
        test_diffusion_reasoning,
        test_neuro_symbolic,
        test_consciousness,
        test_visualization,
        test_simple_reasoning
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed >= total * 0.7:
        print("🟢 ULTRA has significant working functionality!")
    elif passed >= total * 0.4:
        print("🟡 ULTRA has partial functionality")
    else:
        print("🔴 ULTRA needs more work")
    
    print("\n🚀 What you can do with ULTRA:")
    if passed > 0:
        print("   • Use individual components for specific tasks")
        print("   • Test reasoning capabilities")
        print("   • Experiment with consciousness simulation")
        print("   • Visualize neural network states")
        print("   • Process inputs through neural architecture")
    
    return passed, total

if __name__ == "__main__":
    demonstrate_working_features()
