#!/usr/bin/env python3
"""
Simple validation script to test if all integration bridges can be imported
after the logger initialization fixes.
"""

import sys
import traceback

# Add ULTRA to Python path
sys.path.insert(0, '/workspaces/Ultra/ultra')

def test_imports():
    """Test importing all integration bridges"""
    bridges = [
        ('neuromorphic_transformer_bridge', 'NeuromorphicTransformerBridge'),
        ('diffusion_neuromorphic_bridge', 'DiffusionNeuromorphicBridge'), 
        ('meta_cognitive_bridge', 'MetaCognitiveBridge'),
        ('consciousness_lattice_bridge', 'ConsciousnessLatticeBridge'),
        ('neuro_symbolic_bridge', 'NeuroSymbolicBridge'),
        ('self_evolution_bridge', 'SelfEvolutionBridge')
    ]
    
    successful = 0
    total = len(bridges)
    
    print("ULTRA Integration Bridge Fix Verification")
    print("=" * 50)
    
    for module_name, class_name in bridges:
        try:
            module_path = f"ultra.integration.{module_name}"
            module = __import__(module_path, fromlist=[class_name])
            bridge_class = getattr(module, class_name)
            print(f"✅ {module_name}: IMPORT SUCCESS")
            successful += 1
        except Exception as e:
            print(f"❌ {module_name}: FAILED - {str(e)}")
            # Print detailed traceback for debugging
            traceback.print_exc()
    
    print("=" * 50)
    success_rate = (successful / total) * 100
    print(f"Results: {successful}/{total} bridges imported successfully ({success_rate:.1f}%)")
    
    if successful == total:
        print("🎉 ALL INTEGRATION BRIDGES FIXED!")
        print("✨ Logger initialization problems resolved!")
        print("📈 ULTRA system should now be 95%+ functional!")
    else:
        print(f"⚠️ {total - successful} bridges still need attention")
    
    return successful == total

if __name__ == "__main__":
    try:
        success = test_imports()
        if success:
            exit(0)
        else:
            exit(1)
    except Exception as e:
        print(f"Test script failed: {e}")
        traceback.print_exc()
        exit(1)
