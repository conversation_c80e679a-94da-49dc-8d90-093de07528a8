#!/usr/bin/env python3
"""
Simple test script to check ULTRA training.
"""

import sys
import torch
print("Python version:", sys.version)
print("PyTorch version:", torch.__version__)
print("CUDA available:", torch.cuda.is_available())

try:
    from train_ultra_system import TrainingConfig, ULTRATrainer
    print("Import successful")
    
    config = TrainingConfig(
        neuromorphic_neurons=100,  # Small for testing
        d_model=64,               # Small for testing
        nhead=4,
        num_layers=2,
        num_epochs=5             # Quick test
    )
    print(f"Config created - device: {config.device}")
    
    trainer = ULTRATrainer(config)
    print("Trainer created")
    
    # Test knowledge base
    trainer.knowledge_db.populate_knowledge_base()
    qa_pairs = trainer.knowledge_db.generate_qa_pairs()
    print(f"Generated {len(qa_pairs)} Q&A pairs")
    
    # Test component initialization
    trainer.initialize_components()
    print("Components initialized successfully!")
    
    print("✅ All tests passed!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
