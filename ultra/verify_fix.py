#!/usr/bin/env python3
"""
Simple verification script for the meta-learning fix.
Tests if the StrategySelector now properly prioritizes performance predictions.
"""

import sys
import os
sys.path.append('/workspaces/Ultra/ultra')

try:
    # Import necessary components
    from tests.test_meta_cognitive.test_meta_learning import (
        StrategySelector, ReasoningStrategy, ProblemDomain
    )
    
    print("✅ Successfully imported test components")
    
    # Create a strategy selector
    selector = StrategySelector()
    
    # Check the weights
    weights = selector.selection_weights
    print(f"📊 Current selection weights: {weights}")
    
    # Verify our fix is in place
    performance_weight = weights.get('performance_prediction', 0)
    if performance_weight >= 0.7:
        print(f"✅ Performance prediction weight is properly set to {performance_weight}")
        print("✅ Fix appears to be in place!")
    else:
        print(f"❌ Performance prediction weight is only {performance_weight}, expected >= 0.7")
        print("❌ Fix may not be applied correctly")
    
    # Test the selector with a simple mathematical problem
    try:
        # Train the performance predictor to show deductive reasoning works well
        selector.update_performance({
            'strategy': ReasoningStrategy.DEDUCTIVE_REASONING,
            'success': True,
            'domain': ProblemDomain.MATHEMATICAL,
            'score': 0.9
        })
        
        # Test strategy selection for a mathematical problem
        selected_strategy = selector.select_strategy(
            problem_context={
                'domain': ProblemDomain.MATHEMATICAL,
                'complexity': 'medium',
                'type': 'equation_solving'
            }
        )
        
        print(f"🎯 Selected strategy: {selected_strategy}")
        
        if selected_strategy == ReasoningStrategy.DEDUCTIVE_REASONING:
            print("✅ Strategy selection working correctly!")
            print("✅ Test fix verification PASSED!")
        else:
            print(f"❌ Expected deductive reasoning but got {selected_strategy}")
            print("❌ Test fix verification FAILED!")
            
    except Exception as e:
        print(f"❌ Error testing strategy selection: {e}")
        
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("❌ Could not import test components")
except Exception as e:
    print(f"❌ Unexpected error: {e}")

print("\n🔍 Verification complete!")
