#!/usr/bin/env python3
"""
ULTRA Multimodal Processing Example

This script demonstrates the multimodal processing capabilities of the ULTRA system,
integrating text, image, and audio inputs through the Cross-Modal Dimension Mapper,
Self-Evolving Dynamic Attention, and other key components of the ULTRA architecture.

The example showcases:
1. Processing of multiple input modalities
2. Cross-modal attention and integration
3. Generation of unified representations
4. Multimodal reasoning and inference
5. Modality-specific and cross-modal outputs

Author: ULTRA Development Team
"""

import os
import sys
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional, Union, Any
from PIL import Image
import librosa
import time
import logging
from dataclasses import dataclass, field

# Add the ULTRA path to sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import ULTRA components
from ultra.hyper_transformer.dynamic_attention import SelfEvolvingDynamicAttention
from ultra.hyper_transformer.cross_modal_mapper import CrossModalMapper
from ultra.hyper_transformer.multiscale_embedding import MultiScaleKnowledgeEmbedding
from ultra.core_neural.neuromorphic_core import NeuromorphicCore
from ultra.diffusion_reasoning.thought_latent_space import ThoughtLatentSpace
from ultra.meta_cognitive.reasoning_graphs import ReasoningGraphs
from ultra.emergent_consciousness.global_workspace import GlobalWorkspace
from ultra.utils.ultra_logging import setup_logging
from ultra.utils.visualization import plot_attention_maps, visualize_reasoning_path


# Set up logging
logger = setup_logger("multimodal_processing")


@dataclass
class ModalityConfig:
    """Configuration for each modality pipeline"""
    
    name: str  # Modality name (text, image, audio)
    input_dim: int  # Raw input dimension
    embedding_dim: int  # Embedding dimension
    num_heads: int = 8  # Number of attention heads
    hidden_dim: int = 1024  # Hidden dimension for processing
    num_layers: int = 3  # Number of processing layers
    dropout: float = 0.1  # Dropout rate
    activation: str = "gelu"  # Activation function
    max_seq_len: int = 512  # Maximum sequence length
    modality_weights: List[float] = field(default_factory=lambda: [1.0, 1.0, 1.0])  # Weights for modality attention


class ModalityEncoder(nn.Module):
    """
    Encoder for specific modality (text, image, audio)
    Each modality has its specialized processing pipeline before integration
    """
    
    def __init__(self, config: ModalityConfig):
        super().__init__()
        self.config = config
        self.name = config.name
        
        # Input projection layer
        self.input_projection = nn.Linear(config.input_dim, config.embedding_dim)
        
        # Positional encoding
        self.pos_encoding = nn.Parameter(
            torch.zeros(1, config.max_seq_len, config.embedding_dim)
        )
        
        # Processing layers
        self.layers = nn.ModuleList([
            nn.TransformerEncoderLayer(
                d_model=config.embedding_dim,
                nhead=config.num_heads,
                dim_feedforward=config.hidden_dim,
                dropout=config.dropout,
                activation=config.activation
            ) for _ in range(config.num_layers)
        ])
        
        # Modality-specific processing
        if config.name == "text":
            self.special_layer = nn.LSTM(
                config.embedding_dim, config.embedding_dim // 2, 
                bidirectional=True, batch_first=True
            )
        elif config.name == "image":
            self.special_layer = nn.Conv2d(
                in_channels=config.embedding_dim, 
                out_channels=config.embedding_dim,
                kernel_size=3, padding=1
            )
        elif config.name == "audio":
            self.special_layer = nn.Conv1d(
                in_channels=config.embedding_dim,
                out_channels=config.embedding_dim,
                kernel_size=5, padding=2
            )
        else:
            self.special_layer = nn.Identity()
            
        # Output normalization
        self.layer_norm = nn.LayerNorm(config.embedding_dim)
        
    def forward(self, x, mask=None):
        """
        Forward pass through the modality encoder
        
        Args:
            x: Input tensor specific to the modality
            mask: Optional attention mask
            
        Returns:
            Encoded representation of the input
        """
        # Apply modality-specific preprocessing
        if self.name == "text":
            # Text input shape: [batch_size, seq_len, input_dim]
            x = self.input_projection(x)
            # Add positional encoding
            seq_len = x.size(1)
            x = x + self.pos_encoding[:, :seq_len, :]
            # Apply transformer layers
            for layer in self.layers:
                x = layer(x, src_mask=mask)
            # Apply special layer (LSTM for text)
            x, _ = self.special_layer(x)
            
        elif self.name == "image":
            # Image input shape: [batch_size, channels, height, width]
            batch_size, channels, height, width = x.shape
            # Reshape to sequence for transformer
            x = x.view(batch_size, channels, -1).permute(0, 2, 1)  # [B, H*W, C]
            x = self.input_projection(x)
            # Add positional encoding
            seq_len = x.size(1)
            x = x + self.pos_encoding[:, :seq_len, :]
            # Apply transformer layers
            for layer in self.layers:
                x = layer(x, src_mask=mask)
            # Reshape back for special layer (Conv2D for image)
            x = x.permute(0, 2, 1).view(batch_size, -1, height, width)
            x = self.special_layer(x)
            # Reshape back to sequence
            x = x.view(batch_size, -1, height * width).permute(0, 2, 1)
            
        elif self.name == "audio":
            # Audio input shape: [batch_size, time_steps, features]
            batch_size, time_steps, features = x.shape
            x = self.input_projection(x)
            # Add positional encoding
            x = x + self.pos_encoding[:, :time_steps, :]
            # Apply transformer layers
            for layer in self.layers:
                x = layer(x, src_mask=mask)
            # Apply special layer (Conv1D for audio)
            x = x.permute(0, 2, 1)  # [B, C, T]
            x = self.special_layer(x)
            x = x.permute(0, 2, 1)  # [B, T, C]
            
        # Final layer normalization
        x = self.layer_norm(x)
        return x


class MultimodalIntegrator(nn.Module):
    """
    Integrates representations from multiple modalities using:
    1. Cross-Modal Dimension Mapper
    2. Self-Evolving Dynamic Attention
    3. Multi-Scale Knowledge Embedding
    """
    
    def __init__(
        self,
        modality_configs: Dict[str, ModalityConfig],
        joint_dim: int = 1024,
        num_attention_heads: int = 16,
        num_cross_attention_layers: int = 3,
        num_integration_layers: int = 2,
        n_thought_dims: int = 256,
        dropout: float = 0.1
    ):
        super().__init__()
        self.modality_configs = modality_configs
        self.joint_dim = joint_dim
        self.modalities = list(modality_configs.keys())
        
        # Create modality encoders
        self.modality_encoders = nn.ModuleDict({
            modality: ModalityEncoder(config)
            for modality, config in modality_configs.items()
        })
        
        # Create cross-modal mapper
        modality_dims = {
            modality: config.embedding_dim
            for modality, config in modality_configs.items()
        }
        self.cross_modal_mapper = CrossModalMapper(modality_dims, joint_dim)
        
        # Create multi-scale knowledge embedding
        scales = [0.25, 0.5, 1.0, 2.0]
        self.multi_scale_embedding = MultiScaleKnowledgeEmbedding(
            input_dim=joint_dim,
            output_dim=joint_dim,
            scales=scales
        )
        
        # Self-evolving dynamic attention
        self.dynamic_attention = SelfEvolvingDynamicAttention(
            d_model=joint_dim,
            nhead=num_attention_heads,
            dropout=dropout
        )
        
        # Integration transformer layers
        self.integration_layers = nn.ModuleList([
            nn.TransformerEncoderLayer(
                d_model=joint_dim,
                nhead=num_attention_heads,
                dim_feedforward=4 * joint_dim,
                dropout=dropout,
                activation="gelu"
            ) for _ in range(num_integration_layers)
        ])
        
        # Thought latent space projector
        self.thought_projector = nn.Linear(joint_dim, n_thought_dims)
        
        # Initialize neuromorphic core for enhanced processing
        self.neuromorphic_enhancer = NeuromorphicCore(
            input_size=joint_dim,
            hidden_size=joint_dim * 2,
            output_size=joint_dim,
            neuron_type="LIF"  # Leaky Integrate-and-Fire neurons
        )
        
        # Modality-specific output projections
        self.output_projections = nn.ModuleDict({
            modality: nn.Linear(joint_dim, config.embedding_dim)
            for modality, config in modality_configs.items()
        })
        
        # Cross-modal output projection
        self.cross_modal_output = nn.Linear(joint_dim, joint_dim)
        
        # Initialize global workspace integration
        self.global_workspace = GlobalWorkspace(
            input_dim=joint_dim,
            hidden_dim=joint_dim * 2,
            output_dim=joint_dim,
            num_slots=8,
            competition_strength=2.0
        )
        
        # Performance tracking for adaptive attention
        self.performance_metrics = {
            modality: 0.0 for modality in self.modalities
        }
        
        # Thought latent space for reasoning
        self.thought_latent_space = ThoughtLatentSpace(
            dimension=n_thought_dims,
            num_clusters=len(self.modalities) * 10
        )
        
        # Reasoning graphs for multimodal inference
        self.reasoning_graphs = ReasoningGraphs()
        
        # Layer normalization
        self.layer_norm = nn.LayerNorm(joint_dim)
        
    def forward(
        self, 
        inputs: Dict[str, torch.Tensor], 
        masks: Optional[Dict[str, torch.Tensor]] = None,
        context_embedding: Optional[torch.Tensor] = None,
        task: Optional[str] = None
    ) -> Dict[str, torch.Tensor]:
        """
        Forward pass through the multimodal integrator
        
        Args:
            inputs: Dictionary of inputs for each modality
            masks: Optional dictionary of attention masks
            context_embedding: Optional context for biasing attention
            task: Optional task specification
            
        Returns:
            Dictionary of output representations for each modality and cross-modal
        """
        # Process inputs through modality-specific encoders
        encoded_inputs = {}
        for modality, encoder in self.modality_encoders.items():
            if modality in inputs:
                mask = masks.get(modality, None) if masks else None
                encoded_inputs[modality] = encoder(inputs[modality], mask)
        
        # Apply cross-modal mapping
        mapped_representations = self.cross_modal_mapper(encoded_inputs)
        
        # Apply multi-scale knowledge embedding
        multi_scale_reps = {}
        for modality, rep in mapped_representations.items():
            multi_scale_reps[modality] = self.multi_scale_embedding(rep)
        
        # Integrate modalities through self-evolving dynamic attention
        # First, concatenate all modality representations
        modality_keys = list(multi_scale_reps.keys())
        if len(modality_keys) > 0:
            rep_list = [multi_scale_reps[m] for m in modality_keys]
            # Get sequence lengths
            seq_lengths = [rep.size(1) for rep in rep_list]
            # Create modality indicators to distinguish different sources
            modality_indicators = []
            for i, length in enumerate(seq_lengths):
                indicator = torch.full((rep_list[i].size(0), length, 1), i, 
                                      dtype=torch.float, device=rep_list[0].device)
                modality_indicators.append(indicator)
            
            # Concatenate along sequence dimension
            concat_rep = torch.cat(rep_list, dim=1)
            concat_indicators = torch.cat(modality_indicators, dim=1)
            
            # Add indicators as an extra channel
            enhanced_rep = torch.cat([concat_rep, concat_indicators], dim=-1)
            enhanced_rep = nn.Linear(enhanced_rep.size(-1), self.joint_dim)(enhanced_rep)
            
            # Apply self-evolving dynamic attention
            if context_embedding is not None:
                # Compute contextual bias
                contextual_bias = self.dynamic_attention.compute_contextual_bias(context_embedding)
            else:
                contextual_bias = None
                
            # Apply dynamic attention mechanism
            # This tracks performance to update attention parameters
            attention_output, attention_weights = self.dynamic_attention(
                enhanced_rep, enhanced_rep, enhanced_rep, 
                contextual_bias=contextual_bias,
                task_performance=self.performance_metrics
            )
            
            # Apply integration transformer layers
            integrated_rep = attention_output
            for layer in self.integration_layers:
                integrated_rep = layer(integrated_rep)
            
            # Apply neuromorphic enhancement
            neuromorphic_input = integrated_rep.mean(dim=1)  # Aggregate sequence
            neuromorphic_output = self.neuromorphic_enhancer(neuromorphic_input)
            
            # Enhance integrated representation with neuromorphic processing
            integrated_rep = integrated_rep + neuromorphic_output.unsqueeze(1)
            
            # Project to thought latent space
            thought_vectors = self.thought_projector(integrated_rep)
            
            # Process through thought latent space
            processed_thoughts = self.thought_latent_space.process_vector_operation(
                thought_vectors, operation_type="integration"
            )
            
            # Project back to joint dimension
            enhanced_latent = nn.Linear(processed_thoughts.size(-1), self.joint_dim)(
                processed_thoughts
            )
            
            # Normalize and combine with integrated representation
            integrated_rep = integrated_rep + enhanced_latent
            integrated_rep = self.layer_norm(integrated_rep)
            
            # Process through global workspace for enhanced integration
            integrated_rep = self.global_workspace(integrated_rep, task=task)
            
            # Split back into modality-specific representations
            outputs = {}
            idx = 0
            for i, modality in enumerate(modality_keys):
                length = seq_lengths[i]
                modality_rep = integrated_rep[:, idx:idx+length, :]
                idx += length
                
                # Project to modality-specific dimension
                outputs[modality] = self.output_projections[modality](modality_rep)
            
            # Add cross-modal representation
            outputs["cross_modal"] = self.cross_modal_output(integrated_rep.mean(dim=1))
            
            # Update reasoning graphs based on integrated representation
            self.reasoning_graphs.update_graph(
                integrated_rep.detach(), 
                modality_keys, 
                task=task
            )
            
            return outputs
        else:
            logger.warning("No valid modality inputs provided")
            return {}
    
    def update_performance(self, metrics: Dict[str, float]):
        """Update performance metrics for adaptive attention"""
        for modality, value in metrics.items():
            if modality in self.performance_metrics:
                # Exponential moving average
                alpha = 0.9
                self.performance_metrics[modality] = (
                    alpha * self.performance_metrics[modality] + (1 - alpha) * value
                )
    
    def get_reasoning_paths(self) -> List[Dict]:
        """Retrieve reasoning paths from the reasoning graphs"""
        return self.reasoning_graphs.get_reasoning_paths()


class ModalityProcessor:
    """
    High-level processor for multimodal data that handles:
    1. Data preparation and preprocessing
    2. Integration through the MultimodalIntegrator
    3. Task-specific processing
    4. Visualization and output generation
    """
    
    def __init__(self, model_path: Optional[str] = None):
        """
        Initialize the multimodal processor
        
        Args:
            model_path: Optional path to pre-trained model
        """
        # Configure modalities
        self.modality_configs = {
            "text": ModalityConfig(
                name="text",
                input_dim=768,    # BERT-like embedding dimension
                embedding_dim=512,
                num_heads=8,
                hidden_dim=2048,
                num_layers=4,
                dropout=0.1,
                activation="gelu",
                max_seq_len=512
            ),
            "image": ModalityConfig(
                name="image",
                input_dim=2048,   # ResNet-like feature dimension
                embedding_dim=512,
                num_heads=8,
                hidden_dim=2048,
                num_layers=4,
                dropout=0.1,
                activation="gelu",
                max_seq_len=196   # 14x14 feature map
            ),
            "audio": ModalityConfig(
                name="audio",
                input_dim=1024,   # Audio feature dimension
                embedding_dim=512,
                num_heads=8,
                hidden_dim=2048,
                num_layers=3,
                dropout=0.1,
                activation="gelu",
                max_seq_len=1000  # Audio sequence length
            )
        }
        
        # Initialize integrator
        self.integrator = MultimodalIntegrator(
            modality_configs=self.modality_configs,
            joint_dim=768,
            num_attention_heads=12,
            num_cross_attention_layers=3,
            num_integration_layers=2,
            n_thought_dims=256,
            dropout=0.1
        )
        
        # Load pretrained model if provided
        if model_path and os.path.exists(model_path):
            logger.info(f"Loading model from {model_path}")
            self.integrator.load_state_dict(torch.load(model_path))
        
        # Initialize preprocessors for each modality
        self.preprocessors = self._init_preprocessors()
        
        # Set device
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.integrator.to(self.device)
        
        logger.info(f"Multimodal processor initialized on {self.device}")
        
    def _init_preprocessors(self):
        """Initialize preprocessing components for each modality"""
        preprocessors = {}
        
        # Text preprocessor using transformers
        try:
            from transformers import BertTokenizer, BertModel
            
            class TextPreprocessor:
                def __init__(self):
                    self.tokenizer = BertTokenizer.from_pretrained('bert-base-uncased')
                    self.model = BertModel.from_pretrained('bert-base-uncased')
                    self.model.eval()
                    
                def __call__(self, text, max_length=512):
                    # Tokenize and encode text
                    inputs = self.tokenizer(
                        text, 
                        return_tensors="pt", 
                        max_length=max_length, 
                        padding="max_length", 
                        truncation=True
                    )
                    
                    # Get BERT embeddings
                    with torch.no_grad():
                        outputs = self.model(**inputs)
                        
                    # Return last hidden states
                    return outputs.last_hidden_state
            
            preprocessors["text"] = TextPreprocessor()
            logger.info("Text preprocessor initialized with BERT")
            
        except ImportError:
            logger.warning("Transformers library not found, using dummy text preprocessor")
            
            class DummyTextPreprocessor:
                def __call__(self, text, max_length=512):
                    # Create random embeddings for demonstration
                    if isinstance(text, list):
                        batch_size = len(text)
                    else:
                        batch_size = 1
                        
                    return torch.randn(batch_size, max_length, 768)
            
            preprocessors["text"] = DummyTextPreprocessor()
        
        # Image preprocessor
        try:
            import torchvision.transforms as transforms
            from torchvision.models import resnet101, ResNet101_Weights
            
            class ImagePreprocessor:
                def __init__(self):
                    self.transform = transforms.Compose([
                        transforms.Resize(256),
                        transforms.CenterCrop(224),
                        transforms.ToTensor(),
                        transforms.Normalize(
                            mean=[0.485, 0.456, 0.406],
                            std=[0.229, 0.224, 0.225]
                        )
                    ])
                    
                    # Initialize ResNet without classification head
                    self.model = resnet101(weights=ResNet101_Weights.DEFAULT)
                    self.model.fc = nn.Identity()  # Remove classification layer
                    self.model.eval()
                    
                    # Feature extraction hooks
                    self.features = {}
                    self.hooks = []
                    
                    # Register hooks to extract features
                    self.hooks.append(self.model.layer4.register_forward_hook(
                        lambda module, input, output: self._hook_fn('layer4', output)
                    ))
                    
                def _hook_fn(self, name, output):
                    self.features[name] = output
                    
                def __call__(self, image):
                    if isinstance(image, list):
                        # Process batch of images
                        processed = []
                        for img in image:
                            if isinstance(img, str) and os.path.exists(img):
                                img = Image.open(img).convert('RGB')
                            if isinstance(img, Image.Image):
                                processed.append(self.transform(img))
                        
                        if processed:
                            processed = torch.stack(processed)
                        else:
                            return torch.randn(len(image), 196, 2048)
                    else:
                        # Process single image
                        if isinstance(image, str) and os.path.exists(image):
                            image = Image.open(image).convert('RGB')
                        
                        if isinstance(image, Image.Image):
                            processed = self.transform(image).unsqueeze(0)
                        else:
                            return torch.randn(1, 196, 2048)
                    
                    # Extract features
                    with torch.no_grad():
                        _ = self.model(processed)
                        
                    # Get features from layer4
                    features = self.features['layer4']
                    
                    # Reshape: [B, C, H, W] -> [B, H*W, C]
                    batch_size, channels, height, width = features.shape
                    features = features.view(batch_size, channels, -1).permute(0, 2, 1)
                    
                    return features
            
            preprocessors["image"] = ImagePreprocessor()
            logger.info("Image preprocessor initialized with ResNet101")
            
        except ImportError:
            logger.warning("Torchvision library not found, using dummy image preprocessor")
            
            class DummyImagePreprocessor:
                def __call__(self, image):
                    if isinstance(image, list):
                        batch_size = len(image)
                    else:
                        batch_size = 1
                        
                    return torch.randn(batch_size, 196, 2048)
            
            preprocessors["image"] = DummyImagePreprocessor()
        
        # Audio preprocessor
        try:
            import torchaudio
            from torchaudio.transforms import MelSpectrogram
            
            class AudioPreprocessor:
                def __init__(self):
                    self.sample_rate = 16000
                    self.n_fft = 400
                    self.win_length = 400
                    self.hop_length = 160
                    self.n_mels = 80
                    
                    self.mel_spec = MelSpectrogram(
                        sample_rate=self.sample_rate,
                        n_fft=self.n_fft,
                        win_length=self.win_length,
                        hop_length=self.hop_length,
                        n_mels=self.n_mels
                    )
                    
                    # Load pretrained model if available
                    try:
                        # Use Wav2Vec2 if available
                        from transformers import Wav2Vec2Model, Wav2Vec2Processor
                        
                        self.processor = Wav2Vec2Processor.from_pretrained(
                            "facebook/wav2vec2-base-960h"
                        )
                        self.model = Wav2Vec2Model.from_pretrained(
                            "facebook/wav2vec2-base-960h"
                        )
                        self.model.eval()
                        self.has_wav2vec = True
                        logger.info("Using Wav2Vec2 for audio processing")
                    except:
                        self.has_wav2vec = False
                        logger.info("Using Mel spectrogram for audio processing")
                    
                def __call__(self, audio):
                    if isinstance(audio, list):
                        # Process batch of audio files
                        processed = []
                        for aud in audio:
                            if isinstance(aud, str) and os.path.exists(aud):
                                waveform, sr = librosa.load(aud, sr=self.sample_rate)
                                waveform = torch.from_numpy(waveform).float()
                                processed.append(waveform)
                        
                        if processed:
                            # Pad or truncate to max length
                            max_len = 160000  # 10s at 16kHz
                            padded = []
                            for wf in processed:
                                if wf.size(0) > max_len:
                                    padded.append(wf[:max_len])
                                else:
                                    padded.append(F.pad(
                                        wf, (0, max_len - wf.size(0)), 'constant', 0
                                    ))
                            processed = torch.stack(padded)
                        else:
                            return torch.randn(len(audio), 1000, 1024)
                    else:
                        # Process single audio file
                        if isinstance(audio, str) and os.path.exists(audio):
                            waveform, sr = librosa.load(audio, sr=self.sample_rate)
                            waveform = torch.from_numpy(waveform).float()
                            
                            # Pad or truncate to 10s
                            max_len = 160000  # 10s at 16kHz
                            if waveform.size(0) > max_len:
                                waveform = waveform[:max_len]
                            else:
                                waveform = F.pad(
                                    waveform, (0, max_len - waveform.size(0)), 'constant', 0
                                )
                            
                            processed = waveform.unsqueeze(0)
                        else:
                            return torch.randn(1, 1000, 1024)
                    
                    # Process audio
                    if self.has_wav2vec:
                        # Use Wav2Vec2 model
                        with torch.no_grad():
                            inputs = self.processor(
                                processed, sampling_rate=self.sample_rate, return_tensors="pt", padding=True
                            )
                            outputs = self.model(**inputs)
                            
                        # Get hidden states
                        features = outputs.last_hidden_state
                        
                        # Ensure consistent sequence length through adaptive pooling
                        adaptive_pool = nn.AdaptiveAvgPool1d(1000)
                        features = features.transpose(1, 2)
                        features = adaptive_pool(features)
                        features = features.transpose(1, 2)
                        
                        return features
                    else:
                        # Use Mel spectrogram
                        with torch.no_grad():
                            # Compute mel spectrogram
                            mel = self.mel_spec(processed)
                            
                            # Log mel spectrogram
                            mel = torch.log(mel + 1e-9)
                            
                            # Normalize
                            mean = mel.mean(dim=(1, 2), keepdim=True)
                            std = mel.std(dim=(1, 2), keepdim=True)
                            mel = (mel - mean) / (std + 1e-9)
                            
                            # Reshape: [B, F, T] -> [B, T, F]
                            mel = mel.transpose(1, 2)
                            
                            # Project to feature dimension
                            proj = nn.Linear(self.n_mels, 1024)
                            features = proj(mel)
                            
                            # Ensure consistent sequence length
                            adaptive_pool = nn.AdaptiveAvgPool1d(1000)
                            features = features.transpose(1, 2)
                            features = adaptive_pool(features)
                            features = features.transpose(1, 2)
                            
                        return features
            
            preprocessors["audio"] = AudioPreprocessor()
            logger.info("Audio preprocessor initialized")
            
        except ImportError:
            logger.warning("Audio libraries not found, using dummy audio preprocessor")
            
            class DummyAudioPreprocessor:
                def __call__(self, audio):
                    if isinstance(audio, list):
                        batch_size = len(audio)
                    else:
                        batch_size = 1
                        
                    return torch.randn(batch_size, 1000, 1024)
            
            preprocessors["audio"] = DummyAudioPreprocessor()
        
        return preprocessors
    
    def process(
        self, 
        inputs: Dict[str, Any], 
        task: Optional[str] = None,
        context: Optional[str] = None,
        visualize: bool = False
    ) -> Dict[str, Any]:
        """
        Process multimodal inputs
        
        Args:
            inputs: Dictionary of inputs for each modality
                - text: String or list of strings
                - image: PIL Image, path to image, or list of these
                - audio: Path to audio file or list of paths
            task: Optional task specification
            context: Optional context for biasing processing
            visualize: Whether to generate visualizations
            
        Returns:
            Dictionary of outputs
        """
        # Preprocess inputs
        processed_inputs = {}
        for modality, preprocessor in self.preprocessors.items():
            if modality in inputs and inputs[modality] is not None:
                try:
                    processed = preprocessor(inputs[modality])
                    processed_inputs[modality] = processed.to(self.device)
                    logger.debug(f"Processed {modality} input with shape {processed.shape}")
                except Exception as e:
                    logger.error(f"Error processing {modality} input: {e}")
        
        # Generate context embedding if provided
        if context:
            context_embedding = self.preprocessors["text"](context).mean(dim=1)
            context_embedding = context_embedding.to(self.device)
        else:
            context_embedding = None
        
        # Process through integrator
        with torch.no_grad():
            outputs = self.integrator(
                processed_inputs, 
                context_embedding=context_embedding,
                task=task
            )
        
        # Generate task-specific outputs
        results = self._generate_outputs(outputs, task, visualize)
        
        # Add reasoning paths
        if task:
            reasoning_paths = self.integrator.get_reasoning_paths()
            results["reasoning_paths"] = reasoning_paths
            
            # Visualize reasoning path if requested
            if visualize and reasoning_paths:
                path_visualization = visualize_reasoning_path(reasoning_paths[0])
                results["visualizations"]["reasoning_path"] = path_visualization
        
        return results
    
    def _generate_outputs(
        self, 
        integrated_outputs: Dict[str, torch.Tensor],
        task: Optional[str] = None,
        visualize: bool = False
    ) -> Dict[str, Any]:
        """
        Generate task-specific outputs from integrated representations
        
        Args:
            integrated_outputs: Integrated representations from the model
            task: Optional task specification
            visualize: Whether to generate visualizations
            
        Returns:
            Dictionary of results
        """
        results = {
            "embeddings": {},
            "outputs": {},
            "visualizations": {}
        }
        
        # Extract embeddings
        for modality, tensor in integrated_outputs.items():
            if modality == "cross_modal":
                # For cross-modal, use mean pooling across sequence dimension
                if tensor.dim() > 2:
                    results["embeddings"][modality] = tensor.mean(dim=1).cpu().numpy()
                else:
                    results["embeddings"][modality] = tensor.cpu().numpy()
            else:
                # For modality-specific, keep sequence dimension
                results["embeddings"][modality] = tensor.cpu().numpy()
        
        # Visualize attention if requested
        if visualize and "cross_modal" in integrated_outputs:
            # Get attention weights from the integrator
            attention_weights = self.integrator.dynamic_attention.last_attention_weights
            
            if attention_weights is not None:
                try:
                    # Create attention map visualization
                    attention_vis = plot_attention_maps(attention_weights.cpu().numpy())
                    results["visualizations"]["attention_maps"] = attention_vis
                except Exception as e:
                    logger.error(f"Error creating attention visualization: {e}")
        
        # Generate task-specific outputs
        if task:
            cross_modal_embedding = integrated_outputs.get("cross_modal", None)
            
            if cross_modal_embedding is not None:
                # Apply task-specific processing
                if task == "classification":
                    # Simple classifier head for demonstration
                    classifier = nn.Linear(cross_modal_embedding.size(-1), 10).to(self.device)
                    logits = classifier(cross_modal_embedding)
                    probs = F.softmax(logits, dim=-1)
                    results["outputs"]["classification"] = probs.cpu().numpy()
                    
                elif task == "captioning" and "image" in integrated_outputs:
                    # Simple captioning for demonstration
                    caption_generator = nn.Linear(
                        cross_modal_embedding.size(-1), 
                        self.modality_configs["text"].embedding_dim
                    ).to(self.device)
                    
                    caption_features = caption_generator(cross_modal_embedding)
                    results["outputs"]["caption_features"] = caption_features.cpu().numpy()
                    # In a real system, these features would be decoded into text
                    
                elif task == "vqa" and "image" in integrated_outputs and "text" in integrated_outputs:
                    # Simple VQA for demonstration
                    vqa_module = nn.Sequential(
                        nn.Linear(cross_modal_embedding.size(-1), 512),
                        nn.ReLU(),
                        nn.Linear(512, 100)  # Vocabulary size
                    ).to(self.device)
                    
                    answer_logits = vqa_module(cross_modal_embedding)
                    results["outputs"]["answer_logits"] = answer_logits.cpu().numpy()
                    
                elif task == "audio_classification" and "audio" in integrated_outputs:
                    # Audio classification
                    audio_classifier = nn.Linear(cross_modal_embedding.size(-1), 50).to(self.device)
                    logits = audio_classifier(cross_modal_embedding)
                    probs = F.softmax(logits, dim=-1)
                    results["outputs"]["audio_classification"] = probs.cpu().numpy()
                    
                elif task == "multimodal_retrieval":
                    # Store normalized embeddings for retrieval
                    normalized = F.normalize(cross_modal_embedding, p=2, dim=-1)
                    results["outputs"]["retrieval_embedding"] = normalized.cpu().numpy()
        
        return results
    
    def align_modalities(
        self, 
        source_modality: str, 
        target_modality: str,
        source_data: Any,
        context: Optional[str] = None
    ) -> torch.Tensor:
        """
        Align information from source modality to target modality
        
        Args:
            source_modality: Source modality name
            target_modality: Target modality name
            source_data: Data from source modality
            context: Optional context for alignment
            
        Returns:
            Tensor aligned to target modality
        """
        if source_modality not in self.preprocessors or target_modality not in self.preprocessors:
            logger.error(f"Invalid modalities: {source_modality}, {target_modality}")
            return None
        
        # Preprocess source data
        preprocessed = self.preprocessors[source_modality](source_data)
        preprocessed = preprocessed.to(self.device)
        
        # Process context if provided
        if context:
            context_embedding = self.preprocessors["text"](context).mean(dim=1)
            context_embedding = context_embedding.to(self.device)
        else:
            context_embedding = None
        
        # Process through integrator
        with torch.no_grad():
            inputs = {source_modality: preprocessed}
            outputs = self.integrator(
                inputs,
                context_embedding=context_embedding,
                task=f"align_{source_modality}_to_{target_modality}"
            )
        
        # Get cross-modal representation
        cross_modal = outputs.get("cross_modal", None)
        
        if cross_modal is None:
            logger.error("Failed to get cross-modal representation")
            return None
        
        # Project to target modality
        target_projection = self.integrator.output_projections[target_modality]
        aligned = target_projection(cross_modal)
        
        return aligned
    
    def similarity(self, embedding1: torch.Tensor, embedding2: torch.Tensor) -> float:
        """Compute cosine similarity between embeddings"""
        embedding1 = F.normalize(embedding1, p=2, dim=-1)
        embedding2 = F.normalize(embedding2, p=2, dim=-1)
        
        return torch.sum(embedding1 * embedding2).item()
    
    def retrieve(
        self, 
        query_embedding: torch.Tensor, 
        database: List[torch.Tensor], 
        top_k: int = 5
    ) -> List[Tuple[int, float]]:
        """
        Retrieve most similar items from a database
        
        Args:
            query_embedding: Query embedding
            database: List of database embeddings
            top_k: Number of top matches to return
            
        Returns:
            List of (index, similarity) tuples
        """
        similarities = []
        for i, embedding in enumerate(database):
            sim = self.similarity(query_embedding, embedding)
            similarities.append((i, sim))
        
        # Sort by similarity (descending)
        similarities.sort(key=lambda x: x[1], reverse=True)
        
        return similarities[:top_k]
    
    def save_model(self, path: str):
        """Save model to path"""
        os.makedirs(os.path.dirname(path), exist_ok=True)
        torch.save(self.integrator.state_dict(), path)
        logger.info(f"Model saved to {path}")
    
    def load_model(self, path: str):
        """Load model from path"""
        if os.path.exists(path):
            self.integrator.load_state_dict(torch.load(path))
            logger.info(f"Model loaded from {path}")
        else:
            logger.error(f"Model file not found: {path}")


def run_example():
    """Run a multimodal processing example"""
    # Initialize processor
    processor = ModalityProcessor()
    
    # Example inputs
    inputs = {
        "text": "What is shown in this image?",
        "image": "example_image.jpg" if os.path.exists("example_image.jpg") else None,
        "audio": "example_audio.wav" if os.path.exists("example_audio.wav") else None
    }
    
    # Process inputs
    logger.info("Processing multimodal inputs...")
    results = processor.process(inputs, task="vqa", visualize=True)
    
    # Print results
    logger.info("Processing complete.")
    logger.info(f"Number of modalities processed: {len(results['embeddings'])}")
    
    # Save visualizations if available
    for vis_name, vis_data in results["visualizations"].items():
        if vis_data is not None:
            logger.info(f"Generated visualization: {vis_name}")
    
    # Example of modal alignment
    if "text" in inputs and inputs["text"] is not None:
        logger.info("Demonstrating text-to-image alignment...")
        aligned = processor.align_modalities(
            source_modality="text",
            target_modality="image",
            source_data="A beautiful sunset over mountains",
            context="Nature imagery"
        )
        
        if aligned is not None:
            logger.info(f"Aligned representation shape: {aligned.shape}")
    
    return results


if __name__ == "__main__":
    results = run_example()
    logger.info("Example completed successfully")