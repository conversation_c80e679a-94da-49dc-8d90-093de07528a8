#!/usr/bin/env python3
"""
ULTRA: Ultimate Learning & Thought Reasoning Architecture
Basic Usage Example

This script demonstrates the basic usage of the ULTRA system for a simple reasoning task.
It initializes each major component and shows the flow of information through the system.
"""

import os
import sys
import numpy as np
import logging
import time
import torch
import torch.nn as nn
import matplotlib.pyplot as plt
from datetime import datetime

# Add the parent directory to path to allow importing the ULTRA package
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import ULTRA components
from ultra.core_neural.neuromorphic_core import Neuromorphic<PERSON>ore
from ultra.core_neural.neuroplasticity_engine import NeuroplasticityEngine
from ultra.core_neural.synaptic_pruning import SynapticPruningModule
from ultra.core_neural.neuromodulation import NeuromodulationSystem
from ultra.core_neural.biological_timing import BiologicalTimingCircuits

from ultra.hyper_transformer.dynamic_attention import DynamicAttention
from ultra.hyper_transformer.contextual_bias import ContextualBiasMatrix
from ultra.hyper_transformer.recursive_transformer import RecursiveTransformer
from ultra.hyper_transformer.temporal_causal import TemporalCausalTransformer
from ultra.hyper_transformer.multiscale_embedding import MultiScaleKnowledgeEmbedding
from ultra.hyper_transformer.cross_modal_mapper import CrossModalMapper

from ultra.diffusion_reasoning.conceptual_diffusion import ConceptualDiffusion
from ultra.diffusion_reasoning.thought_latent_space import ThoughtLatentSpace
from ultra.diffusion_reasoning.reverse_diffusion import ReverseDiffusionReasoning
from ultra.diffusion_reasoning.bayesian_uncertainty import BayesianUncertaintyQuantification
from ultra.diffusion_reasoning.probabilistic_inference import ProbabilisticInferenceEngine

from ultra.meta_cognitive.chain_of_thought import MultiPathChainOfThought
from ultra.meta_cognitive.tree_of_thought import TreeOfThoughtExploration
from ultra.meta_cognitive.reasoning_graphs import ReasoningGraphs
from ultra.meta_cognitive.self_critique import SelfCritiqueLoop
from ultra.meta_cognitive.bias_detection import BiasDetection
from ultra.meta_cognitive.meta_learning import MetaLearningController

from ultra.neuromorphic_processing.spiking_networks import SpikingNeuralNetwork
from ultra.neuromorphic_processing.event_based_computing import EventBasedProcessor
from ultra.neuromorphic_processing.memristor_array import MemristorArray
from ultra.neuromorphic_processing.reservoir_computing import ReservoirComputing
from ultra.neuromorphic_processing.brain_region_emulation import BrainRegionEmulator

from ultra.emergent_consciousness.self_awareness import SelfAwarenessModule
from ultra.emergent_consciousness.intentionality import IntentionalitySystem
from ultra.emergent_consciousness.integrated_information import IntegratedInformationMatrix
from ultra.emergent_consciousness.attentional_awareness import AttentionalAwareness
from ultra.emergent_consciousness.global_workspace import GlobalWorkspace

from ultra.neuro_symbolic.logical_reasoning import LogicalReasoningEngine
from ultra.neuro_symbolic.symbolic_representation import SymbolicRepresentationLearning
from ultra.neuro_symbolic.neuro_symbolic_bridge import NeuroSymbolicBridge
from ultra.neuro_symbolic.program_synthesis import ProgramSynthesis
from ultra.neuro_symbolic.rule_extraction import RuleExtraction

from ultra.self_evolution.architecture_search import NeuralArchitectureSearch
from ultra.self_evolution.self_modification import SelfModificationProtocols
from ultra.self_evolution.computational_reflection import ComputationalReflection
from ultra.self_evolution.evolutionary_steering import EvolutionarySteeringSystem
from ultra.self_evolution.innovation_detection import EmergentInnovationDetection

from ultra.utils.ultra_logging import setup_logging
from ultra.utils.visualization import visualize_reasoning_path, visualize_attention, visualize_system_state
from ultra.utils.config import load_config
from ultra.utils.monitoring import SystemMonitor
from ultra.main import ULTRA

# Setup logging
setup_logging(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """
    Main function demonstrating the basic usage of ULTRA system
    """
    logger.info("Starting ULTRA Basic Usage Example")
    
    # Load configuration
    config_path = os.path.join(os.path.dirname(__file__), "../config/default_config.yaml")
    config = load_config(config_path)
    
    # Initialize the ULTRA system
    ultra_system = create_ultra_system(config)
    
    # Define a simple reasoning problem
    problem = """
    Alice is looking at Bob. Bob is looking at Charlie. Alice is married.
    Charlie is not married. Is a married person looking at an unmarried person?
    """
    
    # Process the problem through the system
    results = process_reasoning_task(ultra_system, problem)
    
    # Display and analyze the results
    display_results(results, problem)
    
    # Visualize the system's internal state
    visualize_system_operation(ultra_system, results)
    
    # Demonstrate self-evolution capabilities
    demonstrate_self_evolution(ultra_system, problem)
    
    logger.info("ULTRA Basic Usage Example completed successfully")


def create_ultra_system(config):
    """
    Create and initialize the ULTRA system with all components
    
    Args:
        config: Configuration dictionary loaded from YAML file
    
    Returns:
        Initialized ULTRA system
    """
    logger.info("Initializing ULTRA system components")
    
    # Create the ULTRA system (the main class handles component initialization)
    ultra_system = ULTRA()
    
    # Configure the system based on the loaded configuration
    ultra_system.configure(config)
    
    # Initialize the cognitive processing pipeline
    ultra_system.initialize_pipeline()
    
    logger.info(f"ULTRA system initialized with {len(ultra_system.components)} components")
    return ultra_system


def process_reasoning_task(ultra_system, problem_text):
    """
    Process a reasoning task through the ULTRA system
    
    Args:
        ultra_system: Initialized ULTRA system
        problem_text: Text description of the reasoning problem
    
    Returns:
        Dictionary containing processing results and internal states
    """
    logger.info(f"Processing reasoning task: {problem_text.strip()}")
    
    # Start monitoring system performance
    monitor = SystemMonitor()
    monitor.start()
    
    # Initialize result container
    results = {
        'problem': problem_text,
        'start_time': datetime.now(),
        'internal_states': [],
        'reasoning_paths': [],
        'execution_times': {},
        'resource_usage': {}
    }
    
    try:
        # 1. Input Processing
        t_start = time.time()
        
        # Encode the problem into the appropriate representation
        encoded_problem = ultra_system.encode_input(problem_text)
        results['execution_times']['input_encoding'] = time.time() - t_start
        results['internal_states'].append(('encoded_problem', encoded_problem))
        
        # 2. Core Neural Processing
        t_start = time.time()
        neural_representation = ultra_system.process_core_neural(encoded_problem)
        results['execution_times']['core_neural'] = time.time() - t_start
        results['internal_states'].append(('neural_representation', neural_representation))
        
        # 3. Transformer Processing
        t_start = time.time()
        transformer_output = ultra_system.process_transformer(neural_representation)
        results['execution_times']['transformer'] = time.time() - t_start
        results['internal_states'].append(('transformer_output', transformer_output))
        
        # 4. Diffusion-Based Reasoning
        t_start = time.time()
        diffusion_output = ultra_system.process_diffusion(transformer_output)
        results['execution_times']['diffusion'] = time.time() - t_start
        results['internal_states'].append(('diffusion_output', diffusion_output))
        
        # 5. Meta-Cognitive Reasoning
        t_start = time.time()
        reasoning_output, reasoning_paths = ultra_system.process_metacognitive(diffusion_output)
        results['execution_times']['metacognitive'] = time.time() - t_start
        results['internal_states'].append(('reasoning_output', reasoning_output))
        results['reasoning_paths'] = reasoning_paths
        
        # 6. Neuro-Symbolic Integration
        t_start = time.time()
        symbolic_output = ultra_system.process_neurosymbolic(reasoning_output)
        results['execution_times']['neurosymbolic'] = time.time() - t_start
        results['internal_states'].append(('symbolic_output', symbolic_output))
        
        # 7. Response Generation
        t_start = time.time()
        final_answer, confidence = ultra_system.generate_response(symbolic_output)
        results['execution_times']['response_generation'] = time.time() - t_start
        
        # Store the final answer and confidence
        results['final_answer'] = final_answer
        results['confidence'] = confidence
        
        # 8. Self-reflection and Learning
        t_start = time.time()
        learning_updates = ultra_system.self_reflect(problem_text, final_answer, confidence)
        results['execution_times']['self_reflection'] = time.time() - t_start
        results['learning_updates'] = learning_updates
        
    except Exception as e:
        logger.error(f"Error during processing: {str(e)}")
        results['error'] = str(e)
    
    # Stop monitoring and collect resource usage data
    results['resource_usage'] = monitor.stop()
    results['end_time'] = datetime.now()
    results['total_processing_time'] = (results['end_time'] - results['start_time']).total_seconds()
    
    return results


def display_results(results, problem):
    """
    Display processing results in a structured format
    
    Args:
        results: Dictionary containing processing results
        problem: Original problem text
    """
    logger.info("=== ULTRA Processing Results ===")
    
    # Display problem and answer
    print("\n" + "="*80)
    print("PROBLEM:")
    print(problem.strip())
    print("-"*80)
    
    if 'error' in results:
        print(f"ERROR: {results['error']}")
    else:
        print("ANSWER:")
        print(f"{results['final_answer']}")
        print(f"Confidence: {results['confidence']:.2f}")
    
    print("-"*80)
    
    # Display reasoning path
    if 'reasoning_paths' in results and results['reasoning_paths']:
        print("REASONING PATH:")
        best_path = results['reasoning_paths'][0]  # Assume first path is the best one
        for i, step in enumerate(best_path):
            print(f"  Step {i+1}: {step}")
    
    print("-"*80)
    
    # Display performance metrics
    print("PERFORMANCE METRICS:")
    if 'execution_times' in results:
        for component, time_taken in results['execution_times'].items():
            print(f"  {component.replace('_', ' ').title()}: {time_taken:.3f} seconds")
    
    if 'total_processing_time' in results:
        print(f"  Total Processing Time: {results['total_processing_time']:.3f} seconds")
    
    if 'resource_usage' in results:
        print(f"  Peak Memory Usage: {results['resource_usage'].get('peak_memory_mb', 'N/A')} MB")
        print(f"  CPU Utilization: {results['resource_usage'].get('cpu_percent', 'N/A')}%")
    
    print("="*80 + "\n")


def visualize_system_operation(ultra_system, results):
    """
    Visualize the operation of various ULTRA components
    
    Args:
        ultra_system: Initialized ULTRA system
        results: Dictionary containing processing results
    """
    # Create visualization directory if it doesn't exist
    vis_dir = os.path.join(os.path.dirname(__file__), "../output/visualizations")
    os.makedirs(vis_dir, exist_ok=True)
    
    # Visualize the reasoning paths
    if 'reasoning_paths' in results and results['reasoning_paths']:
        reasoning_graph_path = os.path.join(vis_dir, "reasoning_graph.png")
        visualize_reasoning_path(results['reasoning_paths'], 
                                save_path=reasoning_graph_path)
        logger.info(f"Reasoning path visualization saved to {reasoning_graph_path}")
    
    # Visualize attention patterns
    if hasattr(ultra_system, 'hyper_transformer') and ultra_system.hyper_transformer.dynamic_attention:
        attention_path = os.path.join(vis_dir, "attention_patterns.png")
        visualize_attention(ultra_system.hyper_transformer.dynamic_attention.get_attention_weights(), 
                           save_path=attention_path)
        logger.info(f"Attention pattern visualization saved to {attention_path}")
    
    # Visualize overall system state
    system_state_path = os.path.join(vis_dir, "system_state.png")
    visualize_system_state(ultra_system, 
                         results.get('internal_states', []),
                         save_path=system_state_path)
    logger.info(f"System state visualization saved to {system_state_path}")


def demonstrate_self_evolution(ultra_system, problem):
    """
    Demonstrate the self-evolution capabilities of the ULTRA system
    
    Args:
        ultra_system: Initialized ULTRA system
        problem: Problem used for evaluation
    """
    logger.info("Demonstrating self-evolution capabilities")
    
    # 1. Collect baseline performance metrics
    baseline_metrics = ultra_system.evaluate_performance([problem])
    print("Baseline Performance:")
    for metric, value in baseline_metrics.items():
        print(f"  {metric}: {value}")
    
    # 2. Perform neural architecture search
    logger.info("Performing neural architecture search...")
    architecture_changes = ultra_system.self_evolution.architecture_search.search(
        [problem], 
        iterations=1,  # Limited for demonstration
        population_size=3,
        evaluation_metric='accuracy'
    )
    
    # 3. Apply the most promising architectural changes
    if architecture_changes:
        best_architecture = architecture_changes[0]
        logger.info(f"Applying best architecture: {best_architecture['description']}")
        ultra_system.self_evolution.self_modification.apply_modifications(
            best_architecture['modifications']
        )
    
    # 4. Evaluate improved performance
    improved_metrics = ultra_system.evaluate_performance([problem])
    print("\nImproved Performance:")
    for metric, value in improved_metrics.items():
        print(f"  {metric}: {value}")
    
    # 5. Perform computational reflection
    reflection_results = ultra_system.self_evolution.computational_reflection.reflect(
        problem, 
        baseline_metrics, 
        improved_metrics
    )
    
    print("\nSelf-Evolution Analysis:")
    for key, value in reflection_results.items():
        print(f"  {key}: {value}")


if __name__ == "__main__":
    main()