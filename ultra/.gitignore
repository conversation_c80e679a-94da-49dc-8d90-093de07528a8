# ULTRA System .gitignore
# Comprehensive .gitignore for the Ultimate Learning & Thought Reasoning Architecture

# Python-related ignores
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
.pytest_cache/
htmlcov/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Virtual environments
venv/
env/
ENV/
ultra-env/
.env
.venv
env.bak/
venv.bak/

# IDE-specific files
.idea/
.vscode/
*.sublime-workspace
*.sublime-project
*.swp
*.swo
*~
.project
.pydevproject
.settings/
.DS_Store

# Jupyter Notebook
.ipynb_checkpoints
*.ipynb

# Data files and directories
data/knowledge_bases/*
!data/knowledge_bases/.gitkeep
data/embeddings/*
!data/embeddings/.gitkeep
data/pretrained/*
!data/pretrained/.gitkeep
data/user_data/*
!data/user_data/.gitkeep
*.hdf5
*.h5
*.pkl
*.pickle
*.npy
*.npz
*.gz
*.zip
*.tar
*.csv
*.json
*.jsonl
*.tsv
*.txt
!requirements.txt
!setup.txt

# Model checkpoints and weights
*.pt
*.pth
*.ckpt
*.pb
*.tflite
*.onnx
*.h5
model_weights/
checkpoints/

# Log files
logs/
*.log
tensorboard/
wandb/
runs/

# Documentation builds
docs/_build/
docs/api/_build/
docs/generated/

# C/C++ extensions for neuromorphic processing
*.o
*.a
*.so
*.dll
*.dylib
*.exe

# CUDA artifacts
*.i
*.ii
*.gpu
*.ptx
*.cubin
*.fatbin
*.cu.cpp

# Memristor simulation output
memristor_array_states/
memristor_logs/

# Specialized hardware configuration files
hardware_configs/local*
hardware_configs/*_private.yaml

# Self-evolution temporary files
self_evolution_temp/
architecture_search_results/
modification_proposals/

# Neuromorphic hardware interface files
*.neuron_map
*.synapse_config
*.snn_simulation

# Security and access tokens
.secrets/
*.key
*.pem
*.token

# Experimental features
experimental/
ultra/experimental/

# Diffusion model temporary states
diffusion_states/
concept_latent_spaces/

# Test output
test-output/
test-reports/

# Backup files
*.bak
*_backup
backup/

# OS-specific files
Thumbs.db
.DS_Store
.directory
*.swp
*~

# Ignore all demonstration files except examples
demos/*
!demos/examples/

# Temporary files from runs
.temp/
temp/
tmp/