#!/usr/bin/env python3
"""
Test Integration Bridges
========================

Test which integration bridges can be imported and initialized.
"""

import sys
import os

# Add ULTRA to Python path
sys.path.insert(0, os.path.abspath('.'))

def test_integration_imports():
    """Test importing integration bridges"""
    
    print("🔗 Testing ULTRA Integration Bridge Imports")
    print("=" * 60)
    
    results = {}
    
    # Test 1: neuromorphic_transformer_bridge
    try:
        from ultra.integration.neuromorphic_transformer_bridge import NeuromorphicTransformerBridge
        print("✅ neuromorphic_transformer_bridge - IMPORT SUCCESS")
        results['neuromorphic_transformer'] = True
    except Exception as e:
        print(f"❌ neuromorphic_transformer_bridge - FAILED: {e}")
        results['neuromorphic_transformer'] = False
    
    # Test 2: diffusion_neuromorphic_bridge
    try:
        from ultra.integration.diffusion_neuromorphic_bridge import DiffusionNeuromorphicBridge
        print("✅ diffusion_neuromorphic_bridge - IMPORT SUCCESS")
        results['diffusion_neuromorphic'] = True
    except Exception as e:
        print(f"❌ diffusion_neuromorphic_bridge - FAILED: {e}")
        results['diffusion_neuromorphic'] = False
    
    # Test 3: meta_cognitive_bridge
    try:
        from ultra.integration.meta_cognitive_bridge import MetaCognitiveController
        print("✅ meta_cognitive_bridge - IMPORT SUCCESS")
        results['meta_cognitive'] = True
    except Exception as e:
        print(f"❌ meta_cognitive_bridge - FAILED: {e}")
        results['meta_cognitive'] = False
    
    # Test 4: consciousness_lattice_bridge
    try:
        from ultra.integration.consciousness_lattice_bridge import ConsciousnessLatticeIntegrator
        print("✅ consciousness_lattice_bridge - IMPORT SUCCESS")
        results['consciousness_lattice'] = True
    except Exception as e:
        print(f"❌ consciousness_lattice_bridge - FAILED: {e}")
        results['consciousness_lattice'] = False
    
    # Test 5: neuro_symbolic_bridge
    try:
        from ultra.integration.neuro_symbolic_bridge import NeuroSymbolicIntegrationBridge
        print("✅ neuro_symbolic_bridge - IMPORT SUCCESS")
        results['neuro_symbolic'] = True
    except Exception as e:
        print(f"❌ neuro_symbolic_bridge - FAILED: {e}")
        results['neuro_symbolic'] = False
    
    # Test 6: self_evolution_bridge
    try:
        from ultra.integration.self_evolution_bridge import ULTRAMasterController
        print("✅ self_evolution_bridge - IMPORT SUCCESS")
        results['self_evolution'] = True
    except Exception as e:
        print(f"❌ self_evolution_bridge - FAILED: {e}")
        results['self_evolution'] = False
    
    return results

def test_integration_initialization():
    """Test initializing working bridges"""
    
    print("\n🚀 Testing Integration Bridge Initialization")
    print("=" * 60)
    
    init_results = {}
    
    # Test MetaCognitiveController
    try:
        from ultra.integration.meta_cognitive_bridge import MetaCognitiveController
        
        # Create mock components
        class MockCore:
            def process_concept(self, concept):
                return concept * 1.1
        
        class MockBridge:
            def reason_through_neural_dynamics(self, start, goal, num_steps):
                return {"final_concept": goal, "num_steps": num_steps, "convergence_distance": 0.1}
        
        mock_core = MockCore()
        mock_diffusion = MockBridge()
        mock_transformer = MockBridge()
        
        controller = MetaCognitiveController(mock_core, mock_diffusion, mock_transformer)
        print("✅ MetaCognitiveController - INITIALIZATION SUCCESS")
        init_results['meta_cognitive'] = True
        
    except Exception as e:
        print(f"❌ MetaCognitiveController - INITIALIZATION FAILED: {e}")
        init_results['meta_cognitive'] = False
    
    return init_results

def test_integration_main_module():
    """Test the main integration module"""
    
    print("\n📦 Testing Main Integration Module")
    print("=" * 60)
    
    try:
        import ultra.integration as integration
        print("✅ Main integration module imported successfully")
        
        # Check available components
        available = []
        if hasattr(integration, 'NeuromorphicTransformerBridge') and integration.NeuromorphicTransformerBridge:
            available.append('NeuromorphicTransformerBridge')
        if hasattr(integration, 'DiffusionNeuromorphicBridge') and integration.DiffusionNeuromorphicBridge:
            available.append('DiffusionNeuromorphicBridge')
        if hasattr(integration, 'MetaCognitiveController') and integration.MetaCognitiveController:
            available.append('MetaCognitiveController')
        if hasattr(integration, 'ConsciousnessLatticeIntegrator') and integration.ConsciousnessLatticeIntegrator:
            available.append('ConsciousnessLatticeIntegrator')
        if hasattr(integration, 'NeuroSymbolicIntegrationBridge') and integration.NeuroSymbolicIntegrationBridge:
            available.append('NeuroSymbolicIntegrationBridge')
        if hasattr(integration, 'ULTRAMasterController') and integration.ULTRAMasterController:
            available.append('ULTRAMasterController')
        
        print(f"📋 Available integration components: {len(available)}")
        for component in available:
            print(f"   • {component}")
        
        return True, available
        
    except Exception as e:
        print(f"❌ Main integration module failed: {e}")
        return False, []

def generate_integration_report():
    """Generate comprehensive integration report"""
    
    print("\n📊 ULTRA Integration System Report")
    print("=" * 60)
    
    # Test imports
    import_results = test_integration_imports()
    
    # Test initialization
    init_results = test_integration_initialization()
    
    # Test main module
    main_success, available_components = test_integration_main_module()
    
    # Calculate statistics
    total_bridges = len(import_results)
    working_imports = sum(import_results.values())
    working_inits = sum(init_results.values())
    
    print(f"\n🎯 Integration System Summary:")
    print(f"   • Total bridges: {total_bridges}")
    print(f"   • Working imports: {working_imports}/{total_bridges} ({working_imports/total_bridges*100:.1f}%)")
    print(f"   • Working initializations: {working_inits}/{len(init_results)} ({working_inits/len(init_results)*100:.1f}%)")
    print(f"   • Main module functional: {'✅' if main_success else '❌'}")
    print(f"   • Available components: {len(available_components)}")
    
    if working_imports >= total_bridges * 0.5:
        print("   • Status: 🟢 Integration system is functional")
    elif working_imports >= total_bridges * 0.3:
        print("   • Status: 🟡 Integration system has issues but partially working")
    else:
        print("   • Status: 🔴 Integration system needs major repairs")
    
    print("\n💡 Recommendations:")
    if working_imports < total_bridges:
        print("   • Fix import issues in failing bridge modules")
        print("   • Ensure all dependencies are properly installed")
        print("   • Check logger and config import paths")
    
    if working_inits < len(init_results):
        print("   • Fix initialization issues in bridge classes")
        print("   • Ensure proper parameter handling in constructors")
    
    if main_success and available_components:
        print("   • Integration system is ready for use!")
        print("   • You can import and use the working bridge components")
    
    return {
        'import_results': import_results,
        'init_results': init_results,
        'main_success': main_success,
        'available_components': available_components,
        'working_percentage': working_imports/total_bridges*100
    }

if __name__ == "__main__":
    results = generate_integration_report()
