#!/usr/bin/env python3
"""
Test All 8 ULTRA Cores - ROOT CAUSE ANALYSIS
============================================

This script tests each of the 8 ULTRA cores individually to find
the EXACT import errors and missing dependencies preventing them
from loading properly.
"""

import sys
import traceback
from pathlib import Path

# Add ULTRA to path
ultra_root = Path(__file__).parent
sys.path.insert(0, str(ultra_root))

def test_core_import(core_name, module_path, class_name):
    """Test importing a specific core component"""
    print(f"\n🔧 Testing {core_name}...")
    print(f"   Module: {module_path}")
    print(f"   Class: {class_name}")
    
    try:
        # Import the module
        module = __import__(module_path, fromlist=[class_name])
        print(f"   ✅ Module imported successfully")
        
        # Get the class
        core_class = getattr(module, class_name)
        print(f"   ✅ Class found: {core_class}")
        
        # Try to instantiate
        if class_name == 'HyperDimensionalTransformer':
            # This one needs proper config object
            from ultra.utils.config import HyperTransformerConfig
            config = HyperTransformerConfig()
            instance = core_class(config=config)
        elif class_name == 'MetaCognitiveSystem':
            # This one needs config dict
            instance = core_class(config={})
        else:
            instance = core_class()
        print(f"   ✅ Instance created successfully: {type(instance)}")
        
        return True, instance
        
    except Exception as e:
        print(f"   ❌ FAILED: {str(e)}")
        print(f"   📋 Traceback:")
        traceback.print_exc()
        return False, str(e)

def main():
    """Test all 8 ULTRA cores"""
    print("🔍 ULTRA 8-CORE ROOT CAUSE ANALYSIS")
    print("=" * 60)
    print("Testing each core individually to find import errors")
    print("=" * 60)
    
    # Define all 8 cores with their exact import paths
    cores = [
        ("Core 1: Neural Interface", "ultra.core_neural", "CoreNeuralInterface"),
        ("Core 2: Diffusion Reasoning", "ultra.diffusion_reasoning", "DiffusionBasedReasoning"),
        ("Core 3: Meta-Cognitive", "ultra.meta_cognitive", "MetaCognitiveSystem"),
        ("Core 4: Hyper-Transformer", "ultra.hyper_transformer", "HyperDimensionalTransformer"),
        ("Core 5: Neuromorphic Processing", "ultra.neuromorphic_processing", "NeuromorphicProcessingLayer"),
        ("Core 6: Emergent Consciousness", "ultra.emergent_consciousness", "EmergentConsciousnessLattice"),
        ("Core 7: Neuro-Symbolic", "ultra.neuro_symbolic", "NeuroSymbolicIntegration"),
        ("Core 8: Self-Evolution", "ultra.self_evolution", "SelfEvolutionSystem")
    ]
    
    results = {}
    working_cores = []
    failed_cores = []
    
    for core_name, module_path, class_name in cores:
        success, result = test_core_import(core_name, module_path, class_name)
        results[core_name] = (success, result)
        
        if success:
            working_cores.append((core_name, result))
        else:
            failed_cores.append((core_name, result))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 CORE ANALYSIS SUMMARY")
    print("=" * 60)
    
    print(f"\n✅ WORKING CORES ({len(working_cores)}/8):")
    for core_name, instance in working_cores:
        print(f"   ✅ {core_name}")
        print(f"      Type: {type(instance)}")
    
    print(f"\n❌ FAILED CORES ({len(failed_cores)}/8):")
    for core_name, error in failed_cores:
        print(f"   ❌ {core_name}")
        print(f"      Error: {error}")
    
    # Detailed analysis
    print(f"\n🔍 DETAILED FAILURE ANALYSIS:")
    print("-" * 40)
    
    for core_name, error in failed_cores:
        print(f"\n🚨 {core_name}:")
        if "No module named" in str(error):
            print("   🔧 FIX: Missing module - check __init__.py files")
        elif "cannot import name" in str(error):
            print("   🔧 FIX: Class not found - check class definitions")
        elif "config" in str(error).lower():
            print("   🔧 FIX: Configuration issue - check constructor parameters")
        elif "dependency" in str(error).lower() or "import" in str(error).lower():
            print("   🔧 FIX: Missing dependency - install required packages")
        else:
            print(f"   🔧 FIX: Unknown error - investigate: {error}")
    
    # Final assessment
    print(f"\n🎯 FINAL ASSESSMENT:")
    print("-" * 30)
    
    if len(working_cores) == 8:
        print("🎉 ALL 8 CORES WORKING - System ready!")
        return True
    elif len(working_cores) >= 6:
        print(f"⚠️ {len(working_cores)}/8 cores working - Mostly functional")
        print("🔧 Fix remaining cores for full functionality")
        return False
    elif len(working_cores) >= 3:
        print(f"⚠️ {len(working_cores)}/8 cores working - Partially functional")
        print("🔧 Multiple cores need fixing")
        return False
    else:
        print(f"❌ Only {len(working_cores)}/8 cores working - System broken")
        print("🔧 Major fixes needed")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ Testing interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
        traceback.print_exc()
        sys.exit(1)
