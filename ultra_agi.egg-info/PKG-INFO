Metadata-Version: 2.4
Name: ultra-agi
Version: 0.1.0
Summary: Ultimate Learning & Thought Reasoning Architecture
Home-page: https://github.com/ULTRA-AGI/ultra
Author: ULTRA Research Team
Author-email: <EMAIL>
License: MIT
Project-URL: Documentation, https://ultra-agi.readthedocs.io/
Project-URL: Source, https://github.com/ULTRA-AGI/ultra
Project-URL: Tracker, https://github.com/ULTRA-AGI/ultra/issues
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Scientific/Engineering
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Classifier: Topic :: Scientific/Engineering :: Mathematics
Classifier: Operating System :: POSIX :: Linux
Classifier: Operating System :: MacOS
Classifier: Operating System :: Microsoft :: Windows
Requires-Python: >=3.8
Description-Content-Type: text/markdown
Requires-Dist: numpy>=1.20.0
Requires-Dist: scipy>=1.7.0
Requires-Dist: pandas>=1.3.0
Requires-Dist: scikit-learn>=1.0.0
Requires-Dist: matplotlib>=3.4.0
Requires-Dist: sympy>=1.8.0
Requires-Dist: networkx>=2.6.0
Requires-Dist: einops>=0.4.0
Requires-Dist: torch>=1.10.0
Requires-Dist: transformers>=4.20.0
Requires-Dist: diffusers>=0.12.0
Requires-Dist: brian2>=2.4.0
Requires-Dist: nengo>=3.0.0
Requires-Dist: pymc>=4.0.0
Requires-Dist: pyro-ppl>=1.7.0
Requires-Dist: z3-solver>=4.8.0
Requires-Dist: tqdm>=4.60.0
Requires-Dist: pyyaml>=6.0
Requires-Dist: hydra-core>=1.0.0
Requires-Dist: wandb>=0.12.0
Requires-Dist: pytest>=6.0.0
Requires-Dist: black>=22.0.0
Provides-Extra: dev
Requires-Dist: pytest>=6.0.0; extra == "dev"
Requires-Dist: pytest-cov>=2.12.0; extra == "dev"
Requires-Dist: flake8>=3.9.0; extra == "dev"
Requires-Dist: black>=22.0.0; extra == "dev"
Requires-Dist: isort>=5.9.0; extra == "dev"
Requires-Dist: mypy>=0.900; extra == "dev"
Requires-Dist: sphinx>=4.0.0; extra == "dev"
Requires-Dist: sphinx-rtd-theme>=1.0.0; extra == "dev"
Requires-Dist: pre-commit>=2.13.0; extra == "dev"
Provides-Extra: docs
Requires-Dist: sphinx>=4.0.0; extra == "docs"
Requires-Dist: sphinx-rtd-theme>=1.0.0; extra == "docs"
Requires-Dist: myst-parser>=0.15.0; extra == "docs"
Requires-Dist: nbsphinx>=0.8.0; extra == "docs"
Requires-Dist: sphinx-copybutton>=0.4.0; extra == "docs"
Provides-Extra: neuromorphic
Requires-Dist: brian2>=2.4.0; extra == "neuromorphic"
Requires-Dist: nengo>=3.0.0; extra == "neuromorphic"
Requires-Dist: snn-toolbox>=0.9.0; extra == "neuromorphic"
Provides-Extra: diffusion
Requires-Dist: diffusers>=0.12.0; extra == "diffusion"
Requires-Dist: transformers>=4.20.0; extra == "diffusion"
Provides-Extra: symbolic
Requires-Dist: z3-solver>=4.8.0; extra == "symbolic"
Requires-Dist: sympy>=1.8.0; extra == "symbolic"
Requires-Dist: prolog>=0.3.0; extra == "symbolic"
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: license
Dynamic: project-url
Dynamic: provides-extra
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

# Ultra
ULTRA (Ultimate Learning &amp; Thought Reasoning Architecture)
