{"timestamp": "2025-06-03T09:15:29.658059+00:00", "level": "WARNING", "logger": "ultra.integration.neuro_symbolic_bridge", "message": "Neuro-symbolic modules not fully available: cannot import name 'SymbolicRepresentation<PERSON>earner' from 'ultra.neuro_symbolic.symbolic_representation' (/workspaces/Ultra/ultra/ultra/neuro_symbolic/symbolic_representation.py)", "module": "neuro_symbolic_bridge", "function": "<module>", "line": 83, "taskName": null, "asctime": "2025-06-03 09:15:29,658"}
{"timestamp": "2025-06-03T09:15:29.658328+00:00", "level": "WARNING", "logger": "ultra.integration.neuro_symbolic_bridge", "message": "Neuromorphic core not available: cannot import name 'BiophysicalNeuron' from 'ultra.core_neural.neuromorphic_core' (/workspaces/Ultra/ultra/ultra/core_neural/neuromorphic_core.py)", "module": "neuro_symbolic_bridge", "function": "<module>", "line": 94, "taskName": null, "asctime": "2025-06-03 09:15:29,658"}
{"timestamp": "2025-06-03T09:15:29.658504+00:00", "level": "WARNING", "logger": "ultra.integration.neuro_symbolic_bridge", "message": "Other integration bridges not available: cannot import name 'MetaCognitiveBridge' from 'ultra.integration.meta_cognitive_bridge' (/workspaces/Ultra/ultra/ultra/integration/meta_cognitive_bridge.py)", "module": "neuro_symbolic_bridge", "function": "<module>", "line": 105, "taskName": null, "asctime": "2025-06-03 09:15:29,658"}
{"timestamp": "2025-06-03T09:22:02.000696+00:00", "level": "WARNING", "logger": "ultra.integration.neuro_symbolic_bridge", "message": "Neuro-symbolic modules not fully available: cannot import name 'SymbolicRepresentationLearner' from 'ultra.neuro_symbolic.symbolic_representation' (/workspaces/Ultra/ultra/ultra/neuro_symbolic/symbolic_representation.py)", "module": "neuro_symbolic_bridge", "function": "<module>", "line": 83, "taskName": null, "asctime": "2025-06-03 09:22:02,000"}
{"timestamp": "2025-06-03T09:22:02.000970+00:00", "level": "WARNING", "logger": "ultra.integration.neuro_symbolic_bridge", "message": "Neuromorphic core not available: cannot import name 'BiophysicalNeuron' from 'ultra.core_neural.neuromorphic_core' (/workspaces/Ultra/ultra/ultra/core_neural/neuromorphic_core.py)", "module": "neuro_symbolic_bridge", "function": "<module>", "line": 94, "taskName": null, "asctime": "2025-06-03 09:22:02,000"}
{"timestamp": "2025-06-03T09:22:02.001121+00:00", "level": "WARNING", "logger": "ultra.integration.neuro_symbolic_bridge", "message": "Other integration bridges not available: cannot import name 'MetaCognitiveBridge' from 'ultra.integration.meta_cognitive_bridge' (/workspaces/Ultra/ultra/ultra/integration/meta_cognitive_bridge.py)", "module": "neuro_symbolic_bridge", "function": "<module>", "line": 105, "taskName": null, "asctime": "2025-06-03 09:22:02,001"}
{"timestamp": "2025-06-03T09:25:28.806959+00:00", "level": "WARNING", "logger": "ultra.integration.neuro_symbolic_bridge", "message": "Neuro-symbolic modules not fully available: cannot import name 'SymbolicRepresentationLearner' from 'ultra.neuro_symbolic.symbolic_representation' (/workspaces/Ultra/ultra/ultra/neuro_symbolic/symbolic_representation.py)", "module": "neuro_symbolic_bridge", "function": "<module>", "line": 83, "taskName": null, "asctime": "2025-06-03 09:25:28,806"}
{"timestamp": "2025-06-03T09:25:28.807230+00:00", "level": "WARNING", "logger": "ultra.integration.neuro_symbolic_bridge", "message": "Neuromorphic core not available: cannot import name 'BiophysicalNeuron' from 'ultra.core_neural.neuromorphic_core' (/workspaces/Ultra/ultra/ultra/core_neural/neuromorphic_core.py)", "module": "neuro_symbolic_bridge", "function": "<module>", "line": 94, "taskName": null, "asctime": "2025-06-03 09:25:28,807"}
{"timestamp": "2025-06-03T09:25:28.807405+00:00", "level": "WARNING", "logger": "ultra.integration.neuro_symbolic_bridge", "message": "Other integration bridges not available: cannot import name 'MetaCognitiveBridge' from 'ultra.integration.meta_cognitive_bridge' (/workspaces/Ultra/ultra/ultra/integration/meta_cognitive_bridge.py)", "module": "neuro_symbolic_bridge", "function": "<module>", "line": 105, "taskName": null, "asctime": "2025-06-03 09:25:28,807"}
{"timestamp": "2025-06-03T09:33:50.328433+00:00", "level": "WARNING", "logger": "ultra.integration.neuro_symbolic_bridge", "message": "Neuro-symbolic modules not fully available: cannot import name 'SymbolicRepresentationLearner' from 'ultra.neuro_symbolic.symbolic_representation' (/workspaces/Ultra/ultra/ultra/neuro_symbolic/symbolic_representation.py)", "module": "neuro_symbolic_bridge", "function": "<module>", "line": 83, "taskName": null, "asctime": "2025-06-03 09:33:50,328"}
{"timestamp": "2025-06-03T09:33:50.328739+00:00", "level": "WARNING", "logger": "ultra.integration.neuro_symbolic_bridge", "message": "Neuromorphic core not available: cannot import name 'BiophysicalNeuron' from 'ultra.core_neural.neuromorphic_core' (/workspaces/Ultra/ultra/ultra/core_neural/neuromorphic_core.py)", "module": "neuro_symbolic_bridge", "function": "<module>", "line": 94, "taskName": null, "asctime": "2025-06-03 09:33:50,328"}
{"timestamp": "2025-06-03T09:33:50.328922+00:00", "level": "WARNING", "logger": "ultra.integration.neuro_symbolic_bridge", "message": "Other integration bridges not available: cannot import name 'MetaCognitiveBridge' from 'ultra.integration.meta_cognitive_bridge' (/workspaces/Ultra/ultra/ultra/integration/meta_cognitive_bridge.py)", "module": "neuro_symbolic_bridge", "function": "<module>", "line": 105, "taskName": null, "asctime": "2025-06-03 09:33:50,328"}
{"timestamp": "2025-06-03T10:04:30.857639+00:00", "level": "WARNING", "logger": "ultra.integration.neuro_symbolic_bridge", "message": "Neuro-symbolic modules not fully available: No module named 'logical_reasoning'", "module": "neuro_symbolic_bridge", "function": "<module>", "line": 83, "taskName": null, "asctime": "2025-06-03 10:04:30,857"}
{"timestamp": "2025-06-03T10:04:30.860122+00:00", "level": "WARNING", "logger": "ultra.integration.neuro_symbolic_bridge", "message": "Other integration bridges not available: cannot import name 'MetaCognitiveBridge' from 'ultra.integration.meta_cognitive_bridge' (/workspaces/Ultra/ultra/ultra/integration/meta_cognitive_bridge.py)", "module": "neuro_symbolic_bridge", "function": "<module>", "line": 105, "taskName": null, "asctime": "2025-06-03 10:04:30,860"}
{"timestamp": "2025-06-03T10:28:34.928717+00:00", "level": "WARNING", "logger": "ultra.integration.neuro_symbolic_bridge", "message": "Neuro-symbolic modules not fully available: No module named 'logical_reasoning'", "module": "neuro_symbolic_bridge", "function": "<module>", "line": 83, "taskName": null, "asctime": "2025-06-03 10:28:34,928"}
{"timestamp": "2025-06-03T10:28:34.929549+00:00", "level": "WARNING", "logger": "ultra.integration.neuro_symbolic_bridge", "message": "Other integration bridges not available: cannot import name 'MetaCognitiveBridge' from 'ultra.integration.meta_cognitive_bridge' (/workspaces/Ultra/ultra/ultra/integration/meta_cognitive_bridge.py)", "module": "neuro_symbolic_bridge", "function": "<module>", "line": 105, "taskName": null, "asctime": "2025-06-03 10:28:34,929"}
{"timestamp": "2025-06-03T12:47:49.797122+00:00", "level": "WARNING", "logger": "ultra.integration.neuro_symbolic_bridge", "message": "Neuro-symbolic modules not fully available: No module named 'logical_reasoning'", "module": "neuro_symbolic_bridge", "function": "<module>", "line": 83, "taskName": null, "asctime": "2025-06-03 12:47:49,797"}
{"timestamp": "2025-06-03T12:47:49.797509+00:00", "level": "WARNING", "logger": "ultra.integration.neuro_symbolic_bridge", "message": "Other integration bridges not available: cannot import name 'MetaCognitiveBridge' from 'ultra.integration.meta_cognitive_bridge' (/workspaces/Ultra/ultra/ultra/integration/meta_cognitive_bridge.py)", "module": "neuro_symbolic_bridge", "function": "<module>", "line": 105, "taskName": null, "asctime": "2025-06-03 12:47:49,797"}
{"timestamp": "2025-06-03T12:52:16.709936+00:00", "level": "WARNING", "logger": "ultra.integration.neuro_symbolic_bridge", "message": "Neuro-symbolic modules not fully available: No module named 'logical_reasoning'", "module": "neuro_symbolic_bridge", "function": "<module>", "line": 83, "taskName": null, "asctime": "2025-06-03 12:52:16,709"}
{"timestamp": "2025-06-03T12:52:16.710828+00:00", "level": "WARNING", "logger": "ultra.integration.neuro_symbolic_bridge", "message": "Other integration bridges not available: cannot import name 'MetaCognitiveBridge' from 'ultra.integration.meta_cognitive_bridge' (/workspaces/Ultra/ultra/ultra/integration/meta_cognitive_bridge.py)", "module": "neuro_symbolic_bridge", "function": "<module>", "line": 105, "taskName": null, "asctime": "2025-06-03 12:52:16,710"}
{"timestamp": "2025-06-03T12:56:05.750074+00:00", "level": "WARNING", "logger": "ultra.integration.neuro_symbolic_bridge", "message": "Neuro-symbolic modules not fully available: No module named 'logical_reasoning'", "module": "neuro_symbolic_bridge", "function": "<module>", "line": 83, "taskName": null, "asctime": "2025-06-03 12:56:05,750"}
{"timestamp": "2025-06-03T12:56:05.750451+00:00", "level": "WARNING", "logger": "ultra.integration.neuro_symbolic_bridge", "message": "Other integration bridges not available: cannot import name 'MetaCognitiveBridge' from 'ultra.integration.meta_cognitive_bridge' (/workspaces/Ultra/ultra/ultra/integration/meta_cognitive_bridge.py)", "module": "neuro_symbolic_bridge", "function": "<module>", "line": 105, "taskName": null, "asctime": "2025-06-03 12:56:05,750"}
{"timestamp": "2025-06-03T13:14:50.053953+00:00", "level": "WARNING", "logger": "ultra.integration.neuro_symbolic_bridge", "message": "Neuro-symbolic modules not fully available: No module named 'logical_reasoning'", "module": "neuro_symbolic_bridge", "function": "<module>", "line": 83, "taskName": null, "asctime": "2025-06-03 13:14:50,053"}
{"timestamp": "2025-06-03T13:14:50.054319+00:00", "level": "WARNING", "logger": "ultra.integration.neuro_symbolic_bridge", "message": "Other integration bridges not available: cannot import name 'MetaCognitiveBridge' from 'ultra.integration.meta_cognitive_bridge' (/workspaces/Ultra/ultra/ultra/integration/meta_cognitive_bridge.py)", "module": "neuro_symbolic_bridge", "function": "<module>", "line": 105, "taskName": null, "asctime": "2025-06-03 13:14:50,054"}
{"timestamp": "2025-06-03T13:29:03.888087+00:00", "level": "WARNING", "logger": "ultra.integration.neuro_symbolic_bridge", "message": "Neuro-symbolic modules not fully available: No module named 'logical_reasoning'", "module": "neuro_symbolic_bridge", "function": "<module>", "line": 83, "taskName": null, "asctime": "2025-06-03 13:29:03,888"}
{"timestamp": "2025-06-03T13:29:03.888492+00:00", "level": "WARNING", "logger": "ultra.integration.neuro_symbolic_bridge", "message": "Other integration bridges not available: cannot import name 'MetaCognitiveBridge' from 'ultra.integration.meta_cognitive_bridge' (/workspaces/Ultra/ultra/ultra/integration/meta_cognitive_bridge.py)", "module": "neuro_symbolic_bridge", "function": "<module>", "line": 105, "taskName": null, "asctime": "2025-06-03 13:29:03,888"}
