{"timestamp": "2025-06-03T09:15:28.758081+00:00", "level": "WARNING", "logger": "ultra.integration.diffusion_neuromorphic_bridge", "message": "Neuromorphic core not available: cannot import name 'BiophysicalNeuron' from 'ultra.core_neural.neuromorphic_core' (/workspaces/Ultra/ultra/ultra/core_neural/neuromorphic_core.py)", "module": "diffusion_neuromorphic_bridge", "function": "<module>", "line": 75, "taskName": null, "asctime": "2025-06-03 09:15:28,758"}
{"timestamp": "2025-06-03T09:22:01.469770+00:00", "level": "WARNING", "logger": "ultra.integration.diffusion_neuromorphic_bridge", "message": "Neuromorphic core not available: cannot import name 'BiophysicalNeuron' from 'ultra.core_neural.neuromorphic_core' (/workspaces/Ultra/ultra/ultra/core_neural/neuromorphic_core.py)", "module": "diffusion_neuromorphic_bridge", "function": "<module>", "line": 75, "taskName": null, "asctime": "2025-06-03 09:22:01,469"}
{"timestamp": "2025-06-03T09:25:28.319192+00:00", "level": "WARNING", "logger": "ultra.integration.diffusion_neuromorphic_bridge", "message": "Neuromorphic core not available: cannot import name 'BiophysicalNeuron' from 'ultra.core_neural.neuromorphic_core' (/workspaces/Ultra/ultra/ultra/core_neural/neuromorphic_core.py)", "module": "diffusion_neuromorphic_bridge", "function": "<module>", "line": 75, "taskName": null, "asctime": "2025-06-03 09:25:28,319"}
{"timestamp": "2025-06-03T09:33:49.718697+00:00", "level": "WARNING", "logger": "ultra.integration.diffusion_neuromorphic_bridge", "message": "Neuromorphic core not available: cannot import name 'BiophysicalNeuron' from 'ultra.core_neural.neuromorphic_core' (/workspaces/Ultra/ultra/ultra/core_neural/neuromorphic_core.py)", "module": "diffusion_neuromorphic_bridge", "function": "<module>", "line": 75, "taskName": null, "asctime": "2025-06-03 09:33:49,718"}
