{"timestamp": "2025-06-03T09:15:28.766469+00:00", "level": "WARNING", "logger": "ultra.integration.meta_cognitive_bridge", "message": "Meta-cognitive modules not fully available: cannot import name 'SystemConfig' from 'ultra.config' (/workspaces/Ultra/ultra/ultra/config/__init__.py)", "module": "meta_cognitive_bridge", "function": "<module>", "line": 64, "taskName": null, "asctime": "2025-06-03 09:15:28,766"}
{"timestamp": "2025-06-03T09:15:28.766999+00:00", "level": "WARNING", "logger": "ultra.integration.meta_cognitive_bridge", "message": "Neuromorphic core not available: cannot import name 'BiophysicalNeuron' from 'ultra.core_neural.neuromorphic_core' (/workspaces/Ultra/ultra/ultra/core_neural/neuromorphic_core.py)", "module": "meta_cognitive_bridge", "function": "<module>", "line": 75, "taskName": null, "asctime": "2025-06-03 09:15:28,766"}
{"timestamp": "2025-06-03T09:22:01.478716+00:00", "level": "WARNING", "logger": "ultra.integration.meta_cognitive_bridge", "message": "Meta-cognitive modules not fully available: cannot import name 'SystemConfig' from 'ultra.config' (/workspaces/Ultra/ultra/ultra/config/__init__.py)", "module": "meta_cognitive_bridge", "function": "<module>", "line": 64, "taskName": null, "asctime": "2025-06-03 09:22:01,478"}
{"timestamp": "2025-06-03T09:22:01.479287+00:00", "level": "WARNING", "logger": "ultra.integration.meta_cognitive_bridge", "message": "Neuromorphic core not available: cannot import name 'BiophysicalNeuron' from 'ultra.core_neural.neuromorphic_core' (/workspaces/Ultra/ultra/ultra/core_neural/neuromorphic_core.py)", "module": "meta_cognitive_bridge", "function": "<module>", "line": 75, "taskName": null, "asctime": "2025-06-03 09:22:01,479"}
{"timestamp": "2025-06-03T09:25:28.325241+00:00", "level": "WARNING", "logger": "ultra.integration.meta_cognitive_bridge", "message": "Meta-cognitive modules not fully available: cannot import name 'SystemConfig' from 'ultra.config' (/workspaces/Ultra/ultra/ultra/config/__init__.py)", "module": "meta_cognitive_bridge", "function": "<module>", "line": 64, "taskName": null, "asctime": "2025-06-03 09:25:28,325"}
{"timestamp": "2025-06-03T09:25:28.325491+00:00", "level": "WARNING", "logger": "ultra.integration.meta_cognitive_bridge", "message": "Neuromorphic core not available: cannot import name 'BiophysicalNeuron' from 'ultra.core_neural.neuromorphic_core' (/workspaces/Ultra/ultra/ultra/core_neural/neuromorphic_core.py)", "module": "meta_cognitive_bridge", "function": "<module>", "line": 75, "taskName": null, "asctime": "2025-06-03 09:25:28,325"}
{"timestamp": "2025-06-03T09:33:49.724996+00:00", "level": "WARNING", "logger": "ultra.integration.meta_cognitive_bridge", "message": "Meta-cognitive modules not fully available: cannot import name 'SystemConfig' from 'ultra.config' (/workspaces/Ultra/ultra/ultra/config/__init__.py)", "module": "meta_cognitive_bridge", "function": "<module>", "line": 64, "taskName": null, "asctime": "2025-06-03 09:33:49,724"}
{"timestamp": "2025-06-03T09:33:49.725241+00:00", "level": "WARNING", "logger": "ultra.integration.meta_cognitive_bridge", "message": "Neuromorphic core not available: cannot import name 'BiophysicalNeuron' from 'ultra.core_neural.neuromorphic_core' (/workspaces/Ultra/ultra/ultra/core_neural/neuromorphic_core.py)", "module": "meta_cognitive_bridge", "function": "<module>", "line": 75, "taskName": null, "asctime": "2025-06-03 09:33:49,725"}
{"timestamp": "2025-06-03T10:04:28.942848+00:00", "level": "WARNING", "logger": "ultra.integration.meta_cognitive_bridge", "message": "Meta-cognitive modules not fully available: No module named 'ultra.episodic_knowledge'", "module": "meta_cognitive_bridge", "function": "<module>", "line": 64, "taskName": null, "asctime": "2025-06-03 10:04:28,942"}
{"timestamp": "2025-06-03T10:28:34.048578+00:00", "level": "WARNING", "logger": "ultra.integration.meta_cognitive_bridge", "message": "Meta-cognitive modules not fully available: No module named 'ultra.episodic_knowledge'", "module": "meta_cognitive_bridge", "function": "<module>", "line": 64, "taskName": null, "asctime": "2025-06-03 10:28:34,048"}
{"timestamp": "2025-06-03T12:47:49.025990+00:00", "level": "WARNING", "logger": "ultra.integration.meta_cognitive_bridge", "message": "Meta-cognitive modules not fully available: No module named 'ultra.episodic_knowledge'", "module": "meta_cognitive_bridge", "function": "<module>", "line": 64, "taskName": null, "asctime": "2025-06-03 12:47:49,025"}
{"timestamp": "2025-06-03T12:52:16.106929+00:00", "level": "WARNING", "logger": "ultra.integration.meta_cognitive_bridge", "message": "Meta-cognitive modules not fully available: No module named 'ultra.episodic_knowledge'", "module": "meta_cognitive_bridge", "function": "<module>", "line": 64, "taskName": null, "asctime": "2025-06-03 12:52:16,106"}
{"timestamp": "2025-06-03T12:56:05.294569+00:00", "level": "WARNING", "logger": "ultra.integration.meta_cognitive_bridge", "message": "Meta-cognitive modules not fully available: No module named 'ultra.episodic_knowledge'", "module": "meta_cognitive_bridge", "function": "<module>", "line": 64, "taskName": null, "asctime": "2025-06-03 12:56:05,294"}
{"timestamp": "2025-06-03T13:14:49.398607+00:00", "level": "WARNING", "logger": "ultra.integration.meta_cognitive_bridge", "message": "Meta-cognitive modules not fully available: No module named 'ultra.episodic_knowledge'", "module": "meta_cognitive_bridge", "function": "<module>", "line": 64, "taskName": null, "asctime": "2025-06-03 13:14:49,398"}
{"timestamp": "2025-06-03T13:29:03.464168+00:00", "level": "WARNING", "logger": "ultra.integration.meta_cognitive_bridge", "message": "Meta-cognitive modules not fully available: No module named 'ultra.episodic_knowledge'", "module": "meta_cognitive_bridge", "function": "<module>", "line": 64, "taskName": null, "asctime": "2025-06-03 13:29:03,464"}
