{"timestamp": "2025-06-03T09:15:28.798032+00:00", "level": "WARNING", "logger": "ultra.integration.consciousness_lattice_bridge", "message": "Consciousness modules not fully available: No module named 'ultra.emergent_consciousness.integrated_information_matrix'", "module": "consciousness_lattice_bridge", "function": "<module>", "line": 74, "taskName": null, "asctime": "2025-06-03 09:15:28,798"}
{"timestamp": "2025-06-03T09:15:28.798349+00:00", "level": "WARNING", "logger": "ultra.integration.consciousness_lattice_bridge", "message": "Neuromorphic core not available: cannot import name 'BiophysicalNeuron' from 'ultra.core_neural.neuromorphic_core' (/workspaces/Ultra/ultra/ultra/core_neural/neuromorphic_core.py)", "module": "consciousness_lattice_bridge", "function": "<module>", "line": 85, "taskName": null, "asctime": "2025-06-03 09:15:28,798"}
{"timestamp": "2025-06-03T09:15:28.798698+00:00", "level": "WARNING", "logger": "ultra.integration.consciousness_lattice_bridge", "message": "Other integration bridges not available: cannot import name 'MetaCognitiveBridge' from 'ultra.integration.meta_cognitive_bridge' (/workspaces/Ultra/ultra/ultra/integration/meta_cognitive_bridge.py)", "module": "consciousness_lattice_bridge", "function": "<module>", "line": 95, "taskName": null, "asctime": "2025-06-03 09:15:28,798"}
{"timestamp": "2025-06-03T09:22:01.498007+00:00", "level": "WARNING", "logger": "ultra.integration.consciousness_lattice_bridge", "message": "Consciousness modules not fully available: No module named 'ultra.emergent_consciousness.integrated_information_matrix'", "module": "consciousness_lattice_bridge", "function": "<module>", "line": 74, "taskName": null, "asctime": "2025-06-03 09:22:01,498"}
{"timestamp": "2025-06-03T09:22:01.498312+00:00", "level": "WARNING", "logger": "ultra.integration.consciousness_lattice_bridge", "message": "Neuromorphic core not available: cannot import name 'BiophysicalNeuron' from 'ultra.core_neural.neuromorphic_core' (/workspaces/Ultra/ultra/ultra/core_neural/neuromorphic_core.py)", "module": "consciousness_lattice_bridge", "function": "<module>", "line": 85, "taskName": null, "asctime": "2025-06-03 09:22:01,498"}
{"timestamp": "2025-06-03T09:22:01.499399+00:00", "level": "WARNING", "logger": "ultra.integration.consciousness_lattice_bridge", "message": "Other integration bridges not available: cannot import name 'MetaCognitiveBridge' from 'ultra.integration.meta_cognitive_bridge' (/workspaces/Ultra/ultra/ultra/integration/meta_cognitive_bridge.py)", "module": "consciousness_lattice_bridge", "function": "<module>", "line": 95, "taskName": null, "asctime": "2025-06-03 09:22:01,499"}
{"timestamp": "2025-06-03T09:25:28.340257+00:00", "level": "WARNING", "logger": "ultra.integration.consciousness_lattice_bridge", "message": "Consciousness modules not fully available: No module named 'ultra.emergent_consciousness.integrated_information_matrix'", "module": "consciousness_lattice_bridge", "function": "<module>", "line": 74, "taskName": null, "asctime": "2025-06-03 09:25:28,340"}
{"timestamp": "2025-06-03T09:25:28.340495+00:00", "level": "WARNING", "logger": "ultra.integration.consciousness_lattice_bridge", "message": "Neuromorphic core not available: cannot import name 'BiophysicalNeuron' from 'ultra.core_neural.neuromorphic_core' (/workspaces/Ultra/ultra/ultra/core_neural/neuromorphic_core.py)", "module": "consciousness_lattice_bridge", "function": "<module>", "line": 85, "taskName": null, "asctime": "2025-06-03 09:25:28,340"}
{"timestamp": "2025-06-03T09:25:28.341309+00:00", "level": "WARNING", "logger": "ultra.integration.consciousness_lattice_bridge", "message": "Other integration bridges not available: cannot import name 'MetaCognitiveBridge' from 'ultra.integration.meta_cognitive_bridge' (/workspaces/Ultra/ultra/ultra/integration/meta_cognitive_bridge.py)", "module": "consciousness_lattice_bridge", "function": "<module>", "line": 95, "taskName": null, "asctime": "2025-06-03 09:25:28,341"}
{"timestamp": "2025-06-03T09:33:49.740311+00:00", "level": "WARNING", "logger": "ultra.integration.consciousness_lattice_bridge", "message": "Consciousness modules not fully available: No module named 'ultra.emergent_consciousness.integrated_information_matrix'", "module": "consciousness_lattice_bridge", "function": "<module>", "line": 74, "taskName": null, "asctime": "2025-06-03 09:33:49,740"}
{"timestamp": "2025-06-03T09:33:49.740566+00:00", "level": "WARNING", "logger": "ultra.integration.consciousness_lattice_bridge", "message": "Neuromorphic core not available: cannot import name 'BiophysicalNeuron' from 'ultra.core_neural.neuromorphic_core' (/workspaces/Ultra/ultra/ultra/core_neural/neuromorphic_core.py)", "module": "consciousness_lattice_bridge", "function": "<module>", "line": 85, "taskName": null, "asctime": "2025-06-03 09:33:49,740"}
{"timestamp": "2025-06-03T09:33:49.741404+00:00", "level": "WARNING", "logger": "ultra.integration.consciousness_lattice_bridge", "message": "Other integration bridges not available: cannot import name 'MetaCognitiveBridge' from 'ultra.integration.meta_cognitive_bridge' (/workspaces/Ultra/ultra/ultra/integration/meta_cognitive_bridge.py)", "module": "consciousness_lattice_bridge", "function": "<module>", "line": 95, "taskName": null, "asctime": "2025-06-03 09:33:49,741"}
{"timestamp": "2025-06-03T10:04:28.881468+00:00", "level": "WARNING", "logger": "ultra.integration.consciousness_lattice_bridge", "message": "Consciousness modules not fully available: No module named 'ultra.emergent_consciousness.integrated_information_matrix'", "module": "consciousness_lattice_bridge", "function": "<module>", "line": 74, "taskName": null, "asctime": "2025-06-03 10:04:28,881"}
{"timestamp": "2025-06-03T10:04:28.947666+00:00", "level": "WARNING", "logger": "ultra.integration.consciousness_lattice_bridge", "message": "Other integration bridges not available: cannot import name 'MetaCognitiveBridge' from 'ultra.integration.meta_cognitive_bridge' (/workspaces/Ultra/ultra/ultra/integration/meta_cognitive_bridge.py)", "module": "consciousness_lattice_bridge", "function": "<module>", "line": 95, "taskName": null, "asctime": "2025-06-03 10:04:28,947"}
{"timestamp": "2025-06-03T10:28:34.013458+00:00", "level": "WARNING", "logger": "ultra.integration.consciousness_lattice_bridge", "message": "Consciousness modules not fully available: No module named 'ultra.emergent_consciousness.integrated_information_matrix'", "module": "consciousness_lattice_bridge", "function": "<module>", "line": 74, "taskName": null, "asctime": "2025-06-03 10:28:34,013"}
{"timestamp": "2025-06-03T10:28:34.051590+00:00", "level": "WARNING", "logger": "ultra.integration.consciousness_lattice_bridge", "message": "Other integration bridges not available: cannot import name 'MetaCognitiveBridge' from 'ultra.integration.meta_cognitive_bridge' (/workspaces/Ultra/ultra/ultra/integration/meta_cognitive_bridge.py)", "module": "consciousness_lattice_bridge", "function": "<module>", "line": 95, "taskName": null, "asctime": "2025-06-03 10:28:34,051"}
{"timestamp": "2025-06-03T12:47:49.020653+00:00", "level": "WARNING", "logger": "ultra.integration.consciousness_lattice_bridge", "message": "Consciousness modules not fully available: No module named 'ultra.emergent_consciousness.integrated_information_matrix'", "module": "consciousness_lattice_bridge", "function": "<module>", "line": 74, "taskName": null, "asctime": "2025-06-03 12:47:49,020"}
{"timestamp": "2025-06-03T12:47:49.028898+00:00", "level": "WARNING", "logger": "ultra.integration.consciousness_lattice_bridge", "message": "Other integration bridges not available: cannot import name 'MetaCognitiveBridge' from 'ultra.integration.meta_cognitive_bridge' (/workspaces/Ultra/ultra/ultra/integration/meta_cognitive_bridge.py)", "module": "consciousness_lattice_bridge", "function": "<module>", "line": 95, "taskName": null, "asctime": "2025-06-03 12:47:49,028"}
{"timestamp": "2025-06-03T12:52:16.101575+00:00", "level": "WARNING", "logger": "ultra.integration.consciousness_lattice_bridge", "message": "Consciousness modules not fully available: No module named 'ultra.emergent_consciousness.integrated_information_matrix'", "module": "consciousness_lattice_bridge", "function": "<module>", "line": 74, "taskName": null, "asctime": "2025-06-03 12:52:16,101"}
{"timestamp": "2025-06-03T12:52:16.110578+00:00", "level": "WARNING", "logger": "ultra.integration.consciousness_lattice_bridge", "message": "Other integration bridges not available: cannot import name 'MetaCognitiveBridge' from 'ultra.integration.meta_cognitive_bridge' (/workspaces/Ultra/ultra/ultra/integration/meta_cognitive_bridge.py)", "module": "consciousness_lattice_bridge", "function": "<module>", "line": 95, "taskName": null, "asctime": "2025-06-03 12:52:16,110"}
{"timestamp": "2025-06-03T12:56:05.290594+00:00", "level": "WARNING", "logger": "ultra.integration.consciousness_lattice_bridge", "message": "Consciousness modules not fully available: No module named 'ultra.emergent_consciousness.integrated_information_matrix'", "module": "consciousness_lattice_bridge", "function": "<module>", "line": 74, "taskName": null, "asctime": "2025-06-03 12:56:05,290"}
{"timestamp": "2025-06-03T12:56:05.297440+00:00", "level": "WARNING", "logger": "ultra.integration.consciousness_lattice_bridge", "message": "Other integration bridges not available: cannot import name 'MetaCognitiveBridge' from 'ultra.integration.meta_cognitive_bridge' (/workspaces/Ultra/ultra/ultra/integration/meta_cognitive_bridge.py)", "module": "consciousness_lattice_bridge", "function": "<module>", "line": 95, "taskName": null, "asctime": "2025-06-03 12:56:05,297"}
{"timestamp": "2025-06-03T13:14:49.394599+00:00", "level": "WARNING", "logger": "ultra.integration.consciousness_lattice_bridge", "message": "Consciousness modules not fully available: No module named 'ultra.emergent_consciousness.integrated_information_matrix'", "module": "consciousness_lattice_bridge", "function": "<module>", "line": 74, "taskName": null, "asctime": "2025-06-03 13:14:49,394"}
{"timestamp": "2025-06-03T13:14:49.401026+00:00", "level": "WARNING", "logger": "ultra.integration.consciousness_lattice_bridge", "message": "Other integration bridges not available: cannot import name 'MetaCognitiveBridge' from 'ultra.integration.meta_cognitive_bridge' (/workspaces/Ultra/ultra/ultra/integration/meta_cognitive_bridge.py)", "module": "consciousness_lattice_bridge", "function": "<module>", "line": 95, "taskName": null, "asctime": "2025-06-03 13:14:49,401"}
{"timestamp": "2025-06-03T13:29:03.459752+00:00", "level": "WARNING", "logger": "ultra.integration.consciousness_lattice_bridge", "message": "Consciousness modules not fully available: No module named 'ultra.emergent_consciousness.integrated_information_matrix'", "module": "consciousness_lattice_bridge", "function": "<module>", "line": 74, "taskName": null, "asctime": "2025-06-03 13:29:03,459"}
{"timestamp": "2025-06-03T13:29:03.466912+00:00", "level": "WARNING", "logger": "ultra.integration.consciousness_lattice_bridge", "message": "Other integration bridges not available: cannot import name 'MetaCognitiveBridge' from 'ultra.integration.meta_cognitive_bridge' (/workspaces/Ultra/ultra/ultra/integration/meta_cognitive_bridge.py)", "module": "consciousness_lattice_bridge", "function": "<module>", "line": 95, "taskName": null, "asctime": "2025-06-03 13:29:03,466"}
