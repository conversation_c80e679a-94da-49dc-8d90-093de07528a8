#!/usr/bin/env python3
"""
ULTRA Integration Module Comprehensive Test
==========================================

Tests all integration bridges with production-grade functionality.
"""

import sys
import os
import time
import numpy as np
import logging
from pathlib import Path

# Add ULTRA to path
ultra_path = Path(__file__).parent / "ultra" / "ultra"
sys.path.insert(0, str(ultra_path))
sys.path.insert(0, str(Path(__file__).parent))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_consciousness_lattice_bridge():
    """Test consciousness lattice bridge functionality."""
    print("\n" + "="*60)
    print("TESTING CONSCIOUSNESS LATTICE BRIDGE")
    print("="*60)
    
    try:
        from ultra.integration.consciousness_lattice_bridge import (
            ConsciousnessLatticeWeaver, ConsciousnessLatticeIntegrator,
            ConsciousnessState, ConsciousnessMetrics
        )
        
        print("✓ Successfully imported consciousness lattice bridge")
        
        # Test weaver initialization
        weaver = ConsciousnessLatticeWeaver()
        print("✓ Consciousness weaver initialized")
        
        # Test metrics calculation
        metrics = weaver.current_metrics
        print(f"✓ Consciousness metrics: IIT={metrics.integrated_information:.3f}")
        
        # Test integrator
        integrator = ConsciousnessLatticeIntegrator()
        print("✓ Consciousness integrator initialized")
        
        print("✓ Consciousness lattice bridge test PASSED")
        return True
        
    except Exception as e:
        print(f"✗ Consciousness lattice bridge test FAILED: {e}")
        return False

def test_neuromorphic_transformer_bridge():
    """Test neuromorphic-transformer bridge functionality."""
    print("\n" + "="*60)
    print("TESTING NEUROMORPHIC-TRANSFORMER BRIDGE")
    print("="*60)
    
    try:
        from ultra.integration.neuromorphic_transformer_bridge import (
            NeuronStateEncoder, AttentionGuidedStimulator, 
            EpisodicMemoryManager, BridgeConfig
        )
        
        print("✓ Successfully imported neuromorphic-transformer bridge")
        
        # Test configuration
        config = BridgeConfig()
        print(f"✓ Bridge config: embedding_dim={config.neuron_embedding_dim}")
        
        # Test encoder
        encoder = NeuronStateEncoder(config)
        print("✓ Neuron state encoder initialized")
        
        # Test stimulator
        stimulator = AttentionGuidedStimulator(config)
        print("✓ Attention-guided stimulator initialized")
        
        # Test memory manager
        memory = EpisodicMemoryManager(config)
        print("✓ Episodic memory manager initialized")
        
        # Test memory operations
        dummy_states = np.random.random((10, 4))
        dummy_outputs = np.random.random((5, 128))
        dummy_attention = np.random.random((1, 4, 5, 5))
        
        memory.store_episode(dummy_states, dummy_outputs, dummy_attention, time.time())
        episodes = memory.retrieve_relevant_episodes(dummy_states, k=3)
        print(f"✓ Memory operations: stored and retrieved {len(episodes)} episodes")
        
        print("✓ Neuromorphic-transformer bridge test PASSED")
        return True
        
    except Exception as e:
        print(f"✗ Neuromorphic-transformer bridge test FAILED: {e}")
        return False

def test_diffusion_neuromorphic_bridge():
    """Test diffusion-neuromorphic bridge functionality."""
    print("\n" + "="*60)
    print("TESTING DIFFUSION-NEUROMORPHIC BRIDGE")
    print("="*60)
    
    try:
        from ultra.integration.diffusion_neuromorphic_bridge import (
            DiffusionNeuromorphicBridge, DiffusionNeuromorphicConfig,
            NeuralStateEncoder, ConceptualStateDecoder,
            DiffusionGuidedEvolution, BiologicalTimingIntegrator
        )
        
        print("✓ Successfully imported diffusion-neuromorphic bridge")
        
        # Test configuration
        config = DiffusionNeuromorphicConfig()
        print(f"✓ Config: concept_dim={config.concept_dim}, diffusion_steps={config.diffusion_steps}")
        
        # Test encoder
        encoder = NeuralStateEncoder(config)
        dummy_neural_states = np.random.normal(0, 1, (50, 4))
        concept_embedding = encoder.encode_neural_population(dummy_neural_states)
        print(f"✓ Neural encoding: {dummy_neural_states.shape} -> {concept_embedding.shape}")
        
        # Test decoder
        decoder = ConceptualStateDecoder(config)
        neural_params = decoder.decode_to_neural_params(concept_embedding)
        print(f"✓ Concept decoding: {len(neural_params)} parameters extracted")
        
        # Test evolution guide
        evolution = DiffusionGuidedEvolution(config)
        dummy_network = {'neuron_count': 100, 'learning_rate': 0.01}
        target_concept = np.random.normal(0, 1, config.concept_dim)
        evolution_result = evolution.evolve_network_structure(dummy_network, target_concept, 0.5)
        print(f"✓ Network evolution: proposal generated with improvement {evolution_result.get('expected_improvement', 0):.3f}")
        
        # Test timing integrator
        timing = BiologicalTimingIntegrator(config)
        dummy_activity = np.random.normal(0, 1, 1000)
        rhythms = timing.extract_neural_rhythms(dummy_activity)
        print(f"✓ Timing integration: extracted {len(rhythms)} rhythm features")
        
        # Test main bridge
        bridge = DiffusionNeuromorphicBridge(config=config)
        init_success = bridge.initialize_bridge()
        print(f"✓ Bridge initialization: {'SUCCESS' if init_success else 'PARTIAL'}")
        
        # Test bridge processing
        bridge._process_bridge_cycle()
        performance = bridge.get_performance_summary()
        print(f"✓ Bridge processing: {performance.get('update_counter', 0)} updates completed")
        
        print("✓ Diffusion-neuromorphic bridge test PASSED")
        return True
        
    except Exception as e:
        print(f"✗ Diffusion-neuromorphic bridge test FAILED: {e}")
        return False

def test_integration_performance():
    """Test integration performance and benchmarks."""
    print("\n" + "="*60)
    print("TESTING INTEGRATION PERFORMANCE")
    print("="*60)
    
    try:
        # Test neural state processing speed
        print("Testing neural state processing speed...")
        
        start_time = time.time()
        for i in range(100):
            # Simulate neural state processing
            neural_states = np.random.normal(0, 1, (100, 4))
            concept_embedding = np.mean(neural_states, axis=0)
            neural_params = {
                'excitability': np.mean(concept_embedding),
                'inhibition': np.std(concept_embedding)
            }
        
        processing_time = time.time() - start_time
        throughput = 100 / processing_time
        print(f"✓ Processing throughput: {throughput:.1f} cycles/second")
        
        # Test memory efficiency
        print("Testing memory efficiency...")
        
        import psutil
        import gc
        
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Create large data structures
        large_arrays = []
        for i in range(10):
            large_arrays.append(np.random.random((1000, 256)))
        
        peak_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Clean up
        del large_arrays
        gc.collect()
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        print(f"✓ Memory usage: {initial_memory:.1f}MB -> {peak_memory:.1f}MB -> {final_memory:.1f}MB")
        
        # Test integration latency
        print("Testing integration latency...")
        
        latencies = []
        for i in range(50):
            start = time.time()
            
            # Simulate integration operations
            neural_data = np.random.random((20, 4))
            concept_data = np.mean(neural_data, axis=0)
            transformed = np.tanh(concept_data * 1.5)
            neural_output = np.tile(transformed, (20, 1))
            
            latency = (time.time() - start) * 1000  # ms
            latencies.append(latency)
        
        avg_latency = np.mean(latencies)
        p95_latency = np.percentile(latencies, 95)
        
        print(f"✓ Integration latency: avg={avg_latency:.2f}ms, p95={p95_latency:.2f}ms")
        
        print("✓ Integration performance test PASSED")
        return True
        
    except Exception as e:
        print(f"✗ Integration performance test FAILED: {e}")
        return False

def test_error_handling():
    """Test error handling and robustness."""
    print("\n" + "="*60)
    print("TESTING ERROR HANDLING")
    print("="*60)
    
    try:
        # Test with malformed inputs
        print("Testing malformed input handling...")
        
        from ultra.integration.diffusion_neuromorphic_bridge import NeuralStateEncoder, DiffusionNeuromorphicConfig
        
        config = DiffusionNeuromorphicConfig()
        encoder = NeuralStateEncoder(config)
        
        # Test with various malformed inputs
        test_cases = [
            np.array([]),  # Empty array
            np.array([np.nan, np.inf, -np.inf, 0]),  # Invalid values
            np.random.random((0, 4)),  # Zero neurons
            np.random.random((1000000, 4)),  # Very large input
        ]
        
        for i, test_input in enumerate(test_cases):
            try:
                result = encoder.encode_neural_population(test_input)
                print(f"  ✓ Test case {i+1}: Handled gracefully, output shape {result.shape}")
            except Exception as e:
                print(f"  ✓ Test case {i+1}: Error caught gracefully: {type(e).__name__}")
        
        # Test missing dependencies
        print("Testing missing dependency handling...")
        
        # This should work even without optional dependencies
        config = DiffusionNeuromorphicConfig()
        encoder = NeuralStateEncoder(config)
        
        # Force fallback mode
        encoder.encoding_network = None
        result = encoder.encode_neural_population(np.random.random((10, 4)))
        print(f"✓ Fallback mode works: output shape {result.shape}")
        
        print("✓ Error handling test PASSED")
        return True
        
    except Exception as e:
        print(f"✗ Error handling test FAILED: {e}")
        return False

def main():
    """Run comprehensive integration tests."""
    print("ULTRA INTEGRATION MODULE COMPREHENSIVE TEST")
    print("=" * 80)
    print(f"Python version: {sys.version}")
    print(f"Test time: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Run all tests
    tests = [
        ("Consciousness Lattice Bridge", test_consciousness_lattice_bridge),
        ("Neuromorphic-Transformer Bridge", test_neuromorphic_transformer_bridge),
        ("Diffusion-Neuromorphic Bridge", test_diffusion_neuromorphic_bridge),
        ("Integration Performance", test_integration_performance),
        ("Error Handling", test_error_handling),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"\n✗ CRITICAL ERROR in {test_name}: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*80)
    print("TEST SUMMARY")
    print("="*80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "PASSED" if result else "FAILED"
        symbol = "✓" if result else "✗"
        print(f"{symbol} {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL INTEGRATION TESTS PASSED! The system is ready for production.")
        return 0
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
