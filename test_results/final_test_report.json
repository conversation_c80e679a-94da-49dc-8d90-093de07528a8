{"test_execution_summary": {"timestamp": "2025-06-17 14:18:25", "total_execution_time": 60.07627081871033, "total_test_suites": 6, "passed_suites": 5, "failed_suites": 1, "suite_success_rate": 0.8333333333333334, "total_individual_tests": 0, "total_passed_tests": 0, "total_failed_tests": 0, "individual_test_success_rate": 0}, "suite_details": {"conceptual_diffusion": {"success": true, "execution_time": 25.45934247970581, "tests_run": 0, "tests_passed": 0, "tests_failed": 0, "error_count": 0, "warning_count": 0}, "thought_latent_space": {"success": true, "execution_time": 53.83513832092285, "tests_run": 0, "tests_passed": 0, "tests_failed": 0, "error_count": 0, "warning_count": 0}, "bayesian_uncertainty": {"success": false, "execution_time": 2.4302284717559814, "tests_run": 0, "tests_passed": 0, "tests_failed": 0, "error_count": 3, "warning_count": 0}, "reverse_diffusion_reasoning": {"success": true, "execution_time": 2.080444812774658, "tests_run": 0, "tests_passed": 0, "tests_failed": 0, "error_count": 0, "warning_count": 0}, "probabilistic_inference": {"success": true, "execution_time": 3.0671048164367676, "tests_run": 0, "tests_passed": 0, "tests_failed": 0, "error_count": 0, "warning_count": 0}, "comprehensive_integration": {"success": true, "execution_time": 0.2040722370147705, "tests_run": 0, "tests_passed": 0, "tests_failed": 0, "error_count": 0, "warning_count": 0}}, "performance_analysis": {"total_execution_time": 60.07627081871033, "average_suite_time": 10.012711803118387, "fastest_suite": "comprehensive_integration", "slowest_suite": "thought_latent_space", "system_resources": {"cpu_count": 2, "cpu_percent": 71.9, "memory_total_gb": 7.751644134521484, "memory_available_gb": 3.107025146484375, "memory_percent": 59.9, "gpu_available": false, "gpu_count": 0, "gpu_memory_gb": 0}}, "error_analysis": {"total_errors": 3, "unique_errors": 3, "error_categories": {"mathematical": 0, "performance": 0, "memory": 0, "integration": 0, "cuda": 0, "import": 1, "other": 2}, "most_common_errors": [["Direct import execution returned False", 1], ["Subprocess execution returned False", 1], ["Unittest discovery returned False", 1]]}, "recommendations": ["Good test success rate - consider addressing remaining failures", "Fast execution time - efficient test implementation", "Import errors detected - check module paths and dependencies"], "configuration": {"run_parallel": true, "max_workers": 4, "device": "cpu", "memory_limit_gb": 16}}