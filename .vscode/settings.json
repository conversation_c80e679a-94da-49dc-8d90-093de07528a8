{
    // Python settings
    "python.defaultInterpreterPath": "./.venv/bin/python",
    "python.terminal.activateEnvironment": true,
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": false,
    "python.linting.flake8Enabled": true,
    "python.formatting.provider": "black",
    "python.analysis.autoImportCompletions": true,
    "python.analysis.typeCheckingMode": "basic",
    
    // C++ settings
    "C_Cpp.default.cppStandard": "c++11",
    "C_Cpp.default.cStandard": "c11",
    "C_Cpp.default.compilerPath": "/usr/bin/g++",
    "C_Cpp.default.intelliSenseMode": "linux-gcc-x64",
    "C_Cpp.default.includePath": [
        "${workspaceFolder}/**",
        "/usr/include/**",
        "/usr/local/cuda/include/**",
        "/opt/cuda/include/**"
    ],
    "C_Cpp.default.defines": [
        "_GNU_SOURCE",
        "__STDC_CONSTANT_MACROS",
        "__STDC_FORMAT_MACROS",
        "__STDC_LIMIT_MACROS"
    ],
    "C_Cpp.default.compilerArgs": [
        "-fopenmp",
        "-std=c++11",
        "-O3"
    ],
    
    // CUDA specific settings
    "files.associations": {
        "*.cu": "cuda-cpp",
        "*.cuh": "cuda-cpp",
        "*.h": "c",
        "*.hpp": "cpp"
    },
    
    // File exclude patterns
    "files.exclude": {
        "**/.git": true,
        "**/.svn": true,
        "**/.hg": true,
        "**/CVS": true,
        "**/.DS_Store": true,
        "**/Thumbs.db": true,
        "**/*.o": true,
        "**/*.so": true,
        "**/*.a": true,
        "**/__pycache__": true,
        "**/*.pyc": true,
        "**/.pytest_cache": true,
        "**/test_diffusion": true,
        "**/conceptual_diffusion": true,
        "**/libconceptual_diffusion.so": true
    },
    
    // Search exclude patterns
    "search.exclude": {
        "**/node_modules": true,
        "**/bower_components": true,
        "**/*.code-search": true,
        "**/__pycache__": true,
        "**/.pytest_cache": true,
        "**/build": true,
        "**/dist": true
    },
    
    // Terminal settings
    "terminal.integrated.shell.linux": "/bin/bash",
    "terminal.integrated.env.linux": {
        "PYTHONPATH": "${workspaceFolder}:${workspaceFolder}/ultra:${workspaceFolder}/ultra/ultra",
        "CUDA_VISIBLE_DEVICES": "0",
        "ULTRA_DEBUG": "1"
    },
    
    // Editor settings
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.organizeImports": "explicit"
    },
    "editor.rulers": [80, 100],
    "editor.renderWhitespace": "boundary",
    "editor.insertSpaces": true,
    "editor.detectIndentation": true,
    
    // Language specific settings
    "[python]": {
        "editor.tabSize": 4,
        "editor.insertSpaces": true,
        "editor.formatOnSave": true,
        "editor.codeActionsOnSave": {
            "source.organizeImports": "explicit"
        }
    },
    "[cpp]": {
        "editor.tabSize": 4,
        "editor.insertSpaces": true,
        "editor.formatOnSave": false
    },
    "[cuda-cpp]": {
        "editor.tabSize": 4,
        "editor.insertSpaces": true,
        "editor.formatOnSave": false
    },
    "[json]": {
        "editor.tabSize": 2,
        "editor.insertSpaces": true
    },
    "[makefile]": {
        "editor.insertSpaces": false,
        "editor.detectIndentation": false
    },
    
    // Git settings
    "git.ignoreLimitWarning": true,
    "git.autofetch": true,
    
    // Extension settings
    "extensions.ignoreRecommendations": false,
    
    // Integrated terminal
    "terminal.integrated.scrollback": 10000,
    "terminal.integrated.copyOnSelection": true,
    
    // Debug console
    "debug.console.fontSize": 14,
    "debug.console.lineHeight": 20,
    
    // Performance settings
    "files.watcherExclude": {
        "**/.git/objects/**": true,
        "**/.git/subtree-cache/**": true,
        "**/node_modules/*/**": true,
        "**/__pycache__/**": true,
        "**/build/**": true,
        "**/dist/**": true
    },
    
    // ULTRA specific settings
    "ultra.diffusion.autoCompile": true,
    "ultra.diffusion.preferCuda": true,
    "ultra.diffusion.debugMode": true,
    "ultra.diffusion.logLevel": "INFO",
    
    // Workspace recommendations
    "extensions.recommendations": [
        "ms-python.python",
        "ms-vscode.cpptools",
        "ms-vscode.cmake-tools",
        "nvidia.nsight-vscode-edition",
        "ms-python.flake8",
        "ms-python.black-formatter",
        "ms-vscode.makefile-tools"
    ]
}