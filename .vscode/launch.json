{"version": "0.2.0", "configurations": [{"name": "🧪 ULTRA: C++ <PERSON>bu<PERSON> (Primary)", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/ultra/ultra/diffusion_reasoning/cuda/test_diffusion", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}/ultra/ultra/diffusion_reasoning/cuda", "environment": [{"name": "CUDA_VISIBLE_DEVICES", "value": "0"}, {"name": "ULTRA_DEBUG", "value": "1"}], "externalConsole": false, "MIMode": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "Set Disassembly Flavor to Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}, {"description": "Set breakpoint on main", "text": "-break-insert main", "ignoreFailures": true}], "preLaunchTask": "build-test-debug", "miDebuggerPath": "/usr/bin/gdb", "presentation": {"hidden": false, "group": "C++ Primary", "order": 1}}, {"name": "⚡ ULTRA: C++ Release Performance", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/ultra/ultra/diffusion_reasoning/cuda/test_diffusion", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}/ultra/ultra/diffusion_reasoning/cuda", "environment": [{"name": "CUDA_VISIBLE_DEVICES", "value": "0"}], "externalConsole": false, "MIMode": "gdb", "preLaunchTask": "build-test-release", "miDebuggerPath": "/usr/bin/gdb", "presentation": {"hidden": false, "group": "C++ Primary", "order": 2}}, {"name": "🔍 ULTRA: C++ Memory Debug (Valgrind)", "type": "cppdbg", "request": "launch", "program": "/usr/bin/valgrind", "args": ["--tool=memcheck", "--leak-check=full", "--show-leak-kinds=all", "--track-origins=yes", "--verbose", "${workspaceFolder}/ultra/ultra/diffusion_reasoning/cuda/test_diffusion"], "stopAtEntry": false, "cwd": "${workspaceFolder}/ultra/ultra/diffusion_reasoning/cuda", "environment": [], "externalConsole": false, "MIMode": "gdb", "preLaunchTask": "build-test-debug", "miDebuggerPath": "/usr/bin/gdb", "presentation": {"hidden": false, "group": "C++ Advanced", "order": 3}}, {"name": "🖥️ ULTRA: C++ CPU Force Mode", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/ultra/ultra/diffusion_reasoning/cuda/test_diffusion", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}/ultra/ultra/diffusion_reasoning/cuda", "environment": [{"name": "CUDA_VISIBLE_DEVICES", "value": ""}, {"name": "ULTRA_FORCE_CPU", "value": "1"}], "externalConsole": false, "MIMode": "gdb", "preLaunchTask": "build-test-cpu-only", "miDebuggerPath": "/usr/bin/gdb", "presentation": {"hidden": false, "group": "C++ Advanced", "order": 4}}, {"name": "🚀 ULTRA: Direct C++ Executable", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/ultra/ultra/diffusion_reasoning/cuda/conceptual_diffusion", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}/ultra/ultra/diffusion_reasoning/cuda", "environment": [], "externalConsole": false, "MIMode": "gdb", "preLaunchTask": "build-executable", "miDebuggerPath": "/usr/bin/gdb", "presentation": {"hidden": false, "group": "C++ Direct", "order": 5}}, {"name": "🎯 ULTRA: Custom C++ Args", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/ultra/ultra/diffusion_reasoning/cuda/test_diffusion", "args": ["--batch-size", "8", "--concept-dim", "1024", "--verbose"], "stopAtEntry": false, "cwd": "${workspaceFolder}/ultra/ultra/diffusion_reasoning/cuda", "environment": [{"name": "CUDA_VISIBLE_DEVICES", "value": "0"}], "externalConsole": false, "MIMode": "gdb", "preLaunchTask": "build-test-debug", "miDebuggerPath": "/usr/bin/gdb", "presentation": {"hidden": false, "group": "C++ Custom", "order": 6}}, {"name": "🔧 ULTRA: Attach to Process", "type": "cppdbg", "request": "attach", "program": "${workspaceFolder}/ultra/ultra/diffusion_reasoning/cuda/test_diffusion", "processId": "${command:pickProcess}", "MIMode": "gdb", "miDebuggerPath": "/usr/bin/gdb", "presentation": {"hidden": false, "group": "C++ Advanced", "order": 7}}, {"name": "🐍 ULTRA: Python Wrapper (Optional)", "type": "python", "request": "launch", "program": "${workspaceFolder}/ultra/ultra/diffusion_reasoning/cuda/conceptual_diffusion_wrapper.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}/ultra/ultra/diffusion_reasoning/cuda", "env": {"PYTHONPATH": "${workspaceFolder}:${workspaceFolder}/ultra:${workspaceFolder}/ultra/ultra", "CUDA_VISIBLE_DEVICES": "0"}, "args": ["--test"], "justMyCode": false, "preLaunchTask": "build-library-auto", "presentation": {"hidden": false, "group": "Python Optional", "order": 8}}, {"name": "🛠️ ULTRA: GDB Command Line", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/ultra/ultra/diffusion_reasoning/cuda/test_diffusion", "args": [], "stopAtEntry": true, "cwd": "${workspaceFolder}/ultra/ultra/diffusion_reasoning/cuda", "environment": [], "externalConsole": true, "MIMode": "gdb", "miDebuggerPath": "/usr/bin/gdb", "preLaunchTask": "build-test-debug", "presentation": {"hidden": false, "group": "C++ Advanced", "order": 9}}, {"name": "📊 ULTRA: C++ Benchmark Mode", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/ultra/ultra/diffusion_reasoning/cuda/test_diffusion", "args": ["--benchmark", "--iterations", "1000"], "stopAtEntry": false, "cwd": "${workspaceFolder}/ultra/ultra/diffusion_reasoning/cuda", "environment": [{"name": "CUDA_VISIBLE_DEVICES", "value": "0"}, {"name": "ULTRA_BENCHMARK_MODE", "value": "1"}], "externalConsole": false, "MIMode": "gdb", "preLaunchTask": "build-test-optimized", "miDebuggerPath": "/usr/bin/gdb", "presentation": {"hidden": false, "group": "C++ Performance", "order": 10}}], "compounds": [{"name": "🎯 ULTRA: Debug & Performance Compare", "configurations": ["🧪 ULTRA: C++ <PERSON>bu<PERSON> (Primary)", "⚡ ULTRA: C++ Release Performance"], "stopAll": true, "presentation": {"hidden": false, "group": "Compounds", "order": 1}}, {"name": "🔍 ULTRA: Memory Analysis Suite", "configurations": ["🔍 ULTRA: C++ Memory Debug (Valgrind)", "🧪 ULTRA: C++ <PERSON>bu<PERSON> (Primary)"], "stopAll": false, "presentation": {"hidden": false, "group": "Compounds", "order": 2}}]}