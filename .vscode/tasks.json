{"version": "2.0.0", "tasks": [{"label": "build-test-debug", "type": "shell", "command": "bash", "args": ["-c", "echo '🔨 Building debug test executable...' && if command -v nvcc >/dev/null 2>&1; then nvcc -O0 -g -std=c++11 --expt-relaxed-constexpr -DTEST_CONCEPTUAL_DIFFUSION conceptual_diffusion.cu -o test_diffusion -lcublas -lcusparse -lcufft -lcurand; else g++ -O0 -g -fopenmp -std=c++11 -DTEST_CONCEPTUAL_DIFFUSION conceptual_diffusion.cu -o test_diffusion -lm; fi && echo '✅ Debug build complete'"], "group": "build", "options": {"cwd": "${workspaceFolder}/ultra/ultra/diffusion_reasoning/cuda"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false, "group": "build"}, "problemMatcher": {"owner": "cpp", "fileLocation": "absolute", "pattern": {"regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}}}, {"label": "build-test-release", "type": "shell", "command": "bash", "args": ["-c", "echo '🚀 Building release test executable...' && if command -v nvcc >/dev/null 2>&1; then nvcc -O3 -DNDEBUG -std=c++11 --expt-relaxed-constexpr -DTEST_CONCEPTUAL_DIFFUSION conceptual_diffusion.cu -o test_diffusion -lcublas -lcusparse -lcufft -lcurand; else g++ -O3 -DNDEBUG -fopenmp -std=c++11 -DTEST_CONCEPTUAL_DIFFUSION conceptual_diffusion.cu -o test_diffusion -lm; fi && echo '✅ Release build complete'"], "group": "build", "options": {"cwd": "${workspaceFolder}/ultra/ultra/diffusion_reasoning/cuda"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false, "group": "build"}, "problemMatcher": {"owner": "cpp", "fileLocation": "absolute", "pattern": {"regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}}}, {"label": "build-test-optimized", "type": "shell", "command": "bash", "args": ["-c", "echo '⚡ Building optimized test executable...' && if command -v nvcc >/dev/null 2>&1; then nvcc -O3 -DNDEBUG -std=c++11 --expt-relaxed-constexpr -Xcompiler -march=native -Xcompiler -mtune=native -DTEST_CONCEPTUAL_DIFFUSION conceptual_diffusion.cu -o test_diffusion -lcublas -lcusparse -lcufft -lcurand; else g++ -O3 -DNDEBUG -march=native -mtune=native -fopenmp -std=c++11 -DTEST_CONCEPTUAL_DIFFUSION conceptual_diffusion.cu -o test_diffusion -lm; fi && echo '✅ Optimized build complete'"], "group": "build", "options": {"cwd": "${workspaceFolder}/ultra/ultra/diffusion_reasoning/cuda"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false, "group": "build"}, "problemMatcher": {"owner": "cpp", "fileLocation": "absolute", "pattern": {"regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}}}, {"label": "build-test-cpu-only", "type": "shell", "command": "g++", "args": ["-O0", "-g", "-fopenmp", "-std=c++11", "-DTEST_CONCEPTUAL_DIFFUSION", "conceptual_diffusion.cu", "-o", "test_diffusion", "-lm"], "group": "build", "options": {"cwd": "${workspaceFolder}/ultra/ultra/diffusion_reasoning/cuda"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false, "group": "build"}, "problemMatcher": {"owner": "cpp", "fileLocation": "absolute", "pattern": {"regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}}}, {"label": "build-executable", "type": "shell", "command": "bash", "args": ["-c", "echo '🔨 Building main executable...' && if command -v nvcc >/dev/null 2>&1; then nvcc -O3 -std=c++11 --expt-relaxed-constexpr conceptual_diffusion.cu -o conceptual_diffusion -lcublas -lcusparse -lcufft -lcurand; else g++ -O3 -fopenmp -std=c++11 conceptual_diffusion.cu -o conceptual_diffusion -lm; fi && echo '✅ Executable build complete'"], "group": "build", "options": {"cwd": "${workspaceFolder}/ultra/ultra/diffusion_reasoning/cuda"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false, "group": "build"}, "problemMatcher": {"owner": "cpp", "fileLocation": "absolute", "pattern": {"regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}}}, {"label": "build-library-auto", "type": "shell", "command": "make", "args": ["library"], "group": "build", "options": {"cwd": "${workspaceFolder}/ultra/ultra/diffusion_reasoning/cuda"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false, "group": "build"}, "problemMatcher": {"owner": "cpp", "fileLocation": "absolute", "pattern": {"regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}}}, {"label": "run-test-direct", "type": "shell", "command": "./test_diffusion", "args": [], "group": "test", "options": {"cwd": "${workspaceFolder}/ultra/ultra/diffusion_reasoning/cuda"}, "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new", "showReuseMessage": false, "clear": true}, "dependsOn": ["build-test-release"]}, {"label": "run-test-with-gdb", "type": "shell", "command": "gdb", "args": ["-ex", "run", "-ex", "bt", "-ex", "quit", "./test_diffusion"], "group": "test", "options": {"cwd": "${workspaceFolder}/ultra/ultra/diffusion_reasoning/cuda"}, "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new", "showReuseMessage": false, "clear": true}, "dependsOn": ["build-test-debug"]}, {"label": "run-valgrind-check", "type": "shell", "command": "valgrind", "args": ["--tool=memcheck", "--leak-check=full", "--show-leak-kinds=all", "--track-origins=yes", "./test_diffusion"], "group": "test", "options": {"cwd": "${workspaceFolder}/ultra/ultra/diffusion_reasoning/cuda"}, "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new", "showReuseMessage": false, "clear": true}, "dependsOn": ["build-test-debug"]}, {"label": "clean-all", "type": "shell", "command": "bash", "args": ["-c", "echo '🧹 Cleaning all build artifacts...' && rm -f test_diffusion conceptual_diffusion *.o *.so *.a && echo '✅ Clean complete'"], "group": "build", "options": {"cwd": "${workspaceFolder}/ultra/ultra/diffusion_reasoning/cuda"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false, "group": "build"}}, {"label": "check-dependencies", "type": "shell", "command": "bash", "args": ["-c", "echo '🔍 Checking system dependencies...' && echo 'C++ Compiler:' && which g++ && g++ --version | head -n1 && echo '' && echo 'CUDA Compiler:' && (which nvcc && nvcc --version | grep 'release' || echo 'nvcc not found') && echo '' && echo 'GDB Debugger:' && (which gdb && gdb --version | head -n1 || echo 'gdb not found') && echo '' && echo 'Valgrind:' && (which valgrind && valgrind --version || echo 'valgrind not found') && echo '' && echo 'Make:' && (which make && make --version | head -n1 || echo 'make not found')"], "group": "build", "options": {"cwd": "${workspaceFolder}/ultra/ultra/diffusion_reasoning/cuda"}, "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new", "showReuseMessage": false, "clear": true}}, {"label": "install-dependencies", "type": "shell", "command": "bash", "args": ["-c", "echo '📦 Installing dependencies...' && sudo apt update && sudo apt install -y build-essential gdb valgrind libomp-dev && echo '✅ Dependencies installed'"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new", "showReuseMessage": false, "clear": true}}, {"label": "cuda-info", "type": "shell", "command": "bash", "args": ["-c", "echo '🔍 CUDA System Information:' && echo '=========================' && if command -v nvidia-smi >/dev/null 2>&1; then nvidia-smi; else echo '❌ nvidia-smi not found'; fi && echo '' && echo 'CUDA Compiler:' && if command -v nvcc >/dev/null 2>&1; then nvcc --version; else echo '❌ nvcc not found'; fi && echo '' && echo 'CUDA Libraries:' && ldconfig -p | grep -i cuda | head -5 || echo 'No CUDA libraries found'"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new", "showReuseMessage": false, "clear": true}}, {"label": "debug-setup", "type": "shell", "command": "bash", "args": ["-c", "echo '🔧 Setting up debug environment...' && sudo apt update && sudo apt install -y gdb gdbserver valgrind strace ltrace && echo '✅ Debug tools installed' && echo '' && echo 'Available tools:' && echo '- GDB: GNU Debugger' && gdb --version | head -n1 && echo '- Valgrind: Memory checker' && valgrind --version && echo '- Strace: System call tracer' && strace --version 2>&1 | head -n1"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new", "showReuseMessage": false, "clear": true}}, {"label": "benchmark-cpp", "type": "shell", "command": "bash", "args": ["-c", "echo '📊 Running C++ performance benchmark...' && echo 'Building optimized version...' && make clean && if command -v nvcc >/dev/null 2>&1; then nvcc -O3 -DNDEBUG -std=c++11 --expt-relaxed-constexpr -DTEST_CONCEPTUAL_DIFFUSION conceptual_diffusion.cu -o test_diffusion -lcublas -lcusparse -lcufft -lcurand; else g++ -O3 -DNDEBUG -march=native -mtune=native -fopenmp -std=c++11 -DTEST_CONCEPTUAL_DIFFUSION conceptual_diffusion.cu -o test_diffusion -lm; fi && echo 'Running benchmark...' && time ./test_diffusion && echo '✅ Benchmark complete'"], "group": "test", "options": {"cwd": "${workspaceFolder}/ultra/ultra/diffusion_reasoning/cuda"}, "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new", "showReuseMessage": false, "clear": true}}, {"label": "watch-and-build", "type": "shell", "command": "bash", "args": ["-c", "echo '👀 Watching for changes in conceptual_diffusion.cu...' && while inotifywait -e modify conceptual_diffusion.cu 2>/dev/null; do echo '🔄 File changed, rebuilding...' && if command -v nvcc >/dev/null 2>&1; then nvcc -O0 -g -std=c++11 --expt-relaxed-constexpr -DTEST_CONCEPTUAL_DIFFUSION conceptual_diffusion.cu -o test_diffusion -lcublas -lcusparse -lcufft -lcurand; else g++ -O0 -g -fopenmp -std=c++11 -DTEST_CONCEPTUAL_DIFFUSION conceptual_diffusion.cu -o test_diffusion -lm; fi && echo '✅ Rebuild complete'; done"], "group": "build", "options": {"cwd": "${workspaceFolder}/ultra/ultra/diffusion_reasoning/cuda"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "dedicated", "showReuseMessage": false, "clear": true}, "isBackground": true, "runOptions": {"instanceLimit": 1}}, {"type": "cppbuild", "label": "C/C++: g++ Aktive <PERSON>i kompilieren", "command": "/usr/bin/g++", "args": ["-fdiagnostics-color=always", "-g", "${file}", "-o", "${fileDirname}/${fileBasenameNoExtension}", "-fopenmp", "-std=c++11", "-O3"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$gcc"], "group": {"kind": "build", "isDefault": true}, "detail": "Vom Debugger generierte Aufgabe."}]}