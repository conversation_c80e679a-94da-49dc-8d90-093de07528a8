# 🚀 ULTRA VS Code Development Environment

This guide explains how to set up and use the complete VS Code development environment for the ULTRA Conceptual Diffusion system.

## 📁 File Structure

Place these files in your `.vscode` directory:

```
/workspaces/Ultra/.vscode/
├── launch.json        # Debug/run configurations
├── tasks.json         # Build and automation tasks
└── settings.json      # VS Code workspace settings
```

Additional project files:
```
/workspaces/Ultra/ultra/ultra/diffusion_reasoning/cuda/
├── conceptual_diffusion.cu           # Main CUDA implementation
├── conceptual_diffusion_wrapper.py   # Python wrapper
├── benchmark_conceptual_diffusion.py # Performance benchmark
├── test_integration.py               # Integration tests
└── Makefile                          # Build system
```

## 🎯 Quick Start

### 1. **First Setup**
```bash
# Navigate to project root
cd /workspaces/Ultra

# Create .vscode directory if it doesn't exist
mkdir -p .vscode

# Copy the configuration files to .vscode/
# (launch.json, tasks.json, settings.json)

# Check system capabilities
cd ultra/ultra/diffusion_reasoning/cuda
make check-system
```

### 2. **Install Dependencies**
Press `Ctrl+Shift+P` and run:
- "Tasks: Run Task" → "install-dependencies"

Or manually:
```bash
make install-deps
```

### 3. **Build the System**
Press `Ctrl+Shift+P` and run:
- "Tasks: Run Task" → "build-library-auto"

### 4. **Run Your First Test**
Press `F5` or select:
- **"🚀 ULTRA: Python Wrapper (Quick Test)"** from the debug dropdown

## 🎮 Available Launch Configurations

### **Primary Configurations**

| Configuration | Description | Use Case |
|---------------|-------------|----------|
| 🚀 **Python Wrapper (Quick Test)** | Run Python wrapper with auto-compile | Quick testing and development |
| 🧪 **C++ Test Suite** | Debug C++ test executable | Low-level debugging |
| ⚡ **C++ Release Performance** | Run optimized C++ version | Performance testing |
| 🐍 **Python Development** | Python development with custom config | Advanced Python debugging |

### **Specialized Configurations**

| Configuration | Description | Use Case |
|---------------|-------------|----------|
| 🖥️ **Force CPU Mode** | Force CPU-only execution | Testing without GPU |
| 🎯 **Custom Python Script** | Run currently open Python file | Script development |
| 📊 **Performance Benchmark** | Run comprehensive benchmarks | Performance analysis |
| 🛠️ **Interactive Development** | IPython interactive session | Experimentation |

### **Advanced Configurations**

| Configuration | Description | Use Case |
|---------------|-------------|----------|
| 🔍 **Debug C++ with Valgrind** | Memory debugging with Valgrind | Memory leak detection |
| 🌐 **Integration Test Suite** | Full integration testing | System validation |
| 🔧 **Build System Test** | Test build system with pytest | Build validation |

### **Compound Configurations**

| Configuration | Description |
|---------------|-------------|
| 🎯 **Full Test Suite (CPU + GPU)** | Run both CPU and GPU tests |
| ⚡ **Performance Comparison** | Compare C++ and Python performance |

## 🔧 Available Tasks

### **Build Tasks**
- **build-library-auto**: Build library with auto-detection (CPU/GPU)
- **build-library-debug**: Build debug version with symbols
- **build-library-cpu**: Force CPU-only build
- **build-library-optimized**: Build with maximum optimizations
- **build-test-debug**: Build test executable with debug info
- **build-test-release**: Build optimized test executable
- **build-all-variants**: Build all variants (library + tests)

### **System Tasks**
- **check-dependencies**: Check system capabilities
- **install-dependencies**: Install required packages
- **clean-all**: Clean build artifacts
- **setup-python-environment**: Setup Python environment

### **Test Tasks**
- **run-quick-test**: Run C++ test suite
- **run-python-test**: Run Python wrapper test
- **benchmark-performance**: Run performance benchmarks

### **Development Tasks**
- **watch-rebuild**: Watch files and rebuild on changes
- **debug-setup**: Install debug tools (gdb, valgrind)
- **cuda-info**: Display CUDA system information

## 🎪 Usage Examples

### **Basic Development Workflow**

1. **Start Development**:
   - Press `F5` → Select "🚀 ULTRA: Python Wrapper (Quick Test)"
   - This will auto-compile and run a quick test

2. **Debug C++ Code**:
   - Press `F5` → Select "🧪 C++ Test Suite"
   - Set breakpoints in the .cu file
   - Step through CUDA kernels and C++ code

3. **Performance Testing**:
   - Press `F5` → Select "📊 Performance Benchmark"
   - Get detailed performance metrics

4. **Interactive Development**:
   - Press `F5` → Select "🛠️ Interactive Development"
   - Opens IPython with ULTRA already loaded

### **Advanced Workflows**

#### **Compare CPU vs GPU Performance**
```bash
# Method 1: Using compound configuration
F5 → "⚡ Performance Comparison"

# Method 2: Manual comparison
F5 → "🖥️ Force CPU Mode"    # Test CPU
F5 → "🚀 Python Wrapper"     # Test GPU
```

#### **Memory Debugging**
```bash
# Run with Valgrind
F5 → "🔍 Debug C++ with Valgrind"

# Or manually
Ctrl+Shift+P → "Tasks: Run Task" → "debug-setup"
# Then use the Valgrind configuration
```

#### **Watch Mode Development**
```bash
# Start file watcher
Ctrl+Shift+P → "Tasks: Run Task" → "watch-rebuild"

# Now any changes to .cu or .py files trigger rebuild
```

### **Custom Configuration**

#### **Environment Variables**
The configurations automatically set:
- `PYTHONPATH`: Includes all necessary directories
- `CUDA_VISIBLE_DEVICES`: Controls GPU visibility
- `ULTRA_DEBUG`: Enables debug mode
- `ULTRA_FORCE_CPU`: Forces CPU mode when needed

#### **Custom Arguments**
Modify launch configurations to add custom arguments:
```json
"args": [
    "--batch-size", "8",
    "--concept-dim", "1024",
    "--num-iterations", "500"
]
```

## 🛠️ Troubleshooting

### **Common Issues**

#### **"Library not found" Error**
```bash
# Solution 1: Auto-compile
F5 → Any configuration will auto-compile

# Solution 2: Manual build
Ctrl+Shift+P → "Tasks: Run Task" → "build-library-auto"

# Solution 3: Check system
Ctrl+Shift+P → "Tasks: Run Task" → "check-dependencies"
```

#### **CUDA Not Available**
```bash
# Check CUDA status
Ctrl+Shift+P → "Tasks: Run Task" → "cuda-info"

# Force CPU mode
F5 → "🖥️ Force CPU Mode"
```

#### **Python Import Errors**
```bash
# Setup Python environment
Ctrl+Shift+P → "Tasks: Run Task" → "setup-python-environment"

# Check PYTHONPATH in terminal
echo $PYTHONPATH
```

#### **Build Errors**
```bash
# Clean and rebuild
Ctrl+Shift+P → "Tasks: Run Task" → "clean-all"
Ctrl+Shift+P → "Tasks: Run Task" → "build-all-variants"

# Install dependencies
Ctrl+Shift+P → "Tasks: Run Task" → "install-dependencies"
```

### **Debug Tips**

#### **C++ Debugging**
- Use "🧪 C++ Test Suite" configuration
- Set breakpoints in .cu files
- Use `printf` debugging in CUDA kernels
- Check the debug console for detailed output

#### **Python Debugging**
- Use "🐍 Python Development" configuration
- Set breakpoints in .py files
- Use the interactive debugger
- Check variable values in the debug panel

#### **Performance Issues**
- Use "📊 Performance Benchmark" configuration
- Compare CPU vs GPU performance
- Check memory usage in the output
- Monitor system resources

## 📊 Monitoring and Logs

### **Output Panels**
- **Terminal**: Build output and error messages
- **Debug Console**: Runtime debugging information
- **Problems**: Compilation errors and warnings
- **Output → Tasks**: Task execution logs

### **Log Levels**
Set in environment or configuration:
```bash
export ULTRA_LOG_LEVEL=DEBUG  # DEBUG, INFO, WARNING, ERROR
```

### **Performance Monitoring**
The benchmark configuration provides:
- Initialization time
- Forward/reverse diffusion throughput
- Memory usage
- Uncertainty quantification performance
- CPU vs GPU comparison

## 🎯 Best Practices

### **Development Workflow**
1. Start with "🚀 Python Wrapper (Quick Test)" for quick validation
2. Use "🧪 C++ Test Suite" for detailed debugging
3. Run "📊 Performance Benchmark" before committing changes
4. Use "🌐 Integration Test Suite" for comprehensive testing

### **Code Organization**
- Keep .cu files in the cuda/ directory
- Use the Python wrapper for high-level interfaces
- Write integration tests for new features
- Use the Makefile for consistent builds

### **Performance Optimization**
- Profile with "📊 Performance Benchmark"
- Compare CPU vs GPU implementations
- Use "⚡ C++ Release Performance" for final testing
- Monitor memory usage during development

## 🚀 Next Steps

1. **Explore the Configurations**: Try each launch configuration to understand the system
2. **Run Benchmarks**: Get baseline performance metrics
3. **Customize Settings**: Modify configurations for your specific needs
4. **Contribute**: Add new test cases and benchmarks
5. **Integrate**: Connect with other ULTRA components

## 📚 Additional Resources

- [VS Code C++ Documentation](https://code.visualstudio.com/docs/languages/cpp)
- [VS Code Python Documentation](https://code.visualstudio.com/docs/languages/python)
- [CUDA Programming Guide](https://docs.nvidia.com/cuda/cuda-c-programming-guide/)
- [ULTRA Project Documentation](../../../README.md)

---

**🎉 Happy coding with ULTRA Conceptual Diffusion!**

For issues or questions, check the troubleshooting section above or refer to the main project documentation.