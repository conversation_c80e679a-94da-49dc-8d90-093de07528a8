cmake_minimum_required(VERSION 3.15)
project(ULTRA VERSION 0.1.0 LANGUAGES CXX C)

# Set global variables
set(ULTRA_SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR})
set(ULTRA_INCLUDE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/include)

# Set C++ standard globally
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Set default build type
if(NOT CMAKE_BUILD_TYPE)
  set(CMAKE_BUILD_TYPE Release)
endif()

# Output directories
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)

# Find common dependencies
find_package(Python3 COMPONENTS Interpreter Development REQUIRED)
find_package(pybind11 REQUIRED)
find_package(Eigen3 REQUIRED)
find_package(Boost COMPONENTS system filesystem REQUIRED)
find_package(TBB REQUIRED)
find_package(OpenMP)
find_package(CUDA)
find_package(GTest)
find_package(Doxygen)

# Create a utils library that's used by multiple components
add_subdirectory(cpp/utils)

# Add component subdirectories
add_subdirectory(cpp/biological_timing)
add_subdirectory(cpp/phi_calculator)

# Add CUDA components if available
if(CUDA_FOUND)
  message(STATUS "CUDA found, building CUDA extensions")
  add_subdirectory(ultra/ultra/neuromorphic_processing/cuda)
  add_subdirectory(ultra/ultra/diffusion_reasoning/cuda)
endif()

# Enable testing
enable_testing()

# Generate and install package config
include(CMakePackageConfigHelpers)
configure_package_config_file(
  ${CMAKE_CURRENT_SOURCE_DIR}/cpp/cmake/ULTRAConfig.cmake.in
  ${CMAKE_CURRENT_BINARY_DIR}/ULTRAConfig.cmake
  INSTALL_DESTINATION lib/cmake/ULTRA
)

write_basic_package_version_file(
  ${CMAKE_CURRENT_BINARY_DIR}/ULTRAConfigVersion.cmake
  VERSION ${PROJECT_VERSION}
  COMPATIBILITY SameMajorVersion
)

install(FILES
  ${CMAKE_CURRENT_BINARY_DIR}/ULTRAConfig.cmake
  ${CMAKE_CURRENT_BINARY_DIR}/ULTRAConfigVersion.cmake
  DESTINATION lib/cmake/ULTRA
)

# Print configuration summary
message(STATUS "")
message(STATUS "ULTRA Configuration Summary:")
message(STATUS "  CMake version       : ${CMAKE_VERSION}")
message(STATUS "  C++ Compiler        : ${CMAKE_CXX_COMPILER_ID} ${CMAKE_CXX_COMPILER_VERSION}")
message(STATUS "  Build type          : ${CMAKE_BUILD_TYPE}")
message(STATUS "  Install prefix      : ${CMAKE_INSTALL_PREFIX}")
message(STATUS "  Python version      : ${Python3_VERSION}")
message(STATUS "  Components:")
message(STATUS "    - biological_timing")
message(STATUS "    - phi_calculator")
if(CUDA_FOUND)
  message(STATUS "    - neuromorphic_processing CUDA")
  message(STATUS "    - diffusion_reasoning CUDA")
endif()
message(STATUS "")